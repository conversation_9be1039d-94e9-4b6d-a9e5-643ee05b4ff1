﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge ORMEntity -> DataObject
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	[Serializable]
	public class ORMGOUser : IORMEntity 
	{
		public virtual System.Guid Id { get; set; }
 
		///
		/// All Persistent non-key fields
		///
		public virtual Nullable<System.Int16> WebsiteAccessLevelValue { get; set; }
		public virtual System.String ExternalUserId { get; set; }
		public virtual System.String NewEmailAddress { get; set; }
		public virtual System.String EmailAddress { get; set; }
		public virtual System.String UserName { get; set; }
		public virtual System.String FullName { get; set; }
		public virtual System.String AllowedDepartmentNames { get; set; }
		public virtual System.String LastName { get; set; }
		public virtual System.String PreferredLocaleString { get; set; }
		public virtual System.String FirstName { get; set; }
		public virtual System.String Password { get; set; }
		public virtual Nullable<System.DateTime> PasswordExpiry { get; set; }
		public virtual System.Boolean EmailChangeValidationInProgress { get; set; }
		public virtual System.Boolean EmailValidated { get; set; }
		public virtual System.Boolean UserValidated { get; set; }
		public virtual Nullable<System.Boolean> NewEmailValidated { get; set; }
		public virtual System.Boolean Blocked { get; set; }
		public virtual System.Boolean Unregistered { get; set; }
		public virtual Nullable<System.Boolean> DealerAdmin { get; set; }
		public virtual System.Int32 WebsiteAccessLevel { get; set; }
		public virtual Nullable<System.Int32> PreferredLocale { get; set; }
 
		///
		/// PK-Side one-to-one relations
		///
		public virtual ORMPerson Person { get; set; } 
		public virtual ORMDealerDriver DealerDriver { get; set; } 
		public virtual ORMTag Tag { get; set; } 
 
		///
		/// All FK-Side Relations
		///
		public virtual ORMDealer Dealer { get; set; }
		public virtual Nullable<System.Guid> DealerId { get; set; }

		// Note: GORole is not mapped to the database, so we only map the FK
		public virtual System.String GORoleName { get; set; }

	
 
		///
		/// PK-Side one-to-many relations
		///
		public virtual IList<ORMReportSubscription> ReportSubscriptionItems { get; set; } = new List<ORMReportSubscription>(); 
		public virtual IList<ORMGOUserGroup> UserGroupItems { get; set; } = new List<ORMGOUserGroup>(); 
		public virtual IList<ORMRevision> RevisionItems { get; set; } = new List<ORMRevision>(); 
		public virtual IList<ORMGOUserDepartment> GOUserDepartmentItems { get; set; } = new List<ORMGOUserDepartment>(); 
		public virtual IList<ORMCustomerAudit> CustomerAuditItemsDeleted { get; set; } = new List<ORMCustomerAudit>(); 
		public virtual IList<ORMCustomerAudit> CustomerAuditItemsModified { get; set; } = new List<ORMCustomerAudit>(); 
		public virtual IList<ORMGoUserToCustomer> GoUserToCustomerItems { get; set; } = new List<ORMGoUserToCustomer>(); 
		public virtual IList<ORMVehicleLockout> VehicleLockoutItems { get; set; } = new List<ORMVehicleLockout>(); 
		public virtual IList<ORMMessageHistory> MessageHistoryItems { get; set; } = new List<ORMMessageHistory>(); 
		public virtual IList<ORMGOUserRole> UserRoleItems { get; set; } = new List<ORMGOUserRole>(); 
		public virtual IList<ORMExportJobStatus> ExportJobStatusItems { get; set; } = new List<ORMExportJobStatus>(); 
		public virtual IList<ORMAlertSubscription> AlertSubscriptionItems { get; set; } = new List<ORMAlertSubscription>(); 
		public virtual IList<ORMCustomerAudit> CustomerAuditItemsCreated { get; set; } = new List<ORMCustomerAudit>(); 
 
		///
		/// Bridge to DataObject
		///
		public virtual IDataObject ToDataObject(IObjectsDataSet dataset, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
            var session = nHibernateSessionController.GetCurrentSession(threadContext);
            session.Evict(this);

			var x = serviceProvider.GetRequiredService<GOUserDataObject>();

 
			SetProperties(x);

			x.IsDirty = x.IsNew = x.IsMarkedForDeletion = false;

			x.ObjectsDataSet = dataset;
			x.ObjectsDataSet.AddObjectIfDoesNotExist(x);

			// Deep-map prefetch relations
			if (PrefetchAssociations.HasPrefetchForEntity("GOUser", dataProviderTransaction?.Parameters))
			{
				SetRelations(x, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction);
			}

			return x;
		}

		protected void SetProperties(GOUserDataObject x)
		{
			x.SetIdValue(Id, false, false);
			x.SetWebsiteAccessLevelValueValue(WebsiteAccessLevelValue, false, false);
			x.SetExternalUserIdValue(ExternalUserId, false, false);
			x.SetNewEmailAddressValue(NewEmailAddress, false, false);
			x.SetEmailAddressValue(EmailAddress, false, false);
			x.SetUserNameValue(UserName, false, false);
			x.SetFullNameValue(FullName, false, false);
			x.SetAllowedDepartmentNamesValue(AllowedDepartmentNames, false, false);
			x.SetLastNameValue(LastName, false, false);
			x.SetPreferredLocaleStringValue(PreferredLocaleString, false, false);
			x.SetFirstNameValue(FirstName, false, false);
			x.SetPasswordValue(Password, false, false);
			x.SetPasswordExpiryValue(PasswordExpiry, false, false);
			x.SetEmailChangeValidationInProgressValue(EmailChangeValidationInProgress, false, false);
			x.SetEmailValidatedValue(EmailValidated, false, false);
			x.SetUserValidatedValue(UserValidated, false, false);
			x.SetNewEmailValidatedValue(NewEmailValidated, false, false);
			x.SetBlockedValue(Blocked, false, false);
			x.SetUnregisteredValue(Unregistered, false, false);
			x.SetDealerAdminValue(DealerAdmin, false, false);
			x.SetWebsiteAccessLevelValue((WebsiteAccessLevelEnum)WebsiteAccessLevel, false, false);
			x.SetPreferredLocaleValue((Nullable<LocaleEnum>)PreferredLocale, false, false);
			x.SetDealerIdValue(this.DealerId, false, false);
			x.SetGORoleNameValue(this.GORoleName, false, false);
		}

		protected void SetRelations(GOUserDataObject x, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
			var prefetches = PrefetchAssociations.Get("GOUser", dataProviderTransaction?.Parameters);

			if (prefetches.Contains("UserGroupItems") && this.UserGroupItems.Count > 0)
			{
				var iter = this.UserGroupItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var userGroupItemsItem = x.ObjectsDataSet.GetObject(new GOUserGroupDataObject((System.String)iter.Current.GOGroupName, (System.Guid)iter.Current.GOUserId) { IsNew = false });

					if (userGroupItemsItem == null)
						userGroupItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as GOUserGroupDataObject;

					x.UserGroupItems.Add(userGroupItemsItem);
				}
			}

			if (prefetches.Contains("UserRoleItems") && this.UserRoleItems.Count > 0)
			{
				var iter = this.UserRoleItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var userRoleItemsItem = x.ObjectsDataSet.GetObject(new GOUserRoleDataObject((System.String)iter.Current.GORoleName, (System.Guid)iter.Current.GOUserId) { IsNew = false });

					if (userRoleItemsItem == null)
						userRoleItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as GOUserRoleDataObject;

					x.UserRoleItems.Add(userRoleItemsItem);
				}
			}

			if (prefetches.Contains("CustomerAuditItemsCreated") && this.CustomerAuditItemsCreated.Count > 0)
			{
				var iter = this.CustomerAuditItemsCreated.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var customerAuditItemsCreatedItem = x.ObjectsDataSet.GetObject(new CustomerAuditDataObject((System.Guid)iter.Current.fkCustomerId) { IsNew = false });

					if (customerAuditItemsCreatedItem == null)
						customerAuditItemsCreatedItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerAuditDataObject;

					x.CustomerAuditItemsCreated.Add(customerAuditItemsCreatedItem);
				}
			}

			if (prefetches.Contains("DealerDriver") && this.DealerDriver != null)
			{
				var dealerDriver = x.ObjectsDataSet.GetObject(new DealerDriverDataObject((System.Guid)this.DealerDriver.Id) { IsNew = false });

				if (dealerDriver == null)
					dealerDriver = this.DealerDriver.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DealerDriverDataObject;

				x.SetDealerDriverValue(dealerDriver);
			}

			if (prefetches.Contains("Person") && this.Person != null)
			{
				var person = x.ObjectsDataSet.GetObject(new PersonDataObject((System.Guid)this.Person.Id) { IsNew = false });

				if (person == null)
					person = this.Person.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as PersonDataObject;

				x.SetPersonValue(person);
			}

			if (prefetches.Contains("CustomerAuditItemsDeleted") && this.CustomerAuditItemsDeleted.Count > 0)
			{
				var iter = this.CustomerAuditItemsDeleted.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var customerAuditItemsDeletedItem = x.ObjectsDataSet.GetObject(new CustomerAuditDataObject((System.Guid)iter.Current.fkCustomerId) { IsNew = false });

					if (customerAuditItemsDeletedItem == null)
						customerAuditItemsDeletedItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerAuditDataObject;

					x.CustomerAuditItemsDeleted.Add(customerAuditItemsDeletedItem);
				}
			}

			if (prefetches.Contains("VehicleLockoutItems") && this.VehicleLockoutItems.Count > 0)
			{
				var iter = this.VehicleLockoutItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleLockoutItemsItem = x.ObjectsDataSet.GetObject(new VehicleLockoutDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleLockoutItemsItem == null)
						vehicleLockoutItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleLockoutDataObject;

					x.VehicleLockoutItems.Add(vehicleLockoutItemsItem);
				}
			}

			if (prefetches.Contains("MessageHistoryItems") && this.MessageHistoryItems.Count > 0)
			{
				var iter = this.MessageHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var messageHistoryItemsItem = x.ObjectsDataSet.GetObject(new MessageHistoryDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (messageHistoryItemsItem == null)
						messageHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as MessageHistoryDataObject;

					x.MessageHistoryItems.Add(messageHistoryItemsItem);
				}
			}

			if (prefetches.Contains("RevisionItems") && this.RevisionItems.Count > 0)
			{
				var iter = this.RevisionItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var revisionItemsItem = x.ObjectsDataSet.GetObject(new RevisionDataObject((System.Int32)iter.Current.RevisionId) { IsNew = false });

					if (revisionItemsItem == null)
						revisionItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as RevisionDataObject;

					x.RevisionItems.Add(revisionItemsItem);
				}
			}

			if (prefetches.Contains("ExportJobStatusItems") && this.ExportJobStatusItems.Count > 0)
			{
				var iter = this.ExportJobStatusItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var exportJobStatusItemsItem = x.ObjectsDataSet.GetObject(new ExportJobStatusDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (exportJobStatusItemsItem == null)
						exportJobStatusItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ExportJobStatusDataObject;

					x.ExportJobStatusItems.Add(exportJobStatusItemsItem);
				}
			}

			if (prefetches.Contains("ReportSubscriptionItems") && this.ReportSubscriptionItems.Count > 0)
			{
				var iter = this.ReportSubscriptionItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var reportSubscriptionItemsItem = x.ObjectsDataSet.GetObject(new ReportSubscriptionDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (reportSubscriptionItemsItem == null)
						reportSubscriptionItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ReportSubscriptionDataObject;

					x.ReportSubscriptionItems.Add(reportSubscriptionItemsItem);
				}
			}

			if (prefetches.Contains("AlertSubscriptionItems") && this.AlertSubscriptionItems.Count > 0)
			{
				var iter = this.AlertSubscriptionItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var alertSubscriptionItemsItem = x.ObjectsDataSet.GetObject(new AlertSubscriptionDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (alertSubscriptionItemsItem == null)
						alertSubscriptionItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as AlertSubscriptionDataObject;

					x.AlertSubscriptionItems.Add(alertSubscriptionItemsItem);
				}
			}

			if (prefetches.Contains("CustomerAuditItemsModified") && this.CustomerAuditItemsModified.Count > 0)
			{
				var iter = this.CustomerAuditItemsModified.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var customerAuditItemsModifiedItem = x.ObjectsDataSet.GetObject(new CustomerAuditDataObject((System.Guid)iter.Current.fkCustomerId) { IsNew = false });

					if (customerAuditItemsModifiedItem == null)
						customerAuditItemsModifiedItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerAuditDataObject;

					x.CustomerAuditItemsModified.Add(customerAuditItemsModifiedItem);
				}
			}

			if (prefetches.Contains("GOUserDepartmentItems") && this.GOUserDepartmentItems.Count > 0)
			{
				var iter = this.GOUserDepartmentItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var gOUserDepartmentItemsItem = x.ObjectsDataSet.GetObject(new GOUserDepartmentDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (gOUserDepartmentItemsItem == null)
						gOUserDepartmentItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as GOUserDepartmentDataObject;

					x.GOUserDepartmentItems.Add(gOUserDepartmentItemsItem);
				}
			}

			if (prefetches.Contains("Tag") && this.Tag != null)
			{
				var tag = x.ObjectsDataSet.GetObject(new TagDataObject((System.Guid)this.Tag.TagId) { IsNew = false });

				if (tag == null)
					tag = this.Tag.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as TagDataObject;

				x.SetTagValue(tag);
			}

			if (prefetches.Contains("GoUserToCustomerItems") && this.GoUserToCustomerItems.Count > 0)
			{
				var iter = this.GoUserToCustomerItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var goUserToCustomerItemsItem = x.ObjectsDataSet.GetObject(new GoUserToCustomerDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (goUserToCustomerItemsItem == null)
						goUserToCustomerItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as GoUserToCustomerDataObject;

					x.GoUserToCustomerItems.Add(goUserToCustomerItemsItem);
				}
			}

			if (prefetches.Contains("Dealer") && this.Dealer != null)
			{
				var dealer = x.ObjectsDataSet.GetObject(new DealerDataObject((System.Guid)this.Dealer.Id) { IsNew = false });

				if (dealer == null)
					dealer = this.Dealer.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DealerDataObject;

				x.SetDealerValue(dealer);
			}

		}
		
		// For database filtering, nothing IsNew or IsDirty, but these stubs needed for ORM security predicates to compile
		public virtual bool IsNew => false;
		public virtual bool IsDirty => false;

	}
}