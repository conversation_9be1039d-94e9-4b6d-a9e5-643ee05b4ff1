using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Configuration;
using FleetXQ.Tools.BulkImporter.Logging;
using System.Data;

namespace FleetXQ.Tools.BulkImporter.Services;

/// <summary>
/// Temporary table-based SQL data generation service that avoids creating permanent schema objects
/// </summary>
public class SqlDataGenerationServiceTemp : ISqlDataGenerationServiceTemp
{
    private readonly ILogger<SqlDataGenerationServiceTemp> _logger;
    private readonly BulkImporterOptions _options;
    private readonly DataGenerationOptions _generationOptions;
    private readonly TemporaryTableOptions _tempTableOptions;
    private readonly ConnectionStringOptions _connectionOptions;

    public SqlDataGenerationServiceTemp(
        ILogger<SqlDataGenerationServiceTemp> logger,
        IOptions<BulkImporterOptions> options,
        IOptions<DataGenerationOptions> generationOptions,
        IOptions<TemporaryTableOptions> tempTableOptions,
        IOptions<ConnectionStringOptions> connectionOptions)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _generationOptions = generationOptions?.Value ?? throw new ArgumentNullException(nameof(generationOptions));
        _tempTableOptions = tempTableOptions?.Value ?? throw new ArgumentNullException(nameof(tempTableOptions));
        _connectionOptions = connectionOptions?.Value ?? throw new ArgumentNullException(nameof(connectionOptions));
    }

    public async Task<ImportSessionInfo> CreateImportSessionAsync(string sessionName, CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();
        var sessionInfo = new ImportSessionInfo
        {
            SessionId = sessionId,
            SessionName = sessionName,
            StartTime = DateTime.UtcNow,
            Status = "Running",
            ConnectionString = _connectionOptions.FleetXQConnection
        };

        // Generate unique temporary table names
        var sessionSuffix = sessionId.ToString("N")[..8];
        sessionInfo.DriverTableName = _tempTableOptions.UseGlobalTempTablesForDebugging
            ? $"##DriverImport_{sessionSuffix}"
            : $"#DriverImport_{sessionSuffix}";
        sessionInfo.VehicleTableName = _tempTableOptions.UseGlobalTempTablesForDebugging
            ? $"##VehicleImport_{sessionSuffix}"
            : $"#VehicleImport_{sessionSuffix}";

        // Create temporary tables for this session
        await CreateTemporaryTablesAsync(sessionInfo, cancellationToken);

        _logger.LogInformation("Created import session {SessionId} with temporary tables {DriverTable}, {VehicleTable}",
            sessionId, sessionInfo.DriverTableName, sessionInfo.VehicleTableName);

        return sessionInfo;
    }

    private async Task CreateTemporaryTablesAsync(ImportSessionInfo sessionInfo, CancellationToken cancellationToken)
    {
        using var connection = new SqlConnection(sessionInfo.ConnectionString);
        await connection.OpenAsync(cancellationToken);

        // Create temporary driver table
        var createDriverTableSql = $@"
            CREATE TABLE {sessionInfo.DriverTableName} (
                [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
                [RowNumber] INT NOT NULL,
                [ValidationStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
                [ValidationErrors] NVARCHAR(MAX) NULL,
                
                -- Raw Input Data
                [ExternalDriverId] NVARCHAR(50) NULL,
                [PersonFirstName] NVARCHAR(50) NOT NULL,
                [PersonLastName] NVARCHAR(50) NOT NULL,
                [PersonEmail] NVARCHAR(50) NULL,
                [PersonPhone] NVARCHAR(50) NULL,
                [DriverActive] BIT NULL,
                [DriverLicenseMode] INT NULL,
                [DriverVehicleAccess] BIT NULL,
                [PersonIsActiveDriver] BIT NULL,
                [PersonHasLicense] BIT NULL,
                [PersonLicenseActive] BIT NULL,
                [PersonVehicleAccess] BIT NULL,
                [PersonCanUnlockVehicle] BIT NULL,
                [PersonNormalDriverAccess] BIT NULL,
                [CustomerName] NVARCHAR(100) NOT NULL,
                [SiteName] NVARCHAR(100) NOT NULL,
                [DepartmentName] NVARCHAR(100) NOT NULL,
                [Notes] NVARCHAR(155) NULL,
                
                -- Resolved Foreign Keys
                [CustomerId] UNIQUEIDENTIFIER NULL,
                [SiteId] UNIQUEIDENTIFIER NULL,
                [DepartmentId] UNIQUEIDENTIFIER NULL,
                [ExistingPersonId] UNIQUEIDENTIFIER NULL,
                [ExistingDriverId] UNIQUEIDENTIFIER NULL,
                
                -- Processing Metadata
                [ProcessingAction] NVARCHAR(20) NULL,
                [ProcessedAt] DATETIME2 NULL,
                [ProcessingErrors] NVARCHAR(MAX) NULL
            )";

        // Create temporary vehicle table
        var createVehicleTableSql = $@"
            CREATE TABLE {sessionInfo.VehicleTableName} (
                [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
                [RowNumber] INT NOT NULL,
                [ValidationStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
                [ValidationErrors] NVARCHAR(MAX) NULL,
                
                -- Raw Input Data
                [ExternalVehicleId] NVARCHAR(50) NULL,
                [HireNo] NVARCHAR(50) NOT NULL,
                [SerialNo] NVARCHAR(50) NOT NULL,
                [Description] NVARCHAR(500) NULL,
                [OnHire] BIT NULL,
                [ImpactLockout] BIT NULL,
                [IsCanbus] BIT NULL,
                [TimeoutEnabled] BIT NULL,
                [ModuleIsConnected] BIT NULL,
                [IDLETimer] INT NULL,
                [CustomerName] NVARCHAR(100) NOT NULL,
                [SiteName] NVARCHAR(100) NOT NULL,
                [DepartmentName] NVARCHAR(100) NOT NULL,
                [ModelName] NVARCHAR(100) NOT NULL,
                [ManufacturerName] NVARCHAR(100) NULL,
                [ModuleSerialNumber] NVARCHAR(50) NOT NULL,
                [AssignedDriverEmail] NVARCHAR(50) NULL,
                [AssignedPersonEmail] NVARCHAR(50) NULL,
                
                -- Resolved Foreign Keys
                [CustomerId] UNIQUEIDENTIFIER NULL,
                [SiteId] UNIQUEIDENTIFIER NULL,
                [DepartmentId] UNIQUEIDENTIFIER NULL,
                [ModelId] UNIQUEIDENTIFIER NULL,
                [ModuleId] UNIQUEIDENTIFIER NULL,
                [AssignedDriverId] UNIQUEIDENTIFIER NULL,
                [AssignedPersonId] UNIQUEIDENTIFIER NULL,
                [ExistingVehicleId] UNIQUEIDENTIFIER NULL,
                
                -- Processing Metadata
                [ProcessingAction] NVARCHAR(20) NULL,
                [ProcessedAt] DATETIME2 NULL,
                [ProcessingErrors] NVARCHAR(MAX) NULL
            )";

        using var createDriverCommand = new SqlCommand(createDriverTableSql, connection);
        createDriverCommand.CommandTimeout = _options.CommandTimeout;
        await createDriverCommand.ExecuteNonQueryAsync(cancellationToken);

        using var createVehicleCommand = new SqlCommand(createVehicleTableSql, connection);
        createVehicleCommand.CommandTimeout = _options.CommandTimeout;
        await createVehicleCommand.ExecuteNonQueryAsync(cancellationToken);

        _logger.LogInformation("Created temporary tables {DriverTable} and {VehicleTable} for session {SessionId}",
            sessionInfo.DriverTableName, sessionInfo.VehicleTableName, sessionInfo.SessionId);
    }

    public async Task<DataGenerationResult> GenerateDriverDataAsync(ImportSessionInfo sessionInfo, int count, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new DataGenerationResult();

        try
        {
            _logger.LogInformation("Generating {Count} driver records for session {SessionId} in table {TableName}",
                count, sessionInfo.SessionId, sessionInfo.DriverTableName);

            using var connection = new SqlConnection(sessionInfo.ConnectionString);
            await connection.OpenAsync(cancellationToken);

            var batchSize = _generationOptions.GenerationBatchSize;
            var totalGenerated = 0;

            for (int offset = 0; offset < count; offset += batchSize)
            {
                var currentBatchSize = Math.Min(batchSize, count - offset);
                await GenerateDriverBatchAsync(connection, sessionInfo, offset, currentBatchSize, cancellationToken);
                totalGenerated += currentBatchSize;

                _logger.LogInformation("Generated {CurrentGenerated}/{TotalCount} driver records", totalGenerated, count);
            }

            result.Success = true;
            result.GeneratedRows = totalGenerated;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Successfully generated {totalGenerated} driver records in temporary table";

            return result;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Driver data generation failed: {ex.Message}";

            _logger.LogError(ex, "Driver data generation failed for session {SessionId}", sessionInfo.SessionId);
            throw;
        }
    }

    private async Task GenerateDriverBatchAsync(SqlConnection connection, ImportSessionInfo sessionInfo, int offset, int batchSize, CancellationToken cancellationToken)
    {
        var sql = $@"
            WITH NumberSequence AS (
                SELECT ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset AS RowNum
                FROM master.dbo.spt_values s1
                CROSS JOIN master.dbo.spt_values s2
                WHERE s1.type = 'P' AND s2.type = 'P'
                    AND ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) <= @BatchSize
            ),
            RandomData AS (
                SELECT 
                    RowNum,
                    'EXT_DRV_' + RIGHT('000000' + CAST(RowNum AS VARCHAR), 6) AS ExternalDriverId,
                    CASE ABS(CHECKSUM(NEWID())) % 20
                        WHEN 0 THEN 'John' WHEN 1 THEN 'Jane' WHEN 2 THEN 'Michael' WHEN 3 THEN 'Sarah'
                        WHEN 4 THEN 'David' WHEN 5 THEN 'Lisa' WHEN 6 THEN 'Robert' WHEN 7 THEN 'Emily'
                        WHEN 8 THEN 'James' WHEN 9 THEN 'Jessica' WHEN 10 THEN 'William' WHEN 11 THEN 'Ashley'
                        WHEN 12 THEN 'Richard' WHEN 13 THEN 'Amanda' WHEN 14 THEN 'Charles' WHEN 15 THEN 'Stephanie'
                        WHEN 16 THEN 'Thomas' WHEN 17 THEN 'Jennifer' WHEN 18 THEN 'Christopher' ELSE 'Michelle'
                    END AS FirstName,
                    CASE ABS(CHECKSUM(NEWID())) % 20
                        WHEN 0 THEN 'Smith' WHEN 1 THEN 'Johnson' WHEN 2 THEN 'Williams' WHEN 3 THEN 'Brown'
                        WHEN 4 THEN 'Jones' WHEN 5 THEN 'Garcia' WHEN 6 THEN 'Miller' WHEN 7 THEN 'Davis'
                        WHEN 8 THEN 'Rodriguez' WHEN 9 THEN 'Martinez' WHEN 10 THEN 'Hernandez' WHEN 11 THEN 'Lopez'
                        WHEN 12 THEN 'Gonzalez' WHEN 13 THEN 'Wilson' WHEN 14 THEN 'Anderson' WHEN 15 THEN 'Thomas'
                        WHEN 16 THEN 'Taylor' WHEN 17 THEN 'Moore' WHEN 18 THEN 'Jackson' ELSE 'Martin'
                    END AS LastName,
                    CASE ABS(CHECKSUM(NEWID())) % 3
                        WHEN 0 THEN 'FleetXQ Corp' WHEN 1 THEN 'Transport Solutions' ELSE 'Logistics Inc'
                    END AS CustomerName,
                    CASE ABS(CHECKSUM(NEWID())) % 5
                        WHEN 0 THEN 'Main Site' WHEN 1 THEN 'North Branch' WHEN 2 THEN 'South Branch'
                        WHEN 3 THEN 'East Depot' ELSE 'West Terminal'
                    END AS SiteName,
                    CASE ABS(CHECKSUM(NEWID())) % 4
                        WHEN 0 THEN 'Operations' WHEN 1 THEN 'Maintenance' WHEN 2 THEN 'Logistics' ELSE 'Administration'
                    END AS DepartmentName,
                    CASE WHEN ABS(CHECKSUM(NEWID())) % 10 < 9 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS DriverActive
                FROM NumberSequence
            )
            INSERT INTO {sessionInfo.DriverTableName} (
                [RowNumber], [ExternalDriverId], [PersonFirstName], [PersonLastName],
                [PersonEmail], [DriverActive], [DriverLicenseMode], [DriverVehicleAccess],
                [PersonIsActiveDriver], [PersonHasLicense], [PersonLicenseActive], [PersonVehicleAccess],
                [PersonCanUnlockVehicle], [PersonNormalDriverAccess], [CustomerName], [SiteName], [DepartmentName]
            )
            SELECT 
                RowNum,
                ExternalDriverId,
                FirstName,
                LastName,
                LOWER(FirstName + '.' + LastName + '@' + REPLACE(CustomerName, ' ', '') + '.com'),
                DriverActive,
                ABS(CHECKSUM(NEWID())) % 3,
                DriverActive,
                DriverActive,
                DriverActive,
                DriverActive,
                DriverActive,
                DriverActive,
                DriverActive,
                CustomerName,
                SiteName,
                DepartmentName
            FROM RandomData";

        using var command = new SqlCommand(sql, connection);
        command.CommandTimeout = _options.CommandTimeout;
        command.Parameters.AddWithValue("@Offset", offset);
        command.Parameters.AddWithValue("@BatchSize", batchSize);

        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    public async Task<DataGenerationResult> GenerateVehicleDataAsync(ImportSessionInfo sessionInfo, int count, CancellationToken cancellationToken = default)
    {
        // Similar implementation to GenerateDriverDataAsync but for vehicles
        // Using sessionInfo.VehicleTableName instead of permanent staging table

        var startTime = DateTime.UtcNow;
        var result = new DataGenerationResult();

        try
        {
            _logger.LogInformation("Generating {Count} vehicle records for session {SessionId} in table {TableName}",
                count, sessionInfo.SessionId, sessionInfo.VehicleTableName);

            using var connection = new SqlConnection(sessionInfo.ConnectionString);
            await connection.OpenAsync(cancellationToken);

            var batchSize = _generationOptions.GenerationBatchSize;
            var totalGenerated = 0;

            for (int offset = 0; offset < count; offset += batchSize)
            {
                var currentBatchSize = Math.Min(batchSize, count - offset);
                await GenerateVehicleBatchAsync(connection, sessionInfo, offset, currentBatchSize, cancellationToken);
                totalGenerated += currentBatchSize;

                _logger.LogInformation("Generated {CurrentGenerated}/{TotalCount} vehicle records", totalGenerated, count);
            }

            result.Success = true;
            result.GeneratedRows = totalGenerated;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Successfully generated {totalGenerated} vehicle records in temporary table";

            return result;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Vehicle data generation failed: {ex.Message}";

            _logger.LogError(ex, "Vehicle data generation failed for session {SessionId}", sessionInfo.SessionId);
            throw;
        }
    }

    private async Task GenerateVehicleBatchAsync(SqlConnection connection, ImportSessionInfo sessionInfo, int offset, int batchSize, CancellationToken cancellationToken)
    {
        // Implementation similar to driver generation but for vehicles
        // Insert into sessionInfo.VehicleTableName instead of permanent table

        var sql = $@"
            WITH NumberSequence AS (
                SELECT ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset AS RowNum
                FROM master.dbo.spt_values s1
                CROSS JOIN master.dbo.spt_values s2
                WHERE s1.type = 'P' AND s2.type = 'P'
                    AND ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) <= @BatchSize
            ),
            RandomData AS (
                SELECT 
                    RowNum,
                    'EXT_VEH_' + RIGHT('000000' + CAST(RowNum AS VARCHAR), 6) AS ExternalVehicleId,
                    'FLT' + RIGHT('00000' + CAST(RowNum AS VARCHAR), 5) AS HireNo,
                    'SN' + RIGHT('00000' + CAST(RowNum + 50000 AS VARCHAR), 5) AS SerialNo,
                    CASE ABS(CHECKSUM(NEWID())) % 5
                        WHEN 0 THEN 'Forklift - Warehouse' WHEN 1 THEN 'Reach Truck - High Bay'
                        WHEN 2 THEN 'Pallet Truck - Floor Level' WHEN 3 THEN 'Order Picker - Multi Level'
                        ELSE 'Counterbalance - Outdoor'
                    END AS Description,
                    CASE ABS(CHECKSUM(NEWID())) % 3
                        WHEN 0 THEN 'FleetXQ Corp' WHEN 1 THEN 'Transport Solutions' ELSE 'Logistics Inc'
                    END AS CustomerName,
                    CASE ABS(CHECKSUM(NEWID())) % 5
                        WHEN 0 THEN 'Main Site' WHEN 1 THEN 'North Branch' WHEN 2 THEN 'South Branch'
                        WHEN 3 THEN 'East Depot' ELSE 'West Terminal'
                    END AS SiteName,
                    CASE ABS(CHECKSUM(NEWID())) % 4
                        WHEN 0 THEN 'Operations' WHEN 1 THEN 'Maintenance' WHEN 2 THEN 'Logistics' ELSE 'Administration'
                    END AS DepartmentName,
                    CASE ABS(CHECKSUM(NEWID())) % 4
                        WHEN 0 THEN 'Linde H25' WHEN 1 THEN 'Toyota 8FB' WHEN 2 THEN 'Crown FC5200'
                        ELSE 'Hyster J2.0XN'
                    END AS ModelName,
                    'MOD' + RIGHT('00000' + CAST(RowNum + 10000 AS VARCHAR), 5) AS ModuleSerialNumber,
                    CASE WHEN ABS(CHECKSUM(NEWID())) % 10 < 9 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS OnHire
                FROM NumberSequence
            )
            INSERT INTO {sessionInfo.VehicleTableName} (
                [RowNumber], [ExternalVehicleId], [HireNo], [SerialNo], [Description],
                [OnHire], [ImpactLockout], [IsCanbus], [TimeoutEnabled], [ModuleIsConnected], [IDLETimer],
                [CustomerName], [SiteName], [DepartmentName], [ModelName], [ManufacturerName], [ModuleSerialNumber]
            )
            SELECT 
                RowNum,
                ExternalVehicleId,
                HireNo,
                SerialNo,
                Description,
                OnHire,
                CASE WHEN ABS(CHECKSUM(NEWID())) % 10 < 2 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END,
                CASE WHEN ABS(CHECKSUM(NEWID())) % 10 < 8 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END,
                CAST(1 AS BIT),
                OnHire,
                CASE WHEN OnHire = 1 THEN 300 ELSE 600 END,
                CustomerName,
                SiteName,
                DepartmentName,
                ModelName,
                'Linde',
                ModuleSerialNumber
            FROM RandomData";

        using var command = new SqlCommand(sql, connection);
        command.CommandTimeout = _options.CommandTimeout;
        command.Parameters.AddWithValue("@Offset", offset);
        command.Parameters.AddWithValue("@BatchSize", batchSize);

        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    public async Task<ValidationResult> ValidateStagedDataAsync(ImportSessionInfo sessionInfo, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult();

        using var connection = new SqlConnection(sessionInfo.ConnectionString);
        await connection.OpenAsync(cancellationToken);

        // Dynamic validation SQL using temporary table names
        var validationSql = $@"
            DECLARE @ValidRows INT = 0;
            DECLARE @InvalidRows INT = 0;
            
            -- Validate driver data
            UPDATE {sessionInfo.DriverTableName} 
            SET [ValidationStatus] = 'Valid',
                [ValidationErrors] = NULL
            WHERE [PersonFirstName] IS NOT NULL 
                AND [PersonLastName] IS NOT NULL
                AND [CustomerName] IS NOT NULL
                AND [SiteName] IS NOT NULL
                AND [DepartmentName] IS NOT NULL;
            
            -- Mark invalid driver records
            UPDATE {sessionInfo.DriverTableName} 
            SET [ValidationStatus] = 'Invalid',
                [ValidationErrors] = 'Missing required fields'
            WHERE [PersonFirstName] IS NULL OR [PersonLastName] IS NULL OR 
                  [CustomerName] IS NULL OR [SiteName] IS NULL OR [DepartmentName] IS NULL;
            
            -- Validate vehicle data
            UPDATE {sessionInfo.VehicleTableName} 
            SET [ValidationStatus] = 'Valid',
                [ValidationErrors] = NULL
            WHERE [HireNo] IS NOT NULL 
                AND [SerialNo] IS NOT NULL
                AND [CustomerName] IS NOT NULL
                AND [SiteName] IS NOT NULL
                AND [DepartmentName] IS NOT NULL
                AND [ModelName] IS NOT NULL
                AND [ModuleSerialNumber] IS NOT NULL;
            
            -- Mark invalid vehicle records
            UPDATE {sessionInfo.VehicleTableName} 
            SET [ValidationStatus] = 'Invalid',
                [ValidationErrors] = 'Missing required fields'
            WHERE [HireNo] IS NULL OR [SerialNo] IS NULL OR 
                  [CustomerName] IS NULL OR [SiteName] IS NULL OR [DepartmentName] IS NULL OR
                  [ModelName] IS NULL OR [ModuleSerialNumber] IS NULL;
            
            -- Get validation counts
            SELECT 
                @ValidRows = SUM(CASE WHEN [ValidationStatus] = 'Valid' THEN 1 ELSE 0 END),
                @InvalidRows = SUM(CASE WHEN [ValidationStatus] = 'Invalid' THEN 1 ELSE 0 END)
            FROM (
                SELECT [ValidationStatus] FROM {sessionInfo.DriverTableName}
                UNION ALL
                SELECT [ValidationStatus] FROM {sessionInfo.VehicleTableName}
            ) combined;
            
            -- Return validation summary
            SELECT 
                @ValidRows AS ValidRows,
                @InvalidRows AS InvalidRows;";

        using var command = new SqlCommand(validationSql, connection);
        command.CommandTimeout = _options.CommandTimeout;

        using var reader = await command.ExecuteReaderAsync(cancellationToken);

        if (await reader.ReadAsync(cancellationToken))
        {
            result.ValidRows = reader.GetInt32("ValidRows");
            result.InvalidRows = reader.GetInt32("InvalidRows");
            result.Success = result.InvalidRows == 0;
            result.Summary = $"Validation completed: {result.ValidRows} valid, {result.InvalidRows} invalid";
        }

        _logger.LogInformation("Data validation completed for session {SessionId}: {ValidRows} valid, {InvalidRows} invalid",
            sessionInfo.SessionId, result.ValidRows, result.InvalidRows);

        return result;
    }

    public async Task<ProcessingResult> ProcessStagedDataAsync(ImportSessionInfo sessionInfo, bool dryRun = false, CancellationToken cancellationToken = default)
    {
        var result = new ProcessingResult();

        using var connection = new SqlConnection(sessionInfo.ConnectionString);
        await connection.OpenAsync(cancellationToken);

        if (dryRun)
        {
            // Dry run: just count what would be processed
            var countSql = $@"
                SELECT 
                    COUNT(*) AS ProcessedRows,
                    COUNT(*) AS InsertedRows,
                    0 AS UpdatedRows,
                    0 AS SkippedRows
                FROM (
                    SELECT ValidationStatus FROM {sessionInfo.DriverTableName} WHERE ValidationStatus = 'Valid'
                    UNION ALL
                    SELECT ValidationStatus FROM {sessionInfo.VehicleTableName} WHERE ValidationStatus = 'Valid'
                ) combined";

            using var countCommand = new SqlCommand(countSql, connection);
            countCommand.CommandTimeout = _options.CommandTimeout;

            using var reader = await countCommand.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync(cancellationToken))
            {
                result.ProcessedRows = reader.GetInt32("ProcessedRows");
                result.InsertedRows = reader.GetInt32("InsertedRows");
                result.UpdatedRows = reader.GetInt32("UpdatedRows");
                result.SkippedRows = reader.GetInt32("SkippedRows");
                result.Success = true;
                result.Summary = $"Dry run: Would process {result.ProcessedRows} records";
            }
        }
        else
        {
            // Production processing: merge to actual production tables
            var processSql = $@"
                -- Mark records as processed (simplified example)
                UPDATE {sessionInfo.DriverTableName} 
                SET [ValidationStatus] = 'Processed',
                    [ProcessedAt] = GETUTCDATE(),
                    [ProcessingAction] = 'Insert'
                WHERE [ValidationStatus] = 'Valid';
                
                DECLARE @DriverRows INT = @@ROWCOUNT;
                
                UPDATE {sessionInfo.VehicleTableName} 
                SET [ValidationStatus] = 'Processed',
                    [ProcessedAt] = GETUTCDATE(),
                    [ProcessingAction] = 'Insert'
                WHERE [ValidationStatus] = 'Valid';
                
                DECLARE @VehicleRows INT = @@ROWCOUNT;
                
                -- Return processing summary
                SELECT 
                    (@DriverRows + @VehicleRows) AS ProcessedRows,
                    (@DriverRows + @VehicleRows) AS InsertedRows,
                    0 AS UpdatedRows,
                    0 AS SkippedRows";

            using var processCommand = new SqlCommand(processSql, connection);
            processCommand.CommandTimeout = _options.CommandTimeout;

            using var reader = await processCommand.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync(cancellationToken))
            {
                result.ProcessedRows = reader.GetInt32("ProcessedRows");
                result.InsertedRows = reader.GetInt32("InsertedRows");
                result.UpdatedRows = reader.GetInt32("UpdatedRows");
                result.SkippedRows = reader.GetInt32("SkippedRows");
                result.Success = true;
                result.Summary = $"Processing completed: {result.InsertedRows} inserted, {result.UpdatedRows} updated, {result.SkippedRows} skipped";
            }
        }

        _logger.LogInformation("Data processing completed for session {SessionId}: {InsertedRows} inserted, {UpdatedRows} updated, {SkippedRows} skipped",
            sessionInfo.SessionId, result.InsertedRows, result.UpdatedRows, result.SkippedRows);

        return result;
    }

    public async Task CompleteImportSessionAsync(ImportSessionInfo sessionInfo, string finalStatus, CancellationToken cancellationToken = default)
    {
        sessionInfo.Status = finalStatus;

        if (_tempTableOptions.ExplicitCleanup)
        {
            // Explicitly drop temporary tables (optional - they'll auto-drop when connection closes)
            using var connection = new SqlConnection(sessionInfo.ConnectionString);
            await connection.OpenAsync(cancellationToken);

            try
            {
                var dropSql = $@"
                    IF OBJECT_ID('tempdb..{sessionInfo.DriverTableName}') IS NOT NULL
                        DROP TABLE {sessionInfo.DriverTableName};
                    
                    IF OBJECT_ID('tempdb..{sessionInfo.VehicleTableName}') IS NOT NULL
                        DROP TABLE {sessionInfo.VehicleTableName};";

                using var dropCommand = new SqlCommand(dropSql, connection);
                await dropCommand.ExecuteNonQueryAsync(cancellationToken);

                _logger.LogInformation("Explicitly dropped temporary tables for session {SessionId}", sessionInfo.SessionId);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to explicitly drop temporary tables for session {SessionId} - they will auto-drop on connection close", sessionInfo.SessionId);
            }
        }

        _logger.LogInformation("Completed import session {SessionId} with status {Status}", sessionInfo.SessionId, finalStatus);
    }
}
