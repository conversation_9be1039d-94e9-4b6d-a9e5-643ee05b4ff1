﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge DataObject -> ORMEntity
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	public partial class AlertHistoryDataObject
	{
		protected IThreadContext _threadContext => _serviceProvider.GetRequiredService<IThreadContext>();
		protected INHibernateSessionController _nhibernateSessionController => _serviceProvider.GetRequiredService<INHibernateSessionController>();

		public override IORMEntity ToORMEntity()
		{
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);

			var result = new ORMAlertHistory(); 

			CopyTo(result, session);

			// Ensure no copy of result (instance with same key) in session
			Evict(result, session);

			return result;
		}

		protected void CopyTo(ORMAlertHistory x, NHibernate.ISession session)
		{
			x.Id = Id;
			x.Description = Description?.Truncate(1000);
			x.CreatedDateTime = CreatedDateTime;
			x.IsResolved = IsResolved;
			x.IsAcknowledged = IsAcknowledged;
				
			x.Alert = this.Alert != null ? session.Load<ORMAlert>(this.Alert.Id) : (this.AlertId != null ? session.Load<ORMAlert>(this.AlertId) : null);
			x.AlertId = this.Alert != null ? this.Alert.Id : AlertId; 
				
			x.Vehicle = this.Vehicle != null ? session.Load<ORMVehicle>(this.Vehicle.Id) : (this.VehicleId != null ? session.Load<ORMVehicle>(this.VehicleId) : null);
			x.VehicleId = this.Vehicle != null ? this.Vehicle.Id : VehicleId; 
				
			x.Driver = this.Driver != null ? session.Load<ORMDriver>(this.Driver.Id) : (this.DriverId != null ? session.Load<ORMDriver>(this.DriverId) : null);
			x.DriverId = this.Driver != null ? this.Driver.Id : DriverId; 
		}
 
		private void Evict(ORMAlertHistory result, NHibernate.ISession session)
		{
			foreach (var entity in session.GetSessionImplementation().PersistenceContext.EntitiesByKey.Values.ToArray())
			{
				var evictee = entity as ORMAlertHistory;

				if (evictee != null && evictee.Id == result.Id)
				{
					session.Evict(evictee);
				}
			}
		}

	}
}