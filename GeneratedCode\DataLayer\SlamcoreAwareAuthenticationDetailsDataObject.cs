﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'SlamcoreAwareAuthenticationDetails'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class SlamcoreAwareAuthenticationDetailsDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("Password")]
		protected System.String _password;
		[JsonProperty ("PasswordDisplay")]
		protected System.String _passwordDisplay;
		[JsonProperty ("Username")]
		protected System.String _username;
		[JsonProperty ("UsernameDisplay")]
		protected System.String _usernameDisplay;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)

		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public SlamcoreAwareAuthenticationDetailsDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public SlamcoreAwareAuthenticationDetailsDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public SlamcoreAwareAuthenticationDetailsDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public SlamcoreAwareAuthenticationDetailsDataObject Initialize(SlamcoreAwareAuthenticationDetailsDataObject template, bool deepCopy)
		{
			this.SetIdValue(template.Id, false, false);
			this.SetPasswordValue(template.Password, false, false);
			this.SetPasswordDisplayValue(template.PasswordDisplay, false, false);
			this.SetUsernameValue(template.Username, false, false);
			this.SetUsernameDisplayValue(template.UsernameDisplay, false, false);
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual SlamcoreAwareAuthenticationDetailsDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual SlamcoreAwareAuthenticationDetailsDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var slamcoreAwareAuthenticationDetailsSource = sourceObject as SlamcoreAwareAuthenticationDetailsDataObject;

			if (ReferenceEquals(null, slamcoreAwareAuthenticationDetailsSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetIdValue(slamcoreAwareAuthenticationDetailsSource.Id, false, false);
			this.SetPasswordValue(slamcoreAwareAuthenticationDetailsSource.Password, false, false);
			this.SetPasswordDisplayValue(slamcoreAwareAuthenticationDetailsSource.PasswordDisplay, false, false);
			this.SetUsernameValue(slamcoreAwareAuthenticationDetailsSource.Username, false, false);
			this.SetUsernameDisplayValue(slamcoreAwareAuthenticationDetailsSource.UsernameDisplay, false, false);

			if (deepCopy)
			{
				this.ObjectsDataSet = slamcoreAwareAuthenticationDetailsSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(slamcoreAwareAuthenticationDetailsSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as SlamcoreAwareAuthenticationDetailsDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {

		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<SlamcoreDeviceDataObject> _slamcoreDeviceService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceDataObject>>();
      public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet)
		{
			SetSlamcoreDeviceValue(valueToSet, true, true);
		}

        public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<SlamcoreDeviceDataObject>(this, "SlamcoreDevice");
			var existing_slamcoreDevice = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("SlamcoreDevice", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._slamcoreAwareAuthenticationDetails_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.SlamcoreAwareAuthenticationDetails = this;
					valueToSet.SlamcoreAwareAuthenticationDetailsId = this.Id;
				}			
			}
			else  if (existing_slamcoreDevice != null)
            {
                ObjectsDataSet.RemoveObject(existing_slamcoreDevice);
            }
			if (!ReferenceEquals(existing_slamcoreDevice ,valueToSet))
				OnPropertyChanged("SlamcoreDevice", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __slamcoreDeviceSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreDeviceAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreDevice", which is a SlamcoreDeviceDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SlamcoreDeviceDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreDeviceAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreDeviceSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreDeviceAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data SlamcoreDevice for the current entity. The DataObjects doesn't have an ObjectsDataSet", "SlamcoreAwareAuthenticationDetailsObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var slamcoreDevice = (this.ObjectsDataSet as ObjectsDataSet).SlamcoreDeviceObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).SlamcoreDeviceObjects.Where(item => item.Value.SlamcoreAwareAuthenticationDetailsId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(slamcoreDevice, null))
					{
						var filterPredicate = "SlamcoreAwareAuthenticationDetailsId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						slamcoreDevice = (await _slamcoreDeviceService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetSlamcoreDeviceValue(slamcoreDevice, false, false);
						__slamcoreDeviceAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a slamcoreDevice, but relation field not set, encourage it to get set by removing and re-adding the slamcoreDevice 
					if (slamcoreDevice != null && this.SlamcoreDevice == null)
					{
						this.ObjectsDataSet.RemoveObject(slamcoreDevice);
						this.ObjectsDataSet.AddObject(slamcoreDevice);
					}			
                    __slamcoreDeviceAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreDeviceAsync(false);
            }
            finally
            {
                __slamcoreDeviceSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SlamcoreDeviceDataObject SlamcoreDevice 
		{
			get
			{			
				return GetSlamcoreDeviceAsync(true).Result;
			}
			set
			{
				SetSlamcoreDeviceValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreDevice()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreAwareAuthenticationDetailsDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreAwareAuthenticationDetailsDataObject"].Contains("SlamcoreDevice");
		}

		public virtual async Task<SlamcoreDeviceDataObject> GetSlamcoreDeviceAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SlamcoreDeviceDataObject slamcoreDevice;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<SlamcoreDeviceDataObject>(this, "SlamcoreDevice");
               	slamcoreDevice = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && slamcoreDevice == null && LazyLoadingEnabled && (!__slamcoreDeviceAlreadyLazyLoaded || forceReload))
				{
					slamcoreDevice = await LoadSlamcoreDeviceAsync(forceReload : forceReload);
				}
			}
				
			return slamcoreDevice;
		}


		public override void ClearLazyLoadFlags()
		{
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadSlamcoreDeviceAsync()) != null)
				result.Add(SlamcoreDevice);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return false;
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the SlamcoreAwareAuthenticationDetails DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetPasswordValue(System.String valueToSet)
		{
			SetPasswordValue(valueToSet, true, true);
		}

		public virtual void SetPasswordValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_password != valueToSet)
			{
				_password = valueToSet;

				OnPropertyChanged("Password", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Password property of the SlamcoreAwareAuthenticationDetails DataObject</summary>
        public virtual System.String Password 
		{
			get	{ return String.IsNullOrEmpty(_password) ? null : _password; }
			
			
			set
			{
				SetPasswordValue(value);
			}
		}		
			
			
		public virtual void SetPasswordDisplayValue(System.String valueToSet)
		{
			SetPasswordDisplayValue(valueToSet, true, true);
		}

		public virtual void SetPasswordDisplayValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_passwordDisplay != valueToSet)
			{
				_passwordDisplay = valueToSet;

				OnPropertyChanged("PasswordDisplay", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The PasswordDisplay property of the SlamcoreAwareAuthenticationDetails DataObject</summary>
        public virtual System.String PasswordDisplay 
		{
			get	{ return String.IsNullOrEmpty(_passwordDisplay) ? null : _passwordDisplay; }
			
			
			set
			{
				SetPasswordDisplayValue(value);
			}
		}		
			
			
		public virtual void SetUsernameValue(System.String valueToSet)
		{
			SetUsernameValue(valueToSet, true, true);
		}

		public virtual void SetUsernameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_username != valueToSet)
			{
				_username = valueToSet;

				OnPropertyChanged("Username", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Username property of the SlamcoreAwareAuthenticationDetails DataObject</summary>
        public virtual System.String Username 
		{
			get	{ return String.IsNullOrEmpty(_username) ? null : _username; }
			
			
			set
			{
				SetUsernameValue(value);
			}
		}		
			
			
		public virtual void SetUsernameDisplayValue(System.String valueToSet)
		{
			SetUsernameDisplayValue(valueToSet, true, true);
		}

		public virtual void SetUsernameDisplayValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_usernameDisplay != valueToSet)
			{
				_usernameDisplay = valueToSet;

				OnPropertyChanged("UsernameDisplay", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The UsernameDisplay property of the SlamcoreAwareAuthenticationDetails DataObject</summary>
        public virtual System.String UsernameDisplay 
		{
			get	{ return String.IsNullOrEmpty(_usernameDisplay) ? null : _usernameDisplay; }
			
			
			set
			{
				SetUsernameDisplayValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
			var slamcoreDevice = GetSlamcoreDeviceAsync(false).Result;
			if (slamcoreDevice != null && this.IsDirty)
            {
				slamcoreDevice.NotifyPropertyChanged("SlamcoreAwareAuthenticationDetails." + propertyName, callers);
			}
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is SlamcoreAwareAuthenticationDetailsDataObject))
				return false;

			var p = (SlamcoreAwareAuthenticationDetailsDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.Password == p.Password;
			fieldsComparison &= this.PasswordDisplay == p.PasswordDisplay;
			fieldsComparison &= this.Username == p.Username;
			fieldsComparison &= this.UsernameDisplay == p.UsernameDisplay;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreAwareAuthenticationDetailsCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public SlamcoreAwareAuthenticationDetailsCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public SlamcoreAwareAuthenticationDetailsCollectionContainer()
		{
		}
		
		public SlamcoreAwareAuthenticationDetailsCollectionContainer Construct(DataObjectCollection<SlamcoreAwareAuthenticationDetailsDataObject> slamcoreAwareAuthenticationDetailsItems)
        {
            if (slamcoreAwareAuthenticationDetailsItems == null)
                return this;
				
			this.PrimaryKeys = slamcoreAwareAuthenticationDetailsItems.Select(c => c.PrimaryKey).ToList();
            if (slamcoreAwareAuthenticationDetailsItems.ObjectsDataSet == null)
            {
                slamcoreAwareAuthenticationDetailsItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = slamcoreAwareAuthenticationDetailsItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = slamcoreAwareAuthenticationDetailsItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<SlamcoreAwareAuthenticationDetailsDataObject> ExtractSlamcoreAwareAuthenticationDetailsItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<SlamcoreAwareAuthenticationDetailsDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<SlamcoreAwareAuthenticationDetailsDataObject>(typeof(SlamcoreAwareAuthenticationDetailsDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreAwareAuthenticationDetailsContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public SlamcoreAwareAuthenticationDetailsContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual SlamcoreAwareAuthenticationDetailsContainer Construct(SlamcoreAwareAuthenticationDetailsDataObject slamcoreAwareAuthenticationDetails, bool includeDirtyObjectsOnly = false)
		{
            if (slamcoreAwareAuthenticationDetails == null)
                return this;

			this.PrimaryKey = slamcoreAwareAuthenticationDetails.PrimaryKey;
			
            if (slamcoreAwareAuthenticationDetails.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(slamcoreAwareAuthenticationDetails);
            }

			if(slamcoreAwareAuthenticationDetails.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity SlamcoreAwareAuthenticationDetails", "Unable to set a dataset to the entity. Container may not be initialized", "SlamcoreAwareAuthenticationDetailsDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : SlamcoreAwareAuthenticationDetails");
			}

			if(slamcoreAwareAuthenticationDetails.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in SlamcoreAwareAuthenticationDetailsDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "SlamcoreAwareAuthenticationDetailsDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in SlamcoreAwareAuthenticationDetailsDataObject");
			}
			this.InternalObjectId = (int) slamcoreAwareAuthenticationDetails.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? slamcoreAwareAuthenticationDetails.ObjectsDataSet.CloneDirtyObjects() : slamcoreAwareAuthenticationDetails.ObjectsDataSet;

			return this;
		}
		
		public SlamcoreAwareAuthenticationDetailsDataObject ExtractSlamcoreAwareAuthenticationDetails()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<SlamcoreAwareAuthenticationDetailsDataObject>(typeof(SlamcoreAwareAuthenticationDetailsDataObject), InternalObjectId);
			
			return result;
        }	
	}

}