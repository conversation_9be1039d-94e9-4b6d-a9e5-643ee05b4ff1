# FleetXQ BulkImporter - First-Time Setup

## Quick Setup Guide

### 1. Prerequisites
- .NET 6.0 SDK or later
- SQL Server with FleetXQ database
- Database permissions to create schemas, tables, and procedures

### 2. Initial Setup

1. **Clone and Build**
   ```bash
   cd Tools/BulkImporter
   dotnet restore
   dotnet build
   ```

2. **Configure Database Connection**
   - Edit `appsettings.json`
   - Update the connection string:
     ```json
     "ConnectionStrings": {
       "FleetXQConnection": "Server=YOUR_SERVER;Database=FleetXQ;Trusted_Connection=true;"
     }
     ```

3. **Setup Database Objects**
   Run the SQL scripts in order:
   ```sql
   -- 1. Create staging schema
   -- Run: 001-CreateStagingSchema.sql
   
   -- 2. Create staging tables
   -- Run: 002-CreateDriverStagingTable.sql
   -- Run: 003-CreateVehicleStagingTable.sql
   
   -- 3. Create stored procedures
   -- Run: 004-CreateValidationProcedures.sql
   -- Run: 005-CreateMergeProcedures.sql
   -- Run: 006-CreateDataGenerationProcedures.sql
   ```

### 3. First Run

Test the setup with a dry run:
```bash
dotnet run -- --generate --dry-run --drivers 10 --vehicles 5
```

### 4. Production Use

Once tested, run without `--dry-run`:
```bash
# Interactive mode
dotnet run

# Or non-interactive
dotnet run -- --drivers 1000 --vehicles 500 --non-interactive
```

## File Structure

Essential files for operation:
```
Tools/BulkImporter/
├── Configuration/
├── Logging/
├── Services/
├── Sql/                    # Database setup scripts
├── appsettings.json        # Main configuration
├── BulkImporter.csproj     # Project file
├── Program.cs              # Entry point
└── README.md               # User guide
```

## Troubleshooting

- **Connection Issues**: Verify connection string in `appsettings.json`
- **Permission Issues**: Ensure database user can create schemas and tables
- **Build Issues**: Run `dotnet restore` and check .NET SDK version
- **Runtime Issues**: Check logs in `logs/` directory
