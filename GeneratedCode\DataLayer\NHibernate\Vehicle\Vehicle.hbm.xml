﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMVehicle" 
		table="[Vehicle]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="DehireTime" >
            <column name="`DehireTime`" sql-type="datetime" not-null="false" />
        </property> 
		<property name="DeletedAtUtc" >
            <column name="`DeletedAtUtc`" sql-type="datetime" not-null="false" />
        </property> 
		<property name="Description" >
            <column name="`Description`" sql-type="nvarchar (500) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="HireNo" >
            <column name="`HireNo`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 
		<property name="HireTime" >
            <column name="`HireTime`" sql-type="datetime" not-null="false" />
        </property> 
		<property name="IDLETimer" >
            <column name="`IDLETimer`" sql-type="int" not-null="false" />
        </property> 
		<property name="ImpactLockout" >
            <column name="`ImpactLockout`" sql-type="bit" not-null="true" />
        </property> 
		<property name="IsCanbus" >
            <column name="`IsCanbus`" sql-type="bit" not-null="true" />
        </property> 
		<property name="LastSessionDate" >
            <column name="`LastSessionDate`" sql-type="datetime" not-null="false" />
        </property> 
		<property name="LastSessionDateTzAdjusted" >
            <column name="`LastSessionDateTzAdjusted`" sql-type="datetime" not-null="false" />
        </property> 
		<property name="LastSessionId" >
            <column name="`LastSessionId`" sql-type="nvarchar (200) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="ModuleIsConnected" >
            <column name="`ModuleIsConnected`" sql-type="bit" not-null="true" />
        </property> 
		<property name="ModuleSwapNote" >
            <column name="`ModuleSwapNote`" sql-type="nvarchar (300) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="OnHire" >
            <column name="`OnHire`" sql-type="bit" not-null="true" />
        </property> 
		<property name="SerialNo" >
            <column name="`SerialNo`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 
		<property name="TimeoutEnabled" >
            <column name="`TimeoutEnabled`" sql-type="bit" not-null="true" />
        </property> 
		<property name="VehicleImage" >
            <column name="`VehicleImage`" sql-type="nvarchar(1000)" not-null="false" />
        </property> 
		<property name="VehicleImageFileSize" >
            <column name="`VehicleImageFileSize`" sql-type="int" not-null="false" />
        </property> 
		<property name="VehicleImageInternalName" >
            <column name="`VehicleImageInternalName`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 

		
		<!-- many-to-one Canrule -->
		<property name="CanruleId" type="System.Guid" not-null="false" formula = "[CanruleId]"></property>  
		<many-to-one name="Canrule"  > 
			<column name="`CanruleId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
		
		<!-- many-to-one Firmware -->
		<property name="FirmwareId" type="System.Guid" not-null="false" formula = "[FirmwareId]"></property>  
		<many-to-one name="Firmware"  > 
			<column name="`FirmwareId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
		
		<!-- many-to-one Department -->
		<property name="DepartmentId" type="System.Guid" not-null="true" formula = "[DepartmentId]"></property>  
		<many-to-one name="Department"  > 
			<column name="`DepartmentId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
		
		<!-- many-to-one Customer -->
		<property name="CustomerId" type="System.Guid" not-null="true" formula = "[CustomerId]"></property>  
		<many-to-one name="Customer"  > 
			<column name="`CustomerId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
		
		<!-- many-to-one Model -->
		<property name="ModelId" type="System.Guid" not-null="true" formula = "[ModelId]"></property>  
		<many-to-one name="Model"  > 
			<column name="`ModelId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
		
		<!-- many-to-one Person -->
		<property name="PersonId" type="System.Guid" not-null="false" formula = "[PersonId]"></property>  
		<many-to-one name="Person"  > 
			<column name="`PersonId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
		
		<!-- many-to-one Site -->
		<property name="SiteId" type="System.Guid" not-null="true" formula = "[SiteId]"></property>  
		<many-to-one name="Site"  > 
			<column name="`SiteId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
		
		<!-- many-to-one DepartmentChecklist -->
		<property name="DepartmentChecklistId" type="System.Guid" not-null="false" formula = "[DepartmentChecklistId]"></property>  
		<many-to-one name="DepartmentChecklist"  > 
			<column name="`DepartmentChecklistId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
 

		<!-- one-to-one VehicleLastGPSLocationView primary key association is mapped as a many-to-one to avoid eager fetch (see code template for more info) -->
		<many-to-one
			name="VehicleLastGPSLocationView" 
			class="ORMVehicleLastGPSLocationView" 
			column="Id" 
			unique="true"
			update="false"
			insert="false"
		>
		</many-to-one>
		<one-to-one 
			name="OnDemandSettings" 
			class="ORMOnDemandSettings" 
			lazy="no-proxy"
			property-ref = "Vehicle"
		>
		</one-to-one>
		<one-to-one 
			name="VehicleDiagnostic" 
			class="ORMVehicleDiagnostic" 
			lazy="no-proxy"
			property-ref = "Vehicle"
		>
		</one-to-one>

		<property name="DriverId" type="System.Guid" not-null = "false" formula = "[DriverId]"></property>
 
		<!-- Vehicle.Driver one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="Driver" unique="true"  > 
				<column name="`DriverId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 

		<property name="ServiceSettingsId" type="System.Guid" not-null = "false" formula = "[ServiceSettingsId]"></property>
 
		<!-- Vehicle.ServiceSettings one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="ServiceSettings" unique="true"  > 
				<column name="`ServiceSettingsId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 

		<property name="ModuleId1" type="System.Guid" not-null = "true" formula = "[ModuleId1]"></property>
 
		<!-- Vehicle.Module one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="Module" unique="true"  > 
				<column name="`ModuleId1`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 

		<property name="VehicleOtherSettingsId" type="System.Guid" not-null = "false" formula = "[VehicleOtherSettingsId]"></property>
 
		<!-- Vehicle.VehicleOtherSettings one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="VehicleOtherSettings" unique="true"  > 
				<column name="`VehicleOtherSettingsId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 

		<property name="ChecklistSettingsId" type="System.Guid" not-null = "false" formula = "[ChecklistSettingsId]"></property>
 
		<!-- Vehicle.ChecklistSettings one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="ChecklistSettings" unique="true"  > 
				<column name="`ChecklistSettingsId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 

		<property name="InspectionId" type="System.Guid" not-null = "false" formula = "[InspectionId]"></property>
 
		<!-- Vehicle.Inspection one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="Inspection" unique="true"  > 
				<column name="`InspectionId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 


		<bag
			name = "OnDemandSessionItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMOnDemandSession" />
		</bag>
		<bag
			name = "Sessions"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMSession" />
		</bag>
		<bag
			name = "MessageHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMMessageHistory" />
		</bag>
		<bag
			name = "ImpactsForVehicleViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMImpactsForVehicleView" />
		</bag>
		<bag
			name = "VehicleAlertSubscriptionItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMVehicleAlertSubscription" />
		</bag>
		<bag
			name = "PedestrianDetectionHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMPedestrianDetectionHistory" />
		</bag>
		<bag
			name = "PerVehicleNormalCardAccessItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMPerVehicleNormalCardAccess" />
		</bag>
		<bag
			name = "VehicleBroadcastMessageItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMVehicleBroadcastMessage" />
		</bag>
		<bag
			name = "VORSettingHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMVORSettingHistory" />
		</bag>
		<bag
			name = "VehicleCardAccesses"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMPerVehicleMasterCardAccess" />
		</bag>
		<bag
			name = "ModuleHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMModuleHistory" />
		</bag>
		<bag
			name = "VehicleGPSLocations"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMVehicleGPS" />
		</bag>
		<bag
			name = "VehicleLockoutItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMVehicleLockout" />
		</bag>
		<bag
			name = "BroadcastMessageHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMBroadcastMessageHistory" />
		</bag>
		<bag
			name = "VehicleHireDehireHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMVehicleHireDehireHistory" />
		</bag>
		<bag
			name = "ChecklistFailurePerVechicleViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMChecklistFailurePerVechicleView" />
		</bag>
		<bag
			name = "PersonToPerVehicleMasterAccessViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMPersonToPerVehicleMasterAccessView" />
		</bag>
		<bag
			name = "CurrentStatusVehicleViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMCurrentStatusVehicleView" />
		</bag>
		<bag
			name = "SlamcoreDeviceHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMSlamcoreDeviceHistory" />
		</bag>
		<bag
			name = "PersonToPerVehicleNormalAccessViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMPersonToPerVehicleNormalAccessView" />
		</bag>
		<bag
			name = "VehicleSessionlessImpactItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMVehicleSessionlessImpact" />
		</bag>
		<bag
			name = "NetworkSettingsItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMNetworkSettings" />
		</bag>
		<bag
			name = "VehicleSupervisorsViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMVehicleSupervisorsView" />
		</bag>
		<bag
			name = "VehicleToPreOpCheckilstItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`VehicleId`" />
			</key>
			<one-to-many class = "ORMVehicleToPreOpChecklistView" />
		</bag>

    </class> 

</hibernate-mapping>