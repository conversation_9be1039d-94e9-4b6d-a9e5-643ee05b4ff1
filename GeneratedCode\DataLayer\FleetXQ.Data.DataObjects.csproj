﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>1.0.0.6765</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Data Objects</Description>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="NHibernate\ChecklistDetail\ChecklistDetail.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ChecklistDetail\ChecklistDetail.hbm.xml" />
    <None Remove="NHibernate\SlamcoreAwareAuthenticationDetails\SlamcoreAwareAuthenticationDetails.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SlamcoreAwareAuthenticationDetails\SlamcoreAwareAuthenticationDetails.hbm.xml" />
    <None Remove="NHibernate\FeatureSubscriptionTemplate\FeatureSubscriptionTemplate.hbm.xml" />
    <EmbeddedResource Include="NHibernate\FeatureSubscriptionTemplate\FeatureSubscriptionTemplate.hbm.xml" />
    <None Remove="NHibernate\PersonToModelVehicleMasterAccessView\PersonToModelVehicleMasterAccessView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonToModelVehicleMasterAccessView\PersonToModelVehicleMasterAccessView.hbm.xml" />
    <None Remove="NHibernate\VehicleLockout\VehicleLockout.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleLockout\VehicleLockout.hbm.xml" />
    <None Remove="NHibernate\Revision\Revision.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Revision\Revision.hbm.xml" />
    <None Remove="NHibernate\Alert\Alert.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Alert\Alert.hbm.xml" />
    <None Remove="NHibernate\WebsiteUser\WebsiteUser.hbm.xml" />
    <EmbeddedResource Include="NHibernate\WebsiteUser\WebsiteUser.hbm.xml" />
    <None Remove="NHibernate\DriverAccessAbuseFilter\DriverAccessAbuseFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DriverAccessAbuseFilter\DriverAccessAbuseFilter.hbm.xml" />
    <None Remove="NHibernate\ContactPersonInformation\ContactPersonInformation.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ContactPersonInformation\ContactPersonInformation.hbm.xml" />
    <None Remove="NHibernate\ChecklistSettings\ChecklistSettings.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ChecklistSettings\ChecklistSettings.hbm.xml" />
    <None Remove="NHibernate\VehicleDiagnostic\VehicleDiagnostic.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleDiagnostic\VehicleDiagnostic.hbm.xml" />
    <None Remove="NHibernate\GOUser2FA\GOUser2FA.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOUser2FA\GOUser2FA.hbm.xml" />
    <None Remove="NHibernate\VehicleGPS\VehicleGPS.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleGPS\VehicleGPS.hbm.xml" />
    <None Remove="NHibernate\Module\Module.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Module\Module.hbm.xml" />
    <None Remove="NHibernate\PersonToPerVehicleMasterAccessView\PersonToPerVehicleMasterAccessView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonToPerVehicleMasterAccessView\PersonToPerVehicleMasterAccessView.hbm.xml" />
    <None Remove="NHibernate\AccessGroup\AccessGroup.hbm.xml" />
    <EmbeddedResource Include="NHibernate\AccessGroup\AccessGroup.hbm.xml" />
    <None Remove="NHibernate\Help\Help.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Help\Help.hbm.xml" />
    <None Remove="NHibernate\CurrentStatusCombinedView\CurrentStatusCombinedView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CurrentStatusCombinedView\CurrentStatusCombinedView.hbm.xml" />
    <None Remove="NHibernate\CurrentVehicleStatusChartView\CurrentVehicleStatusChartView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CurrentVehicleStatusChartView\CurrentVehicleStatusChartView.hbm.xml" />
    <None Remove="NHibernate\DealerFeatureSubscription\DealerFeatureSubscription.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DealerFeatureSubscription\DealerFeatureSubscription.hbm.xml" />
    <None Remove="NHibernate\MessageHistory\MessageHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\MessageHistory\MessageHistory.hbm.xml" />
    <None Remove="NHibernate\AccessGroupToSite\AccessGroupToSite.hbm.xml" />
    <EmbeddedResource Include="NHibernate\AccessGroupToSite\AccessGroupToSite.hbm.xml" />
    <None Remove="NHibernate\CustomerToModel\CustomerToModel.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CustomerToModel\CustomerToModel.hbm.xml" />
    <None Remove="NHibernate\Region\Region.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Region\Region.hbm.xml" />
    <None Remove="NHibernate\Model\Model.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Model\Model.hbm.xml" />
    <None Remove="NHibernate\SlamcoreDeviceConnectionView\SlamcoreDeviceConnectionView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SlamcoreDeviceConnectionView\SlamcoreDeviceConnectionView.hbm.xml" />
    <None Remove="NHibernate\Customer\Customer.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Customer\Customer.hbm.xml" />
    <None Remove="NHibernate\DepartmentChecklist\DepartmentChecklist.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DepartmentChecklist\DepartmentChecklist.hbm.xml" />
    <None Remove="NHibernate\ModuleHistory\ModuleHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ModuleHistory\ModuleHistory.hbm.xml" />
    <None Remove="NHibernate\Country\Country.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Country\Country.hbm.xml" />
    <None Remove="NHibernate\GPSHistory\GPSHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GPSHistory\GPSHistory.hbm.xml" />
    <None Remove="NHibernate\ImpactsForVehicleView\ImpactsForVehicleView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ImpactsForVehicleView\ImpactsForVehicleView.hbm.xml" />
    <None Remove="NHibernate\SlamcorePedestrianDetection\SlamcorePedestrianDetection.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SlamcorePedestrianDetection\SlamcorePedestrianDetection.hbm.xml" />
    <None Remove="NHibernate\IOFIELD\IOFIELD.hbm.xml" />
    <EmbeddedResource Include="NHibernate\IOFIELD\IOFIELD.hbm.xml" />
    <None Remove="NHibernate\Email\Email.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Email\Email.hbm.xml" />
    <None Remove="NHibernate\LicenceDetail\LicenceDetail.hbm.xml" />
    <EmbeddedResource Include="NHibernate\LicenceDetail\LicenceDetail.hbm.xml" />
    <None Remove="NHibernate\PedestrianDetectionHistoryFilter\PedestrianDetectionHistoryFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PedestrianDetectionHistoryFilter\PedestrianDetectionHistoryFilter.hbm.xml" />
    <None Remove="NHibernate\GOLoginHistory\GOLoginHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOLoginHistory\GOLoginHistory.hbm.xml" />
    <None Remove="NHibernate\MainDashboardFilter\MainDashboardFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\MainDashboardFilter\MainDashboardFilter.hbm.xml" />
    <None Remove="NHibernate\AccessGroupTemplate\AccessGroupTemplate.hbm.xml" />
    <EmbeddedResource Include="NHibernate\AccessGroupTemplate\AccessGroupTemplate.hbm.xml" />
    <None Remove="NHibernate\CardToCardAccess\CardToCardAccess.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CardToCardAccess\CardToCardAccess.hbm.xml" />
    <None Remove="NHibernate\ZoneCoordinates\ZoneCoordinates.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ZoneCoordinates\ZoneCoordinates.hbm.xml" />
    <None Remove="NHibernate\Snapshot\Snapshot.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Snapshot\Snapshot.hbm.xml" />
    <None Remove="NHibernate\GOTask\GOTask.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOTask\GOTask.hbm.xml" />
    <None Remove="NHibernate\GeneralProductivityReportFilter\GeneralProductivityReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GeneralProductivityReportFilter\GeneralProductivityReportFilter.hbm.xml" />
    <None Remove="NHibernate\DepartmentVehicleNormalCardAccess\DepartmentVehicleNormalCardAccess.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DepartmentVehicleNormalCardAccess\DepartmentVehicleNormalCardAccess.hbm.xml" />
    <None Remove="NHibernate\CustomerToPersonView\CustomerToPersonView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CustomerToPersonView\CustomerToPersonView.hbm.xml" />
    <None Remove="NHibernate\VehicleSlamcoreLocationHistory\VehicleSlamcoreLocationHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleSlamcoreLocationHistory\VehicleSlamcoreLocationHistory.hbm.xml" />
    <None Remove="NHibernate\Permission\Permission.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Permission\Permission.hbm.xml" />
    <None Remove="NHibernate\SessionDetails\SessionDetails.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SessionDetails\SessionDetails.hbm.xml" />
    <None Remove="NHibernate\Vehicle\Vehicle.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Vehicle\Vehicle.hbm.xml" />
    <None Remove="NHibernate\Driver\Driver.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Driver\Driver.hbm.xml" />
    <None Remove="NHibernate\EmailGroupsToPerson\EmailGroupsToPerson.hbm.xml" />
    <EmbeddedResource Include="NHibernate\EmailGroupsToPerson\EmailGroupsToPerson.hbm.xml" />
    <None Remove="NHibernate\GOGroupRole\GOGroupRole.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOGroupRole\GOGroupRole.hbm.xml" />
    <None Remove="NHibernate\ModelVehicleMasterCardAccess\ModelVehicleMasterCardAccess.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ModelVehicleMasterCardAccess\ModelVehicleMasterCardAccess.hbm.xml" />
    <None Remove="NHibernate\EmailSubscriptionReportFilter\EmailSubscriptionReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\EmailSubscriptionReportFilter\EmailSubscriptionReportFilter.hbm.xml" />
    <None Remove="NHibernate\DashboardDriverCardView\DashboardDriverCardView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DashboardDriverCardView\DashboardDriverCardView.hbm.xml" />
    <None Remove="NHibernate\GOSecurityTokens\GOSecurityTokens.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOSecurityTokens\GOSecurityTokens.hbm.xml" />
    <None Remove="NHibernate\AlertHistory\AlertHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\AlertHistory\AlertHistory.hbm.xml" />
    <None Remove="NHibernate\DashboardFilter\DashboardFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DashboardFilter\DashboardFilter.hbm.xml" />
    <None Remove="NHibernate\VORReportFilter\VORReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VORReportFilter\VORReportFilter.hbm.xml" />
    <None Remove="NHibernate\OnDemandAuthorisationFilter\OnDemandAuthorisationFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\OnDemandAuthorisationFilter\OnDemandAuthorisationFilter.hbm.xml" />
    <None Remove="NHibernate\WebsiteRole\WebsiteRole.hbm.xml" />
    <EmbeddedResource Include="NHibernate\WebsiteRole\WebsiteRole.hbm.xml" />
    <None Remove="NHibernate\GOUserRole\GOUserRole.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOUserRole\GOUserRole.hbm.xml" />
    <None Remove="NHibernate\GeneralProductivityView\GeneralProductivityView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GeneralProductivityView\GeneralProductivityView.hbm.xml" />
    <None Remove="NHibernate\Inspection\Inspection.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Inspection\Inspection.hbm.xml" />
    <None Remove="NHibernate\Impact\Impact.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Impact\Impact.hbm.xml" />
    <None Remove="NHibernate\PersonToDepartmentVehicleMasterAccessView\PersonToDepartmentVehicleMasterAccessView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonToDepartmentVehicleMasterAccessView\PersonToDepartmentVehicleMasterAccessView.hbm.xml" />
    <None Remove="NHibernate\CurrentStatusVehicleView\CurrentStatusVehicleView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CurrentStatusVehicleView\CurrentStatusVehicleView.hbm.xml" />
    <None Remove="NHibernate\CustomerModel\CustomerModel.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CustomerModel\CustomerModel.hbm.xml" />
    <None Remove="NHibernate\Tag\Tag.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Tag\Tag.hbm.xml" />
    <None Remove="NHibernate\VehicleBroadcastMessage\VehicleBroadcastMessage.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleBroadcastMessage\VehicleBroadcastMessage.hbm.xml" />
    <None Remove="NHibernate\FloorPlan\FloorPlan.hbm.xml" />
    <EmbeddedResource Include="NHibernate\FloorPlan\FloorPlan.hbm.xml" />
    <None Remove="NHibernate\ModelVehicleNormalCardAccess\ModelVehicleNormalCardAccess.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ModelVehicleNormalCardAccess\ModelVehicleNormalCardAccess.hbm.xml" />
    <None Remove="NHibernate\SiteVehicleNormalCardAccess\SiteVehicleNormalCardAccess.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SiteVehicleNormalCardAccess\SiteVehicleNormalCardAccess.hbm.xml" />
    <None Remove="NHibernate\ImportJobStatus\ImportJobStatus.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ImportJobStatus\ImportJobStatus.hbm.xml" />
    <None Remove="NHibernate\VehicleOtherSettings\VehicleOtherSettings.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleOtherSettings\VehicleOtherSettings.hbm.xml" />
    <None Remove="NHibernate\DashboardFilterMoreFields\DashboardFilterMoreFields.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DashboardFilterMoreFields\DashboardFilterMoreFields.hbm.xml" />
    <None Remove="NHibernate\VehicleSupervisorsView\VehicleSupervisorsView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleSupervisorsView\VehicleSupervisorsView.hbm.xml" />
    <None Remove="NHibernate\ExportJobStatus\ExportJobStatus.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ExportJobStatus\ExportJobStatus.hbm.xml" />
    <None Remove="NHibernate\Timezone\Timezone.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Timezone\Timezone.hbm.xml" />
    <None Remove="NHibernate\HireDeHireReportFilter\HireDeHireReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\HireDeHireReportFilter\HireDeHireReportFilter.hbm.xml" />
    <None Remove="NHibernate\PersonAllocation\PersonAllocation.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonAllocation\PersonAllocation.hbm.xml" />
    <None Remove="NHibernate\BroadcastMessage\BroadcastMessage.hbm.xml" />
    <EmbeddedResource Include="NHibernate\BroadcastMessage\BroadcastMessage.hbm.xml" />
    <None Remove="NHibernate\VORReportCombinedView\VORReportCombinedView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VORReportCombinedView\VORReportCombinedView.hbm.xml" />
    <None Remove="NHibernate\ChecklistResult\ChecklistResult.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ChecklistResult\ChecklistResult.hbm.xml" />
    <None Remove="NHibernate\VehicleAlertSubscription\VehicleAlertSubscription.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleAlertSubscription\VehicleAlertSubscription.hbm.xml" />
    <None Remove="NHibernate\PersonToSiteVehicleNormalAccessView\PersonToSiteVehicleNormalAccessView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonToSiteVehicleNormalAccessView\PersonToSiteVehicleNormalAccessView.hbm.xml" />
    <None Remove="NHibernate\Session\Session.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Session\Session.hbm.xml" />
    <None Remove="NHibernate\SlamcoreDeviceFilter\SlamcoreDeviceFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SlamcoreDeviceFilter\SlamcoreDeviceFilter.hbm.xml" />
    <None Remove="NHibernate\Dealer\Dealer.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Dealer\Dealer.hbm.xml" />
    <None Remove="NHibernate\GOUser\GOUser.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOUser\GOUser.hbm.xml" />
    <None Remove="NHibernate\ServiceSettings\ServiceSettings.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ServiceSettings\ServiceSettings.hbm.xml" />
    <None Remove="NHibernate\UpdateFirmwareRequest\UpdateFirmwareRequest.hbm.xml" />
    <EmbeddedResource Include="NHibernate\UpdateFirmwareRequest\UpdateFirmwareRequest.hbm.xml" />
    <None Remove="NHibernate\SlamcoreAPIKey\SlamcoreAPIKey.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SlamcoreAPIKey\SlamcoreAPIKey.hbm.xml" />
    <None Remove="NHibernate\Firmware\Firmware.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Firmware\Firmware.hbm.xml" />
    <None Remove="NHibernate\Card\Card.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Card\Card.hbm.xml" />
    <None Remove="NHibernate\GOUserDepartment\GOUserDepartment.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOUserDepartment\GOUserDepartment.hbm.xml" />
    <None Remove="NHibernate\Canrule\Canrule.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Canrule\Canrule.hbm.xml" />
    <None Remove="NHibernate\SlamcoreDeviceHistory\SlamcoreDeviceHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SlamcoreDeviceHistory\SlamcoreDeviceHistory.hbm.xml" />
    <None Remove="NHibernate\VehicleHireDehireHistory\VehicleHireDehireHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleHireDehireHistory\VehicleHireDehireHistory.hbm.xml" />
    <None Remove="NHibernate\ChecklistFailurePerVechicleView\ChecklistFailurePerVechicleView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ChecklistFailurePerVechicleView\ChecklistFailurePerVechicleView.hbm.xml" />
    <None Remove="NHibernate\ImpactReportFilter\ImpactReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ImpactReportFilter\ImpactReportFilter.hbm.xml" />
    <None Remove="NHibernate\ReportSubscription\ReportSubscription.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ReportSubscription\ReportSubscription.hbm.xml" />
    <None Remove="NHibernate\VehicleLastGPSLocationView\VehicleLastGPSLocationView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleLastGPSLocationView\VehicleLastGPSLocationView.hbm.xml" />
    <None Remove="NHibernate\PSTATDetails\PSTATDetails.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PSTATDetails\PSTATDetails.hbm.xml" />
    <None Remove="NHibernate\LicenseExpiryReportFilter\LicenseExpiryReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\LicenseExpiryReportFilter\LicenseExpiryReportFilter.hbm.xml" />
    <None Remove="NHibernate\FloorZones\FloorZones.hbm.xml" />
    <EmbeddedResource Include="NHibernate\FloorZones\FloorZones.hbm.xml" />
    <None Remove="NHibernate\CanruleDetails\CanruleDetails.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CanruleDetails\CanruleDetails.hbm.xml" />
    <None Remove="NHibernate\VehicleHireDehireSynchronizationOptions\VehicleHireDehireSynchronizationOptions.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleHireDehireSynchronizationOptions\VehicleHireDehireSynchronizationOptions.hbm.xml" />
    <None Remove="NHibernate\DepartmentVehicleMasterCardAccess\DepartmentVehicleMasterCardAccess.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DepartmentVehicleMasterCardAccess\DepartmentVehicleMasterCardAccess.hbm.xml" />
    <None Remove="NHibernate\ChecklistFailureView\ChecklistFailureView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ChecklistFailureView\ChecklistFailureView.hbm.xml" />
    <None Remove="NHibernate\PersonChecklistLanguageSettings\PersonChecklistLanguageSettings.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonChecklistLanguageSettings\PersonChecklistLanguageSettings.hbm.xml" />
    <None Remove="NHibernate\VehicleSessionlessImpact\VehicleSessionlessImpact.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleSessionlessImpact\VehicleSessionlessImpact.hbm.xml" />
    <None Remove="NHibernate\LicenseByModel\LicenseByModel.hbm.xml" />
    <EmbeddedResource Include="NHibernate\LicenseByModel\LicenseByModel.hbm.xml" />
    <None Remove="NHibernate\IoTDeviceMessageCache\IoTDeviceMessageCache.hbm.xml" />
    <EmbeddedResource Include="NHibernate\IoTDeviceMessageCache\IoTDeviceMessageCache.hbm.xml" />
    <None Remove="NHibernate\ProficiencyCombinedView\ProficiencyCombinedView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ProficiencyCombinedView\ProficiencyCombinedView.hbm.xml" />
    <None Remove="NHibernate\CustomerSnapshot\CustomerSnapshot.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CustomerSnapshot\CustomerSnapshot.hbm.xml" />
    <None Remove="NHibernate\GOGroup\GOGroup.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOGroup\GOGroup.hbm.xml" />
    <None Remove="NHibernate\VehiclesPerModelReport\VehiclesPerModelReport.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehiclesPerModelReport\VehiclesPerModelReport.hbm.xml" />
    <None Remove="NHibernate\BroadcastMessageHistoryFilter\BroadcastMessageHistoryFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\BroadcastMessageHistoryFilter\BroadcastMessageHistoryFilter.hbm.xml" />
    <None Remove="NHibernate\DealerDriver\DealerDriver.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DealerDriver\DealerDriver.hbm.xml" />
    <None Remove="NHibernate\EmailGroups\EmailGroups.hbm.xml" />
    <EmbeddedResource Include="NHibernate\EmailGroups\EmailGroups.hbm.xml" />
    <None Remove="NHibernate\PreOperationalChecklist\PreOperationalChecklist.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PreOperationalChecklist\PreOperationalChecklist.hbm.xml" />
    <None Remove="NHibernate\SynchronizationStatusReportFilter\SynchronizationStatusReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SynchronizationStatusReportFilter\SynchronizationStatusReportFilter.hbm.xml" />
    <None Remove="NHibernate\SlamcoreDevice\SlamcoreDevice.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SlamcoreDevice\SlamcoreDevice.hbm.xml" />
    <None Remove="NHibernate\Site\Site.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Site\Site.hbm.xml" />
    <None Remove="NHibernate\AlertSubscription\AlertSubscription.hbm.xml" />
    <EmbeddedResource Include="NHibernate\AlertSubscription\AlertSubscription.hbm.xml" />
    <None Remove="NHibernate\CustomerAudit\CustomerAudit.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CustomerAudit\CustomerAudit.hbm.xml" />
    <None Remove="NHibernate\PerVehicleNormalCardAccess\PerVehicleNormalCardAccess.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PerVehicleNormalCardAccess\PerVehicleNormalCardAccess.hbm.xml" />
    <None Remove="NHibernate\PedestrianDetectionHistory\PedestrianDetectionHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PedestrianDetectionHistory\PedestrianDetectionHistory.hbm.xml" />
    <None Remove="NHibernate\PersonToPerVehicleNormalAccessView\PersonToPerVehicleNormalAccessView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonToPerVehicleNormalAccessView\PersonToPerVehicleNormalAccessView.hbm.xml" />
    <None Remove="NHibernate\ImportJobLog\ImportJobLog.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ImportJobLog\ImportJobLog.hbm.xml" />
    <None Remove="NHibernate\ProficiencyReportFilter\ProficiencyReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ProficiencyReportFilter\ProficiencyReportFilter.hbm.xml" />
    <None Remove="NHibernate\OnDemandSession\OnDemandSession.hbm.xml" />
    <EmbeddedResource Include="NHibernate\OnDemandSession\OnDemandSession.hbm.xml" />
    <None Remove="NHibernate\VORSettingHistory\VORSettingHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VORSettingHistory\VORSettingHistory.hbm.xml" />
    <None Remove="NHibernate\CustomerSSODetail\CustomerSSODetail.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CustomerSSODetail\CustomerSSODetail.hbm.xml" />
    <None Remove="NHibernate\UploadLogoRequest\UploadLogoRequest.hbm.xml" />
    <EmbeddedResource Include="NHibernate\UploadLogoRequest\UploadLogoRequest.hbm.xml" />
    <None Remove="NHibernate\AllVehicleCalibrationFilter\AllVehicleCalibrationFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\AllVehicleCalibrationFilter\AllVehicleCalibrationFilter.hbm.xml" />
    <None Remove="NHibernate\DepartmentHourSettings\DepartmentHourSettings.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DepartmentHourSettings\DepartmentHourSettings.hbm.xml" />
    <None Remove="NHibernate\CategoryTemplate\CategoryTemplate.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CategoryTemplate\CategoryTemplate.hbm.xml" />
    <None Remove="NHibernate\DashboardVehicleCardView\DashboardVehicleCardView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DashboardVehicleCardView\DashboardVehicleCardView.hbm.xml" />
    <None Remove="NHibernate\CurrentStatusDriverView\CurrentStatusDriverView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CurrentStatusDriverView\CurrentStatusDriverView.hbm.xml" />
    <None Remove="NHibernate\ReportType\ReportType.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ReportType\ReportType.hbm.xml" />
    <None Remove="NHibernate\SiteVehicleMasterCardAccess\SiteVehicleMasterCardAccess.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SiteVehicleMasterCardAccess\SiteVehicleMasterCardAccess.hbm.xml" />
    <None Remove="NHibernate\PersonToDepartmentVehicleNormalAccessView\PersonToDepartmentVehicleNormalAccessView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonToDepartmentVehicleNormalAccessView\PersonToDepartmentVehicleNormalAccessView.hbm.xml" />
    <None Remove="NHibernate\GOUserGroup\GOUserGroup.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOUserGroup\GOUserGroup.hbm.xml" />
    <None Remove="NHibernate\CustomerFeatureSubscription\CustomerFeatureSubscription.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CustomerFeatureSubscription\CustomerFeatureSubscription.hbm.xml" />
    <None Remove="NHibernate\VehicleToPreOpChecklistView\VehicleToPreOpChecklistView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\VehicleToPreOpChecklistView\VehicleToPreOpChecklistView.hbm.xml" />
    <None Remove="NHibernate\PersonToSiteVehicleMasterAccessView\PersonToSiteVehicleMasterAccessView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonToSiteVehicleMasterAccessView\PersonToSiteVehicleMasterAccessView.hbm.xml" />
    <None Remove="NHibernate\DealerConfiguration\DealerConfiguration.hbm.xml" />
    <EmbeddedResource Include="NHibernate\DealerConfiguration\DealerConfiguration.hbm.xml" />
    <None Remove="NHibernate\CustomerPreOperationalChecklistTemplate\CustomerPreOperationalChecklistTemplate.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CustomerPreOperationalChecklistTemplate\CustomerPreOperationalChecklistTemplate.hbm.xml" />
    <None Remove="NHibernate\CurrentDriverStatusChartView\CurrentDriverStatusChartView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\CurrentDriverStatusChartView\CurrentDriverStatusChartView.hbm.xml" />
    <None Remove="NHibernate\GOChangeDelta\GOChangeDelta.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GOChangeDelta\GOChangeDelta.hbm.xml" />
    <None Remove="NHibernate\PreOpReportFilter\PreOpReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PreOpReportFilter\PreOpReportFilter.hbm.xml" />
    <None Remove="NHibernate\SiteFloorPlan\SiteFloorPlan.hbm.xml" />
    <EmbeddedResource Include="NHibernate\SiteFloorPlan\SiteFloorPlan.hbm.xml" />
    <None Remove="NHibernate\BroadcastMessageHistory\BroadcastMessageHistory.hbm.xml" />
    <EmbeddedResource Include="NHibernate\BroadcastMessageHistory\BroadcastMessageHistory.hbm.xml" />
    <None Remove="NHibernate\GoUserToCustomer\GoUserToCustomer.hbm.xml" />
    <EmbeddedResource Include="NHibernate\GoUserToCustomer\GoUserToCustomer.hbm.xml" />
    <None Remove="NHibernate\ImportJobBatch\ImportJobBatch.hbm.xml" />
    <EmbeddedResource Include="NHibernate\ImportJobBatch\ImportJobBatch.hbm.xml" />
    <None Remove="NHibernate\Person\Person.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Person\Person.hbm.xml" />
    <None Remove="NHibernate\PersonToModelVehicleNormalAccessView\PersonToModelVehicleNormalAccessView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PersonToModelVehicleNormalAccessView\PersonToModelVehicleNormalAccessView.hbm.xml" />
    <None Remove="NHibernate\NetworkSettings\NetworkSettings.hbm.xml" />
    <EmbeddedResource Include="NHibernate\NetworkSettings\NetworkSettings.hbm.xml" />
    <None Remove="NHibernate\Department\Department.hbm.xml" />
    <EmbeddedResource Include="NHibernate\Department\Department.hbm.xml" />
    <None Remove="NHibernate\MachineUnlockReportFilter\MachineUnlockReportFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\MachineUnlockReportFilter\MachineUnlockReportFilter.hbm.xml" />
    <None Remove="NHibernate\PerVehicleMasterCardAccess\PerVehicleMasterCardAccess.hbm.xml" />
    <EmbeddedResource Include="NHibernate\PerVehicleMasterCardAccess\PerVehicleMasterCardAccess.hbm.xml" />
    <None Remove="NHibernate\UnitUtilisationCombinedView\UnitUtilisationCombinedView.hbm.xml" />
    <EmbeddedResource Include="NHibernate\UnitUtilisationCombinedView\UnitUtilisationCombinedView.hbm.xml" />
    <None Remove="NHibernate\FeatureSubscriptionsFilter\FeatureSubscriptionsFilter.hbm.xml" />
    <EmbeddedResource Include="NHibernate\FeatureSubscriptionsFilter\FeatureSubscriptionsFilter.hbm.xml" />
    <None Remove="NHibernate\OnDemandSettings\OnDemandSettings.hbm.xml" />
    <EmbeddedResource Include="NHibernate\OnDemandSettings\OnDemandSettings.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.2.24" />
    <PackageReference Include="I18Next.Net" Version="1.0.0" />
    <PackageReference Include="I18Next.Net.Extensions" Version="1.0.0" />
  </ItemGroup>
</Project>