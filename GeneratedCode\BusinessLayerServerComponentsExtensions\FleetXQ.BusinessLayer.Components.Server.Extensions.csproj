﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
    <PropertyGroup>
    <VersionPrefix>1.0.0.6765</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Business Layer Server Components Extensions</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
    <PackageReference Include="GenerativeObjects.Services" Version="2.0.3" />
    <PackageReference Include="NLog" Version="5.1.4" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\CustomCode\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.Custom.csproj" />
    <ProjectReference Include="..\BusinessLayerServerComponentsExtensionsInterfaces\FleetXQ.BusinessLayer.Components.Server.Extensions.Interfaces.csproj" />
    <ProjectReference Include="..\Resources\FleetXQ.Data.Resources.csproj" />
    <ProjectReference Include="..\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.csproj" />
    <ProjectReference Include="..\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj" />
    <ProjectReference Include="..\BusinessLayer\FleetXQ.BusinessLayer.csproj" />
  </ItemGroup>
</Project>