# FleetXQ Bulk Importer

A .NET console application for performing efficient bulk insertion of driver and vehicle data into the FleetXQ database.

## Prerequisites

- .NET 6.0 or later
- SQL Server with FleetXQ database
- Database permissions for creating schemas, tables, and stored procedures

## First-Time Setup

1. **Build the project**:
   ```bash
   dotnet restore
   dotnet build
   ```

2. **Setup database**:
   - Ensure SQL Server is accessible
   - Run SQL scripts in order (001 through 006) from the `Sql/` directory
   - Update connection string in `appsettings.json`

3. **Configure connection**:
   - Edit `appsettings.json`
   - Set `ConnectionStrings:FleetXQConnection` to your database

### Running

#### Interactive Mode (Default)
```bash
BulkImporter
```

This will prompt you for:
- Number of drivers to process (default: 10,000)
- Number of vehicles to process (default: 5,000)
- Batch size for operations (default: 1,000)
- Data source (generate synthetic data or read from files)
- Dry run confirmation

#### Non-Interactive Mode
```bash
BulkImporter --drivers 1000 --vehicles 500 --non-interactive
```

#### Command Line Options
```bash
# Basic usage
BulkImporter --drivers 5000 --vehicles 2500

# Dry run with synthetic data
BulkImporter --generate --dry-run

# Custom batch size
BulkImporter --batch-size 5000 --non-interactive

# Generate synthetic data
BulkImporter --generate --drivers 1000 --vehicles 500

# Show help
BulkImporter --help
```

## Basic Usage

```bash
# Interactive mode (will prompt for settings)
dotnet run

# Non-interactive with specific counts
dotnet run -- --drivers 1000 --vehicles 500 --non-interactive

# Dry run (no database changes)
dotnet run -- --generate --dry-run

# Help
dotnet run -- --help
```

## Configuration

The main configuration is in `appsettings.json`. Key settings:

- **ConnectionStrings:FleetXQConnection**: Your SQL Server connection string
- **BulkImporter:DefaultDriversCount**: Default number of drivers (10000)
- **BulkImporter:DefaultVehiclesCount**: Default number of vehicles (5000)
- **BulkImporter:DefaultBatchSize**: Processing batch size (1000)