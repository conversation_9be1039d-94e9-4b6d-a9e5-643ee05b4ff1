﻿{
  "entityName": "SlamcoreAPIKey",
  "entityNamePlural": "SlamcoreAPIKeies",   "entityDescription": "This entity represents a SlamcoreAPIKey",
  "fields": {
    "APIKey": {
        "displayName": "APIKey", 
        "description": "APIKey"
    },
    "APIKeyDisplay": {
        "displayName": "APIKeyDisplay", 
        "description": "APIKeyDisplay"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "SlamcoreDevice": {
        "displayName": "SlamcoreDevice", 
        "description": "SlamcoreDevice"
    }
  }
} 