﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge ORMEntity -> DataObject
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	[Serializable]
	public class ORMCustomerFeatureSubscription : IORMEntity 
	{
		public virtual System.Guid Id { get; set; }
 
		///
		/// All Persistent non-key fields
		///
		public virtual System.String Description { get; set; }
		public virtual System.String Name { get; set; }
		public virtual System.Boolean CanAccessSlamcore { get; set; }
		public virtual System.Boolean IsEnabled { get; set; }
		public virtual System.Boolean IsTagged { get; set; }
		public virtual System.Boolean HasAdditionalHardwaresAccess { get; set; }
 
		///
		/// PK-Side one-to-one relations
		///
		public virtual ORMCustomer Customer { get; set; } 
 
		///
		/// All FK-Side Relations
		///
	
 
		///
		/// PK-Side one-to-many relations
		///
 
		///
		/// Bridge to DataObject
		///
		public virtual IDataObject ToDataObject(IObjectsDataSet dataset, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
            var session = nHibernateSessionController.GetCurrentSession(threadContext);
            session.Evict(this);

			var x = serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>();

 
			SetProperties(x);

			x.IsDirty = x.IsNew = x.IsMarkedForDeletion = false;

			x.ObjectsDataSet = dataset;
			x.ObjectsDataSet.AddObjectIfDoesNotExist(x);

			// Deep-map prefetch relations
			if (PrefetchAssociations.HasPrefetchForEntity("CustomerFeatureSubscription", dataProviderTransaction?.Parameters))
			{
				SetRelations(x, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction);
			}

			return x;
		}

		protected void SetProperties(CustomerFeatureSubscriptionDataObject x)
		{
			x.SetIdValue(Id, false, false);
			x.SetDescriptionValue(Description, false, false);
			x.SetNameValue(Name, false, false);
			x.SetCanAccessSlamcoreValue(CanAccessSlamcore, false, false);
			x.SetIsEnabledValue(IsEnabled, false, false);
			x.SetIsTaggedValue(IsTagged, false, false);
			x.SetHasAdditionalHardwaresAccessValue(HasAdditionalHardwaresAccess, false, false);
		}

		protected void SetRelations(CustomerFeatureSubscriptionDataObject x, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
			var prefetches = PrefetchAssociations.Get("CustomerFeatureSubscription", dataProviderTransaction?.Parameters);

			if (prefetches.Contains("Customer") && this.Customer != null)
			{
				var customer = x.ObjectsDataSet.GetObject(new CustomerDataObject((System.Guid)this.Customer.Id) { IsNew = false });

				if (customer == null)
					customer = this.Customer.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerDataObject;

				x.SetCustomerValue(customer);
			}

		}
		
		// For database filtering, nothing IsNew or IsDirty, but these stubs needed for ORM security predicates to compile
		public virtual bool IsNew => false;
		public virtual bool IsDirty => false;

	}
}