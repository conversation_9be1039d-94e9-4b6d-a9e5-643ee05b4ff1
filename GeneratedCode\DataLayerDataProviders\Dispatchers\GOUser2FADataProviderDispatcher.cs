﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Data.DataProviders.Dispatchers
{
    public class GOUser2FADataProviderDispatcher : IDataProviderDispatcher<GOUser2FADataObject>
    {
		protected IServiceProvider _serviceProvider;
		
		public GOUser2FADataProviderDispatcher(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
		
		protected IDataProvider<GORoleDataObject> gORoleDataProvider => _serviceProvider.GetService<IDataProvider<GORoleDataObject>>();
		protected IDataProvider<GoUserToCustomerDataObject> goUserToCustomerDataProvider => _serviceProvider.GetService<IDataProvider<GoUserToCustomerDataObject>>();
		protected IDataProvider<PersonDataObject> personDataProvider => _serviceProvider.GetService<IDataProvider<PersonDataObject>>();
		protected IDataProvider<MessageHistoryDataObject> messageHistoryDataProvider => _serviceProvider.GetService<IDataProvider<MessageHistoryDataObject>>();
		protected IDataProvider<GOUserGroupDataObject> gOUserGroupDataProvider => _serviceProvider.GetService<IDataProvider<GOUserGroupDataObject>>();
		protected IDataProvider<CustomerAuditDataObject> customerAuditDataProvider => _serviceProvider.GetService<IDataProvider<CustomerAuditDataObject>>();
		protected IDataProvider<ReportSubscriptionDataObject> reportSubscriptionDataProvider => _serviceProvider.GetService<IDataProvider<ReportSubscriptionDataObject>>();
		protected IDataProvider<GOUserRoleDataObject> gOUserRoleDataProvider => _serviceProvider.GetService<IDataProvider<GOUserRoleDataObject>>();
		protected IDataProvider<TagDataObject> tagDataProvider => _serviceProvider.GetService<IDataProvider<TagDataObject>>();
		protected IDataProvider<ExportJobStatusDataObject> exportJobStatusDataProvider => _serviceProvider.GetService<IDataProvider<ExportJobStatusDataObject>>();
		protected IDataProvider<RevisionDataObject> revisionDataProvider => _serviceProvider.GetService<IDataProvider<RevisionDataObject>>();
		protected IDataProvider<DealerDataObject> dealerDataProvider => _serviceProvider.GetService<IDataProvider<DealerDataObject>>();
		protected IDataProvider<GOUserDepartmentDataObject> gOUserDepartmentDataProvider => _serviceProvider.GetService<IDataProvider<GOUserDepartmentDataObject>>();
		protected IDataProvider<DealerDriverDataObject> dealerDriverDataProvider => _serviceProvider.GetService<IDataProvider<DealerDriverDataObject>>();
		protected IDataProvider<VehicleLockoutDataObject> vehicleLockoutDataProvider => _serviceProvider.GetService<IDataProvider<VehicleLockoutDataObject>>();
		protected IDataProvider<AlertSubscriptionDataObject> alertSubscriptionDataProvider => _serviceProvider.GetService<IDataProvider<AlertSubscriptionDataObject>>();

        public async Task DispatchForEntityAsync(GOUser2FADataObject entity, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("GOUser2FA", parameters);

            foreach (var include in includes)
            {
	
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
                  case "gorole":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("GORole"))
									break;

								if (entity.GORoleName != null) 
								{
									try
									{
										var objectToFetch = await gORoleDataProvider.GetAsync(new GORoleDataObject((System.String)entity.GORoleName), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "gousertocustomeritems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("GoUserToCustomerItems"))
									break;

								try
								{
									var objectToFetch = await goUserToCustomerDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "person":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Person"))
									break;

								try
								{
									var objectToFetch = await personDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "messagehistoryitems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("MessageHistoryItems"))
									break;

								try
								{
									var objectToFetch = await messageHistoryDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "usergroupitems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("UserGroupItems"))
									break;

								try
								{
									var objectToFetch = await gOUserGroupDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customeraudititemscreated":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerAuditItemsCreated"))
									break;

								try
								{
									var objectToFetch = await customerAuditDataProvider.GetCollectionAsync(null, "CreatedBy == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "reportsubscriptionitems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ReportSubscriptionItems"))
									break;

								try
								{
									var objectToFetch = await reportSubscriptionDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "userroleitems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("UserRoleItems"))
									break;

								try
								{
									var objectToFetch = await gOUserRoleDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customeraudititemsdeleted":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerAuditItemsDeleted"))
									break;

								try
								{
									var objectToFetch = await customerAuditDataProvider.GetCollectionAsync(null, "DeletedBy == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "tag":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Tag"))
									break;

								try
								{
									var objectToFetch = await tagDataProvider.GetCollectionAsync(null, "Author == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "exportjobstatusitems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ExportJobStatusItems"))
									break;

								try
								{
									var objectToFetch = await exportJobStatusDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "revisionitems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("RevisionItems"))
									break;

								try
								{
									var objectToFetch = await revisionDataProvider.GetCollectionAsync(null, "fkGOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "dealer":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Dealer"))
									break;

								if (entity.DealerId != null) 
								{
									try
									{
										var objectToFetch = await dealerDataProvider.GetAsync(new DealerDataObject((System.Guid)entity.DealerId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "gouserdepartmentitems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("GOUserDepartmentItems"))
									break;

								try
								{
									var objectToFetch = await gOUserDepartmentDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customeraudititemsmodified":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerAuditItemsModified"))
									break;

								try
								{
									var objectToFetch = await customerAuditDataProvider.GetCollectionAsync(null, "LastModifiedBy == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "dealerdriver":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DealerDriver"))
									break;

								try
								{
									var objectToFetch = await dealerDriverDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclelockoutitems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleLockoutItems"))
									break;

								try
								{
									var objectToFetch = await vehicleLockoutDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "alertsubscriptionitems":
							{
								// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("AlertSubscriptionItems"))
									break;

								try
								{
									var objectToFetch = await alertSubscriptionDataProvider.GetCollectionAsync(null, "GOUserId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
						default:
							throw new ApplicationException("GOUser2FA Entity has no relation named " + relation);
					}
          }        
		}

        public async Task DispatchForEntityCollectionAsync(IEnumerable<GOUser2FADataObject> entities, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("GOUser2FA", parameters);

            foreach (var include in includes)
            {
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
						case "gorole":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("GORole"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.GORoleName != null).Select(e => (System.String)e.GORoleName).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await gORoleDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Name))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "gousertocustomeritems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("GoUserToCustomerItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await goUserToCustomerDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "person":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Person"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await personDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "messagehistoryitems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("MessageHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await messageHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "usergroupitems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("UserGroupItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await gOUserGroupDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customeraudititemscreated":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerAuditItemsCreated"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerAuditDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CreatedBy.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "reportsubscriptionitems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ReportSubscriptionItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await reportSubscriptionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "userroleitems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("UserRoleItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await gOUserRoleDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customeraudititemsdeleted":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerAuditItemsDeleted"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerAuditDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DeletedBy.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "tag":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Tag"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await tagDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Author.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "exportjobstatusitems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ExportJobStatusItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await exportJobStatusDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "revisionitems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("RevisionItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await revisionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.fkGOUserId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "dealer":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Dealer"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.DealerId != null).Select(e => (System.Guid)e.DealerId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await dealerDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "gouserdepartmentitems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("GOUserDepartmentItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await gOUserDepartmentDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customeraudititemsmodified":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerAuditItemsModified"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerAuditDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.LastModifiedBy.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "dealerdriver":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DealerDriver"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await dealerDriverDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclelockoutitems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleLockoutItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleLockoutDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "alertsubscriptionitems":
                        {
							// custom code can implement IPrefetch<ORMGOUser2FA> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("AlertSubscriptionItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await alertSubscriptionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.GOUserId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
                  default:
                        throw new ApplicationException("GOUser2FA Entity has no relation named " + relation);
					}
            }        
        }
	}
}