﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMAlertHistory" 
		table="[AlertHistory]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="CreatedDateTime" >
            <column name="`CreatedDateTime`" sql-type="datetime" not-null="true" />
        </property> 
		<property name="Description" >
            <column name="`Description`" sql-type="nvarchar (1000) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="IsAcknowledged" >
            <column name="`IsAcknowledged`" sql-type="bit" not-null="true" />
        </property> 
		<property name="IsResolved" >
            <column name="`IsResolved`" sql-type="bit" not-null="true" />
        </property> 

		
		<!-- many-to-one Alert -->
		<property name="AlertId" type="System.Guid" not-null="true" formula = "[AlertId]"></property>  
		<many-to-one name="Alert"  > 
			<column name="`AlertId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
		
		<!-- many-to-one Vehicle -->
		<property name="VehicleId" type="System.Guid" not-null="true" formula = "[VehicleId]"></property>  
		<many-to-one name="Vehicle"  > 
			<column name="`VehicleId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
		
		<!-- many-to-one Driver -->
		<property name="DriverId" type="System.Guid" not-null="true" formula = "[DriverId]"></property>  
		<many-to-one name="Driver"  > 
			<column name="`DriverId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
 




    </class> 

</hibernate-mapping>