﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Data.DataProviders.Dispatchers
{
    public class SlamcoreDeviceDataProviderDispatcher : IDataProviderDispatcher<SlamcoreDeviceDataObject>
    {
		protected IServiceProvider _serviceProvider;
		
		public SlamcoreDeviceDataProviderDispatcher(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
		
		protected IDataProvider<CustomerDataObject> customerDataProvider => _serviceProvider.GetService<IDataProvider<CustomerDataObject>>();
		protected IDataProvider<SlamcorePedestrianDetectionDataObject> slamcorePedestrianDetectionDataProvider => _serviceProvider.GetService<IDataProvider<SlamcorePedestrianDetectionDataObject>>();
		protected IDataProvider<SlamcoreAPIKeyDataObject> slamcoreAPIKeyDataProvider => _serviceProvider.GetService<IDataProvider<SlamcoreAPIKeyDataObject>>();
		protected IDataProvider<VehicleDataObject> vehicleDataProvider => _serviceProvider.GetService<IDataProvider<VehicleDataObject>>();
		protected IDataProvider<VehicleSlamcoreLocationHistoryDataObject> vehicleSlamcoreLocationHistoryDataProvider => _serviceProvider.GetService<IDataProvider<VehicleSlamcoreLocationHistoryDataObject>>();
		protected IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject> slamcoreAwareAuthenticationDetailsDataProvider => _serviceProvider.GetService<IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject>>();
		protected IDataProvider<SlamcoreDeviceHistoryDataObject> slamcoreDeviceHistoryDataProvider => _serviceProvider.GetService<IDataProvider<SlamcoreDeviceHistoryDataObject>>();
		protected IDataProvider<SlamcoreDeviceConnectionViewDataObject> slamcoreDeviceConnectionViewDataProvider => _serviceProvider.GetService<IDataProvider<SlamcoreDeviceConnectionViewDataObject>>();

        public async Task DispatchForEntityAsync(SlamcoreDeviceDataObject entity, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("SlamcoreDevice", parameters);

            foreach (var include in includes)
            {
	
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
                  case "customer":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Customer"))
									break;

								try
								{
									var objectToFetch = await customerDataProvider.GetAsync(new CustomerDataObject(entity.CustomerId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "slamcorepedestriandetectionitems":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("SlamcorePedestrianDetectionItems"))
									break;

								try
								{
									var objectToFetch = await slamcorePedestrianDetectionDataProvider.GetCollectionAsync(null, "SlamcoreDeviceId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "slamcoreapikey":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("SlamcoreAPIKey"))
									break;

								if (entity.SlamcoreAPIKeyId != null) 
								{
									try
									{
										var objectToFetch = await slamcoreAPIKeyDataProvider.GetAsync(new SlamcoreAPIKeyDataObject((System.Guid)entity.SlamcoreAPIKeyId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "vehicle":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Vehicle"))
									break;

								if (entity.VehicleId != null) 
								{
									try
									{
										var objectToFetch = await vehicleDataProvider.GetAsync(new VehicleDataObject((System.Guid)entity.VehicleId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "vehicleslamcorelocationhistoryitems":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleSlamcoreLocationHistoryItems"))
									break;

								try
								{
									var objectToFetch = await vehicleSlamcoreLocationHistoryDataProvider.GetCollectionAsync(null, "SlamcoreDeviceId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "slamcoreawareauthenticationdetails":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("SlamcoreAwareAuthenticationDetails"))
									break;

								if (entity.SlamcoreAwareAuthenticationDetailsId != null) 
								{
									try
									{
										var objectToFetch = await slamcoreAwareAuthenticationDetailsDataProvider.GetAsync(new SlamcoreAwareAuthenticationDetailsDataObject((System.Guid)entity.SlamcoreAwareAuthenticationDetailsId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "slamcoredevicehistoryitems":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("SlamcoreDeviceHistoryItems"))
									break;

								try
								{
									var objectToFetch = await slamcoreDeviceHistoryDataProvider.GetCollectionAsync(null, "SlamcoreDeviceId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "slamcoredeviceconnectionviewitems":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("SlamcoreDeviceConnectionViewItems"))
									break;

								try
								{
									var objectToFetch = await slamcoreDeviceConnectionViewDataProvider.GetCollectionAsync(null, "SlamcoreDeviceId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
						default:
							throw new ApplicationException("SlamcoreDevice Entity has no relation named " + relation);
					}
          }        
		}

        public async Task DispatchForEntityCollectionAsync(IEnumerable<SlamcoreDeviceDataObject> entities, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("SlamcoreDevice", parameters);

            foreach (var include in includes)
            {
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
						case "customer":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Customer"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.CustomerId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "slamcorepedestriandetectionitems":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("SlamcorePedestrianDetectionItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await slamcorePedestrianDetectionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SlamcoreDeviceId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "slamcoreapikey":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("SlamcoreAPIKey"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.SlamcoreAPIKeyId != null).Select(e => (System.Guid)e.SlamcoreAPIKeyId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await slamcoreAPIKeyDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicle":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Vehicle"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.VehicleId != null).Select(e => (System.Guid)e.VehicleId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicleslamcorelocationhistoryitems":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleSlamcoreLocationHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleSlamcoreLocationHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SlamcoreDeviceId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "slamcoreawareauthenticationdetails":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("SlamcoreAwareAuthenticationDetails"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.SlamcoreAwareAuthenticationDetailsId != null).Select(e => (System.Guid)e.SlamcoreAwareAuthenticationDetailsId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await slamcoreAwareAuthenticationDetailsDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "slamcoredevicehistoryitems":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("SlamcoreDeviceHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await slamcoreDeviceHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SlamcoreDeviceId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "slamcoredeviceconnectionviewitems":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDevice> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("SlamcoreDeviceConnectionViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await slamcoreDeviceConnectionViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SlamcoreDeviceId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
                  default:
                        throw new ApplicationException("SlamcoreDevice Entity has no relation named " + relation);
					}
            }        
        }
	}
}