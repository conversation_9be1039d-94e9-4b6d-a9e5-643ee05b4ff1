﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Http;

using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Web;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.HostedServices;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
 

using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using Parameters = System.Collections.Generic.Dictionary<string, object>;

namespace FleetXQ.BusinessLayer.Components.Server.Extensions
{
	public partial class SlamcoreAlertHistoryComponentSurrogate : ISlamcoreAlertHistoryComponentSurrogate
	{
		private readonly IServiceProvider _serviceProvider;

		private readonly IHttpContextAccessor _httpContextAccessor;
		private readonly ISlamcoreAlertHistoryComponent _slamcoreAlertHistoryComponent;
		private readonly IThreadContext _threadContext;
		private readonly IServicePath _servicePath;
		private readonly IUserIdentity _userIdentity;
		
		private readonly IAuthentication _authentication;
		private readonly FleetXQ.BusinessLayer.Tasks.IGORunningTasks _goRunningTasks;
		private readonly IBackgroundWorkerQueue _backgroundWorkerQueue;
		private readonly FleetXQ.BusinessLayer.Tasks.GOTaskRunner _goTaskRunner;
	
		private readonly IEnumerable<ISlamcoreAlertHistoryComponentExtension> _extensionServices;

		public SlamcoreAlertHistoryComponentSurrogate(IServiceProvider provider, IHttpContextAccessor httpContextAccessor, ISlamcoreAlertHistoryComponent slamcoreAlertHistoryComponent, IThreadContext threadContext, IServicePath servicePath, IUserIdentity userIdentity, IEnumerable<ISlamcoreAlertHistoryComponentExtension> extensionServices, FleetXQ.BusinessLayer.Tasks.GOTaskRunner goTaskRunner, FleetXQ.BusinessLayer.Tasks.IGORunningTasks goRunningTasks, IAuthentication authentication, IBackgroundWorkerQueue backgroundWorkerQueue)
		{
  			_serviceProvider = provider;
			_httpContextAccessor = httpContextAccessor;
			_slamcoreAlertHistoryComponent = slamcoreAlertHistoryComponent;
			_threadContext = threadContext;
			_servicePath = servicePath;
			_userIdentity = userIdentity;
			_extensionServices = extensionServices;
		
			_goTaskRunner = goTaskRunner;
			_goRunningTasks = goRunningTasks;
			_authentication = authentication;
			_backgroundWorkerQueue = backgroundWorkerQueue;
	
		}

		public SlamcoreAlertHistoryComponent ComponentClass { get { return _slamcoreAlertHistoryComponent as SlamcoreAlertHistoryComponent; } }

		/// <summary>
		/// The extensions for implementors of ISlamcoreAlertHistoryComponentExtension to attach to
		/// </summary>
		public event Func<ComponentExtensionEventArgs, Task> OnBeforeCall;
		public event Func<ComponentExtensionEventArgs, Task> OnAfterCall;

		/// <summary>
		/// Thread-synchronisation gubbins
		/// </summary>
		private bool _extensionsInitialised;
		private Object _lock = new Object();
			
		private SemaphoreSlim _initializationSemaphore = new SemaphoreSlim(1, 1);

		/// <summary>
		/// InitializeExtensionsAsync()
		/// Find and initialise registered extensions of this interface
		/// Done in a thread-safe manner to allow for possibility that component may have been registered as singleton (controlled lifetime)
		/// </summary>
		private async Task InitializeExtensionsAsync()
		{
			await _initializationSemaphore.WaitAsync();
			try
			{
				// Ensure component extensions have been initialised
				if (_extensionsInitialised)
					return;

				if (_extensionsInitialised)
					return;
					
				foreach (var registration in _extensionServices)
				{
					await registration.InitAsync(this);
				}

				_extensionsInitialised = true;
			}
			finally
			{
				_initializationSemaphore.Release();
			}
		}

		/// <summary>
		/// Surrogate implementation of the Export operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ExportJobStatusDataObject> ExportAsync(System.String filterPredicate, System.String filterParameters, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "Export" });
			}
			var result = await this.Start_ExportAsync(filterPredicate, filterParameters, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "Export" });
			}

			return result;
		}

		#region Asynchronous Operation Support for operation Export
					
		class ExportArgs : FleetXQ.BusinessLayer.Tasks.GOTaskRunner.GOTaskArgs<ExportJobStatusDataObject>
		{
			public System.String filterPredicate;
			public System.String filterParameters;
			public Dictionary<string, object> parameters;
			public string baseUrl;
			public string webRootPath;
		}

		/// <summary>
		/// Start asynchronous Export operation
		/// </summary>
		/// <returns></returns>		
		private async Task<ExportJobStatusDataObject> Start_ExportAsync(System.String filterPredicate, System.String filterParameters, Dictionary<string, object> parameters = null)
		{
	        string baseurl = _servicePath.ProjectBaseURL();
	        string webRootPath = _servicePath.WebRootPath();

          
			(var authenticationToken, var applicationToken, var transport) = _authentication.GetBearerTokens(_httpContextAccessor.HttpContext);
			var args = new ExportArgs
			{
			    DbKey = _threadContext.DbKey,  //Propagate DbKey through to worker thread
				UserTaskDataObject = null, 
				UserToken = authenticationToken,		// Propagate the bearer token through to worker thread
				ApplicationToken = applicationToken,    // And the application token
					
				filterPredicate = filterPredicate,
				
				filterParameters = filterParameters,
		
				parameters = parameters,
				baseUrl = baseurl,
				webRootPath = webRootPath
			};

			// Create (or attach) task object used to track the async operation
			var taskObject = _goTaskRunner.Init<ExportJobStatusDataObject>("SlamcoreAlertHistoryComponent.Export", args);

			// Give an opportunity to the component to initialize
			await this.ComponentClass.Export_OnInitTaskAsync(taskObject, filterPredicate, filterParameters, parameters);

            // Note: If you are using a GOTask subtype and getting an exception here because of required fields not being filled,
            // then you need to implement your operation_OnInitTask to fill the required fields
            taskObject = await _serviceProvider.GetRequiredService<IDataProvider<ExportJobStatusDataObject>>().SaveOutOfTransactionAsync(taskObject, parameters: _goTaskRunner.TaskParameters, skipSecurity: true);

            var task = _serviceProvider.GetRequiredService<FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject>>();
			task.TaskObject = taskObject;
			var uri = new Uri(_httpContextAccessor.HttpContext.Request.GetEncodedUrl());
			task.PathBaseUrl = uri.Scheme + "://" + uri.Authority.TrimEnd('/') + _httpContextAccessor.HttpContext.Request.PathBase;
			
			args.Task = task;

			 _goRunningTasks.AddRunningTask(taskObject);
             _backgroundWorkerQueue.QueueBackgroundWorkItem((token, scope) => this.Run_Export(args, scope));
 
            return taskObject;
		}

		/// <summary>
		/// Export operation thread
		/// </summary>
		/// <returns></returns>		
		private async Task Run_Export(ExportArgs args, IServiceScope scope)
		{
			var task = args.Task;
			task.SetScope(scope);
			Exception error = null;

			try
			{
				// Set user identity - not strictly needed because IThreadContext (below) carries this and more, but at least this way either can be used from within thread impl
				var identity = scope.ServiceProvider.GetRequiredService<IUserIdentity>();
                identity.UserToken = args.UserToken;
                identity.ApplicationToken = args.ApplicationToken;
				// Set scope context
				var scopeContext = scope.ServiceProvider.GetRequiredService<IThreadContext>();
				scopeContext.UserToken = args.UserToken;
				scopeContext.ApplicationToken = args.ApplicationToken;
				//threadIdentity.HttpContext = task.HttpContext;
				scopeContext.DbKey = args.DbKey;  //HERE we are in an asynchroneous thread context
 				scopeContext.BaseUrl = args.baseUrl;
 				scopeContext.WebRootPath = args.webRootPath;

				// Set started status
				await task.UpdateAsync(GOTaskStatusEnum.Started, 0);

				// call the asynchronous component operation implementation (we are a background thread)
				// first, bring all entity type parameter into scope
				var dataset = scope.ServiceProvider.GetRequiredService<IObjectsDataSet>();

                var slamcoreAlertHistoryComponentComponent = scope.ServiceProvider.GetRequiredService<ISlamcoreAlertHistoryComponent>();
                await slamcoreAlertHistoryComponentComponent.ExportAsync(task, args.filterPredicate, args.filterParameters,args.parameters);

				// Set completed status
				if (   task.DataObject.TaskStatus != GOTaskStatusEnum.Failed
					&& task.DataObject.TaskStatus != GOTaskStatusEnum.Cancelled
					&& task.DataObject.TaskStatus != GOTaskStatusEnum.Cancelling)
				{
					await task.OnCompleteAsync();
				}
			}
			catch (Exception e)
			{
				error = e;
				await task.OnErrorAsync(e);
			}
			finally
			{
				// Check no transaction left running on the worker thread
				await scope.ServiceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
			}
		}

		#endregion

		}
}
