﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Data.DataProviders.Dispatchers
{
    public class DashboardFilterDataProviderDispatcher : IDataProviderDispatcher<DashboardFilterDataObject>
    {
		protected IServiceProvider _serviceProvider;
		
		public DashboardFilterDataProviderDispatcher(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
		
		protected IDataProvider<DepartmentDataObject> departmentDataProvider => _serviceProvider.GetService<IDataProvider<DepartmentDataObject>>();
		protected IDataProvider<SiteDataObject> siteDataProvider => _serviceProvider.GetService<IDataProvider<SiteDataObject>>();
		protected IDataProvider<CustomerDataObject> customerDataProvider => _serviceProvider.GetService<IDataProvider<CustomerDataObject>>();

		protected IDataProviderDispatcher<DriverAccessAbuseFilterDataObject> driverAccessAbuseFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<DriverAccessAbuseFilterDataObject>>();
		protected IDataProviderDispatcher<PedestrianDetectionHistoryFilterDataObject> pedestrianDetectionHistoryFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<PedestrianDetectionHistoryFilterDataObject>>();
		protected IDataProviderDispatcher<MainDashboardFilterDataObject> mainDashboardFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<MainDashboardFilterDataObject>>();
		protected IDataProviderDispatcher<GeneralProductivityReportFilterDataObject> generalProductivityReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<GeneralProductivityReportFilterDataObject>>();
		protected IDataProviderDispatcher<EmailSubscriptionReportFilterDataObject> emailSubscriptionReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<EmailSubscriptionReportFilterDataObject>>();
		protected IDataProviderDispatcher<VORReportFilterDataObject> vORReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<VORReportFilterDataObject>>();
		protected IDataProviderDispatcher<OnDemandAuthorisationFilterDataObject> onDemandAuthorisationFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<OnDemandAuthorisationFilterDataObject>>();
		protected IDataProviderDispatcher<DashboardFilterMoreFieldsDataObject> dashboardFilterMoreFieldsDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<DashboardFilterMoreFieldsDataObject>>();
		protected IDataProviderDispatcher<HireDeHireReportFilterDataObject> hireDeHireReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<HireDeHireReportFilterDataObject>>();
		protected IDataProviderDispatcher<SlamcoreDeviceFilterDataObject> slamcoreDeviceFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<SlamcoreDeviceFilterDataObject>>();
		protected IDataProviderDispatcher<ImpactReportFilterDataObject> impactReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<ImpactReportFilterDataObject>>();
		protected IDataProviderDispatcher<LicenseExpiryReportFilterDataObject> licenseExpiryReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<LicenseExpiryReportFilterDataObject>>();
		protected IDataProviderDispatcher<BroadcastMessageHistoryFilterDataObject> broadcastMessageHistoryFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<BroadcastMessageHistoryFilterDataObject>>();
		protected IDataProviderDispatcher<SynchronizationStatusReportFilterDataObject> synchronizationStatusReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<SynchronizationStatusReportFilterDataObject>>();
		protected IDataProviderDispatcher<ProficiencyReportFilterDataObject> proficiencyReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<ProficiencyReportFilterDataObject>>();
		protected IDataProviderDispatcher<AllVehicleCalibrationFilterDataObject> allVehicleCalibrationFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<AllVehicleCalibrationFilterDataObject>>();
		protected IDataProviderDispatcher<PreOpReportFilterDataObject> preOpReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<PreOpReportFilterDataObject>>();
		protected IDataProviderDispatcher<MachineUnlockReportFilterDataObject> machineUnlockReportFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<MachineUnlockReportFilterDataObject>>();
		protected IDataProviderDispatcher<FeatureSubscriptionsFilterDataObject> featureSubscriptionsFilterDataDispatcher => _serviceProvider.GetService<IDataProviderDispatcher<FeatureSubscriptionsFilterDataObject>>();
        public async Task DispatchForEntityAsync(DashboardFilterDataObject entity, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("DashboardFilter", parameters);

            foreach (var include in includes)
            {
				bool treated = false;

				// First treat the includes involving entities inherited from current entity
				if(include.Contains(':') && !include.Split(':').ToArray()[0].Contains('.')) // Trying to deal with inheritance
				{
					var includeInheritance = include.Split(':').ToArray();
                    var subincludesInheritance = includeInheritance.Length == 1 ? null : new List<String>() {  include.Replace(includeInheritance[0] + ":", "") };
                    switch(includeInheritance[0].ToLower())
                    {
						case "driveraccessabusefilter":
						if (entity is DriverAccessAbuseFilterDataObject)
						{
							await driverAccessAbuseFilterDataDispatcher.DispatchForEntityAsync(entity as DriverAccessAbuseFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "pedestriandetectionhistoryfilter":
						if (entity is PedestrianDetectionHistoryFilterDataObject)
						{
							await pedestrianDetectionHistoryFilterDataDispatcher.DispatchForEntityAsync(entity as PedestrianDetectionHistoryFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "maindashboardfilter":
						if (entity is MainDashboardFilterDataObject)
						{
							await mainDashboardFilterDataDispatcher.DispatchForEntityAsync(entity as MainDashboardFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "generalproductivityreportfilter":
						if (entity is GeneralProductivityReportFilterDataObject)
						{
							await generalProductivityReportFilterDataDispatcher.DispatchForEntityAsync(entity as GeneralProductivityReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "emailsubscriptionreportfilter":
						if (entity is EmailSubscriptionReportFilterDataObject)
						{
							await emailSubscriptionReportFilterDataDispatcher.DispatchForEntityAsync(entity as EmailSubscriptionReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "vorreportfilter":
						if (entity is VORReportFilterDataObject)
						{
							await vORReportFilterDataDispatcher.DispatchForEntityAsync(entity as VORReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "ondemandauthorisationfilter":
						if (entity is OnDemandAuthorisationFilterDataObject)
						{
							await onDemandAuthorisationFilterDataDispatcher.DispatchForEntityAsync(entity as OnDemandAuthorisationFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "dashboardfiltermorefields":
						if (entity is DashboardFilterMoreFieldsDataObject)
						{
							await dashboardFilterMoreFieldsDataDispatcher.DispatchForEntityAsync(entity as DashboardFilterMoreFieldsDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "hiredehirereportfilter":
						if (entity is HireDeHireReportFilterDataObject)
						{
							await hireDeHireReportFilterDataDispatcher.DispatchForEntityAsync(entity as HireDeHireReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "slamcoredevicefilter":
						if (entity is SlamcoreDeviceFilterDataObject)
						{
							await slamcoreDeviceFilterDataDispatcher.DispatchForEntityAsync(entity as SlamcoreDeviceFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "impactreportfilter":
						if (entity is ImpactReportFilterDataObject)
						{
							await impactReportFilterDataDispatcher.DispatchForEntityAsync(entity as ImpactReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "licenseexpiryreportfilter":
						if (entity is LicenseExpiryReportFilterDataObject)
						{
							await licenseExpiryReportFilterDataDispatcher.DispatchForEntityAsync(entity as LicenseExpiryReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "broadcastmessagehistoryfilter":
						if (entity is BroadcastMessageHistoryFilterDataObject)
						{
							await broadcastMessageHistoryFilterDataDispatcher.DispatchForEntityAsync(entity as BroadcastMessageHistoryFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "synchronizationstatusreportfilter":
						if (entity is SynchronizationStatusReportFilterDataObject)
						{
							await synchronizationStatusReportFilterDataDispatcher.DispatchForEntityAsync(entity as SynchronizationStatusReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "proficiencyreportfilter":
						if (entity is ProficiencyReportFilterDataObject)
						{
							await proficiencyReportFilterDataDispatcher.DispatchForEntityAsync(entity as ProficiencyReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "allvehiclecalibrationfilter":
						if (entity is AllVehicleCalibrationFilterDataObject)
						{
							await allVehicleCalibrationFilterDataDispatcher.DispatchForEntityAsync(entity as AllVehicleCalibrationFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "preopreportfilter":
						if (entity is PreOpReportFilterDataObject)
						{
							await preOpReportFilterDataDispatcher.DispatchForEntityAsync(entity as PreOpReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "machineunlockreportfilter":
						if (entity is MachineUnlockReportFilterDataObject)
						{
							await machineUnlockReportFilterDataDispatcher.DispatchForEntityAsync(entity as MachineUnlockReportFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						case "featuresubscriptionsfilter":
						if (entity is FeatureSubscriptionsFilterDataObject)
						{
							await featureSubscriptionsFilterDataDispatcher.DispatchForEntityAsync(entity as FeatureSubscriptionsFilterDataObject, subincludesInheritance, context, parameters, skipSecurity);
						}

						treated = true;
						break;
						default:
							break;
					}
				}
				
				// Then treat includes involving the current entity of base entities
				if (!treated) 
				{
	
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
                  case "department":
							{
								// custom code can implement IPrefetch<ORMDashboardFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Department"))
									break;

								try
								{
									var objectToFetch = await departmentDataProvider.GetAsync(new DepartmentDataObject(entity.DepartmentId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "site":
							{
								// custom code can implement IPrefetch<ORMDashboardFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Site"))
									break;

								try
								{
									var objectToFetch = await siteDataProvider.GetAsync(new SiteDataObject(entity.SiteId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customer":
							{
								// custom code can implement IPrefetch<ORMDashboardFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Customer"))
									break;

								try
								{
									var objectToFetch = await customerDataProvider.GetAsync(new CustomerDataObject(entity.CustomerId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
						default:
							throw new ApplicationException("DashboardFilter Entity has no relation named " + relation);
					}
				}
          }        
		}

        public async Task DispatchForEntityCollectionAsync(IEnumerable<DashboardFilterDataObject> entities, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("DashboardFilter", parameters);

            foreach (var include in includes)
            {
				bool treated = false;

				// First treat the includes involving entities inherited from current entity
				if(include.Contains(':') && !include.Split(':').ToArray()[0].Contains('.')) // Trying to deal with inheritance
				{
					var includeInheritance = include.Split(':').ToArray();
                    var subincludesInheritance = includeInheritance.Length == 1 ? null : new List<String>() {  include.Replace(includeInheritance[0] + ":", "") };
                    switch(includeInheritance[0].ToLower())
                    {
						case "driveraccessabusefilter":
						{
							var specificEntities = entities.Where(e => e is DriverAccessAbuseFilterDataObject).Select(e => e as DriverAccessAbuseFilterDataObject);
							if (specificEntities.Any())
							{
								await driverAccessAbuseFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "pedestriandetectionhistoryfilter":
						{
							var specificEntities = entities.Where(e => e is PedestrianDetectionHistoryFilterDataObject).Select(e => e as PedestrianDetectionHistoryFilterDataObject);
							if (specificEntities.Any())
							{
								await pedestrianDetectionHistoryFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "maindashboardfilter":
						{
							var specificEntities = entities.Where(e => e is MainDashboardFilterDataObject).Select(e => e as MainDashboardFilterDataObject);
							if (specificEntities.Any())
							{
								await mainDashboardFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "generalproductivityreportfilter":
						{
							var specificEntities = entities.Where(e => e is GeneralProductivityReportFilterDataObject).Select(e => e as GeneralProductivityReportFilterDataObject);
							if (specificEntities.Any())
							{
								await generalProductivityReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "emailsubscriptionreportfilter":
						{
							var specificEntities = entities.Where(e => e is EmailSubscriptionReportFilterDataObject).Select(e => e as EmailSubscriptionReportFilterDataObject);
							if (specificEntities.Any())
							{
								await emailSubscriptionReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "vorreportfilter":
						{
							var specificEntities = entities.Where(e => e is VORReportFilterDataObject).Select(e => e as VORReportFilterDataObject);
							if (specificEntities.Any())
							{
								await vORReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "ondemandauthorisationfilter":
						{
							var specificEntities = entities.Where(e => e is OnDemandAuthorisationFilterDataObject).Select(e => e as OnDemandAuthorisationFilterDataObject);
							if (specificEntities.Any())
							{
								await onDemandAuthorisationFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "dashboardfiltermorefields":
						{
							var specificEntities = entities.Where(e => e is DashboardFilterMoreFieldsDataObject).Select(e => e as DashboardFilterMoreFieldsDataObject);
							if (specificEntities.Any())
							{
								await dashboardFilterMoreFieldsDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "hiredehirereportfilter":
						{
							var specificEntities = entities.Where(e => e is HireDeHireReportFilterDataObject).Select(e => e as HireDeHireReportFilterDataObject);
							if (specificEntities.Any())
							{
								await hireDeHireReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "slamcoredevicefilter":
						{
							var specificEntities = entities.Where(e => e is SlamcoreDeviceFilterDataObject).Select(e => e as SlamcoreDeviceFilterDataObject);
							if (specificEntities.Any())
							{
								await slamcoreDeviceFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "impactreportfilter":
						{
							var specificEntities = entities.Where(e => e is ImpactReportFilterDataObject).Select(e => e as ImpactReportFilterDataObject);
							if (specificEntities.Any())
							{
								await impactReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "licenseexpiryreportfilter":
						{
							var specificEntities = entities.Where(e => e is LicenseExpiryReportFilterDataObject).Select(e => e as LicenseExpiryReportFilterDataObject);
							if (specificEntities.Any())
							{
								await licenseExpiryReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "broadcastmessagehistoryfilter":
						{
							var specificEntities = entities.Where(e => e is BroadcastMessageHistoryFilterDataObject).Select(e => e as BroadcastMessageHistoryFilterDataObject);
							if (specificEntities.Any())
							{
								await broadcastMessageHistoryFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "synchronizationstatusreportfilter":
						{
							var specificEntities = entities.Where(e => e is SynchronizationStatusReportFilterDataObject).Select(e => e as SynchronizationStatusReportFilterDataObject);
							if (specificEntities.Any())
							{
								await synchronizationStatusReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "proficiencyreportfilter":
						{
							var specificEntities = entities.Where(e => e is ProficiencyReportFilterDataObject).Select(e => e as ProficiencyReportFilterDataObject);
							if (specificEntities.Any())
							{
								await proficiencyReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "allvehiclecalibrationfilter":
						{
							var specificEntities = entities.Where(e => e is AllVehicleCalibrationFilterDataObject).Select(e => e as AllVehicleCalibrationFilterDataObject);
							if (specificEntities.Any())
							{
								await allVehicleCalibrationFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "preopreportfilter":
						{
							var specificEntities = entities.Where(e => e is PreOpReportFilterDataObject).Select(e => e as PreOpReportFilterDataObject);
							if (specificEntities.Any())
							{
								await preOpReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "machineunlockreportfilter":
						{
							var specificEntities = entities.Where(e => e is MachineUnlockReportFilterDataObject).Select(e => e as MachineUnlockReportFilterDataObject);
							if (specificEntities.Any())
							{
								await machineUnlockReportFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						case "featuresubscriptionsfilter":
						{
							var specificEntities = entities.Where(e => e is FeatureSubscriptionsFilterDataObject).Select(e => e as FeatureSubscriptionsFilterDataObject);
							if (specificEntities.Any())
							{
								await featureSubscriptionsFilterDataDispatcher.DispatchForEntityCollectionAsync(specificEntities, subincludesInheritance, context, parameters, skipSecurity);
							}

							treated = true;
						}	
						break;
						default:
							break;
					}
				}
				
				// Then treat includes involving the current entity of base entities
				if (!treated) 
				{

					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
						case "department":
                        {
							// custom code can implement IPrefetch<ORMDashboardFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Department"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.DepartmentId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await departmentDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "site":
                        {
							// custom code can implement IPrefetch<ORMDashboardFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Site"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.SiteId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await siteDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customer":
                        {
							// custom code can implement IPrefetch<ORMDashboardFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Customer"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.CustomerId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
                  default:
                        throw new ApplicationException("DashboardFilter Entity has no relation named " + relation);
					}
				}
            }        
        }
	}
}