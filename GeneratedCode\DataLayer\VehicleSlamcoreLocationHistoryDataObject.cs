﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'VehicleSlamcoreLocationHistory'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class VehicleSlamcoreLocationHistoryDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("AcquisitionDateTime")]
		protected System.DateTime _acquisitionDateTime;
		[JsonProperty("AcquisitionDateTime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _acquisitionDateTime_WithTimezoneOffset;
		[JsonProperty ("Bearing")]
		protected Nullable<System.Decimal> _bearing;
		[JsonProperty ("EventType")]
		protected Nullable<SlamcoreEventTypesEnum> _eventType;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("ReferenceFrameCategory")]
		protected SlamcoreReferenceFramesEnum _referenceFrameCategory;
		[JsonProperty ("SlamcoreDeviceId")]
		protected System.Guid _slamcoreDeviceId;
		[JsonProperty ("Speed")]
		protected Nullable<System.Decimal> _speed;
		[JsonProperty ("Status")]
		protected SlamcoreTrackingStatusTypesEnum _status;
		[JsonProperty ("TrailSequence")]
		protected Nullable<System.Int32> _trailSequence;
		[JsonProperty ("WOrientation")]
		protected System.Decimal _wOrientation;
		[JsonProperty ("XOrientation")]
		protected System.Decimal _xOrientation;
		[JsonProperty ("XPosition")]
		protected System.Decimal _xPosition;
		[JsonProperty ("YOrientation")]
		protected System.Decimal _yOrientation;
		[JsonProperty ("YPosition")]
		protected System.Decimal _yPosition;
		[JsonProperty ("ZPosition")]
		protected System.Decimal _zPosition;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)
		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _slamcoreDevice_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_slamcoreDevice_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public VehicleSlamcoreLocationHistoryDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public VehicleSlamcoreLocationHistoryDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public VehicleSlamcoreLocationHistoryDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public VehicleSlamcoreLocationHistoryDataObject Initialize(VehicleSlamcoreLocationHistoryDataObject template, bool deepCopy)
		{
			this.SetAcquisitionDateTimeValue(template.AcquisitionDateTime, false, false);
			this._acquisitionDateTime_WithTimezoneOffset = template._acquisitionDateTime_WithTimezoneOffset;
			this.SetBearingValue(template.Bearing, false, false);
			this.SetEventTypeValue(template.EventType, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetReferenceFrameCategoryValue(template.ReferenceFrameCategory, false, false);
			this.SetSlamcoreDeviceIdValue(template.SlamcoreDeviceId, false, false);
			this.SetSpeedValue(template.Speed, false, false);
			this.SetStatusValue(template.Status, false, false);
			this.SetTrailSequenceValue(template.TrailSequence, false, false);
			this.SetWOrientationValue(template.WOrientation, false, false);
			this.SetXOrientationValue(template.XOrientation, false, false);
			this.SetXPositionValue(template.XPosition, false, false);
			this.SetYOrientationValue(template.YOrientation, false, false);
			this.SetYPositionValue(template.YPosition, false, false);
			this.SetZPositionValue(template.ZPosition, false, false);
 
			this._slamcoreDevice_NewObjectId = template._slamcoreDevice_NewObjectId;
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual VehicleSlamcoreLocationHistoryDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual VehicleSlamcoreLocationHistoryDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<VehicleSlamcoreLocationHistoryDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var vehicleSlamcoreLocationHistorySource = sourceObject as VehicleSlamcoreLocationHistoryDataObject;

			if (ReferenceEquals(null, vehicleSlamcoreLocationHistorySource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetAcquisitionDateTimeValue(vehicleSlamcoreLocationHistorySource.AcquisitionDateTime, false, false);
			this.SetBearingValue(vehicleSlamcoreLocationHistorySource.Bearing, false, false);
			this.SetEventTypeValue(vehicleSlamcoreLocationHistorySource.EventType, false, false);
			this.SetIdValue(vehicleSlamcoreLocationHistorySource.Id, false, false);
			this.SetReferenceFrameCategoryValue(vehicleSlamcoreLocationHistorySource.ReferenceFrameCategory, false, false);
			this.SetSlamcoreDeviceIdValue(vehicleSlamcoreLocationHistorySource.SlamcoreDeviceId, false, false);
			this.SetSpeedValue(vehicleSlamcoreLocationHistorySource.Speed, false, false);
			this.SetStatusValue(vehicleSlamcoreLocationHistorySource.Status, false, false);
			this.SetTrailSequenceValue(vehicleSlamcoreLocationHistorySource.TrailSequence, false, false);
			this.SetWOrientationValue(vehicleSlamcoreLocationHistorySource.WOrientation, false, false);
			this.SetXOrientationValue(vehicleSlamcoreLocationHistorySource.XOrientation, false, false);
			this.SetXPositionValue(vehicleSlamcoreLocationHistorySource.XPosition, false, false);
			this.SetYOrientationValue(vehicleSlamcoreLocationHistorySource.YOrientation, false, false);
			this.SetYPositionValue(vehicleSlamcoreLocationHistorySource.YPosition, false, false);
			this.SetZPositionValue(vehicleSlamcoreLocationHistorySource.ZPosition, false, false);
			this._slamcoreDevice_NewObjectId = (sourceObject as VehicleSlamcoreLocationHistoryDataObject)._slamcoreDevice_NewObjectId;

			if (deepCopy)
			{
				this.ObjectsDataSet = vehicleSlamcoreLocationHistorySource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(vehicleSlamcoreLocationHistorySource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as VehicleSlamcoreLocationHistoryDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {
			if (this._slamcoreDevice_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._slamcoreDevice_NewObjectId))
				{
                    this._slamcoreDevice_NewObjectId = null;
				}
                else
				{
					this._slamcoreDevice_NewObjectId = datasetMergingInternalIdMapping[(int) this._slamcoreDevice_NewObjectId];
				}
			}

		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<SlamcoreDeviceDataObject> _slamcoreDeviceService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceDataObject>>();
      public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet)
		{
			SetSlamcoreDeviceValue(valueToSet, true, true);
		}

        public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			SlamcoreDeviceDataObject existing_slamcoreDevice = null ;

			if ( !(ObjectsDataSet == null))
			{
				SlamcoreDeviceDataObject key;

				if (this._slamcoreDevice_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._slamcoreDevice_NewObjectId;			
				}

				existing_slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_slamcoreDevice ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("SlamcoreDevice", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleSlamcoreLocationHistoryDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_slamcoreDevice_NewObjectId != valueToSet.InternalObjectId)
					{
						_slamcoreDevice_NewObjectId = valueToSet.InternalObjectId;
						_slamcoreDeviceId = valueToSet.Id;
						OnPropertyChanged("SlamcoreDeviceId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_slamcoreDeviceId != valueToSet.Id)
					{
						_slamcoreDevice_NewObjectId = null;

						_slamcoreDeviceId = valueToSet.Id;
						OnPropertyChanged("SlamcoreDeviceId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_slamcoreDeviceId = Guid.Empty;
				OnPropertyChanged("SlamcoreDeviceId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_slamcoreDevice ,valueToSet))
				OnPropertyChanged("SlamcoreDevice", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __slamcoreDeviceSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreDeviceAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreDevice", which is a SlamcoreDeviceDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SlamcoreDeviceDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreDeviceAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreDeviceSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreDeviceAlreadyLazyLoaded || forceReload)
                {
								
					SlamcoreDeviceDataObject slamcoreDevice = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __slamcoreDeviceAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId);
						slamcoreDevice.IsNew = false;
						slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
						if (slamcoreDevice != null)
						{
							return slamcoreDevice;
						}
					}

					slamcoreDevice = await _slamcoreDeviceService.GetAsync(_serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId), parameters : parameters, skipSecurity: skipSecurity);

					SetSlamcoreDeviceValue(slamcoreDevice, false, false);
					__slamcoreDeviceAlreadyLazyLoaded = true;				
		
					slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId);
					slamcoreDevice.IsNew = false;
					slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
                    __slamcoreDeviceAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreDeviceAsync(false);
            }
            finally
            {
                __slamcoreDeviceSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SlamcoreDeviceDataObject SlamcoreDevice 
		{
			get
			{			
				return GetSlamcoreDeviceAsync(true).Result;
			}
			set
			{
				SetSlamcoreDeviceValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreDevice()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleSlamcoreLocationHistoryDataObject") && ObjectsDataSet.RelationsToInclude["VehicleSlamcoreLocationHistoryDataObject"].Contains("SlamcoreDevice");
		}

		public virtual async Task<SlamcoreDeviceDataObject> GetSlamcoreDeviceAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SlamcoreDeviceDataObject slamcoreDevice;

				
			if (_slamcoreDevice_NewObjectId != null)
			{
				slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>();
				slamcoreDevice.IsNew = true;
				slamcoreDevice.InternalObjectId = _slamcoreDevice_NewObjectId;
				slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
			}
			else
			{
				slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId);
				slamcoreDevice.IsNew = false;
				slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
				
				if (allowLazyLoading && slamcoreDevice == null && LazyLoadingEnabled && (!__slamcoreDeviceAlreadyLazyLoaded || forceReload))
				{
					slamcoreDevice = await LoadSlamcoreDeviceAsync(forceReload : forceReload);
				}
			}
				
			return slamcoreDevice;
		}

		public virtual System.Guid SlamcoreDeviceForeignKey
		{
			get { return SlamcoreDeviceId; }
			set 
			{	
				SlamcoreDeviceId = value;
			}
			
		}
		

		public override void ClearLazyLoadFlags()
		{
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadSlamcoreDeviceAsync()) != null)
				result.Add(SlamcoreDevice);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				SlamcoreDevice == other ||
				(other is SlamcoreDeviceDataObject && (SlamcoreDeviceId != default(System.Guid)) && (SlamcoreDeviceId == (other as SlamcoreDeviceDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetAcquisitionDateTimeValue(System.DateTime valueToSet)
		{
			SetAcquisitionDateTimeValue(valueToSet, true, true);
		}

		public virtual void SetAcquisitionDateTimeValue(System.DateTime valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null || valueToSet == DateTime.MinValue) 
			{
				if (_acquisitionDateTime != DateTime.MinValue.ToUniversalTime())
				{
					_acquisitionDateTime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("AcquisitionDateTime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_acquisitionDateTime != DateTime.MaxValue.ToUniversalTime())
				{
					_acquisitionDateTime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("AcquisitionDateTime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_acquisitionDateTime != valueToSet ||
                (_acquisitionDateTime != null && ((DateTime)_acquisitionDateTime).Kind == DateTimeKind.Unspecified))
			{
				_acquisitionDateTime = DateTime.SpecifyKind(valueToSet, DateTimeKind.Local);
				OnPropertyChanged("AcquisitionDateTime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The AcquisitionDateTime property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual System.DateTime AcquisitionDateTime 
		{
			get	{ return _acquisitionDateTime;}
			
			
			set
			{
				SetAcquisitionDateTimeValue(value);
			}
		}		
			
			
		public virtual void SetBearingValue(Nullable<System.Decimal> valueToSet)
		{
			SetBearingValue(valueToSet, true, true);
		}

		public virtual void SetBearingValue(Nullable<System.Decimal> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_bearing != (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 4)))
			{
				_bearing = (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 4));

				OnPropertyChanged("Bearing", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Bearing property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual Nullable<System.Decimal> Bearing 
		{
			get	{ return _bearing;}
			
			
			set
			{
				SetBearingValue(value);
			}
		}		
			
			
		public virtual void SetEventTypeValue(Nullable<SlamcoreEventTypesEnum> valueToSet)
		{
			SetEventTypeValue(valueToSet, true, true);
		}

		public virtual void SetEventTypeValue(Nullable<SlamcoreEventTypesEnum> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_eventType != valueToSet)
			{
				_eventType = valueToSet;

				OnPropertyChanged("EventType", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("EventTypeDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The EventType property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual Nullable<SlamcoreEventTypesEnum> EventType 
		{
			get	{ return _eventType;}
			
			
			set
			{
				SetEventTypeValue(value);
			}
		}		
      public virtual string EventTypeDisplayString
		{
			get
			{
				if (EventType == null)
					return "-";

				return EventTypeEnumDisplayNameCollection.Where(v => v.Value == EventType).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<SlamcoreEventTypesEnum>> EventTypeEnumDisplayNameCollection
	    {
	        get
	        {
                return SlamcoreEventTypesEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetReferenceFrameCategoryValue(SlamcoreReferenceFramesEnum valueToSet)
		{
			SetReferenceFrameCategoryValue(valueToSet, true, true);
		}

		public virtual void SetReferenceFrameCategoryValue(SlamcoreReferenceFramesEnum valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_referenceFrameCategory != valueToSet)
			{
				_referenceFrameCategory = valueToSet;

				OnPropertyChanged("ReferenceFrameCategory", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("ReferenceFrameCategoryDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The ReferenceFrameCategory property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual SlamcoreReferenceFramesEnum ReferenceFrameCategory 
		{
			get	{ return _referenceFrameCategory;}
			
			
			set
			{
				SetReferenceFrameCategoryValue(value);
			}
		}		
      public virtual string ReferenceFrameCategoryDisplayString
		{
			get
			{
				return ReferenceFrameCategoryEnumDisplayNameCollection.Where(v => v.Value == ReferenceFrameCategory).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<SlamcoreReferenceFramesEnum>> ReferenceFrameCategoryEnumDisplayNameCollection
	    {
	        get
	        {
                return SlamcoreReferenceFramesEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetSlamcoreDeviceIdValue(System.Guid valueToSet)
		{
			SetSlamcoreDeviceIdValue(valueToSet, true, true);
		}

		public virtual void SetSlamcoreDeviceIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_slamcoreDeviceId != valueToSet)
			{
				_slamcoreDeviceId = valueToSet;

				OnPropertyChanged("SlamcoreDeviceId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SlamcoreDeviceId property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual System.Guid SlamcoreDeviceId 
		{
			get	{ return _slamcoreDeviceId;}
			
			
			set
			{
				SetSlamcoreDeviceIdValue(value);
			}
		}		
			
			
		public virtual void SetSpeedValue(Nullable<System.Decimal> valueToSet)
		{
			SetSpeedValue(valueToSet, true, true);
		}

		public virtual void SetSpeedValue(Nullable<System.Decimal> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_speed != (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 6)))
			{
				_speed = (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 6));

				OnPropertyChanged("Speed", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Speed property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual Nullable<System.Decimal> Speed 
		{
			get	{ return _speed;}
			
			
			set
			{
				SetSpeedValue(value);
			}
		}		
			
			
		public virtual void SetStatusValue(SlamcoreTrackingStatusTypesEnum valueToSet)
		{
			SetStatusValue(valueToSet, true, true);
		}

		public virtual void SetStatusValue(SlamcoreTrackingStatusTypesEnum valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_status != valueToSet)
			{
				_status = valueToSet;

				OnPropertyChanged("Status", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("StatusDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Status property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual SlamcoreTrackingStatusTypesEnum Status 
		{
			get	{ return _status;}
			
			
			set
			{
				SetStatusValue(value);
			}
		}		
      public virtual string StatusDisplayString
		{
			get
			{
				return StatusEnumDisplayNameCollection.Where(v => v.Value == Status).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<SlamcoreTrackingStatusTypesEnum>> StatusEnumDisplayNameCollection
	    {
	        get
	        {
                return SlamcoreTrackingStatusTypesEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetTrailSequenceValue(Nullable<System.Int32> valueToSet)
		{
			SetTrailSequenceValue(valueToSet, true, true);
		}

		public virtual void SetTrailSequenceValue(Nullable<System.Int32> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_trailSequence != valueToSet)
			{
				_trailSequence = valueToSet;

				OnPropertyChanged("TrailSequence", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The TrailSequence property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual Nullable<System.Int32> TrailSequence 
		{
			get	{ return _trailSequence;}
			
			
			set
			{
				SetTrailSequenceValue(value);
			}
		}		
			
			
		public virtual void SetWOrientationValue(System.Decimal valueToSet)
		{
			SetWOrientationValue(valueToSet, true, true);
		}

		public virtual void SetWOrientationValue(System.Decimal valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_wOrientation != Math.Round(valueToSet, 17))
			{
				_wOrientation = Math.Round(valueToSet, 17);

				OnPropertyChanged("WOrientation", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The WOrientation property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual System.Decimal WOrientation 
		{
			get	{ return _wOrientation;}
			
			
			set
			{
				SetWOrientationValue(value);
			}
		}		
			
			
		public virtual void SetXOrientationValue(System.Decimal valueToSet)
		{
			SetXOrientationValue(valueToSet, true, true);
		}

		public virtual void SetXOrientationValue(System.Decimal valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_xOrientation != Math.Round(valueToSet, 17))
			{
				_xOrientation = Math.Round(valueToSet, 17);

				OnPropertyChanged("XOrientation", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The XOrientation property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual System.Decimal XOrientation 
		{
			get	{ return _xOrientation;}
			
			
			set
			{
				SetXOrientationValue(value);
			}
		}		
			
			
		public virtual void SetXPositionValue(System.Decimal valueToSet)
		{
			SetXPositionValue(valueToSet, true, true);
		}

		public virtual void SetXPositionValue(System.Decimal valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_xPosition != Math.Round(valueToSet, 17))
			{
				_xPosition = Math.Round(valueToSet, 17);

				OnPropertyChanged("XPosition", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The XPosition property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual System.Decimal XPosition 
		{
			get	{ return _xPosition;}
			
			
			set
			{
				SetXPositionValue(value);
			}
		}		
			
			
		public virtual void SetYOrientationValue(System.Decimal valueToSet)
		{
			SetYOrientationValue(valueToSet, true, true);
		}

		public virtual void SetYOrientationValue(System.Decimal valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_yOrientation != Math.Round(valueToSet, 17))
			{
				_yOrientation = Math.Round(valueToSet, 17);

				OnPropertyChanged("YOrientation", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The YOrientation property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual System.Decimal YOrientation 
		{
			get	{ return _yOrientation;}
			
			
			set
			{
				SetYOrientationValue(value);
			}
		}		
			
			
		public virtual void SetYPositionValue(System.Decimal valueToSet)
		{
			SetYPositionValue(valueToSet, true, true);
		}

		public virtual void SetYPositionValue(System.Decimal valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_yPosition != Math.Round(valueToSet, 17))
			{
				_yPosition = Math.Round(valueToSet, 17);

				OnPropertyChanged("YPosition", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The YPosition property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual System.Decimal YPosition 
		{
			get	{ return _yPosition;}
			
			
			set
			{
				SetYPositionValue(value);
			}
		}		
			
			
		public virtual void SetZPositionValue(System.Decimal valueToSet)
		{
			SetZPositionValue(valueToSet, true, true);
		}

		public virtual void SetZPositionValue(System.Decimal valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_zPosition != Math.Round(valueToSet, 17))
			{
				_zPosition = Math.Round(valueToSet, 17);

				OnPropertyChanged("ZPosition", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The ZPosition property of the VehicleSlamcoreLocationHistory DataObject</summary>
        public virtual System.Decimal ZPosition 
		{
			get	{ return _zPosition;}
			
			
			set
			{
				SetZPositionValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<VehicleSlamcoreLocationHistoryDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is VehicleSlamcoreLocationHistoryDataObject))
				return false;

			var p = (VehicleSlamcoreLocationHistoryDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.AcquisitionDateTime == p.AcquisitionDateTime;
			fieldsComparison &= this.Bearing == p.Bearing;
			fieldsComparison &= this.EventType == p.EventType;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.ReferenceFrameCategory == p.ReferenceFrameCategory;
			fieldsComparison &= this.SlamcoreDeviceId == p.SlamcoreDeviceId;
			fieldsComparison &= this.Speed == p.Speed;
			fieldsComparison &= this.Status == p.Status;
			fieldsComparison &= this.TrailSequence == p.TrailSequence;
			fieldsComparison &= this.WOrientation == p.WOrientation;
			fieldsComparison &= this.XOrientation == p.XOrientation;
			fieldsComparison &= this.XPosition == p.XPosition;
			fieldsComparison &= this.YOrientation == p.YOrientation;
			fieldsComparison &= this.YPosition == p.YPosition;
			fieldsComparison &= this.ZPosition == p.ZPosition;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// AcquisitionDateTime is a local datetime: Convert to UTC for server-side handling and storing
			if (this._acquisitionDateTime_WithTimezoneOffset != null)
			{
				this.AcquisitionDateTime = ((DateTimeOffset)this._acquisitionDateTime_WithTimezoneOffset).UtcDateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class VehicleSlamcoreLocationHistoryCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public VehicleSlamcoreLocationHistoryCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public VehicleSlamcoreLocationHistoryCollectionContainer()
		{
		}
		
		public VehicleSlamcoreLocationHistoryCollectionContainer Construct(DataObjectCollection<VehicleSlamcoreLocationHistoryDataObject> vehicleSlamcoreLocationHistoryItems)
        {
            if (vehicleSlamcoreLocationHistoryItems == null)
                return this;
				
			this.PrimaryKeys = vehicleSlamcoreLocationHistoryItems.Select(c => c.PrimaryKey).ToList();
            if (vehicleSlamcoreLocationHistoryItems.ObjectsDataSet == null)
            {
                vehicleSlamcoreLocationHistoryItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = vehicleSlamcoreLocationHistoryItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = vehicleSlamcoreLocationHistoryItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<VehicleSlamcoreLocationHistoryDataObject> ExtractVehicleSlamcoreLocationHistoryItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<VehicleSlamcoreLocationHistoryDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<VehicleSlamcoreLocationHistoryDataObject>(typeof(VehicleSlamcoreLocationHistoryDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class VehicleSlamcoreLocationHistoryContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public VehicleSlamcoreLocationHistoryContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual VehicleSlamcoreLocationHistoryContainer Construct(VehicleSlamcoreLocationHistoryDataObject vehicleSlamcoreLocationHistory, bool includeDirtyObjectsOnly = false)
		{
            if (vehicleSlamcoreLocationHistory == null)
                return this;

			this.PrimaryKey = vehicleSlamcoreLocationHistory.PrimaryKey;
			
            if (vehicleSlamcoreLocationHistory.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(vehicleSlamcoreLocationHistory);
            }

			if(vehicleSlamcoreLocationHistory.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity VehicleSlamcoreLocationHistory", "Unable to set a dataset to the entity. Container may not be initialized", "VehicleSlamcoreLocationHistoryDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : VehicleSlamcoreLocationHistory");
			}

			if(vehicleSlamcoreLocationHistory.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in VehicleSlamcoreLocationHistoryDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "VehicleSlamcoreLocationHistoryDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in VehicleSlamcoreLocationHistoryDataObject");
			}
			this.InternalObjectId = (int) vehicleSlamcoreLocationHistory.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? vehicleSlamcoreLocationHistory.ObjectsDataSet.CloneDirtyObjects() : vehicleSlamcoreLocationHistory.ObjectsDataSet;

			return this;
		}
		
		public VehicleSlamcoreLocationHistoryDataObject ExtractVehicleSlamcoreLocationHistory()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<VehicleSlamcoreLocationHistoryDataObject>(typeof(VehicleSlamcoreLocationHistoryDataObject), InternalObjectId);
			
			return result;
        }	
	}

}