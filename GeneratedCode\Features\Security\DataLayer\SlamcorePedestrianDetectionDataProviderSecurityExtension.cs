﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Dynamic;
using GenerativeObjects.Practices.ExceptionHandling;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using FleetXQ.BusinessLayer.ORMSupportClasses;


namespace FleetXQ.Features.Security.DataProviders
{
    public class SlamcorePedestrianDetectionDataProviderSecurityExtension : IDataProviderExtension<SlamcorePedestrianDetectionDataObject>
    {
 		protected IServiceProvider _serviceProvider;
        protected IConfiguration _configuration;
		protected IAuthentication _authentication;

		public SlamcorePedestrianDetectionDataProviderSecurityExtension(IServiceProvider provider, IConfiguration configuration, IAuthentication authentication)
		{
  			_serviceProvider = provider;
            _configuration = configuration;
			_authentication = authentication;
		}
		
		public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSave += OnBeforeSaveAsync;
            dataProvider.OnBeforeDelete += OnBeforeDeleteAsync;
            dataProvider.OnBeforeCount += OnBeforeCountAsync;
            dataProvider.OnBeforeGet += OnBeforeGetAsync;
            dataProvider.OnBeforeGetCollection += OnBeforeGetCollectionAsync;        
		}
        async Task OnBeforeGetCollectionAsync(OnBeforeGetCollectionEventArgs e)
        {
			if (e.SkipSecurity)
				return;

			ValueWrapper<string> messageWrapper = new ValueWrapper<string>(null);
			ValueWrapper<SecurityPredicate> predicateWrapper = new ValueWrapper<SecurityPredicate>(null);

            var claims = await _authentication.GetCurrentUserClaimsAsync();
            var permissionLevel = await _serviceProvider.GetService<IAuthorizations>().CanReadAsync(_serviceProvider.GetService<SlamcorePedestrianDetectionDataObject>(), claims, messageWrapper, predicateWrapper);

			if (permissionLevel != PermissionLevel.Authorized)
				await _authentication.ThrowAccessDeniedAsync(new GOServerException("accessDenied", String.IsNullOrEmpty(messageWrapper.Value) ? "unauthorized access" : messageWrapper.Value, new ForbiddenAccessException("forbidden access")));

			// If there is filter defined => add it to existing predicate
            if (predicateWrapper.Value != null)
            {
                e.FilterExpression = predicateWrapper.Value.Filter;
            }
		}

        async Task OnBeforeGetAsync(OnBeforeGetEventArgs e)
        {
			if (e.SkipSecurity)
				return;

            var claims = await _authentication.GetCurrentUserClaimsAsync();

			ValueWrapper<string> messageWrapper = new ValueWrapper<string>(null);
			ValueWrapper<SecurityPredicate> predicateWrapper = new ValueWrapper<SecurityPredicate>(null);

            var permissionLevel = await _serviceProvider.GetService<IAuthorizations>().CanReadAsync(e.Entity, claims, messageWrapper, predicateWrapper);
        
			if (permissionLevel != PermissionLevel.Authorized)
				await _authentication.ThrowAccessDeniedAsync(new GOServerException("accessDenied", String.IsNullOrEmpty(messageWrapper.Value) ? "unauthorized access" : messageWrapper.Value, new ForbiddenAccessException("forbidden access")));

			// If there is filter defined => we should verify the data accessed complies with the filter
			if (predicateWrapper.Value != null)
            {
				e.FilterExpression = predicateWrapper.Value.Filter;
            }
		}

        async Task OnBeforeCountAsync(OnBeforeCountEventArgs e)
        {
			if (e.SkipSecurity)
				return;

            var claims = await _authentication.GetCurrentUserClaimsAsync();

			ValueWrapper<string> messageWrapper = new ValueWrapper<string>(null);
			ValueWrapper<SecurityPredicate> predicateWrapper = new ValueWrapper<SecurityPredicate>(null);

			var permissionLevel = await _serviceProvider.GetService<IAuthorizations>().CanReadAsync(_serviceProvider.GetService<SlamcorePedestrianDetectionDataObject>(), claims, messageWrapper, predicateWrapper);

			if (permissionLevel != PermissionLevel.Authorized)
				await _authentication.ThrowAccessDeniedAsync(new GOServerException("accessDenied", String.IsNullOrEmpty(messageWrapper.Value) ? "unauthorized access" : messageWrapper.Value, new ForbiddenAccessException("forbidden access")));

			// If there is filter defined => add it to existing predicate
            if (predicateWrapper.Value != null)
            {
                e.FilterExpression = predicateWrapper.Value.Filter;
            }
		}

        async Task OnBeforeDeleteAsync(OnBeforeDeleteEventArgs e)
        {
			if (e.SkipSecurity)
				return;

			// Security is applied during delete traversal stage only (since predicates require relations to be loaded etc, can't work during commit phase)
			if (e.IsCommitPhase)
				return;

            var claims = await _authentication.GetCurrentUserClaimsAsync();

			ValueWrapper<string> messageWrapper = new ValueWrapper<string>(null);
			ValueWrapper<SecurityPredicate> predicateWrapper = new ValueWrapper<SecurityPredicate>(null);

			var permissionLevel = await _serviceProvider.GetService<IAuthorizations>().CanDeleteAsync(e.Entity, claims, messageWrapper, predicateWrapper);

			if (permissionLevel != PermissionLevel.Authorized)
				await _authentication.ThrowAccessDeniedAsync(new GOServerException("accessDenied", String.IsNullOrEmpty(messageWrapper.Value) ? "unauthorized access" : messageWrapper.Value, new ForbiddenAccessException("forbidden access")));

			// If there is filter defined => we should verify the data accessed complies with the filter
			if (predicateWrapper.Value != null)
            {
				e.FilterExpression = predicateWrapper.Value.Filter;
            }
        }

        async Task OnBeforeSaveAsync(OnBeforeSaveEventArgs e)
        {
			if (e.SkipSecurity)
				return;

            var claims = await _authentication.GetCurrentUserClaimsAsync();

            var dataset = e.Entity.ObjectsDataSet as ObjectsDataSet;

			ValueWrapper<string> messageWrapper = new ValueWrapper<string>(null);

			if (_configuration["DataSetAuthorizationCheckModeRemoveFromDataSet"] != null && Convert.ToBoolean(_configuration["DataSetAuthorizationCheckModeRemoveFromDataSet"]))
			{
				// If 'remove from dataset' option is enabled, do an explicit check of the main entity first
				// so that we don't just quietly skip saves of the context/main entity
				ValueWrapper<SecurityPredicate> predicateWrapper = new ValueWrapper<SecurityPredicate>(null);
				var permissionLevel = await _serviceProvider.GetService<IAuthorizations>().CanUpdateAsync(e.Entity, claims, messageWrapper, predicateWrapper);

				if (permissionLevel != PermissionLevel.Authorized)
					await _authentication.ThrowAccessDeniedAsync(new GOServerException("accessDenied", String.IsNullOrEmpty(messageWrapper.Value) ? "unauthorized access" : messageWrapper.Value, new ForbiddenAccessException("forbidden access")));
			}

			// Perform authorization check on full dataset because could embed multiple saves and deletes
			{
				var permissionLevel = await _serviceProvider.GetService<IAuthorizations>().CheckWriteAuthorizationsOnDataSetAsync(dataset, claims, e.Parameters, messageWrapper);

				if (permissionLevel != PermissionLevel.Authorized)
					await _authentication.ThrowAccessDeniedAsync(new GOServerException("accessDenied", String.IsNullOrEmpty(messageWrapper.Value) ? "unauthorized access" : messageWrapper.Value, new ForbiddenAccessException("forbidden access")));
			}

			// Note that because Save() may involve multiple save / deletes in a single savesset, we do not set e.FiltrExpression here
			// (because there may be a different security data filter for different entities in the saveset)
			// instead, the CheckWriteAuthorizationsOnDataSet has already checked the filter(s) (for the entire dataset)
        }
    }
} 
