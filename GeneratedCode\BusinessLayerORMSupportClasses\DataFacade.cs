﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.BusinessLayer.ORMSupportClasses
{
    public class DataFacade : IDataFacade
    {
        protected IServiceProvider _serviceProvider;

        public DataFacade(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

		public IDataProvider<AccessGroupDataObject> AccessGroupDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AccessGroupDataObject>>();
		public IDataProvider<AccessGroupTemplateDataObject> AccessGroupTemplateDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AccessGroupTemplateDataObject>>();
		public IDataProvider<AccessGroupToSiteDataObject> AccessGroupToSiteDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AccessGroupToSiteDataObject>>();
		public IDataProvider<AlertDataObject> AlertDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AlertDataObject>>();
		public IDataProvider<AlertHistoryDataObject> AlertHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AlertHistoryDataObject>>();
		public IDataProvider<AlertSubscriptionDataObject> AlertSubscriptionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AlertSubscriptionDataObject>>();
		public IDataProvider<AlertSummaryStoreProcedureDataObject> AlertSummaryStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AlertSummaryStoreProcedureDataObject>>();
		public IDataProvider<AllChecklistResultViewDataObject> AllChecklistResultViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllChecklistResultViewDataObject>>();
		public IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject> AllDriverAccessAbuseStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject>>();
		public IDataProvider<AllEmailSubscriptionStoreProcedureDataObject> AllEmailSubscriptionStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllEmailSubscriptionStoreProcedureDataObject>>();
		public IDataProvider<AllImpactsViewDataObject> AllImpactsViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllImpactsViewDataObject>>();
		public IDataProvider<AllLicenseExpiryViewDataObject> AllLicenseExpiryViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllLicenseExpiryViewDataObject>>();
		public IDataProvider<AllMessageHistoryStoreProcedureDataObject> AllMessageHistoryStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllMessageHistoryStoreProcedureDataObject>>();
		public IDataProvider<AllUserSummaryStoreProcedureDataObject> AllUserSummaryStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllUserSummaryStoreProcedureDataObject>>();
		public IDataProvider<AllVehicleCalibrationFilterDataObject> AllVehicleCalibrationFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllVehicleCalibrationFilterDataObject>>();
		public IDataProvider<AllVehicleCalibrationStoreProcedureDataObject> AllVehicleCalibrationStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllVehicleCalibrationStoreProcedureDataObject>>();
		public IDataProvider<AllVehicleUnlocksViewDataObject> AllVehicleUnlocksViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllVehicleUnlocksViewDataObject>>();
		public IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject> AllVORSessionsPerVehicleStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>>();
		public IDataProvider<AllVORStatusStoreProcedureDataObject> AllVORStatusStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<AllVORStatusStoreProcedureDataObject>>();
		public IDataProvider<BroadcastMessageDataObject> BroadcastMessageDataProvider => _serviceProvider.GetRequiredService<IDataProvider<BroadcastMessageDataObject>>();
		public IDataProvider<BroadcastMessageHistoryDataObject> BroadcastMessageHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<BroadcastMessageHistoryDataObject>>();
		public IDataProvider<BroadcastMessageHistoryFilterDataObject> BroadcastMessageHistoryFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<BroadcastMessageHistoryFilterDataObject>>();
		public IDataProvider<CanruleDataObject> CanruleDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CanruleDataObject>>();
		public IDataProvider<CanruleDetailsDataObject> CanruleDetailsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CanruleDetailsDataObject>>();
		public IDataProvider<CardDataObject> CardDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CardDataObject>>();
		public IDataProvider<CardToCardAccessDataObject> CardToCardAccessDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CardToCardAccessDataObject>>();
		public IDataProvider<CategoryTemplateDataObject> CategoryTemplateDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CategoryTemplateDataObject>>();
		public IDataProvider<ChecklistDetailDataObject> ChecklistDetailDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ChecklistDetailDataObject>>();
		public IDataProvider<ChecklistFailurePerVechicleViewDataObject> ChecklistFailurePerVechicleViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ChecklistFailurePerVechicleViewDataObject>>();
		public IDataProvider<ChecklistFailureViewDataObject> ChecklistFailureViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ChecklistFailureViewDataObject>>();
		public IDataProvider<ChecklistResultDataObject> ChecklistResultDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ChecklistResultDataObject>>();
		public IDataProvider<ChecklistSettingsDataObject> ChecklistSettingsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ChecklistSettingsDataObject>>();
		public IDataProvider<ChecklistStatusViewDataObject> ChecklistStatusViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ChecklistStatusViewDataObject>>();
		public IDataProvider<ContactPersonInformationDataObject> ContactPersonInformationDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ContactPersonInformationDataObject>>();
		public IDataProvider<CountryDataObject> CountryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CountryDataObject>>();
		public IDataProvider<CurrentDriverStatusChartViewDataObject> CurrentDriverStatusChartViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CurrentDriverStatusChartViewDataObject>>();
		public IDataProvider<CurrentStatusCombinedViewDataObject> CurrentStatusCombinedViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CurrentStatusCombinedViewDataObject>>();
		public IDataProvider<CurrentStatusDriverViewDataObject> CurrentStatusDriverViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CurrentStatusDriverViewDataObject>>();
		public IDataProvider<CurrentStatusVehicleViewDataObject> CurrentStatusVehicleViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CurrentStatusVehicleViewDataObject>>();
		public IDataProvider<CurrentVehicleStatusChartViewDataObject> CurrentVehicleStatusChartViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CurrentVehicleStatusChartViewDataObject>>();
		public IDataProvider<CustomerDataObject> CustomerDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CustomerDataObject>>();
		public IDataProvider<CustomerAuditDataObject> CustomerAuditDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CustomerAuditDataObject>>();
		public IDataProvider<CustomerFeatureSubscriptionDataObject> CustomerFeatureSubscriptionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CustomerFeatureSubscriptionDataObject>>();
		public IDataProvider<CustomerModelDataObject> CustomerModelDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CustomerModelDataObject>>();
		public IDataProvider<CustomerPreOperationalChecklistTemplateDataObject> CustomerPreOperationalChecklistTemplateDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CustomerPreOperationalChecklistTemplateDataObject>>();
		public IDataProvider<CustomerSnapshotDataObject> CustomerSnapshotDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CustomerSnapshotDataObject>>();
		public IDataProvider<CustomerSSODetailDataObject> CustomerSSODetailDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CustomerSSODetailDataObject>>();
		public IDataProvider<CustomerToModelDataObject> CustomerToModelDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CustomerToModelDataObject>>();
		public IDataProvider<CustomerToPersonViewDataObject> CustomerToPersonViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<CustomerToPersonViewDataObject>>();
		public IDataProvider<DashboardDriverCardStoreProcedureDataObject> DashboardDriverCardStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DashboardDriverCardStoreProcedureDataObject>>();
		public IDataProvider<DashboardDriverCardViewDataObject> DashboardDriverCardViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DashboardDriverCardViewDataObject>>();
		public IDataProvider<DashboardFilterDataObject> DashboardFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DashboardFilterDataObject>>();
		public IDataProvider<DashboardFilterMoreFieldsDataObject> DashboardFilterMoreFieldsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DashboardFilterMoreFieldsDataObject>>();
		public IDataProvider<DashboardVehicleCardStoreProcedureDataObject> DashboardVehicleCardStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DashboardVehicleCardStoreProcedureDataObject>>();
		public IDataProvider<DashboardVehicleCardViewDataObject> DashboardVehicleCardViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DashboardVehicleCardViewDataObject>>();
		public IDataProvider<DealerDataObject> DealerDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DealerDataObject>>();
		public IDataProvider<DealerConfigurationDataObject> DealerConfigurationDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DealerConfigurationDataObject>>();
		public IDataProvider<DealerDriverDataObject> DealerDriverDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DealerDriverDataObject>>();
		public IDataProvider<DealerFeatureSubscriptionDataObject> DealerFeatureSubscriptionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DealerFeatureSubscriptionDataObject>>();
		public IDataProvider<DepartmentDataObject> DepartmentDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DepartmentDataObject>>();
		public IDataProvider<DepartmentChecklistDataObject> DepartmentChecklistDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DepartmentChecklistDataObject>>();
		public IDataProvider<DepartmentHourSettingsDataObject> DepartmentHourSettingsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DepartmentHourSettingsDataObject>>();
		public IDataProvider<DepartmentVehicleMasterCardAccessDataObject> DepartmentVehicleMasterCardAccessDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DepartmentVehicleMasterCardAccessDataObject>>();
		public IDataProvider<DepartmentVehicleNormalCardAccessDataObject> DepartmentVehicleNormalCardAccessDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DepartmentVehicleNormalCardAccessDataObject>>();
		public IDataProvider<DetailedSessionViewDataObject> DetailedSessionViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DetailedSessionViewDataObject>>();
		public IDataProvider<DetailedVORSessionStoreProcedureDataObject> DetailedVORSessionStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DetailedVORSessionStoreProcedureDataObject>>();
		public IDataProvider<DriverDataObject> DriverDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DriverDataObject>>();
		public IDataProvider<DriverAccessAbuseFilterDataObject> DriverAccessAbuseFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DriverAccessAbuseFilterDataObject>>();
		public IDataProvider<DriverLicenseExpiryStoreProcedureDataObject> DriverLicenseExpiryStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DriverLicenseExpiryStoreProcedureDataObject>>();
		public IDataProvider<DriverLicenseExpiryViewDataObject> DriverLicenseExpiryViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DriverLicenseExpiryViewDataObject>>();
		public IDataProvider<DriverProficiencyViewDataObject> DriverProficiencyViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<DriverProficiencyViewDataObject>>();
		public IDataProvider<EmailDataObject> EmailDataProvider => _serviceProvider.GetRequiredService<IDataProvider<EmailDataObject>>();
		public IDataProvider<EmailGroupsDataObject> EmailGroupsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<EmailGroupsDataObject>>();
		public IDataProvider<EmailGroupsToPersonDataObject> EmailGroupsToPersonDataProvider => _serviceProvider.GetRequiredService<IDataProvider<EmailGroupsToPersonDataObject>>();
		public IDataProvider<EmailSubscriptionReportFilterDataObject> EmailSubscriptionReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<EmailSubscriptionReportFilterDataObject>>();
		public IDataProvider<ExportJobStatusDataObject> ExportJobStatusDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ExportJobStatusDataObject>>();
		public IDataProvider<FeatureSubscriptionsFilterDataObject> FeatureSubscriptionsFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<FeatureSubscriptionsFilterDataObject>>();
		public IDataProvider<FeatureSubscriptionTemplateDataObject> FeatureSubscriptionTemplateDataProvider => _serviceProvider.GetRequiredService<IDataProvider<FeatureSubscriptionTemplateDataObject>>();
		public IDataProvider<FirmwareDataObject> FirmwareDataProvider => _serviceProvider.GetRequiredService<IDataProvider<FirmwareDataObject>>();
		public IDataProvider<FloorPlanDataObject> FloorPlanDataProvider => _serviceProvider.GetRequiredService<IDataProvider<FloorPlanDataObject>>();
		public IDataProvider<FloorZonesDataObject> FloorZonesDataProvider => _serviceProvider.GetRequiredService<IDataProvider<FloorZonesDataObject>>();
		public IDataProvider<GeneralProductivityPerDriverViewLatestDataObject> GeneralProductivityPerDriverViewLatestDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityPerDriverViewLatestDataObject>>();
		public IDataProvider<GeneralProductivityPerVehicleViewDataObject> GeneralProductivityPerVehicleViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityPerVehicleViewDataObject>>();
		public IDataProvider<GeneralProductivityReportFilterDataObject> GeneralProductivityReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityReportFilterDataObject>>();
		public IDataProvider<GeneralProductivityViewDataObject> GeneralProductivityViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityViewDataObject>>();
		public IDataProvider<GO2FAConfigurationDataObject> GO2FAConfigurationDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GO2FAConfigurationDataObject>>();
		public IDataProvider<GOChangeDeltaDataObject> GOChangeDeltaDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOChangeDeltaDataObject>>();
		public IDataProvider<GOGroupDataObject> GOGroupDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOGroupDataObject>>();
		public IDataProvider<GOGroupRoleDataObject> GOGroupRoleDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOGroupRoleDataObject>>();
		public IDataProvider<GOLoginHistoryDataObject> GOLoginHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOLoginHistoryDataObject>>();
		public IDataProvider<GORoleDataObject> GORoleDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GORoleDataObject>>();
		public IDataProvider<GOSecurityTokensDataObject> GOSecurityTokensDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOSecurityTokensDataObject>>();
		public IDataProvider<GOTaskDataObject> GOTaskDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOTaskDataObject>>();
		public IDataProvider<GOUserDataObject> GOUserDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOUserDataObject>>();
		public IDataProvider<GOUser2FADataObject> GOUser2FADataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOUser2FADataObject>>();
		public IDataProvider<GOUserDepartmentDataObject> GOUserDepartmentDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOUserDepartmentDataObject>>();
		public IDataProvider<GOUserGroupDataObject> GOUserGroupDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOUserGroupDataObject>>();
		public IDataProvider<GOUserRoleDataObject> GOUserRoleDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GOUserRoleDataObject>>();
		public IDataProvider<GoUserToCustomerDataObject> GoUserToCustomerDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GoUserToCustomerDataObject>>();
		public IDataProvider<GPSHistoryDataObject> GPSHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<GPSHistoryDataObject>>();
		public IDataProvider<HelpDataObject> HelpDataProvider => _serviceProvider.GetRequiredService<IDataProvider<HelpDataObject>>();
		public IDataProvider<HireDeHireReportFilterDataObject> HireDeHireReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<HireDeHireReportFilterDataObject>>();
		public IDataProvider<ImpactDataObject> ImpactDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ImpactDataObject>>();
		public IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject> ImpactFrequencyPerTimeSlotViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject>>();
		public IDataProvider<ImpactFrequencyPerWeekDayViewDataObject> ImpactFrequencyPerWeekDayViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ImpactFrequencyPerWeekDayViewDataObject>>();
		public IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject> ImpactFrequencyPerWeekMonthViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject>>();
		public IDataProvider<ImpactReportFilterDataObject> ImpactReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ImpactReportFilterDataObject>>();
		public IDataProvider<ImpactsForVehicleViewDataObject> ImpactsForVehicleViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ImpactsForVehicleViewDataObject>>();
		public IDataProvider<ImportJobBatchDataObject> ImportJobBatchDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ImportJobBatchDataObject>>();
		public IDataProvider<ImportJobLogDataObject> ImportJobLogDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ImportJobLogDataObject>>();
		public IDataProvider<ImportJobStatusDataObject> ImportJobStatusDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ImportJobStatusDataObject>>();
		public IDataProvider<IncompletedChecklistViewDataObject> IncompletedChecklistViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<IncompletedChecklistViewDataObject>>();
		public IDataProvider<InspectionDataObject> InspectionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<InspectionDataObject>>();
		public IDataProvider<IOFIELDDataObject> IOFIELDDataProvider => _serviceProvider.GetRequiredService<IDataProvider<IOFIELDDataObject>>();
		public IDataProvider<IoTDeviceMessageCacheDataObject> IoTDeviceMessageCacheDataProvider => _serviceProvider.GetRequiredService<IDataProvider<IoTDeviceMessageCacheDataObject>>();
		public IDataProvider<LicenceDetailDataObject> LicenceDetailDataProvider => _serviceProvider.GetRequiredService<IDataProvider<LicenceDetailDataObject>>();
		public IDataProvider<LicenseByModelDataObject> LicenseByModelDataProvider => _serviceProvider.GetRequiredService<IDataProvider<LicenseByModelDataObject>>();
		public IDataProvider<LicenseExpiryReportFilterDataObject> LicenseExpiryReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<LicenseExpiryReportFilterDataObject>>();
		public IDataProvider<LoggedHoursVersusSeatHoursViewDataObject> LoggedHoursVersusSeatHoursViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<LoggedHoursVersusSeatHoursViewDataObject>>();
		public IDataProvider<MachineUnlockReportFilterDataObject> MachineUnlockReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<MachineUnlockReportFilterDataObject>>();
		public IDataProvider<MainDashboardFilterDataObject> MainDashboardFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<MainDashboardFilterDataObject>>();
		public IDataProvider<MessageHistoryDataObject> MessageHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<MessageHistoryDataObject>>();
		public IDataProvider<ModelDataObject> ModelDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ModelDataObject>>();
		public IDataProvider<ModelVehicleMasterCardAccessDataObject> ModelVehicleMasterCardAccessDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ModelVehicleMasterCardAccessDataObject>>();
		public IDataProvider<ModelVehicleNormalCardAccessDataObject> ModelVehicleNormalCardAccessDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ModelVehicleNormalCardAccessDataObject>>();
		public IDataProvider<ModuleDataObject> ModuleDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ModuleDataObject>>();
		public IDataProvider<ModuleHistoryDataObject> ModuleHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ModuleHistoryDataObject>>();
		public IDataProvider<NetworkSettingsDataObject> NetworkSettingsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<NetworkSettingsDataObject>>();
		public IDataProvider<OnDemandAuthorisationFilterDataObject> OnDemandAuthorisationFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<OnDemandAuthorisationFilterDataObject>>();
		public IDataProvider<OnDemandAuthorisationStoreProcedureDataObject> OnDemandAuthorisationStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<OnDemandAuthorisationStoreProcedureDataObject>>();
		public IDataProvider<OnDemandSessionDataObject> OnDemandSessionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<OnDemandSessionDataObject>>();
		public IDataProvider<OnDemandSettingsDataObject> OnDemandSettingsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<OnDemandSettingsDataObject>>();
		public IDataProvider<PedestrianDetectionHistoryDataObject> PedestrianDetectionHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PedestrianDetectionHistoryDataObject>>();
		public IDataProvider<PedestrianDetectionHistoryFilterDataObject> PedestrianDetectionHistoryFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PedestrianDetectionHistoryFilterDataObject>>();
		public IDataProvider<PermissionDataObject> PermissionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PermissionDataObject>>();
		public IDataProvider<PersonDataObject> PersonDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonDataObject>>();
		public IDataProvider<PersonAllocationDataObject> PersonAllocationDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonAllocationDataObject>>();
		public IDataProvider<PersonChecklistLanguageSettingsDataObject> PersonChecklistLanguageSettingsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonChecklistLanguageSettingsDataObject>>();
		public IDataProvider<PersonToDepartmentVehicleMasterAccessViewDataObject> PersonToDepartmentVehicleMasterAccessViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonToDepartmentVehicleMasterAccessViewDataObject>>();
		public IDataProvider<PersonToDepartmentVehicleNormalAccessViewDataObject> PersonToDepartmentVehicleNormalAccessViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonToDepartmentVehicleNormalAccessViewDataObject>>();
		public IDataProvider<PersonToModelVehicleMasterAccessViewDataObject> PersonToModelVehicleMasterAccessViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonToModelVehicleMasterAccessViewDataObject>>();
		public IDataProvider<PersonToModelVehicleNormalAccessViewDataObject> PersonToModelVehicleNormalAccessViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonToModelVehicleNormalAccessViewDataObject>>();
		public IDataProvider<PersonToPerVehicleMasterAccessViewDataObject> PersonToPerVehicleMasterAccessViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonToPerVehicleMasterAccessViewDataObject>>();
		public IDataProvider<PersonToPerVehicleNormalAccessViewDataObject> PersonToPerVehicleNormalAccessViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonToPerVehicleNormalAccessViewDataObject>>();
		public IDataProvider<PersonToSiteVehicleMasterAccessViewDataObject> PersonToSiteVehicleMasterAccessViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonToSiteVehicleMasterAccessViewDataObject>>();
		public IDataProvider<PersonToSiteVehicleNormalAccessViewDataObject> PersonToSiteVehicleNormalAccessViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PersonToSiteVehicleNormalAccessViewDataObject>>();
		public IDataProvider<PerVehicleMasterCardAccessDataObject> PerVehicleMasterCardAccessDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PerVehicleMasterCardAccessDataObject>>();
		public IDataProvider<PerVehicleNormalCardAccessDataObject> PerVehicleNormalCardAccessDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PerVehicleNormalCardAccessDataObject>>();
		public IDataProvider<PreOperationalChecklistDataObject> PreOperationalChecklistDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PreOperationalChecklistDataObject>>();
		public IDataProvider<PreOpReportFilterDataObject> PreOpReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PreOpReportFilterDataObject>>();
		public IDataProvider<ProficiencyCombinedViewDataObject> ProficiencyCombinedViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ProficiencyCombinedViewDataObject>>();
		public IDataProvider<ProficiencyReportFilterDataObject> ProficiencyReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ProficiencyReportFilterDataObject>>();
		public IDataProvider<PSTATDetailsDataObject> PSTATDetailsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<PSTATDetailsDataObject>>();
		public IDataProvider<RegionDataObject> RegionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<RegionDataObject>>();
		public IDataProvider<ReportSubscriptionDataObject> ReportSubscriptionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ReportSubscriptionDataObject>>();
		public IDataProvider<ReportTypeDataObject> ReportTypeDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ReportTypeDataObject>>();
		public IDataProvider<RevisionDataObject> RevisionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<RevisionDataObject>>();
		public IDataProvider<ServiceSettingsDataObject> ServiceSettingsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ServiceSettingsDataObject>>();
		public IDataProvider<SessionDataObject> SessionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SessionDataObject>>();
		public IDataProvider<SessionDetailsDataObject> SessionDetailsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SessionDetailsDataObject>>();
		public IDataProvider<SiteDataObject> SiteDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SiteDataObject>>();
		public IDataProvider<SiteFloorPlanDataObject> SiteFloorPlanDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SiteFloorPlanDataObject>>();
		public IDataProvider<SiteVehicleMasterCardAccessDataObject> SiteVehicleMasterCardAccessDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SiteVehicleMasterCardAccessDataObject>>();
		public IDataProvider<SiteVehicleNormalCardAccessDataObject> SiteVehicleNormalCardAccessDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SiteVehicleNormalCardAccessDataObject>>();
		public IDataProvider<SlamcoreAPIKeyDataObject> SlamcoreAPIKeyDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreAPIKeyDataObject>>();
		public IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject> SlamcoreAwareAuthenticationDetailsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject>>();
		public IDataProvider<SlamcoreDeviceDataObject> SlamcoreDeviceDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceDataObject>>();
		public IDataProvider<SlamcoreDeviceConnectionViewDataObject> SlamcoreDeviceConnectionViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceConnectionViewDataObject>>();
		public IDataProvider<SlamcoreDeviceFilterDataObject> SlamcoreDeviceFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceFilterDataObject>>();
		public IDataProvider<SlamcoreDeviceHistoryDataObject> SlamcoreDeviceHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceHistoryDataObject>>();
		public IDataProvider<SlamcorePedestrianDetectionDataObject> SlamcorePedestrianDetectionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SlamcorePedestrianDetectionDataObject>>();
		public IDataProvider<SnapshotDataObject> SnapshotDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SnapshotDataObject>>();
		public IDataProvider<SynchronizationStatusReportFilterDataObject> SynchronizationStatusReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<SynchronizationStatusReportFilterDataObject>>();
		public IDataProvider<TagDataObject> TagDataProvider => _serviceProvider.GetRequiredService<IDataProvider<TagDataObject>>();
		public IDataProvider<TimezoneDataObject> TimezoneDataProvider => _serviceProvider.GetRequiredService<IDataProvider<TimezoneDataObject>>();
		public IDataProvider<TodaysImpactStoreProcedureDataObject> TodaysImpactStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<TodaysImpactStoreProcedureDataObject>>();
		public IDataProvider<TodaysImpactViewDataObject> TodaysImpactViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<TodaysImpactViewDataObject>>();
		public IDataProvider<TodaysPreopCheckStoreProcedureDataObject> TodaysPreopCheckStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<TodaysPreopCheckStoreProcedureDataObject>>();
		public IDataProvider<TodaysPreopCheckViewDataObject> TodaysPreopCheckViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<TodaysPreopCheckViewDataObject>>();
		public IDataProvider<UnitSummaryReportDataObject> UnitSummaryReportDataProvider => _serviceProvider.GetRequiredService<IDataProvider<UnitSummaryReportDataObject>>();
		public IDataProvider<UnitSummaryStoreProcedureDataObject> UnitSummaryStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<UnitSummaryStoreProcedureDataObject>>();
		public IDataProvider<UnitUnutilisationStoreProcedureDataObject> UnitUnutilisationStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<UnitUnutilisationStoreProcedureDataObject>>();
		public IDataProvider<UnitUtilisationCombinedViewDataObject> UnitUtilisationCombinedViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<UnitUtilisationCombinedViewDataObject>>();
		public IDataProvider<UnitUtilisationStoreProcedureDataObject> UnitUtilisationStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<UnitUtilisationStoreProcedureDataObject>>();
		public IDataProvider<UpdateFirmwareRequestDataObject> UpdateFirmwareRequestDataProvider => _serviceProvider.GetRequiredService<IDataProvider<UpdateFirmwareRequestDataObject>>();
		public IDataProvider<UploadLogoRequestDataObject> UploadLogoRequestDataProvider => _serviceProvider.GetRequiredService<IDataProvider<UploadLogoRequestDataObject>>();
		public IDataProvider<VehicleDataObject> VehicleDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleDataObject>>();
		public IDataProvider<VehicleAlertSubscriptionDataObject> VehicleAlertSubscriptionDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleAlertSubscriptionDataObject>>();
		public IDataProvider<VehicleBroadcastMessageDataObject> VehicleBroadcastMessageDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleBroadcastMessageDataObject>>();
		public IDataProvider<VehicleDiagnosticDataObject> VehicleDiagnosticDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleDiagnosticDataObject>>();
		public IDataProvider<VehicleGPSDataObject> VehicleGPSDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleGPSDataObject>>();
		public IDataProvider<VehicleHireDehireHistoryDataObject> VehicleHireDehireHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleHireDehireHistoryDataObject>>();
		public IDataProvider<VehicleHireDehireSynchronizationOptionsDataObject> VehicleHireDehireSynchronizationOptionsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleHireDehireSynchronizationOptionsDataObject>>();
		public IDataProvider<VehicleLastGPSLocationViewDataObject> VehicleLastGPSLocationViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleLastGPSLocationViewDataObject>>();
		public IDataProvider<VehicleLockoutDataObject> VehicleLockoutDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleLockoutDataObject>>();
		public IDataProvider<VehicleOtherSettingsDataObject> VehicleOtherSettingsDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleOtherSettingsDataObject>>();
		public IDataProvider<VehicleProficiencyViewDataObject> VehicleProficiencyViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleProficiencyViewDataObject>>();
		public IDataProvider<VehicleSessionlessImpactDataObject> VehicleSessionlessImpactDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleSessionlessImpactDataObject>>();
		public IDataProvider<VehicleSlamcoreLocationHistoryDataObject> VehicleSlamcoreLocationHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleSlamcoreLocationHistoryDataObject>>();
		public IDataProvider<VehiclesPerModelReportDataObject> VehiclesPerModelReportDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehiclesPerModelReportDataObject>>();
		public IDataProvider<VehicleSupervisorsViewDataObject> VehicleSupervisorsViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleSupervisorsViewDataObject>>();
		public IDataProvider<VehicleToPreOpChecklistViewDataObject> VehicleToPreOpChecklistViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleToPreOpChecklistViewDataObject>>();
		public IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> VehicleUtilizationLastTwelveHoursStoreProcedureDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>>();
		public IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject> VehicleUtilizationLastTwelveHoursViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject>>();
		public IDataProvider<VORReportCombinedViewDataObject> VORReportCombinedViewDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VORReportCombinedViewDataObject>>();
		public IDataProvider<VORReportFilterDataObject> VORReportFilterDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VORReportFilterDataObject>>();
		public IDataProvider<VORSettingHistoryDataObject> VORSettingHistoryDataProvider => _serviceProvider.GetRequiredService<IDataProvider<VORSettingHistoryDataObject>>();
		public IDataProvider<WebsiteRoleDataObject> WebsiteRoleDataProvider => _serviceProvider.GetRequiredService<IDataProvider<WebsiteRoleDataObject>>();
		public IDataProvider<WebsiteUserDataObject> WebsiteUserDataProvider => _serviceProvider.GetRequiredService<IDataProvider<WebsiteUserDataObject>>();
		public IDataProvider<ZoneCoordinatesDataObject> ZoneCoordinatesDataProvider => _serviceProvider.GetRequiredService<IDataProvider<ZoneCoordinatesDataObject>>();
 
	}
}
