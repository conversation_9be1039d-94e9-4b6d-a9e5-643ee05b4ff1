﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using Newtonsoft.Json;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Components.Server;
using GenerativeObjects.Practices.Logging;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Settings;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Microsoft.AspNetCore.Antiforgery;
using NLog;

namespace FleetXQ.ServiceLayer.ComponentApiControllers
{
    [ApiController]    
    [ServiceFilter(typeof(ObjectGraphOrDataSetSerializationFilterAttribute))]
	public class SlamcoreVehicleTelemetryExportComponentApiController : GOControllerBase
    {
        public SlamcoreVehicleTelemetryExportComponentApiController(IServiceProvider serviceProvider, IConfiguration configuration, ISettingsProvider settingsProvider, ILogEngine logEngine, IAntiforgery antiforgery) : base (serviceProvider, settingsProvider, logEngine)
        {
			_configuration = configuration;	
			_antiforgery = antiforgery;
        }

        private readonly IConfiguration _configuration;
        private readonly Logger _techLogger = LogManager.GetCurrentClassLogger();
		private readonly IAntiforgery _antiforgery;

        private bool _showExceptionDetails => _configuration["ShowExceptionDetails"] == StringValues.Empty ? false : Convert.ToBoolean(_configuration["ShowExceptionDetails"]);
		
        [HttpPost]
		[Route("dataset/api/slamcorevehicletelemetryexportcomponent/export")]
        public async Task<ActionResult<ExportJobStatusContainer>> ExportDataSet([FromForm] System.String filterPredicate, [FromForm] System.String filterParameters)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "filterPredicate", filterPredicate?.ToString() },
					{ "filterParameters", filterParameters?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "Export", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				var response = await _serviceProvider.GetRequiredService<ISlamcoreVehicleTelemetryExportComponentSurrogate>().ExportAsync(filterPredicate, filterParameters, null);

				var componentReturn = response;
				var result = _serviceProvider.GetRequiredService<ExportJobStatusContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "Export", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/slamcorevehicletelemetryexportcomponent/export")]
        public async Task<ActionResult<ExportJobStatusContainer>> Export([FromForm] System.String filterPredicate, [FromForm] System.String filterParameters)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "filterPredicate", filterPredicate?.ToString() },
					{ "filterParameters", filterParameters?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "Export", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				var response = await _serviceProvider.GetRequiredService<ISlamcoreVehicleTelemetryExportComponentSurrogate>().ExportAsync(filterPredicate, filterParameters);

				var componentReturn = response;
				var result = _serviceProvider.GetRequiredService<ExportJobStatusContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "Export", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

    }
}