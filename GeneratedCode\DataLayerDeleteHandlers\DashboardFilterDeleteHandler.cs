﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;

using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DeleteHandlers
{
	public partial class DashboardFilterDeleteHandler : DeleteHandlerBase<DashboardFilterDataObject>
	{
		
        public DashboardFilterDeleteHandler(IServiceProvider serviceProvider, IDataProvider<DashboardFilterDataObject> tentityDataProvider, ITransactionProvider transactionProvider, IEntityDataProvider entityDataProvider) : base(serviceProvider, tentityDataProvider, transactionProvider, entityDataProvider)
        {
        }

		public override async Task RippleDeleteAsync(DashboardFilterDataObject instance, Parameters parameters, DataProviderDeleteSettings settings)
		{
			// Set resync flag initially so that if any processing is done, it's on the latest copy of the data
			NeedResync = true;

			// DashboardFilter ripple to sub-type AllVehicleCalibrationFilter (Hierarchy)
			{
				if (instance is AllVehicleCalibrationFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<AllVehicleCalibrationFilterDataObject>>() as DeleteHandlerBase<AllVehicleCalibrationFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as AllVehicleCalibrationFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type BroadcastMessageHistoryFilter (Hierarchy)
			{
				if (instance is BroadcastMessageHistoryFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<BroadcastMessageHistoryFilterDataObject>>() as DeleteHandlerBase<BroadcastMessageHistoryFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as BroadcastMessageHistoryFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type DashboardFilterMoreFields (Hierarchy)
			{
				if (instance is DashboardFilterMoreFieldsDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<DashboardFilterMoreFieldsDataObject>>() as DeleteHandlerBase<DashboardFilterMoreFieldsDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as DashboardFilterMoreFieldsDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type DriverAccessAbuseFilter (Hierarchy)
			{
				if (instance is DriverAccessAbuseFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<DriverAccessAbuseFilterDataObject>>() as DeleteHandlerBase<DriverAccessAbuseFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as DriverAccessAbuseFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type EmailSubscriptionReportFilter (Hierarchy)
			{
				if (instance is EmailSubscriptionReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<EmailSubscriptionReportFilterDataObject>>() as DeleteHandlerBase<EmailSubscriptionReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as EmailSubscriptionReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type FeatureSubscriptionsFilter (Hierarchy)
			{
				if (instance is FeatureSubscriptionsFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<FeatureSubscriptionsFilterDataObject>>() as DeleteHandlerBase<FeatureSubscriptionsFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as FeatureSubscriptionsFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type GeneralProductivityReportFilter (Hierarchy)
			{
				if (instance is GeneralProductivityReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<GeneralProductivityReportFilterDataObject>>() as DeleteHandlerBase<GeneralProductivityReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as GeneralProductivityReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type HireDeHireReportFilter (Hierarchy)
			{
				if (instance is HireDeHireReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<HireDeHireReportFilterDataObject>>() as DeleteHandlerBase<HireDeHireReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as HireDeHireReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type ImpactReportFilter (Hierarchy)
			{
				if (instance is ImpactReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<ImpactReportFilterDataObject>>() as DeleteHandlerBase<ImpactReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as ImpactReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type LicenseExpiryReportFilter (Hierarchy)
			{
				if (instance is LicenseExpiryReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<LicenseExpiryReportFilterDataObject>>() as DeleteHandlerBase<LicenseExpiryReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as LicenseExpiryReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type MachineUnlockReportFilter (Hierarchy)
			{
				if (instance is MachineUnlockReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<MachineUnlockReportFilterDataObject>>() as DeleteHandlerBase<MachineUnlockReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as MachineUnlockReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type MainDashboardFilter (Hierarchy)
			{
				if (instance is MainDashboardFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<MainDashboardFilterDataObject>>() as DeleteHandlerBase<MainDashboardFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as MainDashboardFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type OnDemandAuthorisationFilter (Hierarchy)
			{
				if (instance is OnDemandAuthorisationFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<OnDemandAuthorisationFilterDataObject>>() as DeleteHandlerBase<OnDemandAuthorisationFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as OnDemandAuthorisationFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type PedestrianDetectionHistoryFilter (Hierarchy)
			{
				if (instance is PedestrianDetectionHistoryFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<PedestrianDetectionHistoryFilterDataObject>>() as DeleteHandlerBase<PedestrianDetectionHistoryFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as PedestrianDetectionHistoryFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type PreOpReportFilter (Hierarchy)
			{
				if (instance is PreOpReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<PreOpReportFilterDataObject>>() as DeleteHandlerBase<PreOpReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as PreOpReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type ProficiencyReportFilter (Hierarchy)
			{
				if (instance is ProficiencyReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<ProficiencyReportFilterDataObject>>() as DeleteHandlerBase<ProficiencyReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as ProficiencyReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type SlamcoreDeviceFilter (Hierarchy)
			{
				if (instance is SlamcoreDeviceFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<SlamcoreDeviceFilterDataObject>>() as DeleteHandlerBase<SlamcoreDeviceFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as SlamcoreDeviceFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type SynchronizationStatusReportFilter (Hierarchy)
			{
				if (instance is SynchronizationStatusReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<SynchronizationStatusReportFilterDataObject>>() as DeleteHandlerBase<SynchronizationStatusReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as SynchronizationStatusReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// DashboardFilter ripple to sub-type VORReportFilter (Hierarchy)
			{
				if (instance is VORReportFilterDataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<VORReportFilterDataObject>>() as DeleteHandlerBase<VORReportFilterDataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as VORReportFilterDataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
		}
	}
}