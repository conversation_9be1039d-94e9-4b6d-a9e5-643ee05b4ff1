﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreDeviceFilterObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _slamcoreDeviceFilterObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all SlamcoreDeviceFilter objects for current dataset
		private ConcurrentDictionary< int, SlamcoreDeviceFilterDataObject> _slamcoreDeviceFilterObjects = new ConcurrentDictionary< int, SlamcoreDeviceFilterDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<SlamcoreDeviceFilterDataObject> _mergedDataObjects;

		private ConcurrentQueue<SlamcoreDeviceFilterDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<SlamcoreDeviceFilterDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> SlamcoreDeviceFilterObjectInternalIds
		{ 
			get { return _slamcoreDeviceFilterObjectInternalIds; }
			set { _slamcoreDeviceFilterObjectInternalIds = value; }
		}
		
		// Collection holding all SlamcoreDeviceFilter objects for current dataset
		[JsonProperty("SlamcoreDeviceFilterObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, SlamcoreDeviceFilterDataObject> SlamcoreDeviceFilterObjects
		{ 
			get { return _slamcoreDeviceFilterObjects; }
			set { _slamcoreDeviceFilterObjects = value; }
		}
		
		// Index to quickly find all SlamcoreDeviceFilter with a given customer foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Customer_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		// Index to quickly find all SlamcoreDeviceFilter with a given department foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Department_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		// Index to quickly find all SlamcoreDeviceFilter with a given site foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Site_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public SlamcoreDeviceFilterObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public SlamcoreDeviceFilterObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<SlamcoreDeviceFilterObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SlamcoreDeviceFilterObjects)
			{
                var cloneObject = (SlamcoreDeviceFilterDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcoreDeviceFilterObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SlamcoreDeviceFilterObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcoreDeviceFilterObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<SlamcoreDeviceFilterObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SlamcoreDeviceFilterObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (SlamcoreDeviceFilterDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.SlamcoreDeviceFilterObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SlamcoreDeviceFilterObjectInternalIds
				.Where(o => this.SlamcoreDeviceFilterObjects[o.Value].IsDirty || this.SlamcoreDeviceFilterObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcoreDeviceFilterObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var slamcoreDeviceFilter in SlamcoreDeviceFilterObjects.Values)
			{
				yield return slamcoreDeviceFilter; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the SlamcoreDeviceFilterObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "SlamcoreDeviceFilterObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcoreDeviceFilterObjects.TryAdd(newInternalId, (SlamcoreDeviceFilterDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (SlamcoreDeviceFilterObjectInternalIds.ContainsKey(((SlamcoreDeviceFilterDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = SlamcoreDeviceFilterObjectInternalIds.TryRemove(((SlamcoreDeviceFilterDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcoreDeviceFilterObjectInternalIds.TryAdd(((SlamcoreDeviceFilterDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as SlamcoreDeviceFilterDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "SlamcoreDeviceFilterDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

			// Update the Customer FK Index 
			if (!Customer_FKIndex.ContainsKey((objectToAdd as SlamcoreDeviceFilterDataObject).CustomerId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Customer_FKIndex.TryAdd((objectToAdd as SlamcoreDeviceFilterDataObject).CustomerId, new List<int>());
				}
			}
				
			if (!Customer_FKIndex[(objectToAdd as SlamcoreDeviceFilterDataObject).CustomerId].Contains(newInternalId))
				Customer_FKIndex[(objectToAdd as SlamcoreDeviceFilterDataObject).CustomerId].Add(newInternalId);

            CustomerDataObject relatedCustomer;
            if ((objectToAdd as SlamcoreDeviceFilterDataObject)._customer_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as SlamcoreDeviceFilterDataObject)._customer_NewObjectId;

	            relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToAdd as SlamcoreDeviceFilterDataObject).CustomerId) { IsNew = false });
            }

			if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
                relatedCustomer.NotifyPropertyChanged("DashboardFilterItems", new SeenObjectCollection());
			
	 
			// Update the Department FK Index 
			if (!Department_FKIndex.ContainsKey((objectToAdd as SlamcoreDeviceFilterDataObject).DepartmentId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Department_FKIndex.TryAdd((objectToAdd as SlamcoreDeviceFilterDataObject).DepartmentId, new List<int>());
				}
			}
				
			if (!Department_FKIndex[(objectToAdd as SlamcoreDeviceFilterDataObject).DepartmentId].Contains(newInternalId))
				Department_FKIndex[(objectToAdd as SlamcoreDeviceFilterDataObject).DepartmentId].Add(newInternalId);

            DepartmentDataObject relatedDepartment;
            if ((objectToAdd as SlamcoreDeviceFilterDataObject)._department_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<DepartmentDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as SlamcoreDeviceFilterDataObject)._department_NewObjectId;

	            relatedDepartment = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedDepartment = _rootObjectDataSet.GetObject(new DepartmentDataObject((objectToAdd as SlamcoreDeviceFilterDataObject).DepartmentId) { IsNew = false });
            }

			if (relatedDepartment != null && this.RootObjectDataSet.NotifyChanges)
                relatedDepartment.NotifyPropertyChanged("DashboardFilterItems", new SeenObjectCollection());
			
	 
			// Update the Site FK Index 
			if (!Site_FKIndex.ContainsKey((objectToAdd as SlamcoreDeviceFilterDataObject).SiteId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Site_FKIndex.TryAdd((objectToAdd as SlamcoreDeviceFilterDataObject).SiteId, new List<int>());
				}
			}
				
			if (!Site_FKIndex[(objectToAdd as SlamcoreDeviceFilterDataObject).SiteId].Contains(newInternalId))
				Site_FKIndex[(objectToAdd as SlamcoreDeviceFilterDataObject).SiteId].Add(newInternalId);

            SiteDataObject relatedSite;
            if ((objectToAdd as SlamcoreDeviceFilterDataObject)._site_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<SiteDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as SlamcoreDeviceFilterDataObject)._site_NewObjectId;

	            relatedSite = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedSite = _rootObjectDataSet.GetObject(new SiteDataObject((objectToAdd as SlamcoreDeviceFilterDataObject).SiteId) { IsNew = false });
            }

			if (relatedSite != null && this.RootObjectDataSet.NotifyChanges)
                relatedSite.NotifyPropertyChanged("DashboardFilterItems", new SeenObjectCollection());
			
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (SlamcoreDeviceFilterObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as SlamcoreDeviceFilterDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "SlamcoreDeviceFilterObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = SlamcoreDeviceFilterObjectInternalIds.ContainsKey((objectToRemove as SlamcoreDeviceFilterDataObject).PrimaryKey) ? (int?) SlamcoreDeviceFilterObjectInternalIds[(objectToRemove as SlamcoreDeviceFilterDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				SlamcoreDeviceFilterDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcoreDeviceFilterObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = SlamcoreDeviceFilterObjectInternalIds.TryRemove((objectToRemove as SlamcoreDeviceFilterDataObject).PrimaryKey, out idvalue);
					}
				}
				
			// Delete the Customer FK Index 
				if (Customer_FKIndex.ContainsKey((objectToRemove as SlamcoreDeviceFilterDataObject).CustomerId) && Customer_FKIndex[(objectToRemove as SlamcoreDeviceFilterDataObject).CustomerId].Contains((int)objectToRemoveInternalId))
				{
					Customer_FKIndex[(objectToRemove as SlamcoreDeviceFilterDataObject).CustomerId].Remove((int)objectToRemoveInternalId);

					if (!Customer_FKIndex[(objectToRemove as SlamcoreDeviceFilterDataObject).CustomerId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Customer_FKIndex.TryRemove((objectToRemove as SlamcoreDeviceFilterDataObject).CustomerId, out outvalue);
						}
					}
				}
				
				CustomerDataObject relatedCustomer;
	            if ((objectToRemove as SlamcoreDeviceFilterDataObject)._customer_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as SlamcoreDeviceFilterDataObject)._customer_NewObjectId;

					relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToRemove as SlamcoreDeviceFilterDataObject).CustomerId) { IsNew = false });
	            }

	            if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
	                relatedCustomer.NotifyPropertyChanged("DashboardFilterItems", new SeenObjectCollection());
				
		 
			// Delete the Department FK Index 
				if (Department_FKIndex.ContainsKey((objectToRemove as SlamcoreDeviceFilterDataObject).DepartmentId) && Department_FKIndex[(objectToRemove as SlamcoreDeviceFilterDataObject).DepartmentId].Contains((int)objectToRemoveInternalId))
				{
					Department_FKIndex[(objectToRemove as SlamcoreDeviceFilterDataObject).DepartmentId].Remove((int)objectToRemoveInternalId);

					if (!Department_FKIndex[(objectToRemove as SlamcoreDeviceFilterDataObject).DepartmentId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Department_FKIndex.TryRemove((objectToRemove as SlamcoreDeviceFilterDataObject).DepartmentId, out outvalue);
						}
					}
				}
				
				DepartmentDataObject relatedDepartment;
	            if ((objectToRemove as SlamcoreDeviceFilterDataObject)._department_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<DepartmentDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as SlamcoreDeviceFilterDataObject)._department_NewObjectId;

					relatedDepartment = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedDepartment = _rootObjectDataSet.GetObject(new DepartmentDataObject((objectToRemove as SlamcoreDeviceFilterDataObject).DepartmentId) { IsNew = false });
	            }

	            if (relatedDepartment != null && this.RootObjectDataSet.NotifyChanges)
	                relatedDepartment.NotifyPropertyChanged("DashboardFilterItems", new SeenObjectCollection());
				
		 
			// Delete the Site FK Index 
				if (Site_FKIndex.ContainsKey((objectToRemove as SlamcoreDeviceFilterDataObject).SiteId) && Site_FKIndex[(objectToRemove as SlamcoreDeviceFilterDataObject).SiteId].Contains((int)objectToRemoveInternalId))
				{
					Site_FKIndex[(objectToRemove as SlamcoreDeviceFilterDataObject).SiteId].Remove((int)objectToRemoveInternalId);

					if (!Site_FKIndex[(objectToRemove as SlamcoreDeviceFilterDataObject).SiteId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Site_FKIndex.TryRemove((objectToRemove as SlamcoreDeviceFilterDataObject).SiteId, out outvalue);
						}
					}
				}
				
				SiteDataObject relatedSite;
	            if ((objectToRemove as SlamcoreDeviceFilterDataObject)._site_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<SiteDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as SlamcoreDeviceFilterDataObject)._site_NewObjectId;

					relatedSite = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedSite = _rootObjectDataSet.GetObject(new SiteDataObject((objectToRemove as SlamcoreDeviceFilterDataObject).SiteId) { IsNew = false });
	            }

	            if (relatedSite != null && this.RootObjectDataSet.NotifyChanges)
	                relatedSite.NotifyPropertyChanged("DashboardFilterItems", new SeenObjectCollection());
				
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return SlamcoreDeviceFilterObjects.ContainsKey(internalObjectId) ? SlamcoreDeviceFilterObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as DashboardFilterDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "SlamcoreDeviceFilterObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = SlamcoreDeviceFilterObjectInternalIds.ContainsKey((objectToGet as DashboardFilterDataObject).PrimaryKey) ? (int?) SlamcoreDeviceFilterObjectInternalIds[(objectToGet as DashboardFilterDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return SlamcoreDeviceFilterObjects.ContainsKey((int)objectToGetInternalId) ? SlamcoreDeviceFilterObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return SlamcoreDeviceFilterObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return SlamcoreDeviceFilterObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		
		public IEnumerable<SlamcoreDeviceFilterDataObject> GetDashboardFilterItemsForCustomer(CustomerDataObject customerInstance) 
		{
			if (customerInstance.IsNew)
            {
			
              return SlamcoreDeviceFilterObjects.Where(o => o.Value._customer_NewObjectId != null && o.Value._customer_NewObjectId == customerInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Customer_FKIndex.ContainsKey(customerInstance.Id))
			{
				return Customer_FKIndex[customerInstance.Id].Where(e => SlamcoreDeviceFilterObjects.ContainsKey(e)).Select(e => SlamcoreDeviceFilterObjects[e]);
			}
			
			return new DataObjectCollection<SlamcoreDeviceFilterDataObject>();
		}
		 
		
		public IEnumerable<SlamcoreDeviceFilterDataObject> GetDashboardFilterItemsForDepartment(DepartmentDataObject departmentInstance) 
		{
			if (departmentInstance.IsNew)
            {
			
              return SlamcoreDeviceFilterObjects.Where(o => o.Value._department_NewObjectId != null && o.Value._department_NewObjectId == departmentInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Department_FKIndex.ContainsKey(departmentInstance.Id))
			{
				return Department_FKIndex[departmentInstance.Id].Where(e => SlamcoreDeviceFilterObjects.ContainsKey(e)).Select(e => SlamcoreDeviceFilterObjects[e]);
			}
			
			return new DataObjectCollection<SlamcoreDeviceFilterDataObject>();
		}
		 
		
		public IEnumerable<SlamcoreDeviceFilterDataObject> GetDashboardFilterItemsForSite(SiteDataObject siteInstance) 
		{
			if (siteInstance.IsNew)
            {
			
              return SlamcoreDeviceFilterObjects.Where(o => o.Value._site_NewObjectId != null && o.Value._site_NewObjectId == siteInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Site_FKIndex.ContainsKey(siteInstance.Id))
			{
				return Site_FKIndex[siteInstance.Id].Where(e => SlamcoreDeviceFilterObjects.ContainsKey(e)).Select(e => SlamcoreDeviceFilterObjects[e]);
			}
			
			return new DataObjectCollection<SlamcoreDeviceFilterDataObject>();
		}
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
 
 
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var SlamcoreDeviceFilterDataSet = dataSetToMerge as SlamcoreDeviceFilterObjectsDataSet;
				if(SlamcoreDeviceFilterDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in SlamcoreDeviceFilterDataSet.SlamcoreDeviceFilterObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "SlamcoreDeviceFilterObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as SlamcoreDeviceFilterDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
			// Reconstruct the Customer FK Index 
			Customer_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in SlamcoreDeviceFilterObjects.Values)
			{
				if (item.CustomerId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.CustomerId;	

				if (!Customer_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Customer_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SlamcoreDeviceFilterObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Customer_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the Department FK Index 
			Department_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in SlamcoreDeviceFilterObjects.Values)
			{
				if (item.DepartmentId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.DepartmentId;	

				if (!Department_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Department_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SlamcoreDeviceFilterObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Department_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the Site FK Index 
			Site_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in SlamcoreDeviceFilterObjects.Values)
			{
				if (item.SiteId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.SiteId;	

				if (!Site_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Site_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SlamcoreDeviceFilterObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Site_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (SlamcoreDeviceFilterObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}