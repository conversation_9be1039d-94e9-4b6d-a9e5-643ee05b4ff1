﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class VehicleObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _vehicleObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all Vehicle objects for current dataset
		private ConcurrentDictionary< int, VehicleDataObject> _vehicleObjects = new ConcurrentDictionary< int, VehicleDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<VehicleDataObject> _mergedDataObjects;

		private ConcurrentQueue<VehicleDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<VehicleDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> VehicleObjectInternalIds
		{ 
			get { return _vehicleObjectInternalIds; }
			set { _vehicleObjectInternalIds = value; }
		}
		
		// Collection holding all Vehicle objects for current dataset
		[JsonProperty("VehicleObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, VehicleDataObject> VehicleObjects
		{ 
			get { return _vehicleObjects; }
			set { _vehicleObjects = value; }
		}
		
		
 
		
 
		
 
		
 
		
 
		// Index to quickly find all Vehicle with a given canrule foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Canrule_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		// Index to quickly find all Vehicle with a given checklistSettings foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> ChecklistSettings_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		// Index to quickly find all Vehicle with a given customer foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Customer_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		// Index to quickly find all Vehicle with a given department foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Department_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		// Index to quickly find all Vehicle with a given departmentChecklist foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> DepartmentChecklist_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
 
		// Index to quickly find all Vehicle with a given driver foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Driver_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		// Index to quickly find all Vehicle with a given firmware foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Firmware_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
 
		// Index to quickly find all Vehicle with a given inspection foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Inspection_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		// Index to quickly find all Vehicle with a given model foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Model_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		// Index to quickly find all Vehicle with a given module foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Module_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		// Index to quickly find all Vehicle with a given person foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Person_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
 
		
 
		// Index to quickly find all Vehicle with a given serviceSettings foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> ServiceSettings_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		// Index to quickly find all Vehicle with a given site foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Site_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		// Index to quickly find all Vehicle with a given vehicleOtherSettings foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> VehicleOtherSettings_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public VehicleObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public VehicleObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<VehicleObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.VehicleObjects)
			{
                var cloneObject = (VehicleDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.VehicleObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.VehicleObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.VehicleObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			foreach(var fkKeyValue in this.Canrule_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Canrule_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Canrule_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.ChecklistSettings_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.ChecklistSettings_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.ChecklistSettings_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Customer_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Customer_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Customer_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Department_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Department_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Department_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.DepartmentChecklist_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.DepartmentChecklist_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.DepartmentChecklist_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Driver_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Driver_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Driver_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Firmware_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Firmware_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Firmware_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Inspection_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Inspection_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Inspection_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Model_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Model_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Model_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Module_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Module_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Module_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Person_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Person_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Person_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.ServiceSettings_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.ServiceSettings_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.ServiceSettings_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Site_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Site_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Site_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.VehicleOtherSettings_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.VehicleOtherSettings_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.VehicleOtherSettings_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<VehicleObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.VehicleObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (VehicleDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.VehicleObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.VehicleObjectInternalIds
				.Where(o => this.VehicleObjects[o.Value].IsDirty || this.VehicleObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.VehicleObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var vehicle in VehicleObjects.Values)
			{
				yield return vehicle; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the VehicleObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "VehicleObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = VehicleObjects.TryAdd(newInternalId, (VehicleDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (VehicleObjectInternalIds.ContainsKey(((VehicleDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = VehicleObjectInternalIds.TryRemove(((VehicleDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = VehicleObjectInternalIds.TryAdd(((VehicleDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as VehicleDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "VehicleDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

	 
	 
	 
	 
	 
			// Update the Canrule FK Index 
			if ((objectToAdd as VehicleDataObject).CanruleId != null)
			{
				if (!Canrule_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleDataObject).CanruleId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Canrule_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleDataObject).CanruleId, new List<int>());
					}
				}
				
				if (!Canrule_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).CanruleId].Contains(newInternalId))	
					Canrule_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).CanruleId].Add(newInternalId);

	            CanruleDataObject relatedCanrule;
	            if ((objectToAdd as VehicleDataObject)._canrule_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<CanruleDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._canrule_NewObjectId;

	                relatedCanrule = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedCanrule = _rootObjectDataSet.GetObject(new CanruleDataObject((System.Guid)(objectToAdd as VehicleDataObject).CanruleId) { IsNew = false });
	            }

	            if (relatedCanrule != null && this.RootObjectDataSet.NotifyChanges)
	                relatedCanrule.NotifyPropertyChanged("Vehicles", new SeenObjectCollection());
			}
			
	 
	 
			// Update the ChecklistSettings FK Index 
			if ((objectToAdd as VehicleDataObject).ChecklistSettingsId != null)
			{
				if (!ChecklistSettings_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleDataObject).ChecklistSettingsId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = ChecklistSettings_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleDataObject).ChecklistSettingsId, new List<int>());
					}
				}
				
				if (!ChecklistSettings_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).ChecklistSettingsId].Contains(newInternalId))	
					ChecklistSettings_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).ChecklistSettingsId].Add(newInternalId);

	            ChecklistSettingsDataObject relatedChecklistSettings;
	            if ((objectToAdd as VehicleDataObject)._checklistSettings_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<ChecklistSettingsDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._checklistSettings_NewObjectId;

	                relatedChecklistSettings = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedChecklistSettings = _rootObjectDataSet.GetObject(new ChecklistSettingsDataObject((System.Guid)(objectToAdd as VehicleDataObject).ChecklistSettingsId) { IsNew = false });
	            }

	            if (relatedChecklistSettings != null && this.RootObjectDataSet.NotifyChanges)
	                relatedChecklistSettings.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
			}
			
	 
	 
			// Update the Customer FK Index 
			if (!Customer_FKIndex.ContainsKey((objectToAdd as VehicleDataObject).CustomerId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Customer_FKIndex.TryAdd((objectToAdd as VehicleDataObject).CustomerId, new List<int>());
				}
			}
				
			if (!Customer_FKIndex[(objectToAdd as VehicleDataObject).CustomerId].Contains(newInternalId))
				Customer_FKIndex[(objectToAdd as VehicleDataObject).CustomerId].Add(newInternalId);

            CustomerDataObject relatedCustomer;
            if ((objectToAdd as VehicleDataObject)._customer_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._customer_NewObjectId;

	            relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToAdd as VehicleDataObject).CustomerId) { IsNew = false });
            }

			if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
                relatedCustomer.NotifyPropertyChanged("VehicleItems", new SeenObjectCollection());
			
	 
			// Update the Department FK Index 
			if (!Department_FKIndex.ContainsKey((objectToAdd as VehicleDataObject).DepartmentId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Department_FKIndex.TryAdd((objectToAdd as VehicleDataObject).DepartmentId, new List<int>());
				}
			}
				
			if (!Department_FKIndex[(objectToAdd as VehicleDataObject).DepartmentId].Contains(newInternalId))
				Department_FKIndex[(objectToAdd as VehicleDataObject).DepartmentId].Add(newInternalId);

            DepartmentDataObject relatedDepartment;
            if ((objectToAdd as VehicleDataObject)._department_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<DepartmentDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._department_NewObjectId;

	            relatedDepartment = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedDepartment = _rootObjectDataSet.GetObject(new DepartmentDataObject((objectToAdd as VehicleDataObject).DepartmentId) { IsNew = false });
            }

			if (relatedDepartment != null && this.RootObjectDataSet.NotifyChanges)
                relatedDepartment.NotifyPropertyChanged("Vehicles", new SeenObjectCollection());
			
	 
			// Update the DepartmentChecklist FK Index 
			if ((objectToAdd as VehicleDataObject).DepartmentChecklistId != null)
			{
				if (!DepartmentChecklist_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleDataObject).DepartmentChecklistId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = DepartmentChecklist_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleDataObject).DepartmentChecklistId, new List<int>());
					}
				}
				
				if (!DepartmentChecklist_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).DepartmentChecklistId].Contains(newInternalId))	
					DepartmentChecklist_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).DepartmentChecklistId].Add(newInternalId);

	            DepartmentChecklistDataObject relatedDepartmentChecklist;
	            if ((objectToAdd as VehicleDataObject)._departmentChecklist_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<DepartmentChecklistDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._departmentChecklist_NewObjectId;

	                relatedDepartmentChecklist = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedDepartmentChecklist = _rootObjectDataSet.GetObject(new DepartmentChecklistDataObject((System.Guid)(objectToAdd as VehicleDataObject).DepartmentChecklistId) { IsNew = false });
	            }

	            if (relatedDepartmentChecklist != null && this.RootObjectDataSet.NotifyChanges)
	                relatedDepartmentChecklist.NotifyPropertyChanged("", new SeenObjectCollection());
			}
			
	 
	 
	 
			// Update the Driver FK Index 
			if ((objectToAdd as VehicleDataObject).DriverId != null)
			{
				if (!Driver_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleDataObject).DriverId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Driver_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleDataObject).DriverId, new List<int>());
					}
				}
				
				if (!Driver_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).DriverId].Contains(newInternalId))	
					Driver_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).DriverId].Add(newInternalId);

	            DriverDataObject relatedDriver;
	            if ((objectToAdd as VehicleDataObject)._driver_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<DriverDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._driver_NewObjectId;

	                relatedDriver = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedDriver = _rootObjectDataSet.GetObject(new DriverDataObject((System.Guid)(objectToAdd as VehicleDataObject).DriverId) { IsNew = false });
	            }

	            if (relatedDriver != null && this.RootObjectDataSet.NotifyChanges)
	                relatedDriver.NotifyPropertyChanged("", new SeenObjectCollection());
			}
			
	 
			// Update the Firmware FK Index 
			if ((objectToAdd as VehicleDataObject).FirmwareId != null)
			{
				if (!Firmware_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleDataObject).FirmwareId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Firmware_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleDataObject).FirmwareId, new List<int>());
					}
				}
				
				if (!Firmware_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).FirmwareId].Contains(newInternalId))	
					Firmware_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).FirmwareId].Add(newInternalId);

	            FirmwareDataObject relatedFirmware;
	            if ((objectToAdd as VehicleDataObject)._firmware_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<FirmwareDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._firmware_NewObjectId;

	                relatedFirmware = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedFirmware = _rootObjectDataSet.GetObject(new FirmwareDataObject((System.Guid)(objectToAdd as VehicleDataObject).FirmwareId) { IsNew = false });
	            }

	            if (relatedFirmware != null && this.RootObjectDataSet.NotifyChanges)
	                relatedFirmware.NotifyPropertyChanged("", new SeenObjectCollection());
			}
			
	 
	 
	 
			// Update the Inspection FK Index 
			if ((objectToAdd as VehicleDataObject).InspectionId != null)
			{
				if (!Inspection_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleDataObject).InspectionId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Inspection_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleDataObject).InspectionId, new List<int>());
					}
				}
				
				if (!Inspection_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).InspectionId].Contains(newInternalId))	
					Inspection_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).InspectionId].Add(newInternalId);

	            InspectionDataObject relatedInspection;
	            if ((objectToAdd as VehicleDataObject)._inspection_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<InspectionDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._inspection_NewObjectId;

	                relatedInspection = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedInspection = _rootObjectDataSet.GetObject(new InspectionDataObject((System.Guid)(objectToAdd as VehicleDataObject).InspectionId) { IsNew = false });
	            }

	            if (relatedInspection != null && this.RootObjectDataSet.NotifyChanges)
	                relatedInspection.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
			}
			
	 
	 
			// Update the Model FK Index 
			if (!Model_FKIndex.ContainsKey((objectToAdd as VehicleDataObject).ModelId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Model_FKIndex.TryAdd((objectToAdd as VehicleDataObject).ModelId, new List<int>());
				}
			}
				
			if (!Model_FKIndex[(objectToAdd as VehicleDataObject).ModelId].Contains(newInternalId))
				Model_FKIndex[(objectToAdd as VehicleDataObject).ModelId].Add(newInternalId);

            ModelDataObject relatedModel;
            if ((objectToAdd as VehicleDataObject)._model_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<ModelDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._model_NewObjectId;

	            relatedModel = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedModel = _rootObjectDataSet.GetObject(new ModelDataObject((objectToAdd as VehicleDataObject).ModelId) { IsNew = false });
            }

			if (relatedModel != null && this.RootObjectDataSet.NotifyChanges)
                relatedModel.NotifyPropertyChanged("Vehicles", new SeenObjectCollection());
			
	 
			// Update the Module FK Index 
			if (!Module_FKIndex.ContainsKey((objectToAdd as VehicleDataObject).ModuleId1))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Module_FKIndex.TryAdd((objectToAdd as VehicleDataObject).ModuleId1, new List<int>());
				}
			}
				
			if (!Module_FKIndex[(objectToAdd as VehicleDataObject).ModuleId1].Contains(newInternalId))
				Module_FKIndex[(objectToAdd as VehicleDataObject).ModuleId1].Add(newInternalId);

            ModuleDataObject relatedModule;
            if ((objectToAdd as VehicleDataObject)._module_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<ModuleDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._module_NewObjectId;

	            relatedModule = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedModule = _rootObjectDataSet.GetObject(new ModuleDataObject((objectToAdd as VehicleDataObject).ModuleId1) { IsNew = false });
            }

			if (relatedModule != null && this.RootObjectDataSet.NotifyChanges)
                relatedModule.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
			
	 
	 
	 
	 
	 
	 
			// Update the Person FK Index 
			if ((objectToAdd as VehicleDataObject).PersonId != null)
			{
				if (!Person_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleDataObject).PersonId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Person_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleDataObject).PersonId, new List<int>());
					}
				}
				
				if (!Person_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).PersonId].Contains(newInternalId))	
					Person_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).PersonId].Add(newInternalId);

	            PersonDataObject relatedPerson;
	            if ((objectToAdd as VehicleDataObject)._person_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<PersonDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._person_NewObjectId;

	                relatedPerson = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedPerson = _rootObjectDataSet.GetObject(new PersonDataObject((System.Guid)(objectToAdd as VehicleDataObject).PersonId) { IsNew = false });
	            }

	            if (relatedPerson != null && this.RootObjectDataSet.NotifyChanges)
	                relatedPerson.NotifyPropertyChanged("VehicleItems", new SeenObjectCollection());
			}
			
	 
	 
	 
	 
			// Update the ServiceSettings FK Index 
			if ((objectToAdd as VehicleDataObject).ServiceSettingsId != null)
			{
				if (!ServiceSettings_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleDataObject).ServiceSettingsId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = ServiceSettings_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleDataObject).ServiceSettingsId, new List<int>());
					}
				}
				
				if (!ServiceSettings_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).ServiceSettingsId].Contains(newInternalId))	
					ServiceSettings_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).ServiceSettingsId].Add(newInternalId);

	            ServiceSettingsDataObject relatedServiceSettings;
	            if ((objectToAdd as VehicleDataObject)._serviceSettings_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<ServiceSettingsDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._serviceSettings_NewObjectId;

	                relatedServiceSettings = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedServiceSettings = _rootObjectDataSet.GetObject(new ServiceSettingsDataObject((System.Guid)(objectToAdd as VehicleDataObject).ServiceSettingsId) { IsNew = false });
	            }

	            if (relatedServiceSettings != null && this.RootObjectDataSet.NotifyChanges)
	                relatedServiceSettings.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
			}
			
	 
	 
			// Update the Site FK Index 
			if (!Site_FKIndex.ContainsKey((objectToAdd as VehicleDataObject).SiteId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Site_FKIndex.TryAdd((objectToAdd as VehicleDataObject).SiteId, new List<int>());
				}
			}
				
			if (!Site_FKIndex[(objectToAdd as VehicleDataObject).SiteId].Contains(newInternalId))
				Site_FKIndex[(objectToAdd as VehicleDataObject).SiteId].Add(newInternalId);

            SiteDataObject relatedSite;
            if ((objectToAdd as VehicleDataObject)._site_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<SiteDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._site_NewObjectId;

	            relatedSite = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedSite = _rootObjectDataSet.GetObject(new SiteDataObject((objectToAdd as VehicleDataObject).SiteId) { IsNew = false });
            }

			if (relatedSite != null && this.RootObjectDataSet.NotifyChanges)
                relatedSite.NotifyPropertyChanged("VehicleItems", new SeenObjectCollection());
			
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
			// Update the VehicleOtherSettings FK Index 
			if ((objectToAdd as VehicleDataObject).VehicleOtherSettingsId != null)
			{
				if (!VehicleOtherSettings_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleDataObject).VehicleOtherSettingsId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = VehicleOtherSettings_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleDataObject).VehicleOtherSettingsId, new List<int>());
					}
				}
				
				if (!VehicleOtherSettings_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).VehicleOtherSettingsId].Contains(newInternalId))	
					VehicleOtherSettings_FKIndex[(System.Guid)(objectToAdd as VehicleDataObject).VehicleOtherSettingsId].Add(newInternalId);

	            VehicleOtherSettingsDataObject relatedVehicleOtherSettings;
	            if ((objectToAdd as VehicleDataObject)._vehicleOtherSettings_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<VehicleOtherSettingsDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleDataObject)._vehicleOtherSettings_NewObjectId;

	                relatedVehicleOtherSettings = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedVehicleOtherSettings = _rootObjectDataSet.GetObject(new VehicleOtherSettingsDataObject((System.Guid)(objectToAdd as VehicleDataObject).VehicleOtherSettingsId) { IsNew = false });
	            }

	            if (relatedVehicleOtherSettings != null && this.RootObjectDataSet.NotifyChanges)
	                relatedVehicleOtherSettings.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
			}
			
	 
	 
	 
	 
	 
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (VehicleObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as VehicleDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "VehicleObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = VehicleObjectInternalIds.ContainsKey((objectToRemove as VehicleDataObject).PrimaryKey) ? (int?) VehicleObjectInternalIds[(objectToRemove as VehicleDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				VehicleDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = VehicleObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = VehicleObjectInternalIds.TryRemove((objectToRemove as VehicleDataObject).PrimaryKey, out idvalue);
					}
				}
				
		 
		 
		 
		 
		 
			// Delete the Canrule FK Index 
				if ((objectToRemove as VehicleDataObject).CanruleId != null)
				{
					if (Canrule_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleDataObject).CanruleId) && Canrule_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).CanruleId].Contains((int)objectToRemoveInternalId))
					{
						Canrule_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).CanruleId].Remove((int)objectToRemoveInternalId);

						if (!Canrule_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).CanruleId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Canrule_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleDataObject).CanruleId, out outvalue);
							}
						}
					}

					CanruleDataObject relatedCanrule;
		            if ((objectToRemove as VehicleDataObject)._canrule_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<CanruleDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._canrule_NewObjectId;

						relatedCanrule = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedCanrule = _rootObjectDataSet.GetObject(new CanruleDataObject((System.Guid)(objectToRemove as VehicleDataObject).CanruleId) { IsNew = false });
		            }

		            if (relatedCanrule != null && this.RootObjectDataSet.NotifyChanges)
		                relatedCanrule.NotifyPropertyChanged("Vehicles", new SeenObjectCollection());
					
				}			
		 
		 
			// Delete the ChecklistSettings FK Index 
				if ((objectToRemove as VehicleDataObject).ChecklistSettingsId != null)
				{
					if (ChecklistSettings_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleDataObject).ChecklistSettingsId) && ChecklistSettings_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).ChecklistSettingsId].Contains((int)objectToRemoveInternalId))
					{
						ChecklistSettings_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).ChecklistSettingsId].Remove((int)objectToRemoveInternalId);

						if (!ChecklistSettings_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).ChecklistSettingsId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = ChecklistSettings_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleDataObject).ChecklistSettingsId, out outvalue);
							}
						}
					}

					ChecklistSettingsDataObject relatedChecklistSettings;
		            if ((objectToRemove as VehicleDataObject)._checklistSettings_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<ChecklistSettingsDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._checklistSettings_NewObjectId;

						relatedChecklistSettings = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedChecklistSettings = _rootObjectDataSet.GetObject(new ChecklistSettingsDataObject((System.Guid)(objectToRemove as VehicleDataObject).ChecklistSettingsId) { IsNew = false });
		            }

		            if (relatedChecklistSettings != null && this.RootObjectDataSet.NotifyChanges)
		                relatedChecklistSettings.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
					
				}			
		 
		 
			// Delete the Customer FK Index 
				if (Customer_FKIndex.ContainsKey((objectToRemove as VehicleDataObject).CustomerId) && Customer_FKIndex[(objectToRemove as VehicleDataObject).CustomerId].Contains((int)objectToRemoveInternalId))
				{
					Customer_FKIndex[(objectToRemove as VehicleDataObject).CustomerId].Remove((int)objectToRemoveInternalId);

					if (!Customer_FKIndex[(objectToRemove as VehicleDataObject).CustomerId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Customer_FKIndex.TryRemove((objectToRemove as VehicleDataObject).CustomerId, out outvalue);
						}
					}
				}
				
				CustomerDataObject relatedCustomer;
	            if ((objectToRemove as VehicleDataObject)._customer_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._customer_NewObjectId;

					relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToRemove as VehicleDataObject).CustomerId) { IsNew = false });
	            }

	            if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
	                relatedCustomer.NotifyPropertyChanged("VehicleItems", new SeenObjectCollection());
				
		 
			// Delete the Department FK Index 
				if (Department_FKIndex.ContainsKey((objectToRemove as VehicleDataObject).DepartmentId) && Department_FKIndex[(objectToRemove as VehicleDataObject).DepartmentId].Contains((int)objectToRemoveInternalId))
				{
					Department_FKIndex[(objectToRemove as VehicleDataObject).DepartmentId].Remove((int)objectToRemoveInternalId);

					if (!Department_FKIndex[(objectToRemove as VehicleDataObject).DepartmentId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Department_FKIndex.TryRemove((objectToRemove as VehicleDataObject).DepartmentId, out outvalue);
						}
					}
				}
				
				DepartmentDataObject relatedDepartment;
	            if ((objectToRemove as VehicleDataObject)._department_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<DepartmentDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._department_NewObjectId;

					relatedDepartment = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedDepartment = _rootObjectDataSet.GetObject(new DepartmentDataObject((objectToRemove as VehicleDataObject).DepartmentId) { IsNew = false });
	            }

	            if (relatedDepartment != null && this.RootObjectDataSet.NotifyChanges)
	                relatedDepartment.NotifyPropertyChanged("Vehicles", new SeenObjectCollection());
				
		 
			// Delete the DepartmentChecklist FK Index 
				if ((objectToRemove as VehicleDataObject).DepartmentChecklistId != null)
				{
					if (DepartmentChecklist_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleDataObject).DepartmentChecklistId) && DepartmentChecklist_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).DepartmentChecklistId].Contains((int)objectToRemoveInternalId))
					{
						DepartmentChecklist_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).DepartmentChecklistId].Remove((int)objectToRemoveInternalId);

						if (!DepartmentChecklist_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).DepartmentChecklistId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = DepartmentChecklist_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleDataObject).DepartmentChecklistId, out outvalue);
							}
						}
					}

					DepartmentChecklistDataObject relatedDepartmentChecklist;
		            if ((objectToRemove as VehicleDataObject)._departmentChecklist_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<DepartmentChecklistDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._departmentChecklist_NewObjectId;

						relatedDepartmentChecklist = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedDepartmentChecklist = _rootObjectDataSet.GetObject(new DepartmentChecklistDataObject((System.Guid)(objectToRemove as VehicleDataObject).DepartmentChecklistId) { IsNew = false });
		            }

		            if (relatedDepartmentChecklist != null && this.RootObjectDataSet.NotifyChanges)
		                relatedDepartmentChecklist.NotifyPropertyChanged("", new SeenObjectCollection());
					
				}			
		 
		 
		 
			// Delete the Driver FK Index 
				if ((objectToRemove as VehicleDataObject).DriverId != null)
				{
					if (Driver_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleDataObject).DriverId) && Driver_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).DriverId].Contains((int)objectToRemoveInternalId))
					{
						Driver_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).DriverId].Remove((int)objectToRemoveInternalId);

						if (!Driver_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).DriverId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Driver_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleDataObject).DriverId, out outvalue);
							}
						}
					}

					DriverDataObject relatedDriver;
		            if ((objectToRemove as VehicleDataObject)._driver_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<DriverDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._driver_NewObjectId;

						relatedDriver = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedDriver = _rootObjectDataSet.GetObject(new DriverDataObject((System.Guid)(objectToRemove as VehicleDataObject).DriverId) { IsNew = false });
		            }

		            if (relatedDriver != null && this.RootObjectDataSet.NotifyChanges)
		                relatedDriver.NotifyPropertyChanged("", new SeenObjectCollection());
					
				}			
		 
			// Delete the Firmware FK Index 
				if ((objectToRemove as VehicleDataObject).FirmwareId != null)
				{
					if (Firmware_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleDataObject).FirmwareId) && Firmware_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).FirmwareId].Contains((int)objectToRemoveInternalId))
					{
						Firmware_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).FirmwareId].Remove((int)objectToRemoveInternalId);

						if (!Firmware_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).FirmwareId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Firmware_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleDataObject).FirmwareId, out outvalue);
							}
						}
					}

					FirmwareDataObject relatedFirmware;
		            if ((objectToRemove as VehicleDataObject)._firmware_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<FirmwareDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._firmware_NewObjectId;

						relatedFirmware = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedFirmware = _rootObjectDataSet.GetObject(new FirmwareDataObject((System.Guid)(objectToRemove as VehicleDataObject).FirmwareId) { IsNew = false });
		            }

		            if (relatedFirmware != null && this.RootObjectDataSet.NotifyChanges)
		                relatedFirmware.NotifyPropertyChanged("", new SeenObjectCollection());
					
				}			
		 
		 
		 
			// Delete the Inspection FK Index 
				if ((objectToRemove as VehicleDataObject).InspectionId != null)
				{
					if (Inspection_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleDataObject).InspectionId) && Inspection_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).InspectionId].Contains((int)objectToRemoveInternalId))
					{
						Inspection_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).InspectionId].Remove((int)objectToRemoveInternalId);

						if (!Inspection_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).InspectionId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Inspection_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleDataObject).InspectionId, out outvalue);
							}
						}
					}

					InspectionDataObject relatedInspection;
		            if ((objectToRemove as VehicleDataObject)._inspection_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<InspectionDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._inspection_NewObjectId;

						relatedInspection = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedInspection = _rootObjectDataSet.GetObject(new InspectionDataObject((System.Guid)(objectToRemove as VehicleDataObject).InspectionId) { IsNew = false });
		            }

		            if (relatedInspection != null && this.RootObjectDataSet.NotifyChanges)
		                relatedInspection.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
					
				}			
		 
		 
			// Delete the Model FK Index 
				if (Model_FKIndex.ContainsKey((objectToRemove as VehicleDataObject).ModelId) && Model_FKIndex[(objectToRemove as VehicleDataObject).ModelId].Contains((int)objectToRemoveInternalId))
				{
					Model_FKIndex[(objectToRemove as VehicleDataObject).ModelId].Remove((int)objectToRemoveInternalId);

					if (!Model_FKIndex[(objectToRemove as VehicleDataObject).ModelId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Model_FKIndex.TryRemove((objectToRemove as VehicleDataObject).ModelId, out outvalue);
						}
					}
				}
				
				ModelDataObject relatedModel;
	            if ((objectToRemove as VehicleDataObject)._model_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<ModelDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._model_NewObjectId;

					relatedModel = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedModel = _rootObjectDataSet.GetObject(new ModelDataObject((objectToRemove as VehicleDataObject).ModelId) { IsNew = false });
	            }

	            if (relatedModel != null && this.RootObjectDataSet.NotifyChanges)
	                relatedModel.NotifyPropertyChanged("Vehicles", new SeenObjectCollection());
				
		 
			// Delete the Module FK Index 
				if (Module_FKIndex.ContainsKey((objectToRemove as VehicleDataObject).ModuleId1) && Module_FKIndex[(objectToRemove as VehicleDataObject).ModuleId1].Contains((int)objectToRemoveInternalId))
				{
					Module_FKIndex[(objectToRemove as VehicleDataObject).ModuleId1].Remove((int)objectToRemoveInternalId);

					if (!Module_FKIndex[(objectToRemove as VehicleDataObject).ModuleId1].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Module_FKIndex.TryRemove((objectToRemove as VehicleDataObject).ModuleId1, out outvalue);
						}
					}
				}
				
				ModuleDataObject relatedModule;
	            if ((objectToRemove as VehicleDataObject)._module_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<ModuleDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._module_NewObjectId;

					relatedModule = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedModule = _rootObjectDataSet.GetObject(new ModuleDataObject((objectToRemove as VehicleDataObject).ModuleId1) { IsNew = false });
	            }

	            if (relatedModule != null && this.RootObjectDataSet.NotifyChanges)
	                relatedModule.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
				
		 
		 
		 
		 
		 
		 
			// Delete the Person FK Index 
				if ((objectToRemove as VehicleDataObject).PersonId != null)
				{
					if (Person_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleDataObject).PersonId) && Person_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).PersonId].Contains((int)objectToRemoveInternalId))
					{
						Person_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).PersonId].Remove((int)objectToRemoveInternalId);

						if (!Person_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).PersonId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Person_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleDataObject).PersonId, out outvalue);
							}
						}
					}

					PersonDataObject relatedPerson;
		            if ((objectToRemove as VehicleDataObject)._person_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<PersonDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._person_NewObjectId;

						relatedPerson = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedPerson = _rootObjectDataSet.GetObject(new PersonDataObject((System.Guid)(objectToRemove as VehicleDataObject).PersonId) { IsNew = false });
		            }

		            if (relatedPerson != null && this.RootObjectDataSet.NotifyChanges)
		                relatedPerson.NotifyPropertyChanged("VehicleItems", new SeenObjectCollection());
					
				}			
		 
		 
		 
		 
			// Delete the ServiceSettings FK Index 
				if ((objectToRemove as VehicleDataObject).ServiceSettingsId != null)
				{
					if (ServiceSettings_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleDataObject).ServiceSettingsId) && ServiceSettings_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).ServiceSettingsId].Contains((int)objectToRemoveInternalId))
					{
						ServiceSettings_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).ServiceSettingsId].Remove((int)objectToRemoveInternalId);

						if (!ServiceSettings_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).ServiceSettingsId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = ServiceSettings_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleDataObject).ServiceSettingsId, out outvalue);
							}
						}
					}

					ServiceSettingsDataObject relatedServiceSettings;
		            if ((objectToRemove as VehicleDataObject)._serviceSettings_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<ServiceSettingsDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._serviceSettings_NewObjectId;

						relatedServiceSettings = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedServiceSettings = _rootObjectDataSet.GetObject(new ServiceSettingsDataObject((System.Guid)(objectToRemove as VehicleDataObject).ServiceSettingsId) { IsNew = false });
		            }

		            if (relatedServiceSettings != null && this.RootObjectDataSet.NotifyChanges)
		                relatedServiceSettings.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
					
				}			
		 
		 
			// Delete the Site FK Index 
				if (Site_FKIndex.ContainsKey((objectToRemove as VehicleDataObject).SiteId) && Site_FKIndex[(objectToRemove as VehicleDataObject).SiteId].Contains((int)objectToRemoveInternalId))
				{
					Site_FKIndex[(objectToRemove as VehicleDataObject).SiteId].Remove((int)objectToRemoveInternalId);

					if (!Site_FKIndex[(objectToRemove as VehicleDataObject).SiteId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Site_FKIndex.TryRemove((objectToRemove as VehicleDataObject).SiteId, out outvalue);
						}
					}
				}
				
				SiteDataObject relatedSite;
	            if ((objectToRemove as VehicleDataObject)._site_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<SiteDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._site_NewObjectId;

					relatedSite = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedSite = _rootObjectDataSet.GetObject(new SiteDataObject((objectToRemove as VehicleDataObject).SiteId) { IsNew = false });
	            }

	            if (relatedSite != null && this.RootObjectDataSet.NotifyChanges)
	                relatedSite.NotifyPropertyChanged("VehicleItems", new SeenObjectCollection());
				
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			// Delete the VehicleOtherSettings FK Index 
				if ((objectToRemove as VehicleDataObject).VehicleOtherSettingsId != null)
				{
					if (VehicleOtherSettings_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleDataObject).VehicleOtherSettingsId) && VehicleOtherSettings_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).VehicleOtherSettingsId].Contains((int)objectToRemoveInternalId))
					{
						VehicleOtherSettings_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).VehicleOtherSettingsId].Remove((int)objectToRemoveInternalId);

						if (!VehicleOtherSettings_FKIndex[(System.Guid)(objectToRemove as VehicleDataObject).VehicleOtherSettingsId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = VehicleOtherSettings_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleDataObject).VehicleOtherSettingsId, out outvalue);
							}
						}
					}

					VehicleOtherSettingsDataObject relatedVehicleOtherSettings;
		            if ((objectToRemove as VehicleDataObject)._vehicleOtherSettings_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<VehicleOtherSettingsDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleDataObject)._vehicleOtherSettings_NewObjectId;

						relatedVehicleOtherSettings = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedVehicleOtherSettings = _rootObjectDataSet.GetObject(new VehicleOtherSettingsDataObject((System.Guid)(objectToRemove as VehicleDataObject).VehicleOtherSettingsId) { IsNew = false });
		            }

		            if (relatedVehicleOtherSettings != null && this.RootObjectDataSet.NotifyChanges)
		                relatedVehicleOtherSettings.NotifyPropertyChanged("Vehicle", new SeenObjectCollection());
					
				}			
		 
		 
		 
		 
		 
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return VehicleObjects.ContainsKey(internalObjectId) ? VehicleObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as VehicleDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = VehicleObjectInternalIds.ContainsKey((objectToGet as VehicleDataObject).PrimaryKey) ? (int?) VehicleObjectInternalIds[(objectToGet as VehicleDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return VehicleObjects.ContainsKey((int)objectToGetInternalId) ? VehicleObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return VehicleObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return VehicleObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		 
		 
		 
		
		public IEnumerable<VehicleDataObject> GetVehiclesForCanrule(CanruleDataObject canruleInstance) 
		{
			if (canruleInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._canrule_NewObjectId != null && o.Value._canrule_NewObjectId == canruleInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Canrule_FKIndex.ContainsKey(canruleInstance.Id))
			{
				return Canrule_FKIndex[canruleInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		
		public IEnumerable<VehicleDataObject> GetVehicleForChecklistSettings(ChecklistSettingsDataObject checklistSettingsInstance) 
		{
			if (checklistSettingsInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._checklistSettings_NewObjectId != null && o.Value._checklistSettings_NewObjectId == checklistSettingsInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (ChecklistSettings_FKIndex.ContainsKey(checklistSettingsInstance.Id))
			{
				return ChecklistSettings_FKIndex[checklistSettingsInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		
		public IEnumerable<VehicleDataObject> GetVehicleItemsForCustomer(CustomerDataObject customerInstance) 
		{
			if (customerInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._customer_NewObjectId != null && o.Value._customer_NewObjectId == customerInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Customer_FKIndex.ContainsKey(customerInstance.Id))
			{
				return Customer_FKIndex[customerInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		
		public IEnumerable<VehicleDataObject> GetVehiclesForDepartment(DepartmentDataObject departmentInstance) 
		{
			if (departmentInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._department_NewObjectId != null && o.Value._department_NewObjectId == departmentInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Department_FKIndex.ContainsKey(departmentInstance.Id))
			{
				return Department_FKIndex[departmentInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		
		public IEnumerable<VehicleDataObject> GetForDepartmentChecklist(DepartmentChecklistDataObject departmentChecklistInstance) 
		{
			if (departmentChecklistInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._departmentChecklist_NewObjectId != null && o.Value._departmentChecklist_NewObjectId == departmentChecklistInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (DepartmentChecklist_FKIndex.ContainsKey(departmentChecklistInstance.Id))
			{
				return DepartmentChecklist_FKIndex[departmentChecklistInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		 
		
		public IEnumerable<VehicleDataObject> GetForDriver(DriverDataObject driverInstance) 
		{
			if (driverInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._driver_NewObjectId != null && o.Value._driver_NewObjectId == driverInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Driver_FKIndex.ContainsKey(driverInstance.Id))
			{
				return Driver_FKIndex[driverInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		
		public IEnumerable<VehicleDataObject> GetForFirmware(FirmwareDataObject firmwareInstance) 
		{
			if (firmwareInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._firmware_NewObjectId != null && o.Value._firmware_NewObjectId == firmwareInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Firmware_FKIndex.ContainsKey(firmwareInstance.Id))
			{
				return Firmware_FKIndex[firmwareInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		 
		
		public IEnumerable<VehicleDataObject> GetVehicleForInspection(InspectionDataObject inspectionInstance) 
		{
			if (inspectionInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._inspection_NewObjectId != null && o.Value._inspection_NewObjectId == inspectionInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Inspection_FKIndex.ContainsKey(inspectionInstance.Id))
			{
				return Inspection_FKIndex[inspectionInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		
		public IEnumerable<VehicleDataObject> GetVehiclesForModel(ModelDataObject modelInstance) 
		{
			if (modelInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._model_NewObjectId != null && o.Value._model_NewObjectId == modelInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Model_FKIndex.ContainsKey(modelInstance.Id))
			{
				return Model_FKIndex[modelInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		
		public IEnumerable<VehicleDataObject> GetVehicleForModule(ModuleDataObject moduleInstance) 
		{
			if (moduleInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._module_NewObjectId != null && o.Value._module_NewObjectId == moduleInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Module_FKIndex.ContainsKey(moduleInstance.Id))
			{
				return Module_FKIndex[moduleInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		 
		 
		 
		 
		
		public IEnumerable<VehicleDataObject> GetVehicleItemsForPerson(PersonDataObject personInstance) 
		{
			if (personInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._person_NewObjectId != null && o.Value._person_NewObjectId == personInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Person_FKIndex.ContainsKey(personInstance.Id))
			{
				return Person_FKIndex[personInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		 
		 
		
		public IEnumerable<VehicleDataObject> GetVehicleForServiceSettings(ServiceSettingsDataObject serviceSettingsInstance) 
		{
			if (serviceSettingsInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._serviceSettings_NewObjectId != null && o.Value._serviceSettings_NewObjectId == serviceSettingsInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (ServiceSettings_FKIndex.ContainsKey(serviceSettingsInstance.Id))
			{
				return ServiceSettings_FKIndex[serviceSettingsInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		
		public IEnumerable<VehicleDataObject> GetVehicleItemsForSite(SiteDataObject siteInstance) 
		{
			if (siteInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._site_NewObjectId != null && o.Value._site_NewObjectId == siteInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Site_FKIndex.ContainsKey(siteInstance.Id))
			{
				return Site_FKIndex[siteInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		
		public IEnumerable<VehicleDataObject> GetVehicleForVehicleOtherSettings(VehicleOtherSettingsDataObject vehicleOtherSettingsInstance) 
		{
			if (vehicleOtherSettingsInstance.IsNew)
            {
			
              return VehicleObjects.Where(o => o.Value._vehicleOtherSettings_NewObjectId != null && o.Value._vehicleOtherSettings_NewObjectId == vehicleOtherSettingsInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (VehicleOtherSettings_FKIndex.ContainsKey(vehicleOtherSettingsInstance.Id))
			{
				return VehicleOtherSettings_FKIndex[vehicleOtherSettingsInstance.Id].Where(e => VehicleObjects.ContainsKey(e)).Select(e => VehicleObjects[e]);
			}
			
			return new DataObjectCollection<VehicleDataObject>();
		}
		 
		 
		 
		 
		 
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
			if (relationName == "AllVehicleCalibrationStoreProcedureItems")
            {
				IEnumerable< AllVehicleCalibrationStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.AllVehicleCalibrationStoreProcedureObjectsDataSet.GetAllVehicleCalibrationStoreProcedureItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "AllVORSessionsPerVehicleStoreProcedureItems")
            {
				IEnumerable< AllVORSessionsPerVehicleStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.AllVORSessionsPerVehicleStoreProcedureObjectsDataSet.GetAllVORSessionsPerVehicleStoreProcedureItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "BroadcastMessageHistoryItems")
            {
				IEnumerable< BroadcastMessageHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.BroadcastMessageHistoryObjectsDataSet.GetBroadcastMessageHistoryItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "ChecklistFailurePerVechicleViewItems")
            {
				IEnumerable< ChecklistFailurePerVechicleViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ChecklistFailurePerVechicleViewObjectsDataSet.GetChecklistFailurePerVechicleViewItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "CurrentStatusVehicleViewItems")
            {
				IEnumerable< CurrentStatusVehicleViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CurrentStatusVehicleViewObjectsDataSet.GetCurrentStatusVehicleViewItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
 
 
			if (relationName == "DetailedSessionViewItems")
            {
				IEnumerable< DetailedSessionViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DetailedSessionViewObjectsDataSet.GetDetailedSessionViewItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DetailedVORSessionStoreProcedureItems")
            {
				IEnumerable< DetailedVORSessionStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DetailedVORSessionStoreProcedureObjectsDataSet.GetDetailedVORSessionStoreProcedureItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
 
			if (relationName == "GeneralProductivityPerVehicleViewItems")
            {
				IEnumerable< GeneralProductivityPerVehicleViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GeneralProductivityPerVehicleViewObjectsDataSet.GetGeneralProductivityPerVehicleViewItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactsForVehicleViewItems")
            {
				IEnumerable< ImpactsForVehicleViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactsForVehicleViewObjectsDataSet.GetImpactsForVehicleViewItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "MessageHistoryItems")
            {
				IEnumerable< MessageHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.MessageHistoryObjectsDataSet.GetMessageHistoryItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
 
			if (relationName == "ModuleHistoryItems")
            {
				IEnumerable< ModuleHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ModuleHistoryObjectsDataSet.GetModuleHistoryItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "NetworkSettingsItems")
            {
				IEnumerable< NetworkSettingsDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.NetworkSettingsObjectsDataSet.GetNetworkSettingsItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "OnDemandSessionItems")
            {
				IEnumerable< OnDemandSessionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.OnDemandSessionObjectsDataSet.GetOnDemandSessionItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "OnDemandSettings")
            {
				IEnumerable< OnDemandSettingsDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.OnDemandSettingsObjectsDataSet.GetOnDemandSettingsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PedestrianDetectionHistoryItems")
            {
				IEnumerable< PedestrianDetectionHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PedestrianDetectionHistoryObjectsDataSet.GetPedestrianDetectionHistoryItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "PersonToPerVehicleMasterAccessViewItems")
            {
				IEnumerable< PersonToPerVehicleMasterAccessViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonToPerVehicleMasterAccessViewObjectsDataSet.GetPersonToPerVehicleMasterAccessViewItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonToPerVehicleNormalAccessViewItems")
            {
				IEnumerable< PersonToPerVehicleNormalAccessViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonToPerVehicleNormalAccessViewObjectsDataSet.GetPersonToPerVehicleNormalAccessViewItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PerVehicleNormalCardAccessItems")
            {
				IEnumerable< PerVehicleNormalCardAccessDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PerVehicleNormalCardAccessObjectsDataSet.GetPerVehicleNormalCardAccessItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "Sessions")
            {
				IEnumerable< SessionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SessionObjectsDataSet.GetSessionsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "SlamcoreDeviceHistoryItems")
            {
				IEnumerable< SlamcoreDeviceHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SlamcoreDeviceHistoryObjectsDataSet.GetSlamcoreDeviceHistoryItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "UnitUnutilisationStoreProcedureItems")
            {
				IEnumerable< UnitUtilisationStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.UnitUtilisationStoreProcedureObjectsDataSet.GetUnitUnutilisationStoreProcedureItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "UnitUtilisationStoreProcedureItems")
            {
				IEnumerable< UnitUnutilisationStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.UnitUnutilisationStoreProcedureObjectsDataSet.GetUnitUtilisationStoreProcedureItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleAlertSubscriptionItems")
            {
				IEnumerable< VehicleAlertSubscriptionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleAlertSubscriptionObjectsDataSet.GetVehicleAlertSubscriptionItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleBroadcastMessageItems")
            {
				IEnumerable< VehicleBroadcastMessageDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleBroadcastMessageObjectsDataSet.GetVehicleBroadcastMessageItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleCardAccesses")
            {
				IEnumerable< PerVehicleMasterCardAccessDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PerVehicleMasterCardAccessObjectsDataSet.GetVehicleCardAccessesForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleDiagnostic")
            {
				IEnumerable< VehicleDiagnosticDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleDiagnosticObjectsDataSet.GetVehicleDiagnosticForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleGPSLocations")
            {
				IEnumerable< VehicleGPSDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleGPSObjectsDataSet.GetVehicleGPSLocationsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleHireDehireHistoryItems")
            {
				IEnumerable< VehicleHireDehireHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleHireDehireHistoryObjectsDataSet.GetVehicleHireDehireHistoryItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleLastGPSLocationView")
            {
				IEnumerable< VehicleLastGPSLocationViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleLastGPSLocationViewObjectsDataSet.GetVehicleLastGPSLocationViewForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleLockoutItems")
            {
				IEnumerable< VehicleLockoutDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleLockoutObjectsDataSet.GetVehicleLockoutItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "VehicleProficiencyViewItems")
            {
				IEnumerable< VehicleProficiencyViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleProficiencyViewObjectsDataSet.GetVehicleProficiencyViewItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleSessionlessImpactItems")
            {
				IEnumerable< VehicleSessionlessImpactDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleSessionlessImpactObjectsDataSet.GetVehicleSessionlessImpactItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleSupervisorsViewItems")
            {
				IEnumerable< VehicleSupervisorsViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleSupervisorsViewObjectsDataSet.GetVehicleSupervisorsViewItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleToPreOpCheckilstItems")
            {
				IEnumerable< VehicleToPreOpChecklistViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleToPreOpChecklistViewObjectsDataSet.GetVehicleToPreOpCheckilstItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VORSettingHistoryItems")
            {
				IEnumerable< VORSettingHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VORSettingHistoryObjectsDataSet.GetVORSettingHistoryItemsForVehicle(rootObject as VehicleDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var VehicleDataSet = dataSetToMerge as VehicleObjectsDataSet;
				if(VehicleDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in VehicleDataSet.VehicleObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "VehicleObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as VehicleDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
		 
		 
		 
		 
		 
			// Reconstruct the Canrule FK Index 
			Canrule_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.CanruleId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.CanruleId;	

				if (!Canrule_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Canrule_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Canrule_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
			// Reconstruct the ChecklistSettings FK Index 
			ChecklistSettings_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.ChecklistSettingsId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.ChecklistSettingsId;	

				if (!ChecklistSettings_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = ChecklistSettings_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				ChecklistSettings_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
			// Reconstruct the Customer FK Index 
			Customer_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.CustomerId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.CustomerId;	

				if (!Customer_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Customer_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Customer_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the Department FK Index 
			Department_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.DepartmentId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.DepartmentId;	

				if (!Department_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Department_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Department_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the DepartmentChecklist FK Index 
			DepartmentChecklist_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.DepartmentChecklistId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.DepartmentChecklistId;	

				if (!DepartmentChecklist_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = DepartmentChecklist_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				DepartmentChecklist_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
			// Reconstruct the Driver FK Index 
			Driver_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.DriverId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.DriverId;	

				if (!Driver_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Driver_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Driver_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the Firmware FK Index 
			Firmware_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.FirmwareId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.FirmwareId;	

				if (!Firmware_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Firmware_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Firmware_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
			// Reconstruct the Inspection FK Index 
			Inspection_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.InspectionId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.InspectionId;	

				if (!Inspection_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Inspection_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Inspection_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
			// Reconstruct the Model FK Index 
			Model_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.ModelId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.ModelId;	

				if (!Model_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Model_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Model_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the Module FK Index 
			Module_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.ModuleId1 == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.ModuleId1;	

				if (!Module_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Module_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Module_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
			// Reconstruct the Person FK Index 
			Person_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.PersonId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.PersonId;	

				if (!Person_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Person_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Person_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
			// Reconstruct the ServiceSettings FK Index 
			ServiceSettings_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.ServiceSettingsId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.ServiceSettingsId;	

				if (!ServiceSettings_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = ServiceSettings_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				ServiceSettings_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
			// Reconstruct the Site FK Index 
			Site_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.SiteId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.SiteId;	

				if (!Site_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Site_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Site_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			// Reconstruct the VehicleOtherSettings FK Index 
			VehicleOtherSettings_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleObjects.Values)
			{
				if (item.VehicleOtherSettingsId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.VehicleOtherSettingsId;	

				if (!VehicleOtherSettings_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = VehicleOtherSettings_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				VehicleOtherSettings_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (VehicleObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}