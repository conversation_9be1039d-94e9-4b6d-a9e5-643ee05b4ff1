﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>1.0.0.6765</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Resources</Description>
  </PropertyGroup>
  <ItemGroup>
    <Compile Update="Strings.Designer.cs">
      <AutoGen>true</AutoGen>
      <DesignTime>true</DesignTime>
      <DependentUpon>Strings.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices" Version="2.0.3" />
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.410601"><PrivateAssets>all</PrivateAssets></PackageReference>
  </ItemGroup>
</Project>