﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'Driver'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class DriverDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("Active")]
		protected System.Boolean _active;
		[JsonProperty ("CardDetailsId")]
		protected Nullable<System.Guid> _cardDetailsId;
		[JsonProperty ("CustomerId")]
		protected Nullable<System.Guid> _customerId;
		[JsonProperty ("DepartmentId")]
		protected Nullable<System.Guid> _departmentId;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("LastSessionDate")]
		protected Nullable<System.DateTime> _lastSessionDate;
		[JsonProperty("LastSessionDate_WithTimezoneOffset")]
		protected System.DateTimeOffset? _lastSessionDate_WithTimezoneOffset;
		[JsonProperty ("LastSessionDateTzAdjusted")]
		protected Nullable<System.DateTime> _lastSessionDateTzAdjusted;
		[JsonProperty("LastSessionDateTzAdjusted_WithTimezoneOffset")]
		protected System.DateTimeOffset? _lastSessionDateTzAdjusted_WithTimezoneOffset;
		[JsonProperty ("LastSessionId")]
		protected System.String _lastSessionId;
		[JsonProperty ("LicenceDetailId")]
		protected Nullable<System.Guid> _licenceDetailId;
		[JsonProperty ("LicenseMode")]
		protected ModeEnum _licenseMode;
		[JsonProperty ("SiteId")]
		protected Nullable<System.Guid> _siteId;
		[JsonProperty ("VehicleAccess")]
		protected Nullable<System.Boolean> _vehicleAccess;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)



		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _card_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_card_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }



		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _customer_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_customer_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _department_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_department_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }




		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _generalLicence_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_generalLicence_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }









		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _site_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_site_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }



		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public DriverDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
			SetVehicleAccessValue(false, false, false);
			SetActiveValue(false, false, false);
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public DriverDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public DriverDataObject Initialize(System.Guid id)
		{
			this._id = id;
			SetVehicleAccessValue(false, false, false);
			SetActiveValue(false, false, false);
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public DriverDataObject Initialize(DriverDataObject template, bool deepCopy)
		{
			this.SetLastSessionDateValue(template.LastSessionDate, false, false);
			this._lastSessionDate_WithTimezoneOffset = template._lastSessionDate_WithTimezoneOffset;
			this.SetLastSessionDateTzAdjustedValue(template.LastSessionDateTzAdjusted, false, false);
			this._lastSessionDateTzAdjusted_WithTimezoneOffset = template._lastSessionDateTzAdjusted_WithTimezoneOffset;
			this.SetActiveValue(template.Active, false, false);
			this.SetCardDetailsIdValue(template.CardDetailsId, false, false);
			this.SetCustomerIdValue(template.CustomerId, false, false);
			this.SetDepartmentIdValue(template.DepartmentId, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetLastSessionIdValue(template.LastSessionId, false, false);
			this.SetLicenceDetailIdValue(template.LicenceDetailId, false, false);
			this.SetLicenseModeValue(template.LicenseMode, false, false);
			this.SetSiteIdValue(template.SiteId, false, false);
			this.SetVehicleAccessValue(template.VehicleAccess, false, false);
 
 
 
 
			this._card_NewObjectId = template._card_NewObjectId;
 
 
			this._customer_NewObjectId = template._customer_NewObjectId;
 
			this._department_NewObjectId = template._department_NewObjectId;
 
 
 
			this._generalLicence_NewObjectId = template._generalLicence_NewObjectId;
 
 
 
 
 
 
 
 
			this._site_NewObjectId = template._site_NewObjectId;
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual DriverDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual DriverDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var driverSource = sourceObject as DriverDataObject;

			if (ReferenceEquals(null, driverSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetActiveValue(driverSource.Active, false, false);
			this.SetCardDetailsIdValue(driverSource.CardDetailsId, false, false);
			this.SetCustomerIdValue(driverSource.CustomerId, false, false);
			this.SetDepartmentIdValue(driverSource.DepartmentId, false, false);
			this.SetIdValue(driverSource.Id, false, false);
			this.SetLastSessionDateValue(driverSource.LastSessionDate, false, false);
			this.SetLastSessionDateTzAdjustedValue(driverSource.LastSessionDateTzAdjusted, false, false);
			this.SetLastSessionIdValue(driverSource.LastSessionId, false, false);
			this.SetLicenceDetailIdValue(driverSource.LicenceDetailId, false, false);
			this.SetLicenseModeValue(driverSource.LicenseMode, false, false);
			this.SetSiteIdValue(driverSource.SiteId, false, false);
			this.SetVehicleAccessValue(driverSource.VehicleAccess, false, false);



			this._card_NewObjectId = (sourceObject as DriverDataObject)._card_NewObjectId;


			this._customer_NewObjectId = (sourceObject as DriverDataObject)._customer_NewObjectId;

			this._department_NewObjectId = (sourceObject as DriverDataObject)._department_NewObjectId;



			this._generalLicence_NewObjectId = (sourceObject as DriverDataObject)._generalLicence_NewObjectId;








			this._site_NewObjectId = (sourceObject as DriverDataObject)._site_NewObjectId;


			if (deepCopy)
			{
				this.ObjectsDataSet = driverSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(driverSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as DriverDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {



			if (this._card_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._card_NewObjectId))
				{
                    this._card_NewObjectId = null;
				}
                else
				{
					this._card_NewObjectId = datasetMergingInternalIdMapping[(int) this._card_NewObjectId];
				}
			}


			if (this._customer_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._customer_NewObjectId))
				{
                    this._customer_NewObjectId = null;
				}
                else
				{
					this._customer_NewObjectId = datasetMergingInternalIdMapping[(int) this._customer_NewObjectId];
				}
			}

			if (this._department_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._department_NewObjectId))
				{
                    this._department_NewObjectId = null;
				}
                else
				{
					this._department_NewObjectId = datasetMergingInternalIdMapping[(int) this._department_NewObjectId];
				}
			}



			if (this._generalLicence_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._generalLicence_NewObjectId))
				{
                    this._generalLicence_NewObjectId = null;
				}
                else
				{
					this._generalLicence_NewObjectId = datasetMergingInternalIdMapping[(int) this._generalLicence_NewObjectId];
				}
			}








			if (this._site_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._site_NewObjectId))
				{
                    this._site_NewObjectId = null;
				}
                else
				{
					this._site_NewObjectId = datasetMergingInternalIdMapping[(int) this._site_NewObjectId];
				}
			}


		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject> _allDriverAccessAbuseStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __allDriverAccessAbuseStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __allDriverAccessAbuseStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "AllDriverAccessAbuseStoreProcedureItems", which is a collection of AllDriverAccessAbuseStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of AllDriverAccessAbuseStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<AllDriverAccessAbuseStoreProcedureDataObject>> LoadAllDriverAccessAbuseStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadAllDriverAccessAbuseStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<AllDriverAccessAbuseStoreProcedureDataObject>> LoadAllDriverAccessAbuseStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __allDriverAccessAbuseStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__allDriverAccessAbuseStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _allDriverAccessAbuseStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __allDriverAccessAbuseStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetAllDriverAccessAbuseStoreProcedureItemsAsync(false);
            }
            finally
            {
                __allDriverAccessAbuseStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<AllDriverAccessAbuseStoreProcedureDataObject> AllDriverAccessAbuseStoreProcedureItems 
		{
			get
			{			
				return GetAllDriverAccessAbuseStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeAllDriverAccessAbuseStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("AllDriverAccessAbuseStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<AllDriverAccessAbuseStoreProcedureDataObject>> GetAllDriverAccessAbuseStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__allDriverAccessAbuseStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadAllDriverAccessAbuseStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var allDriverAccessAbuseStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<AllDriverAccessAbuseStoreProcedureDataObject>(this, "AllDriverAccessAbuseStoreProcedureItems");							
			allDriverAccessAbuseStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(AllDriverAccessAbuseStoreProcedureItems_CollectionChanged);
				
			return allDriverAccessAbuseStoreProcedureItems;
		}

        private void AllDriverAccessAbuseStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as AllDriverAccessAbuseStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : AllDriverAccessAbuseStoreProcedure", "DriverDataObject.AllDriverAccessAbuseStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add AllDriverAccessAbuseStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(System.Guid))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as AllDriverAccessAbuseStoreProcedureDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<AllLicenseExpiryViewDataObject> _allLicenseExpiryViewService => _serviceProvider.GetRequiredService<IDataProvider<AllLicenseExpiryViewDataObject>>();

		private readonly SemaphoreSlim __allLicenseExpiryViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __allLicenseExpiryViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "AllLicenseExpiryViewItems", which is a collection of AllLicenseExpiryViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of AllLicenseExpiryViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<AllLicenseExpiryViewDataObject>> LoadAllLicenseExpiryViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadAllLicenseExpiryViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<AllLicenseExpiryViewDataObject>> LoadAllLicenseExpiryViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __allLicenseExpiryViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__allLicenseExpiryViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _allLicenseExpiryViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __allLicenseExpiryViewItemsAlreadyLazyLoaded = true;
                }

                return await GetAllLicenseExpiryViewItemsAsync(false);
            }
            finally
            {
                __allLicenseExpiryViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<AllLicenseExpiryViewDataObject> AllLicenseExpiryViewItems 
		{
			get
			{			
				return GetAllLicenseExpiryViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeAllLicenseExpiryViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("AllLicenseExpiryViewItems");
		}

		public virtual async Task<DataObjectCollection<AllLicenseExpiryViewDataObject>> GetAllLicenseExpiryViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__allLicenseExpiryViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadAllLicenseExpiryViewItemsAsync(forceReload : forceReload);
			}
			var allLicenseExpiryViewItems = ObjectsDataSet.GetRelatedObjects<AllLicenseExpiryViewDataObject>(this, "AllLicenseExpiryViewItems");							
			allLicenseExpiryViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(AllLicenseExpiryViewItems_CollectionChanged);
				
			return allLicenseExpiryViewItems;
		}

        private void AllLicenseExpiryViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as AllLicenseExpiryViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : AllLicenseExpiryView", "DriverDataObject.AllLicenseExpiryViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add AllLicenseExpiryViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(Nullable<System.Guid>))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as AllLicenseExpiryViewDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<BroadcastMessageHistoryDataObject> _broadcastMessageHistoryService => _serviceProvider.GetRequiredService<IDataProvider<BroadcastMessageHistoryDataObject>>();

		private readonly SemaphoreSlim __broadcastMessageHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __broadcastMessageHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "BroadcastMessageHistoryItems", which is a collection of BroadcastMessageHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of BroadcastMessageHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<BroadcastMessageHistoryDataObject>> LoadBroadcastMessageHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadBroadcastMessageHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<BroadcastMessageHistoryDataObject>> LoadBroadcastMessageHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __broadcastMessageHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__broadcastMessageHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _broadcastMessageHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __broadcastMessageHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetBroadcastMessageHistoryItemsAsync(false);
            }
            finally
            {
                __broadcastMessageHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<BroadcastMessageHistoryDataObject> BroadcastMessageHistoryItems 
		{
			get
			{			
				return GetBroadcastMessageHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeBroadcastMessageHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("BroadcastMessageHistoryItems");
		}

		public virtual async Task<DataObjectCollection<BroadcastMessageHistoryDataObject>> GetBroadcastMessageHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__broadcastMessageHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadBroadcastMessageHistoryItemsAsync(forceReload : forceReload);
			}
			var broadcastMessageHistoryItems = ObjectsDataSet.GetRelatedObjects<BroadcastMessageHistoryDataObject>(this, "BroadcastMessageHistoryItems");							
			broadcastMessageHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(BroadcastMessageHistoryItems_CollectionChanged);
				
			return broadcastMessageHistoryItems;
		}

        private void BroadcastMessageHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as BroadcastMessageHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : BroadcastMessageHistory", "DriverDataObject.BroadcastMessageHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add BroadcastMessageHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(Nullable<System.Guid>))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as BroadcastMessageHistoryDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CardDataObject> _cardService => _serviceProvider.GetRequiredService<IDataProvider<CardDataObject>>();
      public virtual void SetCardValue(CardDataObject valueToSet)
		{
			SetCardValue(valueToSet, true, true);
		}

        public virtual void SetCardValue(CardDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			CardDataObject existing_card = null ;

			if ( !(this.CardDetailsId == null || ObjectsDataSet == null))
			{
				CardDataObject key;

				if (this._card_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<CardDataObject>().Initialize((System.Guid)this.CardDetailsId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<CardDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._card_NewObjectId;			
				}

				existing_card = (CardDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_card ,valueToSet))
            {
                if (valueToSet == null)
                {
					_card_NewObjectId = null;
					_cardDetailsId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Card", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "DriverDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_card_NewObjectId != valueToSet.InternalObjectId)
					{
						_card_NewObjectId = valueToSet.InternalObjectId;
						_cardDetailsId = valueToSet.Id;
						OnPropertyChanged("CardDetailsId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_cardDetailsId != valueToSet.Id)
					{
						_card_NewObjectId = null;

						_cardDetailsId = valueToSet.Id;
						OnPropertyChanged("CardDetailsId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_card_NewObjectId = null;
					_cardDetailsId = null;
					
				OnPropertyChanged("CardDetailsId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_card ,valueToSet))
				OnPropertyChanged("Card", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __cardSemaphore = new SemaphoreSlim(1, 1);
		private bool __cardAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Card", which is a CardDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a CardDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<CardDataObject> LoadCardAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCardAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<CardDataObject> LoadCardAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __cardSemaphore.WaitAsync();
			
	        try
            {
                if (!__cardAlreadyLazyLoaded || forceReload)
                {
								
					if (this.CardDetailsId == null)
					{
						return null;
					}
				
					CardDataObject card = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __cardAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						card = _serviceProvider.GetRequiredService<CardDataObject>().Initialize((System.Guid)this.CardDetailsId);
						card.IsNew = false;
						card = (CardDataObject)ObjectsDataSet.GetObject(card);
						if (card != null)
						{
							return card;
						}
					}

					card = await _cardService.GetAsync(_serviceProvider.GetRequiredService<CardDataObject>().Initialize((System.Guid)this.CardDetailsId), parameters : parameters, skipSecurity: skipSecurity);

					SetCardValue(card, false, false);
					__cardAlreadyLazyLoaded = true;				
		
					card = _serviceProvider.GetRequiredService<CardDataObject>().Initialize((System.Guid)this.CardDetailsId);
					card.IsNew = false;
					card = (CardDataObject)ObjectsDataSet.GetObject(card);
                    __cardAlreadyLazyLoaded = true;
                }

                return await GetCardAsync(false);
            }
            finally
            {
                __cardSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual CardDataObject Card 
		{
			get
			{			
				return GetCardAsync(true).Result;
			}
			set
			{
				SetCardValue(value);
			}
		}
		
		public virtual bool ShouldSerializeCard()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("Card");
		}

		public virtual async Task<CardDataObject> GetCardAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			CardDataObject card;

				
			if (_card_NewObjectId != null)
			{
				card = _serviceProvider.GetRequiredService<CardDataObject>();
				card.IsNew = true;
				card.InternalObjectId = _card_NewObjectId;
				card = (CardDataObject)ObjectsDataSet.GetObject(card);
			}
			else
			{
				if (this.CardDetailsId == null)
					return null;
				if (CardDetailsId == null)
					card = null;
				else
				card = _serviceProvider.GetRequiredService<CardDataObject>().Initialize((System.Guid)this.CardDetailsId);
				card.IsNew = false;
				card = (CardDataObject)ObjectsDataSet.GetObject(card);
				
				if (allowLazyLoading && card == null && LazyLoadingEnabled && (!__cardAlreadyLazyLoaded || forceReload))
				{
					card = await LoadCardAsync(forceReload : forceReload);
				}
			}
				
			return card;
		}

		public virtual Nullable<System.Guid> CardForeignKey
		{
			get { return CardDetailsId; }
			set 
			{	
				CardDetailsId = value;
			}
			
		}
		

		protected IDataProvider<CurrentStatusDriverViewDataObject> _currentStatusDriverViewService => _serviceProvider.GetRequiredService<IDataProvider<CurrentStatusDriverViewDataObject>>();

		private readonly SemaphoreSlim __currentStatusDriverViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __currentStatusDriverViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CurrentStatusDriverViewItems", which is a collection of CurrentStatusDriverViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CurrentStatusDriverViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CurrentStatusDriverViewDataObject>> LoadCurrentStatusDriverViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCurrentStatusDriverViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CurrentStatusDriverViewDataObject>> LoadCurrentStatusDriverViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __currentStatusDriverViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__currentStatusDriverViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _currentStatusDriverViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __currentStatusDriverViewItemsAlreadyLazyLoaded = true;
                }

                return await GetCurrentStatusDriverViewItemsAsync(false);
            }
            finally
            {
                __currentStatusDriverViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CurrentStatusDriverViewDataObject> CurrentStatusDriverViewItems 
		{
			get
			{			
				return GetCurrentStatusDriverViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCurrentStatusDriverViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("CurrentStatusDriverViewItems");
		}

		public virtual async Task<DataObjectCollection<CurrentStatusDriverViewDataObject>> GetCurrentStatusDriverViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__currentStatusDriverViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCurrentStatusDriverViewItemsAsync(forceReload : forceReload);
			}
			var currentStatusDriverViewItems = ObjectsDataSet.GetRelatedObjects<CurrentStatusDriverViewDataObject>(this, "CurrentStatusDriverViewItems");							
			currentStatusDriverViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CurrentStatusDriverViewItems_CollectionChanged);
				
			return currentStatusDriverViewItems;
		}

        private void CurrentStatusDriverViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CurrentStatusDriverViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CurrentStatusDriverView", "DriverDataObject.CurrentStatusDriverViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add CurrentStatusDriverViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(System.Guid))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CurrentStatusDriverViewDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CustomerDataObject> _customerService => _serviceProvider.GetRequiredService<IDataProvider<CustomerDataObject>>();
      public virtual void SetCustomerValue(CustomerDataObject valueToSet)
		{
			SetCustomerValue(valueToSet, true, true);
		}

        public virtual void SetCustomerValue(CustomerDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			CustomerDataObject existing_customer = null ;

			if ( !(this.CustomerId == null || ObjectsDataSet == null))
			{
				CustomerDataObject key;

				if (this._customer_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize((System.Guid)this.CustomerId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<CustomerDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._customer_NewObjectId;			
				}

				existing_customer = (CustomerDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_customer ,valueToSet))
            {
                if (valueToSet == null)
                {
					_customer_NewObjectId = null;
					_customerId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Customer", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "DriverDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_customer_NewObjectId != valueToSet.InternalObjectId)
					{
						_customer_NewObjectId = valueToSet.InternalObjectId;
						_customerId = valueToSet.Id;
						OnPropertyChanged("CustomerId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_customerId != valueToSet.Id)
					{
						_customer_NewObjectId = null;

						_customerId = valueToSet.Id;
						OnPropertyChanged("CustomerId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_customer_NewObjectId = null;
					_customerId = null;
					
				OnPropertyChanged("CustomerId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_customer ,valueToSet))
				OnPropertyChanged("Customer", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __customerSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Customer", which is a CustomerDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a CustomerDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<CustomerDataObject> LoadCustomerAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<CustomerDataObject> LoadCustomerAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerAlreadyLazyLoaded || forceReload)
                {
								
					if (this.CustomerId == null)
					{
						return null;
					}
				
					CustomerDataObject customer = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __customerAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						customer = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize((System.Guid)this.CustomerId);
						customer.IsNew = false;
						customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
						if (customer != null)
						{
							return customer;
						}
					}

					customer = await _customerService.GetAsync(_serviceProvider.GetRequiredService<CustomerDataObject>().Initialize((System.Guid)this.CustomerId), parameters : parameters, skipSecurity: skipSecurity);

					SetCustomerValue(customer, false, false);
					__customerAlreadyLazyLoaded = true;				
		
					customer = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize((System.Guid)this.CustomerId);
					customer.IsNew = false;
					customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
                    __customerAlreadyLazyLoaded = true;
                }

                return await GetCustomerAsync(false);
            }
            finally
            {
                __customerSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual CustomerDataObject Customer 
		{
			get
			{			
				return GetCustomerAsync(true).Result;
			}
			set
			{
				SetCustomerValue(value);
			}
		}
		
		public virtual bool ShouldSerializeCustomer()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("Customer");
		}

		public virtual async Task<CustomerDataObject> GetCustomerAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			CustomerDataObject customer;

				
			if (_customer_NewObjectId != null)
			{
				customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
				customer.IsNew = true;
				customer.InternalObjectId = _customer_NewObjectId;
				customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
			}
			else
			{
				if (this.CustomerId == null)
					return null;
				if (CustomerId == null)
					customer = null;
				else
				customer = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize((System.Guid)this.CustomerId);
				customer.IsNew = false;
				customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
				
				if (allowLazyLoading && customer == null && LazyLoadingEnabled && (!__customerAlreadyLazyLoaded || forceReload))
				{
					customer = await LoadCustomerAsync(forceReload : forceReload);
				}
			}
				
			return customer;
		}

		public virtual Nullable<System.Guid> CustomerForeignKey
		{
			get { return CustomerId; }
			set 
			{	
				CustomerId = value;
			}
			
		}
		

		protected IDataProvider<DepartmentDataObject> _departmentService => _serviceProvider.GetRequiredService<IDataProvider<DepartmentDataObject>>();
      public virtual void SetDepartmentValue(DepartmentDataObject valueToSet)
		{
			SetDepartmentValue(valueToSet, true, true);
		}

        public virtual void SetDepartmentValue(DepartmentDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DepartmentDataObject existing_department = null ;

			if ( !(this.DepartmentId == null || ObjectsDataSet == null))
			{
				DepartmentDataObject key;

				if (this._department_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize((System.Guid)this.DepartmentId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DepartmentDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._department_NewObjectId;			
				}

				existing_department = (DepartmentDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_department ,valueToSet))
            {
                if (valueToSet == null)
                {
					_department_NewObjectId = null;
					_departmentId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Department", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "DriverDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_department_NewObjectId != valueToSet.InternalObjectId)
					{
						_department_NewObjectId = valueToSet.InternalObjectId;
						_departmentId = valueToSet.Id;
						OnPropertyChanged("DepartmentId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_departmentId != valueToSet.Id)
					{
						_department_NewObjectId = null;

						_departmentId = valueToSet.Id;
						OnPropertyChanged("DepartmentId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_department_NewObjectId = null;
					_departmentId = null;
					
				OnPropertyChanged("DepartmentId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_department ,valueToSet))
				OnPropertyChanged("Department", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __departmentSemaphore = new SemaphoreSlim(1, 1);
		private bool __departmentAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Department", which is a DepartmentDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DepartmentDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DepartmentDataObject> LoadDepartmentAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDepartmentAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DepartmentDataObject> LoadDepartmentAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __departmentSemaphore.WaitAsync();
			
	        try
            {
                if (!__departmentAlreadyLazyLoaded || forceReload)
                {
								
					if (this.DepartmentId == null)
					{
						return null;
					}
				
					DepartmentDataObject department = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __departmentAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						department = _serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize((System.Guid)this.DepartmentId);
						department.IsNew = false;
						department = (DepartmentDataObject)ObjectsDataSet.GetObject(department);
						if (department != null)
						{
							return department;
						}
					}

					department = await _departmentService.GetAsync(_serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize((System.Guid)this.DepartmentId), parameters : parameters, skipSecurity: skipSecurity);

					SetDepartmentValue(department, false, false);
					__departmentAlreadyLazyLoaded = true;				
		
					department = _serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize((System.Guid)this.DepartmentId);
					department.IsNew = false;
					department = (DepartmentDataObject)ObjectsDataSet.GetObject(department);
                    __departmentAlreadyLazyLoaded = true;
                }

                return await GetDepartmentAsync(false);
            }
            finally
            {
                __departmentSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DepartmentDataObject Department 
		{
			get
			{			
				return GetDepartmentAsync(true).Result;
			}
			set
			{
				SetDepartmentValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDepartment()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("Department");
		}

		public virtual async Task<DepartmentDataObject> GetDepartmentAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DepartmentDataObject department;

				
			if (_department_NewObjectId != null)
			{
				department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
				department.IsNew = true;
				department.InternalObjectId = _department_NewObjectId;
				department = (DepartmentDataObject)ObjectsDataSet.GetObject(department);
			}
			else
			{
				if (this.DepartmentId == null)
					return null;
				if (DepartmentId == null)
					department = null;
				else
				department = _serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize((System.Guid)this.DepartmentId);
				department.IsNew = false;
				department = (DepartmentDataObject)ObjectsDataSet.GetObject(department);
				
				if (allowLazyLoading && department == null && LazyLoadingEnabled && (!__departmentAlreadyLazyLoaded || forceReload))
				{
					department = await LoadDepartmentAsync(forceReload : forceReload);
				}
			}
				
			return department;
		}

		public virtual Nullable<System.Guid> DepartmentForeignKey
		{
			get { return DepartmentId; }
			set 
			{	
				DepartmentId = value;
			}
			
		}
		

		protected IDataProvider<DetailedSessionViewDataObject> _detailedSessionViewService => _serviceProvider.GetRequiredService<IDataProvider<DetailedSessionViewDataObject>>();

		private readonly SemaphoreSlim __detailedSessionViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __detailedSessionViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DetailedSessionViewItems", which is a collection of DetailedSessionViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DetailedSessionViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DetailedSessionViewDataObject>> LoadDetailedSessionViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDetailedSessionViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DetailedSessionViewDataObject>> LoadDetailedSessionViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __detailedSessionViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__detailedSessionViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _detailedSessionViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __detailedSessionViewItemsAlreadyLazyLoaded = true;
                }

                return await GetDetailedSessionViewItemsAsync(false);
            }
            finally
            {
                __detailedSessionViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DetailedSessionViewDataObject> DetailedSessionViewItems 
		{
			get
			{			
				return GetDetailedSessionViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDetailedSessionViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("DetailedSessionViewItems");
		}

		public virtual async Task<DataObjectCollection<DetailedSessionViewDataObject>> GetDetailedSessionViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__detailedSessionViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDetailedSessionViewItemsAsync(forceReload : forceReload);
			}
			var detailedSessionViewItems = ObjectsDataSet.GetRelatedObjects<DetailedSessionViewDataObject>(this, "DetailedSessionViewItems");							
			detailedSessionViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DetailedSessionViewItems_CollectionChanged);
				
			return detailedSessionViewItems;
		}

        private void DetailedSessionViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DetailedSessionViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DetailedSessionView", "DriverDataObject.DetailedSessionViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add DetailedSessionViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(System.Guid))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DetailedSessionViewDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DriverProficiencyViewDataObject> _driverProficiencyViewService => _serviceProvider.GetRequiredService<IDataProvider<DriverProficiencyViewDataObject>>();

		private readonly SemaphoreSlim __driverProficiencyViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __driverProficiencyViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DriverProficiencyViewItems", which is a collection of DriverProficiencyViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DriverProficiencyViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DriverProficiencyViewDataObject>> LoadDriverProficiencyViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDriverProficiencyViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DriverProficiencyViewDataObject>> LoadDriverProficiencyViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __driverProficiencyViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__driverProficiencyViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _driverProficiencyViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __driverProficiencyViewItemsAlreadyLazyLoaded = true;
                }

                return await GetDriverProficiencyViewItemsAsync(false);
            }
            finally
            {
                __driverProficiencyViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DriverProficiencyViewDataObject> DriverProficiencyViewItems 
		{
			get
			{			
				return GetDriverProficiencyViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDriverProficiencyViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("DriverProficiencyViewItems");
		}

		public virtual async Task<DataObjectCollection<DriverProficiencyViewDataObject>> GetDriverProficiencyViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__driverProficiencyViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDriverProficiencyViewItemsAsync(forceReload : forceReload);
			}
			var driverProficiencyViewItems = ObjectsDataSet.GetRelatedObjects<DriverProficiencyViewDataObject>(this, "DriverProficiencyViewItems");							
			driverProficiencyViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DriverProficiencyViewItems_CollectionChanged);
				
			return driverProficiencyViewItems;
		}

        private void DriverProficiencyViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DriverProficiencyViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DriverProficiencyView", "DriverDataObject.DriverProficiencyViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add DriverProficiencyViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(System.Guid))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DriverProficiencyViewDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<LicenceDetailDataObject> _licenceDetailService => _serviceProvider.GetRequiredService<IDataProvider<LicenceDetailDataObject>>();
      public virtual void SetGeneralLicenceValue(LicenceDetailDataObject valueToSet)
		{
			SetGeneralLicenceValue(valueToSet, true, true);
		}

        public virtual void SetGeneralLicenceValue(LicenceDetailDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			LicenceDetailDataObject existing_generalLicence = null ;

			if ( !(this.LicenceDetailId == null || ObjectsDataSet == null))
			{
				LicenceDetailDataObject key;

				if (this._generalLicence_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<LicenceDetailDataObject>().Initialize((System.Guid)this.LicenceDetailId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<LicenceDetailDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._generalLicence_NewObjectId;			
				}

				existing_generalLicence = (LicenceDetailDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_generalLicence ,valueToSet))
            {
                if (valueToSet == null)
                {
					_generalLicence_NewObjectId = null;
					_licenceDetailId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("GeneralLicence", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "DriverDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_generalLicence_NewObjectId != valueToSet.InternalObjectId)
					{
						_generalLicence_NewObjectId = valueToSet.InternalObjectId;
						_licenceDetailId = valueToSet.Id;
						OnPropertyChanged("LicenceDetailId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_licenceDetailId != valueToSet.Id)
					{
						_generalLicence_NewObjectId = null;

						_licenceDetailId = valueToSet.Id;
						OnPropertyChanged("LicenceDetailId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_generalLicence_NewObjectId = null;
					_licenceDetailId = null;
					
				OnPropertyChanged("LicenceDetailId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_generalLicence ,valueToSet))
				OnPropertyChanged("GeneralLicence", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __generalLicenceSemaphore = new SemaphoreSlim(1, 1);
		private bool __generalLicenceAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "GeneralLicence", which is a LicenceDetailDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a LicenceDetailDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<LicenceDetailDataObject> LoadGeneralLicenceAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadGeneralLicenceAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<LicenceDetailDataObject> LoadGeneralLicenceAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __generalLicenceSemaphore.WaitAsync();
			
	        try
            {
                if (!__generalLicenceAlreadyLazyLoaded || forceReload)
                {
								
					if (this.LicenceDetailId == null)
					{
						return null;
					}
				
					LicenceDetailDataObject generalLicence = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __generalLicenceAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						generalLicence = _serviceProvider.GetRequiredService<LicenceDetailDataObject>().Initialize((System.Guid)this.LicenceDetailId);
						generalLicence.IsNew = false;
						generalLicence = (LicenceDetailDataObject)ObjectsDataSet.GetObject(generalLicence);
						if (generalLicence != null)
						{
							return generalLicence;
						}
					}

					generalLicence = await _licenceDetailService.GetAsync(_serviceProvider.GetRequiredService<LicenceDetailDataObject>().Initialize((System.Guid)this.LicenceDetailId), parameters : parameters, skipSecurity: skipSecurity);

					SetGeneralLicenceValue(generalLicence, false, false);
					__generalLicenceAlreadyLazyLoaded = true;				
		
					generalLicence = _serviceProvider.GetRequiredService<LicenceDetailDataObject>().Initialize((System.Guid)this.LicenceDetailId);
					generalLicence.IsNew = false;
					generalLicence = (LicenceDetailDataObject)ObjectsDataSet.GetObject(generalLicence);
                    __generalLicenceAlreadyLazyLoaded = true;
                }

                return await GetGeneralLicenceAsync(false);
            }
            finally
            {
                __generalLicenceSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual LicenceDetailDataObject GeneralLicence 
		{
			get
			{			
				return GetGeneralLicenceAsync(true).Result;
			}
			set
			{
				SetGeneralLicenceValue(value);
			}
		}
		
		public virtual bool ShouldSerializeGeneralLicence()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("GeneralLicence");
		}

		public virtual async Task<LicenceDetailDataObject> GetGeneralLicenceAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			LicenceDetailDataObject generalLicence;

				
			if (_generalLicence_NewObjectId != null)
			{
				generalLicence = _serviceProvider.GetRequiredService<LicenceDetailDataObject>();
				generalLicence.IsNew = true;
				generalLicence.InternalObjectId = _generalLicence_NewObjectId;
				generalLicence = (LicenceDetailDataObject)ObjectsDataSet.GetObject(generalLicence);
			}
			else
			{
				if (this.LicenceDetailId == null)
					return null;
				if (LicenceDetailId == null)
					generalLicence = null;
				else
				generalLicence = _serviceProvider.GetRequiredService<LicenceDetailDataObject>().Initialize((System.Guid)this.LicenceDetailId);
				generalLicence.IsNew = false;
				generalLicence = (LicenceDetailDataObject)ObjectsDataSet.GetObject(generalLicence);
				
				if (allowLazyLoading && generalLicence == null && LazyLoadingEnabled && (!__generalLicenceAlreadyLazyLoaded || forceReload))
				{
					generalLicence = await LoadGeneralLicenceAsync(forceReload : forceReload);
				}
			}
				
			return generalLicence;
		}

		public virtual Nullable<System.Guid> GeneralLicenceForeignKey
		{
			get { return LicenceDetailId; }
			set 
			{	
				LicenceDetailId = value;
			}
			
		}
		

		protected IDataProvider<GeneralProductivityPerDriverViewLatestDataObject> _generalProductivityPerDriverViewLatestService => _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityPerDriverViewLatestDataObject>>();

		private readonly SemaphoreSlim __generalProductivityPerDriverViewLatestItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __generalProductivityPerDriverViewLatestItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "GeneralProductivityPerDriverViewLatestItems", which is a collection of GeneralProductivityPerDriverViewLatestDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of GeneralProductivityPerDriverViewLatestDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<GeneralProductivityPerDriverViewLatestDataObject>> LoadGeneralProductivityPerDriverViewLatestItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadGeneralProductivityPerDriverViewLatestItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<GeneralProductivityPerDriverViewLatestDataObject>> LoadGeneralProductivityPerDriverViewLatestItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __generalProductivityPerDriverViewLatestItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__generalProductivityPerDriverViewLatestItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _generalProductivityPerDriverViewLatestService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __generalProductivityPerDriverViewLatestItemsAlreadyLazyLoaded = true;
                }

                return await GetGeneralProductivityPerDriverViewLatestItemsAsync(false);
            }
            finally
            {
                __generalProductivityPerDriverViewLatestItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<GeneralProductivityPerDriverViewLatestDataObject> GeneralProductivityPerDriverViewLatestItems 
		{
			get
			{			
				return GetGeneralProductivityPerDriverViewLatestItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeGeneralProductivityPerDriverViewLatestItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("GeneralProductivityPerDriverViewLatestItems");
		}

		public virtual async Task<DataObjectCollection<GeneralProductivityPerDriverViewLatestDataObject>> GetGeneralProductivityPerDriverViewLatestItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__generalProductivityPerDriverViewLatestItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadGeneralProductivityPerDriverViewLatestItemsAsync(forceReload : forceReload);
			}
			var generalProductivityPerDriverViewLatestItems = ObjectsDataSet.GetRelatedObjects<GeneralProductivityPerDriverViewLatestDataObject>(this, "GeneralProductivityPerDriverViewLatestItems");							
			generalProductivityPerDriverViewLatestItems.CollectionChanged += new NotifyCollectionChangedEventHandler(GeneralProductivityPerDriverViewLatestItems_CollectionChanged);
				
			return generalProductivityPerDriverViewLatestItems;
		}

        private void GeneralProductivityPerDriverViewLatestItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as GeneralProductivityPerDriverViewLatestDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : GeneralProductivityPerDriverViewLatest", "DriverDataObject.GeneralProductivityPerDriverViewLatestItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add GeneralProductivityPerDriverViewLatestDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(System.Guid))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as GeneralProductivityPerDriverViewLatestDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ImpactsForVehicleViewDataObject> _impactsForVehicleViewService => _serviceProvider.GetRequiredService<IDataProvider<ImpactsForVehicleViewDataObject>>();

		private readonly SemaphoreSlim __impactsForVehicleViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __impactsForVehicleViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ImpactsForVehicleViewItems", which is a collection of ImpactsForVehicleViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ImpactsForVehicleViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ImpactsForVehicleViewDataObject>> LoadImpactsForVehicleViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadImpactsForVehicleViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ImpactsForVehicleViewDataObject>> LoadImpactsForVehicleViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __impactsForVehicleViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__impactsForVehicleViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _impactsForVehicleViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __impactsForVehicleViewItemsAlreadyLazyLoaded = true;
                }

                return await GetImpactsForVehicleViewItemsAsync(false);
            }
            finally
            {
                __impactsForVehicleViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ImpactsForVehicleViewDataObject> ImpactsForVehicleViewItems 
		{
			get
			{			
				return GetImpactsForVehicleViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeImpactsForVehicleViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("ImpactsForVehicleViewItems");
		}

		public virtual async Task<DataObjectCollection<ImpactsForVehicleViewDataObject>> GetImpactsForVehicleViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__impactsForVehicleViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadImpactsForVehicleViewItemsAsync(forceReload : forceReload);
			}
			var impactsForVehicleViewItems = ObjectsDataSet.GetRelatedObjects<ImpactsForVehicleViewDataObject>(this, "ImpactsForVehicleViewItems");							
			impactsForVehicleViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ImpactsForVehicleViewItems_CollectionChanged);
				
			return impactsForVehicleViewItems;
		}

        private void ImpactsForVehicleViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ImpactsForVehicleViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ImpactsForVehicleView", "DriverDataObject.ImpactsForVehicleViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add ImpactsForVehicleViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(System.Guid))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ImpactsForVehicleViewDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<LicenseByModelDataObject> _licenseByModelService => _serviceProvider.GetRequiredService<IDataProvider<LicenseByModelDataObject>>();

		private readonly SemaphoreSlim __licensesByModelSemaphore = new SemaphoreSlim(1, 1);
		private bool __licensesByModelAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "LicensesByModel", which is a collection of LicenseByModelDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of LicenseByModelDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<LicenseByModelDataObject>> LoadLicensesByModelAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadLicensesByModelAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<LicenseByModelDataObject>> LoadLicensesByModelAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __licensesByModelSemaphore.WaitAsync();
			
	        try
            {
                if (!__licensesByModelAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _licenseByModelService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __licensesByModelAlreadyLazyLoaded = true;
                }

                return await GetLicensesByModelAsync(false);
            }
            finally
            {
                __licensesByModelSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<LicenseByModelDataObject> LicensesByModel 
		{
			get
			{			
				return GetLicensesByModelAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeLicensesByModel()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("LicensesByModel");
		}

		public virtual async Task<DataObjectCollection<LicenseByModelDataObject>> GetLicensesByModelAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__licensesByModelAlreadyLazyLoaded || forceReload) )
			{
				await LoadLicensesByModelAsync(forceReload : forceReload);
			}
			var licensesByModel = ObjectsDataSet.GetRelatedObjects<LicenseByModelDataObject>(this, "LicensesByModel");							
			licensesByModel.CollectionChanged += new NotifyCollectionChangedEventHandler(LicensesByModel_CollectionChanged);
				
			return licensesByModel;
		}

        private void LicensesByModel_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as LicenseByModelDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : LicenseByModel", "DriverDataObject.LicensesByModel_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add LicenseByModelDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(System.Guid))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as LicenseByModelDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<OnDemandSessionDataObject> _onDemandSessionService => _serviceProvider.GetRequiredService<IDataProvider<OnDemandSessionDataObject>>();

		private readonly SemaphoreSlim __onDemandSessionItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __onDemandSessionItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "OnDemandSessionItems", which is a collection of OnDemandSessionDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of OnDemandSessionDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<OnDemandSessionDataObject>> LoadOnDemandSessionItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadOnDemandSessionItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<OnDemandSessionDataObject>> LoadOnDemandSessionItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __onDemandSessionItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__onDemandSessionItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _onDemandSessionService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __onDemandSessionItemsAlreadyLazyLoaded = true;
                }

                return await GetOnDemandSessionItemsAsync(false);
            }
            finally
            {
                __onDemandSessionItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<OnDemandSessionDataObject> OnDemandSessionItems 
		{
			get
			{			
				return GetOnDemandSessionItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeOnDemandSessionItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("OnDemandSessionItems");
		}

		public virtual async Task<DataObjectCollection<OnDemandSessionDataObject>> GetOnDemandSessionItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__onDemandSessionItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadOnDemandSessionItemsAsync(forceReload : forceReload);
			}
			var onDemandSessionItems = ObjectsDataSet.GetRelatedObjects<OnDemandSessionDataObject>(this, "OnDemandSessionItems");							
			onDemandSessionItems.CollectionChanged += new NotifyCollectionChangedEventHandler(OnDemandSessionItems_CollectionChanged);
				
			return onDemandSessionItems;
		}

        private void OnDemandSessionItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as OnDemandSessionDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : OnDemandSession", "DriverDataObject.OnDemandSessionItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add OnDemandSessionDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(System.Guid))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as OnDemandSessionDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<PedestrianDetectionHistoryDataObject> _pedestrianDetectionHistoryService => _serviceProvider.GetRequiredService<IDataProvider<PedestrianDetectionHistoryDataObject>>();

		private readonly SemaphoreSlim __pedestrianDetectionHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __pedestrianDetectionHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "PedestrianDetectionHistoryItems", which is a collection of PedestrianDetectionHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of PedestrianDetectionHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<PedestrianDetectionHistoryDataObject>> LoadPedestrianDetectionHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPedestrianDetectionHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<PedestrianDetectionHistoryDataObject>> LoadPedestrianDetectionHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __pedestrianDetectionHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__pedestrianDetectionHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _pedestrianDetectionHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __pedestrianDetectionHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetPedestrianDetectionHistoryItemsAsync(false);
            }
            finally
            {
                __pedestrianDetectionHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<PedestrianDetectionHistoryDataObject> PedestrianDetectionHistoryItems 
		{
			get
			{			
				return GetPedestrianDetectionHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializePedestrianDetectionHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("PedestrianDetectionHistoryItems");
		}

		public virtual async Task<DataObjectCollection<PedestrianDetectionHistoryDataObject>> GetPedestrianDetectionHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__pedestrianDetectionHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadPedestrianDetectionHistoryItemsAsync(forceReload : forceReload);
			}
			var pedestrianDetectionHistoryItems = ObjectsDataSet.GetRelatedObjects<PedestrianDetectionHistoryDataObject>(this, "PedestrianDetectionHistoryItems");							
			pedestrianDetectionHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(PedestrianDetectionHistoryItems_CollectionChanged);
				
			return pedestrianDetectionHistoryItems;
		}

        private void PedestrianDetectionHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as PedestrianDetectionHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : PedestrianDetectionHistory", "DriverDataObject.PedestrianDetectionHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add PedestrianDetectionHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(Nullable<System.Guid>))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as PedestrianDetectionHistoryDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<PersonDataObject> _personService => _serviceProvider.GetRequiredService<IDataProvider<PersonDataObject>>();
      public virtual void SetPersonValue(PersonDataObject valueToSet)
		{
			SetPersonValue(valueToSet, true, true);
		}

        public virtual void SetPersonValue(PersonDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<PersonDataObject>(this, "Person");
			var existing_person = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Person", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._driver_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.Driver = this;
					valueToSet.DriverId = this.Id;
				}			
			}
			else  if (existing_person != null)
            {
                ObjectsDataSet.RemoveObject(existing_person);
            }
			if (!ReferenceEquals(existing_person ,valueToSet))
				OnPropertyChanged("Person", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __personSemaphore = new SemaphoreSlim(1, 1);
		private bool __personAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Person", which is a PersonDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a PersonDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<PersonDataObject> LoadPersonAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPersonAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<PersonDataObject> LoadPersonAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __personSemaphore.WaitAsync();
			
	        try
            {
                if (!__personAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data Person for the current entity. The DataObjects doesn't have an ObjectsDataSet", "DriverObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var person = (this.ObjectsDataSet as ObjectsDataSet).PersonObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).PersonObjects.Where(item => item.Value.DriverId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(person, null))
					{
						var filterPredicate = "DriverId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						person = (await _personService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetPersonValue(person, false, false);
						__personAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a person, but relation field not set, encourage it to get set by removing and re-adding the person 
					if (person != null && this.Person == null)
					{
						this.ObjectsDataSet.RemoveObject(person);
						this.ObjectsDataSet.AddObject(person);
					}			
                    __personAlreadyLazyLoaded = true;
                }

                return await GetPersonAsync(false);
            }
            finally
            {
                __personSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual PersonDataObject Person 
		{
			get
			{			
				return GetPersonAsync(true).Result;
			}
			set
			{
				SetPersonValue(value);
			}
		}
		
		public virtual bool ShouldSerializePerson()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("Person");
		}

		public virtual async Task<PersonDataObject> GetPersonAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			PersonDataObject person;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<PersonDataObject>(this, "Person");
               	person = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && person == null && LazyLoadingEnabled && (!__personAlreadyLazyLoaded || forceReload))
				{
					person = await LoadPersonAsync(forceReload : forceReload);
				}
			}
				
			return person;
		}


		protected IDataProvider<SessionDataObject> _sessionService => _serviceProvider.GetRequiredService<IDataProvider<SessionDataObject>>();

		private readonly SemaphoreSlim __sessionsSemaphore = new SemaphoreSlim(1, 1);
		private bool __sessionsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Sessions", which is a collection of SessionDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SessionDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SessionDataObject>> LoadSessionsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSessionsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SessionDataObject>> LoadSessionsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __sessionsSemaphore.WaitAsync();
			
	        try
            {
                if (!__sessionsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _sessionService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __sessionsAlreadyLazyLoaded = true;
                }

                return await GetSessionsAsync(false);
            }
            finally
            {
                __sessionsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SessionDataObject> Sessions 
		{
			get
			{			
				return GetSessionsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSessions()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("Sessions");
		}

		public virtual async Task<DataObjectCollection<SessionDataObject>> GetSessionsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__sessionsAlreadyLazyLoaded || forceReload) )
			{
				await LoadSessionsAsync(forceReload : forceReload);
			}
			var sessions = ObjectsDataSet.GetRelatedObjects<SessionDataObject>(this, "Sessions");							
			sessions.CollectionChanged += new NotifyCollectionChangedEventHandler(Sessions_CollectionChanged);
				
			return sessions;
		}

        private void Sessions_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SessionDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : Session", "DriverDataObject.Sessions_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add SessionDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(System.Guid))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SessionDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<SiteDataObject> _siteService => _serviceProvider.GetRequiredService<IDataProvider<SiteDataObject>>();
      public virtual void SetSiteValue(SiteDataObject valueToSet)
		{
			SetSiteValue(valueToSet, true, true);
		}

        public virtual void SetSiteValue(SiteDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			SiteDataObject existing_site = null ;

			if ( !(this.SiteId == null || ObjectsDataSet == null))
			{
				SiteDataObject key;

				if (this._site_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<SiteDataObject>().Initialize((System.Guid)this.SiteId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<SiteDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._site_NewObjectId;			
				}

				existing_site = (SiteDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_site ,valueToSet))
            {
                if (valueToSet == null)
                {
					_site_NewObjectId = null;
					_siteId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Site", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "DriverDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_site_NewObjectId != valueToSet.InternalObjectId)
					{
						_site_NewObjectId = valueToSet.InternalObjectId;
						_siteId = valueToSet.Id;
						OnPropertyChanged("SiteId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_siteId != valueToSet.Id)
					{
						_site_NewObjectId = null;

						_siteId = valueToSet.Id;
						OnPropertyChanged("SiteId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_site_NewObjectId = null;
					_siteId = null;
					
				OnPropertyChanged("SiteId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_site ,valueToSet))
				OnPropertyChanged("Site", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __siteSemaphore = new SemaphoreSlim(1, 1);
		private bool __siteAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Site", which is a SiteDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SiteDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SiteDataObject> LoadSiteAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSiteAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SiteDataObject> LoadSiteAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __siteSemaphore.WaitAsync();
			
	        try
            {
                if (!__siteAlreadyLazyLoaded || forceReload)
                {
								
					if (this.SiteId == null)
					{
						return null;
					}
				
					SiteDataObject site = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __siteAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						site = _serviceProvider.GetRequiredService<SiteDataObject>().Initialize((System.Guid)this.SiteId);
						site.IsNew = false;
						site = (SiteDataObject)ObjectsDataSet.GetObject(site);
						if (site != null)
						{
							return site;
						}
					}

					site = await _siteService.GetAsync(_serviceProvider.GetRequiredService<SiteDataObject>().Initialize((System.Guid)this.SiteId), parameters : parameters, skipSecurity: skipSecurity);

					SetSiteValue(site, false, false);
					__siteAlreadyLazyLoaded = true;				
		
					site = _serviceProvider.GetRequiredService<SiteDataObject>().Initialize((System.Guid)this.SiteId);
					site.IsNew = false;
					site = (SiteDataObject)ObjectsDataSet.GetObject(site);
                    __siteAlreadyLazyLoaded = true;
                }

                return await GetSiteAsync(false);
            }
            finally
            {
                __siteSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SiteDataObject Site 
		{
			get
			{			
				return GetSiteAsync(true).Result;
			}
			set
			{
				SetSiteValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSite()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("Site");
		}

		public virtual async Task<SiteDataObject> GetSiteAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SiteDataObject site;

				
			if (_site_NewObjectId != null)
			{
				site = _serviceProvider.GetRequiredService<SiteDataObject>();
				site.IsNew = true;
				site.InternalObjectId = _site_NewObjectId;
				site = (SiteDataObject)ObjectsDataSet.GetObject(site);
			}
			else
			{
				if (this.SiteId == null)
					return null;
				if (SiteId == null)
					site = null;
				else
				site = _serviceProvider.GetRequiredService<SiteDataObject>().Initialize((System.Guid)this.SiteId);
				site.IsNew = false;
				site = (SiteDataObject)ObjectsDataSet.GetObject(site);
				
				if (allowLazyLoading && site == null && LazyLoadingEnabled && (!__siteAlreadyLazyLoaded || forceReload))
				{
					site = await LoadSiteAsync(forceReload : forceReload);
				}
			}
				
			return site;
		}

		public virtual Nullable<System.Guid> SiteForeignKey
		{
			get { return SiteId; }
			set 
			{	
				SiteId = value;
			}
			
		}
		

		protected IDataProvider<VehicleLockoutDataObject> _vehicleLockoutService => _serviceProvider.GetRequiredService<IDataProvider<VehicleLockoutDataObject>>();

		private readonly SemaphoreSlim __vehicleLockoutsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleLockoutsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleLockouts", which is a collection of VehicleLockoutDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleLockoutDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> LoadVehicleLockoutsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleLockoutsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> LoadVehicleLockoutsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleLockoutsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleLockoutsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DriverId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleLockoutService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleLockoutsAlreadyLazyLoaded = true;
                }

                return await GetVehicleLockoutsAsync(false);
            }
            finally
            {
                __vehicleLockoutsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleLockoutDataObject> VehicleLockouts 
		{
			get
			{			
				return GetVehicleLockoutsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleLockouts()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("DriverDataObject") && ObjectsDataSet.RelationsToInclude["DriverDataObject"].Contains("VehicleLockouts");
		}

		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> GetVehicleLockoutsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleLockoutsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleLockoutsAsync(forceReload : forceReload);
			}
			var vehicleLockouts = ObjectsDataSet.GetRelatedObjects<VehicleLockoutDataObject>(this, "VehicleLockouts");							
			vehicleLockouts.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleLockouts_CollectionChanged);
				
			return vehicleLockouts;
		}

        private void VehicleLockouts_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleLockoutDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleLockout", "DriverDataObject.VehicleLockouts_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of DriverDataObject throw an exception while trying to add VehicleLockoutDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._driver_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DriverId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DriverId == default(Nullable<System.Guid>))
							relatedObj.DriverId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleLockoutDataObject).Driver = null;
                    // }
                    break;
            }            
        }

		public override void ClearLazyLoadFlags()
		{
			__allDriverAccessAbuseStoreProcedureItemsAlreadyLazyLoaded = false;
			__allLicenseExpiryViewItemsAlreadyLazyLoaded = false;
			__broadcastMessageHistoryItemsAlreadyLazyLoaded = false;
			__currentStatusDriverViewItemsAlreadyLazyLoaded = false;
			__detailedSessionViewItemsAlreadyLazyLoaded = false;
			__driverProficiencyViewItemsAlreadyLazyLoaded = false;
			__generalProductivityPerDriverViewLatestItemsAlreadyLazyLoaded = false;
			__impactsForVehicleViewItemsAlreadyLazyLoaded = false;
			__licensesByModelAlreadyLazyLoaded = false;
			__onDemandSessionItemsAlreadyLazyLoaded = false;
			__pedestrianDetectionHistoryItemsAlreadyLazyLoaded = false;
			__sessionsAlreadyLazyLoaded = false;
			__vehicleLockoutsAlreadyLazyLoaded = false;
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadCardAsync()) != null)
				result.Add(Card);
			if ((await LoadCustomerAsync()) != null)
				result.Add(Customer);
			if ((await LoadDepartmentAsync()) != null)
				result.Add(Department);
			if ((await LoadGeneralLicenceAsync()) != null)
				result.Add(GeneralLicence);
			if ((await LoadSiteAsync()) != null)
				result.Add(Site);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadAllDriverAccessAbuseStoreProcedureItemsAsync()) != null)
				result.AddRange(AllDriverAccessAbuseStoreProcedureItems);
			if ((await LoadAllLicenseExpiryViewItemsAsync()) != null)
				result.AddRange(AllLicenseExpiryViewItems);
			if ((await LoadBroadcastMessageHistoryItemsAsync()) != null)
				result.AddRange(BroadcastMessageHistoryItems);
			if ((await LoadCurrentStatusDriverViewItemsAsync()) != null)
				result.AddRange(CurrentStatusDriverViewItems);
			if ((await LoadDetailedSessionViewItemsAsync()) != null)
				result.AddRange(DetailedSessionViewItems);
			if ((await LoadDriverProficiencyViewItemsAsync()) != null)
				result.AddRange(DriverProficiencyViewItems);
			if ((await LoadGeneralProductivityPerDriverViewLatestItemsAsync()) != null)
				result.AddRange(GeneralProductivityPerDriverViewLatestItems);
			if ((await LoadImpactsForVehicleViewItemsAsync()) != null)
				result.AddRange(ImpactsForVehicleViewItems);
			if ((await LoadLicensesByModelAsync()) != null)
				result.AddRange(LicensesByModel);
			if ((await LoadOnDemandSessionItemsAsync()) != null)
				result.AddRange(OnDemandSessionItems);
			if ((await LoadPedestrianDetectionHistoryItemsAsync()) != null)
				result.AddRange(PedestrianDetectionHistoryItems);
			if ((await LoadPersonAsync()) != null)
				result.Add(Person);
			if ((await LoadSessionsAsync()) != null)
				result.AddRange(Sessions);
			if ((await LoadVehicleLockoutsAsync()) != null)
				result.AddRange(VehicleLockouts);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				Customer == other ||
				(other is CustomerDataObject && (CustomerId != default(Nullable<System.Guid>)) && (CustomerId == (other as CustomerDataObject).Id)) || 
				Site == other ||
				(other is SiteDataObject && (SiteId != default(Nullable<System.Guid>)) && (SiteId == (other as SiteDataObject).Id)) || 
				Department == other ||
				(other is DepartmentDataObject && (DepartmentId != default(Nullable<System.Guid>)) && (DepartmentId == (other as DepartmentDataObject).Id)) || 
				Card == other ||
				(other is CardDataObject && (CardDetailsId != default(Nullable<System.Guid>)) && (CardDetailsId == (other as CardDataObject).Id)) || 
				GeneralLicence == other ||
				(other is LicenceDetailDataObject && (LicenceDetailId != default(Nullable<System.Guid>)) && (LicenceDetailId == (other as LicenceDetailDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetActiveValue(System.Boolean valueToSet)
		{
			SetActiveValue(valueToSet, true, true);
		}

		public virtual void SetActiveValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_active != valueToSet)
			{
				_active = valueToSet;

				OnPropertyChanged("Active", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Status property of the Driver DataObject</summary>
        public virtual System.Boolean Active 
		{
			get	{ return _active;}
			
			
			set
			{
				SetActiveValue(value);
			}
		}		
			
			
		public virtual void SetCardDetailsIdValue(Nullable<System.Guid> valueToSet)
		{
			SetCardDetailsIdValue(valueToSet, true, true);
		}

		public virtual void SetCardDetailsIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_cardDetailsId != valueToSet)
			{
				_cardDetailsId = valueToSet;

				// CardDetailsId is a FK. Setting its value should result in a event
				OnPropertyChanged("Card", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("CardDetailsId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The CardDetailsId property of the Driver DataObject</summary>
        public virtual Nullable<System.Guid> CardDetailsId 
		{
			get	{ return _cardDetailsId;}
			
			
			set
			{
				SetCardDetailsIdValue(value);
			}
		}		
			
			
		public virtual void SetCustomerIdValue(Nullable<System.Guid> valueToSet)
		{
			SetCustomerIdValue(valueToSet, true, true);
		}

		public virtual void SetCustomerIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_customerId != valueToSet)
			{
				_customerId = valueToSet;

				// CustomerId is a FK. Setting its value should result in a event
				OnPropertyChanged("Customer", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("CustomerId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The CustomerId property of the Driver DataObject</summary>
        public virtual Nullable<System.Guid> CustomerId 
		{
			get	{ return _customerId;}
			
			
			set
			{
				SetCustomerIdValue(value);
			}
		}		
			
			
		public virtual void SetDepartmentIdValue(Nullable<System.Guid> valueToSet)
		{
			SetDepartmentIdValue(valueToSet, true, true);
		}

		public virtual void SetDepartmentIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_departmentId != valueToSet)
			{
				_departmentId = valueToSet;

				// DepartmentId is a FK. Setting its value should result in a event
				OnPropertyChanged("Department", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("DepartmentId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DepartmentId property of the Driver DataObject</summary>
        public virtual Nullable<System.Guid> DepartmentId 
		{
			get	{ return _departmentId;}
			
			
			set
			{
				SetDepartmentIdValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the Driver DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetLastSessionDateValue(Nullable<System.DateTime> valueToSet)
		{
			SetLastSessionDateValue(valueToSet, true, true);
		}

		public virtual void SetLastSessionDateValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_lastSessionDate != null)
				{
					_lastSessionDate = null;
					OnPropertyChanged("LastSessionDate", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_lastSessionDate != DateTime.MinValue.ToUniversalTime())
				{
					_lastSessionDate = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("LastSessionDate", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_lastSessionDate != DateTime.MaxValue.ToUniversalTime())
				{
					_lastSessionDate = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("LastSessionDate", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_lastSessionDate != valueToSet ||
                (_lastSessionDate != null && ((DateTime)_lastSessionDate).Kind == DateTimeKind.Unspecified))
			{
				_lastSessionDate = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("LastSessionDate", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Last Session Date property of the Driver DataObject</summary>
        public virtual Nullable<System.DateTime> LastSessionDate 
		{
			get	{ return _lastSessionDate;}
			
			
			set
			{
				SetLastSessionDateValue(value);
			}
		}		
			
			
		public virtual void SetLastSessionDateTzAdjustedValue(Nullable<System.DateTime> valueToSet)
		{
			SetLastSessionDateTzAdjustedValue(valueToSet, true, true);
		}

		public virtual void SetLastSessionDateTzAdjustedValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_lastSessionDateTzAdjusted != null)
				{
					_lastSessionDateTzAdjusted = null;
					OnPropertyChanged("LastSessionDateTzAdjusted", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_lastSessionDateTzAdjusted != DateTime.MinValue.ToUniversalTime())
				{
					_lastSessionDateTzAdjusted = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("LastSessionDateTzAdjusted", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_lastSessionDateTzAdjusted != DateTime.MaxValue.ToUniversalTime())
				{
					_lastSessionDateTzAdjusted = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("LastSessionDateTzAdjusted", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_lastSessionDateTzAdjusted != valueToSet ||
                (_lastSessionDateTzAdjusted != null && ((DateTime)_lastSessionDateTzAdjusted).Kind == DateTimeKind.Unspecified))
			{
				_lastSessionDateTzAdjusted = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("LastSessionDateTzAdjusted", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Last Session Date Tz Adjusted property of the Driver DataObject</summary>
        public virtual Nullable<System.DateTime> LastSessionDateTzAdjusted 
		{
			get	{ return _lastSessionDateTzAdjusted;}
			
			
			set
			{
				SetLastSessionDateTzAdjustedValue(value);
			}
		}		
			
			
		public virtual void SetLastSessionIdValue(System.String valueToSet)
		{
			SetLastSessionIdValue(valueToSet, true, true);
		}

		public virtual void SetLastSessionIdValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_lastSessionId != valueToSet)
			{
				_lastSessionId = valueToSet;

				OnPropertyChanged("LastSessionId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The LastSessionId property of the Driver DataObject</summary>
        public virtual System.String LastSessionId 
		{
			get	{ return String.IsNullOrEmpty(_lastSessionId) ? null : _lastSessionId; }
			
			
			set
			{
				SetLastSessionIdValue(value);
			}
		}		
			
			
		public virtual void SetLicenceDetailIdValue(Nullable<System.Guid> valueToSet)
		{
			SetLicenceDetailIdValue(valueToSet, true, true);
		}

		public virtual void SetLicenceDetailIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_licenceDetailId != valueToSet)
			{
				_licenceDetailId = valueToSet;

				// LicenceDetailId is a FK. Setting its value should result in a event
				OnPropertyChanged("GeneralLicence", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("LicenceDetailId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The LicenceDetailId property of the Driver DataObject</summary>
        public virtual Nullable<System.Guid> LicenceDetailId 
		{
			get	{ return _licenceDetailId;}
			
			
			set
			{
				SetLicenceDetailIdValue(value);
			}
		}		
			
			
		public virtual void SetLicenseModeValue(ModeEnum valueToSet)
		{
			SetLicenseModeValue(valueToSet, true, true);
		}

		public virtual void SetLicenseModeValue(ModeEnum valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_licenseMode != valueToSet)
			{
				_licenseMode = valueToSet;

				OnPropertyChanged("LicenseMode", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("LicenseModeDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The License Mode property of the Driver DataObject</summary>
        public virtual ModeEnum LicenseMode 
		{
			get	{ return _licenseMode;}
			
			
			set
			{
				SetLicenseModeValue(value);
			}
		}		
      public virtual string LicenseModeDisplayString
		{
			get
			{
				return LicenseModeEnumDisplayNameCollection.Where(v => v.Value == LicenseMode).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<ModeEnum>> LicenseModeEnumDisplayNameCollection
	    {
	        get
	        {
                return ModeEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetSiteIdValue(Nullable<System.Guid> valueToSet)
		{
			SetSiteIdValue(valueToSet, true, true);
		}

		public virtual void SetSiteIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_siteId != valueToSet)
			{
				_siteId = valueToSet;

				// SiteId is a FK. Setting its value should result in a event
				OnPropertyChanged("Site", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("SiteId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SiteId property of the Driver DataObject</summary>
        public virtual Nullable<System.Guid> SiteId 
		{
			get	{ return _siteId;}
			
			
			set
			{
				SetSiteIdValue(value);
			}
		}		
			
			
		public virtual void SetVehicleAccessValue(Nullable<System.Boolean> valueToSet)
		{
			SetVehicleAccessValue(valueToSet, true, true);
		}

		public virtual void SetVehicleAccessValue(Nullable<System.Boolean> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleAccess != valueToSet)
			{
				_vehicleAccess = valueToSet;

				OnPropertyChanged("VehicleAccess", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Vehicle Access property of the Driver DataObject</summary>
        public virtual Nullable<System.Boolean> VehicleAccess 
		{
			get	{ return _vehicleAccess;}
			
			
			set
			{
				SetVehicleAccessValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
			var _allDriverAccessAbuseStoreProcedureItems = GetAllDriverAccessAbuseStoreProcedureItemsAsync(false).Result;
			if (_allDriverAccessAbuseStoreProcedureItems != null)
            {
                foreach (var item in _allDriverAccessAbuseStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var _allLicenseExpiryViewItems = GetAllLicenseExpiryViewItemsAsync(false).Result;
			if (_allLicenseExpiryViewItems != null)
            {
                foreach (var item in _allLicenseExpiryViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var _broadcastMessageHistoryItems = GetBroadcastMessageHistoryItemsAsync(false).Result;
			if (_broadcastMessageHistoryItems != null)
            {
                foreach (var item in _broadcastMessageHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var card = GetCardAsync(false).Result;
			if (card != null && this.IsDirty)
            {
				card.NotifyPropertyChanged("Driver." + propertyName, callers);
			}
			var _currentStatusDriverViewItems = GetCurrentStatusDriverViewItemsAsync(false).Result;
			if (_currentStatusDriverViewItems != null)
            {
                foreach (var item in _currentStatusDriverViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var _detailedSessionViewItems = GetDetailedSessionViewItemsAsync(false).Result;
			if (_detailedSessionViewItems != null)
            {
                foreach (var item in _detailedSessionViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var _driverProficiencyViewItems = GetDriverProficiencyViewItemsAsync(false).Result;
			if (_driverProficiencyViewItems != null)
            {
                foreach (var item in _driverProficiencyViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var generalLicence = GetGeneralLicenceAsync(false).Result;
			if (generalLicence != null && this.IsDirty)
            {
				generalLicence.NotifyPropertyChanged("Driver." + propertyName, callers);
			}
			var _generalProductivityPerDriverViewLatestItems = GetGeneralProductivityPerDriverViewLatestItemsAsync(false).Result;
			if (_generalProductivityPerDriverViewLatestItems != null)
            {
                foreach (var item in _generalProductivityPerDriverViewLatestItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var _impactsForVehicleViewItems = GetImpactsForVehicleViewItemsAsync(false).Result;
			if (_impactsForVehicleViewItems != null)
            {
                foreach (var item in _impactsForVehicleViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var _licensesByModel = GetLicensesByModelAsync(false).Result;
			if (_licensesByModel != null)
            {
                foreach (var item in _licensesByModel)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var _onDemandSessionItems = GetOnDemandSessionItemsAsync(false).Result;
			if (_onDemandSessionItems != null)
            {
                foreach (var item in _onDemandSessionItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var _pedestrianDetectionHistoryItems = GetPedestrianDetectionHistoryItemsAsync(false).Result;
			if (_pedestrianDetectionHistoryItems != null)
            {
                foreach (var item in _pedestrianDetectionHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var person = GetPersonAsync(false).Result;
			if (person != null && this.IsDirty)
            {
				person.NotifyPropertyChanged("Driver." + propertyName, callers);
			}
			var _sessions = GetSessionsAsync(false).Result;
			if (_sessions != null)
            {
                foreach (var item in _sessions)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
			var _vehicleLockouts = GetVehicleLockoutsAsync(false).Result;
			if (_vehicleLockouts != null)
            {
                foreach (var item in _vehicleLockouts)
                {
                    item.NotifyPropertyChanged(String.Concat("Driver.", propertyName), callers);                    
                }
            }
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<DriverDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is DriverDataObject))
				return false;

			var p = (DriverDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.Active == p.Active;
			fieldsComparison &= this.CardDetailsId == p.CardDetailsId;
			fieldsComparison &= this.CustomerId == p.CustomerId;
			fieldsComparison &= this.DepartmentId == p.DepartmentId;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.LastSessionDate == p.LastSessionDate;
			fieldsComparison &= this.LastSessionDateTzAdjusted == p.LastSessionDateTzAdjusted;
			fieldsComparison &= this.LastSessionId == p.LastSessionId;
			fieldsComparison &= this.LicenceDetailId == p.LicenceDetailId;
			fieldsComparison &= this.LicenseMode == p.LicenseMode;
			fieldsComparison &= this.SiteId == p.SiteId;
			fieldsComparison &= this.VehicleAccess == p.VehicleAccess;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// LastSessionDate is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._lastSessionDate_WithTimezoneOffset != null)
			{
				this.LastSessionDate = ((DateTimeOffset)this._lastSessionDate_WithTimezoneOffset).DateTime;
			}
			// LastSessionDateTzAdjusted is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._lastSessionDateTzAdjusted_WithTimezoneOffset != null)
			{
				this.LastSessionDateTzAdjusted = ((DateTimeOffset)this._lastSessionDateTzAdjusted_WithTimezoneOffset).DateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class DriverCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public DriverCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public DriverCollectionContainer()
		{
		}
		
		public DriverCollectionContainer Construct(DataObjectCollection<DriverDataObject> driverItems)
        {
            if (driverItems == null)
                return this;
				
			this.PrimaryKeys = driverItems.Select(c => c.PrimaryKey).ToList();
            if (driverItems.ObjectsDataSet == null)
            {
                driverItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = driverItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = driverItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<DriverDataObject> ExtractDriverItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<DriverDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<DriverDataObject>(typeof(DriverDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class DriverContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public DriverContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual DriverContainer Construct(DriverDataObject driver, bool includeDirtyObjectsOnly = false)
		{
            if (driver == null)
                return this;

			this.PrimaryKey = driver.PrimaryKey;
			
            if (driver.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(driver);
            }

			if(driver.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity Driver", "Unable to set a dataset to the entity. Container may not be initialized", "DriverDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : Driver");
			}

			if(driver.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in DriverDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "DriverDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in DriverDataObject");
			}
			this.InternalObjectId = (int) driver.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? driver.ObjectsDataSet.CloneDirtyObjects() : driver.ObjectsDataSet;

			return this;
		}
		
		public DriverDataObject ExtractDriver()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<DriverDataObject>(typeof(DriverDataObject), InternalObjectId);
			
			return result;
        }	
	}

}