﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Data.DataProviders.Dispatchers
{
    public class VehicleDataProviderDispatcher : IDataProviderDispatcher<VehicleDataObject>
    {
		protected IServiceProvider _serviceProvider;
		
		public VehicleDataProviderDispatcher(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
		
		protected IDataProvider<UnitUtilisationStoreProcedureDataObject> unitUtilisationStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<UnitUtilisationStoreProcedureDataObject>>();
		protected IDataProvider<BroadcastMessageHistoryDataObject> broadcastMessageHistoryDataProvider => _serviceProvider.GetService<IDataProvider<BroadcastMessageHistoryDataObject>>();
		protected IDataProvider<SessionDataObject> sessionDataProvider => _serviceProvider.GetService<IDataProvider<SessionDataObject>>();
		protected IDataProvider<SlamcoreDeviceHistoryDataObject> slamcoreDeviceHistoryDataProvider => _serviceProvider.GetService<IDataProvider<SlamcoreDeviceHistoryDataObject>>();
		protected IDataProvider<DepartmentChecklistDataObject> departmentChecklistDataProvider => _serviceProvider.GetService<IDataProvider<DepartmentChecklistDataObject>>();
		protected IDataProvider<FirmwareDataObject> firmwareDataProvider => _serviceProvider.GetService<IDataProvider<FirmwareDataObject>>();
		protected IDataProvider<VehicleOtherSettingsDataObject> vehicleOtherSettingsDataProvider => _serviceProvider.GetService<IDataProvider<VehicleOtherSettingsDataObject>>();
		protected IDataProvider<VehicleLastGPSLocationViewDataObject> vehicleLastGPSLocationViewDataProvider => _serviceProvider.GetService<IDataProvider<VehicleLastGPSLocationViewDataObject>>();
		protected IDataProvider<CustomerDataObject> customerDataProvider => _serviceProvider.GetService<IDataProvider<CustomerDataObject>>();
		protected IDataProvider<ServiceSettingsDataObject> serviceSettingsDataProvider => _serviceProvider.GetService<IDataProvider<ServiceSettingsDataObject>>();
		protected IDataProvider<VehicleSupervisorsViewDataObject> vehicleSupervisorsViewDataProvider => _serviceProvider.GetService<IDataProvider<VehicleSupervisorsViewDataObject>>();
		protected IDataProvider<AllVehicleCalibrationStoreProcedureDataObject> allVehicleCalibrationStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<AllVehicleCalibrationStoreProcedureDataObject>>();
		protected IDataProvider<PersonToPerVehicleMasterAccessViewDataObject> personToPerVehicleMasterAccessViewDataProvider => _serviceProvider.GetService<IDataProvider<PersonToPerVehicleMasterAccessViewDataObject>>();
		protected IDataProvider<InspectionDataObject> inspectionDataProvider => _serviceProvider.GetService<IDataProvider<InspectionDataObject>>();
		protected IDataProvider<OnDemandSessionDataObject> onDemandSessionDataProvider => _serviceProvider.GetService<IDataProvider<OnDemandSessionDataObject>>();
		protected IDataProvider<PedestrianDetectionHistoryDataObject> pedestrianDetectionHistoryDataProvider => _serviceProvider.GetService<IDataProvider<PedestrianDetectionHistoryDataObject>>();
		protected IDataProvider<MessageHistoryDataObject> messageHistoryDataProvider => _serviceProvider.GetService<IDataProvider<MessageHistoryDataObject>>();
		protected IDataProvider<VORSettingHistoryDataObject> vORSettingHistoryDataProvider => _serviceProvider.GetService<IDataProvider<VORSettingHistoryDataObject>>();
		protected IDataProvider<PerVehicleMasterCardAccessDataObject> perVehicleMasterCardAccessDataProvider => _serviceProvider.GetService<IDataProvider<PerVehicleMasterCardAccessDataObject>>();
		protected IDataProvider<SiteDataObject> siteDataProvider => _serviceProvider.GetService<IDataProvider<SiteDataObject>>();
		protected IDataProvider<DriverDataObject> driverDataProvider => _serviceProvider.GetService<IDataProvider<DriverDataObject>>();
		protected IDataProvider<VehicleAlertSubscriptionDataObject> vehicleAlertSubscriptionDataProvider => _serviceProvider.GetService<IDataProvider<VehicleAlertSubscriptionDataObject>>();
		protected IDataProvider<VehicleLockoutDataObject> vehicleLockoutDataProvider => _serviceProvider.GetService<IDataProvider<VehicleLockoutDataObject>>();
		protected IDataProvider<GeneralProductivityPerVehicleViewDataObject> generalProductivityPerVehicleViewDataProvider => _serviceProvider.GetService<IDataProvider<GeneralProductivityPerVehicleViewDataObject>>();
		protected IDataProvider<VehicleToPreOpChecklistViewDataObject> vehicleToPreOpChecklistViewDataProvider => _serviceProvider.GetService<IDataProvider<VehicleToPreOpChecklistViewDataObject>>();
		protected IDataProvider<VehicleSessionlessImpactDataObject> vehicleSessionlessImpactDataProvider => _serviceProvider.GetService<IDataProvider<VehicleSessionlessImpactDataObject>>();
		protected IDataProvider<CurrentStatusVehicleViewDataObject> currentStatusVehicleViewDataProvider => _serviceProvider.GetService<IDataProvider<CurrentStatusVehicleViewDataObject>>();
		protected IDataProvider<NetworkSettingsDataObject> networkSettingsDataProvider => _serviceProvider.GetService<IDataProvider<NetworkSettingsDataObject>>();
		protected IDataProvider<OnDemandSettingsDataObject> onDemandSettingsDataProvider => _serviceProvider.GetService<IDataProvider<OnDemandSettingsDataObject>>();
		protected IDataProvider<ModuleDataObject> moduleDataProvider => _serviceProvider.GetService<IDataProvider<ModuleDataObject>>();
		protected IDataProvider<DetailedSessionViewDataObject> detailedSessionViewDataProvider => _serviceProvider.GetService<IDataProvider<DetailedSessionViewDataObject>>();
		protected IDataProvider<ChecklistSettingsDataObject> checklistSettingsDataProvider => _serviceProvider.GetService<IDataProvider<ChecklistSettingsDataObject>>();
		protected IDataProvider<VehicleDiagnosticDataObject> vehicleDiagnosticDataProvider => _serviceProvider.GetService<IDataProvider<VehicleDiagnosticDataObject>>();
		protected IDataProvider<VehicleBroadcastMessageDataObject> vehicleBroadcastMessageDataProvider => _serviceProvider.GetService<IDataProvider<VehicleBroadcastMessageDataObject>>();
		protected IDataProvider<VehicleGPSDataObject> vehicleGPSDataProvider => _serviceProvider.GetService<IDataProvider<VehicleGPSDataObject>>();
		protected IDataProvider<VehicleHireDehireHistoryDataObject> vehicleHireDehireHistoryDataProvider => _serviceProvider.GetService<IDataProvider<VehicleHireDehireHistoryDataObject>>();
		protected IDataProvider<ModuleHistoryDataObject> moduleHistoryDataProvider => _serviceProvider.GetService<IDataProvider<ModuleHistoryDataObject>>();
		protected IDataProvider<UnitUnutilisationStoreProcedureDataObject> unitUnutilisationStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<UnitUnutilisationStoreProcedureDataObject>>();
		protected IDataProvider<DetailedVORSessionStoreProcedureDataObject> detailedVORSessionStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<DetailedVORSessionStoreProcedureDataObject>>();
		protected IDataProvider<DepartmentDataObject> departmentDataProvider => _serviceProvider.GetService<IDataProvider<DepartmentDataObject>>();
		protected IDataProvider<PersonDataObject> personDataProvider => _serviceProvider.GetService<IDataProvider<PersonDataObject>>();
		protected IDataProvider<PerVehicleNormalCardAccessDataObject> perVehicleNormalCardAccessDataProvider => _serviceProvider.GetService<IDataProvider<PerVehicleNormalCardAccessDataObject>>();
		protected IDataProvider<ImpactsForVehicleViewDataObject> impactsForVehicleViewDataProvider => _serviceProvider.GetService<IDataProvider<ImpactsForVehicleViewDataObject>>();
		protected IDataProvider<VehicleProficiencyViewDataObject> vehicleProficiencyViewDataProvider => _serviceProvider.GetService<IDataProvider<VehicleProficiencyViewDataObject>>();
		protected IDataProvider<ChecklistFailurePerVechicleViewDataObject> checklistFailurePerVechicleViewDataProvider => _serviceProvider.GetService<IDataProvider<ChecklistFailurePerVechicleViewDataObject>>();
		protected IDataProvider<PersonToPerVehicleNormalAccessViewDataObject> personToPerVehicleNormalAccessViewDataProvider => _serviceProvider.GetService<IDataProvider<PersonToPerVehicleNormalAccessViewDataObject>>();
		protected IDataProvider<CanruleDataObject> canruleDataProvider => _serviceProvider.GetService<IDataProvider<CanruleDataObject>>();
		protected IDataProvider<ModelDataObject> modelDataProvider => _serviceProvider.GetService<IDataProvider<ModelDataObject>>();
		protected IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject> allVORSessionsPerVehicleStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>>();

        public async Task DispatchForEntityAsync(VehicleDataObject entity, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("Vehicle", parameters);

            foreach (var include in includes)
            {
	
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
                  case "unitunutilisationstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("UnitUnutilisationStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await unitUtilisationStoreProcedureDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "broadcastmessagehistoryitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("BroadcastMessageHistoryItems"))
									break;

								try
								{
									var objectToFetch = await broadcastMessageHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "sessions":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Sessions"))
									break;

								try
								{
									var objectToFetch = await sessionDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "slamcoredevicehistoryitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("SlamcoreDeviceHistoryItems"))
									break;

								try
								{
									var objectToFetch = await slamcoreDeviceHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "departmentchecklist":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DepartmentChecklist"))
									break;

								if (entity.DepartmentChecklistId != null) 
								{
									try
									{
										var objectToFetch = await departmentChecklistDataProvider.GetAsync(new DepartmentChecklistDataObject((System.Guid)entity.DepartmentChecklistId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "firmware":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Firmware"))
									break;

								if (entity.FirmwareId != null) 
								{
									try
									{
										var objectToFetch = await firmwareDataProvider.GetAsync(new FirmwareDataObject((System.Guid)entity.FirmwareId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "vehicleothersettings":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleOtherSettings"))
									break;

								if (entity.VehicleOtherSettingsId != null) 
								{
									try
									{
										var objectToFetch = await vehicleOtherSettingsDataProvider.GetAsync(new VehicleOtherSettingsDataObject((System.Guid)entity.VehicleOtherSettingsId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "vehiclelastgpslocationview":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleLastGPSLocationView"))
									break;

								try
								{
									var objectToFetch = await vehicleLastGPSLocationViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customer":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Customer"))
									break;

								try
								{
									var objectToFetch = await customerDataProvider.GetAsync(new CustomerDataObject(entity.CustomerId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "servicesettings":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ServiceSettings"))
									break;

								if (entity.ServiceSettingsId != null) 
								{
									try
									{
										var objectToFetch = await serviceSettingsDataProvider.GetAsync(new ServiceSettingsDataObject((System.Guid)entity.ServiceSettingsId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "vehiclesupervisorsviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleSupervisorsViewItems"))
									break;

								try
								{
									var objectToFetch = await vehicleSupervisorsViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "allvehiclecalibrationstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("AllVehicleCalibrationStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await allVehicleCalibrationStoreProcedureDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "persontopervehiclemasteraccessviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("PersonToPerVehicleMasterAccessViewItems"))
									break;

								try
								{
									var objectToFetch = await personToPerVehicleMasterAccessViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "inspection":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Inspection"))
									break;

								if (entity.InspectionId != null) 
								{
									try
									{
										var objectToFetch = await inspectionDataProvider.GetAsync(new InspectionDataObject((System.Guid)entity.InspectionId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "ondemandsessionitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("OnDemandSessionItems"))
									break;

								try
								{
									var objectToFetch = await onDemandSessionDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "pedestriandetectionhistoryitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("PedestrianDetectionHistoryItems"))
									break;

								try
								{
									var objectToFetch = await pedestrianDetectionHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "messagehistoryitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("MessageHistoryItems"))
									break;

								try
								{
									var objectToFetch = await messageHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vorsettinghistoryitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VORSettingHistoryItems"))
									break;

								try
								{
									var objectToFetch = await vORSettingHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclecardaccesses":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleCardAccesses"))
									break;

								try
								{
									var objectToFetch = await perVehicleMasterCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "site":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Site"))
									break;

								try
								{
									var objectToFetch = await siteDataProvider.GetAsync(new SiteDataObject(entity.SiteId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "driver":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Driver"))
									break;

								if (entity.DriverId != null) 
								{
									try
									{
										var objectToFetch = await driverDataProvider.GetAsync(new DriverDataObject((System.Guid)entity.DriverId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "vehiclealertsubscriptionitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleAlertSubscriptionItems"))
									break;

								try
								{
									var objectToFetch = await vehicleAlertSubscriptionDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclelockoutitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleLockoutItems"))
									break;

								try
								{
									var objectToFetch = await vehicleLockoutDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "generalproductivitypervehicleviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("GeneralProductivityPerVehicleViewItems"))
									break;

								try
								{
									var objectToFetch = await generalProductivityPerVehicleViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehicletopreopcheckilstitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleToPreOpCheckilstItems"))
									break;

								try
								{
									var objectToFetch = await vehicleToPreOpChecklistViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclesessionlessimpactitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleSessionlessImpactItems"))
									break;

								try
								{
									var objectToFetch = await vehicleSessionlessImpactDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "currentstatusvehicleviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CurrentStatusVehicleViewItems"))
									break;

								try
								{
									var objectToFetch = await currentStatusVehicleViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "networksettingsitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("NetworkSettingsItems"))
									break;

								try
								{
									var objectToFetch = await networkSettingsDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "ondemandsettings":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("OnDemandSettings"))
									break;

								try
								{
									var objectToFetch = await onDemandSettingsDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "module":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Module"))
									break;

								try
								{
									var objectToFetch = await moduleDataProvider.GetAsync(new ModuleDataObject(entity.ModuleId1), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "detailedsessionviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DetailedSessionViewItems"))
									break;

								try
								{
									var objectToFetch = await detailedSessionViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "checklistsettings":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ChecklistSettings"))
									break;

								if (entity.ChecklistSettingsId != null) 
								{
									try
									{
										var objectToFetch = await checklistSettingsDataProvider.GetAsync(new ChecklistSettingsDataObject((System.Guid)entity.ChecklistSettingsId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "vehiclediagnostic":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleDiagnostic"))
									break;

								try
								{
									var objectToFetch = await vehicleDiagnosticDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclebroadcastmessageitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleBroadcastMessageItems"))
									break;

								try
								{
									var objectToFetch = await vehicleBroadcastMessageDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclegpslocations":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleGPSLocations"))
									break;

								try
								{
									var objectToFetch = await vehicleGPSDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclehiredehirehistoryitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleHireDehireHistoryItems"))
									break;

								try
								{
									var objectToFetch = await vehicleHireDehireHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "modulehistoryitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ModuleHistoryItems"))
									break;

								try
								{
									var objectToFetch = await moduleHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "unitutilisationstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("UnitUtilisationStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await unitUnutilisationStoreProcedureDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "detailedvorsessionstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DetailedVORSessionStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await detailedVORSessionStoreProcedureDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "department":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Department"))
									break;

								try
								{
									var objectToFetch = await departmentDataProvider.GetAsync(new DepartmentDataObject(entity.DepartmentId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "person":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Person"))
									break;

								if (entity.PersonId != null) 
								{
									try
									{
										var objectToFetch = await personDataProvider.GetAsync(new PersonDataObject((System.Guid)entity.PersonId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "pervehiclenormalcardaccessitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("PerVehicleNormalCardAccessItems"))
									break;

								try
								{
									var objectToFetch = await perVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "impactsforvehicleviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ImpactsForVehicleViewItems"))
									break;

								try
								{
									var objectToFetch = await impactsForVehicleViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehicleproficiencyviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleProficiencyViewItems"))
									break;

								try
								{
									var objectToFetch = await vehicleProficiencyViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "checklistfailurepervechicleviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ChecklistFailurePerVechicleViewItems"))
									break;

								try
								{
									var objectToFetch = await checklistFailurePerVechicleViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "persontopervehiclenormalaccessviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("PersonToPerVehicleNormalAccessViewItems"))
									break;

								try
								{
									var objectToFetch = await personToPerVehicleNormalAccessViewDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "canrule":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Canrule"))
									break;

								if (entity.CanruleId != null) 
								{
									try
									{
										var objectToFetch = await canruleDataProvider.GetAsync(new CanruleDataObject((System.Guid)entity.CanruleId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "model":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Model"))
									break;

								try
								{
									var objectToFetch = await modelDataProvider.GetAsync(new ModelDataObject(entity.ModelId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "allvorsessionspervehiclestoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("AllVORSessionsPerVehicleStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await allVORSessionsPerVehicleStoreProcedureDataProvider.GetCollectionAsync(null, "VehicleId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
						default:
							throw new ApplicationException("Vehicle Entity has no relation named " + relation);
					}
          }        
		}

        public async Task DispatchForEntityCollectionAsync(IEnumerable<VehicleDataObject> entities, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("Vehicle", parameters);

            foreach (var include in includes)
            {
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
						case "unitunutilisationstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("UnitUnutilisationStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await unitUtilisationStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "broadcastmessagehistoryitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("BroadcastMessageHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await broadcastMessageHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "sessions":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Sessions"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await sessionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "slamcoredevicehistoryitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("SlamcoreDeviceHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await slamcoreDeviceHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "departmentchecklist":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DepartmentChecklist"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.DepartmentChecklistId != null).Select(e => (System.Guid)e.DepartmentChecklistId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await departmentChecklistDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "firmware":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Firmware"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.FirmwareId != null).Select(e => (System.Guid)e.FirmwareId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await firmwareDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicleothersettings":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleOtherSettings"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.VehicleOtherSettingsId != null).Select(e => (System.Guid)e.VehicleOtherSettingsId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleOtherSettingsDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclelastgpslocationview":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleLastGPSLocationView"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleLastGPSLocationViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customer":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Customer"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.CustomerId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "servicesettings":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ServiceSettings"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.ServiceSettingsId != null).Select(e => (System.Guid)e.ServiceSettingsId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await serviceSettingsDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclesupervisorsviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleSupervisorsViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleSupervisorsViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "allvehiclecalibrationstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("AllVehicleCalibrationStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await allVehicleCalibrationStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "persontopervehiclemasteraccessviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("PersonToPerVehicleMasterAccessViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await personToPerVehicleMasterAccessViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "inspection":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Inspection"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.InspectionId != null).Select(e => (System.Guid)e.InspectionId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await inspectionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "ondemandsessionitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("OnDemandSessionItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await onDemandSessionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "pedestriandetectionhistoryitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("PedestrianDetectionHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await pedestrianDetectionHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "messagehistoryitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("MessageHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await messageHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vorsettinghistoryitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VORSettingHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vORSettingHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclecardaccesses":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleCardAccesses"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await perVehicleMasterCardAccessDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "site":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Site"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.SiteId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await siteDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "driver":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Driver"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.DriverId != null).Select(e => (System.Guid)e.DriverId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await driverDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclealertsubscriptionitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleAlertSubscriptionItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleAlertSubscriptionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclelockoutitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleLockoutItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleLockoutDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "generalproductivitypervehicleviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("GeneralProductivityPerVehicleViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await generalProductivityPerVehicleViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicletopreopcheckilstitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleToPreOpCheckilstItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleToPreOpChecklistViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclesessionlessimpactitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleSessionlessImpactItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleSessionlessImpactDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "currentstatusvehicleviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CurrentStatusVehicleViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await currentStatusVehicleViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "networksettingsitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("NetworkSettingsItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await networkSettingsDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "ondemandsettings":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("OnDemandSettings"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await onDemandSettingsDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "module":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Module"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.ModuleId1).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await moduleDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "detailedsessionviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DetailedSessionViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await detailedSessionViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "checklistsettings":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ChecklistSettings"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.ChecklistSettingsId != null).Select(e => (System.Guid)e.ChecklistSettingsId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await checklistSettingsDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclediagnostic":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleDiagnostic"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleDiagnosticDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclebroadcastmessageitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleBroadcastMessageItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleBroadcastMessageDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclegpslocations":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleGPSLocations"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleGPSDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclehiredehirehistoryitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleHireDehireHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleHireDehireHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "modulehistoryitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ModuleHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await moduleHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "unitutilisationstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("UnitUtilisationStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await unitUnutilisationStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "detailedvorsessionstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DetailedVORSessionStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await detailedVORSessionStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "department":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Department"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.DepartmentId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await departmentDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "person":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Person"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.PersonId != null).Select(e => (System.Guid)e.PersonId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await personDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "pervehiclenormalcardaccessitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("PerVehicleNormalCardAccessItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await perVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "impactsforvehicleviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ImpactsForVehicleViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await impactsForVehicleViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicleproficiencyviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleProficiencyViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleProficiencyViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "checklistfailurepervechicleviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ChecklistFailurePerVechicleViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await checklistFailurePerVechicleViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "persontopervehiclenormalaccessviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("PersonToPerVehicleNormalAccessViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await personToPerVehicleNormalAccessViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "canrule":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Canrule"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.CanruleId != null).Select(e => (System.Guid)e.CanruleId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await canruleDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "model":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Model"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.ModelId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await modelDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "allvorsessionspervehiclestoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMVehicle> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("AllVORSessionsPerVehicleStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await allVORSessionsPerVehicleStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
                  default:
                        throw new ApplicationException("Vehicle Entity has no relation named " + relation);
					}
            }        
        }
	}
}