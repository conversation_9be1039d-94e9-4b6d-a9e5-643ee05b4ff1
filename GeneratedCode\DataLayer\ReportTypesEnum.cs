﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using GenerativeObjects.Practices;

namespace FleetXQ.Data.DataObjects
{
	public enum ReportTypesEnum : int
	{
		CurrentStatusReport = 10,
		MachineUnlockReport = 20,
		ServiceCheckReport = 30,
		ImpactReport = 40,
		ProficiencyReport = 50,
		GeneralProductivityReport = 60,
		PreOpCheckReport = 70,
		DriverAccessAbuseReport = 80,
		LicenseExpiryReport = 90,
		SynchronizationStatusReport = 100,
		VORReport = 110,
		VehicleCalibrationReport = 120,
		BroadcastMessageReport = 130,
		PedestrianDetectionReport = 140,
		VehicleTelemetrySchedule = 150,
		PathHistorySchedule = 160,
		AlertsReportSchedule = 170,
		DeviceStatusSchedule = 180,
		PedestrianDetectionSchedule = 190,
	}

    public static class ReportTypesEnumDisplayNames
    {
        public static readonly List<EnumDisplayString<ReportTypesEnum>> Items
            = new List<EnumDisplayString<ReportTypesEnum>>
                  {
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.CurrentStatusReport, "Current Status Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.MachineUnlockReport, "Machine Unlock Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.ServiceCheckReport, "Service Check Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.ImpactReport, "Impact Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.ProficiencyReport, "Proficiency Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.GeneralProductivityReport, "General Productivity Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.PreOpCheckReport, "Pre-Op Check Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.DriverAccessAbuseReport, "Driver Access Abuse Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.LicenseExpiryReport, "License Expiry Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.SynchronizationStatusReport, "Synchronization Status Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.VORReport, "VOR Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.VehicleCalibrationReport, "Vehicle Calibration Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.BroadcastMessageReport, "Broadcast Message Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.PedestrianDetectionReport, "Pedestrian Detection Report"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.VehicleTelemetrySchedule, "Vehicle Telemetry Schedule"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.PathHistorySchedule, "Path History Schedule"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.AlertsReportSchedule, "Alerts Report Schedule"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.DeviceStatusSchedule, "Device Status Schedule"),
					new EnumDisplayString<ReportTypesEnum>(ReportTypesEnum.PedestrianDetectionSchedule, "Pedestrian Detection Schedule"),
	              };
    }
}