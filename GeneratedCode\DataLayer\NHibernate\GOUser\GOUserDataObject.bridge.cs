﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge DataObject -> ORMEntity
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	public partial class GOUserDataObject
	{
		protected IThreadContext _threadContext => _serviceProvider.GetRequiredService<IThreadContext>();
		protected INHibernateSessionController _nhibernateSessionController => _serviceProvider.GetRequiredService<INHibernateSessionController>();

		public override IORMEntity ToORMEntity()
		{
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);

			var result = new ORMGOUser(); 

			CopyTo(result, session);

			// Ensure no copy of result (instance with same key) in session
			Evict(result, session);

			return result;
		}

		protected void CopyTo(ORMGOUser x, NHibernate.ISession session)
		{
			x.Id = Id;
			x.WebsiteAccessLevelValue = WebsiteAccessLevelValue;
			x.ExternalUserId = ExternalUserId?.Truncate(500);
			x.NewEmailAddress = NewEmailAddress?.Truncate(150);
			x.EmailAddress = EmailAddress?.Truncate(150);
			x.UserName = UserName?.Truncate(150);
			x.FullName = FullName?.Truncate(250);
			x.AllowedDepartmentNames = AllowedDepartmentNames?.Truncate(1000);
			x.LastName = LastName?.Truncate(100);
			x.PreferredLocaleString = PreferredLocaleString?.Truncate(50);
			x.FirstName = FirstName?.Truncate(100);
			x.Password = Password?.Truncate(150);
			x.PasswordExpiry = PasswordExpiry;
			x.EmailChangeValidationInProgress = EmailChangeValidationInProgress;
			x.EmailValidated = EmailValidated;
			x.UserValidated = UserValidated;
			x.NewEmailValidated = NewEmailValidated;
			x.Blocked = Blocked;
			x.Unregistered = Unregistered;
			x.DealerAdmin = DealerAdmin;
			x.WebsiteAccessLevel = (int)WebsiteAccessLevel;
			x.PreferredLocale = (int?)PreferredLocale;
				
			x.Dealer = this.Dealer != null ? session.Load<ORMDealer>(this.Dealer.Id) : (this.DealerId != null ? session.Load<ORMDealer>(this.DealerId) : null);
			x.DealerId = this.Dealer != null ? this.Dealer.Id : DealerId; 
			x.GORoleName = this.GORole != null ? this.GORole.Name : GORoleName; 
		}
 
		private void Evict(ORMGOUser result, NHibernate.ISession session)
		{
			foreach (var entity in session.GetSessionImplementation().PersistenceContext.EntitiesByKey.Values.ToArray())
			{
				var evictee = entity as ORMGOUser;

				if (evictee != null && evictee.Id == result.Id)
				{
					session.Evict(evictee);
				}
			}
		}

	}
}