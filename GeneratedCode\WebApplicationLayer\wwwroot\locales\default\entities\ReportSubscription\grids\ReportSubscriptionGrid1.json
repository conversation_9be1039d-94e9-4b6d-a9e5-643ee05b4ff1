﻿{
  "title": "Report Scheduler",
  "description": " ",
  "columns": {
    "CustomerNameGridColumn": "Customer Name",
    "NameGridColumn": "Name",
    "NameGridColumn1": "Name",
    "FrequencyGridColumn": "Frequency",
    "ReportEndTimeGridColumn": "Report End Time",
    "ReportStartTimeGridColumn": "Report Start Time",
    "ReportNameGridColumn": "Report Name"
  },
  "messages": {
    "noDataMessage": "No Report Subscription data",
    "elementsCountLabel": "Items"
  },
  "commands": {
    "Subscribe": "Create Vehicle Telemetry Schedule",
    "CreateVehicleTelemetrySchedule": "Create Path History Schedule",
    "Create": "Create Alerts Report Schedule",
    "Create1": "Create Device Status Schedule",
    "PedestrianDetectionReport": "Create Pedestrian Detection Report Schedule "
  }
} 