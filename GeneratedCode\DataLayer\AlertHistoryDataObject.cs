﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'AlertHistory'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class AlertHistoryDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("AlertId")]
		protected System.Guid _alertId;
		[JsonProperty ("CreatedDateTime")]
		protected System.DateTime _createdDateTime;
		[JsonProperty("CreatedDateTime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _createdDateTime_WithTimezoneOffset;
		[JsonProperty ("Description")]
		protected System.String _description;
		[JsonProperty ("DriverId")]
		protected System.Guid _driverId;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("IsAcknowledged")]
		protected System.Boolean _isAcknowledged;
		[JsonProperty ("IsResolved")]
		protected System.Boolean _isResolved;
		[JsonProperty ("VehicleId")]
		protected System.Guid _vehicleId;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)
		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _alert_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_alert_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _driver_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_driver_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _vehicle_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_vehicle_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public AlertHistoryDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
			SetIsAcknowledgedValue(false, false, false);
			SetIsResolvedValue(false, false, false);
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public AlertHistoryDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public AlertHistoryDataObject Initialize(System.Guid id)
		{
			this._id = id;
			SetIsAcknowledgedValue(false, false, false);
			SetIsResolvedValue(false, false, false);
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public AlertHistoryDataObject Initialize(AlertHistoryDataObject template, bool deepCopy)
		{
			this.SetCreatedDateTimeValue(template.CreatedDateTime, false, false);
			this._createdDateTime_WithTimezoneOffset = template._createdDateTime_WithTimezoneOffset;
			this.SetAlertIdValue(template.AlertId, false, false);
			this.SetDescriptionValue(template.Description, false, false);
			this.SetDriverIdValue(template.DriverId, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetIsAcknowledgedValue(template.IsAcknowledged, false, false);
			this.SetIsResolvedValue(template.IsResolved, false, false);
			this.SetVehicleIdValue(template.VehicleId, false, false);
 
			this._alert_NewObjectId = template._alert_NewObjectId;
 
			this._driver_NewObjectId = template._driver_NewObjectId;
 
			this._vehicle_NewObjectId = template._vehicle_NewObjectId;
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual AlertHistoryDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual AlertHistoryDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<AlertHistoryDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var alertHistorySource = sourceObject as AlertHistoryDataObject;

			if (ReferenceEquals(null, alertHistorySource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetAlertIdValue(alertHistorySource.AlertId, false, false);
			this.SetCreatedDateTimeValue(alertHistorySource.CreatedDateTime, false, false);
			this.SetDescriptionValue(alertHistorySource.Description, false, false);
			this.SetDriverIdValue(alertHistorySource.DriverId, false, false);
			this.SetIdValue(alertHistorySource.Id, false, false);
			this.SetIsAcknowledgedValue(alertHistorySource.IsAcknowledged, false, false);
			this.SetIsResolvedValue(alertHistorySource.IsResolved, false, false);
			this.SetVehicleIdValue(alertHistorySource.VehicleId, false, false);
			this._alert_NewObjectId = (sourceObject as AlertHistoryDataObject)._alert_NewObjectId;

			this._driver_NewObjectId = (sourceObject as AlertHistoryDataObject)._driver_NewObjectId;

			this._vehicle_NewObjectId = (sourceObject as AlertHistoryDataObject)._vehicle_NewObjectId;

			if (deepCopy)
			{
				this.ObjectsDataSet = alertHistorySource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(alertHistorySource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as AlertHistoryDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {
			if (this._alert_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._alert_NewObjectId))
				{
                    this._alert_NewObjectId = null;
				}
                else
				{
					this._alert_NewObjectId = datasetMergingInternalIdMapping[(int) this._alert_NewObjectId];
				}
			}

			if (this._driver_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._driver_NewObjectId))
				{
                    this._driver_NewObjectId = null;
				}
                else
				{
					this._driver_NewObjectId = datasetMergingInternalIdMapping[(int) this._driver_NewObjectId];
				}
			}

			if (this._vehicle_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._vehicle_NewObjectId))
				{
                    this._vehicle_NewObjectId = null;
				}
                else
				{
					this._vehicle_NewObjectId = datasetMergingInternalIdMapping[(int) this._vehicle_NewObjectId];
				}
			}

		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<AlertDataObject> _alertService => _serviceProvider.GetRequiredService<IDataProvider<AlertDataObject>>();
      public virtual void SetAlertValue(AlertDataObject valueToSet)
		{
			SetAlertValue(valueToSet, true, true);
		}

        public virtual void SetAlertValue(AlertDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			AlertDataObject existing_alert = null ;

			if ( !(ObjectsDataSet == null))
			{
				AlertDataObject key;

				if (this._alert_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<AlertDataObject>().Initialize(this.AlertId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<AlertDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._alert_NewObjectId;			
				}

				existing_alert = (AlertDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_alert ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Alert", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "AlertHistoryDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_alert_NewObjectId != valueToSet.InternalObjectId)
					{
						_alert_NewObjectId = valueToSet.InternalObjectId;
						_alertId = valueToSet.Id;
						OnPropertyChanged("AlertId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_alertId != valueToSet.Id)
					{
						_alert_NewObjectId = null;

						_alertId = valueToSet.Id;
						OnPropertyChanged("AlertId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_alertId = Guid.Empty;
				OnPropertyChanged("AlertId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_alert ,valueToSet))
				OnPropertyChanged("Alert", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __alertSemaphore = new SemaphoreSlim(1, 1);
		private bool __alertAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Alert", which is a AlertDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a AlertDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<AlertDataObject> LoadAlertAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadAlertAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<AlertDataObject> LoadAlertAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __alertSemaphore.WaitAsync();
			
	        try
            {
                if (!__alertAlreadyLazyLoaded || forceReload)
                {
								
					AlertDataObject alert = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __alertAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						alert = _serviceProvider.GetRequiredService<AlertDataObject>().Initialize(this.AlertId);
						alert.IsNew = false;
						alert = (AlertDataObject)ObjectsDataSet.GetObject(alert);
						if (alert != null)
						{
							return alert;
						}
					}

					alert = await _alertService.GetAsync(_serviceProvider.GetRequiredService<AlertDataObject>().Initialize(this.AlertId), parameters : parameters, skipSecurity: skipSecurity);

					SetAlertValue(alert, false, false);
					__alertAlreadyLazyLoaded = true;				
		
					alert = _serviceProvider.GetRequiredService<AlertDataObject>().Initialize(this.AlertId);
					alert.IsNew = false;
					alert = (AlertDataObject)ObjectsDataSet.GetObject(alert);
                    __alertAlreadyLazyLoaded = true;
                }

                return await GetAlertAsync(false);
            }
            finally
            {
                __alertSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual AlertDataObject Alert 
		{
			get
			{			
				return GetAlertAsync(true).Result;
			}
			set
			{
				SetAlertValue(value);
			}
		}
		
		public virtual bool ShouldSerializeAlert()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("AlertHistoryDataObject") && ObjectsDataSet.RelationsToInclude["AlertHistoryDataObject"].Contains("Alert");
		}

		public virtual async Task<AlertDataObject> GetAlertAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			AlertDataObject alert;

				
			if (_alert_NewObjectId != null)
			{
				alert = _serviceProvider.GetRequiredService<AlertDataObject>();
				alert.IsNew = true;
				alert.InternalObjectId = _alert_NewObjectId;
				alert = (AlertDataObject)ObjectsDataSet.GetObject(alert);
			}
			else
			{
				alert = _serviceProvider.GetRequiredService<AlertDataObject>().Initialize(this.AlertId);
				alert.IsNew = false;
				alert = (AlertDataObject)ObjectsDataSet.GetObject(alert);
				
				if (allowLazyLoading && alert == null && LazyLoadingEnabled && (!__alertAlreadyLazyLoaded || forceReload))
				{
					alert = await LoadAlertAsync(forceReload : forceReload);
				}
			}
				
			return alert;
		}

		public virtual System.Guid AlertForeignKey
		{
			get { return AlertId; }
			set 
			{	
				AlertId = value;
			}
			
		}
		

		protected IDataProvider<DriverDataObject> _driverService => _serviceProvider.GetRequiredService<IDataProvider<DriverDataObject>>();
      public virtual void SetDriverValue(DriverDataObject valueToSet)
		{
			SetDriverValue(valueToSet, true, true);
		}

        public virtual void SetDriverValue(DriverDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DriverDataObject existing_driver = null ;

			if ( !(ObjectsDataSet == null))
			{
				DriverDataObject key;

				if (this._driver_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DriverDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._driver_NewObjectId;			
				}

				existing_driver = (DriverDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_driver ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Driver", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "AlertHistoryDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_driver_NewObjectId != valueToSet.InternalObjectId)
					{
						_driver_NewObjectId = valueToSet.InternalObjectId;
						_driverId = valueToSet.Id;
						OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_driverId != valueToSet.Id)
					{
						_driver_NewObjectId = null;

						_driverId = valueToSet.Id;
						OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_driverId = Guid.Empty;
				OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_driver ,valueToSet))
				OnPropertyChanged("Driver", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __driverSemaphore = new SemaphoreSlim(1, 1);
		private bool __driverAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Driver", which is a DriverDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DriverDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DriverDataObject> LoadDriverAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDriverAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DriverDataObject> LoadDriverAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __driverSemaphore.WaitAsync();
			
	        try
            {
                if (!__driverAlreadyLazyLoaded || forceReload)
                {
								
					DriverDataObject driver = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __driverAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId);
						driver.IsNew = false;
						driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
						if (driver != null)
						{
							return driver;
						}
					}

					driver = await _driverService.GetAsync(_serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId), parameters : parameters, skipSecurity: skipSecurity);

					SetDriverValue(driver, false, false);
					__driverAlreadyLazyLoaded = true;				
		
					driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId);
					driver.IsNew = false;
					driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
                    __driverAlreadyLazyLoaded = true;
                }

                return await GetDriverAsync(false);
            }
            finally
            {
                __driverSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DriverDataObject Driver 
		{
			get
			{			
				return GetDriverAsync(true).Result;
			}
			set
			{
				SetDriverValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDriver()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("AlertHistoryDataObject") && ObjectsDataSet.RelationsToInclude["AlertHistoryDataObject"].Contains("Driver");
		}

		public virtual async Task<DriverDataObject> GetDriverAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DriverDataObject driver;

				
			if (_driver_NewObjectId != null)
			{
				driver = _serviceProvider.GetRequiredService<DriverDataObject>();
				driver.IsNew = true;
				driver.InternalObjectId = _driver_NewObjectId;
				driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
			}
			else
			{
				driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId);
				driver.IsNew = false;
				driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
				
				if (allowLazyLoading && driver == null && LazyLoadingEnabled && (!__driverAlreadyLazyLoaded || forceReload))
				{
					driver = await LoadDriverAsync(forceReload : forceReload);
				}
			}
				
			return driver;
		}

		public virtual System.Guid DriverForeignKey
		{
			get { return DriverId; }
			set 
			{	
				DriverId = value;
			}
			
		}
		

		protected IDataProvider<VehicleDataObject> _vehicleService => _serviceProvider.GetRequiredService<IDataProvider<VehicleDataObject>>();
      public virtual void SetVehicleValue(VehicleDataObject valueToSet)
		{
			SetVehicleValue(valueToSet, true, true);
		}

        public virtual void SetVehicleValue(VehicleDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			VehicleDataObject existing_vehicle = null ;

			if ( !(ObjectsDataSet == null))
			{
				VehicleDataObject key;

				if (this._vehicle_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<VehicleDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._vehicle_NewObjectId;			
				}

				existing_vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_vehicle ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Vehicle", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "AlertHistoryDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_vehicle_NewObjectId != valueToSet.InternalObjectId)
					{
						_vehicle_NewObjectId = valueToSet.InternalObjectId;
						_vehicleId = valueToSet.Id;
						OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_vehicleId != valueToSet.Id)
					{
						_vehicle_NewObjectId = null;

						_vehicleId = valueToSet.Id;
						OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_vehicleId = Guid.Empty;
				OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_vehicle ,valueToSet))
				OnPropertyChanged("Vehicle", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __vehicleSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Vehicle", which is a VehicleDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a VehicleDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<VehicleDataObject> LoadVehicleAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<VehicleDataObject> LoadVehicleAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleAlreadyLazyLoaded || forceReload)
                {
								
					VehicleDataObject vehicle = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __vehicleAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId);
						vehicle.IsNew = false;
						vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
						if (vehicle != null)
						{
							return vehicle;
						}
					}

					vehicle = await _vehicleService.GetAsync(_serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId), parameters : parameters, skipSecurity: skipSecurity);

					SetVehicleValue(vehicle, false, false);
					__vehicleAlreadyLazyLoaded = true;				
		
					vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId);
					vehicle.IsNew = false;
					vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
                    __vehicleAlreadyLazyLoaded = true;
                }

                return await GetVehicleAsync(false);
            }
            finally
            {
                __vehicleSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual VehicleDataObject Vehicle 
		{
			get
			{			
				return GetVehicleAsync(true).Result;
			}
			set
			{
				SetVehicleValue(value);
			}
		}
		
		public virtual bool ShouldSerializeVehicle()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("AlertHistoryDataObject") && ObjectsDataSet.RelationsToInclude["AlertHistoryDataObject"].Contains("Vehicle");
		}

		public virtual async Task<VehicleDataObject> GetVehicleAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			VehicleDataObject vehicle;

				
			if (_vehicle_NewObjectId != null)
			{
				vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
				vehicle.IsNew = true;
				vehicle.InternalObjectId = _vehicle_NewObjectId;
				vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
			}
			else
			{
				vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId);
				vehicle.IsNew = false;
				vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
				
				if (allowLazyLoading && vehicle == null && LazyLoadingEnabled && (!__vehicleAlreadyLazyLoaded || forceReload))
				{
					vehicle = await LoadVehicleAsync(forceReload : forceReload);
				}
			}
				
			return vehicle;
		}

		public virtual System.Guid VehicleForeignKey
		{
			get { return VehicleId; }
			set 
			{	
				VehicleId = value;
			}
			
		}
		

		public override void ClearLazyLoadFlags()
		{
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadAlertAsync()) != null)
				result.Add(Alert);
			if ((await LoadDriverAsync()) != null)
				result.Add(Driver);
			if ((await LoadVehicleAsync()) != null)
				result.Add(Vehicle);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				Alert == other ||
				(other is AlertDataObject && (AlertId != default(System.Guid)) && (AlertId == (other as AlertDataObject).Id)) || 
				Vehicle == other ||
				(other is VehicleDataObject && (VehicleId != default(System.Guid)) && (VehicleId == (other as VehicleDataObject).Id)) || 
				Driver == other ||
				(other is DriverDataObject && (DriverId != default(System.Guid)) && (DriverId == (other as DriverDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetAlertIdValue(System.Guid valueToSet)
		{
			SetAlertIdValue(valueToSet, true, true);
		}

		public virtual void SetAlertIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_alertId != valueToSet)
			{
				_alertId = valueToSet;

				OnPropertyChanged("AlertId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The AlertId property of the AlertHistory DataObject</summary>
        public virtual System.Guid AlertId 
		{
			get	{ return _alertId;}
			
			
			set
			{
				SetAlertIdValue(value);
			}
		}		
			
			
		public virtual void SetCreatedDateTimeValue(System.DateTime valueToSet)
		{
			SetCreatedDateTimeValue(valueToSet, true, true);
		}

		public virtual void SetCreatedDateTimeValue(System.DateTime valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null || valueToSet == DateTime.MinValue) 
			{
				if (_createdDateTime != DateTime.MinValue.ToUniversalTime())
				{
					_createdDateTime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("CreatedDateTime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_createdDateTime != DateTime.MaxValue.ToUniversalTime())
				{
					_createdDateTime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("CreatedDateTime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_createdDateTime != valueToSet ||
                (_createdDateTime != null && ((DateTime)_createdDateTime).Kind == DateTimeKind.Unspecified))
			{
				_createdDateTime = DateTime.SpecifyKind(valueToSet, DateTimeKind.Local);
				OnPropertyChanged("CreatedDateTime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The CreatedDateTime property of the AlertHistory DataObject</summary>
        public virtual System.DateTime CreatedDateTime 
		{
			get	{ return _createdDateTime;}
			
			
			set
			{
				SetCreatedDateTimeValue(value);
			}
		}		
			
			
		public virtual void SetDescriptionValue(System.String valueToSet)
		{
			SetDescriptionValue(valueToSet, true, true);
		}

		public virtual void SetDescriptionValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_description != valueToSet)
			{
				_description = valueToSet;

				OnPropertyChanged("Description", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Description property of the AlertHistory DataObject</summary>
        public virtual System.String Description 
		{
			get	{ return String.IsNullOrEmpty(_description) ? null : _description; }
			
			
			set
			{
				SetDescriptionValue(value);
			}
		}		
			
			
		public virtual void SetDriverIdValue(System.Guid valueToSet)
		{
			SetDriverIdValue(valueToSet, true, true);
		}

		public virtual void SetDriverIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_driverId != valueToSet)
			{
				_driverId = valueToSet;

				OnPropertyChanged("DriverId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DriverId property of the AlertHistory DataObject</summary>
        public virtual System.Guid DriverId 
		{
			get	{ return _driverId;}
			
			
			set
			{
				SetDriverIdValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the AlertHistory DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetIsAcknowledgedValue(System.Boolean valueToSet)
		{
			SetIsAcknowledgedValue(valueToSet, true, true);
		}

		public virtual void SetIsAcknowledgedValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_isAcknowledged != valueToSet)
			{
				_isAcknowledged = valueToSet;

				OnPropertyChanged("IsAcknowledged", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The IsAcknowledged property of the AlertHistory DataObject</summary>
        public virtual System.Boolean IsAcknowledged 
		{
			get	{ return _isAcknowledged;}
			
			
			set
			{
				SetIsAcknowledgedValue(value);
			}
		}		
			
			
		public virtual void SetIsResolvedValue(System.Boolean valueToSet)
		{
			SetIsResolvedValue(valueToSet, true, true);
		}

		public virtual void SetIsResolvedValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_isResolved != valueToSet)
			{
				_isResolved = valueToSet;

				OnPropertyChanged("IsResolved", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The IsResolved property of the AlertHistory DataObject</summary>
        public virtual System.Boolean IsResolved 
		{
			get	{ return _isResolved;}
			
			
			set
			{
				SetIsResolvedValue(value);
			}
		}		
			
			
		public virtual void SetVehicleIdValue(System.Guid valueToSet)
		{
			SetVehicleIdValue(valueToSet, true, true);
		}

		public virtual void SetVehicleIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleId != valueToSet)
			{
				_vehicleId = valueToSet;

				OnPropertyChanged("VehicleId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The VehicleId property of the AlertHistory DataObject</summary>
        public virtual System.Guid VehicleId 
		{
			get	{ return _vehicleId;}
			
			
			set
			{
				SetVehicleIdValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<AlertHistoryDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is AlertHistoryDataObject))
				return false;

			var p = (AlertHistoryDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.AlertId == p.AlertId;
			fieldsComparison &= this.CreatedDateTime == p.CreatedDateTime;
			fieldsComparison &= this.Description == p.Description;
			fieldsComparison &= this.DriverId == p.DriverId;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.IsAcknowledged == p.IsAcknowledged;
			fieldsComparison &= this.IsResolved == p.IsResolved;
			fieldsComparison &= this.VehicleId == p.VehicleId;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// CreatedDateTime is a local datetime: Convert to UTC for server-side handling and storing
			if (this._createdDateTime_WithTimezoneOffset != null)
			{
				this.CreatedDateTime = ((DateTimeOffset)this._createdDateTime_WithTimezoneOffset).UtcDateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class AlertHistoryCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public AlertHistoryCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public AlertHistoryCollectionContainer()
		{
		}
		
		public AlertHistoryCollectionContainer Construct(DataObjectCollection<AlertHistoryDataObject> alertHistoryItems)
        {
            if (alertHistoryItems == null)
                return this;
				
			this.PrimaryKeys = alertHistoryItems.Select(c => c.PrimaryKey).ToList();
            if (alertHistoryItems.ObjectsDataSet == null)
            {
                alertHistoryItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = alertHistoryItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = alertHistoryItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<AlertHistoryDataObject> ExtractAlertHistoryItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<AlertHistoryDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<AlertHistoryDataObject>(typeof(AlertHistoryDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class AlertHistoryContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public AlertHistoryContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual AlertHistoryContainer Construct(AlertHistoryDataObject alertHistory, bool includeDirtyObjectsOnly = false)
		{
            if (alertHistory == null)
                return this;

			this.PrimaryKey = alertHistory.PrimaryKey;
			
            if (alertHistory.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(alertHistory);
            }

			if(alertHistory.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity AlertHistory", "Unable to set a dataset to the entity. Container may not be initialized", "AlertHistoryDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : AlertHistory");
			}

			if(alertHistory.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in AlertHistoryDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "AlertHistoryDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in AlertHistoryDataObject");
			}
			this.InternalObjectId = (int) alertHistory.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? alertHistory.ObjectsDataSet.CloneDirtyObjects() : alertHistory.ObjectsDataSet;

			return this;
		}
		
		public AlertHistoryDataObject ExtractAlertHistory()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<AlertHistoryDataObject>(typeof(AlertHistoryDataObject), InternalObjectId);
			
			return result;
        }	
	}

}