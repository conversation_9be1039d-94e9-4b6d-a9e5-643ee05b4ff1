-- =============================================
-- FleetXQ Bulk Importer - Data Generation Stored Procedures
-- Creates procedures for SQL-based data generation and processing
-- =============================================

-- Create procedure to validate import data
IF OBJECT_ID('[Staging].[ValidateImportData]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[ValidateImportData]
GO

CREATE PROCEDURE [Staging].[ValidateImportData]
    @SessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ValidRows INT = 0;
    DECLARE @InvalidRows INT = 0;
    
    -- Validate driver data
    UPDATE [Staging].[DriverImport] 
    SET [ValidationStatus] = 'Valid',
        [ValidationErrors] = NULL
    WHERE [ImportSessionId] = @SessionId
        AND [PersonFirstName] IS NOT NULL 
        AND [PersonLastName] IS NOT NULL
        AND [CustomerName] IS NOT NULL
        AND [SiteName] IS NOT NULL
        AND [DepartmentName] IS NOT NULL;
    
    -- Mark invalid driver records
    UPDATE [Staging].[DriverImport] 
    SET [ValidationStatus] = 'Invalid',
        [ValidationErrors] = 'Missing required fields: ' + 
            CASE WHEN [PersonFirstName] IS NULL THEN 'FirstName ' ELSE '' END +
            CASE WHEN [PersonLastName] IS NULL THEN 'LastName ' ELSE '' END +
            CASE WHEN [CustomerName] IS NULL THEN 'Customer ' ELSE '' END +
            CASE WHEN [SiteName] IS NULL THEN 'Site ' ELSE '' END +
            CASE WHEN [DepartmentName] IS NULL THEN 'Department ' ELSE '' END
    WHERE [ImportSessionId] = @SessionId
        AND ([PersonFirstName] IS NULL OR [PersonLastName] IS NULL OR 
             [CustomerName] IS NULL OR [SiteName] IS NULL OR [DepartmentName] IS NULL);
    
    -- Validate vehicle data
    UPDATE [Staging].[VehicleImport] 
    SET [ValidationStatus] = 'Valid',
        [ValidationErrors] = NULL
    WHERE [ImportSessionId] = @SessionId
        AND [HireNo] IS NOT NULL 
        AND [SerialNo] IS NOT NULL
        AND [CustomerName] IS NOT NULL
        AND [SiteName] IS NOT NULL
        AND [DepartmentName] IS NOT NULL
        AND [ModelName] IS NOT NULL
        AND [ModuleSerialNumber] IS NOT NULL;
    
    -- Mark invalid vehicle records
    UPDATE [Staging].[VehicleImport] 
    SET [ValidationStatus] = 'Invalid',
        [ValidationErrors] = 'Missing required fields: ' + 
            CASE WHEN [HireNo] IS NULL THEN 'HireNo ' ELSE '' END +
            CASE WHEN [SerialNo] IS NULL THEN 'SerialNo ' ELSE '' END +
            CASE WHEN [CustomerName] IS NULL THEN 'Customer ' ELSE '' END +
            CASE WHEN [SiteName] IS NULL THEN 'Site ' ELSE '' END +
            CASE WHEN [DepartmentName] IS NULL THEN 'Department ' ELSE '' END +
            CASE WHEN [ModelName] IS NULL THEN 'Model ' ELSE '' END +
            CASE WHEN [ModuleSerialNumber] IS NULL THEN 'ModuleSerial ' ELSE '' END
    WHERE [ImportSessionId] = @SessionId
        AND ([HireNo] IS NULL OR [SerialNo] IS NULL OR 
             [CustomerName] IS NULL OR [SiteName] IS NULL OR [DepartmentName] IS NULL OR
             [ModelName] IS NULL OR [ModuleSerialNumber] IS NULL);
    
    -- Get validation counts
    SELECT 
        @ValidRows = SUM(CASE WHEN [ValidationStatus] = 'Valid' THEN 1 ELSE 0 END),
        @InvalidRows = SUM(CASE WHEN [ValidationStatus] = 'Invalid' THEN 1 ELSE 0 END)
    FROM (
        SELECT [ValidationStatus] FROM [Staging].[DriverImport] WHERE [ImportSessionId] = @SessionId
        UNION ALL
        SELECT [ValidationStatus] FROM [Staging].[VehicleImport] WHERE [ImportSessionId] = @SessionId
    ) combined;
    
    -- Return validation summary
    SELECT 
        @ValidRows AS ValidRows,
        @InvalidRows AS InvalidRows;
END
GO

-- Create procedure to process import data (production)
IF OBJECT_ID('[Staging].[ProcessImportData]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[ProcessImportData]
GO

CREATE PROCEDURE [Staging].[ProcessImportData]
    @SessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ProcessedRows INT = 0;
    DECLARE @InsertedRows INT = 0;
    DECLARE @UpdatedRows INT = 0;
    DECLARE @SkippedRows INT = 0;
    
    -- Note: This is a simplified example. In a real implementation,
    -- you would merge the staging data into actual production tables
    -- based on your FleetXQ schema
    
    -- For demonstration, we'll just mark records as processed
    UPDATE [Staging].[DriverImport] 
    SET [ValidationStatus] = 'Processed',
        [ProcessedAt] = GETUTCDATE(),
        [ProcessingAction] = 'Insert'
    WHERE [ImportSessionId] = @SessionId
        AND [ValidationStatus] = 'Valid';
    
    SET @InsertedRows = @@ROWCOUNT;
    
    UPDATE [Staging].[VehicleImport] 
    SET [ValidationStatus] = 'Processed',
        [ProcessedAt] = GETUTCDATE(),
        [ProcessingAction] = 'Insert'
    WHERE [ImportSessionId] = @SessionId
        AND [ValidationStatus] = 'Valid';
    
    SET @InsertedRows = @InsertedRows + @@ROWCOUNT;
    SET @ProcessedRows = @InsertedRows + @UpdatedRows;
    
    -- Return processing summary
    SELECT 
        @ProcessedRows AS ProcessedRows,
        @InsertedRows AS InsertedRows,
        @UpdatedRows AS UpdatedRows,
        @SkippedRows AS SkippedRows;
END
GO

-- Create procedure for dry run processing
IF OBJECT_ID('[Staging].[ProcessImportDataDryRun]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[ProcessImportDataDryRun]
GO

CREATE PROCEDURE [Staging].[ProcessImportDataDryRun]
    @SessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ProcessedRows INT = 0;
    DECLARE @InsertedRows INT = 0;
    DECLARE @UpdatedRows INT = 0;
    DECLARE @SkippedRows INT = 0;
    
    -- Count what would be processed without actually doing it
    SELECT 
        @InsertedRows = COUNT(*)
    FROM [Staging].[DriverImport] 
    WHERE [ImportSessionId] = @SessionId
        AND [ValidationStatus] = 'Valid';
    
    SELECT 
        @InsertedRows = @InsertedRows + COUNT(*)
    FROM [Staging].[VehicleImport] 
    WHERE [ImportSessionId] = @SessionId
        AND [ValidationStatus] = 'Valid';
    
    SET @ProcessedRows = @InsertedRows + @UpdatedRows;
    
    -- Return dry run summary
    SELECT 
        @ProcessedRows AS ProcessedRows,
        @InsertedRows AS InsertedRows,
        @UpdatedRows AS UpdatedRows,
        @SkippedRows AS SkippedRows;
END
GO

PRINT 'Created data generation and processing stored procedures'
