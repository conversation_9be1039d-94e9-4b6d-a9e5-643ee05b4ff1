(function (global) {
    FleetXQ.Web.Application.TranslationSourceHandlerCustom = function () {
        var self = this;

        // Global namespaces required for all pages/forms - mapped from globalRequiredSources
        this.globalRequiredNamespaces = [
        ];

        // Element-specific namespaces - mapped from requiredSourcesByElement
        this.requiredNamespacesByElement = {
            "PersonDetailPagePage-Page": [
                'entities/PersonToDepartmentVehicleNormalAccessView/filters/PersonToDepartmentVehicleNormalAccessViewFilter',
                'entities/PersonToDepartmentVehicleMasterAccessView/filters/PersonToDepartmentVehicleMasterAccessViewFilter',
                'entities/PersonToModelVehicleNormalAccessView/filters/PersonToModelVehicleNormalAccessViewFilter',
                'entities/PersonToModelVehicleMasterAccessView/filters/PersonToModelVehicleMasterAccessViewFilter',
                'entities/PersonToSiteVehicleNormalAccessView/filters/PersonToSiteVehicleNormalAccessViewFilter',
                'entities/PersonToSiteVehicleMasterAccessView/filters/PersonToSiteVehicleMasterAccessViewFilter',
                'entities/PersonToPerVehicleNormalAccessView/filters/PersonToPerVehicleNormalAccessViewFilter',
                'entities/PersonToPerVehicleMasterAccessView/filters/PersonToPerVehicleMasterAccessViewFilter'
            ]
        };
    };
}()); 