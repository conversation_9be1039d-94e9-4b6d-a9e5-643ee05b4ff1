﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;

using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DeleteHandlers
{
	public partial class GOUserDeleteHandler : DeleteHandlerBase<GOUserDataObject>
	{
		
        public GOUserDeleteHandler(IServiceProvider serviceProvider, IDataProvider<GOUserDataObject> tentityDataProvider, ITransactionProvider transactionProvider, IEntityDataProvider entityDataProvider) : base(serviceProvider, tentityDataProvider, transactionProvider, entityDataProvider)
        {
        }

		public override async Task RippleDeleteAsync(GOUserDataObject instance, Parameters parameters, DataProviderDeleteSettings settings)
		{
			// Set resync flag initially so that if any processing is done, it's on the latest copy of the data
			NeedResync = true;

			// GOUser.AlertSubscriptionItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadAlertSubscriptionItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.AlertSubscriptionItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// GOUser.CustomerAuditItemsDeleted (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadCustomerAuditItemsDeletedAsync(parameters, skipSecurity: true);
				foreach (var item in instance.CustomerAuditItemsDeleted)			
				{					
					if (item.GOUserWhoDeletedThisCustomer != null)
					{	
						item.GOUserWhoDeletedThisCustomer = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// GOUser.CustomerAuditItemsModified (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadCustomerAuditItemsModifiedAsync(parameters, skipSecurity: true);
				foreach (var item in instance.CustomerAuditItemsModified)			
				{					
					if (item.GOUserWhoModifiedThisCustomer != null)
					{	
						item.GOUserWhoModifiedThisCustomer = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// GOUser.CustomerAuditItemsCreated (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadCustomerAuditItemsCreatedAsync(parameters, skipSecurity: true);
				foreach (var item in instance.CustomerAuditItemsCreated)			
				{					
					if (item.GOUserWhoCreatedThisCustomer != null)
					{	
						item.GOUserWhoCreatedThisCustomer = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// GOUser.DealerDriver (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadDealerDriverAsync(parameters, skipSecurity: true);
				var item = instance.DealerDriver;
				
 
				// Cascade
				if (item != null)
				{
					await DeleteAsync(item, parameters, settings, instance);
				}
			}
			// GOUser.ExportJobStatusItems (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadExportJobStatusItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.ExportJobStatusItems)			
				{					
					if (item.GOUser != null)
					{	
						item.GOUser = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// GOUser ripple to sub-type GOUser2FA (Hierarchy)
			{
				if (instance is GOUser2FADataObject)
				{
					var rippler = _serviceProvider.GetRequiredService<DeleteHandlerBase<GOUser2FADataObject>>() as DeleteHandlerBase<GOUser2FADataObject>;
					rippler.Init(parameters);
					await rippler.RippleDeleteAsync(instance as GOUser2FADataObject, parameters, settings);
					// Propagate deferred delete items back up the hierarchy (else they'll get missed)
					AddDeferredDeleteItems(rippler.DeferredDeleteItems);
				}
			}
			// GOUser.GOUserDepartmentItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadGOUserDepartmentItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.GOUserDepartmentItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// GOUser.UserGroupItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadUserGroupItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.UserGroupItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// GOUser.UserRoleItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadUserRoleItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.UserRoleItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// GOUser.GoUserToCustomerItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadGoUserToCustomerItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.GoUserToCustomerItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// GOUser.MessageHistoryItems (Protected) (i.e. Unable to delete GOUser instances because MessageHistoryItems.GOUser is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadMessageHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.MessageHistoryItems);
			}
			// GOUser.Person (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadPersonAsync(parameters, skipSecurity: true);
				
				// We are the PK side entity in a OneToOne relationship, and the FK reference to us is optional.
				// So delete can proceed, but first we need to clear down the FK reference to us.
				var item = instance.Person;				
				if (item != null && item.GOUser != null)
				{
					item.GOUser = null; 
					await SaveAsync(item);
				}
			}
			// GOUser.ReportSubscriptionItems (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadReportSubscriptionItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.ReportSubscriptionItems)			
				{					
					if (item.GOUser != null)
					{	
						item.GOUser = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// GOUser.RevisionItems (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadRevisionItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.RevisionItems)			
				{					
					if (item.GOUser != null)
					{	
						item.GOUser = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// GOUser.Tag (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadTagAsync(parameters, skipSecurity: true);
				
				// We are the PK side entity in a OneToOne relationship, and the FK reference to us is optional.
				// So delete can proceed, but first we need to clear down the FK reference to us.
				var item = instance.Tag;				
				if (item != null && item.GOUser != null)
				{
					item.GOUser = null; 
					await SaveAsync(item);
				}
			}
			// GOUser.VehicleLockoutItems (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleLockoutItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.VehicleLockoutItems)			
				{					
					if (item.GOUser != null)
					{	
						item.GOUser = null; 	
						await SaveAsync(item);							
					}
				}
			}
		}
	}
}