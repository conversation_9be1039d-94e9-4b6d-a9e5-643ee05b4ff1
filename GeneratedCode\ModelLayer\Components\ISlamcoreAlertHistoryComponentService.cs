﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.Client.Model.Components
{
    public interface ISlamcoreAlertHistoryComponentService
    {
        /// <summary>
        /// ExportAsync component operation
        /// 
        /// </summary>
        /// <param name="filterPredicate"></param>
        /// <param name="filterParameters"></param>
        /// <returns>ExportJobStatus</returns>
        Task<ExportJobStatusDataObject> ExportAsync(System.String filterPredicate, System.String filterParameters);
    }
}
