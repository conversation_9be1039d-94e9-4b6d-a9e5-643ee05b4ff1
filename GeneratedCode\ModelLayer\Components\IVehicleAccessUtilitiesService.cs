﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.Client.Model.Components
{
    public interface IVehicleAccessUtilitiesService
    {
        /// <summary>
        /// CreateOnDemandAccessesAsync component operation
        /// 
        /// </summary>
        /// <param name="cardIds"></param>
        /// <param name="vehicleId"></param>
        /// <returns>PerVehicleNormalCardAccess Collection</returns>
        Task<DataObjectCollection<PerVehicleNormalCardAccessDataObject>> CreateOnDemandAccessesAsync(System.Guid[] cardIds, System.Guid vehicleId);
        /// <summary>
        /// DeleteOnDemandAccessAsync component operation
        /// 
        /// </summary>
        /// <param name="accessId"></param>
        /// <returns>bool</returns>
        Task<System.Boolean> DeleteOnDemandAccessAsync(System.Guid accessId);
        /// <summary>
        /// UpdateVehicleModelAccessesForPersonAsync component operation
        /// Update the model vehicle accesses for a person. 
        /// </summary>
        /// <param name="updateModelAccesses"></param>
        /// <param name="personId"></param>
        /// <returns>bool</returns>
        Task<System.Boolean> UpdateVehicleModelAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updateModelAccesses);
        /// <summary>
        /// CopyUserVehicleAccessAsync component operation
        /// Copy User Vehicle Access
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="driverIds">driverIds</param>
        /// <returns>bool</returns>
        Task<System.Boolean> CopyUserVehicleAccessAsync(System.Guid personId, System.Guid[] driverIds);
        /// <summary>
        /// UpdateVehicleDepartmentAccessesForPersonAsync component operation
        /// Update the department vehicle accesses for a person. 
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="updatedPersonToDepartmentAccesses"></param>
        /// <returns>bool</returns>
        Task<System.Boolean> UpdateVehicleDepartmentAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updatedPersonToDepartmentAccesses);
        /// <summary>
        /// GetAccessesForDepartmentsAsync component operation
        /// 
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="personToSiteAccesses"></param>
        /// <param name="permissionLevel"></param>
        /// <returns>PersonToDepartmentVehicleNormalAccessView Collection</returns>
        Task<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>> GetAccessesForDepartmentsAsync(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses, System.Guid personId, System.Int32 permissionLevel);
        /// <summary>
        /// UpdateVehicleSiteAccessesForPersonAsync component operation
        /// Update the sites vehicle accesses for a person. 
        /// </summary>
        /// <param name="updatedPersonToSiteAccesses"></param>
        /// <param name="personId"></param>
        /// <returns>bool</returns>
        Task<System.Boolean> UpdateVehicleSiteAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updatedPersonToSiteAccesses);
        /// <summary>
        /// GetAccessesForVehiclesAsync component operation
        /// 
        /// </summary>
        /// <param name="personToModelAccesses"></param>
        /// <param name="personToDepartmentAccesses"></param>
        /// <param name="personId"></param>
        /// <param name="permissionLevel"></param>
        /// <returns>PersonToPerVehicleNormalAccessView Collection</returns>
        Task<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>> GetAccessesForVehiclesAsync(DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses, System.Guid personId, System.Int32 permissionLevel);
        /// <summary>
        /// UpdateVehiclePerVehicleAccessesForPersonAsync component operation
        /// Update the per vehicle accesses for a person.
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="updatePerVehicleAccesses"></param>
        /// <returns>bool</returns>
        Task<System.Boolean> UpdateVehiclePerVehicleAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updatePerVehicleAccesses);
        /// <summary>
        /// GetAccessesForModelsAsync component operation
        /// 
        /// </summary>
        /// <param name="personToDepartmentAccesses"></param>
        /// <param name="personId"></param>
        /// <param name="permissionLevel"></param>
        /// <returns>PersonToModelVehicleNormalAccessView Collection</returns>
        Task<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>> GetAccessesForModelsAsync(DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, System.Guid personId, System.Int32 permissionLevel);
        /// <summary>
        /// UpdateAccessesForPersonAsync component operation
        /// 
        /// </summary>
        /// <param name="PermissionLevel"></param>
        /// <param name="personToSiteAccesses"></param>
        /// <param name="personId"></param>
        /// <param name="personToModelAccesses"></param>
        /// <param name="cascadeAddPermission">true for cascading add permissions. For example : if I add permission to department, cascading will add permission for all models and vehicles in the departments added</param>
        /// <param name="personToDepartmentAccesses"></param>
        /// <param name="personToVehicleAccesses"></param>
        /// <returns>bool</returns>
        Task<System.Boolean> UpdateAccessesForPersonAsync(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses, DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses, DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses, System.Guid personId, System.Int32 PermissionLevel, System.Boolean cascadeAddPermission);
    }
}
