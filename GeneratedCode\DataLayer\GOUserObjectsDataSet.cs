﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class GOUserObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _gOUserObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all GOUser objects for current dataset
		private ConcurrentDictionary< int, GOUserDataObject> _gOUserObjects = new ConcurrentDictionary< int, GOUserDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<GOUserDataObject> _mergedDataObjects;

		private ConcurrentQueue<GOUserDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<GOUserDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> GOUserObjectInternalIds
		{ 
			get { return _gOUserObjectInternalIds; }
			set { _gOUserObjectInternalIds = value; }
		}
		
		// Collection holding all GOUser objects for current dataset
		[JsonProperty("GOUserObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, GOUserDataObject> GOUserObjects
		{ 
			get { return _gOUserObjects; }
			set { _gOUserObjects = value; }
		}
		
		
 
		
 
		
 
		
 
		// Index to quickly find all GOUser with a given dealer foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Dealer_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
 
		// Index to quickly find all GOUser with a given gORole foreign key
		public ConcurrentDictionary<System.String, List<int>> GORole_FKIndex = new ConcurrentDictionary<System.String, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public GOUserObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public GOUserObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<GOUserObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.GOUserObjects)
			{
                var cloneObject = (GOUserDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.GOUserObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.GOUserObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.GOUserObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			foreach(var fkKeyValue in this.Dealer_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "GOUserObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Dealer_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Dealer_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.GORole_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.GORole_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.GORole_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<GOUserObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.GOUserObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (GOUserDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.GOUserObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.GOUserObjectInternalIds
				.Where(o => this.GOUserObjects[o.Value].IsDirty || this.GOUserObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.GOUserObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var gOUser in GOUserObjects.Values)
			{
				yield return gOUser; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the GOUserObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "GOUserObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = GOUserObjects.TryAdd(newInternalId, (GOUserDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (GOUserObjectInternalIds.ContainsKey(((GOUserDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = GOUserObjectInternalIds.TryRemove(((GOUserDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = GOUserObjectInternalIds.TryAdd(((GOUserDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as GOUserDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "GOUserDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

	 
	 
	 
	 
			// Update the Dealer FK Index 
			if ((objectToAdd as GOUserDataObject).DealerId != null)
			{
				if (!Dealer_FKIndex.ContainsKey((System.Guid)(objectToAdd as GOUserDataObject).DealerId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Dealer_FKIndex.TryAdd((System.Guid)(objectToAdd as GOUserDataObject).DealerId, new List<int>());
					}
				}
				
				if (!Dealer_FKIndex[(System.Guid)(objectToAdd as GOUserDataObject).DealerId].Contains(newInternalId))	
					Dealer_FKIndex[(System.Guid)(objectToAdd as GOUserDataObject).DealerId].Add(newInternalId);

	            DealerDataObject relatedDealer;
	            if ((objectToAdd as GOUserDataObject)._dealer_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<DealerDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as GOUserDataObject)._dealer_NewObjectId;

	                relatedDealer = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedDealer = _rootObjectDataSet.GetObject(new DealerDataObject((System.Guid)(objectToAdd as GOUserDataObject).DealerId) { IsNew = false });
	            }

	            if (relatedDealer != null && this.RootObjectDataSet.NotifyChanges)
	                relatedDealer.NotifyPropertyChanged("GOUserItems", new SeenObjectCollection());
			}
			
	 
	 
	 
			// Update the GORole FK Index 
			if ((objectToAdd as GOUserDataObject).GORoleName != null)
			{
				if (!GORole_FKIndex.ContainsKey((System.String)(objectToAdd as GOUserDataObject).GORoleName))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = GORole_FKIndex.TryAdd((System.String)(objectToAdd as GOUserDataObject).GORoleName, new List<int>());
					}
				}
				
				if (!GORole_FKIndex[(System.String)(objectToAdd as GOUserDataObject).GORoleName].Contains(newInternalId))	
					GORole_FKIndex[(System.String)(objectToAdd as GOUserDataObject).GORoleName].Add(newInternalId);

	            GORoleDataObject relatedGORole;
	            if ((objectToAdd as GOUserDataObject)._gORole_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<GORoleDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as GOUserDataObject)._gORole_NewObjectId;

	                relatedGORole = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedGORole = _rootObjectDataSet.GetObject(new GORoleDataObject((System.String)(objectToAdd as GOUserDataObject).GORoleName) { IsNew = false });
	            }

	            if (relatedGORole != null && this.RootObjectDataSet.NotifyChanges)
	                relatedGORole.NotifyPropertyChanged("GOUserItems", new SeenObjectCollection());
			}
			
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (GOUserObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as GOUserDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "GOUserObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = GOUserObjectInternalIds.ContainsKey((objectToRemove as GOUserDataObject).PrimaryKey) ? (int?) GOUserObjectInternalIds[(objectToRemove as GOUserDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				GOUserDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = GOUserObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = GOUserObjectInternalIds.TryRemove((objectToRemove as GOUserDataObject).PrimaryKey, out idvalue);
					}
				}
				
		 
		 
		 
		 
			// Delete the Dealer FK Index 
				if ((objectToRemove as GOUserDataObject).DealerId != null)
				{
					if (Dealer_FKIndex.ContainsKey((System.Guid)(objectToRemove as GOUserDataObject).DealerId) && Dealer_FKIndex[(System.Guid)(objectToRemove as GOUserDataObject).DealerId].Contains((int)objectToRemoveInternalId))
					{
						Dealer_FKIndex[(System.Guid)(objectToRemove as GOUserDataObject).DealerId].Remove((int)objectToRemoveInternalId);

						if (!Dealer_FKIndex[(System.Guid)(objectToRemove as GOUserDataObject).DealerId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Dealer_FKIndex.TryRemove((System.Guid)(objectToRemove as GOUserDataObject).DealerId, out outvalue);
							}
						}
					}

					DealerDataObject relatedDealer;
		            if ((objectToRemove as GOUserDataObject)._dealer_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<DealerDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as GOUserDataObject)._dealer_NewObjectId;

						relatedDealer = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedDealer = _rootObjectDataSet.GetObject(new DealerDataObject((System.Guid)(objectToRemove as GOUserDataObject).DealerId) { IsNew = false });
		            }

		            if (relatedDealer != null && this.RootObjectDataSet.NotifyChanges)
		                relatedDealer.NotifyPropertyChanged("GOUserItems", new SeenObjectCollection());
					
				}			
		 
		 
		 
			// Delete the GORole FK Index 
				if ((objectToRemove as GOUserDataObject).GORoleName != null)
				{
					if (GORole_FKIndex.ContainsKey((System.String)(objectToRemove as GOUserDataObject).GORoleName) && GORole_FKIndex[(System.String)(objectToRemove as GOUserDataObject).GORoleName].Contains((int)objectToRemoveInternalId))
					{
						GORole_FKIndex[(System.String)(objectToRemove as GOUserDataObject).GORoleName].Remove((int)objectToRemoveInternalId);

						if (!GORole_FKIndex[(System.String)(objectToRemove as GOUserDataObject).GORoleName].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = GORole_FKIndex.TryRemove((System.String)(objectToRemove as GOUserDataObject).GORoleName, out outvalue);
							}
						}
					}

					GORoleDataObject relatedGORole;
		            if ((objectToRemove as GOUserDataObject)._gORole_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<GORoleDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as GOUserDataObject)._gORole_NewObjectId;

						relatedGORole = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedGORole = _rootObjectDataSet.GetObject(new GORoleDataObject((System.String)(objectToRemove as GOUserDataObject).GORoleName) { IsNew = false });
		            }

		            if (relatedGORole != null && this.RootObjectDataSet.NotifyChanges)
		                relatedGORole.NotifyPropertyChanged("GOUserItems", new SeenObjectCollection());
					
				}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return GOUserObjects.ContainsKey(internalObjectId) ? GOUserObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as GOUserDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "GOUserObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = GOUserObjectInternalIds.ContainsKey((objectToGet as GOUserDataObject).PrimaryKey) ? (int?) GOUserObjectInternalIds[(objectToGet as GOUserDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return GOUserObjects.ContainsKey((int)objectToGetInternalId) ? GOUserObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return GOUserObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return GOUserObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		 
		 
		 
		 
		
		public IEnumerable<GOUserDataObject> GetGOUserItemsForDealer(DealerDataObject dealerInstance) 
		{
			if (dealerInstance.IsNew)
            {
			
              return GOUserObjects.Where(o => o.Value._dealer_NewObjectId != null && o.Value._dealer_NewObjectId == dealerInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Dealer_FKIndex.ContainsKey(dealerInstance.Id))
			{
				return Dealer_FKIndex[dealerInstance.Id].Where(e => GOUserObjects.ContainsKey(e)).Select(e => GOUserObjects[e]);
			}
			
			return new DataObjectCollection<GOUserDataObject>();
		}
		 
		 
		 
		
		public IEnumerable<GOUserDataObject> GetGOUserItemsForGORole(GORoleDataObject gORoleInstance) 
		{
			if (gORoleInstance.IsNew)
            {
			
              return GOUserObjects.Where(o => o.Value._gORole_NewObjectId != null && o.Value._gORole_NewObjectId == gORoleInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (GORole_FKIndex.ContainsKey(gORoleInstance.Name))
			{
				return GORole_FKIndex[gORoleInstance.Name].Where(e => GOUserObjects.ContainsKey(e)).Select(e => GOUserObjects[e]);
			}
			
			return new DataObjectCollection<GOUserDataObject>();
		}
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
			if (relationName == "AlertSubscriptionItems")
            {
				IEnumerable< AlertSubscriptionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.AlertSubscriptionObjectsDataSet.GetAlertSubscriptionItemsForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerAuditItemsCreated")
            {
				IEnumerable< CustomerAuditDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerAuditObjectsDataSet.GetCustomerAuditItemsCreatedForGOUserWhoCreatedThisCustomer(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerAuditItemsDeleted")
            {
				IEnumerable< CustomerAuditDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerAuditObjectsDataSet.GetCustomerAuditItemsDeletedForGOUserWhoDeletedThisCustomer(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerAuditItemsModified")
            {
				IEnumerable< CustomerAuditDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerAuditObjectsDataSet.GetCustomerAuditItemsModifiedForGOUserWhoModifiedThisCustomer(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "DealerDriver")
            {
				IEnumerable< DealerDriverDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DealerDriverObjectsDataSet.GetDealerDriverForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ExportJobStatusItems")
            {
				IEnumerable< ExportJobStatusDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ExportJobStatusObjectsDataSet.GetExportJobStatusItemsForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "GOUserDepartmentItems")
            {
				IEnumerable< GOUserDepartmentDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GOUserDepartmentObjectsDataSet.GetGOUserDepartmentItemsForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "GoUserToCustomerItems")
            {
				IEnumerable< GoUserToCustomerDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GoUserToCustomerObjectsDataSet.GetGoUserToCustomerItemsForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "MessageHistoryItems")
            {
				IEnumerable< MessageHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.MessageHistoryObjectsDataSet.GetMessageHistoryItemsForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "Person")
            {
				IEnumerable< PersonDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonObjectsDataSet.GetPersonForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ReportSubscriptionItems")
            {
				IEnumerable< ReportSubscriptionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ReportSubscriptionObjectsDataSet.GetReportSubscriptionItemsForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "RevisionItems")
            {
				IEnumerable< RevisionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.RevisionObjectsDataSet.GetRevisionItemsForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "Tag")
            {
				IEnumerable< TagDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TagObjectsDataSet.GetTagForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "UserGroupItems")
            {
				IEnumerable< GOUserGroupDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GOUserGroupObjectsDataSet.GetUserGroupItemsForUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "UserRoleItems")
            {
				IEnumerable< GOUserRoleDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GOUserRoleObjectsDataSet.GetUserRoleItemsForUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleLockoutItems")
            {
				IEnumerable< VehicleLockoutDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleLockoutObjectsDataSet.GetVehicleLockoutItemsForGOUser(rootObject as GOUserDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var GOUserDataSet = dataSetToMerge as GOUserObjectsDataSet;
				if(GOUserDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in GOUserDataSet.GOUserObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "GOUserObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as GOUserDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
		 
		 
		 
		 
			// Reconstruct the Dealer FK Index 
			Dealer_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in GOUserObjects.Values)
			{
				if (item.DealerId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.DealerId;	

				if (!Dealer_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Dealer_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "GOUserObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Dealer_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
			// Reconstruct the GORole FK Index 
			GORole_FKIndex = new ConcurrentDictionary< System.String, List<int>>();
				
			foreach (var item in GOUserObjects.Values)
			{
				if (item.GORoleName == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.GORoleName;	

				if (!GORole_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = GORole_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "GOUserObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				GORole_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (GOUserObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}