﻿{
  "entityName": "VehicleSlamcoreLocationHistory",
  "entityNamePlural": "VehicleSlamcoreLocationHistories",   "entityDescription": "This entity represents a VehicleSlamcoreLocationHistory",
  "fields": {
    "AcquisitionDateTime": {
        "displayName": "AcquisitionDateTime", 
        "description": "AcquisitionDateTime"
    },
    "Bearing": {
        "displayName": "Bearing", 
        "description": "Bearing", 
        "validationRules": {
            "fc4f514a-ff62-4319-ac9f-43aae356822b" : {
                    "errorMessage": "Value must be a number for the field Bearing"
            }
        }
    },
    "EventType": {
        "displayName": "EventType", 
        "description": "EventType"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "ReferenceFrameCategory": {
        "displayName": "ReferenceFrameCategory", 
        "description": "ReferenceFrameCategory"
    },
    "SlamcoreDevice": {
        "displayName": "SlamcoreDevice", 
        "description": "SlamcoreDevice"
    },
    "SlamcoreDeviceId": {
        "displayName": "SlamcoreDeviceId", 
        "description": "Foreign Key"
    },
    "Speed": {
        "displayName": "Speed", 
        "description": "Speed", 
        "validationRules": {
            "61b91bf9-e6a5-44fd-b63f-b40b423a0860" : {
                    "errorMessage": "Value must be a number for the field Speed"
            }
        }
    },
    "Status": {
        "displayName": "Status", 
        "description": "Status"
    },
    "TrailSequence": {
        "displayName": "TrailSequence", 
        "description": "TrailSequence", 
        "validationRules": {
            "1334ff42-d576-47a3-ba70-f1802459f2af" : {
                    "errorMessage": "Value must be a number for the field TrailSequence"
            }
        }
    },
    "WOrientation": {
        "displayName": "WOrientation", 
        "description": "WOrientation", 
        "validationRules": {
            "2dc861f1-b032-4def-a0ed-790b88d6821a" : {
                    "errorMessage": "Value must be a number for the field WOrientation"
            }
        }
    },
    "XOrientation": {
        "displayName": "XOrientation", 
        "description": "XOrientation", 
        "validationRules": {
            "951604b4-f332-4a78-ac4c-eef77bed305c" : {
                    "errorMessage": "Value must be a number for the field XOrientation"
            }
        }
    },
    "XPosition": {
        "displayName": "XPosition", 
        "description": "XPosition", 
        "validationRules": {
            "6cf733c8-3862-4b42-95f9-5e6d4b3916cf" : {
                    "errorMessage": "Value must be a number for the field XPosition"
            }
        }
    },
    "YOrientation": {
        "displayName": "YOrientation", 
        "description": "YOrientation", 
        "validationRules": {
            "41001fb7-35fe-46ae-bc11-c91ca3561218" : {
                    "errorMessage": "Value must be a number for the field YOrientation"
            }
        }
    },
    "YPosition": {
        "displayName": "YPosition", 
        "description": "YPosition", 
        "validationRules": {
            "83e5985f-d430-4d7e-a004-725f71d3c8c9" : {
                    "errorMessage": "Value must be a number for the field YPosition"
            }
        }
    },
    "ZPosition": {
        "displayName": "ZPosition", 
        "description": "ZPosition", 
        "validationRules": {
            "ea78f224-cee3-4368-b03a-ff150026b64a" : {
                    "errorMessage": "Value must be a number for the field ZPosition"
            }
        }
    }
  }
} 