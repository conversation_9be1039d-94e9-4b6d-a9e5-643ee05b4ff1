﻿{
  "entityName": "GOUser",
  "entityNamePlural": "GOUsers",   "entityDescription": "Generative Objects Membership User",
  "fields": {
    "AlertSubscriptionItems": {
        "displayName": "Alert Subscription Items", 
        "description": "Alert Subscription"
    },
    "AllowedDepartmentNames": {
        "displayName": "AllowedDepartmentNames", 
        "description": "AllowedDepartmentNames"
    },
    "Blocked": {
        "displayName": "Blocked", 
        "description": "Blocked"
    },
    "CustomerAuditItemsCreated": {
        "displayName": "CustomerAuditItemsCreated", 
        "description": "CustomerAuditItemsCreated"
    },
    "CustomerAuditItemsDeleted": {
        "displayName": "CustomerAuditItemsDeleted", 
        "description": "CustomerAuditItemsDeleted"
    },
    "CustomerAuditItemsModified": {
        "displayName": "CustomerAuditItemsModified", 
        "description": "CustomerAuditItemsModified"
    },
    "Dealer": {
        "displayName": "Dealer", 
        "description": "Dealer"
    },
    "DealerAdmin": {
        "displayName": "Dealer Admin", 
        "description": "Dealer Admin"
    },
    "DealerDriver": {
        "displayName": "Dealer Driver", 
        "description": "Dealer Driver"
    },
    "DealerId": {
        "displayName": "DealerId", 
        "description": "Foreign Key"
    },
    "EmailAddress": {
        "displayName": "Email", 
        "description": "Email Address"
    },
    "EmailChangeValidationInProgress": {
        "displayName": "Email change validation in progress", 
        "description": "Email change validation in progress"
    },
    "EmailValidated": {
        "displayName": "Email Verified?", 
        "description": "User has verified their email address?"
    },
    "ExportJobStatusItems": {
        "displayName": "GO Export Job Status Items", 
        "description": "GO Export Job Status Items"
    },
    "ExternalUserId": {
        "displayName": "ExternalUserId", 
        "description": "ExternalUserId"
    },
    "FirstName": {
        "displayName": "First Name", 
        "description": "First Name"
    },
    "FullName": {
        "displayName": "Full Name", 
        "description": "Full Name"
    },
    "GORole": {
        "displayName": "GORole", 
        "description": "GORole"
    },
    "GORoleName": {
        "displayName": "GORoleName", 
        "description": "Foreign Key"
    },
    "GOUserDepartmentItems": {
        "displayName": "GOUserDepartment Items", 
        "description": "GOUserDepartment"
    },
    "GoUserToCustomerItems": {
        "displayName": "Go User To Customer Items", 
        "description": "Alert Items"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Primary Key"
    },
    "LastName": {
        "displayName": "Surname", 
        "description": "Surname"
    },
    "MessageHistoryItems": {
        "displayName": "Message History Items", 
        "description": "Message History Items"
    },
    "NewEmailAddress": {
        "displayName": "New Email", 
        "description": "New Email Address"
    },
    "NewEmailValidated": {
        "displayName": "New Email Verified?", 
        "description": "User has verified their new email address?"
    },
    "Password": {
        "displayName": "Password", 
        "description": "Password"
    },
    "PasswordExpiry": {
        "displayName": "Password Expiry", 
        "description": "When password expires"
    },
    "Person": {
        "displayName": "Person", 
        "description": "Person"
    },
    "PreferredLocale": {
        "displayName": "PreferredLocale", 
        "description": "PreferredLocale"
    },
    "PreferredLocaleString": {
        "displayName": "PreferredLocaleString", 
        "description": "PreferredLocale"
    },
    "ReportSubscriptionItems": {
        "displayName": "Report Subscription Items", 
        "description": "Report Subscription Items"
    },
    "RevisionItems": {
        "displayName": "RevisionItems", 
        "description": "RevisionItems"
    },
    "Tag": {
        "displayName": "Tag", 
        "description": "Tag"
    },
    "Unregistered": {
        "displayName": "Unregistered", 
        "description": "Unregistered"
    },
    "UserGroupItems": {
        "displayName": "UserGroupItems", 
        "description": "UserGroupItems"
    },
    "UserName": {
        "displayName": "User Name", 
        "description": "User Name"
    },
    "UserRoleItems": {
        "displayName": "UserRoleItems", 
        "description": "UserRoleItems"
    },
    "UserValidated": {
        "displayName": "Admin Approved?", 
        "description": "User has been approved by an Administrator?"
    },
    "VehicleLockoutItems": {
        "displayName": "Vehicle Lockout Items", 
        "description": "Vehicle Lockout Items"
    },
    "WebsiteAccessLevel": {
        "displayName": "Website Access Level", 
        "description": "Website Access Level"
    },
    "WebsiteAccessLevelValue": {
        "displayName": "WebsiteAccessLevelValue", 
        "description": "WebsiteAccessLevel", 
        "validationRules": {
            "62062df3-8ab2-4872-8e33-1174a7aa6471" : {
                    "errorMessage": "Value must be a number for the field WebsiteAccessLevelValue"
            }
        }
    }
  }
} 