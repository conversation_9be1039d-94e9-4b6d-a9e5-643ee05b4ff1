﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Storage.Common;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using System.IO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Primitives;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.CsvImportExport;
using FleetXQ.BusinessLayer.Components.Server.Import;
using FleetXQ.BusinessLayer.Tasks;
using Parameters = System.Collections.Generic.Dictionary<string, object>;

namespace FleetXQ.BusinessLayer.Components.Server
{
    public partial interface ISlamcoreVehicleTelemetryExportSection0Component    
	{
		System.Threading.Tasks.Task<bool> ExportAsync(Task<ExportJobStatusDataObject> task, IExportWriter exportWriter, string exportFilePath, string exportFileName, string exportFileInternalName, string filterPredicate, string filterParameters, int startProgress = 0, int endProgress = 100);
	}
    public partial class SlamcoreVehicleTelemetryExportSection0Component : ImportExportComponent<VehicleSlamcoreLocationHistoryDataObject>, ISlamcoreVehicleTelemetryExportSection0Component
    {

		public SlamcoreVehicleTelemetryExportSection0Component(IServiceProvider provider, IConfiguration configuration, IApiFilterArgumentBuilder apiFilterArgumentBuilder) : base(provider, configuration)
		{
			_apiFilterArgumentBuilder = apiFilterArgumentBuilder;	
		}
		
		private readonly IApiFilterArgumentBuilder _apiFilterArgumentBuilder;	

		protected IDataFacade _dataFacade => _serviceProvider.GetRequiredService<IDataFacade>();
		protected IDataImporter _dataImporter => _serviceProvider.GetRequiredService<IDataImporter>();

        private static int _linesToSkip = 1;

 
        private static string _dateTimeFormat = "dd/MM/yyyy";
        
        public const string COL_DEVICENAME = "Device Name";
        public const string COL_IPADDRESS = "IP Address";
        public const string COL_SERIALNO = "Serial No";
        public const string COL_UPDATERATE = "Update Rate";
        public const string COL_LASTCONNECTEDDATETIME = "Last Connected DateTime";
        public const string COL_SPEED = "Speed";
        public const string COL_BEARING = "Bearing";


		public async System.Threading.Tasks.Task<bool> ExportAsync(
			Task<ExportJobStatusDataObject> task, 
			IExportWriter exportWriter, 
			string exportFilePath, 
			string exportFileName,
			string exportFileInternalName,
			string filterPredicate,
			string filterParameters,
			int startProgress = 0, 
			int endProgress = 100)
        {
            EnsureInitialized();
			var exportJob = task.DataObject;
			int count = 0;
            
			string exportLogFileName = $"exportlog-{exportJob.Id}.txt";
            string exportLogFileInternalName = $"exports/{Guid.NewGuid()}.txt";
			string storageContainer = _configuration["StorageContainer"] == StringValues.Empty ? "files" : _configuration["StorageContainer"];
			string exportLogPath = $"{_serviceProvider.GetRequiredService<IServicePath>().ProjectBaseURL().Trim('/')}/{storageContainer}/{exportLogFileInternalName}";

            var outputLog = new StringBuilder();
            var parameters = new Dictionary<string, object>();

			ValueWrapper<string> filterPredicateWrapper = new ValueWrapper<string>(filterPredicate);
            ValueWrapper<string> filterParametersWrapper = new ValueWrapper<string>(filterParameters);

            try
            {
                outputLog.AppendLine($"{DateTime.UtcNow} : Start Exporting data");

				int configuredExportBatchSize;
				double numberOfBatches;

				using (var outputStream = await _dataImporter.GetStorageProvider().GetFileStream(exportFilePath, FileMode.Create))
				{
					using (var writer = new CsvFileWriter(outputStream))
					{
					writer.RowSeparator = ',';

					try
					{
						await FireOnBeforeExportAsync(writer, exportFilePath, filterPredicateWrapper, filterParametersWrapper, outputLog, parameters);

						filterPredicate = filterPredicateWrapper.Value;
						filterParameters = filterParametersWrapper.Value;
					}
					catch(Exception e)
					{
						outputLog.AppendLine($"Error while calling before export : {e.Message}");
					}

					var deserializedApiFilterArguments = _apiFilterArgumentBuilder.DeserializeApiFilterArguments(filterParameters);

					count = await _dataFacade.VehicleSlamcoreLocationHistoryDataProvider.CountAsync(null, filterPredicate, deserializedApiFilterArguments, null);

					configuredExportBatchSize = _configuration["ExportBatchSize"] == StringValues.Empty ? 1000 : Convert.ToInt32(_configuration["ExportBatchSize"]);
					numberOfBatches = Math.Ceiling(((double)count) / configuredExportBatchSize);

					var row = new DataRow();
					row.AddColumn(1, "Device Name");
					row.AddColumn(2, "IP Address");
					row.AddColumn(3, "Serial No");
					row.AddColumn(4, "Update Rate");
					row.AddColumn(5, "Last Connected DateTime");
					row.AddColumn(6, "Speed");
					row.AddColumn(7, "Bearing");
 
                    // First line => headers
					row[1] = "Device Name";
					row[2] = "IP Address";
					row[3] = "Serial No";
					row[4] = "Update Rate";
					row[5] = "Last Connected DateTime";
					row[6] = "Speed";
					row[7] = "Bearing";
 
                    writer.WriteRow(row);
					int totalLinesProcessed = 0;

                    for (int i = 0; i < numberOfBatches; i++)
                    {
                        var entities = await _dataFacade.VehicleSlamcoreLocationHistoryDataProvider.GetCollectionAsync(
							filterPredicate: filterPredicate, 
							filterArguments: deserializedApiFilterArguments,
							pageNumber: i + 1, 
							pageSize : configuredExportBatchSize, 
							parameters: new Parameters { { "ImportContext", true } }
						   ,includes: new List<string> 
							{
								"SlamcoreDevice",
							}
						);

						int lineCount = 0;

                        // Now contents
                        foreach (var entity in entities)
                        {
							totalLinesProcessed++;
							lineCount++;

							short percentCompleted = (short) (((float)totalLinesProcessed / (float)count) * (endProgress - startProgress) + startProgress);

							if (percentCompleted != exportJob.Progress)
							{
								exportJob.Progress = percentCompleted;
								exportJob = await task.SaveProgressAsync(percentCompleted);
							}

                            row.Clean();
                            var beforeDataRowEvent = await FireOnBeforeExportDataRowAsync(writer, exportFilePath, outputLog, row, entity);
	                        if (beforeDataRowEvent.SkipRow) continue;
	                        if (beforeDataRowEvent.ErrorRaised) continue;

							
							// Row 1 => Device Name 
							if (entity.SlamcoreDevice != null)
							{
								row[1] = (entity.SlamcoreDevice.Name.ToString());
							} 
							
							// Row 2 => IP Address 
							if (entity.SlamcoreDevice != null)
							{
								row[2] = (entity.SlamcoreDevice.IPAddress.ToString());
							} 
							
							// Row 3 => Serial No 
							if (entity.SlamcoreDevice != null)
							{
								row[3] = (entity.SlamcoreDevice.SerialNo.ToString());
							} 
							
							// Row 4 => Update Rate 
							if (entity.SlamcoreDevice != null)
							{
								row[4] = (entity.SlamcoreDevice.UpdateRateDisplay.ToString());
							} 
							
							// Row 5 => Last Connected DateTime 
							if (entity.SlamcoreDevice != null)
							{
 								row[5] = (entity.SlamcoreDevice.LastConnectedDateTime == null ? "" : ((System.DateTime)entity.SlamcoreDevice.LastConnectedDateTime).ToString(_dateTimeFormat));
							} 
							
							// Row 6 => Speed 
							{
								row[6] = (entity.Speed == null ? "" : ((System.Decimal)entity.Speed).ToString());
							}
							
							// Row 7 => Bearing 
							{
								row[7] = (entity.Bearing == null ? "" : ((System.Decimal)entity.Bearing).ToString());
							}
 
	                        var afterDataRowEvent = await FireOnAfterExportDataRowAsync(writer, exportFilePath, outputLog, row, entity);
	                        if (afterDataRowEvent.SkipRow) continue;
	                        if (afterDataRowEvent.ErrorRaised) continue;
	                        
                            writer.WriteRow(row);
                        }
                    }
					try
					{
						await FireOnAfterExportAsync(writer, exportFilePath, outputLog, parameters);
					}
					catch(Exception e)
					{
						outputLog.AppendLine($"Error while calling after export : {e.Message}");
					}
                }
					exportJob.ExportedFile = exportFileName;
					exportJob.ExportedFileInternalName = exportFileInternalName;
					// not supported by azure storage.. find another way
					//exportJob.ExportedFileFileSize =(int) outputStream.Length;
				}
              outputLog.AppendLine($"{DateTime.UtcNow} : End Exporting Demandes - {count} Demandes exported");
            }
            catch (Exception e)
            {
                outputLog.AppendLine($"Error while exporting demandes : {e.Message} ");
                if (exportJob != null)
                    exportJob.TaskStatus = GOTaskStatusEnum.Failed;

				return false;
            }
            finally
            {
				using (StreamWriter logfile = new StreamWriter(await _dataImporter.GetStorageProvider().GetFileStream(exportLogPath, FileMode.Create)))
                {
                    logfile.Write(outputLog.ToString());
                    logfile.Flush();
					exportJob.ExportLogFile = exportLogFileName;
					exportJob.ExportLogFileInternalName = exportLogFileInternalName;
					// not supported by azure storage.. find another way
                    //exportJob.ExportLogFileFileSize = (int) logfile.BaseStream.Length;
                }
			}
		
			return true;
		}	
	}
 
	public partial class SlamcoreVehicleTelemetryExportComponent : BaseServerComponent, ISlamcoreVehicleTelemetryExportComponent 
	{
		public SlamcoreVehicleTelemetryExportComponent(IServiceProvider provider, IConfiguration configuration, IDataFacade dataFacade) : base(provider, configuration, dataFacade)
		{
		}

		protected IDataImporter _dataImporter => _serviceProvider.GetRequiredService<IDataImporter>();
 
        public System.Threading.Tasks.Task Export_OnInitTaskAsync(ExportJobStatusDataObject taskObject, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
			return System.Threading.Tasks.Task.CompletedTask;
        }
	
		/// Generated asynchronous version of the API
		public async System.Threading.Tasks.Task ExportAsync(Task<ExportJobStatusDataObject> task, System.String filterPredicate, System.String filterParameters, Dictionary<string, object> parameters = null)
		{	
			int startProgress;
			int endProgress;

			var exportJob = task.DataObject;

			string exportFileName = String.Format("SlamcoreVehicleTelemetryExportFile-{0}.csv", exportJob.Id);
            string exportFileInternalName = $"exports/{Guid.NewGuid()}.csv";
			string storageContainer = _configuration["StorageContainer"] == StringValues.Empty ? "files" : _configuration["StorageContainer"];
            string exportFilePath = $"{_serviceProvider.GetRequiredService<IServicePath>().ProjectBaseURL().Trim('/')}/{storageContainer}/{exportFileInternalName}";

			{
				
				exportFileInternalName = $"exports/{Guid.NewGuid()}.csv";
				storageContainer = _configuration["StorageContainer"] == StringValues.Empty ? "files" : _configuration["StorageContainer"];
				exportFilePath = $"{_serviceProvider.GetRequiredService<IServicePath>().ProjectBaseURL().Trim('/')}/{storageContainer}/{exportFileInternalName}";
				startProgress = 0;
				endProgress = 100;
				var section0ExportComponent = _serviceProvider.GetRequiredService<ISlamcoreVehicleTelemetryExportSection0Component>();
	            await section0ExportComponent.ExportAsync(task, null, exportFilePath, exportFileName, exportFileInternalName, filterPredicate, filterParameters, startProgress, endProgress);
			
			}

            exportJob = task.DataObject;
			exportJob.End = DateTime.UtcNow;

			// GRD-462 Don't overwrite fail export result with success
			if (exportJob.TaskStatus != GOTaskStatusEnum.Failed)
			{
				exportJob.TaskStatus = GOTaskStatusEnum.Complete;
				exportJob.Progress = 100;
			}

			exportJob = await task.SaveProgressAsync(100);       
		}
	}
}
