﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

(function () {
	// 
	FleetXQ.Web.Controllers.AlertReportPageController = function(applicationController) {
		var self = this;
		this.subscriptions = [];
		// store all subscriptions in this array, to unsubscribe on release.
		
		this._objectType = "AlertReportPage";

		this.applicationController = applicationController;
		this.ObjectsDataSet = applicationController.ObjectsDataSet;
		this.contextId = [this.applicationController.getNextContextId()];
		this.customController = undefined;

		// Integrate custom code if any
		if (FleetXQ.Web.Controllers.AlertReportPageControllerCustom !== undefined) {
		    this.customController = new FleetXQ.Web.Controllers.AlertReportPageControllerCustom(self);
		};

		// Initialize View Models and Data Stores
		
			this.AlertHistoryGridViewModel = new FleetXQ.Web.ViewModels.AlertHistoryGridViewModel(this, $("#AlertHistoryGrid"), null, null, this.contextId);		
		this.AlertHistoryGridViewModel.StatusData.ShowTitle(true);		
		this.AlertHistoryGridViewModel.include = "auto-include-id-bc390d44-6b2a-4c2c-9bce-0953f6c11656-8a9a971e-459c-4b6e-b538-823acc074e95";	
		this.customPageTitle = ko.observable(null);

		this.iconToUse = ko.observable(null);

		this.pageTitle = ko.pureComputed (function() {
			if (self.customPageTitle()) {
				return self.customPageTitle();
			}
			return "Alert Report";
		});

		this.pageTitleParams = ko.observable({});
		
		this.showGoBackBadge = ko.observable(false);
		this.goBackBadgeLabel = ko.observable("pages/AlertReportPage:navigation.BackNavigationBadge");
		this.showItemCountBadge = ko.observable(false);
		this.isLoading = ko.observable(false);
		this.itemCount = ko.pureComputed(function() {
			return null;
		}); 
		this.goBack = function () {
			if(self.IsInEditMode && self.IsInEditMode()) {
				if(!confirm(FleetXQ.Web.Messages.i18n.t('messages.unsavedChanges')))
					return;
				
				// Cancel edit mode on current page controller
				if (self.CancelEdit) {
					self.CancelEdit(false);
				}					
			}

};

		this.subscriptions.push(this.AlertHistoryGridViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnAlertHistoryGridViewModelIsBusyChanged(newValue); }));
		
		this.IsInEditMode = function() {

			return  (self.AlertHistoryGridViewModel.StatusData.DisplayMode && self.AlertHistoryGridViewModel.StatusData.DisplayMode() == 'edit');
		};

		this.CancelEdit = function(isCommandCall) {
			if (self.AlertHistoryGridViewModel.CancelEdit) {
				self.AlertHistoryGridViewModel.CancelEdit(isCommandCall);
			}
		};

		// Events Handlers
		this.OnAlertHistoryGridViewModelIsBusyChanged = function (newValue) {
		};

		this.initialize = function() {
			if (FleetXQ.Web.Controllers.Custom && FleetXQ.Web.Controllers.Custom.getPageTitle) {
				self.customPageTitle(FleetXQ.Web.Controllers.Custom.getPageTitle(self));
			}

	
			// Call custom initialize if defined
			if (self.customController !== undefined && self.customController.initialize !== undefined) {
			    self.customController.initialize();
			}
		};
		this.initialize();



		this.release = function() {
			// unsubscribe
			for(var i = 0;i <  self.subscriptions.length;i++) {
				self.subscriptions[i].dispose();
			}
			self.subscriptions = [];
			self.ObjectsDataSet.cleanContext(self.contextId);
			
			self.AlertHistoryGridViewModel.release();
			self.AlertHistoryGridViewModel = null;
		};


		return self;
	};		
	
	if (window.ApplicationSourceHandler)
		window.ApplicationSourceHandler.onSourceLoaded("/Controllers/AlertReportPageController.js");
} ());
