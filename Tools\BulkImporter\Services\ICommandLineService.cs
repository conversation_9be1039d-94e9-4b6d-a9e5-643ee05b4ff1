namespace FleetXQ.Tools.BulkImporter.Services;

/// <summary>
/// Service for parsing command line arguments
/// </summary>
public interface ICommandLineService
{
    /// <summary>
    /// Parses command line arguments into import options
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Parsed import options</returns>
    ImportOptions ParseArguments(string[] args);

    /// <summary>
    /// Displays help information
    /// </summary>
    void ShowHelp();
}

/// <summary>
/// Command line service implementation
/// </summary>
public class CommandLineService : ICommandLineService
{
    public ImportOptions ParseArguments(string[] args)
    {
        var options = new ImportOptions();

        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i].ToLowerInvariant())
            {
                case "--drivers":
                case "-d":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out var drivers))
                    {
                        options.DriversCount = drivers;
                        i++;
                    }
                    break;

                case "--vehicles":
                case "-v":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out var vehicles))
                    {
                        options.VehiclesCount = vehicles;
                        i++;
                    }
                    break;

                case "--batch-size":
                case "-b":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out var batchSize))
                    {
                        options.BatchSize = batchSize;
                        i++;
                    }
                    break;

                case "--dry-run":
                    options.DryRun = true;
                    break;

                case "--generate":
                case "-g":
                    options.GenerateData = true;
                    break;

                case "--non-interactive":
                case "-n":
                    options.Interactive = false;
                    break;

                case "--help":
                case "-h":
                case "-?":
                    ShowHelp();
                    Environment.Exit(0);
                    break;

                default:
                    // Ignore unknown arguments that don't start with '-'
                    if (!args[i].StartsWith('-'))
                    {
                        Console.WriteLine($"Warning: Ignoring unknown argument '{args[i]}'. CSV file input is no longer supported.");
                    }
                    break;
            }
        }

        return options;
    }

    public void ShowHelp()
    {
        Console.WriteLine();
        Console.WriteLine("FleetXQ Bulk Importer");
        Console.WriteLine("Performs efficient bulk insertion of driver and vehicle data into the FleetXQ database.");
        Console.WriteLine();
        Console.WriteLine("Usage:");
        Console.WriteLine("  BulkImporter [options] [input-files]");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("  -d, --drivers <count>       Number of drivers to process (default: from config)");
        Console.WriteLine("  -v, --vehicles <count>      Number of vehicles to process (default: from config)");
        Console.WriteLine("  -b, --batch-size <size>     Batch size for bulk operations (default: from config)");
        Console.WriteLine("  --dry-run                   Validate input without making changes");
        Console.WriteLine("  -g, --generate              Generate synthetic data instead of reading files");
        Console.WriteLine("  -n, --non-interactive       Run without interactive prompts");
        Console.WriteLine("  -h, --help                  Show this help message");
        Console.WriteLine();
        Console.WriteLine("Examples:");
        Console.WriteLine("  BulkImporter --drivers 1000 --vehicles 500");
        Console.WriteLine("  BulkImporter --generate --dry-run");
        Console.WriteLine("  BulkImporter --drivers 1000 --vehicles 500");
        Console.WriteLine("  BulkImporter --non-interactive --batch-size 5000");
        Console.WriteLine();
    }
}