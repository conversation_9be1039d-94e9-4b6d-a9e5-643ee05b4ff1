# FleetXQ Tools Directory - Architecture Documentation

## Overview

This document provides comprehensive documentation for the FleetXQ Tools directory architecture, specifically focusing on the BulkImporter application. The architecture has been modernized to eliminate CSV file dependencies in favor of a pure SQL-based data generation approach.

## Diagram Files

### 1. Main Architecture Diagram
**File:** `fleet-xq-tools-architecture.mermaid`

This comprehensive flowchart visualizes the complete workflow and architecture of the FleetXQ BulkImporter tool, including:

- **Application Startup Flow**: From Program.Main() through configuration loading and service initialization
- **Mode Decision Paths**: Interactive vs non-interactive execution modes
- **Data Generation Workflow**: SQL-based data generation replacing CSV file processing
- **Validation and Processing**: Staging table validation and production data processing
- **Error Handling**: Comprehensive error handling and logging throughout the process
- **Service Layer Interactions**: Dependencies between different services and components
- **External Dependencies**: Third-party packages and their integration points

### 2. Database Schema Diagram
**File:** `fleet-xq-tools-database-schema.mermaid`

This Entity-Relationship diagram shows:

- **Staging Tables**: ImportSession, DriverImport, VehicleImport with all fields and relationships
- **Production Tables**: Simplified FleetXQ schema showing Person, Driver, Vehicle, Customer, Site, Department
- **Data Flow Diagrams**: Generation → Staging → Validation → Processing → Production
- **SQL Script Dependencies**: Order of execution for database setup scripts
- **Stored Procedures**: Validation and processing procedures

## Architecture Components

### Core Services

1. **BulkImportService**
   - Main orchestration service
   - Coordinates the entire import workflow
   - Manages session creation and lifecycle

2. **SqlDataGenerationService**
   - Pure SQL-based data generation
   - Replaces CSV file processing
   - Batch processing for memory efficiency
   - Direct insertion into staging tables

3. **CommandLineService**
   - Parses command line arguments
   - Validates input parameters
   - Provides help and usage information

4. **InteractiveService**
   - Handles user prompts and confirmations
   - Provides guided setup for import operations
   - User-friendly parameter collection

5. **BulkImportHostedService**
   - Application lifecycle management
   - Entry point coordination
   - Result display and exit code handling

### Configuration Management

- **BulkImporterOptions**: Core application settings (batch sizes, timeouts, etc.)
- **DataGenerationOptions**: SQL generation settings (random seed, memory limits)
- **ConnectionStringOptions**: Database connection management
- **Environment Variable Support**: Runtime configuration overrides

### Logging and Monitoring

- **Correlation Context**: Session and operation ID tracking
- **Structured Logging**: Serilog with console and file output
- **Performance Metrics**: Throughput, timing, and progress tracking
- **Error Categorization**: Standardized error handling and reporting

### Database Architecture

#### Staging Schema
- **ImportSession**: Tracks each import operation with metrics and status
- **DriverImport**: Staging table for driver data with validation status
- **VehicleImport**: Staging table for vehicle data with validation status

#### Stored Procedures
- **ValidateImportData**: Field validation and status updates
- **ProcessImportData**: Production processing and data merging
- **ProcessImportDataDryRun**: Simulation mode for testing

## Workflow Execution Paths

### Interactive Mode (Default)
1. Application starts and loads configuration
2. Parses command line arguments
3. Prompts user for missing parameters:
   - Number of drivers to process
   - Number of vehicles to process
   - Batch size for operations
   - Data source selection
   - Dry run confirmation
4. User confirms operation summary
5. Executes import workflow
6. Displays results and exits

### Non-Interactive Mode
1. Application starts and loads configuration
2. Parses and validates all command line arguments
3. Applies default values for missing parameters
4. Executes import workflow directly
5. Displays results and exits

### SQL Data Generation Workflow
1. Creates import session with unique ID
2. Generates driver data in batches (if requested)
   - Uses complex SQL CTEs for realistic data
   - Batch size controlled for memory management
   - Progress logging and metrics
3. Generates vehicle data in batches (if requested)
   - Similar SQL generation approach
   - Realistic vehicle configurations
4. Validates all staged data
   - Field validation and null checks
   - Updates validation status
   - Identifies invalid records
5. Processes data to production (or dry-run simulation)
   - Merges valid records to production tables
   - Updates processing status and timestamps
   - Comprehensive result reporting

## Key Features

### SQL-Based Architecture Benefits
- **No File Dependencies**: Eliminates CSV file management complexity
- **Memory Efficiency**: Batch processing with configurable sizes
- **Database-Native Operations**: Leverages SQL Server performance
- **Transactional Safety**: Full ACID compliance for data operations
- **Reproducible Results**: Configurable random seed for consistent test data

### Error Handling and Reliability
- **Retry Policies**: Configurable retry for transient failures (Polly integration)
- **Comprehensive Logging**: Full operation traceability with correlation IDs
- **Graceful Degradation**: Continues processing despite individual row failures
- **Validation First**: Pre-validates all data before production processing
- **Dry Run Mode**: Safe testing without database modifications

### Performance and Scalability
- **Configurable Batch Sizes**: Memory and performance optimization
- **SQL Bulk Operations**: High-throughput data processing
- **Session Tracking**: Detailed metrics and progress monitoring
- **Timeout Management**: Configurable timeouts for long-running operations

## Usage Examples

### Basic Operations
```bash
# Interactive mode (default)
BulkImporter

# Generate 1000 drivers and 500 vehicles
BulkImporter --drivers 1000 --vehicles 500 --non-interactive

# Dry run testing
BulkImporter --generate --dry-run

# Custom batch size
BulkImporter --batch-size 5000 --non-interactive
```

### Configuration Overrides
```bash
# Environment variable override
FLEETXQ_BULKIMPORTER_BulkImporter__DefaultBatchSize=5000 BulkImporter

# Large scale processing
BulkImporter --drivers 10000 --vehicles 5000 --batch-size 2000 --non-interactive
```

## Migration Notes

### CSV to SQL Migration
The architecture has been completely migrated from CSV file-based processing to pure SQL-based data generation:

- **Removed**: CsvHelper package dependency
- **Eliminated**: All file I/O operations
- **Replaced**: InputFileOptions with DataGenerationOptions
- **Enhanced**: Error handling and validation
- **Improved**: Performance and memory efficiency

### Backward Compatibility
- **Command Line Interface**: All existing flags and options preserved
- **Functionality**: Complete feature parity maintained
- **Configuration**: Enhanced with new SQL-specific options
- **Error Handling**: Improved reliability and error reporting

## Development and Maintenance

### Adding New Features
1. Update configuration models in `Configuration/`
2. Implement business logic in `Services/`
3. Add appropriate logging and error handling
4. Update SQL scripts if database changes needed
5. Update documentation and diagrams

### Testing Recommendations
- Use `--dry-run` mode for safe testing
- Test with various batch sizes and data volumes
- Validate error handling with invalid configurations
- Monitor logs for performance and reliability metrics

### Database Prerequisites
- SQL Server with FleetXQ database
- Staging schema and tables (created by SQL scripts)
- Stored procedures for validation and processing
- Appropriate database permissions for bulk operations

## External Dependencies

- **Microsoft.Data.SqlClient**: Database connectivity
- **Serilog**: Structured logging framework
- **Polly**: Retry policies and resilience
- **Microsoft.Extensions.Hosting**: Application hosting framework
- **Microsoft.Extensions.Configuration**: Configuration management
- **Microsoft.Extensions.DependencyInjection**: Dependency injection container

This architecture provides a robust, scalable, and maintainable solution for bulk data operations in the FleetXQ system, with comprehensive monitoring, error handling, and performance optimization.
