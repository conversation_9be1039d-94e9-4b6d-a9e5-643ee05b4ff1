﻿{
  "entityName": "SlamcoreAuthenticationDetails",
  "entityNamePlural": "SlamcoreAuthenticationDetailses",   "entityDescription": "This entity represents a SlamcoreAuthenticationDetails",
  "fields": {
    "APIKey": {
        "displayName": "APIKey", 
        "description": "APIKey"
    },
    "APIKeyDisplay": {
        "displayName": "APIKeyDisplay", 
        "description": "APIKeyDisplay"
    },
    "CreatedDateTime": {
        "displayName": "CreatedDateTime", 
        "description": "CreatedDateTime"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "SlamcoreDevice": {
        "displayName": "SlamcoreDevice", 
        "description": "SlamcoreDevice"
    }
  }
} 