﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class VehicleLockoutObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _vehicleLockoutObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all VehicleLockout objects for current dataset
		private ConcurrentDictionary< int, VehicleLockoutDataObject> _vehicleLockoutObjects = new ConcurrentDictionary< int, VehicleLockoutDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<VehicleLockoutDataObject> _mergedDataObjects;

		private ConcurrentQueue<VehicleLockoutDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<VehicleLockoutDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> VehicleLockoutObjectInternalIds
		{ 
			get { return _vehicleLockoutObjectInternalIds; }
			set { _vehicleLockoutObjectInternalIds = value; }
		}
		
		// Collection holding all VehicleLockout objects for current dataset
		[JsonProperty("VehicleLockoutObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, VehicleLockoutDataObject> VehicleLockoutObjects
		{ 
			get { return _vehicleLockoutObjects; }
			set { _vehicleLockoutObjects = value; }
		}
		
		
 
		// Index to quickly find all VehicleLockout with a given driver foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Driver_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		// Index to quickly find all VehicleLockout with a given gOUser foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> GOUser_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		// Index to quickly find all VehicleLockout with a given session foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Session_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		// Index to quickly find all VehicleLockout with a given vehicle foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Vehicle_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public VehicleLockoutObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public VehicleLockoutObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<VehicleLockoutObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.VehicleLockoutObjects)
			{
                var cloneObject = (VehicleLockoutDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.VehicleLockoutObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.VehicleLockoutObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.VehicleLockoutObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			foreach(var fkKeyValue in this.Driver_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleLockoutObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Driver_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Driver_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.GOUser_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleLockoutObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.GOUser_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.GOUser_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Session_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleLockoutObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Session_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Session_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Vehicle_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "VehicleLockoutObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Vehicle_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Vehicle_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<VehicleLockoutObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.VehicleLockoutObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (VehicleLockoutDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.VehicleLockoutObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.VehicleLockoutObjectInternalIds
				.Where(o => this.VehicleLockoutObjects[o.Value].IsDirty || this.VehicleLockoutObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.VehicleLockoutObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var vehicleLockout in VehicleLockoutObjects.Values)
			{
				yield return vehicleLockout; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the VehicleLockoutObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "VehicleLockoutObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = VehicleLockoutObjects.TryAdd(newInternalId, (VehicleLockoutDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (VehicleLockoutObjectInternalIds.ContainsKey(((VehicleLockoutDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = VehicleLockoutObjectInternalIds.TryRemove(((VehicleLockoutDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = VehicleLockoutObjectInternalIds.TryAdd(((VehicleLockoutDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as VehicleLockoutDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "VehicleLockoutDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

	 
			// Update the Driver FK Index 
			if ((objectToAdd as VehicleLockoutDataObject).DriverId != null)
			{
				if (!Driver_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleLockoutDataObject).DriverId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Driver_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleLockoutDataObject).DriverId, new List<int>());
					}
				}
				
				if (!Driver_FKIndex[(System.Guid)(objectToAdd as VehicleLockoutDataObject).DriverId].Contains(newInternalId))	
					Driver_FKIndex[(System.Guid)(objectToAdd as VehicleLockoutDataObject).DriverId].Add(newInternalId);

	            DriverDataObject relatedDriver;
	            if ((objectToAdd as VehicleLockoutDataObject)._driver_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<DriverDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleLockoutDataObject)._driver_NewObjectId;

	                relatedDriver = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedDriver = _rootObjectDataSet.GetObject(new DriverDataObject((System.Guid)(objectToAdd as VehicleLockoutDataObject).DriverId) { IsNew = false });
	            }

	            if (relatedDriver != null && this.RootObjectDataSet.NotifyChanges)
	                relatedDriver.NotifyPropertyChanged("VehicleLockouts", new SeenObjectCollection());
			}
			
	 
			// Update the GOUser FK Index 
			if ((objectToAdd as VehicleLockoutDataObject).GOUserId != null)
			{
				if (!GOUser_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleLockoutDataObject).GOUserId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = GOUser_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleLockoutDataObject).GOUserId, new List<int>());
					}
				}
				
				if (!GOUser_FKIndex[(System.Guid)(objectToAdd as VehicleLockoutDataObject).GOUserId].Contains(newInternalId))	
					GOUser_FKIndex[(System.Guid)(objectToAdd as VehicleLockoutDataObject).GOUserId].Add(newInternalId);

	            GOUserDataObject relatedGOUser;
	            if ((objectToAdd as VehicleLockoutDataObject)._gOUser_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<GOUserDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleLockoutDataObject)._gOUser_NewObjectId;

	                relatedGOUser = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedGOUser = _rootObjectDataSet.GetObject(new GOUserDataObject((System.Guid)(objectToAdd as VehicleLockoutDataObject).GOUserId) { IsNew = false });
	            }

	            if (relatedGOUser != null && this.RootObjectDataSet.NotifyChanges)
	                relatedGOUser.NotifyPropertyChanged("VehicleLockoutItems", new SeenObjectCollection());
			}
			
	 
			// Update the Session FK Index 
			if ((objectToAdd as VehicleLockoutDataObject).SessionId != null)
			{
				if (!Session_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleLockoutDataObject).SessionId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Session_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleLockoutDataObject).SessionId, new List<int>());
					}
				}
				
				if (!Session_FKIndex[(System.Guid)(objectToAdd as VehicleLockoutDataObject).SessionId].Contains(newInternalId))	
					Session_FKIndex[(System.Guid)(objectToAdd as VehicleLockoutDataObject).SessionId].Add(newInternalId);

	            SessionDataObject relatedSession;
	            if ((objectToAdd as VehicleLockoutDataObject)._session_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<SessionDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleLockoutDataObject)._session_NewObjectId;

	                relatedSession = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedSession = _rootObjectDataSet.GetObject(new SessionDataObject((System.Guid)(objectToAdd as VehicleLockoutDataObject).SessionId) { IsNew = false });
	            }

	            if (relatedSession != null && this.RootObjectDataSet.NotifyChanges)
	                relatedSession.NotifyPropertyChanged("VehicleLockouts", new SeenObjectCollection());
			}
			
	 
			// Update the Vehicle FK Index 
			if ((objectToAdd as VehicleLockoutDataObject).VehicleId != null)
			{
				if (!Vehicle_FKIndex.ContainsKey((System.Guid)(objectToAdd as VehicleLockoutDataObject).VehicleId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Vehicle_FKIndex.TryAdd((System.Guid)(objectToAdd as VehicleLockoutDataObject).VehicleId, new List<int>());
					}
				}
				
				if (!Vehicle_FKIndex[(System.Guid)(objectToAdd as VehicleLockoutDataObject).VehicleId].Contains(newInternalId))	
					Vehicle_FKIndex[(System.Guid)(objectToAdd as VehicleLockoutDataObject).VehicleId].Add(newInternalId);

	            VehicleDataObject relatedVehicle;
	            if ((objectToAdd as VehicleLockoutDataObject)._vehicle_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<VehicleDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as VehicleLockoutDataObject)._vehicle_NewObjectId;

	                relatedVehicle = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedVehicle = _rootObjectDataSet.GetObject(new VehicleDataObject((System.Guid)(objectToAdd as VehicleLockoutDataObject).VehicleId) { IsNew = false });
	            }

	            if (relatedVehicle != null && this.RootObjectDataSet.NotifyChanges)
	                relatedVehicle.NotifyPropertyChanged("VehicleLockoutItems", new SeenObjectCollection());
			}
			
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (VehicleLockoutObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as VehicleLockoutDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "VehicleLockoutObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = VehicleLockoutObjectInternalIds.ContainsKey((objectToRemove as VehicleLockoutDataObject).PrimaryKey) ? (int?) VehicleLockoutObjectInternalIds[(objectToRemove as VehicleLockoutDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				VehicleLockoutDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = VehicleLockoutObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = VehicleLockoutObjectInternalIds.TryRemove((objectToRemove as VehicleLockoutDataObject).PrimaryKey, out idvalue);
					}
				}
				
		 
			// Delete the Driver FK Index 
				if ((objectToRemove as VehicleLockoutDataObject).DriverId != null)
				{
					if (Driver_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleLockoutDataObject).DriverId) && Driver_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).DriverId].Contains((int)objectToRemoveInternalId))
					{
						Driver_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).DriverId].Remove((int)objectToRemoveInternalId);

						if (!Driver_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).DriverId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Driver_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleLockoutDataObject).DriverId, out outvalue);
							}
						}
					}

					DriverDataObject relatedDriver;
		            if ((objectToRemove as VehicleLockoutDataObject)._driver_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<DriverDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleLockoutDataObject)._driver_NewObjectId;

						relatedDriver = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedDriver = _rootObjectDataSet.GetObject(new DriverDataObject((System.Guid)(objectToRemove as VehicleLockoutDataObject).DriverId) { IsNew = false });
		            }

		            if (relatedDriver != null && this.RootObjectDataSet.NotifyChanges)
		                relatedDriver.NotifyPropertyChanged("VehicleLockouts", new SeenObjectCollection());
					
				}			
		 
			// Delete the GOUser FK Index 
				if ((objectToRemove as VehicleLockoutDataObject).GOUserId != null)
				{
					if (GOUser_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleLockoutDataObject).GOUserId) && GOUser_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).GOUserId].Contains((int)objectToRemoveInternalId))
					{
						GOUser_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).GOUserId].Remove((int)objectToRemoveInternalId);

						if (!GOUser_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).GOUserId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = GOUser_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleLockoutDataObject).GOUserId, out outvalue);
							}
						}
					}

					GOUserDataObject relatedGOUser;
		            if ((objectToRemove as VehicleLockoutDataObject)._gOUser_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<GOUserDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleLockoutDataObject)._gOUser_NewObjectId;

						relatedGOUser = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedGOUser = _rootObjectDataSet.GetObject(new GOUserDataObject((System.Guid)(objectToRemove as VehicleLockoutDataObject).GOUserId) { IsNew = false });
		            }

		            if (relatedGOUser != null && this.RootObjectDataSet.NotifyChanges)
		                relatedGOUser.NotifyPropertyChanged("VehicleLockoutItems", new SeenObjectCollection());
					
				}			
		 
			// Delete the Session FK Index 
				if ((objectToRemove as VehicleLockoutDataObject).SessionId != null)
				{
					if (Session_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleLockoutDataObject).SessionId) && Session_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).SessionId].Contains((int)objectToRemoveInternalId))
					{
						Session_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).SessionId].Remove((int)objectToRemoveInternalId);

						if (!Session_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).SessionId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Session_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleLockoutDataObject).SessionId, out outvalue);
							}
						}
					}

					SessionDataObject relatedSession;
		            if ((objectToRemove as VehicleLockoutDataObject)._session_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<SessionDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleLockoutDataObject)._session_NewObjectId;

						relatedSession = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedSession = _rootObjectDataSet.GetObject(new SessionDataObject((System.Guid)(objectToRemove as VehicleLockoutDataObject).SessionId) { IsNew = false });
		            }

		            if (relatedSession != null && this.RootObjectDataSet.NotifyChanges)
		                relatedSession.NotifyPropertyChanged("VehicleLockouts", new SeenObjectCollection());
					
				}			
		 
			// Delete the Vehicle FK Index 
				if ((objectToRemove as VehicleLockoutDataObject).VehicleId != null)
				{
					if (Vehicle_FKIndex.ContainsKey((System.Guid)(objectToRemove as VehicleLockoutDataObject).VehicleId) && Vehicle_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).VehicleId].Contains((int)objectToRemoveInternalId))
					{
						Vehicle_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).VehicleId].Remove((int)objectToRemoveInternalId);

						if (!Vehicle_FKIndex[(System.Guid)(objectToRemove as VehicleLockoutDataObject).VehicleId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Vehicle_FKIndex.TryRemove((System.Guid)(objectToRemove as VehicleLockoutDataObject).VehicleId, out outvalue);
							}
						}
					}

					VehicleDataObject relatedVehicle;
		            if ((objectToRemove as VehicleLockoutDataObject)._vehicle_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<VehicleDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as VehicleLockoutDataObject)._vehicle_NewObjectId;

						relatedVehicle = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedVehicle = _rootObjectDataSet.GetObject(new VehicleDataObject((System.Guid)(objectToRemove as VehicleLockoutDataObject).VehicleId) { IsNew = false });
		            }

		            if (relatedVehicle != null && this.RootObjectDataSet.NotifyChanges)
		                relatedVehicle.NotifyPropertyChanged("VehicleLockoutItems", new SeenObjectCollection());
					
				}			
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return VehicleLockoutObjects.ContainsKey(internalObjectId) ? VehicleLockoutObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as VehicleLockoutDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "VehicleLockoutObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = VehicleLockoutObjectInternalIds.ContainsKey((objectToGet as VehicleLockoutDataObject).PrimaryKey) ? (int?) VehicleLockoutObjectInternalIds[(objectToGet as VehicleLockoutDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return VehicleLockoutObjects.ContainsKey((int)objectToGetInternalId) ? VehicleLockoutObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return VehicleLockoutObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return VehicleLockoutObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		 
		
		public IEnumerable<VehicleLockoutDataObject> GetVehicleLockoutsForDriver(DriverDataObject driverInstance) 
		{
			if (driverInstance.IsNew)
            {
			
              return VehicleLockoutObjects.Where(o => o.Value._driver_NewObjectId != null && o.Value._driver_NewObjectId == driverInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Driver_FKIndex.ContainsKey(driverInstance.Id))
			{
				return Driver_FKIndex[driverInstance.Id].Where(e => VehicleLockoutObjects.ContainsKey(e)).Select(e => VehicleLockoutObjects[e]);
			}
			
			return new DataObjectCollection<VehicleLockoutDataObject>();
		}
		 
		
		public IEnumerable<VehicleLockoutDataObject> GetVehicleLockoutItemsForGOUser(GOUserDataObject gOUserInstance) 
		{
			if (gOUserInstance.IsNew)
            {
			
              return VehicleLockoutObjects.Where(o => o.Value._gOUser_NewObjectId != null && o.Value._gOUser_NewObjectId == gOUserInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (GOUser_FKIndex.ContainsKey(gOUserInstance.Id))
			{
				return GOUser_FKIndex[gOUserInstance.Id].Where(e => VehicleLockoutObjects.ContainsKey(e)).Select(e => VehicleLockoutObjects[e]);
			}
			
			return new DataObjectCollection<VehicleLockoutDataObject>();
		}
		 
		
		public IEnumerable<VehicleLockoutDataObject> GetVehicleLockoutsForSession(SessionDataObject sessionInstance) 
		{
			if (sessionInstance.IsNew)
            {
			
              return VehicleLockoutObjects.Where(o => o.Value._session_NewObjectId != null && o.Value._session_NewObjectId == sessionInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Session_FKIndex.ContainsKey(sessionInstance.Id))
			{
				return Session_FKIndex[sessionInstance.Id].Where(e => VehicleLockoutObjects.ContainsKey(e)).Select(e => VehicleLockoutObjects[e]);
			}
			
			return new DataObjectCollection<VehicleLockoutDataObject>();
		}
		 
		
		public IEnumerable<VehicleLockoutDataObject> GetVehicleLockoutItemsForVehicle(VehicleDataObject vehicleInstance) 
		{
			if (vehicleInstance.IsNew)
            {
			
              return VehicleLockoutObjects.Where(o => o.Value._vehicle_NewObjectId != null && o.Value._vehicle_NewObjectId == vehicleInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Vehicle_FKIndex.ContainsKey(vehicleInstance.Id))
			{
				return Vehicle_FKIndex[vehicleInstance.Id].Where(e => VehicleLockoutObjects.ContainsKey(e)).Select(e => VehicleLockoutObjects[e]);
			}
			
			return new DataObjectCollection<VehicleLockoutDataObject>();
		}
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
			if (relationName == "AllVehicleUnlocksViewItems")
            {
				IEnumerable< AllVehicleUnlocksViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.AllVehicleUnlocksViewObjectsDataSet.GetAllVehicleUnlocksViewItemsForVehicleLockout(rootObject as VehicleLockoutDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
 
 
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var VehicleLockoutDataSet = dataSetToMerge as VehicleLockoutObjectsDataSet;
				if(VehicleLockoutDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in VehicleLockoutDataSet.VehicleLockoutObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "VehicleLockoutObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as VehicleLockoutDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
		 
			// Reconstruct the Driver FK Index 
			Driver_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleLockoutObjects.Values)
			{
				if (item.DriverId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.DriverId;	

				if (!Driver_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Driver_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleLockoutObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Driver_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the GOUser FK Index 
			GOUser_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleLockoutObjects.Values)
			{
				if (item.GOUserId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.GOUserId;	

				if (!GOUser_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = GOUser_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleLockoutObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				GOUser_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the Session FK Index 
			Session_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleLockoutObjects.Values)
			{
				if (item.SessionId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.SessionId;	

				if (!Session_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Session_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleLockoutObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Session_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the Vehicle FK Index 
			Vehicle_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in VehicleLockoutObjects.Values)
			{
				if (item.VehicleId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.VehicleId;	

				if (!Vehicle_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Vehicle_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "VehicleLockoutObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Vehicle_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (VehicleLockoutObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}