﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

(function () {
	// 
	FleetXQ.Web.Controllers.FloorPlanPagePageController = function(applicationController) {
		var self = this;
		this.subscriptions = [];
		// store all subscriptions in this array, to unsubscribe on release.
		
		this._objectType = "FloorPlanPagePage";

		this.applicationController = applicationController;
		this.ObjectsDataSet = applicationController.ObjectsDataSet;
		this.contextId = [this.applicationController.getNextContextId()];
		this.customController = undefined;

		// Integrate custom code if any
		if (FleetXQ.Web.Controllers.FloorPlanPagePageControllerCustom !== undefined) {
		    this.customController = new FleetXQ.Web.Controllers.FloorPlanPagePageControllerCustom(self);
		};

		// Initialize View Models and Data Stores
		
			this.SiteForm3ViewModel = new FleetXQ.Web.ViewModels.SiteForm3ViewModel(this, $("#SiteForm3Control"), null, null, this.contextId);		
		this.SiteForm3ViewModel.StatusData.ShowTitle(false);		
		this.customPageTitle = ko.observable(null);

		this.iconToUse = ko.observable(null);

		this.pageTitle = ko.pureComputed (function() {
			if (self.customPageTitle()) {
				return self.customPageTitle();
			}
			return "Floor Plan Page";
		});

		this.pageTitleParams = ko.observable({});
		
		this.showGoBackBadge = ko.observable(false);
		this.goBackBadgeLabel = ko.observable("pages/FloorPlanPagePage:navigation.BackNavigationBadge");
		this.showItemCountBadge = ko.observable(false);
		this.isLoading = ko.observable(false);
		this.itemCount = ko.pureComputed(function() {
			return null;
		}); 
		this.goBack = function () {
			if(self.IsInEditMode && self.IsInEditMode()) {
				if(!confirm(FleetXQ.Web.Messages.i18n.t('messages.unsavedChanges')))
					return;
				
				// Cancel edit mode on current page controller
				if (self.CancelEdit) {
					self.CancelEdit(false);
				}					
			}

};

		this.subscriptions.push(this.SiteForm3ViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnSiteForm3ViewModelIsBusyChanged(newValue); }));
		
		this.IsInEditMode = function() {

			return  (self.SiteForm3ViewModel.StatusData.DisplayMode && self.SiteForm3ViewModel.StatusData.DisplayMode() == 'edit');
		};

		this.CancelEdit = function(isCommandCall) {
			if (self.SiteForm3ViewModel.CancelEdit) {
				self.SiteForm3ViewModel.CancelEdit(isCommandCall);
			}
		};

		// Events Handlers
		this.OnSiteForm3ViewModelIsBusyChanged = function (newValue) {
		};

		this.initialize = function() {
			if (FleetXQ.Web.Controllers.Custom && FleetXQ.Web.Controllers.Custom.getPageTitle) {
				self.customPageTitle(FleetXQ.Web.Controllers.Custom.getPageTitle(self));
			}

			var partsCount = location.hash.split("/").length;
			var lasttagindex = 0;
			if (partsCount == 3) {
				var hash = window.location.hash;
				var allPksValid = true;
				lasttagindex = hash.lastIndexOf('/');
				var pk1 = GO.Encoding.UrlDecode(hash.substring(lasttagindex + 1).split(new RegExp("#", "g"))[0]);
				allPksValid = allPksValid && GO.IsGuid(pk1);			
				
				if(allPksValid) {
					var objectToLoad = new FleetXQ.Web.Model.DataObjects.SiteObject();
					objectToLoad.Data.Id(pk1);	
					self.SiteForm3ViewModel.LoadSite(objectToLoad);	
				}
			}
	
			// Call custom initialize if defined
			if (self.customController !== undefined && self.customController.initialize !== undefined) {
			    self.customController.initialize();
			}
		};
		this.initialize();



		this.release = function() {
			// unsubscribe
			for(var i = 0;i <  self.subscriptions.length;i++) {
				self.subscriptions[i].dispose();
			}
			self.subscriptions = [];
			self.ObjectsDataSet.cleanContext(self.contextId);
			
			self.SiteForm3ViewModel.release();
			self.SiteForm3ViewModel = null;
		};


		return self;
	};		
	
	if (window.ApplicationSourceHandler)
		window.ApplicationSourceHandler.onSourceLoaded("/Controllers/FloorPlanPagePageController.js");
} ());
