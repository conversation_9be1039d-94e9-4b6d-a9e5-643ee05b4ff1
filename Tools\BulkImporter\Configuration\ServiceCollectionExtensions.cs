using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Configuration;

namespace FleetXQ.Tools.BulkImporter.Configuration;

/// <summary>
/// Extension methods for configuring services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds configuration options to the service collection
    /// </summary>
    public static IServiceCollection AddBulkImporterConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        // Bind configuration sections to strongly-typed options
        services.Configure<BulkImporterOptions>(configuration.GetSection(BulkImporterOptions.SectionName));
        services.Configure<DataGenerationOptions>(configuration.GetSection(DataGenerationOptions.SectionName));
        services.Configure<ConnectionStringOptions>(configuration.GetSection(ConnectionStringOptions.SectionName));

        // Validate configuration on startup
        services.AddOptions<BulkImporterOptions>()
            .Bind(configuration.GetSection(BulkImporterOptions.SectionName))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        services.AddOptions<DataGenerationOptions>()
            .Bind(configuration.GetSection(DataGenerationOptions.SectionName))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        services.AddOptions<ConnectionStringOptions>()
            .Bind(configuration.GetSection(ConnectionStringOptions.SectionName))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        return services;
    }
}