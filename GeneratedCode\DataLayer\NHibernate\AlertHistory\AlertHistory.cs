﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge ORMEntity -> DataObject
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	[Serializable]
	public class ORMAlertHistory : IORMEntity 
	{
		public virtual System.Guid Id { get; set; }
 
		///
		/// All Persistent non-key fields
		///
		public virtual System.String Description { get; set; }
		public virtual System.DateTime CreatedDateTime { get; set; }
		public virtual System.Boolean IsResolved { get; set; }
		public virtual System.Boolean IsAcknowledged { get; set; }
 
		///
		/// PK-Side one-to-one relations
		///
 
		///
		/// All FK-Side Relations
		///
		public virtual ORMAlert Alert { get; set; }
		public virtual System.Guid AlertId { get; set; }

		public virtual ORMVehicle Vehicle { get; set; }
		public virtual System.Guid VehicleId { get; set; }

		public virtual ORMDriver Driver { get; set; }
		public virtual System.Guid DriverId { get; set; }

	
 
		///
		/// PK-Side one-to-many relations
		///
 
		///
		/// Bridge to DataObject
		///
		public virtual IDataObject ToDataObject(IObjectsDataSet dataset, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
            var session = nHibernateSessionController.GetCurrentSession(threadContext);
            session.Evict(this);

			var x = serviceProvider.GetRequiredService<AlertHistoryDataObject>();

 
			SetProperties(x);

			x.IsDirty = x.IsNew = x.IsMarkedForDeletion = false;

			x.ObjectsDataSet = dataset;
			x.ObjectsDataSet.AddObjectIfDoesNotExist(x);

			// Deep-map prefetch relations
			if (PrefetchAssociations.HasPrefetchForEntity("AlertHistory", dataProviderTransaction?.Parameters))
			{
				SetRelations(x, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction);
			}

			return x;
		}

		protected void SetProperties(AlertHistoryDataObject x)
		{
			x.SetIdValue(Id, false, false);
			x.SetDescriptionValue(Description, false, false);
			x.SetCreatedDateTimeValue(CreatedDateTime, false, false);
			x.SetIsResolvedValue(IsResolved, false, false);
			x.SetIsAcknowledgedValue(IsAcknowledged, false, false);
			x.SetAlertIdValue(this.AlertId, false, false);
			x.SetVehicleIdValue(this.VehicleId, false, false);
			x.SetDriverIdValue(this.DriverId, false, false);
		}

		protected void SetRelations(AlertHistoryDataObject x, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
			var prefetches = PrefetchAssociations.Get("AlertHistory", dataProviderTransaction?.Parameters);

			if (prefetches.Contains("Vehicle") && this.Vehicle != null)
			{
				var vehicle = x.ObjectsDataSet.GetObject(new VehicleDataObject((System.Guid)this.Vehicle.Id) { IsNew = false });

				if (vehicle == null)
					vehicle = this.Vehicle.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleDataObject;

				x.SetVehicleValue(vehicle);
			}

			if (prefetches.Contains("Driver") && this.Driver != null)
			{
				var driver = x.ObjectsDataSet.GetObject(new DriverDataObject((System.Guid)this.Driver.Id) { IsNew = false });

				if (driver == null)
					driver = this.Driver.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DriverDataObject;

				x.SetDriverValue(driver);
			}

			if (prefetches.Contains("Alert") && this.Alert != null)
			{
				var alert = x.ObjectsDataSet.GetObject(new AlertDataObject((System.Guid)this.Alert.Id) { IsNew = false });

				if (alert == null)
					alert = this.Alert.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as AlertDataObject;

				x.SetAlertValue(alert);
			}

		}
		
		// For database filtering, nothing IsNew or IsDirty, but these stubs needed for ORM security predicates to compile
		public virtual bool IsNew => false;
		public virtual bool IsDirty => false;

	}
}