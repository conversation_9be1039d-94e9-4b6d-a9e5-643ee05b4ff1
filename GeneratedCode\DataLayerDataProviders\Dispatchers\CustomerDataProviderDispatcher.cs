﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Data.DataProviders.Dispatchers
{
    public class CustomerDataProviderDispatcher : IDataProviderDispatcher<CustomerDataObject>
    {
		protected IServiceProvider _serviceProvider;
		
		public CustomerDataProviderDispatcher(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
		
		protected IDataProvider<DashboardVehicleCardStoreProcedureDataObject> dashboardVehicleCardStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<DashboardVehicleCardStoreProcedureDataObject>>();
		protected IDataProvider<DriverLicenseExpiryViewDataObject> driverLicenseExpiryViewDataProvider => _serviceProvider.GetService<IDataProvider<DriverLicenseExpiryViewDataObject>>();
		protected IDataProvider<TodaysPreopCheckStoreProcedureDataObject> todaysPreopCheckStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<TodaysPreopCheckStoreProcedureDataObject>>();
		protected IDataProvider<ImpactFrequencyPerWeekDayViewDataObject> impactFrequencyPerWeekDayViewDataProvider => _serviceProvider.GetService<IDataProvider<ImpactFrequencyPerWeekDayViewDataObject>>();
		protected IDataProvider<CurrentDriverStatusChartViewDataObject> currentDriverStatusChartViewDataProvider => _serviceProvider.GetService<IDataProvider<CurrentDriverStatusChartViewDataObject>>();
		protected IDataProvider<SlamcoreDeviceDataObject> slamcoreDeviceDataProvider => _serviceProvider.GetService<IDataProvider<SlamcoreDeviceDataObject>>();
		protected IDataProvider<CustomerSnapshotDataObject> customerSnapshotDataProvider => _serviceProvider.GetService<IDataProvider<CustomerSnapshotDataObject>>();
		protected IDataProvider<DriverDataObject> driverDataProvider => _serviceProvider.GetService<IDataProvider<DriverDataObject>>();
		protected IDataProvider<PersonDataObject> personDataProvider => _serviceProvider.GetService<IDataProvider<PersonDataObject>>();
		protected IDataProvider<DashboardDriverCardViewDataObject> dashboardDriverCardViewDataProvider => _serviceProvider.GetService<IDataProvider<DashboardDriverCardViewDataObject>>();
		protected IDataProvider<CustomerToModelDataObject> customerToModelDataProvider => _serviceProvider.GetService<IDataProvider<CustomerToModelDataObject>>();
		protected IDataProvider<CustomerToPersonViewDataObject> customerToPersonViewDataProvider => _serviceProvider.GetService<IDataProvider<CustomerToPersonViewDataObject>>();
		protected IDataProvider<CustomerModelDataObject> customerModelDataProvider => _serviceProvider.GetService<IDataProvider<CustomerModelDataObject>>();
		protected IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject> vehicleUtilizationLastTwelveHoursViewDataProvider => _serviceProvider.GetService<IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject>>();
		protected IDataProvider<AccessGroupDataObject> accessGroupDataProvider => _serviceProvider.GetService<IDataProvider<AccessGroupDataObject>>();
		protected IDataProvider<DashboardDriverCardStoreProcedureDataObject> dashboardDriverCardStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<DashboardDriverCardStoreProcedureDataObject>>();
		protected IDataProvider<TodaysPreopCheckViewDataObject> todaysPreopCheckViewDataProvider => _serviceProvider.GetService<IDataProvider<TodaysPreopCheckViewDataObject>>();
		protected IDataProvider<CustomerFeatureSubscriptionDataObject> customerFeatureSubscriptionDataProvider => _serviceProvider.GetService<IDataProvider<CustomerFeatureSubscriptionDataObject>>();
		protected IDataProvider<TodaysImpactStoreProcedureDataObject> todaysImpactStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<TodaysImpactStoreProcedureDataObject>>();
		protected IDataProvider<CurrentVehicleStatusChartViewDataObject> currentVehicleStatusChartViewDataProvider => _serviceProvider.GetService<IDataProvider<CurrentVehicleStatusChartViewDataObject>>();
		protected IDataProvider<DashboardFilterDataObject> dashboardFilterDataProvider => _serviceProvider.GetService<IDataProvider<DashboardFilterDataObject>>();
		protected IDataProvider<TodaysImpactViewDataObject> todaysImpactViewDataProvider => _serviceProvider.GetService<IDataProvider<TodaysImpactViewDataObject>>();
		protected IDataProvider<EmailGroupsDataObject> emailGroupsDataProvider => _serviceProvider.GetService<IDataProvider<EmailGroupsDataObject>>();
		protected IDataProvider<DealerDataObject> dealerDataProvider => _serviceProvider.GetService<IDataProvider<DealerDataObject>>();
		protected IDataProvider<CustomerSSODetailDataObject> customerSSODetailDataProvider => _serviceProvider.GetService<IDataProvider<CustomerSSODetailDataObject>>();
		protected IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject> impactFrequencyPerWeekMonthViewDataProvider => _serviceProvider.GetService<IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject>>();
		protected IDataProvider<IncompletedChecklistViewDataObject> incompletedChecklistViewDataProvider => _serviceProvider.GetService<IDataProvider<IncompletedChecklistViewDataObject>>();
		protected IDataProvider<CountryDataObject> countryDataProvider => _serviceProvider.GetService<IDataProvider<CountryDataObject>>();
		protected IDataProvider<DepartmentDataObject> departmentDataProvider => _serviceProvider.GetService<IDataProvider<DepartmentDataObject>>();
		protected IDataProvider<ContactPersonInformationDataObject> contactPersonInformationDataProvider => _serviceProvider.GetService<IDataProvider<ContactPersonInformationDataObject>>();
		protected IDataProvider<GoUserToCustomerDataObject> goUserToCustomerDataProvider => _serviceProvider.GetService<IDataProvider<GoUserToCustomerDataObject>>();
		protected IDataProvider<VehicleDataObject> vehicleDataProvider => _serviceProvider.GetService<IDataProvider<VehicleDataObject>>();
		protected IDataProvider<CustomerAuditDataObject> customerAuditDataProvider => _serviceProvider.GetService<IDataProvider<CustomerAuditDataObject>>();
		protected IDataProvider<CustomerPreOperationalChecklistTemplateDataObject> customerPreOperationalChecklistTemplateDataProvider => _serviceProvider.GetService<IDataProvider<CustomerPreOperationalChecklistTemplateDataObject>>();
		protected IDataProvider<DashboardVehicleCardViewDataObject> dashboardVehicleCardViewDataProvider => _serviceProvider.GetService<IDataProvider<DashboardVehicleCardViewDataObject>>();
		protected IDataProvider<ChecklistStatusViewDataObject> checklistStatusViewDataProvider => _serviceProvider.GetService<IDataProvider<ChecklistStatusViewDataObject>>();
		protected IDataProvider<LoggedHoursVersusSeatHoursViewDataObject> loggedHoursVersusSeatHoursViewDataProvider => _serviceProvider.GetService<IDataProvider<LoggedHoursVersusSeatHoursViewDataObject>>();
		protected IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> vehicleUtilizationLastTwelveHoursStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>>();
		protected IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject> impactFrequencyPerTimeSlotViewDataProvider => _serviceProvider.GetService<IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject>>();
		protected IDataProvider<SiteDataObject> siteDataProvider => _serviceProvider.GetService<IDataProvider<SiteDataObject>>();
		protected IDataProvider<DriverLicenseExpiryStoreProcedureDataObject> driverLicenseExpiryStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<DriverLicenseExpiryStoreProcedureDataObject>>();

        public async Task DispatchForEntityAsync(CustomerDataObject entity, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("Customer", parameters);

            foreach (var include in includes)
            {
	
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
                  case "dashboardvehiclecardstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DashboardVehicleCardStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await dashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "driverlicenseexpiryviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DriverLicenseExpiryViewItems"))
									break;

								try
								{
									var objectToFetch = await driverLicenseExpiryViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "todayspreopcheckstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("TodaysPreopCheckStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await todaysPreopCheckStoreProcedureDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "impactfrequencyperweekdayviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ImpactFrequencyPerWeekDayViewItems"))
									break;

								try
								{
									var objectToFetch = await impactFrequencyPerWeekDayViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "currentdriverstatuschartviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CurrentDriverStatusChartViewItems"))
									break;

								try
								{
									var objectToFetch = await currentDriverStatusChartViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "slamcoredeviceitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("SlamcoreDeviceItems"))
									break;

								try
								{
									var objectToFetch = await slamcoreDeviceDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customersnapshotitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerSnapshotItems"))
									break;

								try
								{
									var objectToFetch = await customerSnapshotDataProvider.GetCollectionAsync(null, "fkCustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "driveritems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DriverItems"))
									break;

								try
								{
									var objectToFetch = await driverDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "personitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("PersonItems"))
									break;

								try
								{
									var objectToFetch = await personDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "dashboardcardviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DashboardCardViewItems"))
									break;

								try
								{
									var objectToFetch = await dashboardDriverCardViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customertomodelitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerToModelItems"))
									break;

								try
								{
									var objectToFetch = await customerToModelDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customertopersonviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerToPersonViewItems"))
									break;

								try
								{
									var objectToFetch = await customerToPersonViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customermodelitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerModelItems"))
									break;

								try
								{
									var objectToFetch = await customerModelDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehicleutilizationlasttwelvehoursviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleUtilizationLastTwelveHoursViewItems"))
									break;

								try
								{
									var objectToFetch = await vehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "accessgroupitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("AccessGroupItems"))
									break;

								try
								{
									var objectToFetch = await accessGroupDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "dashboarddrivercardstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DashboardDriverCardStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await dashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "todayspreopcheckviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("TodaysPreopCheckViewItems"))
									break;

								try
								{
									var objectToFetch = await todaysPreopCheckViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customerfeaturesubscription":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerFeatureSubscription"))
									break;

								if (entity.CustomerFeatureSubscriptionId != null) 
								{
									try
									{
										var objectToFetch = await customerFeatureSubscriptionDataProvider.GetAsync(new CustomerFeatureSubscriptionDataObject((System.Guid)entity.CustomerFeatureSubscriptionId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "todaysimpactstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("TodaysImpactStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await todaysImpactStoreProcedureDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "currentvehiclestatuschartviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CurrentVehicleStatusChartViewItems"))
									break;

								try
								{
									var objectToFetch = await currentVehicleStatusChartViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "dashboardfilteritems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DashboardFilterItems"))
									break;

								try
								{
									var objectToFetch = await dashboardFilterDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "todaysimpactviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("TodaysImpactViewItems"))
									break;

								try
								{
									var objectToFetch = await todaysImpactViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "emailgroupsitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("EmailGroupsItems"))
									break;

								try
								{
									var objectToFetch = await emailGroupsDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "dealer":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Dealer"))
									break;

								try
								{
									var objectToFetch = await dealerDataProvider.GetAsync(new DealerDataObject(entity.DealerId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customerssodetailitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerSSODetailItems"))
									break;

								try
								{
									var objectToFetch = await customerSSODetailDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "impactfrequencyperweekmonthviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ImpactFrequencyPerWeekMonthViewItems"))
									break;

								try
								{
									var objectToFetch = await impactFrequencyPerWeekMonthViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "incompletedchecklistviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("IncompletedChecklistViewItems"))
									break;

								try
								{
									var objectToFetch = await incompletedChecklistViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "country":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Country"))
									break;

								try
								{
									var objectToFetch = await countryDataProvider.GetAsync(new CountryDataObject(entity.CountryId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "departmentitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DepartmentItems"))
									break;

								try
								{
									var objectToFetch = await departmentDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "contactpersoninformation":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ContactPersonInformation"))
									break;

								if (entity.ContactPersonInformationId != null) 
								{
									try
									{
										var objectToFetch = await contactPersonInformationDataProvider.GetAsync(new ContactPersonInformationDataObject((System.Guid)entity.ContactPersonInformationId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "gousertocustomeritems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("GoUserToCustomerItems"))
									break;

								try
								{
									var objectToFetch = await goUserToCustomerDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehicleitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleItems"))
									break;

								try
								{
									var objectToFetch = await vehicleDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customeraudit":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerAudit"))
									break;

								try
								{
									var objectToFetch = await customerAuditDataProvider.GetCollectionAsync(null, "fkCustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customerpreoperationalchecklisttemplateitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CustomerPreOperationalChecklistTemplateItems"))
									break;

								try
								{
									var objectToFetch = await customerPreOperationalChecklistTemplateDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "dashboardvehiclecardviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DashboardVehicleCardViewItems"))
									break;

								try
								{
									var objectToFetch = await dashboardVehicleCardViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "checkliststatusviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ChecklistStatusViewItems"))
									break;

								try
								{
									var objectToFetch = await checklistStatusViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "loggedhoursversusseathoursviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("LoggedHoursVersusSeatHoursViewItems"))
									break;

								try
								{
									var objectToFetch = await loggedHoursVersusSeatHoursViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehicleutilizationlasttwelvehoursstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleUtilizationLastTwelveHoursStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await vehicleUtilizationLastTwelveHoursStoreProcedureDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "impactfrequencypertimeslotviewitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ImpactFrequencyPerTimeSlotViewItems"))
									break;

								try
								{
									var objectToFetch = await impactFrequencyPerTimeSlotViewDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "sites":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Sites"))
									break;

								try
								{
									var objectToFetch = await siteDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "driverlicenseexpirystoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DriverLicenseExpiryStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await driverLicenseExpiryStoreProcedureDataProvider.GetCollectionAsync(null, "CustomerId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
						default:
							throw new ApplicationException("Customer Entity has no relation named " + relation);
					}
          }        
		}

        public async Task DispatchForEntityCollectionAsync(IEnumerable<CustomerDataObject> entities, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("Customer", parameters);

            foreach (var include in includes)
            {
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
						case "dashboardvehiclecardstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DashboardVehicleCardStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await dashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "driverlicenseexpiryviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DriverLicenseExpiryViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await driverLicenseExpiryViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "todayspreopcheckstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("TodaysPreopCheckStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await todaysPreopCheckStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "impactfrequencyperweekdayviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ImpactFrequencyPerWeekDayViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await impactFrequencyPerWeekDayViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "currentdriverstatuschartviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CurrentDriverStatusChartViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await currentDriverStatusChartViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "slamcoredeviceitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("SlamcoreDeviceItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await slamcoreDeviceDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customersnapshotitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerSnapshotItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerSnapshotDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.fkCustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "driveritems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DriverItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await driverDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "personitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("PersonItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await personDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "dashboardcardviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DashboardCardViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await dashboardDriverCardViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customertomodelitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerToModelItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerToModelDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customertopersonviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerToPersonViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerToPersonViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customermodelitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerModelItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerModelDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicleutilizationlasttwelvehoursviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleUtilizationLastTwelveHoursViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "accessgroupitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("AccessGroupItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await accessGroupDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "dashboarddrivercardstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DashboardDriverCardStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await dashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "todayspreopcheckviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("TodaysPreopCheckViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await todaysPreopCheckViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customerfeaturesubscription":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerFeatureSubscription"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.CustomerFeatureSubscriptionId != null).Select(e => (System.Guid)e.CustomerFeatureSubscriptionId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerFeatureSubscriptionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "todaysimpactstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("TodaysImpactStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await todaysImpactStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "currentvehiclestatuschartviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CurrentVehicleStatusChartViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await currentVehicleStatusChartViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "dashboardfilteritems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DashboardFilterItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await dashboardFilterDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "todaysimpactviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("TodaysImpactViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await todaysImpactViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "emailgroupsitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("EmailGroupsItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await emailGroupsDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "dealer":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Dealer"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.DealerId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await dealerDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customerssodetailitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerSSODetailItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerSSODetailDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "impactfrequencyperweekmonthviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ImpactFrequencyPerWeekMonthViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await impactFrequencyPerWeekMonthViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "incompletedchecklistviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("IncompletedChecklistViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await incompletedChecklistViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "country":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Country"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.CountryId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await countryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "departmentitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DepartmentItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await departmentDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "contactpersoninformation":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ContactPersonInformation"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.ContactPersonInformationId != null).Select(e => (System.Guid)e.ContactPersonInformationId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await contactPersonInformationDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "gousertocustomeritems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("GoUserToCustomerItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await goUserToCustomerDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicleitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customeraudit":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerAudit"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerAuditDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.fkCustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customerpreoperationalchecklisttemplateitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CustomerPreOperationalChecklistTemplateItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerPreOperationalChecklistTemplateDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "dashboardvehiclecardviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DashboardVehicleCardViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await dashboardVehicleCardViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "checkliststatusviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ChecklistStatusViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await checklistStatusViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "loggedhoursversusseathoursviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("LoggedHoursVersusSeatHoursViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await loggedHoursVersusSeatHoursViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicleutilizationlasttwelvehoursstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleUtilizationLastTwelveHoursStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleUtilizationLastTwelveHoursStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "impactfrequencypertimeslotviewitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ImpactFrequencyPerTimeSlotViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await impactFrequencyPerTimeSlotViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "sites":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Sites"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await siteDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "driverlicenseexpirystoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMCustomer> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DriverLicenseExpiryStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await driverLicenseExpiryStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.CustomerId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
                  default:
                        throw new ApplicationException("Customer Entity has no relation named " + relation);
					}
            }        
        }
	}
}