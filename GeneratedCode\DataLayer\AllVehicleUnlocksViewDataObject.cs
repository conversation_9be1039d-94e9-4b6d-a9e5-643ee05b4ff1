﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'AllVehicleUnlocksView'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class AllVehicleUnlocksViewDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("DealerId")]
		protected Nullable<System.Guid> _dealerId;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("TimezoneAdjustedLockoutDatetime")]
		protected System.DateTime _timezoneAdjustedLockoutDatetime;
		[JsonProperty("TimezoneAdjustedLockoutDatetime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _timezoneAdjustedLockoutDatetime_WithTimezoneOffset;
		[JsonProperty ("TimezoneAdjustedLockoutDatetimeDisplay")]
		protected System.String _timezoneAdjustedLockoutDatetimeDisplay;
		[JsonProperty ("TimezoneAdjustedUnlockDatetime")]
		protected System.DateTime _timezoneAdjustedUnlockDatetime;
		[JsonProperty("TimezoneAdjustedUnlockDatetime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _timezoneAdjustedUnlockDatetime_WithTimezoneOffset;
		[JsonProperty ("TimezoneAdjustedUnlockDatetimeDisplay")]
		protected System.String _timezoneAdjustedUnlockDatetimeDisplay;
		[JsonProperty ("UnlockedBy")]
		protected System.String _unlockedBy;
		[JsonProperty ("VehicleLockoutId")]
		protected System.Guid _vehicleLockoutId;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)
		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _dealer_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_dealer_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _vehicleLockout_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_vehicleLockout_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public AllVehicleUnlocksViewDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public AllVehicleUnlocksViewDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public AllVehicleUnlocksViewDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public AllVehicleUnlocksViewDataObject Initialize(AllVehicleUnlocksViewDataObject template, bool deepCopy)
		{
			this.SetTimezoneAdjustedLockoutDatetimeValue(template.TimezoneAdjustedLockoutDatetime, false, false);
			this._timezoneAdjustedLockoutDatetime_WithTimezoneOffset = template._timezoneAdjustedLockoutDatetime_WithTimezoneOffset;
			this.SetTimezoneAdjustedUnlockDatetimeValue(template.TimezoneAdjustedUnlockDatetime, false, false);
			this._timezoneAdjustedUnlockDatetime_WithTimezoneOffset = template._timezoneAdjustedUnlockDatetime_WithTimezoneOffset;
			this.SetDealerIdValue(template.DealerId, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetTimezoneAdjustedLockoutDatetimeDisplayValue(template.TimezoneAdjustedLockoutDatetimeDisplay, false, false);
			this.SetTimezoneAdjustedUnlockDatetimeDisplayValue(template.TimezoneAdjustedUnlockDatetimeDisplay, false, false);
			this.SetUnlockedByValue(template.UnlockedBy, false, false);
			this.SetVehicleLockoutIdValue(template.VehicleLockoutId, false, false);
 
			this._dealer_NewObjectId = template._dealer_NewObjectId;
 
			this._vehicleLockout_NewObjectId = template._vehicleLockout_NewObjectId;
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual AllVehicleUnlocksViewDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual AllVehicleUnlocksViewDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<AllVehicleUnlocksViewDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var allVehicleUnlocksViewSource = sourceObject as AllVehicleUnlocksViewDataObject;

			if (ReferenceEquals(null, allVehicleUnlocksViewSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetDealerIdValue(allVehicleUnlocksViewSource.DealerId, false, false);
			this.SetIdValue(allVehicleUnlocksViewSource.Id, false, false);
			this.SetTimezoneAdjustedLockoutDatetimeValue(allVehicleUnlocksViewSource.TimezoneAdjustedLockoutDatetime, false, false);
			this.SetTimezoneAdjustedLockoutDatetimeDisplayValue(allVehicleUnlocksViewSource.TimezoneAdjustedLockoutDatetimeDisplay, false, false);
			this.SetTimezoneAdjustedUnlockDatetimeValue(allVehicleUnlocksViewSource.TimezoneAdjustedUnlockDatetime, false, false);
			this.SetTimezoneAdjustedUnlockDatetimeDisplayValue(allVehicleUnlocksViewSource.TimezoneAdjustedUnlockDatetimeDisplay, false, false);
			this.SetUnlockedByValue(allVehicleUnlocksViewSource.UnlockedBy, false, false);
			this.SetVehicleLockoutIdValue(allVehicleUnlocksViewSource.VehicleLockoutId, false, false);
			this._dealer_NewObjectId = (sourceObject as AllVehicleUnlocksViewDataObject)._dealer_NewObjectId;

			this._vehicleLockout_NewObjectId = (sourceObject as AllVehicleUnlocksViewDataObject)._vehicleLockout_NewObjectId;

			if (deepCopy)
			{
				this.ObjectsDataSet = allVehicleUnlocksViewSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(allVehicleUnlocksViewSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as AllVehicleUnlocksViewDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {
			if (this._dealer_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._dealer_NewObjectId))
				{
                    this._dealer_NewObjectId = null;
				}
                else
				{
					this._dealer_NewObjectId = datasetMergingInternalIdMapping[(int) this._dealer_NewObjectId];
				}
			}

			if (this._vehicleLockout_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._vehicleLockout_NewObjectId))
				{
                    this._vehicleLockout_NewObjectId = null;
				}
                else
				{
					this._vehicleLockout_NewObjectId = datasetMergingInternalIdMapping[(int) this._vehicleLockout_NewObjectId];
				}
			}

		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<DealerDataObject> _dealerService => _serviceProvider.GetRequiredService<IDataProvider<DealerDataObject>>();
      public virtual void SetDealerValue(DealerDataObject valueToSet)
		{
			SetDealerValue(valueToSet, true, true);
		}

        public virtual void SetDealerValue(DealerDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DealerDataObject existing_dealer = null ;

			if ( !(this.DealerId == null || ObjectsDataSet == null))
			{
				DealerDataObject key;

				if (this._dealer_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DealerDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._dealer_NewObjectId;			
				}

				existing_dealer = (DealerDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_dealer ,valueToSet))
            {
                if (valueToSet == null)
                {
					_dealer_NewObjectId = null;
					_dealerId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Dealer", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "AllVehicleUnlocksViewDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_dealer_NewObjectId != valueToSet.InternalObjectId)
					{
						_dealer_NewObjectId = valueToSet.InternalObjectId;
						_dealerId = valueToSet.Id;
						OnPropertyChanged("DealerId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_dealerId != valueToSet.Id)
					{
						_dealer_NewObjectId = null;

						_dealerId = valueToSet.Id;
						OnPropertyChanged("DealerId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_dealer_NewObjectId = null;
					_dealerId = null;
					
				OnPropertyChanged("DealerId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_dealer ,valueToSet))
				OnPropertyChanged("Dealer", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __dealerSemaphore = new SemaphoreSlim(1, 1);
		private bool __dealerAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Dealer", which is a DealerDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DealerDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DealerDataObject> LoadDealerAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDealerAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DealerDataObject> LoadDealerAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __dealerSemaphore.WaitAsync();
			
	        try
            {
                if (!__dealerAlreadyLazyLoaded || forceReload)
                {
								
					if (this.DealerId == null)
					{
						return null;
					}
				
					DealerDataObject dealer = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __dealerAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						dealer = _serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId);
						dealer.IsNew = false;
						dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
						if (dealer != null)
						{
							return dealer;
						}
					}

					dealer = await _dealerService.GetAsync(_serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId), parameters : parameters, skipSecurity: skipSecurity);

					SetDealerValue(dealer, false, false);
					__dealerAlreadyLazyLoaded = true;				
		
					dealer = _serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId);
					dealer.IsNew = false;
					dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
                    __dealerAlreadyLazyLoaded = true;
                }

                return await GetDealerAsync(false);
            }
            finally
            {
                __dealerSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DealerDataObject Dealer 
		{
			get
			{			
				return GetDealerAsync(true).Result;
			}
			set
			{
				SetDealerValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDealer()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("AllVehicleUnlocksViewDataObject") && ObjectsDataSet.RelationsToInclude["AllVehicleUnlocksViewDataObject"].Contains("Dealer");
		}

		public virtual async Task<DealerDataObject> GetDealerAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DealerDataObject dealer;

				
			if (_dealer_NewObjectId != null)
			{
				dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
				dealer.IsNew = true;
				dealer.InternalObjectId = _dealer_NewObjectId;
				dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
			}
			else
			{
				if (this.DealerId == null)
					return null;
				if (DealerId == null)
					dealer = null;
				else
				dealer = _serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId);
				dealer.IsNew = false;
				dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
				
				if (allowLazyLoading && dealer == null && LazyLoadingEnabled && (!__dealerAlreadyLazyLoaded || forceReload))
				{
					dealer = await LoadDealerAsync(forceReload : forceReload);
				}
			}
				
			return dealer;
		}

		public virtual Nullable<System.Guid> DealerForeignKey
		{
			get { return DealerId; }
			set 
			{	
				DealerId = value;
			}
			
		}
		

		protected IDataProvider<VehicleLockoutDataObject> _vehicleLockoutService => _serviceProvider.GetRequiredService<IDataProvider<VehicleLockoutDataObject>>();
      public virtual void SetVehicleLockoutValue(VehicleLockoutDataObject valueToSet)
		{
			SetVehicleLockoutValue(valueToSet, true, true);
		}

        public virtual void SetVehicleLockoutValue(VehicleLockoutDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			VehicleLockoutDataObject existing_vehicleLockout = null ;

			if ( !(ObjectsDataSet == null))
			{
				VehicleLockoutDataObject key;

				if (this._vehicleLockout_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<VehicleLockoutDataObject>().Initialize(this.VehicleLockoutId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<VehicleLockoutDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._vehicleLockout_NewObjectId;			
				}

				existing_vehicleLockout = (VehicleLockoutDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_vehicleLockout ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("VehicleLockout", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "AllVehicleUnlocksViewDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_vehicleLockout_NewObjectId != valueToSet.InternalObjectId)
					{
						_vehicleLockout_NewObjectId = valueToSet.InternalObjectId;
						_vehicleLockoutId = valueToSet.Id;
						OnPropertyChanged("VehicleLockoutId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_vehicleLockoutId != valueToSet.Id)
					{
						_vehicleLockout_NewObjectId = null;

						_vehicleLockoutId = valueToSet.Id;
						OnPropertyChanged("VehicleLockoutId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_vehicleLockoutId = Guid.Empty;
				OnPropertyChanged("VehicleLockoutId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_vehicleLockout ,valueToSet))
				OnPropertyChanged("VehicleLockout", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __vehicleLockoutSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleLockoutAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleLockout", which is a VehicleLockoutDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a VehicleLockoutDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<VehicleLockoutDataObject> LoadVehicleLockoutAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleLockoutAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<VehicleLockoutDataObject> LoadVehicleLockoutAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleLockoutSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleLockoutAlreadyLazyLoaded || forceReload)
                {
								
					VehicleLockoutDataObject vehicleLockout = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __vehicleLockoutAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						vehicleLockout = _serviceProvider.GetRequiredService<VehicleLockoutDataObject>().Initialize(this.VehicleLockoutId);
						vehicleLockout.IsNew = false;
						vehicleLockout = (VehicleLockoutDataObject)ObjectsDataSet.GetObject(vehicleLockout);
						if (vehicleLockout != null)
						{
							return vehicleLockout;
						}
					}

					vehicleLockout = await _vehicleLockoutService.GetAsync(_serviceProvider.GetRequiredService<VehicleLockoutDataObject>().Initialize(this.VehicleLockoutId), parameters : parameters, skipSecurity: skipSecurity);

					SetVehicleLockoutValue(vehicleLockout, false, false);
					__vehicleLockoutAlreadyLazyLoaded = true;				
		
					vehicleLockout = _serviceProvider.GetRequiredService<VehicleLockoutDataObject>().Initialize(this.VehicleLockoutId);
					vehicleLockout.IsNew = false;
					vehicleLockout = (VehicleLockoutDataObject)ObjectsDataSet.GetObject(vehicleLockout);
                    __vehicleLockoutAlreadyLazyLoaded = true;
                }

                return await GetVehicleLockoutAsync(false);
            }
            finally
            {
                __vehicleLockoutSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual VehicleLockoutDataObject VehicleLockout 
		{
			get
			{			
				return GetVehicleLockoutAsync(true).Result;
			}
			set
			{
				SetVehicleLockoutValue(value);
			}
		}
		
		public virtual bool ShouldSerializeVehicleLockout()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("AllVehicleUnlocksViewDataObject") && ObjectsDataSet.RelationsToInclude["AllVehicleUnlocksViewDataObject"].Contains("VehicleLockout");
		}

		public virtual async Task<VehicleLockoutDataObject> GetVehicleLockoutAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			VehicleLockoutDataObject vehicleLockout;

				
			if (_vehicleLockout_NewObjectId != null)
			{
				vehicleLockout = _serviceProvider.GetRequiredService<VehicleLockoutDataObject>();
				vehicleLockout.IsNew = true;
				vehicleLockout.InternalObjectId = _vehicleLockout_NewObjectId;
				vehicleLockout = (VehicleLockoutDataObject)ObjectsDataSet.GetObject(vehicleLockout);
			}
			else
			{
				vehicleLockout = _serviceProvider.GetRequiredService<VehicleLockoutDataObject>().Initialize(this.VehicleLockoutId);
				vehicleLockout.IsNew = false;
				vehicleLockout = (VehicleLockoutDataObject)ObjectsDataSet.GetObject(vehicleLockout);
				
				if (allowLazyLoading && vehicleLockout == null && LazyLoadingEnabled && (!__vehicleLockoutAlreadyLazyLoaded || forceReload))
				{
					vehicleLockout = await LoadVehicleLockoutAsync(forceReload : forceReload);
				}
			}
				
			return vehicleLockout;
		}

		public virtual System.Guid VehicleLockoutForeignKey
		{
			get { return VehicleLockoutId; }
			set 
			{	
				VehicleLockoutId = value;
			}
			
		}
		

		public override void ClearLazyLoadFlags()
		{
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadDealerAsync()) != null)
				result.Add(Dealer);
			if ((await LoadVehicleLockoutAsync()) != null)
				result.Add(VehicleLockout);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				Dealer == other ||
				(other is DealerDataObject && (DealerId != default(Nullable<System.Guid>)) && (DealerId == (other as DealerDataObject).Id)) || 
				VehicleLockout == other ||
				(other is VehicleLockoutDataObject && (VehicleLockoutId != default(System.Guid)) && (VehicleLockoutId == (other as VehicleLockoutDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetDealerIdValue(Nullable<System.Guid> valueToSet)
		{
			SetDealerIdValue(valueToSet, true, true);
		}

		public virtual void SetDealerIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_dealerId != valueToSet)
			{
				_dealerId = valueToSet;

				OnPropertyChanged("DealerId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DealerId property of the AllVehicleUnlocksView DataObject</summary>
        public virtual Nullable<System.Guid> DealerId 
		{
			get	{ return _dealerId;}
			
			
			set
			{
				SetDealerIdValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the AllVehicleUnlocksView DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
		
		/// <summary> The LockoutDateTime property of the AllVehicleUnlocksView DataObject</summary>
        public virtual System.String LockoutDateTime 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return TimezoneAdjustedLockoutDatetime.ToString();				
			}
			
		}		
			
			
		public virtual void SetTimezoneAdjustedLockoutDatetimeValue(System.DateTime valueToSet)
		{
			SetTimezoneAdjustedLockoutDatetimeValue(valueToSet, true, true);
		}

		public virtual void SetTimezoneAdjustedLockoutDatetimeValue(System.DateTime valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null || valueToSet == DateTime.MinValue) 
			{
				if (_timezoneAdjustedLockoutDatetime != DateTime.MinValue.ToUniversalTime())
				{
					_timezoneAdjustedLockoutDatetime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("TimezoneAdjustedLockoutDatetime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_timezoneAdjustedLockoutDatetime != DateTime.MaxValue.ToUniversalTime())
				{
					_timezoneAdjustedLockoutDatetime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("TimezoneAdjustedLockoutDatetime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_timezoneAdjustedLockoutDatetime != valueToSet ||
                (_timezoneAdjustedLockoutDatetime != null && ((DateTime)_timezoneAdjustedLockoutDatetime).Kind == DateTimeKind.Unspecified))
			{
				_timezoneAdjustedLockoutDatetime = DateTime.SpecifyKind(valueToSet, DateTimeKind.Local);
				OnPropertyChanged("TimezoneAdjustedLockoutDatetime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Timezone Adjusted Lockout Datetime property of the AllVehicleUnlocksView DataObject</summary>
        public virtual System.DateTime TimezoneAdjustedLockoutDatetime 
		{
			get	{ return _timezoneAdjustedLockoutDatetime;}
			
			
			set
			{
				SetTimezoneAdjustedLockoutDatetimeValue(value);
			}
		}		
			
			
		public virtual void SetTimezoneAdjustedLockoutDatetimeDisplayValue(System.String valueToSet)
		{
			SetTimezoneAdjustedLockoutDatetimeDisplayValue(valueToSet, true, true);
		}

		public virtual void SetTimezoneAdjustedLockoutDatetimeDisplayValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_timezoneAdjustedLockoutDatetimeDisplay != valueToSet)
			{
				_timezoneAdjustedLockoutDatetimeDisplay = valueToSet;

				OnPropertyChanged("TimezoneAdjustedLockoutDatetimeDisplay", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The TimezoneAdjustedLockoutDatetimeDisplay property of the AllVehicleUnlocksView DataObject</summary>
        public virtual System.String TimezoneAdjustedLockoutDatetimeDisplay 
		{
			get	{ return String.IsNullOrEmpty(_timezoneAdjustedLockoutDatetimeDisplay) ? null : _timezoneAdjustedLockoutDatetimeDisplay; }
			
			
			set
			{
				SetTimezoneAdjustedLockoutDatetimeDisplayValue(value);
			}
		}		
			
			
		public virtual void SetTimezoneAdjustedUnlockDatetimeValue(System.DateTime valueToSet)
		{
			SetTimezoneAdjustedUnlockDatetimeValue(valueToSet, true, true);
		}

		public virtual void SetTimezoneAdjustedUnlockDatetimeValue(System.DateTime valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null || valueToSet == DateTime.MinValue) 
			{
				if (_timezoneAdjustedUnlockDatetime != DateTime.MinValue.ToUniversalTime())
				{
					_timezoneAdjustedUnlockDatetime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("TimezoneAdjustedUnlockDatetime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_timezoneAdjustedUnlockDatetime != DateTime.MaxValue.ToUniversalTime())
				{
					_timezoneAdjustedUnlockDatetime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("TimezoneAdjustedUnlockDatetime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_timezoneAdjustedUnlockDatetime != valueToSet ||
                (_timezoneAdjustedUnlockDatetime != null && ((DateTime)_timezoneAdjustedUnlockDatetime).Kind == DateTimeKind.Unspecified))
			{
				_timezoneAdjustedUnlockDatetime = DateTime.SpecifyKind(valueToSet, DateTimeKind.Local);
				OnPropertyChanged("TimezoneAdjustedUnlockDatetime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Timezone Adjusted Unlock Datetime property of the AllVehicleUnlocksView DataObject</summary>
        public virtual System.DateTime TimezoneAdjustedUnlockDatetime 
		{
			get	{ return _timezoneAdjustedUnlockDatetime;}
			
			
			set
			{
				SetTimezoneAdjustedUnlockDatetimeValue(value);
			}
		}		
			
			
		public virtual void SetTimezoneAdjustedUnlockDatetimeDisplayValue(System.String valueToSet)
		{
			SetTimezoneAdjustedUnlockDatetimeDisplayValue(valueToSet, true, true);
		}

		public virtual void SetTimezoneAdjustedUnlockDatetimeDisplayValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_timezoneAdjustedUnlockDatetimeDisplay != valueToSet)
			{
				_timezoneAdjustedUnlockDatetimeDisplay = valueToSet;

				OnPropertyChanged("TimezoneAdjustedUnlockDatetimeDisplay", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The TimezoneAdjustedUnlockDatetimeDisplay property of the AllVehicleUnlocksView DataObject</summary>
        public virtual System.String TimezoneAdjustedUnlockDatetimeDisplay 
		{
			get	{ return String.IsNullOrEmpty(_timezoneAdjustedUnlockDatetimeDisplay) ? null : _timezoneAdjustedUnlockDatetimeDisplay; }
			
			
			set
			{
				SetTimezoneAdjustedUnlockDatetimeDisplayValue(value);
			}
		}		
			
		
		/// <summary> The UnlockDateTime property of the AllVehicleUnlocksView DataObject</summary>
        public virtual System.String UnlockDateTime 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return TimezoneAdjustedUnlockDatetime.ToString();				
			}
			
		}		
			
			
		public virtual void SetUnlockedByValue(System.String valueToSet)
		{
			SetUnlockedByValue(valueToSet, true, true);
		}

		public virtual void SetUnlockedByValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_unlockedBy != valueToSet)
			{
				_unlockedBy = valueToSet;

				OnPropertyChanged("UnlockedBy", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The UnlockedBy property of the AllVehicleUnlocksView DataObject</summary>
        public virtual System.String UnlockedBy 
		{
			get	{ return String.IsNullOrEmpty(_unlockedBy) ? null : _unlockedBy; }
			
			
			set
			{
				SetUnlockedByValue(value);
			}
		}		
			
			
		public virtual void SetVehicleLockoutIdValue(System.Guid valueToSet)
		{
			SetVehicleLockoutIdValue(valueToSet, true, true);
		}

		public virtual void SetVehicleLockoutIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleLockoutId != valueToSet)
			{
				_vehicleLockoutId = valueToSet;

				OnPropertyChanged("VehicleLockoutId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The VehicleLockoutId property of the AllVehicleUnlocksView DataObject</summary>
        public virtual System.Guid VehicleLockoutId 
		{
			get	{ return _vehicleLockoutId;}
			
			
			set
			{
				SetVehicleLockoutIdValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			if (propertyName == "TimezoneAdjustedLockoutDatetime")
			{
				OnPropertyChanged("LockoutDateTime", true, dirtyHandlerOn);
			}

			if (propertyName == "TimezoneAdjustedUnlockDatetime")
			{
				OnPropertyChanged("UnlockDateTime", true, dirtyHandlerOn);
			}

			
			// Push the notification to related objects
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<AllVehicleUnlocksViewDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is AllVehicleUnlocksViewDataObject))
				return false;

			var p = (AllVehicleUnlocksViewDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.DealerId == p.DealerId;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.TimezoneAdjustedLockoutDatetime == p.TimezoneAdjustedLockoutDatetime;
			fieldsComparison &= this.TimezoneAdjustedUnlockDatetime == p.TimezoneAdjustedUnlockDatetime;
			fieldsComparison &= this.UnlockedBy == p.UnlockedBy;
			fieldsComparison &= this.VehicleLockoutId == p.VehicleLockoutId;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// TimezoneAdjustedUnlockDatetime is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._timezoneAdjustedUnlockDatetime_WithTimezoneOffset != null)
			{
				this.TimezoneAdjustedUnlockDatetime = ((DateTimeOffset)this._timezoneAdjustedUnlockDatetime_WithTimezoneOffset).DateTime;
			}
			// TimezoneAdjustedLockoutDatetime is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._timezoneAdjustedLockoutDatetime_WithTimezoneOffset != null)
			{
				this.TimezoneAdjustedLockoutDatetime = ((DateTimeOffset)this._timezoneAdjustedLockoutDatetime_WithTimezoneOffset).DateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class AllVehicleUnlocksViewCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public AllVehicleUnlocksViewCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public AllVehicleUnlocksViewCollectionContainer()
		{
		}
		
		public AllVehicleUnlocksViewCollectionContainer Construct(DataObjectCollection<AllVehicleUnlocksViewDataObject> allVehicleUnlocksViewItems)
        {
            if (allVehicleUnlocksViewItems == null)
                return this;
				
			this.PrimaryKeys = allVehicleUnlocksViewItems.Select(c => c.PrimaryKey).ToList();
            if (allVehicleUnlocksViewItems.ObjectsDataSet == null)
            {
                allVehicleUnlocksViewItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = allVehicleUnlocksViewItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = allVehicleUnlocksViewItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<AllVehicleUnlocksViewDataObject> ExtractAllVehicleUnlocksViewItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<AllVehicleUnlocksViewDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<AllVehicleUnlocksViewDataObject>(typeof(AllVehicleUnlocksViewDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class AllVehicleUnlocksViewContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public AllVehicleUnlocksViewContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual AllVehicleUnlocksViewContainer Construct(AllVehicleUnlocksViewDataObject allVehicleUnlocksView, bool includeDirtyObjectsOnly = false)
		{
            if (allVehicleUnlocksView == null)
                return this;

			this.PrimaryKey = allVehicleUnlocksView.PrimaryKey;
			
            if (allVehicleUnlocksView.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(allVehicleUnlocksView);
            }

			if(allVehicleUnlocksView.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity AllVehicleUnlocksView", "Unable to set a dataset to the entity. Container may not be initialized", "AllVehicleUnlocksViewDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : All Vehicle Unlocks View");
			}

			if(allVehicleUnlocksView.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in AllVehicleUnlocksViewDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "AllVehicleUnlocksViewDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in All Vehicle Unlocks ViewDataObject");
			}
			this.InternalObjectId = (int) allVehicleUnlocksView.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? allVehicleUnlocksView.ObjectsDataSet.CloneDirtyObjects() : allVehicleUnlocksView.ObjectsDataSet;

			return this;
		}
		
		public AllVehicleUnlocksViewDataObject ExtractAllVehicleUnlocksView()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<AllVehicleUnlocksViewDataObject>(typeof(AllVehicleUnlocksViewDataObject), InternalObjectId);
			
			return result;
        }	
	}

}