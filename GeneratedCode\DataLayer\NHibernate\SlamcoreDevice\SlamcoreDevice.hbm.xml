﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMSlamcoreDevice" 
		table="[SlamcoreDevice]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="IPAddress" >
            <column name="`IPAddress`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 
		<property name="LastConnectedDateTime" >
            <column name="`LastConnectedDateTime`" sql-type="datetime" not-null="false" />
        </property> 
		<property name="Name" >
            <column name="`Name`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 
		<property name="SerialNo" >
            <column name="`SerialNo`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 
		<property name="Status" >
            <column name="`Status`" sql-type="int" not-null="true" />
        </property> 
		<property name="UpdateRate" >
            <column name="`UpdateRate`" sql-type="int" not-null="true" />
        </property> 

		
		<!-- many-to-one Customer -->
		<property name="CustomerId" type="System.Guid" not-null="true" formula = "[CustomerId]"></property>  
		<many-to-one name="Customer"  > 
			<column name="`CustomerId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
 


		<property name="SlamcoreAwareAuthenticationDetailsId" type="System.Guid" not-null = "false" formula = "[SlamcoreAwareAuthenticationDetailsId]"></property>
 
		<!-- SlamcoreDevice.SlamcoreAwareAuthenticationDetails one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="SlamcoreAwareAuthenticationDetails" unique="true"  > 
				<column name="`SlamcoreAwareAuthenticationDetailsId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 

		<property name="VehicleId" type="System.Guid" not-null = "false" formula = "[VehicleId]"></property>
 
		<!-- SlamcoreDevice.Vehicle one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="Vehicle" unique="true"  > 
				<column name="`VehicleId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 

		<property name="SlamcoreAPIKeyId" type="System.Guid" not-null = "false" formula = "[SlamcoreAPIKeyId]"></property>
 
		<!-- SlamcoreDevice.SlamcoreAPIKey one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="SlamcoreAPIKey" unique="true"  > 
				<column name="`SlamcoreAPIKeyId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 


		<bag
			name = "SlamcoreDeviceHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`SlamcoreDeviceId`" />
			</key>
			<one-to-many class = "ORMSlamcoreDeviceHistory" />
		</bag>
		<bag
			name = "SlamcorePedestrianDetectionItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`SlamcoreDeviceId`" />
			</key>
			<one-to-many class = "ORMSlamcorePedestrianDetection" />
		</bag>
		<bag
			name = "SlamcoreDeviceConnectionViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`SlamcoreDeviceId`" />
			</key>
			<one-to-many class = "ORMSlamcoreDeviceConnectionView" />
		</bag>
		<bag
			name = "VehicleSlamcoreLocationHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`SlamcoreDeviceId`" />
			</key>
			<one-to-many class = "ORMVehicleSlamcoreLocationHistory" />
		</bag>

    </class> 

</hibernate-mapping>