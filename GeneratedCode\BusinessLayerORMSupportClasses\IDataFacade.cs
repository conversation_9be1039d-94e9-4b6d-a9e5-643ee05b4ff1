﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.BusinessLayer.ORMSupportClasses
{
    public interface IDataFacade
    {
		IDataProvider<AccessGroupDataObject> AccessGroupDataProvider { get; }
		IDataProvider<AccessGroupTemplateDataObject> AccessGroupTemplateDataProvider { get; }
		IDataProvider<AccessGroupToSiteDataObject> AccessGroupToSiteDataProvider { get; }
		IDataProvider<AlertDataObject> AlertDataProvider { get; }
		IDataProvider<AlertHistoryDataObject> AlertHistoryDataProvider { get; }
		IDataProvider<AlertSubscriptionDataObject> AlertSubscriptionDataProvider { get; }
		IDataProvider<AlertSummaryStoreProcedureDataObject> AlertSummaryStoreProcedureDataProvider { get; }
		IDataProvider<AllChecklistResultViewDataObject> AllChecklistResultViewDataProvider { get; }
		IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject> AllDriverAccessAbuseStoreProcedureDataProvider { get; }
		IDataProvider<AllEmailSubscriptionStoreProcedureDataObject> AllEmailSubscriptionStoreProcedureDataProvider { get; }
		IDataProvider<AllImpactsViewDataObject> AllImpactsViewDataProvider { get; }
		IDataProvider<AllLicenseExpiryViewDataObject> AllLicenseExpiryViewDataProvider { get; }
		IDataProvider<AllMessageHistoryStoreProcedureDataObject> AllMessageHistoryStoreProcedureDataProvider { get; }
		IDataProvider<AllUserSummaryStoreProcedureDataObject> AllUserSummaryStoreProcedureDataProvider { get; }
		IDataProvider<AllVehicleCalibrationFilterDataObject> AllVehicleCalibrationFilterDataProvider { get; }
		IDataProvider<AllVehicleCalibrationStoreProcedureDataObject> AllVehicleCalibrationStoreProcedureDataProvider { get; }
		IDataProvider<AllVehicleUnlocksViewDataObject> AllVehicleUnlocksViewDataProvider { get; }
		IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject> AllVORSessionsPerVehicleStoreProcedureDataProvider { get; }
		IDataProvider<AllVORStatusStoreProcedureDataObject> AllVORStatusStoreProcedureDataProvider { get; }
		IDataProvider<BroadcastMessageDataObject> BroadcastMessageDataProvider { get; }
		IDataProvider<BroadcastMessageHistoryDataObject> BroadcastMessageHistoryDataProvider { get; }
		IDataProvider<BroadcastMessageHistoryFilterDataObject> BroadcastMessageHistoryFilterDataProvider { get; }
		IDataProvider<CanruleDataObject> CanruleDataProvider { get; }
		IDataProvider<CanruleDetailsDataObject> CanruleDetailsDataProvider { get; }
		IDataProvider<CardDataObject> CardDataProvider { get; }
		IDataProvider<CardToCardAccessDataObject> CardToCardAccessDataProvider { get; }
		IDataProvider<CategoryTemplateDataObject> CategoryTemplateDataProvider { get; }
		IDataProvider<ChecklistDetailDataObject> ChecklistDetailDataProvider { get; }
		IDataProvider<ChecklistFailurePerVechicleViewDataObject> ChecklistFailurePerVechicleViewDataProvider { get; }
		IDataProvider<ChecklistFailureViewDataObject> ChecklistFailureViewDataProvider { get; }
		IDataProvider<ChecklistResultDataObject> ChecklistResultDataProvider { get; }
		IDataProvider<ChecklistSettingsDataObject> ChecklistSettingsDataProvider { get; }
		IDataProvider<ChecklistStatusViewDataObject> ChecklistStatusViewDataProvider { get; }
		IDataProvider<ContactPersonInformationDataObject> ContactPersonInformationDataProvider { get; }
		IDataProvider<CountryDataObject> CountryDataProvider { get; }
		IDataProvider<CurrentDriverStatusChartViewDataObject> CurrentDriverStatusChartViewDataProvider { get; }
		IDataProvider<CurrentStatusCombinedViewDataObject> CurrentStatusCombinedViewDataProvider { get; }
		IDataProvider<CurrentStatusDriverViewDataObject> CurrentStatusDriverViewDataProvider { get; }
		IDataProvider<CurrentStatusVehicleViewDataObject> CurrentStatusVehicleViewDataProvider { get; }
		IDataProvider<CurrentVehicleStatusChartViewDataObject> CurrentVehicleStatusChartViewDataProvider { get; }
		IDataProvider<CustomerDataObject> CustomerDataProvider { get; }
		IDataProvider<CustomerAuditDataObject> CustomerAuditDataProvider { get; }
		IDataProvider<CustomerFeatureSubscriptionDataObject> CustomerFeatureSubscriptionDataProvider { get; }
		IDataProvider<CustomerModelDataObject> CustomerModelDataProvider { get; }
		IDataProvider<CustomerPreOperationalChecklistTemplateDataObject> CustomerPreOperationalChecklistTemplateDataProvider { get; }
		IDataProvider<CustomerSnapshotDataObject> CustomerSnapshotDataProvider { get; }
		IDataProvider<CustomerSSODetailDataObject> CustomerSSODetailDataProvider { get; }
		IDataProvider<CustomerToModelDataObject> CustomerToModelDataProvider { get; }
		IDataProvider<CustomerToPersonViewDataObject> CustomerToPersonViewDataProvider { get; }
		IDataProvider<DashboardDriverCardStoreProcedureDataObject> DashboardDriverCardStoreProcedureDataProvider { get; }
		IDataProvider<DashboardDriverCardViewDataObject> DashboardDriverCardViewDataProvider { get; }
		IDataProvider<DashboardFilterDataObject> DashboardFilterDataProvider { get; }
		IDataProvider<DashboardFilterMoreFieldsDataObject> DashboardFilterMoreFieldsDataProvider { get; }
		IDataProvider<DashboardVehicleCardStoreProcedureDataObject> DashboardVehicleCardStoreProcedureDataProvider { get; }
		IDataProvider<DashboardVehicleCardViewDataObject> DashboardVehicleCardViewDataProvider { get; }
		IDataProvider<DealerDataObject> DealerDataProvider { get; }
		IDataProvider<DealerConfigurationDataObject> DealerConfigurationDataProvider { get; }
		IDataProvider<DealerDriverDataObject> DealerDriverDataProvider { get; }
		IDataProvider<DealerFeatureSubscriptionDataObject> DealerFeatureSubscriptionDataProvider { get; }
		IDataProvider<DepartmentDataObject> DepartmentDataProvider { get; }
		IDataProvider<DepartmentChecklistDataObject> DepartmentChecklistDataProvider { get; }
		IDataProvider<DepartmentHourSettingsDataObject> DepartmentHourSettingsDataProvider { get; }
		IDataProvider<DepartmentVehicleMasterCardAccessDataObject> DepartmentVehicleMasterCardAccessDataProvider { get; }
		IDataProvider<DepartmentVehicleNormalCardAccessDataObject> DepartmentVehicleNormalCardAccessDataProvider { get; }
		IDataProvider<DetailedSessionViewDataObject> DetailedSessionViewDataProvider { get; }
		IDataProvider<DetailedVORSessionStoreProcedureDataObject> DetailedVORSessionStoreProcedureDataProvider { get; }
		IDataProvider<DriverDataObject> DriverDataProvider { get; }
		IDataProvider<DriverAccessAbuseFilterDataObject> DriverAccessAbuseFilterDataProvider { get; }
		IDataProvider<DriverLicenseExpiryStoreProcedureDataObject> DriverLicenseExpiryStoreProcedureDataProvider { get; }
		IDataProvider<DriverLicenseExpiryViewDataObject> DriverLicenseExpiryViewDataProvider { get; }
		IDataProvider<DriverProficiencyViewDataObject> DriverProficiencyViewDataProvider { get; }
		IDataProvider<EmailDataObject> EmailDataProvider { get; }
		IDataProvider<EmailGroupsDataObject> EmailGroupsDataProvider { get; }
		IDataProvider<EmailGroupsToPersonDataObject> EmailGroupsToPersonDataProvider { get; }
		IDataProvider<EmailSubscriptionReportFilterDataObject> EmailSubscriptionReportFilterDataProvider { get; }
		IDataProvider<ExportJobStatusDataObject> ExportJobStatusDataProvider { get; }
		IDataProvider<FeatureSubscriptionsFilterDataObject> FeatureSubscriptionsFilterDataProvider { get; }
		IDataProvider<FeatureSubscriptionTemplateDataObject> FeatureSubscriptionTemplateDataProvider { get; }
		IDataProvider<FirmwareDataObject> FirmwareDataProvider { get; }
		IDataProvider<FloorPlanDataObject> FloorPlanDataProvider { get; }
		IDataProvider<FloorZonesDataObject> FloorZonesDataProvider { get; }
		IDataProvider<GeneralProductivityPerDriverViewLatestDataObject> GeneralProductivityPerDriverViewLatestDataProvider { get; }
		IDataProvider<GeneralProductivityPerVehicleViewDataObject> GeneralProductivityPerVehicleViewDataProvider { get; }
		IDataProvider<GeneralProductivityReportFilterDataObject> GeneralProductivityReportFilterDataProvider { get; }
		IDataProvider<GeneralProductivityViewDataObject> GeneralProductivityViewDataProvider { get; }
		IDataProvider<GO2FAConfigurationDataObject> GO2FAConfigurationDataProvider { get; }
		IDataProvider<GOChangeDeltaDataObject> GOChangeDeltaDataProvider { get; }
		IDataProvider<GOGroupDataObject> GOGroupDataProvider { get; }
		IDataProvider<GOGroupRoleDataObject> GOGroupRoleDataProvider { get; }
		IDataProvider<GOLoginHistoryDataObject> GOLoginHistoryDataProvider { get; }
		IDataProvider<GORoleDataObject> GORoleDataProvider { get; }
		IDataProvider<GOSecurityTokensDataObject> GOSecurityTokensDataProvider { get; }
		IDataProvider<GOTaskDataObject> GOTaskDataProvider { get; }
		IDataProvider<GOUserDataObject> GOUserDataProvider { get; }
		IDataProvider<GOUser2FADataObject> GOUser2FADataProvider { get; }
		IDataProvider<GOUserDepartmentDataObject> GOUserDepartmentDataProvider { get; }
		IDataProvider<GOUserGroupDataObject> GOUserGroupDataProvider { get; }
		IDataProvider<GOUserRoleDataObject> GOUserRoleDataProvider { get; }
		IDataProvider<GoUserToCustomerDataObject> GoUserToCustomerDataProvider { get; }
		IDataProvider<GPSHistoryDataObject> GPSHistoryDataProvider { get; }
		IDataProvider<HelpDataObject> HelpDataProvider { get; }
		IDataProvider<HireDeHireReportFilterDataObject> HireDeHireReportFilterDataProvider { get; }
		IDataProvider<ImpactDataObject> ImpactDataProvider { get; }
		IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject> ImpactFrequencyPerTimeSlotViewDataProvider { get; }
		IDataProvider<ImpactFrequencyPerWeekDayViewDataObject> ImpactFrequencyPerWeekDayViewDataProvider { get; }
		IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject> ImpactFrequencyPerWeekMonthViewDataProvider { get; }
		IDataProvider<ImpactReportFilterDataObject> ImpactReportFilterDataProvider { get; }
		IDataProvider<ImpactsForVehicleViewDataObject> ImpactsForVehicleViewDataProvider { get; }
		IDataProvider<ImportJobBatchDataObject> ImportJobBatchDataProvider { get; }
		IDataProvider<ImportJobLogDataObject> ImportJobLogDataProvider { get; }
		IDataProvider<ImportJobStatusDataObject> ImportJobStatusDataProvider { get; }
		IDataProvider<IncompletedChecklistViewDataObject> IncompletedChecklistViewDataProvider { get; }
		IDataProvider<InspectionDataObject> InspectionDataProvider { get; }
		IDataProvider<IOFIELDDataObject> IOFIELDDataProvider { get; }
		IDataProvider<IoTDeviceMessageCacheDataObject> IoTDeviceMessageCacheDataProvider { get; }
		IDataProvider<LicenceDetailDataObject> LicenceDetailDataProvider { get; }
		IDataProvider<LicenseByModelDataObject> LicenseByModelDataProvider { get; }
		IDataProvider<LicenseExpiryReportFilterDataObject> LicenseExpiryReportFilterDataProvider { get; }
		IDataProvider<LoggedHoursVersusSeatHoursViewDataObject> LoggedHoursVersusSeatHoursViewDataProvider { get; }
		IDataProvider<MachineUnlockReportFilterDataObject> MachineUnlockReportFilterDataProvider { get; }
		IDataProvider<MainDashboardFilterDataObject> MainDashboardFilterDataProvider { get; }
		IDataProvider<MessageHistoryDataObject> MessageHistoryDataProvider { get; }
		IDataProvider<ModelDataObject> ModelDataProvider { get; }
		IDataProvider<ModelVehicleMasterCardAccessDataObject> ModelVehicleMasterCardAccessDataProvider { get; }
		IDataProvider<ModelVehicleNormalCardAccessDataObject> ModelVehicleNormalCardAccessDataProvider { get; }
		IDataProvider<ModuleDataObject> ModuleDataProvider { get; }
		IDataProvider<ModuleHistoryDataObject> ModuleHistoryDataProvider { get; }
		IDataProvider<NetworkSettingsDataObject> NetworkSettingsDataProvider { get; }
		IDataProvider<OnDemandAuthorisationFilterDataObject> OnDemandAuthorisationFilterDataProvider { get; }
		IDataProvider<OnDemandAuthorisationStoreProcedureDataObject> OnDemandAuthorisationStoreProcedureDataProvider { get; }
		IDataProvider<OnDemandSessionDataObject> OnDemandSessionDataProvider { get; }
		IDataProvider<OnDemandSettingsDataObject> OnDemandSettingsDataProvider { get; }
		IDataProvider<PedestrianDetectionHistoryDataObject> PedestrianDetectionHistoryDataProvider { get; }
		IDataProvider<PedestrianDetectionHistoryFilterDataObject> PedestrianDetectionHistoryFilterDataProvider { get; }
		IDataProvider<PermissionDataObject> PermissionDataProvider { get; }
		IDataProvider<PersonDataObject> PersonDataProvider { get; }
		IDataProvider<PersonAllocationDataObject> PersonAllocationDataProvider { get; }
		IDataProvider<PersonChecklistLanguageSettingsDataObject> PersonChecklistLanguageSettingsDataProvider { get; }
		IDataProvider<PersonToDepartmentVehicleMasterAccessViewDataObject> PersonToDepartmentVehicleMasterAccessViewDataProvider { get; }
		IDataProvider<PersonToDepartmentVehicleNormalAccessViewDataObject> PersonToDepartmentVehicleNormalAccessViewDataProvider { get; }
		IDataProvider<PersonToModelVehicleMasterAccessViewDataObject> PersonToModelVehicleMasterAccessViewDataProvider { get; }
		IDataProvider<PersonToModelVehicleNormalAccessViewDataObject> PersonToModelVehicleNormalAccessViewDataProvider { get; }
		IDataProvider<PersonToPerVehicleMasterAccessViewDataObject> PersonToPerVehicleMasterAccessViewDataProvider { get; }
		IDataProvider<PersonToPerVehicleNormalAccessViewDataObject> PersonToPerVehicleNormalAccessViewDataProvider { get; }
		IDataProvider<PersonToSiteVehicleMasterAccessViewDataObject> PersonToSiteVehicleMasterAccessViewDataProvider { get; }
		IDataProvider<PersonToSiteVehicleNormalAccessViewDataObject> PersonToSiteVehicleNormalAccessViewDataProvider { get; }
		IDataProvider<PerVehicleMasterCardAccessDataObject> PerVehicleMasterCardAccessDataProvider { get; }
		IDataProvider<PerVehicleNormalCardAccessDataObject> PerVehicleNormalCardAccessDataProvider { get; }
		IDataProvider<PreOperationalChecklistDataObject> PreOperationalChecklistDataProvider { get; }
		IDataProvider<PreOpReportFilterDataObject> PreOpReportFilterDataProvider { get; }
		IDataProvider<ProficiencyCombinedViewDataObject> ProficiencyCombinedViewDataProvider { get; }
		IDataProvider<ProficiencyReportFilterDataObject> ProficiencyReportFilterDataProvider { get; }
		IDataProvider<PSTATDetailsDataObject> PSTATDetailsDataProvider { get; }
		IDataProvider<RegionDataObject> RegionDataProvider { get; }
		IDataProvider<ReportSubscriptionDataObject> ReportSubscriptionDataProvider { get; }
		IDataProvider<ReportTypeDataObject> ReportTypeDataProvider { get; }
		IDataProvider<RevisionDataObject> RevisionDataProvider { get; }
		IDataProvider<ServiceSettingsDataObject> ServiceSettingsDataProvider { get; }
		IDataProvider<SessionDataObject> SessionDataProvider { get; }
		IDataProvider<SessionDetailsDataObject> SessionDetailsDataProvider { get; }
		IDataProvider<SiteDataObject> SiteDataProvider { get; }
		IDataProvider<SiteFloorPlanDataObject> SiteFloorPlanDataProvider { get; }
		IDataProvider<SiteVehicleMasterCardAccessDataObject> SiteVehicleMasterCardAccessDataProvider { get; }
		IDataProvider<SiteVehicleNormalCardAccessDataObject> SiteVehicleNormalCardAccessDataProvider { get; }
		IDataProvider<SlamcoreAPIKeyDataObject> SlamcoreAPIKeyDataProvider { get; }
		IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject> SlamcoreAwareAuthenticationDetailsDataProvider { get; }
		IDataProvider<SlamcoreDeviceDataObject> SlamcoreDeviceDataProvider { get; }
		IDataProvider<SlamcoreDeviceConnectionViewDataObject> SlamcoreDeviceConnectionViewDataProvider { get; }
		IDataProvider<SlamcoreDeviceFilterDataObject> SlamcoreDeviceFilterDataProvider { get; }
		IDataProvider<SlamcoreDeviceHistoryDataObject> SlamcoreDeviceHistoryDataProvider { get; }
		IDataProvider<SlamcorePedestrianDetectionDataObject> SlamcorePedestrianDetectionDataProvider { get; }
		IDataProvider<SnapshotDataObject> SnapshotDataProvider { get; }
		IDataProvider<SynchronizationStatusReportFilterDataObject> SynchronizationStatusReportFilterDataProvider { get; }
		IDataProvider<TagDataObject> TagDataProvider { get; }
		IDataProvider<TimezoneDataObject> TimezoneDataProvider { get; }
		IDataProvider<TodaysImpactStoreProcedureDataObject> TodaysImpactStoreProcedureDataProvider { get; }
		IDataProvider<TodaysImpactViewDataObject> TodaysImpactViewDataProvider { get; }
		IDataProvider<TodaysPreopCheckStoreProcedureDataObject> TodaysPreopCheckStoreProcedureDataProvider { get; }
		IDataProvider<TodaysPreopCheckViewDataObject> TodaysPreopCheckViewDataProvider { get; }
		IDataProvider<UnitSummaryReportDataObject> UnitSummaryReportDataProvider { get; }
		IDataProvider<UnitSummaryStoreProcedureDataObject> UnitSummaryStoreProcedureDataProvider { get; }
		IDataProvider<UnitUnutilisationStoreProcedureDataObject> UnitUnutilisationStoreProcedureDataProvider { get; }
		IDataProvider<UnitUtilisationCombinedViewDataObject> UnitUtilisationCombinedViewDataProvider { get; }
		IDataProvider<UnitUtilisationStoreProcedureDataObject> UnitUtilisationStoreProcedureDataProvider { get; }
		IDataProvider<UpdateFirmwareRequestDataObject> UpdateFirmwareRequestDataProvider { get; }
		IDataProvider<UploadLogoRequestDataObject> UploadLogoRequestDataProvider { get; }
		IDataProvider<VehicleDataObject> VehicleDataProvider { get; }
		IDataProvider<VehicleAlertSubscriptionDataObject> VehicleAlertSubscriptionDataProvider { get; }
		IDataProvider<VehicleBroadcastMessageDataObject> VehicleBroadcastMessageDataProvider { get; }
		IDataProvider<VehicleDiagnosticDataObject> VehicleDiagnosticDataProvider { get; }
		IDataProvider<VehicleGPSDataObject> VehicleGPSDataProvider { get; }
		IDataProvider<VehicleHireDehireHistoryDataObject> VehicleHireDehireHistoryDataProvider { get; }
		IDataProvider<VehicleHireDehireSynchronizationOptionsDataObject> VehicleHireDehireSynchronizationOptionsDataProvider { get; }
		IDataProvider<VehicleLastGPSLocationViewDataObject> VehicleLastGPSLocationViewDataProvider { get; }
		IDataProvider<VehicleLockoutDataObject> VehicleLockoutDataProvider { get; }
		IDataProvider<VehicleOtherSettingsDataObject> VehicleOtherSettingsDataProvider { get; }
		IDataProvider<VehicleProficiencyViewDataObject> VehicleProficiencyViewDataProvider { get; }
		IDataProvider<VehicleSessionlessImpactDataObject> VehicleSessionlessImpactDataProvider { get; }
		IDataProvider<VehicleSlamcoreLocationHistoryDataObject> VehicleSlamcoreLocationHistoryDataProvider { get; }
		IDataProvider<VehiclesPerModelReportDataObject> VehiclesPerModelReportDataProvider { get; }
		IDataProvider<VehicleSupervisorsViewDataObject> VehicleSupervisorsViewDataProvider { get; }
		IDataProvider<VehicleToPreOpChecklistViewDataObject> VehicleToPreOpChecklistViewDataProvider { get; }
		IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> VehicleUtilizationLastTwelveHoursStoreProcedureDataProvider { get; }
		IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject> VehicleUtilizationLastTwelveHoursViewDataProvider { get; }
		IDataProvider<VORReportCombinedViewDataObject> VORReportCombinedViewDataProvider { get; }
		IDataProvider<VORReportFilterDataObject> VORReportFilterDataProvider { get; }
		IDataProvider<VORSettingHistoryDataObject> VORSettingHistoryDataProvider { get; }
		IDataProvider<WebsiteRoleDataObject> WebsiteRoleDataProvider { get; }
		IDataProvider<WebsiteUserDataObject> WebsiteUserDataProvider { get; }
		IDataProvider<ZoneCoordinatesDataObject> ZoneCoordinatesDataProvider { get; }
  }
}
