﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Data.DataProviders.Dispatchers
{
    public class VehicleLockoutDataProviderDispatcher : IDataProviderDispatcher<VehicleLockoutDataObject>
    {
		protected IServiceProvider _serviceProvider;
		
		public VehicleLockoutDataProviderDispatcher(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
		
		protected IDataProvider<SessionDataObject> sessionDataProvider => _serviceProvider.GetService<IDataProvider<SessionDataObject>>();
		protected IDataProvider<AllVehicleUnlocksViewDataObject> allVehicleUnlocksViewDataProvider => _serviceProvider.GetService<IDataProvider<AllVehicleUnlocksViewDataObject>>();
		protected IDataProvider<GOUserDataObject> gOUserDataProvider => _serviceProvider.GetService<IDataProvider<GOUserDataObject>>();
		protected IDataProvider<DriverDataObject> driverDataProvider => _serviceProvider.GetService<IDataProvider<DriverDataObject>>();
		protected IDataProvider<VehicleDataObject> vehicleDataProvider => _serviceProvider.GetService<IDataProvider<VehicleDataObject>>();

        public async Task DispatchForEntityAsync(VehicleLockoutDataObject entity, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("VehicleLockout", parameters);

            foreach (var include in includes)
            {
	
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
                  case "session":
							{
								// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Session"))
									break;

								if (entity.SessionId != null) 
								{
									try
									{
										var objectToFetch = await sessionDataProvider.GetAsync(new SessionDataObject((System.Guid)entity.SessionId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "allvehicleunlocksviewitems":
							{
								// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("AllVehicleUnlocksViewItems"))
									break;

								try
								{
									var objectToFetch = await allVehicleUnlocksViewDataProvider.GetCollectionAsync(null, "VehicleLockoutId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "gouser":
							{
								// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("GOUser"))
									break;

								if (entity.GOUserId != null) 
								{
									try
									{
										var objectToFetch = await gOUserDataProvider.GetAsync(new GOUserDataObject((System.Guid)entity.GOUserId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "driver":
							{
								// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Driver"))
									break;

								if (entity.DriverId != null) 
								{
									try
									{
										var objectToFetch = await driverDataProvider.GetAsync(new DriverDataObject((System.Guid)entity.DriverId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "vehicle":
							{
								// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Vehicle"))
									break;

								if (entity.VehicleId != null) 
								{
									try
									{
										var objectToFetch = await vehicleDataProvider.GetAsync(new VehicleDataObject((System.Guid)entity.VehicleId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
						default:
							throw new ApplicationException("VehicleLockout Entity has no relation named " + relation);
					}
          }        
		}

        public async Task DispatchForEntityCollectionAsync(IEnumerable<VehicleLockoutDataObject> entities, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("VehicleLockout", parameters);

            foreach (var include in includes)
            {
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
						case "session":
                        {
							// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Session"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.SessionId != null).Select(e => (System.Guid)e.SessionId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await sessionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "allvehicleunlocksviewitems":
                        {
							// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("AllVehicleUnlocksViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await allVehicleUnlocksViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.VehicleLockoutId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "gouser":
                        {
							// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("GOUser"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.GOUserId != null).Select(e => (System.Guid)e.GOUserId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await gOUserDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "driver":
                        {
							// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Driver"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.DriverId != null).Select(e => (System.Guid)e.DriverId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await driverDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicle":
                        {
							// custom code can implement IPrefetch<ORMVehicleLockout> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Vehicle"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.VehicleId != null).Select(e => (System.Guid)e.VehicleId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
                  default:
                        throw new ApplicationException("VehicleLockout Entity has no relation named " + relation);
					}
            }        
        }
	}
}