﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;

using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DeleteHandlers
{
	public partial class SessionDeleteHandler : DeleteHandlerBase<SessionDataObject>
	{
		
        public SessionDeleteHandler(IServiceProvider serviceProvider, IDataProvider<SessionDataObject> tentityDataProvider, ITransactionProvider transactionProvider, IEntityDataProvider entityDataProvider) : base(serviceProvider, tentityDataProvider, transactionProvider, entityDataProvider)
        {
        }

		public override async Task RippleDeleteAsync(SessionDataObject instance, Parameters parameters, DataProviderDeleteSettings settings)
		{
			// Set resync flag initially so that if any processing is done, it's on the latest copy of the data
			NeedResync = true;

			// Session.ChecklistResults (Protected) (i.e. Unable to delete Session instances because ChecklistResults.Session is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadChecklistResultsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.ChecklistResults);
			}
			// Session.GPSHistoryItems (Protected) (i.e. Unable to delete Session instances because GPSHistoryItems.Session is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadGPSHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.GPSHistoryItems);
			}
			// Session.Impacts (Protected) (i.e. Unable to delete Session instances because Impacts.Session is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadImpactsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.Impacts);
			}
			// Session.PSTATDetailsItems (Protected) (i.e. Unable to delete Session instances because PSTATDetailsItems.Session is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadPSTATDetailsItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.PSTATDetailsItems);
			}
			// Session.SessionDetailsItems (Protected) (i.e. Unable to delete Session instances because SessionDetailsItems.Session is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSessionDetailsItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.SessionDetailsItems);
			}
			// Session.VehicleGPSItems (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleGPSItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.VehicleGPSItems)			
				{					
					if (item.Session != null)
					{	
						item.Session = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// Session.VehicleLockouts (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleLockoutsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.VehicleLockouts)			
				{					
					if (item.Session != null)
					{	
						item.Session = null; 	
						await SaveAsync(item);							
					}
				}
			}
		}
	}
}