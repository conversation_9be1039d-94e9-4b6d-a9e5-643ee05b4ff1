﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'SlamcoreDeviceFilter'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class SlamcoreDeviceFilterDataObject : DashboardFilterDataObject
	{
		#region dependencies

		#endregion

		#region Fields
 
		[JsonProperty ("Status")]
		protected Nullable<SlamcoreStatusEnum> _status;
	
	
		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public SlamcoreDeviceFilterDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public SlamcoreDeviceFilterDataObject(System.Guid id) : base(id)
		{
			this.Initialize(id);
		}

		public SlamcoreDeviceFilterDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public SlamcoreDeviceFilterDataObject Initialize(SlamcoreDeviceFilterDataObject template, bool deepCopy)
		{
			base.Initialize(template, deepCopy);
			this.SetStatusValue(template.Status, false, false);
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual new SlamcoreDeviceFilterDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual new SlamcoreDeviceFilterDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<SlamcoreDeviceFilterDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var slamcoreDeviceFilterSource = sourceObject as SlamcoreDeviceFilterDataObject;

			// If source object is not of type SlamcoreDeviceFilterDataObject, check if of base class type
			if (ReferenceEquals(null, slamcoreDeviceFilterSource))
			{
				// Try to cast to base class and copy base members
				var baseObjectSource = sourceObject as DashboardFilterDataObject;

				if (ReferenceEquals(null, baseObjectSource))
					throw new GOServerException("Wrong type of object");
						
				base.CopyValuesFrom(baseObjectSource, deepCopy);
				return;
			}
				
			base.CopyValuesFrom(sourceObject, deepCopy);
				
			this.SetStatusValue(slamcoreDeviceFilterSource.Status, false, false);
			if (deepCopy)
			{
				this.ObjectsDataSet = slamcoreDeviceFilterSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(slamcoreDeviceFilterSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as SlamcoreDeviceFilterDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {
			base.UpdateRelatedInternalIds(datasetMergingInternalIdMapping);
				
		}

		#endregion
        
		#region Relation properties		
		
		public override void ClearLazyLoadFlags()
		{
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = (await base.GetAllRelatedReferencedObjectsAsync()).ToList();
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = (await base.GetAllRelatedReferencingObjectsAsync()).ToList();
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				Customer == other ||
				(other is CustomerDataObject && (CustomerId != default(System.Guid)) && (CustomerId == (other as CustomerDataObject).Id)) || 
				Department == other ||
				(other is DepartmentDataObject && (DepartmentId != default(System.Guid)) && (DepartmentId == (other as DepartmentDataObject).Id)) || 
				Site == other ||
				(other is SiteDataObject && (SiteId != default(System.Guid)) && (SiteId == (other as SiteDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual new System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetStatusValue(Nullable<SlamcoreStatusEnum> valueToSet)
		{
			SetStatusValue(valueToSet, true, true);
		}

		public virtual void SetStatusValue(Nullable<SlamcoreStatusEnum> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_status != valueToSet)
			{
				_status = valueToSet;

				OnPropertyChanged("Status", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("StatusDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Status property of the SlamcoreDeviceFilter DataObject</summary>
        public virtual Nullable<SlamcoreStatusEnum> Status 
		{
			get	{ return _status;}
			
			
			set
			{
				SetStatusValue(value);
			}
		}		
      public virtual string StatusDisplayString
		{
			get
			{
				if (Status == null)
					return "-";

				return StatusEnumDisplayNameCollection.Where(v => v.Value == Status).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<SlamcoreStatusEnum>> StatusEnumDisplayNameCollection
	    {
	        get
	        {
                return SlamcoreStatusEnumDisplayNames.Items;
	        }
	    }
		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<SlamcoreDeviceFilterDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is SlamcoreDeviceFilterDataObject))
				return false;

			var p = (SlamcoreDeviceFilterDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.Status == p.Status;
			
			fieldsComparison &= base.Compare(obj);
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreDeviceFilterCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public SlamcoreDeviceFilterCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public SlamcoreDeviceFilterCollectionContainer()
		{
		}
		
		public SlamcoreDeviceFilterCollectionContainer Construct(DataObjectCollection<SlamcoreDeviceFilterDataObject> slamcoreDeviceFilterItems)
        {
            if (slamcoreDeviceFilterItems == null)
                return this;
				
			this.PrimaryKeys = slamcoreDeviceFilterItems.Select(c => c.PrimaryKey).ToList();
            if (slamcoreDeviceFilterItems.ObjectsDataSet == null)
            {
                slamcoreDeviceFilterItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = slamcoreDeviceFilterItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = slamcoreDeviceFilterItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<SlamcoreDeviceFilterDataObject> ExtractSlamcoreDeviceFilterItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<SlamcoreDeviceFilterDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<SlamcoreDeviceFilterDataObject>(typeof(SlamcoreDeviceFilterDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreDeviceFilterContainer : DashboardFilterContainer 
	{
		IServiceProvider _serviceProvider;

        [ActivatorUtilitiesConstructor]
		public SlamcoreDeviceFilterContainer(IServiceProvider serviceProvider) : base(serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

	    public virtual SlamcoreDeviceFilterContainer Construct(SlamcoreDeviceFilterDataObject slamcoreDeviceFilter, bool includeDirtyObjectsOnly = false)
        {
			return base.Construct(slamcoreDeviceFilter, includeDirtyObjectsOnly) as SlamcoreDeviceFilterContainer;
		}
		
		public SlamcoreDeviceFilterDataObject ExtractSlamcoreDeviceFilter()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<SlamcoreDeviceFilterDataObject>(typeof(SlamcoreDeviceFilterDataObject), InternalObjectId);
			
			return result;
        }	
	}

}