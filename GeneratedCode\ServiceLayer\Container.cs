﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.DocumentBuilder;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Serialization;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.Tasks;
using FleetXQ.BusinessLayer.Components.Server.Import;				
using FleetXQ.BusinessLayer.Components.Server.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using FleetXQ.Features.Security;
using FleetXQ.Feature.Security.Common;
using FleetXQ.Features.Security.ServiceLayer;
using FleetXQ.Features.Security.DataProviders;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.SAML20;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.OAuth;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Services.Email;
using GenerativeObjects.Infrastructure.Database;
using FleetXQ.Data.DataProviders;
using FleetXQ.Data.DataProviders.Dispatchers;
using FleetXQ.ServiceLayer.IncludeMapping;
using FleetXQ.BusinessLayer;
using FleetXQ.Data.DataProviders.Database;
using I18Next.Net;
using I18Next.Net.Extensions;
using I18Next.Net.Backends;


using FleetXQ.Data.DataProviders.Custom;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.DependencyInjection;
using FleetXQ.Data.DeleteHandlers;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Storage.Common;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Storage;
using Microsoft.Azure.Management.ContainerInstance.Fluent;
using NHibernate.Id.Insert;
using System.Collections.Generic;
using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.LayerSupportClasses.Web;
using GenerativeObjects.Practices.LayerSupportClasses.Features.HostedServices;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.OAuth;

namespace FleetXQ.ServiceLayer
{ 
	public static class ServiceCollectionExtensions
	{
		public static void AddGenerativeObjectsSupport(this Microsoft.Extensions.DependencyInjection.IServiceCollection services)
		{

			services.AddDataObjectsAndDataSetSupport();

            services.AddScoped<IServicePath, ServicePath>();
            services.AddSingleton<INHibernateSessionController, NHibernateSessionController>();	
			services.AddScoped<ObjectGraphOrDataSetSerializationFilterAttribute>();
			services.AddScoped<IHttpUtilities,HttpUtilities>();
            services.AddTransient<IDataTransactionAdapter, NHibernateDataAccessAdapter>();
			services.AddSingleton<IDatabaseAccessor, DatabaseAccessor>();		

			services.AddSingleton<IOAuthProvider, OAuthProvider> ();
			services.AddSingleton<ICryptoHelper, CryptoHelper> ();
			services.AddHostedService<BackgroundTasksProcessor>();
            services.AddSingleton<IBackgroundWorkerQueue, BackgroundWorkerQueue>();

            services.AddSingleton<IApiFilterArgumentBuilder, ApiFilterArgumentBuilder>();

			// Transactions
			{
				// Singleton provider of transactions
				services.AddScoped<ITransactionProvider, DatabaseTransactionProvider>();
			
				// The default request handler data access transaction. Per-thread so that we can manage / detect incomplete transactions
				// IParameterisedTransaction is a lower-level (ORM support) interface that resolves to the same per-thread transaction (so that low-level can see the transaction parameters)
				services.AddScoped<DatabaseDataProviderTransaction>();
				services.AddScoped<IDataProviderTransaction>(x => x.GetRequiredService<DatabaseDataProviderTransaction>());
				services.AddScoped<IParameterisedTransaction>(x => x.GetRequiredService<DatabaseDataProviderTransaction>());
				
				// Custom / client transaction instances
				services.AddTransient<IClientTransaction, DatabaseDataProviderTransaction>();
			}

			// Save dependency resolution
			services.AddTransient<ISaveDependencyResolver, SaveDependencyResolver>();

			// Delete traversal algorithm		
			services.AddTransient<IDataProviderDeleteStrategy, DeleteResolver>();
			services.AddTransient<DeleteStack, DeleteStack>();
			services.AddTransient<DataProviderDeleteSettings,DataProviderDeleteSettings>();
			services.AddTransient<DataProviderGetSettings, DataProviderGetSettings>();
			services.AddTransient<DataProviderSaveSettings, DataProviderSaveSettings>();
			services.AddTransient<DataProviderCountSettings, DataProviderCountSettings>();
			services.AddTransient<DataProviderGetCollectionSettings, DataProviderGetCollectionSettings>();
			
			// Default Delete blockage explanation 
			services.AddTransient<IDeleteBlockageExplanationProvider, DefaultDeleteBlockageExplainer>();

			// Async / Worker thread context
			services.AddScoped<IThreadContext, ThreadContext>();

			services.AddSingleton(typeof(ILogEngine), typeof(FleetXQ.BusinessLayer.ORMSupportClasses.LogEngine));

			// Server Components
			{		
				services.AddTransient<ISlamcoreVehicleTelemetryExportComponent, SlamcoreVehicleTelemetryExportComponent>();
				services.AddTransient<ISlamcoreVehicleTelemetryExportComponentSurrogate, SlamcoreVehicleTelemetryExportComponentSurrogate>();	
				services.AddTransient<IDriverAuthenticationAPI, DriverAuthenticationAPI>();
				services.AddTransient<IDriverAuthenticationAPISurrogate, DriverAuthenticationAPISurrogate>();	
				services.AddTransient<IDriverImpactAPI, DriverImpactAPI>();
				services.AddTransient<IDriverImpactAPISurrogate, DriverImpactAPISurrogate>();	
				services.AddTransient<IPstatAPI, PstatAPI>();
				services.AddTransient<IPstatAPISurrogate, PstatAPISurrogate>();	
				services.AddTransient<IPreOperationalChecklistImportComponent, PreOperationalChecklistImportComponent>();
				services.AddTransient<IPreOperationalChecklistImportComponentSurrogate, PreOperationalChecklistImportComponentSurrogate>();	
				services.AddTransient<ISlamcoreDeviceExportComponent, SlamcoreDeviceExportComponent>();
				services.AddTransient<ISlamcoreDeviceExportComponentSurrogate, SlamcoreDeviceExportComponentSurrogate>();	
				services.AddTransient<IEmailService, EmailService>();
				services.AddTransient<IEmailServiceSurrogate, EmailServiceSurrogate>();	
				services.AddTransient<IDealerCategoryImportComponent, DealerCategoryImportComponent>();
				services.AddTransient<IDealerCategoryImportComponentSurrogate, DealerCategoryImportComponentSurrogate>();	
				services.AddTransient<IPedestrianDetectionAPI, PedestrianDetectionAPI>();
				services.AddTransient<IPedestrianDetectionAPISurrogate, PedestrianDetectionAPISurrogate>();	
				services.AddTransient<IVehicleAccessImportComponent, VehicleAccessImportComponent>();
				services.AddTransient<IVehicleAccessImportComponentSurrogate, VehicleAccessImportComponentSurrogate>();	
				services.AddTransient<IModuleUtilities, ModuleUtilities>();
				services.AddTransient<IModuleUtilitiesSurrogate, ModuleUtilitiesSurrogate>();	
				services.AddTransient<ISiteAPI, SiteAPI>();
				services.AddTransient<ISiteAPISurrogate, SiteAPISurrogate>();	
				services.AddTransient<IFirmwareUpdate, FirmwareUpdate>();
				services.AddTransient<IFirmwareUpdateSurrogate, FirmwareUpdateSurrogate>();	
				services.AddTransient<IPersonExportComponent, PersonExportComponent>();
				services.AddTransient<IPersonExportComponentSurrogate, PersonExportComponentSurrogate>();	
				services.AddTransient<ICustomerUtilities, CustomerUtilities>();
				services.AddTransient<ICustomerUtilitiesSurrogate, CustomerUtilitiesSurrogate>();	
				services.AddTransient<IDepartmentAPI, DepartmentAPI>();
				services.AddTransient<IDepartmentAPISurrogate, DepartmentAPISurrogate>();	
				services.AddTransient<IPreOpChecklistReportExportComponent, PreOpChecklistReportExportComponent>();
				services.AddTransient<IPreOpChecklistReportExportComponentSurrogate, PreOpChecklistReportExportComponentSurrogate>();	
				services.AddTransient<ISlamcorePedestrianDetectionComponent, SlamcorePedestrianDetectionComponent>();
				services.AddTransient<ISlamcorePedestrianDetectionComponentSurrogate, SlamcorePedestrianDetectionComponentSurrogate>();	
				services.AddTransient<IVehicleOtherSettingsImportComponent, VehicleOtherSettingsImportComponent>();
				services.AddTransient<IVehicleOtherSettingsImportComponentSurrogate, VehicleOtherSettingsImportComponentSurrogate>();	
				services.AddTransient<IModuleParametersAPI, ModuleParametersAPI>();
				services.AddTransient<IModuleParametersAPISurrogate, ModuleParametersAPISurrogate>();	
				services.AddTransient<IMachineUnlockReportExportComponent, MachineUnlockReportExportComponent>();
				services.AddTransient<IMachineUnlockReportExportComponentSurrogate, MachineUnlockReportExportComponentSurrogate>();	
				services.AddTransient<IPedestrianDetectionReportExportComponent, PedestrianDetectionReportExportComponent>();
				services.AddTransient<IPedestrianDetectionReportExportComponentSurrogate, PedestrianDetectionReportExportComponentSurrogate>();	
				services.AddTransient<IPreopXLSXImportComponent, PreopXLSXImportComponent>();
				services.AddTransient<IPreopXLSXImportComponentSurrogate, PreopXLSXImportComponentSurrogate>();	
				services.AddTransient<IGOSecurityProvider, GOSecurityProvider>();
				services.AddTransient<IGOSecurityProviderSurrogate, GOSecurityProviderSurrogate>();	
				services.AddTransient<IGPSMessage, GPSMessage>();
				services.AddTransient<IGPSMessageSurrogate, GPSMessageSurrogate>();	
				services.AddTransient<IUnitUtilisationExportComponent, UnitUtilisationExportComponent>();
				services.AddTransient<IUnitUtilisationExportComponentSurrogate, UnitUtilisationExportComponentSurrogate>();	
				services.AddTransient<IVORStatusAPI, VORStatusAPI>();
				services.AddTransient<IVORStatusAPISurrogate, VORStatusAPISurrogate>();	
				services.AddTransient<IAlertHistoryAPI, AlertHistoryAPI>();
				services.AddTransient<IAlertHistoryAPISurrogate, AlertHistoryAPISurrogate>();	
				services.AddTransient<IServiceCheckReportExportComponent, ServiceCheckReportExportComponent>();
				services.AddTransient<IServiceCheckReportExportComponentSurrogate, ServiceCheckReportExportComponentSurrogate>();	
				services.AddTransient<ICardImportComponent, CardImportComponent>();
				services.AddTransient<ICardImportComponentSurrogate, CardImportComponentSurrogate>();	
				services.AddTransient<IDriverAccessAbuseReportExportComponent, DriverAccessAbuseReportExportComponent>();
				services.AddTransient<IDriverAccessAbuseReportExportComponentSurrogate, DriverAccessAbuseReportExportComponentSurrogate>();	
				services.AddTransient<ISlamcorePathHistoryExportComponent, SlamcorePathHistoryExportComponent>();
				services.AddTransient<ISlamcorePathHistoryExportComponentSurrogate, SlamcorePathHistoryExportComponentSurrogate>();	
				services.AddTransient<IGOReportsHelper, GOReportsHelper>();
				services.AddTransient<IGOReportsHelperSurrogate, GOReportsHelperSurrogate>();	
				services.AddTransient<ISessionAPI, SessionAPI>();
				services.AddTransient<ISessionAPISurrogate, SessionAPISurrogate>();	
				services.AddTransient<IUpdateVehicleLastServiceDateImportComponent, UpdateVehicleLastServiceDateImportComponent>();
				services.AddTransient<IUpdateVehicleLastServiceDateImportComponentSurrogate, UpdateVehicleLastServiceDateImportComponentSurrogate>();	
				services.AddTransient<IDriverSessionAPI, DriverSessionAPI>();
				services.AddTransient<IDriverSessionAPISurrogate, DriverSessionAPISurrogate>();	
				services.AddTransient<IVehicleImportComponent, VehicleImportComponent>();
				services.AddTransient<IVehicleImportComponentSurrogate, VehicleImportComponentSurrogate>();	
				services.AddTransient<IBroadcastMessageHistoryAPI, BroadcastMessageHistoryAPI>();
				services.AddTransient<IBroadcastMessageHistoryAPISurrogate, BroadcastMessageHistoryAPISurrogate>();	
				services.AddTransient<IBroadcastMessageHistoryExportComponent, BroadcastMessageHistoryExportComponent>();
				services.AddTransient<IBroadcastMessageHistoryExportComponentSurrogate, BroadcastMessageHistoryExportComponentSurrogate>();	
				services.AddTransient<IProficiencyReportExportComponent, ProficiencyReportExportComponent>();
				services.AddTransient<IProficiencyReportExportComponentSurrogate, ProficiencyReportExportComponentSurrogate>();	
				services.AddTransient<IGeneralLicenseImportComponent, GeneralLicenseImportComponent>();
				services.AddTransient<IGeneralLicenseImportComponentSurrogate, GeneralLicenseImportComponentSurrogate>();	
				services.AddTransient<IDataUtitilies, DataUtitilies>();
				services.AddTransient<IDataUtitiliesSurrogate, DataUtitiliesSurrogate>();	
				services.AddTransient<IVehicleAccessImportComponent, VehicleAccessImportComponent>();
				services.AddTransient<IVehicleAccessImportComponentSurrogate, VehicleAccessImportComponentSurrogate>();	
				services.AddTransient<IVehicleExportComponent, VehicleExportComponent>();
				services.AddTransient<IVehicleExportComponentSurrogate, VehicleExportComponentSurrogate>();	
				services.AddTransient<IVehicleHourMeter, VehicleHourMeter>();
				services.AddTransient<IVehicleHourMeterSurrogate, VehicleHourMeterSurrogate>();	
				services.AddTransient<IDriverImportComponent, DriverImportComponent>();
				services.AddTransient<IDriverImportComponentSurrogate, DriverImportComponentSurrogate>();	
				services.AddTransient<IOutgoingMessageAPI, OutgoingMessageAPI>();
				services.AddTransient<IOutgoingMessageAPISurrogate, OutgoingMessageAPISurrogate>();	
				services.AddTransient<IVehicleImportComponent, VehicleImportComponent>();
				services.AddTransient<IVehicleImportComponentSurrogate, VehicleImportComponentSurrogate>();	
				services.AddTransient<IGeneralProductivityReportExportComponent, GeneralProductivityReportExportComponent>();
				services.AddTransient<IGeneralProductivityReportExportComponentSurrogate, GeneralProductivityReportExportComponentSurrogate>();	
				services.AddTransient<ILicenseByModelImportComponent, LicenseByModelImportComponent>();
				services.AddTransient<ILicenseByModelImportComponentSurrogate, LicenseByModelImportComponentSurrogate>();	
				services.AddTransient<ICustomerImportComponent, CustomerImportComponent>();
				services.AddTransient<ICustomerImportComponentSurrogate, CustomerImportComponentSurrogate>();	
				services.AddTransient<IVehicleCalibrationReportExportComponent, VehicleCalibrationReportExportComponent>();
				services.AddTransient<IVehicleCalibrationReportExportComponentSurrogate, VehicleCalibrationReportExportComponentSurrogate>();	
				services.AddTransient<ISynchronizationStatusReportExportComponent, SynchronizationStatusReportExportComponent>();
				services.AddTransient<ISynchronizationStatusReportExportComponentSurrogate, SynchronizationStatusReportExportComponentSurrogate>();	
				services.AddTransient<IMachineUnlockAPI, MachineUnlockAPI>();
				services.AddTransient<IMachineUnlockAPISurrogate, MachineUnlockAPISurrogate>();	
				services.AddTransient<IImpactReportExportComponent, ImpactReportExportComponent>();
				services.AddTransient<IImpactReportExportComponentSurrogate, ImpactReportExportComponentSurrogate>();	
				services.AddTransient<IGO2FASecurityProviderComponent, GO2FASecurityProviderComponent>();
				services.AddTransient<IGO2FASecurityProviderComponentSurrogate, GO2FASecurityProviderComponentSurrogate>();	
				services.AddTransient<IChecklistSettingImportComponent, ChecklistSettingImportComponent>();
				services.AddTransient<IChecklistSettingImportComponentSurrogate, ChecklistSettingImportComponentSurrogate>();	
				services.AddTransient<IPreopChecklistAPI, PreopChecklistAPI>();
				services.AddTransient<IPreopChecklistAPISurrogate, PreopChecklistAPISurrogate>();	
				services.AddTransient<IVORReportExportComponent, VORReportExportComponent>();
				services.AddTransient<IVORReportExportComponentSurrogate, VORReportExportComponentSurrogate>();	
				services.AddTransient<IGeneralLicenseImportComponent, GeneralLicenseImportComponent>();
				services.AddTransient<IGeneralLicenseImportComponentSurrogate, GeneralLicenseImportComponentSurrogate>();	
				services.AddTransient<IVehicleCurrentStatusReportExportComponent, VehicleCurrentStatusReportExportComponent>();
				services.AddTransient<IVehicleCurrentStatusReportExportComponentSurrogate, VehicleCurrentStatusReportExportComponentSurrogate>();	
				services.AddTransient<IEHelpdeskAPI, EHelpdeskAPI>();
				services.AddTransient<IEHelpdeskAPISurrogate, EHelpdeskAPISurrogate>();	
				services.AddTransient<ISlamcorePathHistoryExportComponent, SlamcorePathHistoryExportComponent>();
				services.AddTransient<ISlamcorePathHistoryExportComponentSurrogate, SlamcorePathHistoryExportComponentSurrogate>();	
				services.AddTransient<IIoTHubManager, IoTHubManager>();
				services.AddTransient<IIoTHubManagerSurrogate, IoTHubManagerSurrogate>();	
				services.AddTransient<ISupervisorAccessImportComponent, SupervisorAccessImportComponent>();
				services.AddTransient<ISupervisorAccessImportComponentSurrogate, SupervisorAccessImportComponentSurrogate>();	
				services.AddTransient<ISyncProcess, SyncProcess>();
				services.AddTransient<ISyncProcessSurrogate, SyncProcessSurrogate>();	
				services.AddTransient<ISiteImportComponent, SiteImportComponent>();
				services.AddTransient<ISiteImportComponentSurrogate, SiteImportComponentSurrogate>();	
				services.AddTransient<IGeneralLicenseImportComponent, GeneralLicenseImportComponent>();
				services.AddTransient<IGeneralLicenseImportComponentSurrogate, GeneralLicenseImportComponentSurrogate>();	
				services.AddTransient<IPersonImportComponent, PersonImportComponent>();
				services.AddTransient<IPersonImportComponentSurrogate, PersonImportComponentSurrogate>();	
				services.AddTransient<IEmailGroupsToPersonUtilities, EmailGroupsToPersonUtilities>();
				services.AddTransient<IEmailGroupsToPersonUtilitiesSurrogate, EmailGroupsToPersonUtilitiesSurrogate>();	
				services.AddTransient<IReportActionsAPI, ReportActionsAPI>();
				services.AddTransient<IReportActionsAPISurrogate, ReportActionsAPISurrogate>();	
				services.AddTransient<ISendQuestionsToVehicle, SendQuestionsToVehicle>();
				services.AddTransient<ISendQuestionsToVehicleSurrogate, SendQuestionsToVehicleSurrogate>();	
				services.AddTransient<IPersonAPI, PersonAPI>();
				services.AddTransient<IPersonAPISurrogate, PersonAPISurrogate>();	
				services.AddTransient<ISlamcoreAlertHistoryComponent, SlamcoreAlertHistoryComponent>();
				services.AddTransient<ISlamcoreAlertHistoryComponentSurrogate, SlamcoreAlertHistoryComponentSurrogate>();	
				services.AddTransient<ISpareModuleImportComponent, SpareModuleImportComponent>();
				services.AddTransient<ISpareModuleImportComponentSurrogate, SpareModuleImportComponentSurrogate>();	
				services.AddTransient<IVehicleAccessCreation, VehicleAccessCreation>();
				services.AddTransient<IVehicleAccessCreationSurrogate, VehicleAccessCreationSurrogate>();	
				services.AddTransient<IRegisterModuleAPI, RegisterModuleAPI>();
				services.AddTransient<IRegisterModuleAPISurrogate, RegisterModuleAPISurrogate>();	
				services.AddTransient<IDepartmentImportComponent, DepartmentImportComponent>();
				services.AddTransient<IDepartmentImportComponentSurrogate, DepartmentImportComponentSurrogate>();	
				services.AddTransient<IModelUtilities, ModelUtilities>();
				services.AddTransient<IModelUtilitiesSurrogate, ModelUtilitiesSurrogate>();	
				services.AddTransient<ISlamcoreAlertHistoryComponent, SlamcoreAlertHistoryComponent>();
				services.AddTransient<ISlamcoreAlertHistoryComponentSurrogate, SlamcoreAlertHistoryComponentSurrogate>();	
				services.AddTransient<ILocationServiceAPI, LocationServiceAPI>();
				services.AddTransient<ILocationServiceAPISurrogate, LocationServiceAPISurrogate>();	
				services.AddTransient<ILicenseExpiryReportExportComponent, LicenseExpiryReportExportComponent>();
				services.AddTransient<ILicenseExpiryReportExportComponentSurrogate, LicenseExpiryReportExportComponentSurrogate>();	
				services.AddTransient<IVehicleAccessUtilities, VehicleAccessUtilities>();
				services.AddTransient<IVehicleAccessUtilitiesSurrogate, VehicleAccessUtilitiesSurrogate>();	
				services.AddTransient<IPreopExportComponent, PreopExportComponent>();
				services.AddTransient<IPreopExportComponentSurrogate, PreopExportComponentSurrogate>();	
				services.AddTransient<IPersonToDepartmentVehicleNormalAccessViewExportComponent, PersonToDepartmentVehicleNormalAccessViewExportComponent>();
				services.AddTransient<IPersonToDepartmentVehicleNormalAccessViewExportComponentSurrogate, PersonToDepartmentVehicleNormalAccessViewExportComponentSurrogate>();	
				services.AddTransient<ICustomerAPI, CustomerAPI>();
				services.AddTransient<ICustomerAPISurrogate, CustomerAPISurrogate>();	
				services.AddTransient<IVehicleAPI, VehicleAPI>();
				services.AddTransient<IVehicleAPISurrogate, VehicleAPISurrogate>();	
				services.AddTransient<IDriverCurrentStatusReportExportComponent, DriverCurrentStatusReportExportComponent>();
				services.AddTransient<IDriverCurrentStatusReportExportComponentSurrogate, DriverCurrentStatusReportExportComponentSurrogate>();	
				services.AddTransient<IGOFileUploader, GOFileUploader>();
				services.AddTransient<IGOFileUploaderSurrogate, GOFileUploaderSurrogate>();	
				
				// Server Component Extension provider
				{
					services.AddSingleton<IComponentApiExtensionProvider, ComponentApiExtensionProvider>();
				}

			}	// End Server Components
			services.AddScoped<IDataProvider<AllLicenseExpiryViewDataObject>, AllLicenseExpiryViewDataProvider>();
			services.AddScoped<IDataProvider<DriverLicenseExpiryViewDataObject>, DriverLicenseExpiryViewDataProvider>();
			services.AddScoped<IDataProvider<ChecklistStatusViewDataObject>, ChecklistStatusViewDataProvider>();
			services.AddScoped<IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject>, AllDriverAccessAbuseStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<AllEmailSubscriptionStoreProcedureDataObject>, AllEmailSubscriptionStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<AlertSummaryStoreProcedureDataObject>, AlertSummaryStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<DashboardVehicleCardStoreProcedureDataObject>, DashboardVehicleCardStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>, AllVORSessionsPerVehicleStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<AllImpactsViewDataObject>, AllImpactsViewDataProvider>();
			services.AddScoped<IDataProvider<AllChecklistResultViewDataObject>, AllChecklistResultViewDataProvider>();
			services.AddScoped<IDataProvider<AllVehicleUnlocksViewDataObject>, AllVehicleUnlocksViewDataProvider>();
			services.AddScoped<IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject>, ImpactFrequencyPerWeekMonthViewDataProvider>();
			services.AddScoped<IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject>, VehicleUtilizationLastTwelveHoursViewDataProvider>();
			services.AddScoped<IDataProvider<AllVehicleCalibrationStoreProcedureDataObject>, AllVehicleCalibrationStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<GORoleDataObject>, GORoleDataProvider>();
			services.AddScoped<IDataProvider<GeneralProductivityPerVehicleViewDataObject>, GeneralProductivityPerVehicleViewDataProvider>();
			services.AddScoped<IDataProvider<GeneralProductivityPerDriverViewLatestDataObject>, GeneralProductivityPerDriverViewLatestDataProvider>();
			services.AddScoped<IDataProvider<VehicleProficiencyViewDataObject>, VehicleProficiencyViewDataProvider>();
			services.AddScoped<IDataProvider<DriverProficiencyViewDataObject>, DriverProficiencyViewDataProvider>();
			services.AddScoped<IDataProvider<TodaysPreopCheckViewDataObject>, TodaysPreopCheckViewDataProvider>();
			services.AddScoped<IDataProvider<LoggedHoursVersusSeatHoursViewDataObject>, LoggedHoursVersusSeatHoursViewDataProvider>();
			services.AddScoped<IDataProvider<UnitUtilisationStoreProcedureDataObject>, UnitUtilisationStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<UnitUnutilisationStoreProcedureDataObject>, UnitUnutilisationStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<OnDemandAuthorisationStoreProcedureDataObject>, OnDemandAuthorisationStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject>, ImpactFrequencyPerTimeSlotViewDataProvider>();
			services.AddScoped<IDataProvider<ImpactFrequencyPerWeekDayViewDataObject>, ImpactFrequencyPerWeekDayViewDataProvider>();
			services.AddScoped<IDataProvider<IncompletedChecklistViewDataObject>, IncompletedChecklistViewDataProvider>();
			services.AddScoped<IDataProvider<DriverLicenseExpiryStoreProcedureDataObject>, DriverLicenseExpiryStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<UnitSummaryReportDataObject>, UnitSummaryReportDataProvider>();
			services.AddScoped<IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>, VehicleUtilizationLastTwelveHoursStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<AllMessageHistoryStoreProcedureDataObject>, AllMessageHistoryStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<TodaysImpactStoreProcedureDataObject>, TodaysImpactStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<DashboardDriverCardStoreProcedureDataObject>, DashboardDriverCardStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<TodaysImpactViewDataObject>, TodaysImpactViewDataProvider>();
			services.AddScoped<IDataProvider<DetailedSessionViewDataObject>, DetailedSessionViewDataProvider>();
			services.AddScoped<IDataProvider<AllVORStatusStoreProcedureDataObject>, AllVORStatusStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<AllUserSummaryStoreProcedureDataObject>, AllUserSummaryStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<UnitSummaryStoreProcedureDataObject>, UnitSummaryStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<TodaysPreopCheckStoreProcedureDataObject>, TodaysPreopCheckStoreProcedureDataProvider>();
			services.AddScoped<IDataProvider<GO2FAConfigurationDataObject>, GO2FAConfigurationDataProvider>();
			services.AddScoped<IDataProvider<DetailedVORSessionStoreProcedureDataObject>, DetailedVORSessionStoreProcedureDataProvider>();
			
            services.AddScoped<IDataFacade, DataFacade>();               
            services.AddSingleton<IDataProviderProxy, DataProviderProxy>();               
		
            #region Data Providers
			services.AddScoped<IDataProvider<ChecklistDetailDataObject>, ChecklistDetailDataProvider>();
			services.AddScoped<IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject>, SlamcoreAwareAuthenticationDetailsDataProvider>();
			services.AddScoped<IDataProvider<FeatureSubscriptionTemplateDataObject>, FeatureSubscriptionTemplateDataProvider>();
			services.AddScoped<IDataProvider<PersonToModelVehicleMasterAccessViewDataObject>, PersonToModelVehicleMasterAccessViewDataProvider>();
			services.AddScoped<IDataProvider<VehicleLockoutDataObject>, VehicleLockoutDataProvider>();
			services.AddScoped<IDataProvider<RevisionDataObject>, RevisionDataProvider>();
			services.AddScoped<IDataProvider<AlertDataObject>, AlertDataProvider>();
			services.AddScoped<IDataProvider<WebsiteUserDataObject>, WebsiteUserDataProvider>();
			services.AddScoped<IDataProvider<DriverAccessAbuseFilterDataObject>, DriverAccessAbuseFilterDataProvider>();
			services.AddScoped<IDataProvider<ContactPersonInformationDataObject>, ContactPersonInformationDataProvider>();
			services.AddScoped<IDataProvider<ChecklistSettingsDataObject>, ChecklistSettingsDataProvider>();
			services.AddScoped<IDataProvider<VehicleDiagnosticDataObject>, VehicleDiagnosticDataProvider>();
			services.AddScoped<IDataProvider<GOUser2FADataObject>, GOUser2FADataProvider>();
			services.AddScoped<IDataProvider<VehicleGPSDataObject>, VehicleGPSDataProvider>();
			services.AddScoped<IDataProvider<ModuleDataObject>, ModuleDataProvider>();
			services.AddScoped<IDataProvider<PersonToPerVehicleMasterAccessViewDataObject>, PersonToPerVehicleMasterAccessViewDataProvider>();
			services.AddScoped<IDataProvider<AccessGroupDataObject>, AccessGroupDataProvider>();
			services.AddScoped<IDataProvider<HelpDataObject>, HelpDataProvider>();
			services.AddScoped<IDataProvider<CurrentStatusCombinedViewDataObject>, CurrentStatusCombinedViewDataProvider>();
			services.AddScoped<IDataProvider<CurrentVehicleStatusChartViewDataObject>, CurrentVehicleStatusChartViewDataProvider>();
			services.AddScoped<IDataProvider<DealerFeatureSubscriptionDataObject>, DealerFeatureSubscriptionDataProvider>();
			services.AddScoped<IDataProvider<MessageHistoryDataObject>, MessageHistoryDataProvider>();
			services.AddScoped<IDataProvider<AccessGroupToSiteDataObject>, AccessGroupToSiteDataProvider>();
			services.AddScoped<IDataProvider<CustomerToModelDataObject>, CustomerToModelDataProvider>();
			services.AddScoped<IDataProvider<RegionDataObject>, RegionDataProvider>();
			services.AddScoped<IDataProvider<ModelDataObject>, ModelDataProvider>();
			services.AddScoped<IDataProvider<SlamcoreDeviceConnectionViewDataObject>, SlamcoreDeviceConnectionViewDataProvider>();
			services.AddScoped<IDataProvider<CustomerDataObject>, CustomerDataProvider>();
			services.AddScoped<IDataProvider<DepartmentChecklistDataObject>, DepartmentChecklistDataProvider>();
			services.AddScoped<IDataProvider<ModuleHistoryDataObject>, ModuleHistoryDataProvider>();
			services.AddScoped<IDataProvider<CountryDataObject>, CountryDataProvider>();
			services.AddScoped<IDataProvider<GPSHistoryDataObject>, GPSHistoryDataProvider>();
			services.AddScoped<IDataProvider<ImpactsForVehicleViewDataObject>, ImpactsForVehicleViewDataProvider>();
			services.AddScoped<IDataProvider<SlamcorePedestrianDetectionDataObject>, SlamcorePedestrianDetectionDataProvider>();
			services.AddScoped<IDataProvider<IOFIELDDataObject>, IOFIELDDataProvider>();
			services.AddScoped<IDataProvider<EmailDataObject>, EmailDataProvider>();
			services.AddScoped<IDataProvider<LicenceDetailDataObject>, LicenceDetailDataProvider>();
			services.AddScoped<IDataProvider<PedestrianDetectionHistoryFilterDataObject>, PedestrianDetectionHistoryFilterDataProvider>();
			services.AddScoped<IDataProvider<GOLoginHistoryDataObject>, GOLoginHistoryDataProvider>();
			services.AddScoped<IDataProvider<MainDashboardFilterDataObject>, MainDashboardFilterDataProvider>();
			services.AddScoped<IDataProvider<AccessGroupTemplateDataObject>, AccessGroupTemplateDataProvider>();
			services.AddScoped<IDataProvider<CardToCardAccessDataObject>, CardToCardAccessDataProvider>();
			services.AddScoped<IDataProvider<ZoneCoordinatesDataObject>, ZoneCoordinatesDataProvider>();
			services.AddScoped<IDataProvider<SnapshotDataObject>, SnapshotDataProvider>();
			services.AddScoped<IDataProvider<GOTaskDataObject>, GOTaskDataProvider>();
			services.AddScoped<IDataProvider<GeneralProductivityReportFilterDataObject>, GeneralProductivityReportFilterDataProvider>();
			services.AddScoped<IDataProvider<DepartmentVehicleNormalCardAccessDataObject>, DepartmentVehicleNormalCardAccessDataProvider>();
			services.AddScoped<IDataProvider<CustomerToPersonViewDataObject>, CustomerToPersonViewDataProvider>();
			services.AddScoped<IDataProvider<VehicleSlamcoreLocationHistoryDataObject>, VehicleSlamcoreLocationHistoryDataProvider>();
			services.AddScoped<IDataProvider<PermissionDataObject>, PermissionDataProvider>();
			services.AddScoped<IDataProvider<SessionDetailsDataObject>, SessionDetailsDataProvider>();
			services.AddScoped<IDataProvider<VehicleDataObject>, VehicleDataProvider>();
			services.AddScoped<IDataProvider<DriverDataObject>, DriverDataProvider>();
			services.AddScoped<IDataProvider<EmailGroupsToPersonDataObject>, EmailGroupsToPersonDataProvider>();
			services.AddScoped<IDataProvider<GOGroupRoleDataObject>, GOGroupRoleDataProvider>();
			services.AddScoped<IDataProvider<ModelVehicleMasterCardAccessDataObject>, ModelVehicleMasterCardAccessDataProvider>();
			services.AddScoped<IDataProvider<EmailSubscriptionReportFilterDataObject>, EmailSubscriptionReportFilterDataProvider>();
			services.AddScoped<IDataProvider<DashboardDriverCardViewDataObject>, DashboardDriverCardViewDataProvider>();
			services.AddScoped<IDataProvider<GOSecurityTokensDataObject>, GOSecurityTokensDataProvider>();
			services.AddScoped<IDataProvider<AlertHistoryDataObject>, AlertHistoryDataProvider>();
			services.AddScoped<IDataProvider<DashboardFilterDataObject>, DashboardFilterDataProvider>();
			services.AddScoped<IDataProvider<VORReportFilterDataObject>, VORReportFilterDataProvider>();
			services.AddScoped<IDataProvider<OnDemandAuthorisationFilterDataObject>, OnDemandAuthorisationFilterDataProvider>();
			services.AddScoped<IDataProvider<WebsiteRoleDataObject>, WebsiteRoleDataProvider>();
			services.AddScoped<IDataProvider<GOUserRoleDataObject>, GOUserRoleDataProvider>();
			services.AddScoped<IDataProvider<GeneralProductivityViewDataObject>, GeneralProductivityViewDataProvider>();
			services.AddScoped<IDataProvider<InspectionDataObject>, InspectionDataProvider>();
			services.AddScoped<IDataProvider<ImpactDataObject>, ImpactDataProvider>();
			services.AddScoped<IDataProvider<PersonToDepartmentVehicleMasterAccessViewDataObject>, PersonToDepartmentVehicleMasterAccessViewDataProvider>();
			services.AddScoped<IDataProvider<CurrentStatusVehicleViewDataObject>, CurrentStatusVehicleViewDataProvider>();
			services.AddScoped<IDataProvider<CustomerModelDataObject>, CustomerModelDataProvider>();
			services.AddScoped<IDataProvider<TagDataObject>, TagDataProvider>();
			services.AddScoped<IDataProvider<VehicleBroadcastMessageDataObject>, VehicleBroadcastMessageDataProvider>();
			services.AddScoped<IDataProvider<FloorPlanDataObject>, FloorPlanDataProvider>();
			services.AddScoped<IDataProvider<ModelVehicleNormalCardAccessDataObject>, ModelVehicleNormalCardAccessDataProvider>();
			services.AddScoped<IDataProvider<SiteVehicleNormalCardAccessDataObject>, SiteVehicleNormalCardAccessDataProvider>();
			services.AddScoped<IDataProvider<ImportJobStatusDataObject>, ImportJobStatusDataProvider>();
			services.AddScoped<IDataProvider<VehicleOtherSettingsDataObject>, VehicleOtherSettingsDataProvider>();
			services.AddScoped<IDataProvider<DashboardFilterMoreFieldsDataObject>, DashboardFilterMoreFieldsDataProvider>();
			services.AddScoped<IDataProvider<VehicleSupervisorsViewDataObject>, VehicleSupervisorsViewDataProvider>();
			services.AddScoped<IDataProvider<ExportJobStatusDataObject>, ExportJobStatusDataProvider>();
			services.AddScoped<IDataProvider<TimezoneDataObject>, TimezoneDataProvider>();
			services.AddScoped<IDataProvider<HireDeHireReportFilterDataObject>, HireDeHireReportFilterDataProvider>();
			services.AddScoped<IDataProvider<PersonAllocationDataObject>, PersonAllocationDataProvider>();
			services.AddScoped<IDataProvider<BroadcastMessageDataObject>, BroadcastMessageDataProvider>();
			services.AddScoped<IDataProvider<VORReportCombinedViewDataObject>, VORReportCombinedViewDataProvider>();
			services.AddScoped<IDataProvider<ChecklistResultDataObject>, ChecklistResultDataProvider>();
			services.AddScoped<IDataProvider<VehicleAlertSubscriptionDataObject>, VehicleAlertSubscriptionDataProvider>();
			services.AddScoped<IDataProvider<PersonToSiteVehicleNormalAccessViewDataObject>, PersonToSiteVehicleNormalAccessViewDataProvider>();
			services.AddScoped<IDataProvider<SessionDataObject>, SessionDataProvider>();
			services.AddScoped<IDataProvider<SlamcoreDeviceFilterDataObject>, SlamcoreDeviceFilterDataProvider>();
			services.AddScoped<IDataProvider<DealerDataObject>, DealerDataProvider>();
			services.AddScoped<IDataProvider<GOUserDataObject>, GOUserDataProvider>();
			services.AddScoped<IDataProvider<ServiceSettingsDataObject>, ServiceSettingsDataProvider>();
			services.AddScoped<IDataProvider<UpdateFirmwareRequestDataObject>, UpdateFirmwareRequestDataProvider>();
			services.AddScoped<IDataProvider<SlamcoreAPIKeyDataObject>, SlamcoreAPIKeyDataProvider>();
			services.AddScoped<IDataProvider<FirmwareDataObject>, FirmwareDataProvider>();
			services.AddScoped<IDataProvider<CardDataObject>, CardDataProvider>();
			services.AddScoped<IDataProvider<GOUserDepartmentDataObject>, GOUserDepartmentDataProvider>();
			services.AddScoped<IDataProvider<CanruleDataObject>, CanruleDataProvider>();
			services.AddScoped<IDataProvider<SlamcoreDeviceHistoryDataObject>, SlamcoreDeviceHistoryDataProvider>();
			services.AddScoped<IDataProvider<VehicleHireDehireHistoryDataObject>, VehicleHireDehireHistoryDataProvider>();
			services.AddScoped<IDataProvider<ChecklistFailurePerVechicleViewDataObject>, ChecklistFailurePerVechicleViewDataProvider>();
			services.AddScoped<IDataProvider<ImpactReportFilterDataObject>, ImpactReportFilterDataProvider>();
			services.AddScoped<IDataProvider<ReportSubscriptionDataObject>, ReportSubscriptionDataProvider>();
			services.AddScoped<IDataProvider<VehicleLastGPSLocationViewDataObject>, VehicleLastGPSLocationViewDataProvider>();
			services.AddScoped<IDataProvider<PSTATDetailsDataObject>, PSTATDetailsDataProvider>();
			services.AddScoped<IDataProvider<LicenseExpiryReportFilterDataObject>, LicenseExpiryReportFilterDataProvider>();
			services.AddScoped<IDataProvider<FloorZonesDataObject>, FloorZonesDataProvider>();
			services.AddScoped<IDataProvider<CanruleDetailsDataObject>, CanruleDetailsDataProvider>();
			services.AddScoped<IDataProvider<VehicleHireDehireSynchronizationOptionsDataObject>, VehicleHireDehireSynchronizationOptionsDataProvider>();
			services.AddScoped<IDataProvider<DepartmentVehicleMasterCardAccessDataObject>, DepartmentVehicleMasterCardAccessDataProvider>();
			services.AddScoped<IDataProvider<ChecklistFailureViewDataObject>, ChecklistFailureViewDataProvider>();
			services.AddScoped<IDataProvider<PersonChecklistLanguageSettingsDataObject>, PersonChecklistLanguageSettingsDataProvider>();
			services.AddScoped<IDataProvider<VehicleSessionlessImpactDataObject>, VehicleSessionlessImpactDataProvider>();
			services.AddScoped<IDataProvider<LicenseByModelDataObject>, LicenseByModelDataProvider>();
			services.AddScoped<IDataProvider<IoTDeviceMessageCacheDataObject>, IoTDeviceMessageCacheDataProvider>();
			services.AddScoped<IDataProvider<ProficiencyCombinedViewDataObject>, ProficiencyCombinedViewDataProvider>();
			services.AddScoped<IDataProvider<CustomerSnapshotDataObject>, CustomerSnapshotDataProvider>();
			services.AddScoped<IDataProvider<GOGroupDataObject>, GOGroupDataProvider>();
			services.AddScoped<IDataProvider<VehiclesPerModelReportDataObject>, VehiclesPerModelReportDataProvider>();
			services.AddScoped<IDataProvider<BroadcastMessageHistoryFilterDataObject>, BroadcastMessageHistoryFilterDataProvider>();
			services.AddScoped<IDataProvider<DealerDriverDataObject>, DealerDriverDataProvider>();
			services.AddScoped<IDataProvider<EmailGroupsDataObject>, EmailGroupsDataProvider>();
			services.AddScoped<IDataProvider<PreOperationalChecklistDataObject>, PreOperationalChecklistDataProvider>();
			services.AddScoped<IDataProvider<SynchronizationStatusReportFilterDataObject>, SynchronizationStatusReportFilterDataProvider>();
			services.AddScoped<IDataProvider<SlamcoreDeviceDataObject>, SlamcoreDeviceDataProvider>();
			services.AddScoped<IDataProvider<SiteDataObject>, SiteDataProvider>();
			services.AddScoped<IDataProvider<AlertSubscriptionDataObject>, AlertSubscriptionDataProvider>();
			services.AddScoped<IDataProvider<CustomerAuditDataObject>, CustomerAuditDataProvider>();
			services.AddScoped<IDataProvider<PerVehicleNormalCardAccessDataObject>, PerVehicleNormalCardAccessDataProvider>();
			services.AddScoped<IDataProvider<PedestrianDetectionHistoryDataObject>, PedestrianDetectionHistoryDataProvider>();
			services.AddScoped<IDataProvider<PersonToPerVehicleNormalAccessViewDataObject>, PersonToPerVehicleNormalAccessViewDataProvider>();
			services.AddScoped<IDataProvider<ImportJobLogDataObject>, ImportJobLogDataProvider>();
			services.AddScoped<IDataProvider<ProficiencyReportFilterDataObject>, ProficiencyReportFilterDataProvider>();
			services.AddScoped<IDataProvider<OnDemandSessionDataObject>, OnDemandSessionDataProvider>();
			services.AddScoped<IDataProvider<VORSettingHistoryDataObject>, VORSettingHistoryDataProvider>();
			services.AddScoped<IDataProvider<CustomerSSODetailDataObject>, CustomerSSODetailDataProvider>();
			services.AddScoped<IDataProvider<UploadLogoRequestDataObject>, UploadLogoRequestDataProvider>();
			services.AddScoped<IDataProvider<AllVehicleCalibrationFilterDataObject>, AllVehicleCalibrationFilterDataProvider>();
			services.AddScoped<IDataProvider<DepartmentHourSettingsDataObject>, DepartmentHourSettingsDataProvider>();
			services.AddScoped<IDataProvider<CategoryTemplateDataObject>, CategoryTemplateDataProvider>();
			services.AddScoped<IDataProvider<DashboardVehicleCardViewDataObject>, DashboardVehicleCardViewDataProvider>();
			services.AddScoped<IDataProvider<CurrentStatusDriverViewDataObject>, CurrentStatusDriverViewDataProvider>();
			services.AddScoped<IDataProvider<ReportTypeDataObject>, ReportTypeDataProvider>();
			services.AddScoped<IDataProvider<SiteVehicleMasterCardAccessDataObject>, SiteVehicleMasterCardAccessDataProvider>();
			services.AddScoped<IDataProvider<PersonToDepartmentVehicleNormalAccessViewDataObject>, PersonToDepartmentVehicleNormalAccessViewDataProvider>();
			services.AddScoped<IDataProvider<GOUserGroupDataObject>, GOUserGroupDataProvider>();
			services.AddScoped<IDataProvider<CustomerFeatureSubscriptionDataObject>, CustomerFeatureSubscriptionDataProvider>();
			services.AddScoped<IDataProvider<VehicleToPreOpChecklistViewDataObject>, VehicleToPreOpChecklistViewDataProvider>();
			services.AddScoped<IDataProvider<PersonToSiteVehicleMasterAccessViewDataObject>, PersonToSiteVehicleMasterAccessViewDataProvider>();
			services.AddScoped<IDataProvider<DealerConfigurationDataObject>, DealerConfigurationDataProvider>();
			services.AddScoped<IDataProvider<CustomerPreOperationalChecklistTemplateDataObject>, CustomerPreOperationalChecklistTemplateDataProvider>();
			services.AddScoped<IDataProvider<CurrentDriverStatusChartViewDataObject>, CurrentDriverStatusChartViewDataProvider>();
			services.AddScoped<IDataProvider<GOChangeDeltaDataObject>, GOChangeDeltaDataProvider>();
			services.AddScoped<IDataProvider<PreOpReportFilterDataObject>, PreOpReportFilterDataProvider>();
			services.AddScoped<IDataProvider<SiteFloorPlanDataObject>, SiteFloorPlanDataProvider>();
			services.AddScoped<IDataProvider<BroadcastMessageHistoryDataObject>, BroadcastMessageHistoryDataProvider>();
			services.AddScoped<IDataProvider<GoUserToCustomerDataObject>, GoUserToCustomerDataProvider>();
			services.AddScoped<IDataProvider<ImportJobBatchDataObject>, ImportJobBatchDataProvider>();
			services.AddScoped<IDataProvider<PersonDataObject>, PersonDataProvider>();
			services.AddScoped<IDataProvider<PersonToModelVehicleNormalAccessViewDataObject>, PersonToModelVehicleNormalAccessViewDataProvider>();
			services.AddScoped<IDataProvider<NetworkSettingsDataObject>, NetworkSettingsDataProvider>();
			services.AddScoped<IDataProvider<DepartmentDataObject>, DepartmentDataProvider>();
			services.AddScoped<IDataProvider<MachineUnlockReportFilterDataObject>, MachineUnlockReportFilterDataProvider>();
			services.AddScoped<IDataProvider<PerVehicleMasterCardAccessDataObject>, PerVehicleMasterCardAccessDataProvider>();
			services.AddScoped<IDataProvider<UnitUtilisationCombinedViewDataObject>, UnitUtilisationCombinedViewDataProvider>();
			services.AddScoped<IDataProvider<FeatureSubscriptionsFilterDataObject>, FeatureSubscriptionsFilterDataProvider>();
			services.AddScoped<IDataProvider<OnDemandSettingsDataObject>, OnDemandSettingsDataProvider>();
			
			// EntityDataProvider: Given an entity instance, get its corresponding entity data provider
			services.AddScoped<IEntityDataProvider, DatabaseEntityProvider>();
            #endregion Data Providers
			services.AddSingleton<ISettingsProvider, SettingsProvider>(x =>
			{
				var settings = new SettingsProvider(); 
				settings["WrapForPolicyInjection"] = false; 
				settings["RootNamespace"] = "FleetXQ";            
				settings["StorageProvider"] = "FileSystemStorageProvider";

				return settings;
			});   
			
			services.AddScoped<IGOSecuritySupport, GOSecuritySupport>();
			services.AddTransient<IDataProviderExtension<GOUserDataObject>, GOSecurityProvider>();		// The GOSecurityProvider also provides OnBeforeSave extension for GOUsers
		

services.AddScoped<IApiExtension, AuthenticationApiExtension>();
            services.AddScoped<IAuthentication, Authentication>();
            services.AddScoped<IAuthorizations, Authorizations>();
			services.AddScoped<IUserIdentity, UserIdentity>();		// Scoped else one thread can overwrite another's user token
			services.AddSingleton<IUsersCache, UsersCache>(); 
			services.AddSingleton<IEntityAuthorizationCache, EntityAuthorizationCache>(); 
			services.AddScoped<IMembershipProviderSelector, MembershipProviderSelector>();

            #region Authorizations & Data Provider Extensions
			services.AddSingleton<IEntityAuthorizations, ChecklistDetailAuthorizations>();
			services.AddScoped<IDataProviderExtension<ChecklistDetailDataObject>, ChecklistDetailDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SlamcoreAwareAuthenticationDetailsAuthorizations>();
			services.AddScoped<IDataProviderExtension<SlamcoreAwareAuthenticationDetailsDataObject>, SlamcoreAwareAuthenticationDetailsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, FeatureSubscriptionTemplateAuthorizations>();
			services.AddScoped<IDataProviderExtension<FeatureSubscriptionTemplateDataObject>, FeatureSubscriptionTemplateDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonToModelVehicleMasterAccessViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonToModelVehicleMasterAccessViewDataObject>, PersonToModelVehicleMasterAccessViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleLockoutAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleLockoutDataObject>, VehicleLockoutDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, RevisionAuthorizations>();
			services.AddScoped<IDataProviderExtension<RevisionDataObject>, RevisionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AlertAuthorizations>();
			services.AddScoped<IDataProviderExtension<AlertDataObject>, AlertDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, WebsiteUserAuthorizations>();
			services.AddScoped<IDataProviderExtension<WebsiteUserDataObject>, WebsiteUserDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DriverAccessAbuseFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<DriverAccessAbuseFilterDataObject>, DriverAccessAbuseFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllLicenseExpiryViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllLicenseExpiryViewDataObject>, AllLicenseExpiryViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DriverLicenseExpiryViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<DriverLicenseExpiryViewDataObject>, DriverLicenseExpiryViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ContactPersonInformationAuthorizations>();
			services.AddScoped<IDataProviderExtension<ContactPersonInformationDataObject>, ContactPersonInformationDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ChecklistSettingsAuthorizations>();
			services.AddScoped<IDataProviderExtension<ChecklistSettingsDataObject>, ChecklistSettingsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleDiagnosticAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleDiagnosticDataObject>, VehicleDiagnosticDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOUser2FAAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOUser2FADataObject>, GOUser2FADataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleGPSAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleGPSDataObject>, VehicleGPSDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ModuleAuthorizations>();
			services.AddScoped<IDataProviderExtension<ModuleDataObject>, ModuleDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonToPerVehicleMasterAccessViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonToPerVehicleMasterAccessViewDataObject>, PersonToPerVehicleMasterAccessViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AccessGroupAuthorizations>();
			services.AddScoped<IDataProviderExtension<AccessGroupDataObject>, AccessGroupDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, HelpAuthorizations>();
			services.AddScoped<IDataProviderExtension<HelpDataObject>, HelpDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ChecklistStatusViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<ChecklistStatusViewDataObject>, ChecklistStatusViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CurrentStatusCombinedViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<CurrentStatusCombinedViewDataObject>, CurrentStatusCombinedViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CurrentVehicleStatusChartViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<CurrentVehicleStatusChartViewDataObject>, CurrentVehicleStatusChartViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DealerFeatureSubscriptionAuthorizations>();
			services.AddScoped<IDataProviderExtension<DealerFeatureSubscriptionDataObject>, DealerFeatureSubscriptionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, MessageHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<MessageHistoryDataObject>, MessageHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AccessGroupToSiteAuthorizations>();
			services.AddScoped<IDataProviderExtension<AccessGroupToSiteDataObject>, AccessGroupToSiteDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllDriverAccessAbuseStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllDriverAccessAbuseStoreProcedureDataObject>, AllDriverAccessAbuseStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CustomerToModelAuthorizations>();
			services.AddScoped<IDataProviderExtension<CustomerToModelDataObject>, CustomerToModelDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllEmailSubscriptionStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllEmailSubscriptionStoreProcedureDataObject>, AllEmailSubscriptionStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AlertSummaryStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<AlertSummaryStoreProcedureDataObject>, AlertSummaryStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DashboardVehicleCardStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<DashboardVehicleCardStoreProcedureDataObject>, DashboardVehicleCardStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, RegionAuthorizations>();
			services.AddScoped<IDataProviderExtension<RegionDataObject>, RegionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ModelAuthorizations>();
			services.AddScoped<IDataProviderExtension<ModelDataObject>, ModelDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllVORSessionsPerVehicleStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllVORSessionsPerVehicleStoreProcedureDataObject>, AllVORSessionsPerVehicleStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SlamcoreDeviceConnectionViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<SlamcoreDeviceConnectionViewDataObject>, SlamcoreDeviceConnectionViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllImpactsViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllImpactsViewDataObject>, AllImpactsViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CustomerAuthorizations>();
			services.AddScoped<IDataProviderExtension<CustomerDataObject>, CustomerDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DepartmentChecklistAuthorizations>();
			services.AddScoped<IDataProviderExtension<DepartmentChecklistDataObject>, DepartmentChecklistDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ModuleHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<ModuleHistoryDataObject>, ModuleHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CountryAuthorizations>();
			services.AddScoped<IDataProviderExtension<CountryDataObject>, CountryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllChecklistResultViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllChecklistResultViewDataObject>, AllChecklistResultViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GPSHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<GPSHistoryDataObject>, GPSHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ImpactsForVehicleViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<ImpactsForVehicleViewDataObject>, ImpactsForVehicleViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllVehicleUnlocksViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllVehicleUnlocksViewDataObject>, AllVehicleUnlocksViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SlamcorePedestrianDetectionAuthorizations>();
			services.AddScoped<IDataProviderExtension<SlamcorePedestrianDetectionDataObject>, SlamcorePedestrianDetectionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ImpactFrequencyPerWeekMonthViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<ImpactFrequencyPerWeekMonthViewDataObject>, ImpactFrequencyPerWeekMonthViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleUtilizationLastTwelveHoursViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleUtilizationLastTwelveHoursViewDataObject>, VehicleUtilizationLastTwelveHoursViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, IOFIELDAuthorizations>();
			services.AddScoped<IDataProviderExtension<IOFIELDDataObject>, IOFIELDDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, EmailAuthorizations>();
			services.AddScoped<IDataProviderExtension<EmailDataObject>, EmailDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, LicenceDetailAuthorizations>();
			services.AddScoped<IDataProviderExtension<LicenceDetailDataObject>, LicenceDetailDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PedestrianDetectionHistoryFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<PedestrianDetectionHistoryFilterDataObject>, PedestrianDetectionHistoryFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOLoginHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOLoginHistoryDataObject>, GOLoginHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, MainDashboardFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<MainDashboardFilterDataObject>, MainDashboardFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllVehicleCalibrationStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllVehicleCalibrationStoreProcedureDataObject>, AllVehicleCalibrationStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AccessGroupTemplateAuthorizations>();
			services.AddScoped<IDataProviderExtension<AccessGroupTemplateDataObject>, AccessGroupTemplateDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CardToCardAccessAuthorizations>();
			services.AddScoped<IDataProviderExtension<CardToCardAccessDataObject>, CardToCardAccessDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ZoneCoordinatesAuthorizations>();
			services.AddScoped<IDataProviderExtension<ZoneCoordinatesDataObject>, ZoneCoordinatesDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SnapshotAuthorizations>();
			services.AddScoped<IDataProviderExtension<SnapshotDataObject>, SnapshotDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOTaskAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOTaskDataObject>, GOTaskDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GeneralProductivityReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<GeneralProductivityReportFilterDataObject>, GeneralProductivityReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DepartmentVehicleNormalCardAccessAuthorizations>();
			services.AddScoped<IDataProviderExtension<DepartmentVehicleNormalCardAccessDataObject>, DepartmentVehicleNormalCardAccessDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CustomerToPersonViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<CustomerToPersonViewDataObject>, CustomerToPersonViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleSlamcoreLocationHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleSlamcoreLocationHistoryDataObject>, VehicleSlamcoreLocationHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PermissionAuthorizations>();
			services.AddScoped<IDataProviderExtension<PermissionDataObject>, PermissionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SessionDetailsAuthorizations>();
			services.AddScoped<IDataProviderExtension<SessionDetailsDataObject>, SessionDetailsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GORoleAuthorizations>();
			services.AddScoped<IDataProviderExtension<GORoleDataObject>, GORoleDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GeneralProductivityPerVehicleViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<GeneralProductivityPerVehicleViewDataObject>, GeneralProductivityPerVehicleViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleDataObject>, VehicleDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GeneralProductivityPerDriverViewLatestAuthorizations>();
			services.AddScoped<IDataProviderExtension<GeneralProductivityPerDriverViewLatestDataObject>, GeneralProductivityPerDriverViewLatestDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DriverAuthorizations>();
			services.AddScoped<IDataProviderExtension<DriverDataObject>, DriverDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, EmailGroupsToPersonAuthorizations>();
			services.AddScoped<IDataProviderExtension<EmailGroupsToPersonDataObject>, EmailGroupsToPersonDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOGroupRoleAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOGroupRoleDataObject>, GOGroupRoleDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ModelVehicleMasterCardAccessAuthorizations>();
			services.AddScoped<IDataProviderExtension<ModelVehicleMasterCardAccessDataObject>, ModelVehicleMasterCardAccessDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, EmailSubscriptionReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<EmailSubscriptionReportFilterDataObject>, EmailSubscriptionReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleProficiencyViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleProficiencyViewDataObject>, VehicleProficiencyViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DashboardDriverCardViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<DashboardDriverCardViewDataObject>, DashboardDriverCardViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DriverProficiencyViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<DriverProficiencyViewDataObject>, DriverProficiencyViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOSecurityTokensAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOSecurityTokensDataObject>, GOSecurityTokensDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AlertHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<AlertHistoryDataObject>, AlertHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DashboardFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<DashboardFilterDataObject>, DashboardFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VORReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<VORReportFilterDataObject>, VORReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, OnDemandAuthorisationFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<OnDemandAuthorisationFilterDataObject>, OnDemandAuthorisationFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, WebsiteRoleAuthorizations>();
			services.AddScoped<IDataProviderExtension<WebsiteRoleDataObject>, WebsiteRoleDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOUserRoleAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOUserRoleDataObject>, GOUserRoleDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GeneralProductivityViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<GeneralProductivityViewDataObject>, GeneralProductivityViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, InspectionAuthorizations>();
			services.AddScoped<IDataProviderExtension<InspectionDataObject>, InspectionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ImpactAuthorizations>();
			services.AddScoped<IDataProviderExtension<ImpactDataObject>, ImpactDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonToDepartmentVehicleMasterAccessViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonToDepartmentVehicleMasterAccessViewDataObject>, PersonToDepartmentVehicleMasterAccessViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CurrentStatusVehicleViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<CurrentStatusVehicleViewDataObject>, CurrentStatusVehicleViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CustomerModelAuthorizations>();
			services.AddScoped<IDataProviderExtension<CustomerModelDataObject>, CustomerModelDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, TagAuthorizations>();
			services.AddScoped<IDataProviderExtension<TagDataObject>, TagDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleBroadcastMessageAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleBroadcastMessageDataObject>, VehicleBroadcastMessageDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, FloorPlanAuthorizations>();
			services.AddScoped<IDataProviderExtension<FloorPlanDataObject>, FloorPlanDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, TodaysPreopCheckViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<TodaysPreopCheckViewDataObject>, TodaysPreopCheckViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ModelVehicleNormalCardAccessAuthorizations>();
			services.AddScoped<IDataProviderExtension<ModelVehicleNormalCardAccessDataObject>, ModelVehicleNormalCardAccessDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SiteVehicleNormalCardAccessAuthorizations>();
			services.AddScoped<IDataProviderExtension<SiteVehicleNormalCardAccessDataObject>, SiteVehicleNormalCardAccessDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ImportJobStatusAuthorizations>();
			services.AddScoped<IDataProviderExtension<ImportJobStatusDataObject>, ImportJobStatusDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleOtherSettingsAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleOtherSettingsDataObject>, VehicleOtherSettingsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DashboardFilterMoreFieldsAuthorizations>();
			services.AddScoped<IDataProviderExtension<DashboardFilterMoreFieldsDataObject>, DashboardFilterMoreFieldsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleSupervisorsViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleSupervisorsViewDataObject>, VehicleSupervisorsViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ExportJobStatusAuthorizations>();
			services.AddScoped<IDataProviderExtension<ExportJobStatusDataObject>, ExportJobStatusDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, TimezoneAuthorizations>();
			services.AddScoped<IDataProviderExtension<TimezoneDataObject>, TimezoneDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, HireDeHireReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<HireDeHireReportFilterDataObject>, HireDeHireReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, LoggedHoursVersusSeatHoursViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<LoggedHoursVersusSeatHoursViewDataObject>, LoggedHoursVersusSeatHoursViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonAllocationAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonAllocationDataObject>, PersonAllocationDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, BroadcastMessageAuthorizations>();
			services.AddScoped<IDataProviderExtension<BroadcastMessageDataObject>, BroadcastMessageDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VORReportCombinedViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<VORReportCombinedViewDataObject>, VORReportCombinedViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, UnitUtilisationStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<UnitUtilisationStoreProcedureDataObject>, UnitUtilisationStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ChecklistResultAuthorizations>();
			services.AddScoped<IDataProviderExtension<ChecklistResultDataObject>, ChecklistResultDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, UnitUnutilisationStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<UnitUnutilisationStoreProcedureDataObject>, UnitUnutilisationStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleAlertSubscriptionAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleAlertSubscriptionDataObject>, VehicleAlertSubscriptionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, OnDemandAuthorisationStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<OnDemandAuthorisationStoreProcedureDataObject>, OnDemandAuthorisationStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ImpactFrequencyPerTimeSlotViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<ImpactFrequencyPerTimeSlotViewDataObject>, ImpactFrequencyPerTimeSlotViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonToSiteVehicleNormalAccessViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonToSiteVehicleNormalAccessViewDataObject>, PersonToSiteVehicleNormalAccessViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SessionAuthorizations>();
			services.AddScoped<IDataProviderExtension<SessionDataObject>, SessionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SlamcoreDeviceFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<SlamcoreDeviceFilterDataObject>, SlamcoreDeviceFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DealerAuthorizations>();
			services.AddScoped<IDataProviderExtension<DealerDataObject>, DealerDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOUserAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOUserDataObject>, GOUserDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ServiceSettingsAuthorizations>();
			services.AddScoped<IDataProviderExtension<ServiceSettingsDataObject>, ServiceSettingsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, UpdateFirmwareRequestAuthorizations>();
			services.AddScoped<IDataProviderExtension<UpdateFirmwareRequestDataObject>, UpdateFirmwareRequestDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SlamcoreAPIKeyAuthorizations>();
			services.AddScoped<IDataProviderExtension<SlamcoreAPIKeyDataObject>, SlamcoreAPIKeyDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, FirmwareAuthorizations>();
			services.AddScoped<IDataProviderExtension<FirmwareDataObject>, FirmwareDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CardAuthorizations>();
			services.AddScoped<IDataProviderExtension<CardDataObject>, CardDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOUserDepartmentAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOUserDepartmentDataObject>, GOUserDepartmentDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CanruleAuthorizations>();
			services.AddScoped<IDataProviderExtension<CanruleDataObject>, CanruleDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ImpactFrequencyPerWeekDayViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<ImpactFrequencyPerWeekDayViewDataObject>, ImpactFrequencyPerWeekDayViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SlamcoreDeviceHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<SlamcoreDeviceHistoryDataObject>, SlamcoreDeviceHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleHireDehireHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleHireDehireHistoryDataObject>, VehicleHireDehireHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ChecklistFailurePerVechicleViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<ChecklistFailurePerVechicleViewDataObject>, ChecklistFailurePerVechicleViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ImpactReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<ImpactReportFilterDataObject>, ImpactReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, IncompletedChecklistViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<IncompletedChecklistViewDataObject>, IncompletedChecklistViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ReportSubscriptionAuthorizations>();
			services.AddScoped<IDataProviderExtension<ReportSubscriptionDataObject>, ReportSubscriptionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleLastGPSLocationViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleLastGPSLocationViewDataObject>, VehicleLastGPSLocationViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PSTATDetailsAuthorizations>();
			services.AddScoped<IDataProviderExtension<PSTATDetailsDataObject>, PSTATDetailsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, LicenseExpiryReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<LicenseExpiryReportFilterDataObject>, LicenseExpiryReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, FloorZonesAuthorizations>();
			services.AddScoped<IDataProviderExtension<FloorZonesDataObject>, FloorZonesDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CanruleDetailsAuthorizations>();
			services.AddScoped<IDataProviderExtension<CanruleDetailsDataObject>, CanruleDetailsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleHireDehireSynchronizationOptionsAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleHireDehireSynchronizationOptionsDataObject>, VehicleHireDehireSynchronizationOptionsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DriverLicenseExpiryStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<DriverLicenseExpiryStoreProcedureDataObject>, DriverLicenseExpiryStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, UnitSummaryReportAuthorizations>();
			services.AddScoped<IDataProviderExtension<UnitSummaryReportDataObject>, UnitSummaryReportDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DepartmentVehicleMasterCardAccessAuthorizations>();
			services.AddScoped<IDataProviderExtension<DepartmentVehicleMasterCardAccessDataObject>, DepartmentVehicleMasterCardAccessDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleUtilizationLastTwelveHoursStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>, VehicleUtilizationLastTwelveHoursStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ChecklistFailureViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<ChecklistFailureViewDataObject>, ChecklistFailureViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonChecklistLanguageSettingsAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonChecklistLanguageSettingsDataObject>, PersonChecklistLanguageSettingsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleSessionlessImpactAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleSessionlessImpactDataObject>, VehicleSessionlessImpactDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, LicenseByModelAuthorizations>();
			services.AddScoped<IDataProviderExtension<LicenseByModelDataObject>, LicenseByModelDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllMessageHistoryStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllMessageHistoryStoreProcedureDataObject>, AllMessageHistoryStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, IoTDeviceMessageCacheAuthorizations>();
			services.AddScoped<IDataProviderExtension<IoTDeviceMessageCacheDataObject>, IoTDeviceMessageCacheDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ProficiencyCombinedViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<ProficiencyCombinedViewDataObject>, ProficiencyCombinedViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CustomerSnapshotAuthorizations>();
			services.AddScoped<IDataProviderExtension<CustomerSnapshotDataObject>, CustomerSnapshotDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOGroupAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOGroupDataObject>, GOGroupDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehiclesPerModelReportAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehiclesPerModelReportDataObject>, VehiclesPerModelReportDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, TodaysImpactStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<TodaysImpactStoreProcedureDataObject>, TodaysImpactStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, BroadcastMessageHistoryFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<BroadcastMessageHistoryFilterDataObject>, BroadcastMessageHistoryFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DealerDriverAuthorizations>();
			services.AddScoped<IDataProviderExtension<DealerDriverDataObject>, DealerDriverDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, EmailGroupsAuthorizations>();
			services.AddScoped<IDataProviderExtension<EmailGroupsDataObject>, EmailGroupsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PreOperationalChecklistAuthorizations>();
			services.AddScoped<IDataProviderExtension<PreOperationalChecklistDataObject>, PreOperationalChecklistDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SynchronizationStatusReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<SynchronizationStatusReportFilterDataObject>, SynchronizationStatusReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SlamcoreDeviceAuthorizations>();
			services.AddScoped<IDataProviderExtension<SlamcoreDeviceDataObject>, SlamcoreDeviceDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SiteAuthorizations>();
			services.AddScoped<IDataProviderExtension<SiteDataObject>, SiteDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AlertSubscriptionAuthorizations>();
			services.AddScoped<IDataProviderExtension<AlertSubscriptionDataObject>, AlertSubscriptionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CustomerAuditAuthorizations>();
			services.AddScoped<IDataProviderExtension<CustomerAuditDataObject>, CustomerAuditDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PerVehicleNormalCardAccessAuthorizations>();
			services.AddScoped<IDataProviderExtension<PerVehicleNormalCardAccessDataObject>, PerVehicleNormalCardAccessDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PedestrianDetectionHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<PedestrianDetectionHistoryDataObject>, PedestrianDetectionHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonToPerVehicleNormalAccessViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonToPerVehicleNormalAccessViewDataObject>, PersonToPerVehicleNormalAccessViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DashboardDriverCardStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<DashboardDriverCardStoreProcedureDataObject>, DashboardDriverCardStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, TodaysImpactViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<TodaysImpactViewDataObject>, TodaysImpactViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DetailedSessionViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<DetailedSessionViewDataObject>, DetailedSessionViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ImportJobLogAuthorizations>();
			services.AddScoped<IDataProviderExtension<ImportJobLogDataObject>, ImportJobLogDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ProficiencyReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<ProficiencyReportFilterDataObject>, ProficiencyReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, OnDemandSessionAuthorizations>();
			services.AddScoped<IDataProviderExtension<OnDemandSessionDataObject>, OnDemandSessionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllVORStatusStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllVORStatusStoreProcedureDataObject>, AllVORStatusStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VORSettingHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<VORSettingHistoryDataObject>, VORSettingHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllUserSummaryStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllUserSummaryStoreProcedureDataObject>, AllUserSummaryStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CustomerSSODetailAuthorizations>();
			services.AddScoped<IDataProviderExtension<CustomerSSODetailDataObject>, CustomerSSODetailDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, UploadLogoRequestAuthorizations>();
			services.AddScoped<IDataProviderExtension<UploadLogoRequestDataObject>, UploadLogoRequestDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, AllVehicleCalibrationFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<AllVehicleCalibrationFilterDataObject>, AllVehicleCalibrationFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DepartmentHourSettingsAuthorizations>();
			services.AddScoped<IDataProviderExtension<DepartmentHourSettingsDataObject>, DepartmentHourSettingsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CategoryTemplateAuthorizations>();
			services.AddScoped<IDataProviderExtension<CategoryTemplateDataObject>, CategoryTemplateDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DashboardVehicleCardViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<DashboardVehicleCardViewDataObject>, DashboardVehicleCardViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CurrentStatusDriverViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<CurrentStatusDriverViewDataObject>, CurrentStatusDriverViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, UnitSummaryStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<UnitSummaryStoreProcedureDataObject>, UnitSummaryStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ReportTypeAuthorizations>();
			services.AddScoped<IDataProviderExtension<ReportTypeDataObject>, ReportTypeDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SiteVehicleMasterCardAccessAuthorizations>();
			services.AddScoped<IDataProviderExtension<SiteVehicleMasterCardAccessDataObject>, SiteVehicleMasterCardAccessDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonToDepartmentVehicleNormalAccessViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonToDepartmentVehicleNormalAccessViewDataObject>, PersonToDepartmentVehicleNormalAccessViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOUserGroupAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOUserGroupDataObject>, GOUserGroupDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CustomerFeatureSubscriptionAuthorizations>();
			services.AddScoped<IDataProviderExtension<CustomerFeatureSubscriptionDataObject>, CustomerFeatureSubscriptionDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, VehicleToPreOpChecklistViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<VehicleToPreOpChecklistViewDataObject>, VehicleToPreOpChecklistViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonToSiteVehicleMasterAccessViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonToSiteVehicleMasterAccessViewDataObject>, PersonToSiteVehicleMasterAccessViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DealerConfigurationAuthorizations>();
			services.AddScoped<IDataProviderExtension<DealerConfigurationDataObject>, DealerConfigurationDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, TodaysPreopCheckStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<TodaysPreopCheckStoreProcedureDataObject>, TodaysPreopCheckStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CustomerPreOperationalChecklistTemplateAuthorizations>();
			services.AddScoped<IDataProviderExtension<CustomerPreOperationalChecklistTemplateDataObject>, CustomerPreOperationalChecklistTemplateDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, CurrentDriverStatusChartViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<CurrentDriverStatusChartViewDataObject>, CurrentDriverStatusChartViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GOChangeDeltaAuthorizations>();
			services.AddScoped<IDataProviderExtension<GOChangeDeltaDataObject>, GOChangeDeltaDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GO2FAConfigurationAuthorizations>();
			services.AddScoped<IDataProviderExtension<GO2FAConfigurationDataObject>, GO2FAConfigurationDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PreOpReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<PreOpReportFilterDataObject>, PreOpReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, SiteFloorPlanAuthorizations>();
			services.AddScoped<IDataProviderExtension<SiteFloorPlanDataObject>, SiteFloorPlanDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, BroadcastMessageHistoryAuthorizations>();
			services.AddScoped<IDataProviderExtension<BroadcastMessageHistoryDataObject>, BroadcastMessageHistoryDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, GoUserToCustomerAuthorizations>();
			services.AddScoped<IDataProviderExtension<GoUserToCustomerDataObject>, GoUserToCustomerDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, ImportJobBatchAuthorizations>();
			services.AddScoped<IDataProviderExtension<ImportJobBatchDataObject>, ImportJobBatchDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonDataObject>, PersonDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DetailedVORSessionStoreProcedureAuthorizations>();
			services.AddScoped<IDataProviderExtension<DetailedVORSessionStoreProcedureDataObject>, DetailedVORSessionStoreProcedureDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PersonToModelVehicleNormalAccessViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<PersonToModelVehicleNormalAccessViewDataObject>, PersonToModelVehicleNormalAccessViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, NetworkSettingsAuthorizations>();
			services.AddScoped<IDataProviderExtension<NetworkSettingsDataObject>, NetworkSettingsDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, DepartmentAuthorizations>();
			services.AddScoped<IDataProviderExtension<DepartmentDataObject>, DepartmentDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, MachineUnlockReportFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<MachineUnlockReportFilterDataObject>, MachineUnlockReportFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, PerVehicleMasterCardAccessAuthorizations>();
			services.AddScoped<IDataProviderExtension<PerVehicleMasterCardAccessDataObject>, PerVehicleMasterCardAccessDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, UnitUtilisationCombinedViewAuthorizations>();
			services.AddScoped<IDataProviderExtension<UnitUtilisationCombinedViewDataObject>, UnitUtilisationCombinedViewDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, FeatureSubscriptionsFilterAuthorizations>();
			services.AddScoped<IDataProviderExtension<FeatureSubscriptionsFilterDataObject>, FeatureSubscriptionsFilterDataProviderSecurityExtension>();
			services.AddSingleton<IEntityAuthorizations, OnDemandSettingsAuthorizations>();
			services.AddScoped<IDataProviderExtension<OnDemandSettingsDataObject>, OnDemandSettingsDataProviderSecurityExtension>();
            #endregion Authorizations & Data Provider Extensions

            #region Server Component Security Extensions
			{ // Server Component Security Extensions
				services.AddScoped<ISlamcoreVehicleTelemetryExportComponentExtension, SlamcoreVehicleTelemetryExportComponentSecurityExtension>();
				services.AddScoped<IDriverAuthenticationAPIExtension, DriverAuthenticationAPISecurityExtension>();
				services.AddScoped<IDriverImpactAPIExtension, DriverImpactAPISecurityExtension>();
				services.AddScoped<IPstatAPIExtension, PstatAPISecurityExtension>();
				services.AddScoped<IPreOperationalChecklistImportComponentExtension, PreOperationalChecklistImportComponentSecurityExtension>();
				services.AddScoped<ISlamcoreDeviceExportComponentExtension, SlamcoreDeviceExportComponentSecurityExtension>();
				services.AddScoped<IEmailServiceExtension, EmailServiceSecurityExtension>();
				services.AddScoped<IDealerCategoryImportComponentExtension, DealerCategoryImportComponentSecurityExtension>();
				services.AddScoped<IPedestrianDetectionAPIExtension, PedestrianDetectionAPISecurityExtension>();
				services.AddScoped<IVehicleAccessImportComponentExtension, VehicleAccessImportComponentSecurityExtension>();
				services.AddScoped<IModuleUtilitiesExtension, ModuleUtilitiesSecurityExtension>();
				services.AddScoped<ISiteAPIExtension, SiteAPISecurityExtension>();
				services.AddScoped<IFirmwareUpdateExtension, FirmwareUpdateSecurityExtension>();
				services.AddScoped<IPersonExportComponentExtension, PersonExportComponentSecurityExtension>();
				services.AddScoped<ICustomerUtilitiesExtension, CustomerUtilitiesSecurityExtension>();
				services.AddScoped<IDepartmentAPIExtension, DepartmentAPISecurityExtension>();
				services.AddScoped<IPreOpChecklistReportExportComponentExtension, PreOpChecklistReportExportComponentSecurityExtension>();
				services.AddScoped<ISlamcorePedestrianDetectionComponentExtension, SlamcorePedestrianDetectionComponentSecurityExtension>();
				services.AddScoped<IVehicleOtherSettingsImportComponentExtension, VehicleOtherSettingsImportComponentSecurityExtension>();
				services.AddScoped<IModuleParametersAPIExtension, ModuleParametersAPISecurityExtension>();
				services.AddScoped<IMachineUnlockReportExportComponentExtension, MachineUnlockReportExportComponentSecurityExtension>();
				services.AddScoped<IPedestrianDetectionReportExportComponentExtension, PedestrianDetectionReportExportComponentSecurityExtension>();
				services.AddScoped<IPreopXLSXImportComponentExtension, PreopXLSXImportComponentSecurityExtension>();
				services.AddScoped<IGOSecurityProviderExtension, GOSecurityProviderSecurityExtension>();
				services.AddScoped<IGPSMessageExtension, GPSMessageSecurityExtension>();
				services.AddScoped<IUnitUtilisationExportComponentExtension, UnitUtilisationExportComponentSecurityExtension>();
				services.AddScoped<IVORStatusAPIExtension, VORStatusAPISecurityExtension>();
				services.AddScoped<IAlertHistoryAPIExtension, AlertHistoryAPISecurityExtension>();
				services.AddScoped<IServiceCheckReportExportComponentExtension, ServiceCheckReportExportComponentSecurityExtension>();
				services.AddScoped<ICardImportComponentExtension, CardImportComponentSecurityExtension>();
				services.AddScoped<IDriverAccessAbuseReportExportComponentExtension, DriverAccessAbuseReportExportComponentSecurityExtension>();
				services.AddScoped<ISlamcorePathHistoryExportComponentExtension, SlamcorePathHistoryExportComponentSecurityExtension>();
				services.AddScoped<IGOReportsHelperExtension, GOReportsHelperSecurityExtension>();
				services.AddScoped<ISessionAPIExtension, SessionAPISecurityExtension>();
				services.AddScoped<IUpdateVehicleLastServiceDateImportComponentExtension, UpdateVehicleLastServiceDateImportComponentSecurityExtension>();
				services.AddScoped<IDriverSessionAPIExtension, DriverSessionAPISecurityExtension>();
				services.AddScoped<IVehicleImportComponentExtension, VehicleImportComponentSecurityExtension>();
				services.AddScoped<IBroadcastMessageHistoryAPIExtension, BroadcastMessageHistoryAPISecurityExtension>();
				services.AddScoped<IBroadcastMessageHistoryExportComponentExtension, BroadcastMessageHistoryExportComponentSecurityExtension>();
				services.AddScoped<IProficiencyReportExportComponentExtension, ProficiencyReportExportComponentSecurityExtension>();
				services.AddScoped<IGeneralLicenseImportComponentExtension, GeneralLicenseImportComponentSecurityExtension>();
				services.AddScoped<IDataUtitiliesExtension, DataUtitiliesSecurityExtension>();
				services.AddScoped<IVehicleAccessImportComponentExtension, VehicleAccessImportComponentSecurityExtension>();
				services.AddScoped<IVehicleExportComponentExtension, VehicleExportComponentSecurityExtension>();
				services.AddScoped<IVehicleHourMeterExtension, VehicleHourMeterSecurityExtension>();
				services.AddScoped<IDriverImportComponentExtension, DriverImportComponentSecurityExtension>();
				services.AddScoped<IOutgoingMessageAPIExtension, OutgoingMessageAPISecurityExtension>();
				services.AddScoped<IVehicleImportComponentExtension, VehicleImportComponentSecurityExtension>();
				services.AddScoped<IGeneralProductivityReportExportComponentExtension, GeneralProductivityReportExportComponentSecurityExtension>();
				services.AddScoped<ILicenseByModelImportComponentExtension, LicenseByModelImportComponentSecurityExtension>();
				services.AddScoped<ICustomerImportComponentExtension, CustomerImportComponentSecurityExtension>();
				services.AddScoped<IVehicleCalibrationReportExportComponentExtension, VehicleCalibrationReportExportComponentSecurityExtension>();
				services.AddScoped<ISynchronizationStatusReportExportComponentExtension, SynchronizationStatusReportExportComponentSecurityExtension>();
				services.AddScoped<IMachineUnlockAPIExtension, MachineUnlockAPISecurityExtension>();
				services.AddScoped<IImpactReportExportComponentExtension, ImpactReportExportComponentSecurityExtension>();
				services.AddScoped<IGO2FASecurityProviderComponentExtension, GO2FASecurityProviderComponentSecurityExtension>();
				services.AddScoped<IChecklistSettingImportComponentExtension, ChecklistSettingImportComponentSecurityExtension>();
				services.AddScoped<IPreopChecklistAPIExtension, PreopChecklistAPISecurityExtension>();
				services.AddScoped<IVORReportExportComponentExtension, VORReportExportComponentSecurityExtension>();
				services.AddScoped<IGeneralLicenseImportComponentExtension, GeneralLicenseImportComponentSecurityExtension>();
				services.AddScoped<IVehicleCurrentStatusReportExportComponentExtension, VehicleCurrentStatusReportExportComponentSecurityExtension>();
				services.AddScoped<IEHelpdeskAPIExtension, EHelpdeskAPISecurityExtension>();
				services.AddScoped<ISlamcorePathHistoryExportComponentExtension, SlamcorePathHistoryExportComponentSecurityExtension>();
				services.AddScoped<IIoTHubManagerExtension, IoTHubManagerSecurityExtension>();
				services.AddScoped<ISupervisorAccessImportComponentExtension, SupervisorAccessImportComponentSecurityExtension>();
				services.AddScoped<ISyncProcessExtension, SyncProcessSecurityExtension>();
				services.AddScoped<ISiteImportComponentExtension, SiteImportComponentSecurityExtension>();
				services.AddScoped<IGeneralLicenseImportComponentExtension, GeneralLicenseImportComponentSecurityExtension>();
				services.AddScoped<IPersonImportComponentExtension, PersonImportComponentSecurityExtension>();
				services.AddScoped<IEmailGroupsToPersonUtilitiesExtension, EmailGroupsToPersonUtilitiesSecurityExtension>();
				services.AddScoped<IReportActionsAPIExtension, ReportActionsAPISecurityExtension>();
				services.AddScoped<ISendQuestionsToVehicleExtension, SendQuestionsToVehicleSecurityExtension>();
				services.AddScoped<IPersonAPIExtension, PersonAPISecurityExtension>();
				services.AddScoped<ISlamcoreAlertHistoryComponentExtension, SlamcoreAlertHistoryComponentSecurityExtension>();
				services.AddScoped<ISpareModuleImportComponentExtension, SpareModuleImportComponentSecurityExtension>();
				services.AddScoped<IVehicleAccessCreationExtension, VehicleAccessCreationSecurityExtension>();
				services.AddScoped<IRegisterModuleAPIExtension, RegisterModuleAPISecurityExtension>();
				services.AddScoped<IDepartmentImportComponentExtension, DepartmentImportComponentSecurityExtension>();
				services.AddScoped<IModelUtilitiesExtension, ModelUtilitiesSecurityExtension>();
				services.AddScoped<ISlamcoreAlertHistoryComponentExtension, SlamcoreAlertHistoryComponentSecurityExtension>();
				services.AddScoped<ILocationServiceAPIExtension, LocationServiceAPISecurityExtension>();
				services.AddScoped<ILicenseExpiryReportExportComponentExtension, LicenseExpiryReportExportComponentSecurityExtension>();
				services.AddScoped<IVehicleAccessUtilitiesExtension, VehicleAccessUtilitiesSecurityExtension>();
				services.AddScoped<IPreopExportComponentExtension, PreopExportComponentSecurityExtension>();
				services.AddScoped<IPersonToDepartmentVehicleNormalAccessViewExportComponentExtension, PersonToDepartmentVehicleNormalAccessViewExportComponentSecurityExtension>();
				services.AddScoped<ICustomerAPIExtension, CustomerAPISecurityExtension>();
				services.AddScoped<IVehicleAPIExtension, VehicleAPISecurityExtension>();
				services.AddScoped<IDriverCurrentStatusReportExportComponentExtension, DriverCurrentStatusReportExportComponentSecurityExtension>();
				services.AddScoped<IGOFileUploaderExtension, GOFileUploaderSecurityExtension>();
			} // End Server Component Security Extensions
            #endregion Server Component Security Extensions
            #region Data Provider Dispatchers
			services.AddScoped<IDataProviderDispatcher<ChecklistDetailDataObject>, ChecklistDetailDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SlamcoreAwareAuthenticationDetailsDataObject>, SlamcoreAwareAuthenticationDetailsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<FeatureSubscriptionTemplateDataObject>, FeatureSubscriptionTemplateDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonToModelVehicleMasterAccessViewDataObject>, PersonToModelVehicleMasterAccessViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleLockoutDataObject>, VehicleLockoutDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<RevisionDataObject>, RevisionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AlertDataObject>, AlertDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<WebsiteUserDataObject>, WebsiteUserDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DriverAccessAbuseFilterDataObject>, DriverAccessAbuseFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllLicenseExpiryViewDataObject>, AllLicenseExpiryViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DriverLicenseExpiryViewDataObject>, DriverLicenseExpiryViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ContactPersonInformationDataObject>, ContactPersonInformationDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ChecklistSettingsDataObject>, ChecklistSettingsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleDiagnosticDataObject>, VehicleDiagnosticDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOUser2FADataObject>, GOUser2FADataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleGPSDataObject>, VehicleGPSDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ModuleDataObject>, ModuleDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonToPerVehicleMasterAccessViewDataObject>, PersonToPerVehicleMasterAccessViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AccessGroupDataObject>, AccessGroupDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<HelpDataObject>, HelpDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ChecklistStatusViewDataObject>, ChecklistStatusViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CurrentStatusCombinedViewDataObject>, CurrentStatusCombinedViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CurrentVehicleStatusChartViewDataObject>, CurrentVehicleStatusChartViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DealerFeatureSubscriptionDataObject>, DealerFeatureSubscriptionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<MessageHistoryDataObject>, MessageHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AccessGroupToSiteDataObject>, AccessGroupToSiteDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllDriverAccessAbuseStoreProcedureDataObject>, AllDriverAccessAbuseStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CustomerToModelDataObject>, CustomerToModelDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllEmailSubscriptionStoreProcedureDataObject>, AllEmailSubscriptionStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AlertSummaryStoreProcedureDataObject>, AlertSummaryStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DashboardVehicleCardStoreProcedureDataObject>, DashboardVehicleCardStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<RegionDataObject>, RegionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ModelDataObject>, ModelDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllVORSessionsPerVehicleStoreProcedureDataObject>, AllVORSessionsPerVehicleStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SlamcoreDeviceConnectionViewDataObject>, SlamcoreDeviceConnectionViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllImpactsViewDataObject>, AllImpactsViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CustomerDataObject>, CustomerDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DepartmentChecklistDataObject>, DepartmentChecklistDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ModuleHistoryDataObject>, ModuleHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CountryDataObject>, CountryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllChecklistResultViewDataObject>, AllChecklistResultViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GPSHistoryDataObject>, GPSHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ImpactsForVehicleViewDataObject>, ImpactsForVehicleViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllVehicleUnlocksViewDataObject>, AllVehicleUnlocksViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SlamcorePedestrianDetectionDataObject>, SlamcorePedestrianDetectionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ImpactFrequencyPerWeekMonthViewDataObject>, ImpactFrequencyPerWeekMonthViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleUtilizationLastTwelveHoursViewDataObject>, VehicleUtilizationLastTwelveHoursViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<IOFIELDDataObject>, IOFIELDDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<EmailDataObject>, EmailDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<LicenceDetailDataObject>, LicenceDetailDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PedestrianDetectionHistoryFilterDataObject>, PedestrianDetectionHistoryFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOLoginHistoryDataObject>, GOLoginHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<MainDashboardFilterDataObject>, MainDashboardFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllVehicleCalibrationStoreProcedureDataObject>, AllVehicleCalibrationStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AccessGroupTemplateDataObject>, AccessGroupTemplateDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CardToCardAccessDataObject>, CardToCardAccessDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ZoneCoordinatesDataObject>, ZoneCoordinatesDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SnapshotDataObject>, SnapshotDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOTaskDataObject>, GOTaskDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GeneralProductivityReportFilterDataObject>, GeneralProductivityReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DepartmentVehicleNormalCardAccessDataObject>, DepartmentVehicleNormalCardAccessDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CustomerToPersonViewDataObject>, CustomerToPersonViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleSlamcoreLocationHistoryDataObject>, VehicleSlamcoreLocationHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PermissionDataObject>, PermissionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SessionDetailsDataObject>, SessionDetailsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GORoleDataObject>, GORoleDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GeneralProductivityPerVehicleViewDataObject>, GeneralProductivityPerVehicleViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleDataObject>, VehicleDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GeneralProductivityPerDriverViewLatestDataObject>, GeneralProductivityPerDriverViewLatestDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DriverDataObject>, DriverDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<EmailGroupsToPersonDataObject>, EmailGroupsToPersonDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOGroupRoleDataObject>, GOGroupRoleDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ModelVehicleMasterCardAccessDataObject>, ModelVehicleMasterCardAccessDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<EmailSubscriptionReportFilterDataObject>, EmailSubscriptionReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleProficiencyViewDataObject>, VehicleProficiencyViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DashboardDriverCardViewDataObject>, DashboardDriverCardViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DriverProficiencyViewDataObject>, DriverProficiencyViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOSecurityTokensDataObject>, GOSecurityTokensDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AlertHistoryDataObject>, AlertHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DashboardFilterDataObject>, DashboardFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VORReportFilterDataObject>, VORReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<OnDemandAuthorisationFilterDataObject>, OnDemandAuthorisationFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<WebsiteRoleDataObject>, WebsiteRoleDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOUserRoleDataObject>, GOUserRoleDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GeneralProductivityViewDataObject>, GeneralProductivityViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<InspectionDataObject>, InspectionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ImpactDataObject>, ImpactDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonToDepartmentVehicleMasterAccessViewDataObject>, PersonToDepartmentVehicleMasterAccessViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CurrentStatusVehicleViewDataObject>, CurrentStatusVehicleViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CustomerModelDataObject>, CustomerModelDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<TagDataObject>, TagDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleBroadcastMessageDataObject>, VehicleBroadcastMessageDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<FloorPlanDataObject>, FloorPlanDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<TodaysPreopCheckViewDataObject>, TodaysPreopCheckViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ModelVehicleNormalCardAccessDataObject>, ModelVehicleNormalCardAccessDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SiteVehicleNormalCardAccessDataObject>, SiteVehicleNormalCardAccessDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ImportJobStatusDataObject>, ImportJobStatusDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleOtherSettingsDataObject>, VehicleOtherSettingsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DashboardFilterMoreFieldsDataObject>, DashboardFilterMoreFieldsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleSupervisorsViewDataObject>, VehicleSupervisorsViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ExportJobStatusDataObject>, ExportJobStatusDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<TimezoneDataObject>, TimezoneDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<HireDeHireReportFilterDataObject>, HireDeHireReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<LoggedHoursVersusSeatHoursViewDataObject>, LoggedHoursVersusSeatHoursViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonAllocationDataObject>, PersonAllocationDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<BroadcastMessageDataObject>, BroadcastMessageDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VORReportCombinedViewDataObject>, VORReportCombinedViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<UnitUtilisationStoreProcedureDataObject>, UnitUtilisationStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ChecklistResultDataObject>, ChecklistResultDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<UnitUnutilisationStoreProcedureDataObject>, UnitUnutilisationStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleAlertSubscriptionDataObject>, VehicleAlertSubscriptionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<OnDemandAuthorisationStoreProcedureDataObject>, OnDemandAuthorisationStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ImpactFrequencyPerTimeSlotViewDataObject>, ImpactFrequencyPerTimeSlotViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonToSiteVehicleNormalAccessViewDataObject>, PersonToSiteVehicleNormalAccessViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SessionDataObject>, SessionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SlamcoreDeviceFilterDataObject>, SlamcoreDeviceFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DealerDataObject>, DealerDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOUserDataObject>, GOUserDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ServiceSettingsDataObject>, ServiceSettingsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<UpdateFirmwareRequestDataObject>, UpdateFirmwareRequestDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SlamcoreAPIKeyDataObject>, SlamcoreAPIKeyDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<FirmwareDataObject>, FirmwareDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CardDataObject>, CardDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOUserDepartmentDataObject>, GOUserDepartmentDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CanruleDataObject>, CanruleDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ImpactFrequencyPerWeekDayViewDataObject>, ImpactFrequencyPerWeekDayViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SlamcoreDeviceHistoryDataObject>, SlamcoreDeviceHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleHireDehireHistoryDataObject>, VehicleHireDehireHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ChecklistFailurePerVechicleViewDataObject>, ChecklistFailurePerVechicleViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ImpactReportFilterDataObject>, ImpactReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<IncompletedChecklistViewDataObject>, IncompletedChecklistViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ReportSubscriptionDataObject>, ReportSubscriptionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleLastGPSLocationViewDataObject>, VehicleLastGPSLocationViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PSTATDetailsDataObject>, PSTATDetailsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<LicenseExpiryReportFilterDataObject>, LicenseExpiryReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<FloorZonesDataObject>, FloorZonesDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CanruleDetailsDataObject>, CanruleDetailsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleHireDehireSynchronizationOptionsDataObject>, VehicleHireDehireSynchronizationOptionsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DriverLicenseExpiryStoreProcedureDataObject>, DriverLicenseExpiryStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<UnitSummaryReportDataObject>, UnitSummaryReportDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DepartmentVehicleMasterCardAccessDataObject>, DepartmentVehicleMasterCardAccessDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>, VehicleUtilizationLastTwelveHoursStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ChecklistFailureViewDataObject>, ChecklistFailureViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonChecklistLanguageSettingsDataObject>, PersonChecklistLanguageSettingsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleSessionlessImpactDataObject>, VehicleSessionlessImpactDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<LicenseByModelDataObject>, LicenseByModelDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllMessageHistoryStoreProcedureDataObject>, AllMessageHistoryStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<IoTDeviceMessageCacheDataObject>, IoTDeviceMessageCacheDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ProficiencyCombinedViewDataObject>, ProficiencyCombinedViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CustomerSnapshotDataObject>, CustomerSnapshotDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOGroupDataObject>, GOGroupDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehiclesPerModelReportDataObject>, VehiclesPerModelReportDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<TodaysImpactStoreProcedureDataObject>, TodaysImpactStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<BroadcastMessageHistoryFilterDataObject>, BroadcastMessageHistoryFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DealerDriverDataObject>, DealerDriverDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<EmailGroupsDataObject>, EmailGroupsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PreOperationalChecklistDataObject>, PreOperationalChecklistDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SynchronizationStatusReportFilterDataObject>, SynchronizationStatusReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SlamcoreDeviceDataObject>, SlamcoreDeviceDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SiteDataObject>, SiteDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AlertSubscriptionDataObject>, AlertSubscriptionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CustomerAuditDataObject>, CustomerAuditDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PerVehicleNormalCardAccessDataObject>, PerVehicleNormalCardAccessDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PedestrianDetectionHistoryDataObject>, PedestrianDetectionHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonToPerVehicleNormalAccessViewDataObject>, PersonToPerVehicleNormalAccessViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DashboardDriverCardStoreProcedureDataObject>, DashboardDriverCardStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<TodaysImpactViewDataObject>, TodaysImpactViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DetailedSessionViewDataObject>, DetailedSessionViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ImportJobLogDataObject>, ImportJobLogDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ProficiencyReportFilterDataObject>, ProficiencyReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<OnDemandSessionDataObject>, OnDemandSessionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllVORStatusStoreProcedureDataObject>, AllVORStatusStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VORSettingHistoryDataObject>, VORSettingHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllUserSummaryStoreProcedureDataObject>, AllUserSummaryStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CustomerSSODetailDataObject>, CustomerSSODetailDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<UploadLogoRequestDataObject>, UploadLogoRequestDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<AllVehicleCalibrationFilterDataObject>, AllVehicleCalibrationFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DepartmentHourSettingsDataObject>, DepartmentHourSettingsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CategoryTemplateDataObject>, CategoryTemplateDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DashboardVehicleCardViewDataObject>, DashboardVehicleCardViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CurrentStatusDriverViewDataObject>, CurrentStatusDriverViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<UnitSummaryStoreProcedureDataObject>, UnitSummaryStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ReportTypeDataObject>, ReportTypeDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SiteVehicleMasterCardAccessDataObject>, SiteVehicleMasterCardAccessDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonToDepartmentVehicleNormalAccessViewDataObject>, PersonToDepartmentVehicleNormalAccessViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOUserGroupDataObject>, GOUserGroupDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CustomerFeatureSubscriptionDataObject>, CustomerFeatureSubscriptionDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<VehicleToPreOpChecklistViewDataObject>, VehicleToPreOpChecklistViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonToSiteVehicleMasterAccessViewDataObject>, PersonToSiteVehicleMasterAccessViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DealerConfigurationDataObject>, DealerConfigurationDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<TodaysPreopCheckStoreProcedureDataObject>, TodaysPreopCheckStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CustomerPreOperationalChecklistTemplateDataObject>, CustomerPreOperationalChecklistTemplateDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<CurrentDriverStatusChartViewDataObject>, CurrentDriverStatusChartViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GOChangeDeltaDataObject>, GOChangeDeltaDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GO2FAConfigurationDataObject>, GO2FAConfigurationDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PreOpReportFilterDataObject>, PreOpReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<SiteFloorPlanDataObject>, SiteFloorPlanDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<BroadcastMessageHistoryDataObject>, BroadcastMessageHistoryDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<GoUserToCustomerDataObject>, GoUserToCustomerDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<ImportJobBatchDataObject>, ImportJobBatchDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonDataObject>, PersonDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DetailedVORSessionStoreProcedureDataObject>, DetailedVORSessionStoreProcedureDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PersonToModelVehicleNormalAccessViewDataObject>, PersonToModelVehicleNormalAccessViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<NetworkSettingsDataObject>, NetworkSettingsDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<DepartmentDataObject>, DepartmentDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<MachineUnlockReportFilterDataObject>, MachineUnlockReportFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<PerVehicleMasterCardAccessDataObject>, PerVehicleMasterCardAccessDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<UnitUtilisationCombinedViewDataObject>, UnitUtilisationCombinedViewDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<FeatureSubscriptionsFilterDataObject>, FeatureSubscriptionsFilterDataProviderDispatcher>();
			services.AddScoped<IDataProviderDispatcher<OnDemandSettingsDataObject>, OnDemandSettingsDataProviderDispatcher>();
            #endregion Data Provider Dispatchers
					
			services.AddTransient<IImportJobLogger, ImportJobLogger>();
			services.AddScoped<IDataImporter, DataImporter>();
			services.AddTransient<IImportTracker, ImportTracker>();
			services.AddTransient<IMachineUnlockReportExportSection0Component, MachineUnlockReportExportSection0Component>();	
			services.AddTransient<IGeneralLicenseImportSection0Component, GeneralLicenseImportSection0Component>();	
			services.AddTransient<ISlamcoreDeviceExportSection0Component, SlamcoreDeviceExportSection0Component>();	
			services.AddTransient<IPersonToDepartmentVehicleNormalAccessViewExportSection0Component, PersonToDepartmentVehicleNormalAccessViewExportSection0Component>();	
			services.AddTransient<IPreopXLSXImportSection0Component, PreopXLSXImportSection0Component>();	
			services.AddTransient<IGeneralProductivityReportExportSection0Component, GeneralProductivityReportExportSection0Component>();	
			services.AddTransient<IGeneralProductivityReportExportSection1Component, GeneralProductivityReportExportSection1Component>();	
			services.AddTransient<ILicenseExpiryReportExportSection0Component, LicenseExpiryReportExportSection0Component>();	
			services.AddTransient<IPersonExportSection0Component, PersonExportSection0Component>();	
			services.AddTransient<IDriverImportSection0Component, DriverImportSection0Component>();	
			services.AddTransient<IImpactReportExportSection0Component, ImpactReportExportSection0Component>();	
			services.AddTransient<IVehicleAccessImportSection0Component, VehicleAccessImportSection0Component>();	
			services.AddTransient<ICustomerImportSection0Component, CustomerImportSection0Component>();	
			services.AddTransient<IVORReportExportSection0Component, VORReportExportSection0Component>();	
			services.AddTransient<IVORReportExportSection1Component, VORReportExportSection1Component>();	
			services.AddTransient<ICardImportSection0Component, CardImportSection0Component>();	
			services.AddTransient<IPedestrianDetectionReportExportSection0Component, PedestrianDetectionReportExportSection0Component>();	
			services.AddTransient<ISupervisorAccessImportSection0Component, SupervisorAccessImportSection0Component>();	
			services.AddTransient<IPreOperationalChecklistImportSection0Component, PreOperationalChecklistImportSection0Component>();	
			services.AddTransient<ISpareModuleImportSection0Component, SpareModuleImportSection0Component>();	
			services.AddTransient<IVehicleImportSection0Component, VehicleImportSection0Component>();	
			services.AddTransient<IDriverAccessAbuseReportExportSection0Component, DriverAccessAbuseReportExportSection0Component>();	
			services.AddTransient<ISlamcoreVehicleTelemetryExportSection0Component, SlamcoreVehicleTelemetryExportSection0Component>();	
			services.AddTransient<ISiteImportSection0Component, SiteImportSection0Component>();	
			services.AddTransient<IVehicleCalibrationReportExportSection0Component, VehicleCalibrationReportExportSection0Component>();	
			services.AddTransient<IVehicleCurrentStatusReportExportSection0Component, VehicleCurrentStatusReportExportSection0Component>();	
			services.AddTransient<IPersonImportSection0Component, PersonImportSection0Component>();	
			services.AddTransient<IPreopExportSection0Component, PreopExportSection0Component>();	
			services.AddTransient<IServiceCheckReportExportSection0Component, ServiceCheckReportExportSection0Component>();	
			services.AddTransient<IDriverCurrentStatusReportExportSection0Component, DriverCurrentStatusReportExportSection0Component>();	
			services.AddTransient<IBroadcastMessageHistoryExportSection0Component, BroadcastMessageHistoryExportSection0Component>();	
			services.AddTransient<ISynchronizationStatusReportExportSection0Component, SynchronizationStatusReportExportSection0Component>();	
			services.AddTransient<IDealerCategoryImportSection0Component, DealerCategoryImportSection0Component>();	
			services.AddTransient<IDepartmentImportSection0Component, DepartmentImportSection0Component>();	
			services.AddTransient<IPreOpChecklistReportExportSection0Component, PreOpChecklistReportExportSection0Component>();	
			services.AddTransient<IProficiencyReportExportSection0Component, ProficiencyReportExportSection0Component>();	
			services.AddTransient<IProficiencyReportExportSection1Component, ProficiencyReportExportSection1Component>();	
			services.AddTransient<IUnitUtilisationExportSection0Component, UnitUtilisationExportSection0Component>();	
			services.AddTransient<IVehicleExportSection0Component, VehicleExportSection0Component>();	
			services.AddTransient<ISlamcorePathHistoryExportSection0Component, SlamcorePathHistoryExportSection0Component>();	
			services.AddTransient<IVehicleOtherSettingsImportSection0Component, VehicleOtherSettingsImportSection0Component>();	
			services.AddTransient<ISlamcoreAlertHistorySection0Component, SlamcoreAlertHistorySection0Component>();	
			services.AddTransient<ILicenseByModelImportSection0Component, LicenseByModelImportSection0Component>();	
			services.AddTransient<IUpdateVehicleLastServiceDateImportSection0Component, UpdateVehicleLastServiceDateImportSection0Component>();	
			services.AddTransient<IChecklistSettingImportSection0Component, ChecklistSettingImportSection0Component>();	
			services.AddTransient<ISlamcorePedestrianDetectionSection0Component, SlamcorePedestrianDetectionSection0Component>();	
      
			// Extension providers for Entities
            #region Extension providers for Entities
			{
				// ChecklistDetail

				services.AddSingleton<IEntityApiExtensionProvider<ChecklistDetailDataObject>, EntityApiExtensionProvider<ChecklistDetailDataObject>>();
				// SlamcoreAwareAuthenticationDetails

				services.AddSingleton<IEntityApiExtensionProvider<SlamcoreAwareAuthenticationDetailsDataObject>, EntityApiExtensionProvider<SlamcoreAwareAuthenticationDetailsDataObject>>();
				// FeatureSubscriptionTemplate

				services.AddSingleton<IEntityApiExtensionProvider<FeatureSubscriptionTemplateDataObject>, EntityApiExtensionProvider<FeatureSubscriptionTemplateDataObject>>();
				// PersonToModelVehicleMasterAccessView

				services.AddSingleton<IEntityApiExtensionProvider<PersonToModelVehicleMasterAccessViewDataObject>, EntityApiExtensionProvider<PersonToModelVehicleMasterAccessViewDataObject>>();
				// VehicleLockout

				services.AddSingleton<IEntityApiExtensionProvider<VehicleLockoutDataObject>, EntityApiExtensionProvider<VehicleLockoutDataObject>>();
				// Revision

				services.AddSingleton<IEntityApiExtensionProvider<RevisionDataObject>, EntityApiExtensionProvider<RevisionDataObject>>();
				// Alert

				services.AddSingleton<IEntityApiExtensionProvider<AlertDataObject>, EntityApiExtensionProvider<AlertDataObject>>();
				// WebsiteUser

				services.AddSingleton<IEntityApiExtensionProvider<WebsiteUserDataObject>, EntityApiExtensionProvider<WebsiteUserDataObject>>();
				// DriverAccessAbuseFilter

				services.AddSingleton<IEntityApiExtensionProvider<DriverAccessAbuseFilterDataObject>, EntityApiExtensionProvider<DriverAccessAbuseFilterDataObject>>();
				// AllLicenseExpiryView

				services.AddSingleton<IEntityApiExtensionProvider<AllLicenseExpiryViewDataObject>, EntityApiExtensionProvider<AllLicenseExpiryViewDataObject>>();
				// DriverLicenseExpiryView

				services.AddSingleton<IEntityApiExtensionProvider<DriverLicenseExpiryViewDataObject>, EntityApiExtensionProvider<DriverLicenseExpiryViewDataObject>>();
				// ContactPersonInformation

				services.AddSingleton<IEntityApiExtensionProvider<ContactPersonInformationDataObject>, EntityApiExtensionProvider<ContactPersonInformationDataObject>>();
				// ChecklistSettings

				services.AddSingleton<IEntityApiExtensionProvider<ChecklistSettingsDataObject>, EntityApiExtensionProvider<ChecklistSettingsDataObject>>();
				// VehicleDiagnostic

				services.AddSingleton<IEntityApiExtensionProvider<VehicleDiagnosticDataObject>, EntityApiExtensionProvider<VehicleDiagnosticDataObject>>();
				// GOUser2FA

				services.AddSingleton<IEntityApiExtensionProvider<GOUser2FADataObject>, EntityApiExtensionProvider<GOUser2FADataObject>>();
				// VehicleGPS

				services.AddSingleton<IEntityApiExtensionProvider<VehicleGPSDataObject>, EntityApiExtensionProvider<VehicleGPSDataObject>>();
				// Module

				services.AddSingleton<IEntityApiExtensionProvider<ModuleDataObject>, EntityApiExtensionProvider<ModuleDataObject>>();
				// PersonToPerVehicleMasterAccessView

				services.AddSingleton<IEntityApiExtensionProvider<PersonToPerVehicleMasterAccessViewDataObject>, EntityApiExtensionProvider<PersonToPerVehicleMasterAccessViewDataObject>>();
				// AccessGroup

				services.AddSingleton<IEntityApiExtensionProvider<AccessGroupDataObject>, EntityApiExtensionProvider<AccessGroupDataObject>>();
				// Help

				services.AddSingleton<IEntityApiExtensionProvider<HelpDataObject>, EntityApiExtensionProvider<HelpDataObject>>();
				// ChecklistStatusView

				services.AddSingleton<IEntityApiExtensionProvider<ChecklistStatusViewDataObject>, EntityApiExtensionProvider<ChecklistStatusViewDataObject>>();
				// CurrentStatusCombinedView

				services.AddSingleton<IEntityApiExtensionProvider<CurrentStatusCombinedViewDataObject>, EntityApiExtensionProvider<CurrentStatusCombinedViewDataObject>>();
				// CurrentVehicleStatusChartView

				services.AddSingleton<IEntityApiExtensionProvider<CurrentVehicleStatusChartViewDataObject>, EntityApiExtensionProvider<CurrentVehicleStatusChartViewDataObject>>();
				// DealerFeatureSubscription

				services.AddSingleton<IEntityApiExtensionProvider<DealerFeatureSubscriptionDataObject>, EntityApiExtensionProvider<DealerFeatureSubscriptionDataObject>>();
				// MessageHistory

				services.AddSingleton<IEntityApiExtensionProvider<MessageHistoryDataObject>, EntityApiExtensionProvider<MessageHistoryDataObject>>();
				// AccessGroupToSite

				services.AddSingleton<IEntityApiExtensionProvider<AccessGroupToSiteDataObject>, EntityApiExtensionProvider<AccessGroupToSiteDataObject>>();
				// AllDriverAccessAbuseStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<AllDriverAccessAbuseStoreProcedureDataObject>, EntityApiExtensionProvider<AllDriverAccessAbuseStoreProcedureDataObject>>();
				// CustomerToModel

				services.AddSingleton<IEntityApiExtensionProvider<CustomerToModelDataObject>, EntityApiExtensionProvider<CustomerToModelDataObject>>();
				// AllEmailSubscriptionStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<AllEmailSubscriptionStoreProcedureDataObject>, EntityApiExtensionProvider<AllEmailSubscriptionStoreProcedureDataObject>>();
				// AlertSummaryStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<AlertSummaryStoreProcedureDataObject>, EntityApiExtensionProvider<AlertSummaryStoreProcedureDataObject>>();
				// DashboardVehicleCardStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<DashboardVehicleCardStoreProcedureDataObject>, EntityApiExtensionProvider<DashboardVehicleCardStoreProcedureDataObject>>();
				// Region

				services.AddSingleton<IEntityApiExtensionProvider<RegionDataObject>, EntityApiExtensionProvider<RegionDataObject>>();
				// Model

				services.AddSingleton<IEntityApiExtensionProvider<ModelDataObject>, EntityApiExtensionProvider<ModelDataObject>>();
				// AllVORSessionsPerVehicleStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>, EntityApiExtensionProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>>();
				// SlamcoreDeviceConnectionView

				services.AddSingleton<IEntityApiExtensionProvider<SlamcoreDeviceConnectionViewDataObject>, EntityApiExtensionProvider<SlamcoreDeviceConnectionViewDataObject>>();
				// AllImpactsView

				services.AddSingleton<IEntityApiExtensionProvider<AllImpactsViewDataObject>, EntityApiExtensionProvider<AllImpactsViewDataObject>>();
				// Customer

				services.AddSingleton<IEntityApiExtensionProvider<CustomerDataObject>, EntityApiExtensionProvider<CustomerDataObject>>();
				// DepartmentChecklist

				services.AddSingleton<IEntityApiExtensionProvider<DepartmentChecklistDataObject>, EntityApiExtensionProvider<DepartmentChecklistDataObject>>();
				// ModuleHistory

				services.AddSingleton<IEntityApiExtensionProvider<ModuleHistoryDataObject>, EntityApiExtensionProvider<ModuleHistoryDataObject>>();
				// Country

				services.AddSingleton<IEntityApiExtensionProvider<CountryDataObject>, EntityApiExtensionProvider<CountryDataObject>>();
				// AllChecklistResultView

				services.AddSingleton<IEntityApiExtensionProvider<AllChecklistResultViewDataObject>, EntityApiExtensionProvider<AllChecklistResultViewDataObject>>();
				// GPSHistory

				services.AddSingleton<IEntityApiExtensionProvider<GPSHistoryDataObject>, EntityApiExtensionProvider<GPSHistoryDataObject>>();
				// ImpactsForVehicleView

				services.AddSingleton<IEntityApiExtensionProvider<ImpactsForVehicleViewDataObject>, EntityApiExtensionProvider<ImpactsForVehicleViewDataObject>>();
				// AllVehicleUnlocksView

				services.AddSingleton<IEntityApiExtensionProvider<AllVehicleUnlocksViewDataObject>, EntityApiExtensionProvider<AllVehicleUnlocksViewDataObject>>();
				// SlamcorePedestrianDetection

				services.AddSingleton<IEntityApiExtensionProvider<SlamcorePedestrianDetectionDataObject>, EntityApiExtensionProvider<SlamcorePedestrianDetectionDataObject>>();
				// ImpactFrequencyPerWeekMonthView

				services.AddSingleton<IEntityApiExtensionProvider<ImpactFrequencyPerWeekMonthViewDataObject>, EntityApiExtensionProvider<ImpactFrequencyPerWeekMonthViewDataObject>>();
				// VehicleUtilizationLastTwelveHoursView

				services.AddSingleton<IEntityApiExtensionProvider<VehicleUtilizationLastTwelveHoursViewDataObject>, EntityApiExtensionProvider<VehicleUtilizationLastTwelveHoursViewDataObject>>();
				// IOFIELD

				services.AddSingleton<IEntityApiExtensionProvider<IOFIELDDataObject>, EntityApiExtensionProvider<IOFIELDDataObject>>();
				// Email

				services.AddSingleton<IEntityApiExtensionProvider<EmailDataObject>, EntityApiExtensionProvider<EmailDataObject>>();
				// LicenceDetail

				services.AddSingleton<IEntityApiExtensionProvider<LicenceDetailDataObject>, EntityApiExtensionProvider<LicenceDetailDataObject>>();
				// PedestrianDetectionHistoryFilter

				services.AddSingleton<IEntityApiExtensionProvider<PedestrianDetectionHistoryFilterDataObject>, EntityApiExtensionProvider<PedestrianDetectionHistoryFilterDataObject>>();
				// GOLoginHistory

				services.AddSingleton<IEntityApiExtensionProvider<GOLoginHistoryDataObject>, EntityApiExtensionProvider<GOLoginHistoryDataObject>>();
				// MainDashboardFilter

				services.AddSingleton<IEntityApiExtensionProvider<MainDashboardFilterDataObject>, EntityApiExtensionProvider<MainDashboardFilterDataObject>>();
				// AllVehicleCalibrationStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<AllVehicleCalibrationStoreProcedureDataObject>, EntityApiExtensionProvider<AllVehicleCalibrationStoreProcedureDataObject>>();
				// AccessGroupTemplate

				services.AddSingleton<IEntityApiExtensionProvider<AccessGroupTemplateDataObject>, EntityApiExtensionProvider<AccessGroupTemplateDataObject>>();
				// CardToCardAccess

				services.AddSingleton<IEntityApiExtensionProvider<CardToCardAccessDataObject>, EntityApiExtensionProvider<CardToCardAccessDataObject>>();
				// ZoneCoordinates

				services.AddSingleton<IEntityApiExtensionProvider<ZoneCoordinatesDataObject>, EntityApiExtensionProvider<ZoneCoordinatesDataObject>>();
				// Snapshot

				services.AddSingleton<IEntityApiExtensionProvider<SnapshotDataObject>, EntityApiExtensionProvider<SnapshotDataObject>>();
				// GOTask

				services.AddSingleton<IEntityApiExtensionProvider<GOTaskDataObject>, EntityApiExtensionProvider<GOTaskDataObject>>();
				// GeneralProductivityReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<GeneralProductivityReportFilterDataObject>, EntityApiExtensionProvider<GeneralProductivityReportFilterDataObject>>();
				// DepartmentVehicleNormalCardAccess

				services.AddSingleton<IEntityApiExtensionProvider<DepartmentVehicleNormalCardAccessDataObject>, EntityApiExtensionProvider<DepartmentVehicleNormalCardAccessDataObject>>();
				// CustomerToPersonView

				services.AddSingleton<IEntityApiExtensionProvider<CustomerToPersonViewDataObject>, EntityApiExtensionProvider<CustomerToPersonViewDataObject>>();
				// VehicleSlamcoreLocationHistory

				services.AddSingleton<IEntityApiExtensionProvider<VehicleSlamcoreLocationHistoryDataObject>, EntityApiExtensionProvider<VehicleSlamcoreLocationHistoryDataObject>>();
				// Permission

				services.AddSingleton<IEntityApiExtensionProvider<PermissionDataObject>, EntityApiExtensionProvider<PermissionDataObject>>();
				// SessionDetails

				services.AddSingleton<IEntityApiExtensionProvider<SessionDetailsDataObject>, EntityApiExtensionProvider<SessionDetailsDataObject>>();
				// GORole

				services.AddSingleton<IEntityApiExtensionProvider<GORoleDataObject>, EntityApiExtensionProvider<GORoleDataObject>>();
				// GeneralProductivityPerVehicleView

				services.AddSingleton<IEntityApiExtensionProvider<GeneralProductivityPerVehicleViewDataObject>, EntityApiExtensionProvider<GeneralProductivityPerVehicleViewDataObject>>();
				// Vehicle

				services.AddSingleton<IEntityApiExtensionProvider<VehicleDataObject>, EntityApiExtensionProvider<VehicleDataObject>>();
				// GeneralProductivityPerDriverViewLatest

				services.AddSingleton<IEntityApiExtensionProvider<GeneralProductivityPerDriverViewLatestDataObject>, EntityApiExtensionProvider<GeneralProductivityPerDriverViewLatestDataObject>>();
				// Driver

				services.AddSingleton<IEntityApiExtensionProvider<DriverDataObject>, EntityApiExtensionProvider<DriverDataObject>>();
				// EmailGroupsToPerson

				services.AddSingleton<IEntityApiExtensionProvider<EmailGroupsToPersonDataObject>, EntityApiExtensionProvider<EmailGroupsToPersonDataObject>>();
				// GOGroupRole

				services.AddSingleton<IEntityApiExtensionProvider<GOGroupRoleDataObject>, EntityApiExtensionProvider<GOGroupRoleDataObject>>();
				// ModelVehicleMasterCardAccess

				services.AddSingleton<IEntityApiExtensionProvider<ModelVehicleMasterCardAccessDataObject>, EntityApiExtensionProvider<ModelVehicleMasterCardAccessDataObject>>();
				// EmailSubscriptionReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<EmailSubscriptionReportFilterDataObject>, EntityApiExtensionProvider<EmailSubscriptionReportFilterDataObject>>();
				// VehicleProficiencyView

				services.AddSingleton<IEntityApiExtensionProvider<VehicleProficiencyViewDataObject>, EntityApiExtensionProvider<VehicleProficiencyViewDataObject>>();
				// DashboardDriverCardView

				services.AddSingleton<IEntityApiExtensionProvider<DashboardDriverCardViewDataObject>, EntityApiExtensionProvider<DashboardDriverCardViewDataObject>>();
				// DriverProficiencyView

				services.AddSingleton<IEntityApiExtensionProvider<DriverProficiencyViewDataObject>, EntityApiExtensionProvider<DriverProficiencyViewDataObject>>();
				// GOSecurityTokens

				services.AddSingleton<IEntityApiExtensionProvider<GOSecurityTokensDataObject>, EntityApiExtensionProvider<GOSecurityTokensDataObject>>();
				// AlertHistory

				services.AddSingleton<IEntityApiExtensionProvider<AlertHistoryDataObject>, EntityApiExtensionProvider<AlertHistoryDataObject>>();
				// DashboardFilter

				services.AddSingleton<IEntityApiExtensionProvider<DashboardFilterDataObject>, EntityApiExtensionProvider<DashboardFilterDataObject>>();
				// VORReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<VORReportFilterDataObject>, EntityApiExtensionProvider<VORReportFilterDataObject>>();
				// OnDemandAuthorisationFilter

				services.AddSingleton<IEntityApiExtensionProvider<OnDemandAuthorisationFilterDataObject>, EntityApiExtensionProvider<OnDemandAuthorisationFilterDataObject>>();
				// WebsiteRole

				services.AddSingleton<IEntityApiExtensionProvider<WebsiteRoleDataObject>, EntityApiExtensionProvider<WebsiteRoleDataObject>>();
				// GOUserRole

				services.AddSingleton<IEntityApiExtensionProvider<GOUserRoleDataObject>, EntityApiExtensionProvider<GOUserRoleDataObject>>();
				// GeneralProductivityView

				services.AddSingleton<IEntityApiExtensionProvider<GeneralProductivityViewDataObject>, EntityApiExtensionProvider<GeneralProductivityViewDataObject>>();
				// Inspection

				services.AddSingleton<IEntityApiExtensionProvider<InspectionDataObject>, EntityApiExtensionProvider<InspectionDataObject>>();
				// Impact

				services.AddSingleton<IEntityApiExtensionProvider<ImpactDataObject>, EntityApiExtensionProvider<ImpactDataObject>>();
				// PersonToDepartmentVehicleMasterAccessView

				services.AddSingleton<IEntityApiExtensionProvider<PersonToDepartmentVehicleMasterAccessViewDataObject>, EntityApiExtensionProvider<PersonToDepartmentVehicleMasterAccessViewDataObject>>();
				// CurrentStatusVehicleView

				services.AddSingleton<IEntityApiExtensionProvider<CurrentStatusVehicleViewDataObject>, EntityApiExtensionProvider<CurrentStatusVehicleViewDataObject>>();
				// CustomerModel

				services.AddSingleton<IEntityApiExtensionProvider<CustomerModelDataObject>, EntityApiExtensionProvider<CustomerModelDataObject>>();
				// Tag

				services.AddSingleton<IEntityApiExtensionProvider<TagDataObject>, EntityApiExtensionProvider<TagDataObject>>();
				// VehicleBroadcastMessage

				services.AddSingleton<IEntityApiExtensionProvider<VehicleBroadcastMessageDataObject>, EntityApiExtensionProvider<VehicleBroadcastMessageDataObject>>();
				// FloorPlan

				services.AddSingleton<IEntityApiExtensionProvider<FloorPlanDataObject>, EntityApiExtensionProvider<FloorPlanDataObject>>();
				// TodaysPreopCheckView

				services.AddSingleton<IEntityApiExtensionProvider<TodaysPreopCheckViewDataObject>, EntityApiExtensionProvider<TodaysPreopCheckViewDataObject>>();
				// ModelVehicleNormalCardAccess

				services.AddSingleton<IEntityApiExtensionProvider<ModelVehicleNormalCardAccessDataObject>, EntityApiExtensionProvider<ModelVehicleNormalCardAccessDataObject>>();
				// SiteVehicleNormalCardAccess

				services.AddSingleton<IEntityApiExtensionProvider<SiteVehicleNormalCardAccessDataObject>, EntityApiExtensionProvider<SiteVehicleNormalCardAccessDataObject>>();
				// ImportJobStatus

				services.AddSingleton<IEntityApiExtensionProvider<ImportJobStatusDataObject>, EntityApiExtensionProvider<ImportJobStatusDataObject>>();
				// VehicleOtherSettings

				services.AddSingleton<IEntityApiExtensionProvider<VehicleOtherSettingsDataObject>, EntityApiExtensionProvider<VehicleOtherSettingsDataObject>>();
				// DashboardFilterMoreFields

				services.AddSingleton<IEntityApiExtensionProvider<DashboardFilterMoreFieldsDataObject>, EntityApiExtensionProvider<DashboardFilterMoreFieldsDataObject>>();
				// VehicleSupervisorsView

				services.AddSingleton<IEntityApiExtensionProvider<VehicleSupervisorsViewDataObject>, EntityApiExtensionProvider<VehicleSupervisorsViewDataObject>>();
				// ExportJobStatus

				services.AddSingleton<IEntityApiExtensionProvider<ExportJobStatusDataObject>, EntityApiExtensionProvider<ExportJobStatusDataObject>>();
				// Timezone

				services.AddSingleton<IEntityApiExtensionProvider<TimezoneDataObject>, EntityApiExtensionProvider<TimezoneDataObject>>();
				// HireDeHireReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<HireDeHireReportFilterDataObject>, EntityApiExtensionProvider<HireDeHireReportFilterDataObject>>();
				// LoggedHoursVersusSeatHoursView

				services.AddSingleton<IEntityApiExtensionProvider<LoggedHoursVersusSeatHoursViewDataObject>, EntityApiExtensionProvider<LoggedHoursVersusSeatHoursViewDataObject>>();
				// PersonAllocation

				services.AddSingleton<IEntityApiExtensionProvider<PersonAllocationDataObject>, EntityApiExtensionProvider<PersonAllocationDataObject>>();
				// BroadcastMessage

				services.AddSingleton<IEntityApiExtensionProvider<BroadcastMessageDataObject>, EntityApiExtensionProvider<BroadcastMessageDataObject>>();
				// VORReportCombinedView

				services.AddSingleton<IEntityApiExtensionProvider<VORReportCombinedViewDataObject>, EntityApiExtensionProvider<VORReportCombinedViewDataObject>>();
				// UnitUtilisationStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<UnitUtilisationStoreProcedureDataObject>, EntityApiExtensionProvider<UnitUtilisationStoreProcedureDataObject>>();
				// ChecklistResult

				services.AddSingleton<IEntityApiExtensionProvider<ChecklistResultDataObject>, EntityApiExtensionProvider<ChecklistResultDataObject>>();
				// UnitUnutilisationStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<UnitUnutilisationStoreProcedureDataObject>, EntityApiExtensionProvider<UnitUnutilisationStoreProcedureDataObject>>();
				// VehicleAlertSubscription

				services.AddSingleton<IEntityApiExtensionProvider<VehicleAlertSubscriptionDataObject>, EntityApiExtensionProvider<VehicleAlertSubscriptionDataObject>>();
				// OnDemandAuthorisationStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<OnDemandAuthorisationStoreProcedureDataObject>, EntityApiExtensionProvider<OnDemandAuthorisationStoreProcedureDataObject>>();
				// ImpactFrequencyPerTimeSlotView

				services.AddSingleton<IEntityApiExtensionProvider<ImpactFrequencyPerTimeSlotViewDataObject>, EntityApiExtensionProvider<ImpactFrequencyPerTimeSlotViewDataObject>>();
				// PersonToSiteVehicleNormalAccessView

				services.AddSingleton<IEntityApiExtensionProvider<PersonToSiteVehicleNormalAccessViewDataObject>, EntityApiExtensionProvider<PersonToSiteVehicleNormalAccessViewDataObject>>();
				// Session

				services.AddSingleton<IEntityApiExtensionProvider<SessionDataObject>, EntityApiExtensionProvider<SessionDataObject>>();
				// SlamcoreDeviceFilter

				services.AddSingleton<IEntityApiExtensionProvider<SlamcoreDeviceFilterDataObject>, EntityApiExtensionProvider<SlamcoreDeviceFilterDataObject>>();
				// Dealer

				services.AddSingleton<IEntityApiExtensionProvider<DealerDataObject>, EntityApiExtensionProvider<DealerDataObject>>();
				// GOUser

				services.AddSingleton<IEntityApiExtensionProvider<GOUserDataObject>, EntityApiExtensionProvider<GOUserDataObject>>();
				// ServiceSettings

				services.AddSingleton<IEntityApiExtensionProvider<ServiceSettingsDataObject>, EntityApiExtensionProvider<ServiceSettingsDataObject>>();
				// UpdateFirmwareRequest

				services.AddSingleton<IEntityApiExtensionProvider<UpdateFirmwareRequestDataObject>, EntityApiExtensionProvider<UpdateFirmwareRequestDataObject>>();
				// SlamcoreAPIKey

				services.AddSingleton<IEntityApiExtensionProvider<SlamcoreAPIKeyDataObject>, EntityApiExtensionProvider<SlamcoreAPIKeyDataObject>>();
				// Firmware

				services.AddSingleton<IEntityApiExtensionProvider<FirmwareDataObject>, EntityApiExtensionProvider<FirmwareDataObject>>();
				// Card

				services.AddSingleton<IEntityApiExtensionProvider<CardDataObject>, EntityApiExtensionProvider<CardDataObject>>();
				// GOUserDepartment

				services.AddSingleton<IEntityApiExtensionProvider<GOUserDepartmentDataObject>, EntityApiExtensionProvider<GOUserDepartmentDataObject>>();
				// Canrule

				services.AddSingleton<IEntityApiExtensionProvider<CanruleDataObject>, EntityApiExtensionProvider<CanruleDataObject>>();
				// ImpactFrequencyPerWeekDayView

				services.AddSingleton<IEntityApiExtensionProvider<ImpactFrequencyPerWeekDayViewDataObject>, EntityApiExtensionProvider<ImpactFrequencyPerWeekDayViewDataObject>>();
				// SlamcoreDeviceHistory

				services.AddSingleton<IEntityApiExtensionProvider<SlamcoreDeviceHistoryDataObject>, EntityApiExtensionProvider<SlamcoreDeviceHistoryDataObject>>();
				// VehicleHireDehireHistory

				services.AddSingleton<IEntityApiExtensionProvider<VehicleHireDehireHistoryDataObject>, EntityApiExtensionProvider<VehicleHireDehireHistoryDataObject>>();
				// ChecklistFailurePerVechicleView

				services.AddSingleton<IEntityApiExtensionProvider<ChecklistFailurePerVechicleViewDataObject>, EntityApiExtensionProvider<ChecklistFailurePerVechicleViewDataObject>>();
				// ImpactReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<ImpactReportFilterDataObject>, EntityApiExtensionProvider<ImpactReportFilterDataObject>>();
				// IncompletedChecklistView

				services.AddSingleton<IEntityApiExtensionProvider<IncompletedChecklistViewDataObject>, EntityApiExtensionProvider<IncompletedChecklistViewDataObject>>();
				// ReportSubscription

				services.AddSingleton<IEntityApiExtensionProvider<ReportSubscriptionDataObject>, EntityApiExtensionProvider<ReportSubscriptionDataObject>>();
				// VehicleLastGPSLocationView

				services.AddSingleton<IEntityApiExtensionProvider<VehicleLastGPSLocationViewDataObject>, EntityApiExtensionProvider<VehicleLastGPSLocationViewDataObject>>();
				// PSTATDetails

				services.AddSingleton<IEntityApiExtensionProvider<PSTATDetailsDataObject>, EntityApiExtensionProvider<PSTATDetailsDataObject>>();
				// LicenseExpiryReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<LicenseExpiryReportFilterDataObject>, EntityApiExtensionProvider<LicenseExpiryReportFilterDataObject>>();
				// FloorZones

				services.AddSingleton<IEntityApiExtensionProvider<FloorZonesDataObject>, EntityApiExtensionProvider<FloorZonesDataObject>>();
				// CanruleDetails

				services.AddSingleton<IEntityApiExtensionProvider<CanruleDetailsDataObject>, EntityApiExtensionProvider<CanruleDetailsDataObject>>();
				// VehicleHireDehireSynchronizationOptions

				services.AddSingleton<IEntityApiExtensionProvider<VehicleHireDehireSynchronizationOptionsDataObject>, EntityApiExtensionProvider<VehicleHireDehireSynchronizationOptionsDataObject>>();
				// DriverLicenseExpiryStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<DriverLicenseExpiryStoreProcedureDataObject>, EntityApiExtensionProvider<DriverLicenseExpiryStoreProcedureDataObject>>();
				// UnitSummaryReport

				services.AddSingleton<IEntityApiExtensionProvider<UnitSummaryReportDataObject>, EntityApiExtensionProvider<UnitSummaryReportDataObject>>();
				// DepartmentVehicleMasterCardAccess

				services.AddSingleton<IEntityApiExtensionProvider<DepartmentVehicleMasterCardAccessDataObject>, EntityApiExtensionProvider<DepartmentVehicleMasterCardAccessDataObject>>();
				// VehicleUtilizationLastTwelveHoursStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>, EntityApiExtensionProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>>();
				// ChecklistFailureView

				services.AddSingleton<IEntityApiExtensionProvider<ChecklistFailureViewDataObject>, EntityApiExtensionProvider<ChecklistFailureViewDataObject>>();
				// PersonChecklistLanguageSettings

				services.AddSingleton<IEntityApiExtensionProvider<PersonChecklistLanguageSettingsDataObject>, EntityApiExtensionProvider<PersonChecklistLanguageSettingsDataObject>>();
				// VehicleSessionlessImpact

				services.AddSingleton<IEntityApiExtensionProvider<VehicleSessionlessImpactDataObject>, EntityApiExtensionProvider<VehicleSessionlessImpactDataObject>>();
				// LicenseByModel

				services.AddSingleton<IEntityApiExtensionProvider<LicenseByModelDataObject>, EntityApiExtensionProvider<LicenseByModelDataObject>>();
				// AllMessageHistoryStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<AllMessageHistoryStoreProcedureDataObject>, EntityApiExtensionProvider<AllMessageHistoryStoreProcedureDataObject>>();
				// IoTDeviceMessageCache

				services.AddSingleton<IEntityApiExtensionProvider<IoTDeviceMessageCacheDataObject>, EntityApiExtensionProvider<IoTDeviceMessageCacheDataObject>>();
				// ProficiencyCombinedView

				services.AddSingleton<IEntityApiExtensionProvider<ProficiencyCombinedViewDataObject>, EntityApiExtensionProvider<ProficiencyCombinedViewDataObject>>();
				// CustomerSnapshot

				services.AddSingleton<IEntityApiExtensionProvider<CustomerSnapshotDataObject>, EntityApiExtensionProvider<CustomerSnapshotDataObject>>();
				// GOGroup

				services.AddSingleton<IEntityApiExtensionProvider<GOGroupDataObject>, EntityApiExtensionProvider<GOGroupDataObject>>();
				// VehiclesPerModelReport

				services.AddSingleton<IEntityApiExtensionProvider<VehiclesPerModelReportDataObject>, EntityApiExtensionProvider<VehiclesPerModelReportDataObject>>();
				// TodaysImpactStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<TodaysImpactStoreProcedureDataObject>, EntityApiExtensionProvider<TodaysImpactStoreProcedureDataObject>>();
				// BroadcastMessageHistoryFilter

				services.AddSingleton<IEntityApiExtensionProvider<BroadcastMessageHistoryFilterDataObject>, EntityApiExtensionProvider<BroadcastMessageHistoryFilterDataObject>>();
				// DealerDriver

				services.AddSingleton<IEntityApiExtensionProvider<DealerDriverDataObject>, EntityApiExtensionProvider<DealerDriverDataObject>>();
				// EmailGroups

				services.AddSingleton<IEntityApiExtensionProvider<EmailGroupsDataObject>, EntityApiExtensionProvider<EmailGroupsDataObject>>();
				// PreOperationalChecklist

				services.AddSingleton<IEntityApiExtensionProvider<PreOperationalChecklistDataObject>, EntityApiExtensionProvider<PreOperationalChecklistDataObject>>();
				// SynchronizationStatusReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<SynchronizationStatusReportFilterDataObject>, EntityApiExtensionProvider<SynchronizationStatusReportFilterDataObject>>();
				// SlamcoreDevice

				services.AddSingleton<IEntityApiExtensionProvider<SlamcoreDeviceDataObject>, EntityApiExtensionProvider<SlamcoreDeviceDataObject>>();
				// Site

				services.AddSingleton<IEntityApiExtensionProvider<SiteDataObject>, EntityApiExtensionProvider<SiteDataObject>>();
				// AlertSubscription

				services.AddSingleton<IEntityApiExtensionProvider<AlertSubscriptionDataObject>, EntityApiExtensionProvider<AlertSubscriptionDataObject>>();
				// CustomerAudit

				services.AddSingleton<IEntityApiExtensionProvider<CustomerAuditDataObject>, EntityApiExtensionProvider<CustomerAuditDataObject>>();
				// PerVehicleNormalCardAccess

				services.AddSingleton<IEntityApiExtensionProvider<PerVehicleNormalCardAccessDataObject>, EntityApiExtensionProvider<PerVehicleNormalCardAccessDataObject>>();
				// PedestrianDetectionHistory

				services.AddSingleton<IEntityApiExtensionProvider<PedestrianDetectionHistoryDataObject>, EntityApiExtensionProvider<PedestrianDetectionHistoryDataObject>>();
				// PersonToPerVehicleNormalAccessView

				services.AddSingleton<IEntityApiExtensionProvider<PersonToPerVehicleNormalAccessViewDataObject>, EntityApiExtensionProvider<PersonToPerVehicleNormalAccessViewDataObject>>();
				// DashboardDriverCardStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<DashboardDriverCardStoreProcedureDataObject>, EntityApiExtensionProvider<DashboardDriverCardStoreProcedureDataObject>>();
				// TodaysImpactView

				services.AddSingleton<IEntityApiExtensionProvider<TodaysImpactViewDataObject>, EntityApiExtensionProvider<TodaysImpactViewDataObject>>();
				// DetailedSessionView

				services.AddSingleton<IEntityApiExtensionProvider<DetailedSessionViewDataObject>, EntityApiExtensionProvider<DetailedSessionViewDataObject>>();
				// ImportJobLog

				services.AddSingleton<IEntityApiExtensionProvider<ImportJobLogDataObject>, EntityApiExtensionProvider<ImportJobLogDataObject>>();
				// ProficiencyReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<ProficiencyReportFilterDataObject>, EntityApiExtensionProvider<ProficiencyReportFilterDataObject>>();
				// OnDemandSession

				services.AddSingleton<IEntityApiExtensionProvider<OnDemandSessionDataObject>, EntityApiExtensionProvider<OnDemandSessionDataObject>>();
				// AllVORStatusStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<AllVORStatusStoreProcedureDataObject>, EntityApiExtensionProvider<AllVORStatusStoreProcedureDataObject>>();
				// VORSettingHistory

				services.AddSingleton<IEntityApiExtensionProvider<VORSettingHistoryDataObject>, EntityApiExtensionProvider<VORSettingHistoryDataObject>>();
				// AllUserSummaryStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<AllUserSummaryStoreProcedureDataObject>, EntityApiExtensionProvider<AllUserSummaryStoreProcedureDataObject>>();
				// CustomerSSODetail

				services.AddSingleton<IEntityApiExtensionProvider<CustomerSSODetailDataObject>, EntityApiExtensionProvider<CustomerSSODetailDataObject>>();
				// UploadLogoRequest

				services.AddSingleton<IEntityApiExtensionProvider<UploadLogoRequestDataObject>, EntityApiExtensionProvider<UploadLogoRequestDataObject>>();
				// AllVehicleCalibrationFilter

				services.AddSingleton<IEntityApiExtensionProvider<AllVehicleCalibrationFilterDataObject>, EntityApiExtensionProvider<AllVehicleCalibrationFilterDataObject>>();
				// DepartmentHourSettings

				services.AddSingleton<IEntityApiExtensionProvider<DepartmentHourSettingsDataObject>, EntityApiExtensionProvider<DepartmentHourSettingsDataObject>>();
				// CategoryTemplate

				services.AddSingleton<IEntityApiExtensionProvider<CategoryTemplateDataObject>, EntityApiExtensionProvider<CategoryTemplateDataObject>>();
				// DashboardVehicleCardView

				services.AddSingleton<IEntityApiExtensionProvider<DashboardVehicleCardViewDataObject>, EntityApiExtensionProvider<DashboardVehicleCardViewDataObject>>();
				// CurrentStatusDriverView

				services.AddSingleton<IEntityApiExtensionProvider<CurrentStatusDriverViewDataObject>, EntityApiExtensionProvider<CurrentStatusDriverViewDataObject>>();
				// UnitSummaryStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<UnitSummaryStoreProcedureDataObject>, EntityApiExtensionProvider<UnitSummaryStoreProcedureDataObject>>();
				// ReportType

				services.AddSingleton<IEntityApiExtensionProvider<ReportTypeDataObject>, EntityApiExtensionProvider<ReportTypeDataObject>>();
				// SiteVehicleMasterCardAccess

				services.AddSingleton<IEntityApiExtensionProvider<SiteVehicleMasterCardAccessDataObject>, EntityApiExtensionProvider<SiteVehicleMasterCardAccessDataObject>>();
				// PersonToDepartmentVehicleNormalAccessView

				services.AddSingleton<IEntityApiExtensionProvider<PersonToDepartmentVehicleNormalAccessViewDataObject>, EntityApiExtensionProvider<PersonToDepartmentVehicleNormalAccessViewDataObject>>();
				// GOUserGroup

				services.AddSingleton<IEntityApiExtensionProvider<GOUserGroupDataObject>, EntityApiExtensionProvider<GOUserGroupDataObject>>();
				// CustomerFeatureSubscription

				services.AddSingleton<IEntityApiExtensionProvider<CustomerFeatureSubscriptionDataObject>, EntityApiExtensionProvider<CustomerFeatureSubscriptionDataObject>>();
				// VehicleToPreOpChecklistView

				services.AddSingleton<IEntityApiExtensionProvider<VehicleToPreOpChecklistViewDataObject>, EntityApiExtensionProvider<VehicleToPreOpChecklistViewDataObject>>();
				// PersonToSiteVehicleMasterAccessView

				services.AddSingleton<IEntityApiExtensionProvider<PersonToSiteVehicleMasterAccessViewDataObject>, EntityApiExtensionProvider<PersonToSiteVehicleMasterAccessViewDataObject>>();
				// DealerConfiguration

				services.AddSingleton<IEntityApiExtensionProvider<DealerConfigurationDataObject>, EntityApiExtensionProvider<DealerConfigurationDataObject>>();
				// TodaysPreopCheckStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<TodaysPreopCheckStoreProcedureDataObject>, EntityApiExtensionProvider<TodaysPreopCheckStoreProcedureDataObject>>();
				// CustomerPreOperationalChecklistTemplate

				services.AddSingleton<IEntityApiExtensionProvider<CustomerPreOperationalChecklistTemplateDataObject>, EntityApiExtensionProvider<CustomerPreOperationalChecklistTemplateDataObject>>();
				// CurrentDriverStatusChartView

				services.AddSingleton<IEntityApiExtensionProvider<CurrentDriverStatusChartViewDataObject>, EntityApiExtensionProvider<CurrentDriverStatusChartViewDataObject>>();
				// GOChangeDelta

				services.AddSingleton<IEntityApiExtensionProvider<GOChangeDeltaDataObject>, EntityApiExtensionProvider<GOChangeDeltaDataObject>>();
				// GO2FAConfiguration

				services.AddSingleton<IEntityApiExtensionProvider<GO2FAConfigurationDataObject>, EntityApiExtensionProvider<GO2FAConfigurationDataObject>>();
				// PreOpReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<PreOpReportFilterDataObject>, EntityApiExtensionProvider<PreOpReportFilterDataObject>>();
				// SiteFloorPlan

				services.AddSingleton<IEntityApiExtensionProvider<SiteFloorPlanDataObject>, EntityApiExtensionProvider<SiteFloorPlanDataObject>>();
				// BroadcastMessageHistory

				services.AddSingleton<IEntityApiExtensionProvider<BroadcastMessageHistoryDataObject>, EntityApiExtensionProvider<BroadcastMessageHistoryDataObject>>();
				// GoUserToCustomer

				services.AddSingleton<IEntityApiExtensionProvider<GoUserToCustomerDataObject>, EntityApiExtensionProvider<GoUserToCustomerDataObject>>();
				// ImportJobBatch

				services.AddSingleton<IEntityApiExtensionProvider<ImportJobBatchDataObject>, EntityApiExtensionProvider<ImportJobBatchDataObject>>();
				// Person

				services.AddSingleton<IEntityApiExtensionProvider<PersonDataObject>, EntityApiExtensionProvider<PersonDataObject>>();
				// DetailedVORSessionStoreProcedure

				services.AddSingleton<IEntityApiExtensionProvider<DetailedVORSessionStoreProcedureDataObject>, EntityApiExtensionProvider<DetailedVORSessionStoreProcedureDataObject>>();
				// PersonToModelVehicleNormalAccessView

				services.AddSingleton<IEntityApiExtensionProvider<PersonToModelVehicleNormalAccessViewDataObject>, EntityApiExtensionProvider<PersonToModelVehicleNormalAccessViewDataObject>>();
				// NetworkSettings

				services.AddSingleton<IEntityApiExtensionProvider<NetworkSettingsDataObject>, EntityApiExtensionProvider<NetworkSettingsDataObject>>();
				// Department

				services.AddSingleton<IEntityApiExtensionProvider<DepartmentDataObject>, EntityApiExtensionProvider<DepartmentDataObject>>();
				// MachineUnlockReportFilter

				services.AddSingleton<IEntityApiExtensionProvider<MachineUnlockReportFilterDataObject>, EntityApiExtensionProvider<MachineUnlockReportFilterDataObject>>();
				// PerVehicleMasterCardAccess

				services.AddSingleton<IEntityApiExtensionProvider<PerVehicleMasterCardAccessDataObject>, EntityApiExtensionProvider<PerVehicleMasterCardAccessDataObject>>();
				// UnitUtilisationCombinedView

				services.AddSingleton<IEntityApiExtensionProvider<UnitUtilisationCombinedViewDataObject>, EntityApiExtensionProvider<UnitUtilisationCombinedViewDataObject>>();
				// FeatureSubscriptionsFilter

				services.AddSingleton<IEntityApiExtensionProvider<FeatureSubscriptionsFilterDataObject>, EntityApiExtensionProvider<FeatureSubscriptionsFilterDataObject>>();
				// OnDemandSettings

				services.AddSingleton<IEntityApiExtensionProvider<OnDemandSettingsDataObject>, EntityApiExtensionProvider<OnDemandSettingsDataObject>>();
			}
            #endregion Extension providers for Entities

			services.AddScoped<IStorageProvider, FileSystemStorageProvider>();
            
			
	

            #region DeleteHandler DataProvider Extensions
			// DeleteHandler DataProvider Extensions
			{
				services.AddTransient<DeleteHandlerBase<ChecklistDetailDataObject>, ChecklistDetailDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ChecklistDetailDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ChecklistDetailDataObject>>());
				services.AddTransient<DeleteHandlerBase<SlamcoreAwareAuthenticationDetailsDataObject>, SlamcoreAwareAuthenticationDetailsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SlamcoreAwareAuthenticationDetailsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SlamcoreAwareAuthenticationDetailsDataObject>>());
				services.AddTransient<DeleteHandlerBase<FeatureSubscriptionTemplateDataObject>, FeatureSubscriptionTemplateDeleteHandler>();
				services.AddTransient<IDataProviderExtension<FeatureSubscriptionTemplateDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<FeatureSubscriptionTemplateDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonToModelVehicleMasterAccessViewDataObject>, PersonToModelVehicleMasterAccessViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonToModelVehicleMasterAccessViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonToModelVehicleMasterAccessViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleLockoutDataObject>, VehicleLockoutDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleLockoutDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleLockoutDataObject>>());
				services.AddTransient<DeleteHandlerBase<RevisionDataObject>, RevisionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<RevisionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<RevisionDataObject>>());
				services.AddTransient<DeleteHandlerBase<AlertDataObject>, AlertDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AlertDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AlertDataObject>>());
				services.AddTransient<DeleteHandlerBase<WebsiteUserDataObject>, WebsiteUserDeleteHandler>();
				services.AddTransient<IDataProviderExtension<WebsiteUserDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<WebsiteUserDataObject>>());
				services.AddTransient<DeleteHandlerBase<DriverAccessAbuseFilterDataObject>, DriverAccessAbuseFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DriverAccessAbuseFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DriverAccessAbuseFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllLicenseExpiryViewDataObject>, AllLicenseExpiryViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllLicenseExpiryViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllLicenseExpiryViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<DriverLicenseExpiryViewDataObject>, DriverLicenseExpiryViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DriverLicenseExpiryViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DriverLicenseExpiryViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<ContactPersonInformationDataObject>, ContactPersonInformationDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ContactPersonInformationDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ContactPersonInformationDataObject>>());
				services.AddTransient<DeleteHandlerBase<ChecklistSettingsDataObject>, ChecklistSettingsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ChecklistSettingsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ChecklistSettingsDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleDiagnosticDataObject>, VehicleDiagnosticDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleDiagnosticDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleDiagnosticDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOUser2FADataObject>, GOUser2FADeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOUser2FADataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOUser2FADataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleGPSDataObject>, VehicleGPSDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleGPSDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleGPSDataObject>>());
				services.AddTransient<DeleteHandlerBase<ModuleDataObject>, ModuleDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ModuleDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ModuleDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonToPerVehicleMasterAccessViewDataObject>, PersonToPerVehicleMasterAccessViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonToPerVehicleMasterAccessViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonToPerVehicleMasterAccessViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<AccessGroupDataObject>, AccessGroupDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AccessGroupDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AccessGroupDataObject>>());
				services.AddTransient<DeleteHandlerBase<HelpDataObject>, HelpDeleteHandler>();
				services.AddTransient<IDataProviderExtension<HelpDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<HelpDataObject>>());
				services.AddTransient<DeleteHandlerBase<ChecklistStatusViewDataObject>, ChecklistStatusViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ChecklistStatusViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ChecklistStatusViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<CurrentStatusCombinedViewDataObject>, CurrentStatusCombinedViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CurrentStatusCombinedViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CurrentStatusCombinedViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<CurrentVehicleStatusChartViewDataObject>, CurrentVehicleStatusChartViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CurrentVehicleStatusChartViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CurrentVehicleStatusChartViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<DealerFeatureSubscriptionDataObject>, DealerFeatureSubscriptionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DealerFeatureSubscriptionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DealerFeatureSubscriptionDataObject>>());
				services.AddTransient<DeleteHandlerBase<MessageHistoryDataObject>, MessageHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<MessageHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<MessageHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<AccessGroupToSiteDataObject>, AccessGroupToSiteDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AccessGroupToSiteDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AccessGroupToSiteDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllDriverAccessAbuseStoreProcedureDataObject>, AllDriverAccessAbuseStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllDriverAccessAbuseStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllDriverAccessAbuseStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<CustomerToModelDataObject>, CustomerToModelDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CustomerToModelDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CustomerToModelDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllEmailSubscriptionStoreProcedureDataObject>, AllEmailSubscriptionStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllEmailSubscriptionStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllEmailSubscriptionStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<AlertSummaryStoreProcedureDataObject>, AlertSummaryStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AlertSummaryStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AlertSummaryStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<DashboardVehicleCardStoreProcedureDataObject>, DashboardVehicleCardStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DashboardVehicleCardStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DashboardVehicleCardStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<RegionDataObject>, RegionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<RegionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<RegionDataObject>>());
				services.AddTransient<DeleteHandlerBase<ModelDataObject>, ModelDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ModelDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ModelDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllVORSessionsPerVehicleStoreProcedureDataObject>, AllVORSessionsPerVehicleStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllVORSessionsPerVehicleStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllVORSessionsPerVehicleStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<SlamcoreDeviceConnectionViewDataObject>, SlamcoreDeviceConnectionViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SlamcoreDeviceConnectionViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SlamcoreDeviceConnectionViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllImpactsViewDataObject>, AllImpactsViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllImpactsViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllImpactsViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<CustomerDataObject>, CustomerDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CustomerDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CustomerDataObject>>());
				services.AddTransient<DeleteHandlerBase<DepartmentChecklistDataObject>, DepartmentChecklistDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DepartmentChecklistDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DepartmentChecklistDataObject>>());
				services.AddTransient<DeleteHandlerBase<ModuleHistoryDataObject>, ModuleHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ModuleHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ModuleHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<CountryDataObject>, CountryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CountryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CountryDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllChecklistResultViewDataObject>, AllChecklistResultViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllChecklistResultViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllChecklistResultViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<GPSHistoryDataObject>, GPSHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GPSHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GPSHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<ImpactsForVehicleViewDataObject>, ImpactsForVehicleViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ImpactsForVehicleViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ImpactsForVehicleViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllVehicleUnlocksViewDataObject>, AllVehicleUnlocksViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllVehicleUnlocksViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllVehicleUnlocksViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<SlamcorePedestrianDetectionDataObject>, SlamcorePedestrianDetectionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SlamcorePedestrianDetectionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SlamcorePedestrianDetectionDataObject>>());
				services.AddTransient<DeleteHandlerBase<ImpactFrequencyPerWeekMonthViewDataObject>, ImpactFrequencyPerWeekMonthViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ImpactFrequencyPerWeekMonthViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ImpactFrequencyPerWeekMonthViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleUtilizationLastTwelveHoursViewDataObject>, VehicleUtilizationLastTwelveHoursViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleUtilizationLastTwelveHoursViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleUtilizationLastTwelveHoursViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<IOFIELDDataObject>, IOFIELDDeleteHandler>();
				services.AddTransient<IDataProviderExtension<IOFIELDDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<IOFIELDDataObject>>());
				services.AddTransient<DeleteHandlerBase<EmailDataObject>, EmailDeleteHandler>();
				services.AddTransient<IDataProviderExtension<EmailDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<EmailDataObject>>());
				services.AddTransient<DeleteHandlerBase<LicenceDetailDataObject>, LicenceDetailDeleteHandler>();
				services.AddTransient<IDataProviderExtension<LicenceDetailDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<LicenceDetailDataObject>>());
				services.AddTransient<DeleteHandlerBase<PedestrianDetectionHistoryFilterDataObject>, PedestrianDetectionHistoryFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PedestrianDetectionHistoryFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PedestrianDetectionHistoryFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOLoginHistoryDataObject>, GOLoginHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOLoginHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOLoginHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<MainDashboardFilterDataObject>, MainDashboardFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<MainDashboardFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<MainDashboardFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllVehicleCalibrationStoreProcedureDataObject>, AllVehicleCalibrationStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllVehicleCalibrationStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllVehicleCalibrationStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<AccessGroupTemplateDataObject>, AccessGroupTemplateDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AccessGroupTemplateDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AccessGroupTemplateDataObject>>());
				services.AddTransient<DeleteHandlerBase<CardToCardAccessDataObject>, CardToCardAccessDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CardToCardAccessDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CardToCardAccessDataObject>>());
				services.AddTransient<DeleteHandlerBase<ZoneCoordinatesDataObject>, ZoneCoordinatesDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ZoneCoordinatesDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ZoneCoordinatesDataObject>>());
				services.AddTransient<DeleteHandlerBase<SnapshotDataObject>, SnapshotDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SnapshotDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SnapshotDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOTaskDataObject>, GOTaskDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOTaskDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOTaskDataObject>>());
				services.AddTransient<DeleteHandlerBase<GeneralProductivityReportFilterDataObject>, GeneralProductivityReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GeneralProductivityReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GeneralProductivityReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<DepartmentVehicleNormalCardAccessDataObject>, DepartmentVehicleNormalCardAccessDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DepartmentVehicleNormalCardAccessDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DepartmentVehicleNormalCardAccessDataObject>>());
				services.AddTransient<DeleteHandlerBase<CustomerToPersonViewDataObject>, CustomerToPersonViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CustomerToPersonViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CustomerToPersonViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleSlamcoreLocationHistoryDataObject>, VehicleSlamcoreLocationHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleSlamcoreLocationHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleSlamcoreLocationHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<PermissionDataObject>, PermissionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PermissionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PermissionDataObject>>());
				services.AddTransient<DeleteHandlerBase<SessionDetailsDataObject>, SessionDetailsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SessionDetailsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SessionDetailsDataObject>>());
				services.AddTransient<DeleteHandlerBase<GORoleDataObject>, GORoleDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GORoleDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GORoleDataObject>>());
				services.AddTransient<DeleteHandlerBase<GeneralProductivityPerVehicleViewDataObject>, GeneralProductivityPerVehicleViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GeneralProductivityPerVehicleViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GeneralProductivityPerVehicleViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleDataObject>, VehicleDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleDataObject>>());
				services.AddTransient<DeleteHandlerBase<GeneralProductivityPerDriverViewLatestDataObject>, GeneralProductivityPerDriverViewLatestDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GeneralProductivityPerDriverViewLatestDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GeneralProductivityPerDriverViewLatestDataObject>>());
				services.AddTransient<DeleteHandlerBase<DriverDataObject>, DriverDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DriverDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DriverDataObject>>());
				services.AddTransient<DeleteHandlerBase<EmailGroupsToPersonDataObject>, EmailGroupsToPersonDeleteHandler>();
				services.AddTransient<IDataProviderExtension<EmailGroupsToPersonDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<EmailGroupsToPersonDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOGroupRoleDataObject>, GOGroupRoleDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOGroupRoleDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOGroupRoleDataObject>>());
				services.AddTransient<DeleteHandlerBase<ModelVehicleMasterCardAccessDataObject>, ModelVehicleMasterCardAccessDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ModelVehicleMasterCardAccessDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ModelVehicleMasterCardAccessDataObject>>());
				services.AddTransient<DeleteHandlerBase<EmailSubscriptionReportFilterDataObject>, EmailSubscriptionReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<EmailSubscriptionReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<EmailSubscriptionReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleProficiencyViewDataObject>, VehicleProficiencyViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleProficiencyViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleProficiencyViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<DashboardDriverCardViewDataObject>, DashboardDriverCardViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DashboardDriverCardViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DashboardDriverCardViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<DriverProficiencyViewDataObject>, DriverProficiencyViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DriverProficiencyViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DriverProficiencyViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOSecurityTokensDataObject>, GOSecurityTokensDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOSecurityTokensDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOSecurityTokensDataObject>>());
				services.AddTransient<DeleteHandlerBase<AlertHistoryDataObject>, AlertHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AlertHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AlertHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<DashboardFilterDataObject>, DashboardFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DashboardFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DashboardFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<VORReportFilterDataObject>, VORReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VORReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VORReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<OnDemandAuthorisationFilterDataObject>, OnDemandAuthorisationFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<OnDemandAuthorisationFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<OnDemandAuthorisationFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<WebsiteRoleDataObject>, WebsiteRoleDeleteHandler>();
				services.AddTransient<IDataProviderExtension<WebsiteRoleDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<WebsiteRoleDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOUserRoleDataObject>, GOUserRoleDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOUserRoleDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOUserRoleDataObject>>());
				services.AddTransient<DeleteHandlerBase<GeneralProductivityViewDataObject>, GeneralProductivityViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GeneralProductivityViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GeneralProductivityViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<InspectionDataObject>, InspectionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<InspectionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<InspectionDataObject>>());
				services.AddTransient<DeleteHandlerBase<ImpactDataObject>, ImpactDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ImpactDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ImpactDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonToDepartmentVehicleMasterAccessViewDataObject>, PersonToDepartmentVehicleMasterAccessViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonToDepartmentVehicleMasterAccessViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonToDepartmentVehicleMasterAccessViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<CurrentStatusVehicleViewDataObject>, CurrentStatusVehicleViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CurrentStatusVehicleViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CurrentStatusVehicleViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<CustomerModelDataObject>, CustomerModelDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CustomerModelDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CustomerModelDataObject>>());
				services.AddTransient<DeleteHandlerBase<TagDataObject>, TagDeleteHandler>();
				services.AddTransient<IDataProviderExtension<TagDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<TagDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleBroadcastMessageDataObject>, VehicleBroadcastMessageDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleBroadcastMessageDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleBroadcastMessageDataObject>>());
				services.AddTransient<DeleteHandlerBase<FloorPlanDataObject>, FloorPlanDeleteHandler>();
				services.AddTransient<IDataProviderExtension<FloorPlanDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<FloorPlanDataObject>>());
				services.AddTransient<DeleteHandlerBase<TodaysPreopCheckViewDataObject>, TodaysPreopCheckViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<TodaysPreopCheckViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<TodaysPreopCheckViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<ModelVehicleNormalCardAccessDataObject>, ModelVehicleNormalCardAccessDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ModelVehicleNormalCardAccessDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ModelVehicleNormalCardAccessDataObject>>());
				services.AddTransient<DeleteHandlerBase<SiteVehicleNormalCardAccessDataObject>, SiteVehicleNormalCardAccessDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SiteVehicleNormalCardAccessDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SiteVehicleNormalCardAccessDataObject>>());
				services.AddTransient<DeleteHandlerBase<ImportJobStatusDataObject>, ImportJobStatusDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ImportJobStatusDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ImportJobStatusDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleOtherSettingsDataObject>, VehicleOtherSettingsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleOtherSettingsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleOtherSettingsDataObject>>());
				services.AddTransient<DeleteHandlerBase<DashboardFilterMoreFieldsDataObject>, DashboardFilterMoreFieldsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DashboardFilterMoreFieldsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DashboardFilterMoreFieldsDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleSupervisorsViewDataObject>, VehicleSupervisorsViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleSupervisorsViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleSupervisorsViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<ExportJobStatusDataObject>, ExportJobStatusDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ExportJobStatusDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ExportJobStatusDataObject>>());
				services.AddTransient<DeleteHandlerBase<TimezoneDataObject>, TimezoneDeleteHandler>();
				services.AddTransient<IDataProviderExtension<TimezoneDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<TimezoneDataObject>>());
				services.AddTransient<DeleteHandlerBase<HireDeHireReportFilterDataObject>, HireDeHireReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<HireDeHireReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<HireDeHireReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<LoggedHoursVersusSeatHoursViewDataObject>, LoggedHoursVersusSeatHoursViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<LoggedHoursVersusSeatHoursViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<LoggedHoursVersusSeatHoursViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonAllocationDataObject>, PersonAllocationDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonAllocationDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonAllocationDataObject>>());
				services.AddTransient<DeleteHandlerBase<BroadcastMessageDataObject>, BroadcastMessageDeleteHandler>();
				services.AddTransient<IDataProviderExtension<BroadcastMessageDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<BroadcastMessageDataObject>>());
				services.AddTransient<DeleteHandlerBase<VORReportCombinedViewDataObject>, VORReportCombinedViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VORReportCombinedViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VORReportCombinedViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<UnitUtilisationStoreProcedureDataObject>, UnitUtilisationStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<UnitUtilisationStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<UnitUtilisationStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<ChecklistResultDataObject>, ChecklistResultDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ChecklistResultDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ChecklistResultDataObject>>());
				services.AddTransient<DeleteHandlerBase<UnitUnutilisationStoreProcedureDataObject>, UnitUnutilisationStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<UnitUnutilisationStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<UnitUnutilisationStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleAlertSubscriptionDataObject>, VehicleAlertSubscriptionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleAlertSubscriptionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleAlertSubscriptionDataObject>>());
				services.AddTransient<DeleteHandlerBase<OnDemandAuthorisationStoreProcedureDataObject>, OnDemandAuthorisationStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<OnDemandAuthorisationStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<OnDemandAuthorisationStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<ImpactFrequencyPerTimeSlotViewDataObject>, ImpactFrequencyPerTimeSlotViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ImpactFrequencyPerTimeSlotViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ImpactFrequencyPerTimeSlotViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonToSiteVehicleNormalAccessViewDataObject>, PersonToSiteVehicleNormalAccessViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonToSiteVehicleNormalAccessViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonToSiteVehicleNormalAccessViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<SessionDataObject>, SessionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SessionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SessionDataObject>>());
				services.AddTransient<DeleteHandlerBase<SlamcoreDeviceFilterDataObject>, SlamcoreDeviceFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SlamcoreDeviceFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SlamcoreDeviceFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<DealerDataObject>, DealerDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DealerDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DealerDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOUserDataObject>, GOUserDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOUserDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOUserDataObject>>());
				services.AddTransient<DeleteHandlerBase<ServiceSettingsDataObject>, ServiceSettingsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ServiceSettingsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ServiceSettingsDataObject>>());
				services.AddTransient<DeleteHandlerBase<UpdateFirmwareRequestDataObject>, UpdateFirmwareRequestDeleteHandler>();
				services.AddTransient<IDataProviderExtension<UpdateFirmwareRequestDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<UpdateFirmwareRequestDataObject>>());
				services.AddTransient<DeleteHandlerBase<SlamcoreAPIKeyDataObject>, SlamcoreAPIKeyDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SlamcoreAPIKeyDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SlamcoreAPIKeyDataObject>>());
				services.AddTransient<DeleteHandlerBase<FirmwareDataObject>, FirmwareDeleteHandler>();
				services.AddTransient<IDataProviderExtension<FirmwareDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<FirmwareDataObject>>());
				services.AddTransient<DeleteHandlerBase<CardDataObject>, CardDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CardDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CardDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOUserDepartmentDataObject>, GOUserDepartmentDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOUserDepartmentDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOUserDepartmentDataObject>>());
				services.AddTransient<DeleteHandlerBase<CanruleDataObject>, CanruleDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CanruleDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CanruleDataObject>>());
				services.AddTransient<DeleteHandlerBase<ImpactFrequencyPerWeekDayViewDataObject>, ImpactFrequencyPerWeekDayViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ImpactFrequencyPerWeekDayViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ImpactFrequencyPerWeekDayViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<SlamcoreDeviceHistoryDataObject>, SlamcoreDeviceHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SlamcoreDeviceHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SlamcoreDeviceHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleHireDehireHistoryDataObject>, VehicleHireDehireHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleHireDehireHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleHireDehireHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<ChecklistFailurePerVechicleViewDataObject>, ChecklistFailurePerVechicleViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ChecklistFailurePerVechicleViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ChecklistFailurePerVechicleViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<ImpactReportFilterDataObject>, ImpactReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ImpactReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ImpactReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<IncompletedChecklistViewDataObject>, IncompletedChecklistViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<IncompletedChecklistViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<IncompletedChecklistViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<ReportSubscriptionDataObject>, ReportSubscriptionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ReportSubscriptionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ReportSubscriptionDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleLastGPSLocationViewDataObject>, VehicleLastGPSLocationViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleLastGPSLocationViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleLastGPSLocationViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<PSTATDetailsDataObject>, PSTATDetailsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PSTATDetailsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PSTATDetailsDataObject>>());
				services.AddTransient<DeleteHandlerBase<LicenseExpiryReportFilterDataObject>, LicenseExpiryReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<LicenseExpiryReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<LicenseExpiryReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<FloorZonesDataObject>, FloorZonesDeleteHandler>();
				services.AddTransient<IDataProviderExtension<FloorZonesDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<FloorZonesDataObject>>());
				services.AddTransient<DeleteHandlerBase<CanruleDetailsDataObject>, CanruleDetailsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CanruleDetailsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CanruleDetailsDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleHireDehireSynchronizationOptionsDataObject>, VehicleHireDehireSynchronizationOptionsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleHireDehireSynchronizationOptionsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleHireDehireSynchronizationOptionsDataObject>>());
				services.AddTransient<DeleteHandlerBase<DriverLicenseExpiryStoreProcedureDataObject>, DriverLicenseExpiryStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DriverLicenseExpiryStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DriverLicenseExpiryStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<UnitSummaryReportDataObject>, UnitSummaryReportDeleteHandler>();
				services.AddTransient<IDataProviderExtension<UnitSummaryReportDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<UnitSummaryReportDataObject>>());
				services.AddTransient<DeleteHandlerBase<DepartmentVehicleMasterCardAccessDataObject>, DepartmentVehicleMasterCardAccessDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DepartmentVehicleMasterCardAccessDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DepartmentVehicleMasterCardAccessDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>, VehicleUtilizationLastTwelveHoursStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<ChecklistFailureViewDataObject>, ChecklistFailureViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ChecklistFailureViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ChecklistFailureViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonChecklistLanguageSettingsDataObject>, PersonChecklistLanguageSettingsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonChecklistLanguageSettingsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonChecklistLanguageSettingsDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleSessionlessImpactDataObject>, VehicleSessionlessImpactDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleSessionlessImpactDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleSessionlessImpactDataObject>>());
				services.AddTransient<DeleteHandlerBase<LicenseByModelDataObject>, LicenseByModelDeleteHandler>();
				services.AddTransient<IDataProviderExtension<LicenseByModelDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<LicenseByModelDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllMessageHistoryStoreProcedureDataObject>, AllMessageHistoryStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllMessageHistoryStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllMessageHistoryStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<IoTDeviceMessageCacheDataObject>, IoTDeviceMessageCacheDeleteHandler>();
				services.AddTransient<IDataProviderExtension<IoTDeviceMessageCacheDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<IoTDeviceMessageCacheDataObject>>());
				services.AddTransient<DeleteHandlerBase<ProficiencyCombinedViewDataObject>, ProficiencyCombinedViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ProficiencyCombinedViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ProficiencyCombinedViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<CustomerSnapshotDataObject>, CustomerSnapshotDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CustomerSnapshotDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CustomerSnapshotDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOGroupDataObject>, GOGroupDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOGroupDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOGroupDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehiclesPerModelReportDataObject>, VehiclesPerModelReportDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehiclesPerModelReportDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehiclesPerModelReportDataObject>>());
				services.AddTransient<DeleteHandlerBase<TodaysImpactStoreProcedureDataObject>, TodaysImpactStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<TodaysImpactStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<TodaysImpactStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<BroadcastMessageHistoryFilterDataObject>, BroadcastMessageHistoryFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<BroadcastMessageHistoryFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<BroadcastMessageHistoryFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<DealerDriverDataObject>, DealerDriverDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DealerDriverDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DealerDriverDataObject>>());
				services.AddTransient<DeleteHandlerBase<EmailGroupsDataObject>, EmailGroupsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<EmailGroupsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<EmailGroupsDataObject>>());
				services.AddTransient<DeleteHandlerBase<PreOperationalChecklistDataObject>, PreOperationalChecklistDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PreOperationalChecklistDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PreOperationalChecklistDataObject>>());
				services.AddTransient<DeleteHandlerBase<SynchronizationStatusReportFilterDataObject>, SynchronizationStatusReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SynchronizationStatusReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SynchronizationStatusReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<SlamcoreDeviceDataObject>, SlamcoreDeviceDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SlamcoreDeviceDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SlamcoreDeviceDataObject>>());
				services.AddTransient<DeleteHandlerBase<SiteDataObject>, SiteDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SiteDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SiteDataObject>>());
				services.AddTransient<DeleteHandlerBase<AlertSubscriptionDataObject>, AlertSubscriptionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AlertSubscriptionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AlertSubscriptionDataObject>>());
				services.AddTransient<DeleteHandlerBase<CustomerAuditDataObject>, CustomerAuditDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CustomerAuditDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CustomerAuditDataObject>>());
				services.AddTransient<DeleteHandlerBase<PerVehicleNormalCardAccessDataObject>, PerVehicleNormalCardAccessDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PerVehicleNormalCardAccessDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PerVehicleNormalCardAccessDataObject>>());
				services.AddTransient<DeleteHandlerBase<PedestrianDetectionHistoryDataObject>, PedestrianDetectionHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PedestrianDetectionHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PedestrianDetectionHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonToPerVehicleNormalAccessViewDataObject>, PersonToPerVehicleNormalAccessViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonToPerVehicleNormalAccessViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonToPerVehicleNormalAccessViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<DashboardDriverCardStoreProcedureDataObject>, DashboardDriverCardStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DashboardDriverCardStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DashboardDriverCardStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<TodaysImpactViewDataObject>, TodaysImpactViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<TodaysImpactViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<TodaysImpactViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<DetailedSessionViewDataObject>, DetailedSessionViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DetailedSessionViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DetailedSessionViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<ImportJobLogDataObject>, ImportJobLogDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ImportJobLogDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ImportJobLogDataObject>>());
				services.AddTransient<DeleteHandlerBase<ProficiencyReportFilterDataObject>, ProficiencyReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ProficiencyReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ProficiencyReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<OnDemandSessionDataObject>, OnDemandSessionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<OnDemandSessionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<OnDemandSessionDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllVORStatusStoreProcedureDataObject>, AllVORStatusStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllVORStatusStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllVORStatusStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<VORSettingHistoryDataObject>, VORSettingHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VORSettingHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VORSettingHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllUserSummaryStoreProcedureDataObject>, AllUserSummaryStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllUserSummaryStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllUserSummaryStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<CustomerSSODetailDataObject>, CustomerSSODetailDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CustomerSSODetailDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CustomerSSODetailDataObject>>());
				services.AddTransient<DeleteHandlerBase<UploadLogoRequestDataObject>, UploadLogoRequestDeleteHandler>();
				services.AddTransient<IDataProviderExtension<UploadLogoRequestDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<UploadLogoRequestDataObject>>());
				services.AddTransient<DeleteHandlerBase<AllVehicleCalibrationFilterDataObject>, AllVehicleCalibrationFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<AllVehicleCalibrationFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<AllVehicleCalibrationFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<DepartmentHourSettingsDataObject>, DepartmentHourSettingsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DepartmentHourSettingsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DepartmentHourSettingsDataObject>>());
				services.AddTransient<DeleteHandlerBase<CategoryTemplateDataObject>, CategoryTemplateDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CategoryTemplateDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CategoryTemplateDataObject>>());
				services.AddTransient<DeleteHandlerBase<DashboardVehicleCardViewDataObject>, DashboardVehicleCardViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DashboardVehicleCardViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DashboardVehicleCardViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<CurrentStatusDriverViewDataObject>, CurrentStatusDriverViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CurrentStatusDriverViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CurrentStatusDriverViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<UnitSummaryStoreProcedureDataObject>, UnitSummaryStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<UnitSummaryStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<UnitSummaryStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<ReportTypeDataObject>, ReportTypeDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ReportTypeDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ReportTypeDataObject>>());
				services.AddTransient<DeleteHandlerBase<SiteVehicleMasterCardAccessDataObject>, SiteVehicleMasterCardAccessDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SiteVehicleMasterCardAccessDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SiteVehicleMasterCardAccessDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonToDepartmentVehicleNormalAccessViewDataObject>, PersonToDepartmentVehicleNormalAccessViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonToDepartmentVehicleNormalAccessViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonToDepartmentVehicleNormalAccessViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOUserGroupDataObject>, GOUserGroupDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOUserGroupDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOUserGroupDataObject>>());
				services.AddTransient<DeleteHandlerBase<CustomerFeatureSubscriptionDataObject>, CustomerFeatureSubscriptionDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CustomerFeatureSubscriptionDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CustomerFeatureSubscriptionDataObject>>());
				services.AddTransient<DeleteHandlerBase<VehicleToPreOpChecklistViewDataObject>, VehicleToPreOpChecklistViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<VehicleToPreOpChecklistViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<VehicleToPreOpChecklistViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonToSiteVehicleMasterAccessViewDataObject>, PersonToSiteVehicleMasterAccessViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonToSiteVehicleMasterAccessViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonToSiteVehicleMasterAccessViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<DealerConfigurationDataObject>, DealerConfigurationDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DealerConfigurationDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DealerConfigurationDataObject>>());
				services.AddTransient<DeleteHandlerBase<TodaysPreopCheckStoreProcedureDataObject>, TodaysPreopCheckStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<TodaysPreopCheckStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<TodaysPreopCheckStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<CustomerPreOperationalChecklistTemplateDataObject>, CustomerPreOperationalChecklistTemplateDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CustomerPreOperationalChecklistTemplateDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CustomerPreOperationalChecklistTemplateDataObject>>());
				services.AddTransient<DeleteHandlerBase<CurrentDriverStatusChartViewDataObject>, CurrentDriverStatusChartViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<CurrentDriverStatusChartViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<CurrentDriverStatusChartViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<GOChangeDeltaDataObject>, GOChangeDeltaDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GOChangeDeltaDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GOChangeDeltaDataObject>>());
				services.AddTransient<DeleteHandlerBase<GO2FAConfigurationDataObject>, GO2FAConfigurationDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GO2FAConfigurationDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GO2FAConfigurationDataObject>>());
				services.AddTransient<DeleteHandlerBase<PreOpReportFilterDataObject>, PreOpReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PreOpReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PreOpReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<SiteFloorPlanDataObject>, SiteFloorPlanDeleteHandler>();
				services.AddTransient<IDataProviderExtension<SiteFloorPlanDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<SiteFloorPlanDataObject>>());
				services.AddTransient<DeleteHandlerBase<BroadcastMessageHistoryDataObject>, BroadcastMessageHistoryDeleteHandler>();
				services.AddTransient<IDataProviderExtension<BroadcastMessageHistoryDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<BroadcastMessageHistoryDataObject>>());
				services.AddTransient<DeleteHandlerBase<GoUserToCustomerDataObject>, GoUserToCustomerDeleteHandler>();
				services.AddTransient<IDataProviderExtension<GoUserToCustomerDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<GoUserToCustomerDataObject>>());
				services.AddTransient<DeleteHandlerBase<ImportJobBatchDataObject>, ImportJobBatchDeleteHandler>();
				services.AddTransient<IDataProviderExtension<ImportJobBatchDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<ImportJobBatchDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonDataObject>, PersonDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonDataObject>>());
				services.AddTransient<DeleteHandlerBase<DetailedVORSessionStoreProcedureDataObject>, DetailedVORSessionStoreProcedureDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DetailedVORSessionStoreProcedureDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DetailedVORSessionStoreProcedureDataObject>>());
				services.AddTransient<DeleteHandlerBase<PersonToModelVehicleNormalAccessViewDataObject>, PersonToModelVehicleNormalAccessViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PersonToModelVehicleNormalAccessViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PersonToModelVehicleNormalAccessViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<NetworkSettingsDataObject>, NetworkSettingsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<NetworkSettingsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<NetworkSettingsDataObject>>());
				services.AddTransient<DeleteHandlerBase<DepartmentDataObject>, DepartmentDeleteHandler>();
				services.AddTransient<IDataProviderExtension<DepartmentDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<DepartmentDataObject>>());
				services.AddTransient<DeleteHandlerBase<MachineUnlockReportFilterDataObject>, MachineUnlockReportFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<MachineUnlockReportFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<MachineUnlockReportFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<PerVehicleMasterCardAccessDataObject>, PerVehicleMasterCardAccessDeleteHandler>();
				services.AddTransient<IDataProviderExtension<PerVehicleMasterCardAccessDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<PerVehicleMasterCardAccessDataObject>>());
				services.AddTransient<DeleteHandlerBase<UnitUtilisationCombinedViewDataObject>, UnitUtilisationCombinedViewDeleteHandler>();
				services.AddTransient<IDataProviderExtension<UnitUtilisationCombinedViewDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<UnitUtilisationCombinedViewDataObject>>());
				services.AddTransient<DeleteHandlerBase<FeatureSubscriptionsFilterDataObject>, FeatureSubscriptionsFilterDeleteHandler>();
				services.AddTransient<IDataProviderExtension<FeatureSubscriptionsFilterDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<FeatureSubscriptionsFilterDataObject>>());
				services.AddTransient<DeleteHandlerBase<OnDemandSettingsDataObject>, OnDemandSettingsDeleteHandler>();
				services.AddTransient<IDataProviderExtension<OnDemandSettingsDataObject>>(x => x.GetRequiredService<DeleteHandlerBase<OnDemandSettingsDataObject>>());
			}
            #endregion DeleteHandler DataProvider Extensions
			// Auto-Include path mapping for Presentation Elements
			services.AddSingleton<IAutoInclude, AutoInclude>();
			
            //In the future it should be interesting to add this dependency only when we need it, ie when we have at least one Publipostage
            //services.AddSingleton<IDocumentProcessor, DocumentProcessor>();
 			services.AddScoped<GOTaskRunner, GOTaskRunner>();
			services.AddTransient(typeof(Task<>), typeof(Task<>));
			services.AddSingleton<IGORunningTasks, GORunningTasks>();
 

			// ImageField AutoResize / Restricted Size Extensions
			{
				services.AddTransient<IDataProviderExtension<ModelDataObject>, GOFileUploader>();
				services.AddTransient<IDataProviderExtension<CustomerDataObject>, GOFileUploader>();
				services.AddTransient<IDataProviderExtension<VehicleDataObject>, GOFileUploader>();
				services.AddTransient<IDataProviderExtension<LicenseByModelDataObject>, GOFileUploader>();
				services.AddTransient<IDataProviderExtension<CategoryTemplateDataObject>, GOFileUploader>();
				services.AddTransient<IDataProviderExtension<PersonDataObject>, GOFileUploader>();
			}
 

			// Internationalization with I18Next
			// Translation files should be located at:
			// - wwwroot/locales/{language}/translation.json
			// Example structure:
			// - wwwroot/locales/backend/en/translation.json
			// - wwwroot/locales/backend/fr/translation.json
			// Each JSON file should contain key-value pairs for translations
			services.AddI18NextLocalization(i18n =>
			{
				// Use JSON files from the locales folder
				i18n.AddBackend(new JsonFileBackend("wwwroot/locales/backend"));

				// Set default language and namespace
				i18n.UseDefaultLanguage("english");
				i18n.UseDefaultNamespace("translation");
			});

			// Framework Services
			{
				services.AddTransient<IMailbox, OutlookMailbox>();
				services.AddTransient<ISMTPClient, SMTPClient>();
			}

			services.AddTransient<DataObjectConverter>();
			services.AddTransient<ObjectsDataSetConverter>();

			// Register custom types if any
            CustomContainer.RegisterCustomTypes(services);
        }
    }
}





 