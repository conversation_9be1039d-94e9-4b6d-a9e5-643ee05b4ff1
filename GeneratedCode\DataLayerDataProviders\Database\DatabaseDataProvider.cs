﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using Microsoft.Extensions.DependencyInjection;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;

namespace FleetXQ.Data.DataProviders.Database
{
    public abstract class DatabaseDataProvider<TENTITY> : DataProvider<TENTITY> where TENTITY : class, IDataObject
    {	
        public DatabaseDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<TENTITY> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, INHibernateSessionController nhibernateSessionController) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
		{
			_nhibernateSessionController = nhibernateSessionController;
		}

		protected IAuthentication _authentication => _serviceProvider.GetRequiredService<IAuthentication>();
		protected INHibernateSessionController _nhibernateSessionController;

        protected static bool HasDatabaseDataSource(IDataObject entity)
        {
			return (entity is ChecklistDetailDataObject || entity is SlamcoreAwareAuthenticationDetailsDataObject || entity is FeatureSubscriptionTemplateDataObject || entity is PersonToModelVehicleMasterAccessViewDataObject || entity is VehicleLockoutDataObject || entity is RevisionDataObject || entity is AlertDataObject || entity is WebsiteUserDataObject || entity is DriverAccessAbuseFilterDataObject || entity is ContactPersonInformationDataObject || entity is ChecklistSettingsDataObject || entity is VehicleDiagnosticDataObject || entity is GOUser2FADataObject || entity is VehicleGPSDataObject || entity is ModuleDataObject || entity is PersonToPerVehicleMasterAccessViewDataObject || entity is AccessGroupDataObject || entity is HelpDataObject || entity is CurrentStatusCombinedViewDataObject || entity is CurrentVehicleStatusChartViewDataObject || entity is DealerFeatureSubscriptionDataObject || entity is MessageHistoryDataObject || entity is AccessGroupToSiteDataObject || entity is CustomerToModelDataObject || entity is RegionDataObject || entity is ModelDataObject || entity is SlamcoreDeviceConnectionViewDataObject || entity is CustomerDataObject || entity is DepartmentChecklistDataObject || entity is ModuleHistoryDataObject || entity is CountryDataObject || entity is GPSHistoryDataObject || entity is ImpactsForVehicleViewDataObject || entity is SlamcorePedestrianDetectionDataObject || entity is IOFIELDDataObject || entity is EmailDataObject || entity is LicenceDetailDataObject || entity is PedestrianDetectionHistoryFilterDataObject || entity is GOLoginHistoryDataObject || entity is MainDashboardFilterDataObject || entity is AccessGroupTemplateDataObject || entity is CardToCardAccessDataObject || entity is ZoneCoordinatesDataObject || entity is SnapshotDataObject || entity is GOTaskDataObject || entity is GeneralProductivityReportFilterDataObject || entity is DepartmentVehicleNormalCardAccessDataObject || entity is CustomerToPersonViewDataObject || entity is VehicleSlamcoreLocationHistoryDataObject || entity is PermissionDataObject || entity is SessionDetailsDataObject || entity is VehicleDataObject || entity is DriverDataObject || entity is EmailGroupsToPersonDataObject || entity is GOGroupRoleDataObject || entity is ModelVehicleMasterCardAccessDataObject || entity is EmailSubscriptionReportFilterDataObject || entity is DashboardDriverCardViewDataObject || entity is GOSecurityTokensDataObject || entity is AlertHistoryDataObject || entity is DashboardFilterDataObject || entity is VORReportFilterDataObject || entity is OnDemandAuthorisationFilterDataObject || entity is WebsiteRoleDataObject || entity is GOUserRoleDataObject || entity is GeneralProductivityViewDataObject || entity is InspectionDataObject || entity is ImpactDataObject || entity is PersonToDepartmentVehicleMasterAccessViewDataObject || entity is CurrentStatusVehicleViewDataObject || entity is CustomerModelDataObject || entity is TagDataObject || entity is VehicleBroadcastMessageDataObject || entity is FloorPlanDataObject || entity is ModelVehicleNormalCardAccessDataObject || entity is SiteVehicleNormalCardAccessDataObject || entity is ImportJobStatusDataObject || entity is VehicleOtherSettingsDataObject || entity is DashboardFilterMoreFieldsDataObject || entity is VehicleSupervisorsViewDataObject || entity is ExportJobStatusDataObject || entity is TimezoneDataObject || entity is HireDeHireReportFilterDataObject || entity is PersonAllocationDataObject || entity is BroadcastMessageDataObject || entity is VORReportCombinedViewDataObject || entity is ChecklistResultDataObject || entity is VehicleAlertSubscriptionDataObject || entity is PersonToSiteVehicleNormalAccessViewDataObject || entity is SessionDataObject || entity is SlamcoreDeviceFilterDataObject || entity is DealerDataObject || entity is GOUserDataObject || entity is ServiceSettingsDataObject || entity is UpdateFirmwareRequestDataObject || entity is SlamcoreAPIKeyDataObject || entity is FirmwareDataObject || entity is CardDataObject || entity is GOUserDepartmentDataObject || entity is CanruleDataObject || entity is SlamcoreDeviceHistoryDataObject || entity is VehicleHireDehireHistoryDataObject || entity is ChecklistFailurePerVechicleViewDataObject || entity is ImpactReportFilterDataObject || entity is ReportSubscriptionDataObject || entity is VehicleLastGPSLocationViewDataObject || entity is PSTATDetailsDataObject || entity is LicenseExpiryReportFilterDataObject || entity is FloorZonesDataObject || entity is CanruleDetailsDataObject || entity is VehicleHireDehireSynchronizationOptionsDataObject || entity is DepartmentVehicleMasterCardAccessDataObject || entity is ChecklistFailureViewDataObject || entity is PersonChecklistLanguageSettingsDataObject || entity is VehicleSessionlessImpactDataObject || entity is LicenseByModelDataObject || entity is IoTDeviceMessageCacheDataObject || entity is ProficiencyCombinedViewDataObject || entity is CustomerSnapshotDataObject || entity is GOGroupDataObject || entity is VehiclesPerModelReportDataObject || entity is BroadcastMessageHistoryFilterDataObject || entity is DealerDriverDataObject || entity is EmailGroupsDataObject || entity is PreOperationalChecklistDataObject || entity is SynchronizationStatusReportFilterDataObject || entity is SlamcoreDeviceDataObject || entity is SiteDataObject || entity is AlertSubscriptionDataObject || entity is CustomerAuditDataObject || entity is PerVehicleNormalCardAccessDataObject || entity is PedestrianDetectionHistoryDataObject || entity is PersonToPerVehicleNormalAccessViewDataObject || entity is ImportJobLogDataObject || entity is ProficiencyReportFilterDataObject || entity is OnDemandSessionDataObject || entity is VORSettingHistoryDataObject || entity is CustomerSSODetailDataObject || entity is UploadLogoRequestDataObject || entity is AllVehicleCalibrationFilterDataObject || entity is DepartmentHourSettingsDataObject || entity is CategoryTemplateDataObject || entity is DashboardVehicleCardViewDataObject || entity is CurrentStatusDriverViewDataObject || entity is ReportTypeDataObject || entity is SiteVehicleMasterCardAccessDataObject || entity is PersonToDepartmentVehicleNormalAccessViewDataObject || entity is GOUserGroupDataObject || entity is CustomerFeatureSubscriptionDataObject || entity is VehicleToPreOpChecklistViewDataObject || entity is PersonToSiteVehicleMasterAccessViewDataObject || entity is DealerConfigurationDataObject || entity is CustomerPreOperationalChecklistTemplateDataObject || entity is CurrentDriverStatusChartViewDataObject || entity is GOChangeDeltaDataObject || entity is PreOpReportFilterDataObject || entity is SiteFloorPlanDataObject || entity is BroadcastMessageHistoryDataObject || entity is GoUserToCustomerDataObject || entity is ImportJobBatchDataObject || entity is PersonDataObject || entity is PersonToModelVehicleNormalAccessViewDataObject || entity is NetworkSettingsDataObject || entity is DepartmentDataObject || entity is MachineUnlockReportFilterDataObject || entity is PerVehicleMasterCardAccessDataObject || entity is UnitUtilisationCombinedViewDataObject || entity is FeatureSubscriptionsFilterDataObject || entity is OnDemandSettingsDataObject);
		}

        // filterExpression is used to filter data, when filter is statically known. dynamicFilterExpression is used for dynamic filtering, when filter is not known at compile time. Both can be used at the same time
		protected abstract Task<int> DoCountFromDatabaseAsync(LambdaExpression filterExpression, string dynamicFilterExpression, object[] dynamicFilterArguments, IObjectsDataSet context, Parameters parameters);
        // securityFilterExpression is for security : check if the user is allowed to delete the entity
        protected abstract Task DoDeleteFromDatabaseAsync(TENTITY entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Parameters parameters);
        // securityFilterExpression is for security : check if the user is allowed to read the entity
        protected abstract Task<TENTITY> DoGetFromDatabaseAsync(TENTITY entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Parameters parameters);
        // filterExpression is used to filter data, when filter is statically known. dynamicFilterExpression is used for dynamic filtering, when filter is not known at compile time. Both can be used at the same time
        protected abstract Task<DataObjectCollection<TENTITY>> DoGetCollectionFromDatabaseAsync(LambdaExpression filterExpression, string dynamicFilterExpression, object[] dynamicFilterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Parameters parameters);
       
        protected virtual async Task<TENTITY> DoSaveToDatabaseAsync(
			TENTITY entity, 
			List<string> includes, 
			IObjectsDataSet context, 
			Parameters parameters)
		{
			// Ensure got a dataset
			if (entity.ObjectsDataSet == null)
				entity.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

			// Ensure entity is in its own dataset
			var self = entity.ObjectsDataSet.GetObject(entity);
			if (self == null)
				entity.ObjectsDataSet.AddObject(entity);

			// Note: We can skip objects marked for deletion, because before arriving here, the delete resolution algorithm will already have processed them 
			// Wee just need to remove the marked-for-deletion objects so that they aren't mapped or returned to caller
			foreach (var deletedObj in entity.ObjectsDataSet.GetObjectsMarkedForDeletion().ToArray())
			{
				entity.ObjectsDataSet.RemoveObject(deletedObj);

				// Remove from the context too 
				if (context != null)
					context.RemoveObject(deletedObj);
			}

			// Get data mapping setting from the parameters (deep is the default)
			// Deep mapping means we'll explore all relations, else will only treat the specified entity (i.e. a 'flat' single entity save)
			bool deepMapping = parameters == null || !parameters.ContainsKey(ParameterKeys.DeepDataMapping) || (bool)parameters[ParameterKeys.DeepDataMapping] == true;

			// entity returned from SaveToDatabaseThroughORMAsync can be null (e.g. if nothing dirty/new in the saveset, or if deleted)
			entity = await SaveToDatabaseThroughORMAsync(entity, deepMapping);
			
				// for the refetch, security can be skipped if no related data requested (since we just wrote it, it's just a flat re-sync)
			bool canRefetchSkipSecurity = includes == null || !includes.Any() || ParameterKeys.IsOptionEnabled(parameters, ParameterKeys.ORMSaveRefetchSkipSecurity);
			return entity == null ? null : await GetAsync(entity, includes: includes, parameters: parameters, skipSecurity: canRefetchSkipSecurity);
		}

		/// Does the Save or Update through the ORM and returns an entity instance with any database generated PK assigned
		/// Note: We don't support databse generated composite keys - best avoided.
		private async Task<TENTITY> SaveToDatabaseThroughORMAsync(TENTITY entity, bool deepMapping)
		{
			TENTITY result = null;

			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);

			if (entity.ObjectsDataSet.GetAllObjects().Count(o => o.IsDirty || o.IsNew) == 0)
			{
				return entity;
			}
			else
			{
				object newPK = null;

				if (deepMapping)
				{
					// Deep mapping is implemented as a dependency graph resolution via ISaveDependencyResolver
					// Note: This means if a client application has a specific known dependency that the default implemenation fails to resolve, you can hook your own implementation in here
					// (e.g. call the default one, and then adjust the result)
					var saveList = _serviceProvider.GetRequiredService<ISaveDependencyResolver>().Sort(entity.ObjectsDataSet);
					foreach (var obj in saveList)
					{
						var resultPk = await SaveOrUpdateThroughORMAsync(session, obj, isMainEntity: ReferenceEquals(obj, entity));
						newPK = newPK ?? resultPk;
					}
				}
				else
				{
					newPK = await SaveOrUpdateThroughORMAsync(session, entity, isMainEntity: true);
				}

				if (newPK != null)
				{
					result = (TENTITY)entity.Clone(recursive: false);
					result.AssignPrimaryKey(newPK);
				}
			}

			return result ?? entity;
		}

		/// if obj.IsNew, returns any database generated PK, or else the already-assigned PK
		private async Task<object> SaveOrUpdateThroughORMAsync(NHibernate.ISession session, IDataObject obj, bool isMainEntity = false)
		{
			object newPK = null;

			var toSave = obj.ToORMEntity();

			if (toSave == null) // case for non-database data sourced entities
            {
				return null;
            }

			if (obj.IsNew)
			{
				var pk = await session.SaveAsync(toSave);

				if (isMainEntity)
				{
					newPK = pk;
				}
			}
			else
			{
				await session.UpdateAsync(toSave);
			}

			return newPK;
		}

        // filterExpression is used to filter data, when filter is statically known. dynamicFilterExpression is used for dynamic filtering, when filter is not known at compile time. Both can be used at the same time
        protected override async Task<int> DoCountAsync(LambdaExpression filterExpression, string dynamicFilterExpression, object[] dynamicFilterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            Int32 toReturn;

			IsolationLevel isolationLevel = IsolationLevel.ReadCommitted;

			if (parameters.ContainsKey("IsolationLevel"))
            {
				if (!Enum.TryParse(parameters["IsolationLevel"].ToString(), out isolationLevel))
                {
					throw new GOServerException($"Unknown Isolation Level {parameters["IsolationLevel"].ToString()}");
                }
            }

			var transaction = EnsureTransaction(parameters);
			var transactInfo = transaction == null ? null : new TransactionParticipant { Who = this, Id = Guid.NewGuid().ToString(), Info = "DoCount(" + typeof(TENTITY).Name + ")" };
			await transaction.JoinAsync(parameters, "DoCount", transactInfo, isolationLevel : isolationLevel);

            try
            {
                toReturn = await DoCountFromDatabaseAsync(filterExpression, dynamicFilterExpression, dynamicFilterArguments, context, parameters);
            }
			catch (Exception e)
			{
				await transaction.LeaveAsync(e, transactInfo);
				throw;
			}
			
			await transaction.LeaveAsync(null, transactInfo);

            return toReturn;
        }

        // securityFilterExpression is for security : check if the user is allowed to delete the entity
        protected override async Task DoDeleteAsync(TENTITY entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
			IsolationLevel isolationLevel = IsolationLevel.ReadCommitted;

			if (parameters.ContainsKey("IsolationLevel"))
            {
				if (!Enum.TryParse(parameters["IsolationLevel"].ToString(), out isolationLevel))
                {
					throw new GOServerException($"Unknown Isolation Level {parameters["IsolationLevel"].ToString()}");
                }
            }

			var transaction = EnsureTransaction(parameters);
			var transactInfo = transaction == null ? null : new TransactionParticipant { Who = this, Id = Guid.NewGuid().ToString(), Info = "DoDelete(" + typeof(TENTITY).Name + ")" };	
			await transaction.JoinAsync(parameters, "DoDelete", transactInfo, isolationLevel : isolationLevel);

            try
            {
                var entityToDelete = await DoGetAsync(entity, securityFilterExpression, null, context, parameters);

				// Don't throw ResourceNotFoundException during a Delete: If the resource isn't there, then that's what we want, stay quiet
				if (entityToDelete != null)
				{
					await DoDeleteFromDatabaseAsync(entityToDelete, securityFilterExpression, context, parameters);
				}
            }
			catch (Exception e)
			{
				await transaction.LeaveAsync(e, transactInfo);	
				throw;
			}
			
			await transaction.LeaveAsync(null, transactInfo);
        }

        // securityFilterExpression is for security : check if the user is allowed to read the entity
        protected override async Task<TENTITY> DoGetAsync(TENTITY entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            TENTITY result;
        
			IsolationLevel isolationLevel = IsolationLevel.ReadCommitted;

			if (parameters.ContainsKey("IsolationLevel"))
            {
				if (!Enum.TryParse(parameters["IsolationLevel"].ToString(), out isolationLevel))
                {
					throw new GOServerException($"Unknown Isolation Level {parameters["IsolationLevel"].ToString()}");
                }
            }
    
			var transaction = EnsureTransaction(parameters);
			var transactInfo = transaction == null ? null : new TransactionParticipant { Who = this, Id = Guid.NewGuid().ToString(), Info = "DoGet(" + typeof(TENTITY).Name + ")" };
			await transaction.JoinAsync(parameters, "DoGet", transactInfo, isolationLevel : isolationLevel);

            try
            {
                result = await DoGetFromDatabaseAsync(entity, securityFilterExpression, includes, context, parameters);
            }
			catch (Exception e)
			{
				await transaction.LeaveAsync(e, transactInfo);
				throw;
			}
			
			await transaction.LeaveAsync(null, transactInfo);

            return result;
        }

        // filterExpression is used to filter data, when filter is statically known. dynamicFilterExpression is used for dynamic filtering, when filter is not known at compile time. Both can be used at the same time
        protected override async Task<DataObjectCollection<TENTITY>> DoGetCollectionAsync(LambdaExpression filterExpression, string dynamicFilterExpression, object[] dynamicFilterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            DataObjectCollection<TENTITY> toReturn;

			IsolationLevel isolationLevel = IsolationLevel.ReadCommitted;

			if (parameters.ContainsKey("IsolationLevel"))
            {
				if (!Enum.TryParse(parameters["IsolationLevel"].ToString(), out isolationLevel))
                {
					throw new GOServerException($"Unknown Isolation Level {parameters["IsolationLevel"].ToString()}");
                }
            }

			var transaction = EnsureTransaction(parameters);
			var transactInfo = transaction == null ? null : new TransactionParticipant { Who = this, Id = Guid.NewGuid().ToString(), Info = "DoGetCollection(" + typeof(TENTITY).Name + ")" };			
			await transaction.JoinAsync(parameters, "DoGetCollection", transactInfo, isolationLevel : isolationLevel);

            try
            {
                toReturn = await DoGetCollectionFromDatabaseAsync(filterExpression, dynamicFilterExpression, dynamicFilterArguments, orderByPredicate,  pageNumber, pageSize, includes, context, parameters);
            }
			catch (Exception e)
			{
				await transaction.LeaveAsync(e, transactInfo);
				throw;
			}
			
			await transaction.LeaveAsync(null, transactInfo);

            return toReturn;
        }

        // securityFilterExpression no longer checked here - has already been checked higher up the stack
        protected override async Task<TENTITY> DoSaveAsync(TENTITY entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            TENTITY toReturn;
			
			IsolationLevel isolationLevel = IsolationLevel.ReadCommitted;

			if (parameters.ContainsKey("IsolationLevel"))
            {
				if (!Enum.TryParse(parameters["IsolationLevel"].ToString(), out isolationLevel))
                {
					throw new GOServerException($"Unknown Isolation Level {parameters["IsolationLevel"].ToString()}");
                }
            }

			var transaction = EnsureTransaction(parameters);
			var transactInfo = transaction == null ? null : new TransactionParticipant { Who = this, Id = Guid.NewGuid().ToString(), Info = "DoSave(" + typeof(TENTITY).Name + ")" };
			await transaction.JoinAsync(parameters, "DoSave", transactInfo, isolationLevel : isolationLevel);

            try
            {
				toReturn = await DoSaveToDatabaseAsync(entity, includes, context, parameters);
            }
			catch (Exception e)
			{
				await transaction.LeaveAsync(e, transactInfo);
				throw;
			}
			
			await transaction.LeaveAsync(null, transactInfo);

            return toReturn;
        }

		private DatabaseDataProviderTransaction EnsureTransaction(Parameters parameters)
		{
			if (parameters == null)
				parameters = new Parameters();

			var transaction = _serviceProvider.GetRequiredService<ITransactionProvider>().GetTransaction(parameters) as DatabaseDataProviderTransaction;

			if (transaction == null)
				transaction = _serviceProvider.GetRequiredService<ITransactionProvider>().NewTransaction(parameters) as DatabaseDataProviderTransaction;

			return transaction;
		}

		public static Type GetConcreteEntityDatabaseProvider(IDataObject entity)
		{
			Type entityDataProviderType = null;

			switch (entity.GetType().Name)
			{
			case "ChecklistDetailDataObject":
				entityDataProviderType = typeof(ChecklistDetailDataProvider);
				break;
			case "SlamcoreAwareAuthenticationDetailsDataObject":
				entityDataProviderType = typeof(SlamcoreAwareAuthenticationDetailsDataProvider);
				break;
			case "FeatureSubscriptionTemplateDataObject":
				entityDataProviderType = typeof(FeatureSubscriptionTemplateDataProvider);
				break;
			case "PersonToModelVehicleMasterAccessViewDataObject":
				entityDataProviderType = typeof(PersonToModelVehicleMasterAccessViewDataProvider);
				break;
			case "VehicleLockoutDataObject":
				entityDataProviderType = typeof(VehicleLockoutDataProvider);
				break;
			case "RevisionDataObject":
				entityDataProviderType = typeof(RevisionDataProvider);
				break;
			case "AlertDataObject":
				entityDataProviderType = typeof(AlertDataProvider);
				break;
			case "WebsiteUserDataObject":
				entityDataProviderType = typeof(WebsiteUserDataProvider);
				break;
			case "DriverAccessAbuseFilterDataObject":
				entityDataProviderType = typeof(DriverAccessAbuseFilterDataProvider);
				break;
			case "ContactPersonInformationDataObject":
				entityDataProviderType = typeof(ContactPersonInformationDataProvider);
				break;
			case "ChecklistSettingsDataObject":
				entityDataProviderType = typeof(ChecklistSettingsDataProvider);
				break;
			case "VehicleDiagnosticDataObject":
				entityDataProviderType = typeof(VehicleDiagnosticDataProvider);
				break;
			case "GOUser2FADataObject":
				entityDataProviderType = typeof(GOUser2FADataProvider);
				break;
			case "VehicleGPSDataObject":
				entityDataProviderType = typeof(VehicleGPSDataProvider);
				break;
			case "ModuleDataObject":
				entityDataProviderType = typeof(ModuleDataProvider);
				break;
			case "PersonToPerVehicleMasterAccessViewDataObject":
				entityDataProviderType = typeof(PersonToPerVehicleMasterAccessViewDataProvider);
				break;
			case "AccessGroupDataObject":
				entityDataProviderType = typeof(AccessGroupDataProvider);
				break;
			case "HelpDataObject":
				entityDataProviderType = typeof(HelpDataProvider);
				break;
			case "CurrentStatusCombinedViewDataObject":
				entityDataProviderType = typeof(CurrentStatusCombinedViewDataProvider);
				break;
			case "CurrentVehicleStatusChartViewDataObject":
				entityDataProviderType = typeof(CurrentVehicleStatusChartViewDataProvider);
				break;
			case "DealerFeatureSubscriptionDataObject":
				entityDataProviderType = typeof(DealerFeatureSubscriptionDataProvider);
				break;
			case "MessageHistoryDataObject":
				entityDataProviderType = typeof(MessageHistoryDataProvider);
				break;
			case "AccessGroupToSiteDataObject":
				entityDataProviderType = typeof(AccessGroupToSiteDataProvider);
				break;
			case "CustomerToModelDataObject":
				entityDataProviderType = typeof(CustomerToModelDataProvider);
				break;
			case "RegionDataObject":
				entityDataProviderType = typeof(RegionDataProvider);
				break;
			case "ModelDataObject":
				entityDataProviderType = typeof(ModelDataProvider);
				break;
			case "SlamcoreDeviceConnectionViewDataObject":
				entityDataProviderType = typeof(SlamcoreDeviceConnectionViewDataProvider);
				break;
			case "CustomerDataObject":
				entityDataProviderType = typeof(CustomerDataProvider);
				break;
			case "DepartmentChecklistDataObject":
				entityDataProviderType = typeof(DepartmentChecklistDataProvider);
				break;
			case "ModuleHistoryDataObject":
				entityDataProviderType = typeof(ModuleHistoryDataProvider);
				break;
			case "CountryDataObject":
				entityDataProviderType = typeof(CountryDataProvider);
				break;
			case "GPSHistoryDataObject":
				entityDataProviderType = typeof(GPSHistoryDataProvider);
				break;
			case "ImpactsForVehicleViewDataObject":
				entityDataProviderType = typeof(ImpactsForVehicleViewDataProvider);
				break;
			case "SlamcorePedestrianDetectionDataObject":
				entityDataProviderType = typeof(SlamcorePedestrianDetectionDataProvider);
				break;
			case "IOFIELDDataObject":
				entityDataProviderType = typeof(IOFIELDDataProvider);
				break;
			case "EmailDataObject":
				entityDataProviderType = typeof(EmailDataProvider);
				break;
			case "LicenceDetailDataObject":
				entityDataProviderType = typeof(LicenceDetailDataProvider);
				break;
			case "PedestrianDetectionHistoryFilterDataObject":
				entityDataProviderType = typeof(PedestrianDetectionHistoryFilterDataProvider);
				break;
			case "GOLoginHistoryDataObject":
				entityDataProviderType = typeof(GOLoginHistoryDataProvider);
				break;
			case "MainDashboardFilterDataObject":
				entityDataProviderType = typeof(MainDashboardFilterDataProvider);
				break;
			case "AccessGroupTemplateDataObject":
				entityDataProviderType = typeof(AccessGroupTemplateDataProvider);
				break;
			case "CardToCardAccessDataObject":
				entityDataProviderType = typeof(CardToCardAccessDataProvider);
				break;
			case "ZoneCoordinatesDataObject":
				entityDataProviderType = typeof(ZoneCoordinatesDataProvider);
				break;
			case "SnapshotDataObject":
				entityDataProviderType = typeof(SnapshotDataProvider);
				break;
			case "GOTaskDataObject":
				entityDataProviderType = typeof(GOTaskDataProvider);
				break;
			case "GeneralProductivityReportFilterDataObject":
				entityDataProviderType = typeof(GeneralProductivityReportFilterDataProvider);
				break;
			case "DepartmentVehicleNormalCardAccessDataObject":
				entityDataProviderType = typeof(DepartmentVehicleNormalCardAccessDataProvider);
				break;
			case "CustomerToPersonViewDataObject":
				entityDataProviderType = typeof(CustomerToPersonViewDataProvider);
				break;
			case "VehicleSlamcoreLocationHistoryDataObject":
				entityDataProviderType = typeof(VehicleSlamcoreLocationHistoryDataProvider);
				break;
			case "PermissionDataObject":
				entityDataProviderType = typeof(PermissionDataProvider);
				break;
			case "SessionDetailsDataObject":
				entityDataProviderType = typeof(SessionDetailsDataProvider);
				break;
			case "VehicleDataObject":
				entityDataProviderType = typeof(VehicleDataProvider);
				break;
			case "DriverDataObject":
				entityDataProviderType = typeof(DriverDataProvider);
				break;
			case "EmailGroupsToPersonDataObject":
				entityDataProviderType = typeof(EmailGroupsToPersonDataProvider);
				break;
			case "GOGroupRoleDataObject":
				entityDataProviderType = typeof(GOGroupRoleDataProvider);
				break;
			case "ModelVehicleMasterCardAccessDataObject":
				entityDataProviderType = typeof(ModelVehicleMasterCardAccessDataProvider);
				break;
			case "EmailSubscriptionReportFilterDataObject":
				entityDataProviderType = typeof(EmailSubscriptionReportFilterDataProvider);
				break;
			case "DashboardDriverCardViewDataObject":
				entityDataProviderType = typeof(DashboardDriverCardViewDataProvider);
				break;
			case "GOSecurityTokensDataObject":
				entityDataProviderType = typeof(GOSecurityTokensDataProvider);
				break;
			case "AlertHistoryDataObject":
				entityDataProviderType = typeof(AlertHistoryDataProvider);
				break;
			case "DashboardFilterDataObject":
				entityDataProviderType = typeof(DashboardFilterDataProvider);
				break;
			case "VORReportFilterDataObject":
				entityDataProviderType = typeof(VORReportFilterDataProvider);
				break;
			case "OnDemandAuthorisationFilterDataObject":
				entityDataProviderType = typeof(OnDemandAuthorisationFilterDataProvider);
				break;
			case "WebsiteRoleDataObject":
				entityDataProviderType = typeof(WebsiteRoleDataProvider);
				break;
			case "GOUserRoleDataObject":
				entityDataProviderType = typeof(GOUserRoleDataProvider);
				break;
			case "GeneralProductivityViewDataObject":
				entityDataProviderType = typeof(GeneralProductivityViewDataProvider);
				break;
			case "InspectionDataObject":
				entityDataProviderType = typeof(InspectionDataProvider);
				break;
			case "ImpactDataObject":
				entityDataProviderType = typeof(ImpactDataProvider);
				break;
			case "PersonToDepartmentVehicleMasterAccessViewDataObject":
				entityDataProviderType = typeof(PersonToDepartmentVehicleMasterAccessViewDataProvider);
				break;
			case "CurrentStatusVehicleViewDataObject":
				entityDataProviderType = typeof(CurrentStatusVehicleViewDataProvider);
				break;
			case "CustomerModelDataObject":
				entityDataProviderType = typeof(CustomerModelDataProvider);
				break;
			case "TagDataObject":
				entityDataProviderType = typeof(TagDataProvider);
				break;
			case "VehicleBroadcastMessageDataObject":
				entityDataProviderType = typeof(VehicleBroadcastMessageDataProvider);
				break;
			case "FloorPlanDataObject":
				entityDataProviderType = typeof(FloorPlanDataProvider);
				break;
			case "ModelVehicleNormalCardAccessDataObject":
				entityDataProviderType = typeof(ModelVehicleNormalCardAccessDataProvider);
				break;
			case "SiteVehicleNormalCardAccessDataObject":
				entityDataProviderType = typeof(SiteVehicleNormalCardAccessDataProvider);
				break;
			case "ImportJobStatusDataObject":
				entityDataProviderType = typeof(ImportJobStatusDataProvider);
				break;
			case "VehicleOtherSettingsDataObject":
				entityDataProviderType = typeof(VehicleOtherSettingsDataProvider);
				break;
			case "DashboardFilterMoreFieldsDataObject":
				entityDataProviderType = typeof(DashboardFilterMoreFieldsDataProvider);
				break;
			case "VehicleSupervisorsViewDataObject":
				entityDataProviderType = typeof(VehicleSupervisorsViewDataProvider);
				break;
			case "ExportJobStatusDataObject":
				entityDataProviderType = typeof(ExportJobStatusDataProvider);
				break;
			case "TimezoneDataObject":
				entityDataProviderType = typeof(TimezoneDataProvider);
				break;
			case "HireDeHireReportFilterDataObject":
				entityDataProviderType = typeof(HireDeHireReportFilterDataProvider);
				break;
			case "PersonAllocationDataObject":
				entityDataProviderType = typeof(PersonAllocationDataProvider);
				break;
			case "BroadcastMessageDataObject":
				entityDataProviderType = typeof(BroadcastMessageDataProvider);
				break;
			case "VORReportCombinedViewDataObject":
				entityDataProviderType = typeof(VORReportCombinedViewDataProvider);
				break;
			case "ChecklistResultDataObject":
				entityDataProviderType = typeof(ChecklistResultDataProvider);
				break;
			case "VehicleAlertSubscriptionDataObject":
				entityDataProviderType = typeof(VehicleAlertSubscriptionDataProvider);
				break;
			case "PersonToSiteVehicleNormalAccessViewDataObject":
				entityDataProviderType = typeof(PersonToSiteVehicleNormalAccessViewDataProvider);
				break;
			case "SessionDataObject":
				entityDataProviderType = typeof(SessionDataProvider);
				break;
			case "SlamcoreDeviceFilterDataObject":
				entityDataProviderType = typeof(SlamcoreDeviceFilterDataProvider);
				break;
			case "DealerDataObject":
				entityDataProviderType = typeof(DealerDataProvider);
				break;
			case "GOUserDataObject":
				entityDataProviderType = typeof(GOUserDataProvider);
				break;
			case "ServiceSettingsDataObject":
				entityDataProviderType = typeof(ServiceSettingsDataProvider);
				break;
			case "UpdateFirmwareRequestDataObject":
				entityDataProviderType = typeof(UpdateFirmwareRequestDataProvider);
				break;
			case "SlamcoreAPIKeyDataObject":
				entityDataProviderType = typeof(SlamcoreAPIKeyDataProvider);
				break;
			case "FirmwareDataObject":
				entityDataProviderType = typeof(FirmwareDataProvider);
				break;
			case "CardDataObject":
				entityDataProviderType = typeof(CardDataProvider);
				break;
			case "GOUserDepartmentDataObject":
				entityDataProviderType = typeof(GOUserDepartmentDataProvider);
				break;
			case "CanruleDataObject":
				entityDataProviderType = typeof(CanruleDataProvider);
				break;
			case "SlamcoreDeviceHistoryDataObject":
				entityDataProviderType = typeof(SlamcoreDeviceHistoryDataProvider);
				break;
			case "VehicleHireDehireHistoryDataObject":
				entityDataProviderType = typeof(VehicleHireDehireHistoryDataProvider);
				break;
			case "ChecklistFailurePerVechicleViewDataObject":
				entityDataProviderType = typeof(ChecklistFailurePerVechicleViewDataProvider);
				break;
			case "ImpactReportFilterDataObject":
				entityDataProviderType = typeof(ImpactReportFilterDataProvider);
				break;
			case "ReportSubscriptionDataObject":
				entityDataProviderType = typeof(ReportSubscriptionDataProvider);
				break;
			case "VehicleLastGPSLocationViewDataObject":
				entityDataProviderType = typeof(VehicleLastGPSLocationViewDataProvider);
				break;
			case "PSTATDetailsDataObject":
				entityDataProviderType = typeof(PSTATDetailsDataProvider);
				break;
			case "LicenseExpiryReportFilterDataObject":
				entityDataProviderType = typeof(LicenseExpiryReportFilterDataProvider);
				break;
			case "FloorZonesDataObject":
				entityDataProviderType = typeof(FloorZonesDataProvider);
				break;
			case "CanruleDetailsDataObject":
				entityDataProviderType = typeof(CanruleDetailsDataProvider);
				break;
			case "VehicleHireDehireSynchronizationOptionsDataObject":
				entityDataProviderType = typeof(VehicleHireDehireSynchronizationOptionsDataProvider);
				break;
			case "DepartmentVehicleMasterCardAccessDataObject":
				entityDataProviderType = typeof(DepartmentVehicleMasterCardAccessDataProvider);
				break;
			case "ChecklistFailureViewDataObject":
				entityDataProviderType = typeof(ChecklistFailureViewDataProvider);
				break;
			case "PersonChecklistLanguageSettingsDataObject":
				entityDataProviderType = typeof(PersonChecklistLanguageSettingsDataProvider);
				break;
			case "VehicleSessionlessImpactDataObject":
				entityDataProviderType = typeof(VehicleSessionlessImpactDataProvider);
				break;
			case "LicenseByModelDataObject":
				entityDataProviderType = typeof(LicenseByModelDataProvider);
				break;
			case "IoTDeviceMessageCacheDataObject":
				entityDataProviderType = typeof(IoTDeviceMessageCacheDataProvider);
				break;
			case "ProficiencyCombinedViewDataObject":
				entityDataProviderType = typeof(ProficiencyCombinedViewDataProvider);
				break;
			case "CustomerSnapshotDataObject":
				entityDataProviderType = typeof(CustomerSnapshotDataProvider);
				break;
			case "GOGroupDataObject":
				entityDataProviderType = typeof(GOGroupDataProvider);
				break;
			case "VehiclesPerModelReportDataObject":
				entityDataProviderType = typeof(VehiclesPerModelReportDataProvider);
				break;
			case "BroadcastMessageHistoryFilterDataObject":
				entityDataProviderType = typeof(BroadcastMessageHistoryFilterDataProvider);
				break;
			case "DealerDriverDataObject":
				entityDataProviderType = typeof(DealerDriverDataProvider);
				break;
			case "EmailGroupsDataObject":
				entityDataProviderType = typeof(EmailGroupsDataProvider);
				break;
			case "PreOperationalChecklistDataObject":
				entityDataProviderType = typeof(PreOperationalChecklistDataProvider);
				break;
			case "SynchronizationStatusReportFilterDataObject":
				entityDataProviderType = typeof(SynchronizationStatusReportFilterDataProvider);
				break;
			case "SlamcoreDeviceDataObject":
				entityDataProviderType = typeof(SlamcoreDeviceDataProvider);
				break;
			case "SiteDataObject":
				entityDataProviderType = typeof(SiteDataProvider);
				break;
			case "AlertSubscriptionDataObject":
				entityDataProviderType = typeof(AlertSubscriptionDataProvider);
				break;
			case "CustomerAuditDataObject":
				entityDataProviderType = typeof(CustomerAuditDataProvider);
				break;
			case "PerVehicleNormalCardAccessDataObject":
				entityDataProviderType = typeof(PerVehicleNormalCardAccessDataProvider);
				break;
			case "PedestrianDetectionHistoryDataObject":
				entityDataProviderType = typeof(PedestrianDetectionHistoryDataProvider);
				break;
			case "PersonToPerVehicleNormalAccessViewDataObject":
				entityDataProviderType = typeof(PersonToPerVehicleNormalAccessViewDataProvider);
				break;
			case "ImportJobLogDataObject":
				entityDataProviderType = typeof(ImportJobLogDataProvider);
				break;
			case "ProficiencyReportFilterDataObject":
				entityDataProviderType = typeof(ProficiencyReportFilterDataProvider);
				break;
			case "OnDemandSessionDataObject":
				entityDataProviderType = typeof(OnDemandSessionDataProvider);
				break;
			case "VORSettingHistoryDataObject":
				entityDataProviderType = typeof(VORSettingHistoryDataProvider);
				break;
			case "CustomerSSODetailDataObject":
				entityDataProviderType = typeof(CustomerSSODetailDataProvider);
				break;
			case "UploadLogoRequestDataObject":
				entityDataProviderType = typeof(UploadLogoRequestDataProvider);
				break;
			case "AllVehicleCalibrationFilterDataObject":
				entityDataProviderType = typeof(AllVehicleCalibrationFilterDataProvider);
				break;
			case "DepartmentHourSettingsDataObject":
				entityDataProviderType = typeof(DepartmentHourSettingsDataProvider);
				break;
			case "CategoryTemplateDataObject":
				entityDataProviderType = typeof(CategoryTemplateDataProvider);
				break;
			case "DashboardVehicleCardViewDataObject":
				entityDataProviderType = typeof(DashboardVehicleCardViewDataProvider);
				break;
			case "CurrentStatusDriverViewDataObject":
				entityDataProviderType = typeof(CurrentStatusDriverViewDataProvider);
				break;
			case "ReportTypeDataObject":
				entityDataProviderType = typeof(ReportTypeDataProvider);
				break;
			case "SiteVehicleMasterCardAccessDataObject":
				entityDataProviderType = typeof(SiteVehicleMasterCardAccessDataProvider);
				break;
			case "PersonToDepartmentVehicleNormalAccessViewDataObject":
				entityDataProviderType = typeof(PersonToDepartmentVehicleNormalAccessViewDataProvider);
				break;
			case "GOUserGroupDataObject":
				entityDataProviderType = typeof(GOUserGroupDataProvider);
				break;
			case "CustomerFeatureSubscriptionDataObject":
				entityDataProviderType = typeof(CustomerFeatureSubscriptionDataProvider);
				break;
			case "VehicleToPreOpChecklistViewDataObject":
				entityDataProviderType = typeof(VehicleToPreOpChecklistViewDataProvider);
				break;
			case "PersonToSiteVehicleMasterAccessViewDataObject":
				entityDataProviderType = typeof(PersonToSiteVehicleMasterAccessViewDataProvider);
				break;
			case "DealerConfigurationDataObject":
				entityDataProviderType = typeof(DealerConfigurationDataProvider);
				break;
			case "CustomerPreOperationalChecklistTemplateDataObject":
				entityDataProviderType = typeof(CustomerPreOperationalChecklistTemplateDataProvider);
				break;
			case "CurrentDriverStatusChartViewDataObject":
				entityDataProviderType = typeof(CurrentDriverStatusChartViewDataProvider);
				break;
			case "GOChangeDeltaDataObject":
				entityDataProviderType = typeof(GOChangeDeltaDataProvider);
				break;
			case "PreOpReportFilterDataObject":
				entityDataProviderType = typeof(PreOpReportFilterDataProvider);
				break;
			case "SiteFloorPlanDataObject":
				entityDataProviderType = typeof(SiteFloorPlanDataProvider);
				break;
			case "BroadcastMessageHistoryDataObject":
				entityDataProviderType = typeof(BroadcastMessageHistoryDataProvider);
				break;
			case "GoUserToCustomerDataObject":
				entityDataProviderType = typeof(GoUserToCustomerDataProvider);
				break;
			case "ImportJobBatchDataObject":
				entityDataProviderType = typeof(ImportJobBatchDataProvider);
				break;
			case "PersonDataObject":
				entityDataProviderType = typeof(PersonDataProvider);
				break;
			case "PersonToModelVehicleNormalAccessViewDataObject":
				entityDataProviderType = typeof(PersonToModelVehicleNormalAccessViewDataProvider);
				break;
			case "NetworkSettingsDataObject":
				entityDataProviderType = typeof(NetworkSettingsDataProvider);
				break;
			case "DepartmentDataObject":
				entityDataProviderType = typeof(DepartmentDataProvider);
				break;
			case "MachineUnlockReportFilterDataObject":
				entityDataProviderType = typeof(MachineUnlockReportFilterDataProvider);
				break;
			case "PerVehicleMasterCardAccessDataObject":
				entityDataProviderType = typeof(PerVehicleMasterCardAccessDataProvider);
				break;
			case "UnitUtilisationCombinedViewDataObject":
				entityDataProviderType = typeof(UnitUtilisationCombinedViewDataProvider);
				break;
			case "FeatureSubscriptionsFilterDataObject":
				entityDataProviderType = typeof(FeatureSubscriptionsFilterDataProvider);
				break;
			case "OnDemandSettingsDataObject":
				entityDataProviderType = typeof(OnDemandSettingsDataProvider);
				break;
			}

			return entityDataProviderType;
		}
    }

	public static class DispatchPath
	{
		// Given a relation, find all subinclude paths in includes
		public static List<string> GetSubIncludes(string relation, IEnumerable<string> includes)
		{
			var result = new List<string>();

			relation = relation.ToLower();

			foreach (var include in includes)
			{
				var includeparts = include.Split('.').Select(s => s.ToLower()).ToArray();

				if (includeparts[0] == relation)
				{
					if (includeparts.Length > 1)
					{
						string subInclude = String.Join(".", includeparts.Skip(1));
						result.Add(subInclude);
					}

				}
			}

			return result;
		}
	}

	public class DatabaseEntityProvider : IEntityDataProvider
	{
		protected IServiceProvider _serviceProvider;
		
		public DatabaseEntityProvider(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
	
		public IDataProvider GetDataProviderForEntity(IDataObject entity)
		{
			switch (entity.GetType().Name)
			{
			case "ChecklistDetailDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ChecklistDetailDataObject>>();
			case "SlamcoreAwareAuthenticationDetailsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject>>();
			case "FeatureSubscriptionTemplateDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<FeatureSubscriptionTemplateDataObject>>();
			case "PersonToModelVehicleMasterAccessViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonToModelVehicleMasterAccessViewDataObject>>();
			case "VehicleLockoutDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleLockoutDataObject>>();
			case "RevisionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<RevisionDataObject>>();
			case "AlertDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AlertDataObject>>();
			case "WebsiteUserDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<WebsiteUserDataObject>>();
			case "DriverAccessAbuseFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DriverAccessAbuseFilterDataObject>>();
			case "AllLicenseExpiryViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllLicenseExpiryViewDataObject>>();
			case "DriverLicenseExpiryViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DriverLicenseExpiryViewDataObject>>();
			case "ContactPersonInformationDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ContactPersonInformationDataObject>>();
			case "ChecklistSettingsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ChecklistSettingsDataObject>>();
			case "VehicleDiagnosticDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleDiagnosticDataObject>>();
			case "GOUser2FADataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOUser2FADataObject>>();
			case "VehicleGPSDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleGPSDataObject>>();
			case "ModuleDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ModuleDataObject>>();
			case "PersonToPerVehicleMasterAccessViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonToPerVehicleMasterAccessViewDataObject>>();
			case "AccessGroupDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AccessGroupDataObject>>();
			case "HelpDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<HelpDataObject>>();
			case "ChecklistStatusViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ChecklistStatusViewDataObject>>();
			case "CurrentStatusCombinedViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CurrentStatusCombinedViewDataObject>>();
			case "CurrentVehicleStatusChartViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CurrentVehicleStatusChartViewDataObject>>();
			case "DealerFeatureSubscriptionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DealerFeatureSubscriptionDataObject>>();
			case "MessageHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<MessageHistoryDataObject>>();
			case "AccessGroupToSiteDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AccessGroupToSiteDataObject>>();
			case "AllDriverAccessAbuseStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject>>();
			case "CustomerToModelDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CustomerToModelDataObject>>();
			case "AllEmailSubscriptionStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllEmailSubscriptionStoreProcedureDataObject>>();
			case "AlertSummaryStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AlertSummaryStoreProcedureDataObject>>();
			case "DashboardVehicleCardStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DashboardVehicleCardStoreProcedureDataObject>>();
			case "RegionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<RegionDataObject>>();
			case "ModelDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ModelDataObject>>();
			case "AllVORSessionsPerVehicleStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>>();
			case "SlamcoreDeviceConnectionViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceConnectionViewDataObject>>();
			case "AllImpactsViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllImpactsViewDataObject>>();
			case "CustomerDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CustomerDataObject>>();
			case "DepartmentChecklistDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DepartmentChecklistDataObject>>();
			case "ModuleHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ModuleHistoryDataObject>>();
			case "CountryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CountryDataObject>>();
			case "AllChecklistResultViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllChecklistResultViewDataObject>>();
			case "GPSHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GPSHistoryDataObject>>();
			case "ImpactsForVehicleViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ImpactsForVehicleViewDataObject>>();
			case "AllVehicleUnlocksViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllVehicleUnlocksViewDataObject>>();
			case "SlamcorePedestrianDetectionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SlamcorePedestrianDetectionDataObject>>();
			case "ImpactFrequencyPerWeekMonthViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject>>();
			case "VehicleUtilizationLastTwelveHoursViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject>>();
			case "IOFIELDDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<IOFIELDDataObject>>();
			case "EmailDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<EmailDataObject>>();
			case "LicenceDetailDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<LicenceDetailDataObject>>();
			case "PedestrianDetectionHistoryFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PedestrianDetectionHistoryFilterDataObject>>();
			case "GOLoginHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOLoginHistoryDataObject>>();
			case "MainDashboardFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<MainDashboardFilterDataObject>>();
			case "AllVehicleCalibrationStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllVehicleCalibrationStoreProcedureDataObject>>();
			case "AccessGroupTemplateDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AccessGroupTemplateDataObject>>();
			case "CardToCardAccessDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CardToCardAccessDataObject>>();
			case "ZoneCoordinatesDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ZoneCoordinatesDataObject>>();
			case "SnapshotDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SnapshotDataObject>>();
			case "GOTaskDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOTaskDataObject>>();
			case "GeneralProductivityReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityReportFilterDataObject>>();
			case "DepartmentVehicleNormalCardAccessDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DepartmentVehicleNormalCardAccessDataObject>>();
			case "CustomerToPersonViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CustomerToPersonViewDataObject>>();
			case "VehicleSlamcoreLocationHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleSlamcoreLocationHistoryDataObject>>();
			case "PermissionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PermissionDataObject>>();
			case "SessionDetailsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SessionDetailsDataObject>>();
			case "GORoleDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GORoleDataObject>>();
			case "GeneralProductivityPerVehicleViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityPerVehicleViewDataObject>>();
			case "VehicleDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleDataObject>>();
			case "GeneralProductivityPerDriverViewLatestDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityPerDriverViewLatestDataObject>>();
			case "DriverDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DriverDataObject>>();
			case "EmailGroupsToPersonDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<EmailGroupsToPersonDataObject>>();
			case "GOGroupRoleDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOGroupRoleDataObject>>();
			case "ModelVehicleMasterCardAccessDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ModelVehicleMasterCardAccessDataObject>>();
			case "EmailSubscriptionReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<EmailSubscriptionReportFilterDataObject>>();
			case "VehicleProficiencyViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleProficiencyViewDataObject>>();
			case "DashboardDriverCardViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DashboardDriverCardViewDataObject>>();
			case "DriverProficiencyViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DriverProficiencyViewDataObject>>();
			case "GOSecurityTokensDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOSecurityTokensDataObject>>();
			case "AlertHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AlertHistoryDataObject>>();
			case "DashboardFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DashboardFilterDataObject>>();
			case "VORReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VORReportFilterDataObject>>();
			case "OnDemandAuthorisationFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<OnDemandAuthorisationFilterDataObject>>();
			case "WebsiteRoleDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<WebsiteRoleDataObject>>();
			case "GOUserRoleDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOUserRoleDataObject>>();
			case "GeneralProductivityViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityViewDataObject>>();
			case "InspectionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<InspectionDataObject>>();
			case "ImpactDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ImpactDataObject>>();
			case "PersonToDepartmentVehicleMasterAccessViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonToDepartmentVehicleMasterAccessViewDataObject>>();
			case "CurrentStatusVehicleViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CurrentStatusVehicleViewDataObject>>();
			case "CustomerModelDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CustomerModelDataObject>>();
			case "TagDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<TagDataObject>>();
			case "VehicleBroadcastMessageDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleBroadcastMessageDataObject>>();
			case "FloorPlanDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<FloorPlanDataObject>>();
			case "TodaysPreopCheckViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<TodaysPreopCheckViewDataObject>>();
			case "ModelVehicleNormalCardAccessDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ModelVehicleNormalCardAccessDataObject>>();
			case "SiteVehicleNormalCardAccessDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SiteVehicleNormalCardAccessDataObject>>();
			case "ImportJobStatusDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ImportJobStatusDataObject>>();
			case "VehicleOtherSettingsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleOtherSettingsDataObject>>();
			case "DashboardFilterMoreFieldsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DashboardFilterMoreFieldsDataObject>>();
			case "VehicleSupervisorsViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleSupervisorsViewDataObject>>();
			case "ExportJobStatusDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ExportJobStatusDataObject>>();
			case "TimezoneDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<TimezoneDataObject>>();
			case "HireDeHireReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<HireDeHireReportFilterDataObject>>();
			case "LoggedHoursVersusSeatHoursViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<LoggedHoursVersusSeatHoursViewDataObject>>();
			case "PersonAllocationDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonAllocationDataObject>>();
			case "BroadcastMessageDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<BroadcastMessageDataObject>>();
			case "VORReportCombinedViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VORReportCombinedViewDataObject>>();
			case "UnitUtilisationStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<UnitUtilisationStoreProcedureDataObject>>();
			case "ChecklistResultDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ChecklistResultDataObject>>();
			case "UnitUnutilisationStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<UnitUnutilisationStoreProcedureDataObject>>();
			case "VehicleAlertSubscriptionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleAlertSubscriptionDataObject>>();
			case "OnDemandAuthorisationStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<OnDemandAuthorisationStoreProcedureDataObject>>();
			case "ImpactFrequencyPerTimeSlotViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject>>();
			case "PersonToSiteVehicleNormalAccessViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonToSiteVehicleNormalAccessViewDataObject>>();
			case "SessionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SessionDataObject>>();
			case "SlamcoreDeviceFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceFilterDataObject>>();
			case "DealerDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DealerDataObject>>();
			case "GOUserDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOUserDataObject>>();
			case "ServiceSettingsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ServiceSettingsDataObject>>();
			case "UpdateFirmwareRequestDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<UpdateFirmwareRequestDataObject>>();
			case "SlamcoreAPIKeyDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SlamcoreAPIKeyDataObject>>();
			case "FirmwareDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<FirmwareDataObject>>();
			case "CardDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CardDataObject>>();
			case "GOUserDepartmentDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOUserDepartmentDataObject>>();
			case "CanruleDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CanruleDataObject>>();
			case "ImpactFrequencyPerWeekDayViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ImpactFrequencyPerWeekDayViewDataObject>>();
			case "SlamcoreDeviceHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceHistoryDataObject>>();
			case "VehicleHireDehireHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleHireDehireHistoryDataObject>>();
			case "ChecklistFailurePerVechicleViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ChecklistFailurePerVechicleViewDataObject>>();
			case "ImpactReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ImpactReportFilterDataObject>>();
			case "IncompletedChecklistViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<IncompletedChecklistViewDataObject>>();
			case "ReportSubscriptionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ReportSubscriptionDataObject>>();
			case "VehicleLastGPSLocationViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleLastGPSLocationViewDataObject>>();
			case "PSTATDetailsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PSTATDetailsDataObject>>();
			case "LicenseExpiryReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<LicenseExpiryReportFilterDataObject>>();
			case "FloorZonesDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<FloorZonesDataObject>>();
			case "CanruleDetailsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CanruleDetailsDataObject>>();
			case "VehicleHireDehireSynchronizationOptionsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleHireDehireSynchronizationOptionsDataObject>>();
			case "DriverLicenseExpiryStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DriverLicenseExpiryStoreProcedureDataObject>>();
			case "UnitSummaryReportDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<UnitSummaryReportDataObject>>();
			case "DepartmentVehicleMasterCardAccessDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DepartmentVehicleMasterCardAccessDataObject>>();
			case "VehicleUtilizationLastTwelveHoursStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>>();
			case "ChecklistFailureViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ChecklistFailureViewDataObject>>();
			case "PersonChecklistLanguageSettingsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonChecklistLanguageSettingsDataObject>>();
			case "VehicleSessionlessImpactDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleSessionlessImpactDataObject>>();
			case "LicenseByModelDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<LicenseByModelDataObject>>();
			case "AllMessageHistoryStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllMessageHistoryStoreProcedureDataObject>>();
			case "IoTDeviceMessageCacheDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<IoTDeviceMessageCacheDataObject>>();
			case "ProficiencyCombinedViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ProficiencyCombinedViewDataObject>>();
			case "CustomerSnapshotDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CustomerSnapshotDataObject>>();
			case "GOGroupDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOGroupDataObject>>();
			case "VehiclesPerModelReportDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehiclesPerModelReportDataObject>>();
			case "TodaysImpactStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<TodaysImpactStoreProcedureDataObject>>();
			case "BroadcastMessageHistoryFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<BroadcastMessageHistoryFilterDataObject>>();
			case "DealerDriverDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DealerDriverDataObject>>();
			case "EmailGroupsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<EmailGroupsDataObject>>();
			case "PreOperationalChecklistDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PreOperationalChecklistDataObject>>();
			case "SynchronizationStatusReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SynchronizationStatusReportFilterDataObject>>();
			case "SlamcoreDeviceDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceDataObject>>();
			case "SiteDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SiteDataObject>>();
			case "AlertSubscriptionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AlertSubscriptionDataObject>>();
			case "CustomerAuditDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CustomerAuditDataObject>>();
			case "PerVehicleNormalCardAccessDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PerVehicleNormalCardAccessDataObject>>();
			case "PedestrianDetectionHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PedestrianDetectionHistoryDataObject>>();
			case "PersonToPerVehicleNormalAccessViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonToPerVehicleNormalAccessViewDataObject>>();
			case "DashboardDriverCardStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DashboardDriverCardStoreProcedureDataObject>>();
			case "TodaysImpactViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<TodaysImpactViewDataObject>>();
			case "DetailedSessionViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DetailedSessionViewDataObject>>();
			case "ImportJobLogDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ImportJobLogDataObject>>();
			case "ProficiencyReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ProficiencyReportFilterDataObject>>();
			case "OnDemandSessionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<OnDemandSessionDataObject>>();
			case "AllVORStatusStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllVORStatusStoreProcedureDataObject>>();
			case "VORSettingHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VORSettingHistoryDataObject>>();
			case "AllUserSummaryStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllUserSummaryStoreProcedureDataObject>>();
			case "CustomerSSODetailDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CustomerSSODetailDataObject>>();
			case "UploadLogoRequestDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<UploadLogoRequestDataObject>>();
			case "AllVehicleCalibrationFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<AllVehicleCalibrationFilterDataObject>>();
			case "DepartmentHourSettingsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DepartmentHourSettingsDataObject>>();
			case "CategoryTemplateDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CategoryTemplateDataObject>>();
			case "DashboardVehicleCardViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DashboardVehicleCardViewDataObject>>();
			case "CurrentStatusDriverViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CurrentStatusDriverViewDataObject>>();
			case "UnitSummaryStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<UnitSummaryStoreProcedureDataObject>>();
			case "ReportTypeDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ReportTypeDataObject>>();
			case "SiteVehicleMasterCardAccessDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SiteVehicleMasterCardAccessDataObject>>();
			case "PersonToDepartmentVehicleNormalAccessViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonToDepartmentVehicleNormalAccessViewDataObject>>();
			case "GOUserGroupDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOUserGroupDataObject>>();
			case "CustomerFeatureSubscriptionDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CustomerFeatureSubscriptionDataObject>>();
			case "VehicleToPreOpChecklistViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<VehicleToPreOpChecklistViewDataObject>>();
			case "PersonToSiteVehicleMasterAccessViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonToSiteVehicleMasterAccessViewDataObject>>();
			case "DealerConfigurationDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DealerConfigurationDataObject>>();
			case "TodaysPreopCheckStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<TodaysPreopCheckStoreProcedureDataObject>>();
			case "CustomerPreOperationalChecklistTemplateDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CustomerPreOperationalChecklistTemplateDataObject>>();
			case "CurrentDriverStatusChartViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<CurrentDriverStatusChartViewDataObject>>();
			case "GOChangeDeltaDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GOChangeDeltaDataObject>>();
			case "GO2FAConfigurationDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GO2FAConfigurationDataObject>>();
			case "PreOpReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PreOpReportFilterDataObject>>();
			case "SiteFloorPlanDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<SiteFloorPlanDataObject>>();
			case "BroadcastMessageHistoryDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<BroadcastMessageHistoryDataObject>>();
			case "GoUserToCustomerDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<GoUserToCustomerDataObject>>();
			case "ImportJobBatchDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<ImportJobBatchDataObject>>();
			case "PersonDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonDataObject>>();
			case "DetailedVORSessionStoreProcedureDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DetailedVORSessionStoreProcedureDataObject>>();
			case "PersonToModelVehicleNormalAccessViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PersonToModelVehicleNormalAccessViewDataObject>>();
			case "NetworkSettingsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<NetworkSettingsDataObject>>();
			case "DepartmentDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<DepartmentDataObject>>();
			case "MachineUnlockReportFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<MachineUnlockReportFilterDataObject>>();
			case "PerVehicleMasterCardAccessDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<PerVehicleMasterCardAccessDataObject>>();
			case "UnitUtilisationCombinedViewDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<UnitUtilisationCombinedViewDataObject>>();
			case "FeatureSubscriptionsFilterDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<FeatureSubscriptionsFilterDataObject>>();
			case "OnDemandSettingsDataObject":
				return _serviceProvider.GetRequiredService<IDataProvider<OnDemandSettingsDataObject>>();
			default:
				break;
			}

			return null;
		}
	}
}
