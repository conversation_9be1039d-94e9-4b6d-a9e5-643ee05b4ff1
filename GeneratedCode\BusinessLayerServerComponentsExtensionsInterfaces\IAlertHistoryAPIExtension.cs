﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;

namespace FleetXQ.BusinessLayer.Components.Server.Extensions
{
	/// <summary>
	/// interface IAlertHistoryAPIExtension
	/// Derive from me to extend AlertHistoryAPI behaviour by attaching to the OnBeforeCall and OnAfterCall events.
	/// </summary>
	public interface IAlertHistoryAPIExtension
	{
		Task InitAsync(IAlertHistoryAPISurrogate component);
	}
}
