﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using FleetXQ.Data.DataObjects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Security.Claims;
using System.Threading.Tasks;

namespace FleetXQ.Feature.Security.Common
{
    public class AppUserClaims : UserClaims
    {
		public System.String? UserSubDomain { get; set; }
		public System.String? AllowedDepartmentNames { get; set; }
		public System.String? UserPreferredLocale { get; set; }
		public System.Guid DepartmentId { get; set; }
		public System.Boolean? CanViewDepartment { get; set; }
		public System.String? AccessRules { get; set; }
		public System.Boolean? CanCreateUser { get; set; }
		public System.Boolean? CanViewVehicle { get; set; }
		public List<System.Guid> AllowedCustomerId { get; set; }
		public List<System.Guid> AllowedSiteIds { get; set; }
		public System.Boolean? CanCreateSite { get; set; }
		public System.Boolean? CanEditUser { get; set; }
		public System.Boolean? CanEditDepartment { get; set; }
		public System.Boolean? CanDeleteUser { get; set; }
		public System.Int16? WAL { get; set; }
		public System.Boolean? CanViewSite { get; set; }
		public System.Boolean? CanCreateVehicle { get; set; }
		public System.Guid SiteId { get; set; }
		public System.Guid CustomerId { get; set; }
		public System.Guid UserDealerId { get; set; }
		public System.Boolean? CanViewUsers { get; set; }
		public System.Boolean? CanCreateDepartment { get; set; }
		public System.String? CustomerPreferredLocale { get; set; }
		public System.Guid? DealerId { get; set; }
		public System.Guid? DriverId { get; set; }
		public System.String? UserThemeColor { get; set; }
		public System.Boolean? CanEditVehicle { get; set; }
		public System.Boolean? CanEditSite { get; set; }
		
		/// <summary>
        /// Gets a list of Claim from the provided GOUser
        /// </summary>
        public static async Task<IEnumerable<Claim>> GetExtraUserClaimsAsync(GOUserDataObject user)
        {
			var claims = new List<Claim>();

			
			// Handling UserSubDomain claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadCustomerAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null && user.Person.Customer != null)
			{
				await user.Person.Customer.LoadDealerAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.Customer != null && user.Person.Customer.Dealer != null)
			{
				var userSubDomainString =	user.Person.Customer.Dealer.SubDomain?.ToString();	
				
				if (userSubDomainString != null)
				{
					claims.Add(new Claim("UserSubDomain", userSubDomainString));
				}
			}
					
			
			// Handling AllowedDepartmentNames claim

			if (user != null)
			{
				var allowedDepartmentNamesString =	user.AllowedDepartmentNames?.ToString();	
				
				if (allowedDepartmentNamesString != null)
				{
					claims.Add(new Claim("AllowedDepartmentNames", allowedDepartmentNamesString));
				}
			}
					
			
			// Handling UserPreferredLocale claim

			if (user != null)
			{
				var userPreferredLocaleString =	user.PreferredLocaleString?.ToString();	
				
				if (userPreferredLocaleString != null)
				{
					claims.Add(new Claim("UserPreferredLocale", userPreferredLocaleString));
				}
			}
					
			
			// Handling DepartmentId claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadDepartmentAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.Department != null)
			{
				var departmentIdString =	user.Person.Department.Id.ToString();	
				
				if (departmentIdString != null)
				{
					claims.Add(new Claim("DepartmentId", departmentIdString));
				}
			}
					
			
			// Handling CanViewDepartment claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canViewDepartmentString =	user.Person.AccessGroup.CanViewCustomerDepartment.ToString();	
				
				if (canViewDepartmentString != null)
				{
					claims.Add(new Claim("CanViewDepartment", canViewDepartmentString));
				}
			}
					
			
			// Handling AccessRules claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var accessRulesString =	user.Person.AccessGroup.AccessRules?.ToString();	
				
				if (accessRulesString != null)
				{
					claims.Add(new Claim("AccessRules", accessRulesString));
				}
			}
					
			
			// Handling CanCreateUser claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canCreateUserString =	user.Person.AccessGroup.CanCreateUser.ToString();	
				
				if (canCreateUserString != null)
				{
					claims.Add(new Claim("CanCreateUser", canCreateUserString));
				}
			}
					
			
			// Handling CanViewVehicle claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canViewVehicleString =	user.Person.AccessGroup.CanViewVehicle.ToString();	
				
				if (canViewVehicleString != null)
				{
					claims.Add(new Claim("CanViewVehicle", canViewVehicleString));
				}
			}
					
			
			// Handling AllowedCustomerId claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadGoUserToCustomerItemsAsync(skipSecurity: true);
			}

			if (user != null && user.GoUserToCustomerItems != null)
			{
				foreach(var item in user.GoUserToCustomerItems)
				{
				}				
				var allowedCustomerIdString = $"{{ {string.Join(",", user.GoUserToCustomerItems.Select(x => x.CustomerId.ToString()))} }}" ;
				
				if (allowedCustomerIdString != null)
				{
					claims.Add(new Claim("AllowedCustomerId", allowedCustomerIdString));
				}
			}
					
			
			// Handling AllowedSiteIds claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				await user.Person.AccessGroup.LoadAccessGroupsToSitesAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null && user.Person.AccessGroup.AccessGroupsToSites != null)
			{
				foreach(var item in user.Person.AccessGroup.AccessGroupsToSites)
				{
				}				
				var allowedSiteIdsString = $"{{ {string.Join(",", user.Person.AccessGroup.AccessGroupsToSites.Select(x => x.SiteId.ToString()))} }}" ;
				
				if (allowedSiteIdsString != null)
				{
					claims.Add(new Claim("AllowedSiteIds", allowedSiteIdsString));
				}
			}
					
			
			// Handling CanCreateSite claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canCreateSiteString =	user.Person.AccessGroup.CanCreateCustomerSite.ToString();	
				
				if (canCreateSiteString != null)
				{
					claims.Add(new Claim("CanCreateSite", canCreateSiteString));
				}
			}
					
			
			// Handling CanEditUser claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canEditUserString =	user.Person.AccessGroup.CanEditUser.ToString();	
				
				if (canEditUserString != null)
				{
					claims.Add(new Claim("CanEditUser", canEditUserString));
				}
			}
					
			
			// Handling CanEditDepartment claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canEditDepartmentString =	user.Person.AccessGroup.CanEditCustomerDepartment.ToString();	
				
				if (canEditDepartmentString != null)
				{
					claims.Add(new Claim("CanEditDepartment", canEditDepartmentString));
				}
			}
					
			
			// Handling CanDeleteUser claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canDeleteUserString =	user.Person.AccessGroup.CanDeleteUser.ToString();	
				
				if (canDeleteUserString != null)
				{
					claims.Add(new Claim("CanDeleteUser", canDeleteUserString));
				}
			}
					
			
			// Handling WAL claim

			if (user != null)
			{
				var wALString =	user.WebsiteAccessLevelValue?.ToString();	
				
				if (wALString != null)
				{
					claims.Add(new Claim("WAL", wALString));
				}
			}
					
			
			// Handling CanViewSite claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canViewSiteString =	user.Person.AccessGroup.CanViewCustomerSite.ToString();	
				
				if (canViewSiteString != null)
				{
					claims.Add(new Claim("CanViewSite", canViewSiteString));
				}
			}
					
			
			// Handling CanCreateVehicle claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canCreateVehicleString =	user.Person.AccessGroup.CanCreateVehicle.ToString();	
				
				if (canCreateVehicleString != null)
				{
					claims.Add(new Claim("CanCreateVehicle", canCreateVehicleString));
				}
			}
					
			
			// Handling SiteId claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadSiteAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.Site != null)
			{
				var siteIdString =	user.Person.Site.Id.ToString();	
				
				if (siteIdString != null)
				{
					claims.Add(new Claim("SiteId", siteIdString));
				}
			}
					
			
			// Handling CustomerId claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadCustomerAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.Customer != null)
			{
				var customerIdString =	user.Person.Customer.Id.ToString();	
				
				if (customerIdString != null)
				{
					claims.Add(new Claim("CustomerId", customerIdString));
				}
			}
					
			
			// Handling UserDealerId claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadCustomerAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null && user.Person.Customer != null)
			{
				await user.Person.Customer.LoadDealerAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.Customer != null && user.Person.Customer.Dealer != null)
			{
				var userDealerIdString =	user.Person.Customer.Dealer.Id.ToString();	
				
				if (userDealerIdString != null)
				{
					claims.Add(new Claim("UserDealerId", userDealerIdString));
				}
			}
					
			
			// Handling CanViewUsers claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canViewUsersString =	user.Person.AccessGroup.CanViewUsers.ToString();	
				
				if (canViewUsersString != null)
				{
					claims.Add(new Claim("CanViewUsers", canViewUsersString));
				}
			}
					
			
			// Handling CanCreateDepartment claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canCreateDepartmentString =	user.Person.AccessGroup.CanCreateCustomerDepartment.ToString();	
				
				if (canCreateDepartmentString != null)
				{
					claims.Add(new Claim("CanCreateDepartment", canCreateDepartmentString));
				}
			}
					
			
			// Handling CustomerPreferredLocale claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadCustomerAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.Customer != null)
			{
				var customerPreferredLocaleString =	user.Person.Customer.PreferredLocaleString?.ToString();	
				
				if (customerPreferredLocaleString != null)
				{
					claims.Add(new Claim("CustomerPreferredLocale", customerPreferredLocaleString));
				}
			}
					
			
			// Handling DealerId claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadDealerAsync(skipSecurity: true);
			}

			if (user != null && user.Dealer != null)
			{
				var dealerIdString =	user.Dealer.Id.ToString();	
				
				if (dealerIdString != null)
				{
					claims.Add(new Claim("DealerId", dealerIdString));
				}
			}
					
			
			// Handling DriverId claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadDriverAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.Driver != null)
			{
				var driverIdString =	user.Person.Driver.Id.ToString();	
				
				if (driverIdString != null)
				{
					claims.Add(new Claim("DriverId", driverIdString));
				}
			}
					
			
			// Handling UserThemeColor claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadCustomerAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null && user.Person.Customer != null)
			{
				await user.Person.Customer.LoadDealerAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.Customer != null && user.Person.Customer.Dealer != null)
			{
				var userThemeColorString =	user.Person.Customer.Dealer.ThemeColor?.ToString();	
				
				if (userThemeColorString != null)
				{
					claims.Add(new Claim("UserThemeColor", userThemeColorString));
				}
			}
					
			
			// Handling CanEditVehicle claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canEditVehicleString =	user.Person.AccessGroup.CanEditVehicle.ToString();	
				
				if (canEditVehicleString != null)
				{
					claims.Add(new Claim("CanEditVehicle", canEditVehicleString));
				}
			}
					
			
			// Handling CanEditSite claim
			// if we still have relation to preload
			if (user != null)
			{
				await user.LoadPersonAsync(skipSecurity: true);
			}
			// if we still have relation to preload
			if (user != null && user.Person != null)
			{
				await user.Person.LoadAccessGroupAsync(skipSecurity: true);
			}

			if (user != null && user.Person != null && user.Person.AccessGroup != null)
			{
				var canEditSiteString =	user.Person.AccessGroup.CanEditCustomerSite.ToString();	
				
				if (canEditSiteString != null)
				{
					claims.Add(new Claim("CanEditSite", canEditSiteString));
				}
			}
					
			
			return claims;
		}

		/// <summary>
        /// Tries to set the extra claims from the given principal
        /// </summary>
		public void TryParseExtraUserClaims(ClaimsPrincipal principal)
        {
			
			// Handling UserSubDomain claim		
			try 
			{
				var claim = principal.Claims.Where(c => c.Type == "UserSubDomain").SingleOrDefault();

				if (claim != null)
				{
					this.UserSubDomain = HttpUtility.UrlDecode(claim.Value.ToString());
				}
			}
			catch (Exception e)
			{
                throw new GOServerException("Parsing UserSubDomain", "The value of the claim UserSubDomain is not a valid GenerativeObjects.Data.DataObjects.EntityTextFieldDataObject", e);
            }
			
			// Handling AllowedDepartmentNames claim		
			try 
			{
				var claim = principal.Claims.Where(c => c.Type == "AllowedDepartmentNames").SingleOrDefault();

				if (claim != null)
				{
					this.AllowedDepartmentNames = HttpUtility.UrlDecode(claim.Value.ToString());
				}
			}
			catch (Exception e)
			{
                throw new GOServerException("Parsing AllowedDepartmentNames", "The value of the claim AllowedDepartmentNames is not a valid GenerativeObjects.Data.DataObjects.EntityTextFieldDataObject", e);
            }
			
			// Handling UserPreferredLocale claim		
			try 
			{
				var claim = principal.Claims.Where(c => c.Type == "UserPreferredLocale").SingleOrDefault();

				if (claim != null)
				{
					this.UserPreferredLocale = HttpUtility.UrlDecode(claim.Value.ToString());
				}
			}
			catch (Exception e)
			{
                throw new GOServerException("Parsing UserPreferredLocale", "The value of the claim UserPreferredLocale is not a valid GenerativeObjects.Data.DataObjects.EntityTextFieldDataObject", e);
            }
			
			// Handling DepartmentId claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "DepartmentId").SingleOrDefault();

				if (claim != null)
				{
					var departmentIdValue = claim?.Value.ToString();
					
					this.DepartmentId = Guid.Parse(departmentIdValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing DepartmentId", "The value of the claim DepartmentId is not a valid GenerativeObjects.Data.DataObjects.EntityGuidFieldDataObject", e);
            }
			
			// Handling CanViewDepartment claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanViewDepartment").SingleOrDefault();

				if (claim != null)
				{
					var canViewDepartmentValue = claim?.Value.ToString();
					
					this.CanViewDepartment = bool.Parse(canViewDepartmentValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanViewDepartment", "The value of the claim CanViewDepartment is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling AccessRules claim		
			try 
			{
				var claim = principal.Claims.Where(c => c.Type == "AccessRules").SingleOrDefault();

				if (claim != null)
				{
					this.AccessRules = HttpUtility.UrlDecode(claim.Value.ToString());
				}
			}
			catch (Exception e)
			{
                throw new GOServerException("Parsing AccessRules", "The value of the claim AccessRules is not a valid GenerativeObjects.Data.DataObjects.EntityTextFieldDataObject", e);
            }
			
			// Handling CanCreateUser claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanCreateUser").SingleOrDefault();

				if (claim != null)
				{
					var canCreateUserValue = claim?.Value.ToString();
					
					this.CanCreateUser = bool.Parse(canCreateUserValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanCreateUser", "The value of the claim CanCreateUser is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling CanViewVehicle claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanViewVehicle").SingleOrDefault();

				if (claim != null)
				{
					var canViewVehicleValue = claim?.Value.ToString();
					
					this.CanViewVehicle = bool.Parse(canViewVehicleValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanViewVehicle", "The value of the claim CanViewVehicle is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling AllowedCustomerId claim		
			try	
			{
				
				var claim = principal.Claims.Where(c => c.Type == "AllowedCustomerId").SingleOrDefault();

				if (claim != null)
				{
					var allowedCustomerIdValue = claim?.Value.ToString().Trim('{', '}').Trim();

					// Check if the value is an empty collection "{}" or empty after trimming
                    if (string.IsNullOrEmpty(allowedCustomerIdValue))
                    {
                        this.AllowedCustomerId = new List<Guid>();
                    }
                    else
                    {
                        this.AllowedCustomerId = allowedCustomerIdValue
                            .Split(',')
                            .Select(item => item.Trim())
                            .Select(item => Guid.Parse(item))
                            .ToList();
                    }	
										
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing AllowedCustomerId", "The value of the claim AllowedCustomerId is not a valid GenerativeObjects.Data.DataObjects.EntityRelationFieldDataObject", e);
            }
			
			// Handling AllowedSiteIds claim		
			try	
			{
				
				var claim = principal.Claims.Where(c => c.Type == "AllowedSiteIds").SingleOrDefault();

				if (claim != null)
				{
					var allowedSiteIdsValue = claim?.Value.ToString().Trim('{', '}').Trim();

					// Check if the value is an empty collection "{}" or empty after trimming
                    if (string.IsNullOrEmpty(allowedSiteIdsValue))
                    {
                        this.AllowedSiteIds = new List<Guid>();
                    }
                    else
                    {
                        this.AllowedSiteIds = allowedSiteIdsValue
                            .Split(',')
                            .Select(item => item.Trim())
                            .Select(item => Guid.Parse(item))
                            .ToList();
                    }	
										
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing AllowedSiteIds", "The value of the claim AllowedSiteIds is not a valid GenerativeObjects.Data.DataObjects.EntityRelationFieldDataObject", e);
            }
			
			// Handling CanCreateSite claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanCreateSite").SingleOrDefault();

				if (claim != null)
				{
					var canCreateSiteValue = claim?.Value.ToString();
					
					this.CanCreateSite = bool.Parse(canCreateSiteValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanCreateSite", "The value of the claim CanCreateSite is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling CanEditUser claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanEditUser").SingleOrDefault();

				if (claim != null)
				{
					var canEditUserValue = claim?.Value.ToString();
					
					this.CanEditUser = bool.Parse(canEditUserValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanEditUser", "The value of the claim CanEditUser is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling CanEditDepartment claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanEditDepartment").SingleOrDefault();

				if (claim != null)
				{
					var canEditDepartmentValue = claim?.Value.ToString();
					
					this.CanEditDepartment = bool.Parse(canEditDepartmentValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanEditDepartment", "The value of the claim CanEditDepartment is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling CanDeleteUser claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanDeleteUser").SingleOrDefault();

				if (claim != null)
				{
					var canDeleteUserValue = claim?.Value.ToString();
					
					this.CanDeleteUser = bool.Parse(canDeleteUserValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanDeleteUser", "The value of the claim CanDeleteUser is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling WAL claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "WAL").SingleOrDefault();

				if (claim != null)
				{
					var wALValue = claim?.Value.ToString();
					
					this.WAL = Int16.Parse(wALValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing WAL", "The value of the claim WAL is not a valid GenerativeObjects.Data.DataObjects.EntityNumberFieldDataObject", e);
            }
			
			// Handling CanViewSite claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanViewSite").SingleOrDefault();

				if (claim != null)
				{
					var canViewSiteValue = claim?.Value.ToString();
					
					this.CanViewSite = bool.Parse(canViewSiteValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanViewSite", "The value of the claim CanViewSite is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling CanCreateVehicle claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanCreateVehicle").SingleOrDefault();

				if (claim != null)
				{
					var canCreateVehicleValue = claim?.Value.ToString();
					
					this.CanCreateVehicle = bool.Parse(canCreateVehicleValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanCreateVehicle", "The value of the claim CanCreateVehicle is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling SiteId claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "SiteId").SingleOrDefault();

				if (claim != null)
				{
					var siteIdValue = claim?.Value.ToString();
					
					this.SiteId = Guid.Parse(siteIdValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing SiteId", "The value of the claim SiteId is not a valid GenerativeObjects.Data.DataObjects.EntityGuidFieldDataObject", e);
            }
			
			// Handling CustomerId claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CustomerId").SingleOrDefault();

				if (claim != null)
				{
					var customerIdValue = claim?.Value.ToString();
					
					this.CustomerId = Guid.Parse(customerIdValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CustomerId", "The value of the claim CustomerId is not a valid GenerativeObjects.Data.DataObjects.EntityGuidFieldDataObject", e);
            }
			
			// Handling UserDealerId claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "UserDealerId").SingleOrDefault();

				if (claim != null)
				{
					var userDealerIdValue = claim?.Value.ToString();
					
					this.UserDealerId = Guid.Parse(userDealerIdValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing UserDealerId", "The value of the claim UserDealerId is not a valid GenerativeObjects.Data.DataObjects.EntityGuidFieldDataObject", e);
            }
			
			// Handling CanViewUsers claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanViewUsers").SingleOrDefault();

				if (claim != null)
				{
					var canViewUsersValue = claim?.Value.ToString();
					
					this.CanViewUsers = bool.Parse(canViewUsersValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanViewUsers", "The value of the claim CanViewUsers is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling CanCreateDepartment claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanCreateDepartment").SingleOrDefault();

				if (claim != null)
				{
					var canCreateDepartmentValue = claim?.Value.ToString();
					
					this.CanCreateDepartment = bool.Parse(canCreateDepartmentValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanCreateDepartment", "The value of the claim CanCreateDepartment is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling CustomerPreferredLocale claim		
			try 
			{
				var claim = principal.Claims.Where(c => c.Type == "CustomerPreferredLocale").SingleOrDefault();

				if (claim != null)
				{
					this.CustomerPreferredLocale = HttpUtility.UrlDecode(claim.Value.ToString());
				}
			}
			catch (Exception e)
			{
                throw new GOServerException("Parsing CustomerPreferredLocale", "The value of the claim CustomerPreferredLocale is not a valid GenerativeObjects.Data.DataObjects.EntityTextFieldDataObject", e);
            }
			
			// Handling DealerId claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "DealerId").SingleOrDefault();

				if (claim != null)
				{
					var dealerIdValue = claim?.Value.ToString();
					
					this.DealerId = Guid.Parse(dealerIdValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing DealerId", "The value of the claim DealerId is not a valid GenerativeObjects.Data.DataObjects.EntityGuidFieldDataObject", e);
            }
			
			// Handling DriverId claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "DriverId").SingleOrDefault();

				if (claim != null)
				{
					var driverIdValue = claim?.Value.ToString();
					
					this.DriverId = Guid.Parse(driverIdValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing DriverId", "The value of the claim DriverId is not a valid GenerativeObjects.Data.DataObjects.EntityGuidFieldDataObject", e);
            }
			
			// Handling UserThemeColor claim		
			try 
			{
				var claim = principal.Claims.Where(c => c.Type == "UserThemeColor").SingleOrDefault();

				if (claim != null)
				{
					this.UserThemeColor = HttpUtility.UrlDecode(claim.Value.ToString());
				}
			}
			catch (Exception e)
			{
                throw new GOServerException("Parsing UserThemeColor", "The value of the claim UserThemeColor is not a valid GenerativeObjects.Data.DataObjects.EntityTextFieldDataObject", e);
            }
			
			// Handling CanEditVehicle claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanEditVehicle").SingleOrDefault();

				if (claim != null)
				{
					var canEditVehicleValue = claim?.Value.ToString();
					
					this.CanEditVehicle = bool.Parse(canEditVehicleValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanEditVehicle", "The value of the claim CanEditVehicle is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
			
			// Handling CanEditSite claim		
			try	
			{
				var claim = principal.Claims.Where(c => c.Type == "CanEditSite").SingleOrDefault();

				if (claim != null)
				{
					var canEditSiteValue = claim?.Value.ToString();
					
					this.CanEditSite = bool.Parse(canEditSiteValue);
				}
			}
			catch(Exception e)
			{
                throw new GOServerException("Parsing CanEditSite", "The value of the claim CanEditSite is not a valid GenerativeObjects.Data.DataObjects.EntityBooleanFieldDataObject", e);
            }
		}		
    }
}
 