﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

using GenerativeObjects.Practices;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Storage.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer.Extensions;
using Microsoft.AspNetCore.Antiforgery;

using FleetXQ.Data.DataObjects;

namespace FleetXQ.ServiceLayer.EntityApiControllers
{
    [ApiController]
    [ServiceFilter(typeof(ObjectGraphOrDataSetSerializationFilterAttribute))]
    public class VehicleLockoutApiController : EntityApiController<VehicleLockoutDataObject, VehicleLockoutContainer, VehicleLockoutCollectionContainer>
    {
        public VehicleLockoutApiController(IServiceProvider serviceProvider, IConfiguration configuration, ISettingsProvider settingsProvider, IDataProviderTransaction dataProviderTransaction, IDataProvider<VehicleLockoutDataObject> dataProvider, IDataObjectFactory<VehicleLockoutDataObject> dataObjectFactory, IEntityApiExtensionProvider<VehicleLockoutDataObject> extensions, IEntityDataProvider entityDataProvider, IApiFilterArgumentBuilder apiFilterArgumentBuilder, IAntiforgery antiforgery) : base(serviceProvider, configuration, settingsProvider, dataProviderTransaction, dataProvider, dataObjectFactory, extensions, entityDataProvider, apiFilterArgumentBuilder, antiforgery) { }

        /// <summary>
        /// Get a VehicleLockout instance by primary key and return data in dataset format
        /// </summary>
      /// <param name="id">Id of the VehicleLockout entity to get</param>
      /// <param name="include">list of related data to fetch with the VehicleLockout, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for VehicleLockout are : Session, AllVehicleUnlocksViewItems, GOUser, Driver, Vehicle </param>
        /// <returns>VehicleLockout entity</returns>
        [HttpGet]
        [Route("dataset/api/vehiclelockout/byid/{id}")]
        public async Task<ActionResult<VehicleLockoutContainer>> GetDataSetById(System.Guid id, string include)
        {
            return await DoGetDataSetByIdAsync(new List<string> { id.ToString() }, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Get a VehicleLockout instance by primary key and return data in json structured format
        /// </summary>
      /// <param name="id">Id of the VehicleLockout entity to get</param>
      /// <param name="include">list of related data to fetch with the VehicleLockout, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for VehicleLockout are : Session, AllVehicleUnlocksViewItems, GOUser, Driver, Vehicle </param>
        /// <param name="byRef">Setting to define how to handle reference loop. If byRef is true, reference loops are handled by serializing the first occurrence of the object and replacing subsequent occurrences with a reference. If ByRef is false, reference loops are ignored. Default is false when byRef is not specified</param>
        /// <returns>VehicleLockout entity</returns>
        [HttpGet]
        [Route("api/vehiclelockout/byid/{id}")]
        public async Task<ActionResult<VehicleLockoutDataObject>> GetById(System.Guid id, string include, bool? byRef)
        {
            return await DoGetByIdAsync(new List<string> { id.ToString() }, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Get a list of VehicleLockout instances and return data in dataset format
        /// </summary>
        /// <param name="include">list of related data to fetch with the VehicleLockout, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for VehicleLockout are : Session, AllVehicleUnlocksViewItems, GOUser, Driver, Vehicle </param>
        /// <param name="filter">The filter to apply to the Query. Has to be an expression in C# format and can be referencing VehicleLockout entity properties. Can reference parameters with the format @X , X being the parameter number, starting at 0.</param>
        /// <param name="filterParameters">Filter parameters (TODO : document parameters format).</param>
        /// <param name="pageNumber">To apply pagination to the query. Page number to get. No pagination applied is not set.</param>
        /// <param name="pageSize">Size of the page to return.</param>
        /// <param name="sortOrder">Sort order for the query, desc for descending order, asc for ascending order.</param>
        /// <param name="sortColumn">VehicleLockout entity property to use to sort.</param>
        /// <returns>List of VehicleLockout entities</returns>
        [HttpGet]
        [Route("dataset/api/vehiclelockout/list")]
        public async Task<ActionResult<VehicleLockoutCollectionContainer>> GetDataSetCollection(string include, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn)
        {
            //List<string> includes, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn
            return await DoGetDataSetCollectionAsync(include?.Split(',').Select(i => i.Trim()).ToList(), filter, filterParameters, pageNumber, pageSize, sortOrder, sortColumn);
        }

        /// <summary>
        /// Get a list of VehicleLockout instances and return data in json structured format 
        /// </summary>
        /// <param name="include">list of related data to fetch with the VehicleLockout, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for VehicleLockout are : Session, AllVehicleUnlocksViewItems, GOUser, Driver, Vehicle </param>
        /// <param name="filter">The filter to apply to the Query. Has to be an expression in C# format and can be referencing VehicleLockout entity properties. Can reference parameters with the format @X , X being the parameter number, starting at 0.</param>
        /// <param name="filterParameters">Filter parameters (TODO : document parameters format).</param>
        /// <param name="pageNumber">To apply pagination to the query. Page number to get. No pagination applied is not set.</param>
        /// <param name="pageSize">Size of the page to return.</param>
        /// <param name="sortOrder">Sort order for the query, desc for descending order, asc for ascending order.</param>
        /// <param name="sortProperty">VehicleLockout entity property to use to sort.</param>
        /// <param name="byRef">Setting to define how to handle reference loop. If byRef is true, reference loops are handled by serializing the first occurrence of the object and replacing subsequent occurrences with a reference. If ByRef is false, reference loops are ignored. Default is false when byRef is not specified</param>
        /// <returns>List of VehicleLockout entities</returns>
        [HttpGet]
        [Route("api/vehiclelockout/list")]
        public async Task<ActionResult<DataObjectCollection<VehicleLockoutDataObject>>> GetCollection(string include, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn, bool? byRef)
        {
            //List<string> includes, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn
            return await DoGetCollectionAsync(include?.Split(',').Select(i => i.Trim()).ToList(), filter, filterParameters, pageNumber, pageSize, sortOrder, sortColumn);
        }

        /// <summary>
        /// Count the number of VehicleLockout instances
        /// </summary>
        /// <param name="filter">The filter to apply to the Query. Has to be an expression in C# format and can be referencing VehicleLockout entity properties. Can reference parameters with the format @X , X being the parameter number, starting at 0.</param>
        /// <param name="filterParameters">Filter parameters (TODO : document parameters format).</param>
        [HttpGet]
        [Route("dataset/api/vehiclelockout/count")]
        public async Task<ActionResult<int>> Count(string filter, string filterParameters)
        {
            return await DoCountAsync(filter, filterParameters);
        }

        /// <summary>
        /// Save (create or update if existing) a given VehicleLockout instance, and return the saved instance data
        /// </summary>
        /// <param name="entity">VehicleLockout instance to be saved in json dataset format</param>
        /// <param name="include">list of related data to fetch with the VehicleLockout saved data, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for VehicleLockout are : Session, AllVehicleUnlocksViewItems, GOUser, Driver, Vehicle </param>
        /// <returns>The saved VehicleLockout, after refetch, in json dataset format</returns>
        [HttpPost]
        [Route("dataset/api/vehiclelockout")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<ActionResult<VehicleLockoutContainer>> PostDataSet([FromForm] string entity, [FromForm] string include)
        {
            return await DoPostDataSetAsync(entity, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Save (create or update if existing) a given VehicleLockout instance, and return the saved instance data
        /// </summary>
        /// <param name="entity">VehicleLockout instance to be saved in json structured format</param>
        /// <param name="include">list of related data to fetch with the VehicleLockout saved data, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for VehicleLockout are : Session, AllVehicleUnlocksViewItems, GOUser, Driver, Vehicle </param>
        /// <param name="byRef">Setting to define how to handle reference loop. If byRef is true, reference loops are handled by serializing the first occurrence of the object and replacing subsequent occurrences with a reference. If ByRef is false, reference loops are ignored. Default is false when byRef is not specified</param>
        /// <returns>The saved VehicleLockout, after refetch, in json structured format</returns>
        [HttpPost]
        [Route("api/vehiclelockout")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<ActionResult<VehicleLockoutDataObject>> Post([FromForm] string entity, [FromForm] string include, bool? byRef)
        {
            return await DoPostAsync(entity, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Delete a given VehicleLockout instance
        /// </summary>
        /// <param name="vehicleLockout">VehicleLockout instance to be deleted in json structured format. Only the primary key information is taken into account, rest ignored</param>
        /// <param name="dry">Dry delete if true, full delete otherwise</param>
        /// <returns>Delete information if dry delete</returns>
        [HttpDelete]
        [Route("dataset/api/vehiclelockout")]
        public async Task<ActionResult<string>> Delete(VehicleLockoutDataObject vehicleLockout, bool dry = false)
        {
            return await DoDeleteAsync(vehicleLockout, dry);
        }

        /// <summary>
        /// Delete a given VehicleLockout instance
        /// </summary>
        /// <param name="vehicleLockout">VehicleLockout instance to be deleted in json structured format. Only the primary key information is taken into account, rest ignored</param>
      /// <param name="id">Id of the VehicleLockout entity to delete</param>
      /// <param name="dry">Dry delete if true, full delete otherwise</param>
        /// <returns>Delete information if dry delete</returns>
        [HttpDelete]
        [Route("dataset/api/vehiclelockout/{id}")]
        public async Task<ActionResult<string>> Delete(System.Guid id, bool dry = false)
        {
            return await DoDeleteAsync(new List<string> { id.ToString() }, dry);
        }
    }
}
 