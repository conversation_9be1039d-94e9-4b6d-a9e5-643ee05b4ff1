﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMCustomerFeatureSubscription" 
		table="[CustomerFeatureSubscription]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="CanAccessSlamcore" >
            <column name="`CanAccessSlamcore`" sql-type="bit" not-null="true" />
        </property> 
		<property name="Description" >
            <column name="`Description`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="HasAdditionalHardwaresAccess" >
            <column name="`HasAdditionalHardwaresAccess`" sql-type="bit" not-null="true" />
        </property> 
		<property name="IsEnabled" >
            <column name="`IsEnabled`" sql-type="bit" not-null="true" />
        </property> 
		<property name="IsTagged" >
            <column name="`IsTagged`" sql-type="bit" not-null="true" />
        </property> 
		<property name="Name" >
            <column name="`Name`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 

 

		<one-to-one 
			name="Customer" 
			class="ORMCustomer" 
			lazy="no-proxy"
			property-ref = "CustomerFeatureSubscription"
		>
		</one-to-one>



    </class> 

</hibernate-mapping>