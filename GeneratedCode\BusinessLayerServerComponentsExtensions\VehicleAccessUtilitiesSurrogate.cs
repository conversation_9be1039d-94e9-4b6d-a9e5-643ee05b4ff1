﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Http;

using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Web;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.HostedServices;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
 

using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using Parameters = System.Collections.Generic.Dictionary<string, object>;

namespace FleetXQ.BusinessLayer.Components.Server.Extensions
{
	public partial class VehicleAccessUtilitiesSurrogate : IVehicleAccessUtilitiesSurrogate
	{
		private readonly IServiceProvider _serviceProvider;

		private readonly IHttpContextAccessor _httpContextAccessor;
		private readonly IVehicleAccessUtilities _vehicleAccessUtilities;
		private readonly IThreadContext _threadContext;
		private readonly IServicePath _servicePath;
		private readonly IUserIdentity _userIdentity;
	
		private readonly IEnumerable<IVehicleAccessUtilitiesExtension> _extensionServices;

		public VehicleAccessUtilitiesSurrogate(IServiceProvider provider, IHttpContextAccessor httpContextAccessor, IVehicleAccessUtilities vehicleAccessUtilities, IThreadContext threadContext, IServicePath servicePath, IUserIdentity userIdentity, IEnumerable<IVehicleAccessUtilitiesExtension> extensionServices)
		{
  			_serviceProvider = provider;
			_httpContextAccessor = httpContextAccessor;
			_vehicleAccessUtilities = vehicleAccessUtilities;
			_threadContext = threadContext;
			_servicePath = servicePath;
			_userIdentity = userIdentity;
			_extensionServices = extensionServices;
	
		}

		public VehicleAccessUtilities ComponentClass { get { return _vehicleAccessUtilities as VehicleAccessUtilities; } }

		/// <summary>
		/// The extensions for implementors of IVehicleAccessUtilitiesExtension to attach to
		/// </summary>
		public event Func<ComponentExtensionEventArgs, Task> OnBeforeCall;
		public event Func<ComponentExtensionEventArgs, Task> OnAfterCall;

		/// <summary>
		/// Thread-synchronisation gubbins
		/// </summary>
		private bool _extensionsInitialised;
		private Object _lock = new Object();
			
		private SemaphoreSlim _initializationSemaphore = new SemaphoreSlim(1, 1);

		/// <summary>
		/// InitializeExtensionsAsync()
		/// Find and initialise registered extensions of this interface
		/// Done in a thread-safe manner to allow for possibility that component may have been registered as singleton (controlled lifetime)
		/// </summary>
		private async Task InitializeExtensionsAsync()
		{
			await _initializationSemaphore.WaitAsync();
			try
			{
				// Ensure component extensions have been initialised
				if (_extensionsInitialised)
					return;

				if (_extensionsInitialised)
					return;
					
				foreach (var registration in _extensionServices)
				{
					await registration.InitAsync(this);
				}

				_extensionsInitialised = true;
			}
			finally
			{
				_initializationSemaphore.Release();
			}
		}

		/// <summary>
		/// Surrogate implementation of the CopyUserVehicleAccess operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> CopyUserVehicleAccessAsync(System.Guid personId, System.Guid[] driverIds, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "CopyUserVehicleAccess" });
			}
			var result = await ComponentClass.CopyUserVehicleAccessAsync(personId, driverIds, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "CopyUserVehicleAccess" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the CreateOnDemandAccesses operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>> CreateOnDemandAccessesAsync(System.Guid[] cardIds, System.Guid vehicleId, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "CreateOnDemandAccesses" });
			}
			var result = await ComponentClass.CreateOnDemandAccessesAsync(cardIds, vehicleId, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "CreateOnDemandAccesses" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the DeleteOnDemandAccess operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> DeleteOnDemandAccessAsync(System.Guid accessId, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "DeleteOnDemandAccess" });
			}
			var result = await ComponentClass.DeleteOnDemandAccessAsync(accessId, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "DeleteOnDemandAccess" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the GetAccessesForDepartments operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>> GetAccessesForDepartmentsAsync(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses, System.Guid personId, System.Int32 permissionLevel, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "GetAccessesForDepartments" });
			}
			var result = await ComponentClass.GetAccessesForDepartmentsAsync(personToSiteAccesses, personId, permissionLevel, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "GetAccessesForDepartments" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the GetAccessesForModels operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>> GetAccessesForModelsAsync(DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, System.Guid personId, System.Int32 permissionLevel, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "GetAccessesForModels" });
			}
			var result = await ComponentClass.GetAccessesForModelsAsync(personToDepartmentAccesses, personId, permissionLevel, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "GetAccessesForModels" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the GetAccessesForVehicles operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>> GetAccessesForVehiclesAsync(DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses, System.Guid personId, System.Int32 permissionLevel, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "GetAccessesForVehicles" });
			}
			var result = await ComponentClass.GetAccessesForVehiclesAsync(personToDepartmentAccesses, personToModelAccesses, personId, permissionLevel, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "GetAccessesForVehicles" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the UpdateAccessesForPerson operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateAccessesForPersonAsync(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses, DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses, DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses, System.Guid personId, System.Int32 PermissionLevel, System.Boolean cascadeAddPermission, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "UpdateAccessesForPerson" });
			}
			var result = await ComponentClass.UpdateAccessesForPersonAsync(personToSiteAccesses, personToDepartmentAccesses, personToModelAccesses, personToVehicleAccesses, personId, PermissionLevel, cascadeAddPermission, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "UpdateAccessesForPerson" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the UpdateVehicleDepartmentAccessesForPerson operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateVehicleDepartmentAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updatedPersonToDepartmentAccesses, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "UpdateVehicleDepartmentAccessesForPerson" });
			}
			var result = await ComponentClass.UpdateVehicleDepartmentAccessesForPersonAsync(personId, updatedPersonToDepartmentAccesses, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "UpdateVehicleDepartmentAccessesForPerson" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the UpdateVehicleModelAccessesForPerson operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateVehicleModelAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updateModelAccesses, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "UpdateVehicleModelAccessesForPerson" });
			}
			var result = await ComponentClass.UpdateVehicleModelAccessesForPersonAsync(personId, updateModelAccesses, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "UpdateVehicleModelAccessesForPerson" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the UpdateVehiclePerVehicleAccessesForPerson operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateVehiclePerVehicleAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updatePerVehicleAccesses, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "UpdateVehiclePerVehicleAccessesForPerson" });
			}
			var result = await ComponentClass.UpdateVehiclePerVehicleAccessesForPersonAsync(personId, updatePerVehicleAccesses, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "UpdateVehiclePerVehicleAccessesForPerson" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the UpdateVehicleSiteAccessesForPerson operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateVehicleSiteAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updatedPersonToSiteAccesses, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "UpdateVehicleSiteAccessesForPerson" });
			}
			var result = await ComponentClass.UpdateVehicleSiteAccessesForPersonAsync(personId, updatedPersonToSiteAccesses, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "UpdateVehicleSiteAccessesForPerson" });
			}

			return result;
		}

	}
}
