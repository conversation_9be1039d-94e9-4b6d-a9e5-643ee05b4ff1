﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
//////////////////////////////////////////////////////////////////////////////////////////// 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using FleetXQ.BusinessLayer.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// AlertHistoryAPI Component
	///  
	/// </summary>
	public partial interface IAlertHistoryAPI 
    {
		/// <summary>
        /// Acknowledge Method
		///  
		/// </summary>
		/// <param name="alertHistoryId"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> AcknowledgeAsync(System.Guid alertHistoryId, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// Resolve Method
		///  
		/// </summary>
		/// <param name="alertHistoryId"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> ResolveAsync(System.Guid alertHistoryId, Dictionary<string, object> parameters = null);		

		/// <summary>
		/// Custom code can get IAlertHistoryAPI instances from the DI Container
		/// But resolved instances from the container are actually surrogate (proxy) implementation which perform security checks before calling the underlying component class 'raw' implementation.
		/// But sometimes in custom code it is convenient to be able to access the raw component (to call public methods that are not published as part of the web-facing API, for example)
		/// So that's what this ComponentClass getter does i.e. 
		/// Given an IAlertHistoryAPI  interface instance, IAlertHistoryAPI.ComponentClass gives you the underlying implementation class.
		/// </summary>
		object ComponentClass { get; }
	}
}
