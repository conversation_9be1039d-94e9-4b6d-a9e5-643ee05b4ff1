using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Configuration;
using FleetXQ.Tools.BulkImporter.Logging;
using FleetXQ.Tools.BulkImporter.Services;
using Serilog;

namespace FleetXQ.Tools.BulkImporter;

/// <summary>
/// FleetXQ Bulk Import Console Application
/// Performs efficient bulk insertion of driver data, vehicle details, and related entities into the FleetXQ database.
/// </summary>
public class Program
{
    public static async Task<int> Main(string[] args)
    {
        // Ensure log directories exist before configuring logging
        LoggingExtensions.EnsureLogDirectoriesExist();

        // Create initial logger for startup
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .CreateBootstrapLogger();

        try
        {
            Log.Information("Starting FleetXQ Bulk Importer");

            // Build and run the host
            var host = CreateHostBuilder(args).Build();

            // Run the application
            var exitCode = await RunApplicationAsync(host, args);

            Log.Information("FleetXQ Bulk Importer completed with exit code {ExitCode}", exitCode);
            return exitCode;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "FleetXQ Bulk Importer terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// Creates the host builder with configuration and services
    /// </summary>
    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                var environment = context.HostingEnvironment.EnvironmentName;

                config
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{environment}.json", optional: true, reloadOnChange: true)
                    .AddEnvironmentVariables("FLEETXQ_BULKIMPORTER_")
                    .AddCommandLine(args);
            })
            .ConfigureBulkImporterLogging()
            .ConfigureServices((context, services) =>
            {
                // Add configuration options
                services.AddBulkImporterConfiguration(context.Configuration);

                // Add application services
                services.AddSingleton<IBulkImportService, BulkImportService>();
                services.AddSingleton<ICommandLineService, CommandLineService>();
                services.AddSingleton<IInteractiveService, InteractiveService>();
                services.AddSingleton<ISqlDataGenerationService, SqlDataGenerationService>();

                // Add hosted service
                services.AddHostedService<BulkImportHostedService>();
            });

    /// <summary>
    /// Runs the application with proper error handling and correlation context
    /// </summary>
    private static async Task<int> RunApplicationAsync(IHost host, string[] args)
    {
        try
        {
            // Set up correlation context for the entire operation
            CorrelationContext.EnsureCorrelationId();
            CorrelationContext.CreateOperationId("BULK_IMPORT");

            // Validate configuration
            await ValidateConfigurationAsync(host.Services);

            // Run the application
            await host.RunAsync();

            return 0;
        }
        catch (OptionsValidationException ex)
        {
            var logger = host.Services.GetService<ILogger<Program>>();
            logger?.LogError(ex, "Configuration validation failed: {ValidationErrors}",
                string.Join(", ", ex.Failures));
            return 2;
        }
        catch (Exception ex)
        {
            var logger = host.Services.GetService<ILogger<Program>>();
            logger?.LogError(ex, "Application failed with unhandled exception");
            return 1;
        }
        finally
        {
            CorrelationContext.Clear();
        }
    }

    /// <summary>
    /// Validates application configuration and dependencies
    /// </summary>
    private static async Task ValidateConfigurationAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("Validating configuration...");

        // Validate configuration options
        var bulkImporterOptions = serviceProvider.GetRequiredService<IOptions<BulkImporterOptions>>().Value;
        var dataGenerationOptions = serviceProvider.GetRequiredService<IOptions<DataGenerationOptions>>().Value;
        var connectionOptions = serviceProvider.GetRequiredService<IOptions<ConnectionStringOptions>>().Value;

        // Log configuration summary (without sensitive data)
        logger.LogInformation("Configuration validated successfully");
        logger.LogInformation("Default batch size: {BatchSize}", bulkImporterOptions.DefaultBatchSize);
        logger.LogInformation("Default drivers count: {DriversCount}", bulkImporterOptions.DefaultDriversCount);
        logger.LogInformation("Default vehicles count: {VehiclesCount}", bulkImporterOptions.DefaultVehiclesCount);
        logger.LogInformation("Output directory: {OutputDirectory}", dataGenerationOptions.OutputDirectory);
        logger.LogInformation("Validation enabled: {ValidationEnabled}", bulkImporterOptions.ValidationEnabled);
        logger.LogInformation("Synthetic data generation enabled: {SyntheticDataEnabled}", dataGenerationOptions.EnableSyntheticDataGeneration);

        await Task.CompletedTask;
    }
}