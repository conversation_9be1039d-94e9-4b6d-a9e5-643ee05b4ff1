﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.Client.Model.Components
{
    public interface IAlertHistoryAPIService
    {
        /// <summary>
        /// ResolveAsync component operation
        /// 
        /// </summary>
        /// <param name="alertHistoryId"></param>
        /// <returns>bool</returns>
        Task<System.Boolean> ResolveAsync(System.Guid alertHistoryId);
        /// <summary>
        /// AcknowledgeAsync component operation
        /// 
        /// </summary>
        /// <param name="alertHistoryId"></param>
        /// <returns>bool</returns>
        Task<System.Boolean> AcknowledgeAsync(System.Guid alertHistoryId);
    }
}
