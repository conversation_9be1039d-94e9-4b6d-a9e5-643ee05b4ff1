﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;

 

namespace FleetXQ.Data.DataObjects.Factories
{
    public class SlamcoreAPIKeyFactory : IDataObjectFactory<SlamcoreAPIKeyDataObject> 
    {
		protected IServiceProvider _serviceProvider;
		
		public SlamcoreAPIKeyFactory(IServiceProvider provider)
		{
      		_serviceProvider = provider;
		}

        public object CreateDataSetContainer(SlamcoreAPIKeyDataObject entity)
        {
            var result = _serviceProvider.GetRequiredService<SlamcoreAPIKeyContainer>();
            result.Construct(entity, false);
            return result;
        }

        public object CreateDataSetContainer(DataObjectCollection<SlamcoreAPIKeyDataObject> entityCollection)
        {
            var result = _serviceProvider.GetRequiredService<SlamcoreAPIKeyCollectionContainer>();
            result.Construct(entityCollection);
            return result;
        }

        public SlamcoreAPIKeyDataObject CreateDataObject()
        {
            return _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>();
        }
    
        public SlamcoreAPIKeyDataObject CreateDataObject(IEnumerable<string> pks)
        {
            var pksAsArray = pks.ToArray();

            if (pksAsArray.Length != 1)
                throw new ApplicationException("CreateObject - SlamcoreAPIKey - Wrong number of PKs");

			System.Guid id;

			try 
			{
				id = Guid.Parse(pksAsArray[0]);           
			}
			catch(Exception)
			{
                throw new ApplicationException("Wrong pk type for SlamcoreAPIKey.Id - should be System.Guid");
			}

                         
            var result = _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>();
          result.Id = id;
            return result;
        }

        public SlamcoreAPIKeyDataObject DeserializeFromContainer(string jsonstring, JsonSerializerSettings settings)
        {
            if (string.IsNullOrEmpty(jsonstring) || jsonstring == "null")
            {
                return null;
            }

            var container = _serviceProvider.GetRequiredService<SlamcoreAPIKeyContainer>(); 
            JsonConvert.PopulateObject(jsonstring, container, settings);

			if(container == null)
			 return null;

			var result = container.ExtractSlamcoreAPIKey();

			// result can legitimately be null, because e.g. the dataset may contain an instance related to the main/primary entity, but not the main/primary entity itself
			// In this case we create an 'empty' instance of the main/primary entity and attach + reconstruct the dataset
            if (result == null)
            {
                result = _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>();
			    result.Id = container.PrimaryKey;
              result.IsNew = false;
                result.IsDirty = false;
                result.ObjectsDataSet = container.ObjectsDataSet;

				// Sync the dataset 
				result.ObjectsDataSet.EnsureInitialized();
				result.ObjectsDataSet.ReconstructIndexes();
            }

			result.OnDeserialized();

			return result;
		}

		public SlamcoreAPIKeyDataObject DeserializeObject(string jsonstring, JsonSerializerSettings settings)
        {	
			var result = JsonConvert.DeserializeObject<SlamcoreAPIKeyDataObject>(jsonstring, settings);

			if(result == null)
			 return null;

			result.OnDeserialized();
			return result;
		}
	}
}