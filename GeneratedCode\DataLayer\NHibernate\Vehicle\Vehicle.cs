﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge ORMEntity -> DataObject
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	[Serializable]
	public class ORMVehicle : IORMEntity 
	{
		public virtual System.Guid Id { get; set; }
 
		///
		/// All Persistent non-key fields
		///
		public virtual Nullable<System.Int32> VehicleImageFileSize { get; set; }
		public virtual Nullable<System.Int32> IDLETimer { get; set; }
		public virtual System.String ModuleSwapNote { get; set; }
		public virtual System.String SerialNo { get; set; }
		public virtual System.String LastSessionId { get; set; }
		public virtual System.String VehicleImageInternalName { get; set; }
		public virtual System.String Description { get; set; }
		public virtual System.String HireNo { get; set; }
		public virtual Nullable<System.DateTime> LastSessionDateTzAdjusted { get; set; }
		public virtual Nullable<System.DateTime> DehireTime { get; set; }
		public virtual Nullable<System.DateTime> DeletedAtUtc { get; set; }
		public virtual Nullable<System.DateTime> HireTime { get; set; }
		public virtual Nullable<System.DateTime> LastSessionDate { get; set; }
		public virtual System.Boolean OnHire { get; set; }
		public virtual System.Boolean ModuleIsConnected { get; set; }
		public virtual System.Boolean ImpactLockout { get; set; }
		public virtual System.Boolean TimeoutEnabled { get; set; }
		public virtual System.Boolean IsCanbus { get; set; }
		public virtual System.String VehicleImage { get; set; }
 
		///
		/// PK-Side one-to-one relations
		///
		public virtual ORMVehicleLastGPSLocationView VehicleLastGPSLocationView { get; set; } 
		public virtual ORMOnDemandSettings OnDemandSettings { get; set; } 
		public virtual ORMVehicleDiagnostic VehicleDiagnostic { get; set; } 
 
		///
		/// All FK-Side Relations
		///
		public virtual ORMCanrule Canrule { get; set; }
		public virtual Nullable<System.Guid> CanruleId { get; set; }

		public virtual ORMFirmware Firmware { get; set; }
		public virtual Nullable<System.Guid> FirmwareId { get; set; }

		public virtual ORMDepartment Department { get; set; }
		public virtual System.Guid DepartmentId { get; set; }

		public virtual ORMDriver Driver { get; set; }
		public virtual Nullable<System.Guid> DriverId { get; set; }

		public virtual ORMCustomer Customer { get; set; }
		public virtual System.Guid CustomerId { get; set; }

		public virtual ORMServiceSettings ServiceSettings { get; set; }
		public virtual Nullable<System.Guid> ServiceSettingsId { get; set; }

		public virtual ORMModule Module { get; set; }
		public virtual System.Guid ModuleId1 { get; set; }

		public virtual ORMVehicleOtherSettings VehicleOtherSettings { get; set; }
		public virtual Nullable<System.Guid> VehicleOtherSettingsId { get; set; }

		public virtual ORMChecklistSettings ChecklistSettings { get; set; }
		public virtual Nullable<System.Guid> ChecklistSettingsId { get; set; }

		public virtual ORMModel Model { get; set; }
		public virtual System.Guid ModelId { get; set; }

		public virtual ORMPerson Person { get; set; }
		public virtual Nullable<System.Guid> PersonId { get; set; }

		public virtual ORMInspection Inspection { get; set; }
		public virtual Nullable<System.Guid> InspectionId { get; set; }

		public virtual ORMSite Site { get; set; }
		public virtual System.Guid SiteId { get; set; }

		public virtual ORMDepartmentChecklist DepartmentChecklist { get; set; }
		public virtual Nullable<System.Guid> DepartmentChecklistId { get; set; }

	
 
		///
		/// PK-Side one-to-many relations
		///
		public virtual IList<ORMOnDemandSession> OnDemandSessionItems { get; set; } = new List<ORMOnDemandSession>(); 
		public virtual IList<ORMSession> Sessions { get; set; } = new List<ORMSession>(); 
		// public virtual IList<ORMDetailedSessionView> DetailedSessionViewItems { get; set; } = new List<ORMDetailedSessionView>();DetailedSessionView is not mapped to the database 
		public virtual IList<ORMMessageHistory> MessageHistoryItems { get; set; } = new List<ORMMessageHistory>(); 
		public virtual IList<ORMImpactsForVehicleView> ImpactsForVehicleViewItems { get; set; } = new List<ORMImpactsForVehicleView>(); 
		public virtual IList<ORMVehicleAlertSubscription> VehicleAlertSubscriptionItems { get; set; } = new List<ORMVehicleAlertSubscription>(); 
		public virtual IList<ORMPedestrianDetectionHistory> PedestrianDetectionHistoryItems { get; set; } = new List<ORMPedestrianDetectionHistory>(); 
		// public virtual IList<ORMDetailedVORSessionStoreProcedure> DetailedVORSessionStoreProcedureItems { get; set; } = new List<ORMDetailedVORSessionStoreProcedure>();DetailedVORSessionStoreProcedure is not mapped to the database 
		public virtual IList<ORMPerVehicleNormalCardAccess> PerVehicleNormalCardAccessItems { get; set; } = new List<ORMPerVehicleNormalCardAccess>(); 
		public virtual IList<ORMVehicleBroadcastMessage> VehicleBroadcastMessageItems { get; set; } = new List<ORMVehicleBroadcastMessage>(); 
		// public virtual IList<ORMUnitUnutilisationStoreProcedure> UnitUtilisationStoreProcedureItems { get; set; } = new List<ORMUnitUnutilisationStoreProcedure>();UnitUnutilisationStoreProcedure is not mapped to the database 
		// public virtual IList<ORMVehicleProficiencyView> VehicleProficiencyViewItems { get; set; } = new List<ORMVehicleProficiencyView>();VehicleProficiencyView is not mapped to the database 
		// public virtual IList<ORMAllVORSessionsPerVehicleStoreProcedure> AllVORSessionsPerVehicleStoreProcedureItems { get; set; } = new List<ORMAllVORSessionsPerVehicleStoreProcedure>();AllVORSessionsPerVehicleStoreProcedure is not mapped to the database 
		// public virtual IList<ORMAllVehicleCalibrationStoreProcedure> AllVehicleCalibrationStoreProcedureItems { get; set; } = new List<ORMAllVehicleCalibrationStoreProcedure>();AllVehicleCalibrationStoreProcedure is not mapped to the database 
		public virtual IList<ORMVORSettingHistory> VORSettingHistoryItems { get; set; } = new List<ORMVORSettingHistory>(); 
		public virtual IList<ORMPerVehicleMasterCardAccess> VehicleCardAccesses { get; set; } = new List<ORMPerVehicleMasterCardAccess>(); 
		public virtual IList<ORMModuleHistory> ModuleHistoryItems { get; set; } = new List<ORMModuleHistory>(); 
		public virtual IList<ORMVehicleGPS> VehicleGPSLocations { get; set; } = new List<ORMVehicleGPS>(); 
		public virtual IList<ORMVehicleLockout> VehicleLockoutItems { get; set; } = new List<ORMVehicleLockout>(); 
		public virtual IList<ORMBroadcastMessageHistory> BroadcastMessageHistoryItems { get; set; } = new List<ORMBroadcastMessageHistory>(); 
		public virtual IList<ORMVehicleHireDehireHistory> VehicleHireDehireHistoryItems { get; set; } = new List<ORMVehicleHireDehireHistory>(); 
		public virtual IList<ORMChecklistFailurePerVechicleView> ChecklistFailurePerVechicleViewItems { get; set; } = new List<ORMChecklistFailurePerVechicleView>(); 
		public virtual IList<ORMPersonToPerVehicleMasterAccessView> PersonToPerVehicleMasterAccessViewItems { get; set; } = new List<ORMPersonToPerVehicleMasterAccessView>(); 
		// public virtual IList<ORMGeneralProductivityPerVehicleView> GeneralProductivityPerVehicleViewItems { get; set; } = new List<ORMGeneralProductivityPerVehicleView>();GeneralProductivityPerVehicleView is not mapped to the database 
		public virtual IList<ORMCurrentStatusVehicleView> CurrentStatusVehicleViewItems { get; set; } = new List<ORMCurrentStatusVehicleView>(); 
		public virtual IList<ORMSlamcoreDeviceHistory> SlamcoreDeviceHistoryItems { get; set; } = new List<ORMSlamcoreDeviceHistory>(); 
		public virtual IList<ORMPersonToPerVehicleNormalAccessView> PersonToPerVehicleNormalAccessViewItems { get; set; } = new List<ORMPersonToPerVehicleNormalAccessView>(); 
		public virtual IList<ORMVehicleSessionlessImpact> VehicleSessionlessImpactItems { get; set; } = new List<ORMVehicleSessionlessImpact>(); 
		// public virtual IList<ORMUnitUtilisationStoreProcedure> UnitUnutilisationStoreProcedureItems { get; set; } = new List<ORMUnitUtilisationStoreProcedure>();UnitUtilisationStoreProcedure is not mapped to the database 
		public virtual IList<ORMNetworkSettings> NetworkSettingsItems { get; set; } = new List<ORMNetworkSettings>(); 
		public virtual IList<ORMVehicleSupervisorsView> VehicleSupervisorsViewItems { get; set; } = new List<ORMVehicleSupervisorsView>(); 
		public virtual IList<ORMVehicleToPreOpChecklistView> VehicleToPreOpCheckilstItems { get; set; } = new List<ORMVehicleToPreOpChecklistView>(); 
 
		///
		/// Bridge to DataObject
		///
		public virtual IDataObject ToDataObject(IObjectsDataSet dataset, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
            var session = nHibernateSessionController.GetCurrentSession(threadContext);
            session.Evict(this);

			var x = serviceProvider.GetRequiredService<VehicleDataObject>();

 
			SetProperties(x);

			x.IsDirty = x.IsNew = x.IsMarkedForDeletion = false;

			x.ObjectsDataSet = dataset;
			x.ObjectsDataSet.AddObjectIfDoesNotExist(x);

			// Deep-map prefetch relations
			if (PrefetchAssociations.HasPrefetchForEntity("Vehicle", dataProviderTransaction?.Parameters))
			{
				SetRelations(x, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction);
			}

			return x;
		}

		protected void SetProperties(VehicleDataObject x)
		{
			x.SetIdValue(Id, false, false);
			x.SetVehicleImageFileSizeValue(VehicleImageFileSize, false, false);
			x.SetIDLETimerValue(IDLETimer, false, false);
			x.SetModuleSwapNoteValue(ModuleSwapNote, false, false);
			x.SetSerialNoValue(SerialNo, false, false);
			x.SetLastSessionIdValue(LastSessionId, false, false);
			x.SetVehicleImageInternalNameValue(VehicleImageInternalName, false, false);
			x.SetDescriptionValue(Description, false, false);
			x.SetHireNoValue(HireNo, false, false);
			x.SetLastSessionDateTzAdjustedValue(LastSessionDateTzAdjusted, false, false);
			x.SetDehireTimeValue(DehireTime, false, false);
			x.SetDeletedAtUtcValue(DeletedAtUtc, false, false);
			x.SetHireTimeValue(HireTime, false, false);
			x.SetLastSessionDateValue(LastSessionDate, false, false);
			x.SetOnHireValue(OnHire, false, false);
			x.SetModuleIsConnectedValue(ModuleIsConnected, false, false);
			x.SetImpactLockoutValue(ImpactLockout, false, false);
			x.SetTimeoutEnabledValue(TimeoutEnabled, false, false);
			x.SetIsCanbusValue(IsCanbus, false, false);
			x.SetVehicleImageValue(VehicleImage, false, false);
			x.SetCanruleIdValue(this.CanruleId, false, false);
			x.SetFirmwareIdValue(this.FirmwareId, false, false);
			x.SetDepartmentIdValue(this.DepartmentId, false, false);
			x.SetDriverIdValue(this.DriverId, false, false);
			x.SetCustomerIdValue(this.CustomerId, false, false);
			x.SetServiceSettingsIdValue(this.ServiceSettingsId, false, false);
			x.SetModuleId1Value(this.ModuleId1, false, false);
			x.SetVehicleOtherSettingsIdValue(this.VehicleOtherSettingsId, false, false);
			x.SetChecklistSettingsIdValue(this.ChecklistSettingsId, false, false);
			x.SetModelIdValue(this.ModelId, false, false);
			x.SetPersonIdValue(this.PersonId, false, false);
			x.SetInspectionIdValue(this.InspectionId, false, false);
			x.SetSiteIdValue(this.SiteId, false, false);
			x.SetDepartmentChecklistIdValue(this.DepartmentChecklistId, false, false);
		}

		protected void SetRelations(VehicleDataObject x, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
			var prefetches = PrefetchAssociations.Get("Vehicle", dataProviderTransaction?.Parameters);

			if (prefetches.Contains("Module") && this.Module != null)
			{
				var module = x.ObjectsDataSet.GetObject(new ModuleDataObject((System.Guid)this.Module.Id) { IsNew = false });

				if (module == null)
					module = this.Module.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ModuleDataObject;

				x.SetModuleValue(module);
			}

			if (prefetches.Contains("VehicleToPreOpCheckilstItems") && this.VehicleToPreOpCheckilstItems.Count > 0)
			{
				var iter = this.VehicleToPreOpCheckilstItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleToPreOpCheckilstItemsItem = x.ObjectsDataSet.GetObject(new VehicleToPreOpChecklistViewDataObject((System.Guid)iter.Current.PreOperationalChecklistId, (System.Guid)iter.Current.VehicleId) { IsNew = false });

					if (vehicleToPreOpCheckilstItemsItem == null)
						vehicleToPreOpCheckilstItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleToPreOpChecklistViewDataObject;

					x.VehicleToPreOpCheckilstItems.Add(vehicleToPreOpCheckilstItemsItem);
				}
			}

			if (prefetches.Contains("CurrentStatusVehicleViewItems") && this.CurrentStatusVehicleViewItems.Count > 0)
			{
				var iter = this.CurrentStatusVehicleViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var currentStatusVehicleViewItemsItem = x.ObjectsDataSet.GetObject(new CurrentStatusVehicleViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (currentStatusVehicleViewItemsItem == null)
						currentStatusVehicleViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CurrentStatusVehicleViewDataObject;

					x.CurrentStatusVehicleViewItems.Add(currentStatusVehicleViewItemsItem);
				}
			}

			if (prefetches.Contains("PedestrianDetectionHistoryItems") && this.PedestrianDetectionHistoryItems.Count > 0)
			{
				var iter = this.PedestrianDetectionHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var pedestrianDetectionHistoryItemsItem = x.ObjectsDataSet.GetObject(new PedestrianDetectionHistoryDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (pedestrianDetectionHistoryItemsItem == null)
						pedestrianDetectionHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as PedestrianDetectionHistoryDataObject;

					x.PedestrianDetectionHistoryItems.Add(pedestrianDetectionHistoryItemsItem);
				}
			}

			if (prefetches.Contains("ChecklistSettings") && this.ChecklistSettings != null)
			{
				var checklistSettings = x.ObjectsDataSet.GetObject(new ChecklistSettingsDataObject((System.Guid)this.ChecklistSettings.Id) { IsNew = false });

				if (checklistSettings == null)
					checklistSettings = this.ChecklistSettings.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ChecklistSettingsDataObject;

				x.SetChecklistSettingsValue(checklistSettings);
			}

			if (prefetches.Contains("VehicleHireDehireHistoryItems") && this.VehicleHireDehireHistoryItems.Count > 0)
			{
				var iter = this.VehicleHireDehireHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleHireDehireHistoryItemsItem = x.ObjectsDataSet.GetObject(new VehicleHireDehireHistoryDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleHireDehireHistoryItemsItem == null)
						vehicleHireDehireHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleHireDehireHistoryDataObject;

					x.VehicleHireDehireHistoryItems.Add(vehicleHireDehireHistoryItemsItem);
				}
			}

			if (prefetches.Contains("PersonToPerVehicleMasterAccessViewItems") && this.PersonToPerVehicleMasterAccessViewItems.Count > 0)
			{
				var iter = this.PersonToPerVehicleMasterAccessViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var personToPerVehicleMasterAccessViewItemsItem = x.ObjectsDataSet.GetObject(new PersonToPerVehicleMasterAccessViewDataObject((System.Guid)iter.Current.PersonId, (System.Guid)iter.Current.VehicleId) { IsNew = false });

					if (personToPerVehicleMasterAccessViewItemsItem == null)
						personToPerVehicleMasterAccessViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as PersonToPerVehicleMasterAccessViewDataObject;

					x.PersonToPerVehicleMasterAccessViewItems.Add(personToPerVehicleMasterAccessViewItemsItem);
				}
			}

			if (prefetches.Contains("VehicleSupervisorsViewItems") && this.VehicleSupervisorsViewItems.Count > 0)
			{
				var iter = this.VehicleSupervisorsViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleSupervisorsViewItemsItem = x.ObjectsDataSet.GetObject(new VehicleSupervisorsViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleSupervisorsViewItemsItem == null)
						vehicleSupervisorsViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleSupervisorsViewDataObject;

					x.VehicleSupervisorsViewItems.Add(vehicleSupervisorsViewItemsItem);
				}
			}

			if (prefetches.Contains("VehicleCardAccesses") && this.VehicleCardAccesses.Count > 0)
			{
				var iter = this.VehicleCardAccesses.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleCardAccessesItem = x.ObjectsDataSet.GetObject(new PerVehicleMasterCardAccessDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleCardAccessesItem == null)
						vehicleCardAccessesItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as PerVehicleMasterCardAccessDataObject;

					x.VehicleCardAccesses.Add(vehicleCardAccessesItem);
				}
			}

			if (prefetches.Contains("Inspection") && this.Inspection != null)
			{
				var inspection = x.ObjectsDataSet.GetObject(new InspectionDataObject((System.Guid)this.Inspection.Id) { IsNew = false });

				if (inspection == null)
					inspection = this.Inspection.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as InspectionDataObject;

				x.SetInspectionValue(inspection);
			}

			if (prefetches.Contains("Canrule") && this.Canrule != null)
			{
				var canrule = x.ObjectsDataSet.GetObject(new CanruleDataObject((System.Guid)this.Canrule.Id) { IsNew = false });

				if (canrule == null)
					canrule = this.Canrule.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CanruleDataObject;

				x.SetCanruleValue(canrule);
			}

			if (prefetches.Contains("PersonToPerVehicleNormalAccessViewItems") && this.PersonToPerVehicleNormalAccessViewItems.Count > 0)
			{
				var iter = this.PersonToPerVehicleNormalAccessViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var personToPerVehicleNormalAccessViewItemsItem = x.ObjectsDataSet.GetObject(new PersonToPerVehicleNormalAccessViewDataObject((System.Guid)iter.Current.PermissionId, (System.Guid)iter.Current.PersonId, (System.Guid)iter.Current.VehicleId) { IsNew = false });

					if (personToPerVehicleNormalAccessViewItemsItem == null)
						personToPerVehicleNormalAccessViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as PersonToPerVehicleNormalAccessViewDataObject;

					x.PersonToPerVehicleNormalAccessViewItems.Add(personToPerVehicleNormalAccessViewItemsItem);
				}
			}

			if (prefetches.Contains("Firmware") && this.Firmware != null)
			{
				var firmware = x.ObjectsDataSet.GetObject(new FirmwareDataObject((System.Guid)this.Firmware.Id) { IsNew = false });

				if (firmware == null)
					firmware = this.Firmware.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as FirmwareDataObject;

				x.SetFirmwareValue(firmware);
			}

			if (prefetches.Contains("MessageHistoryItems") && this.MessageHistoryItems.Count > 0)
			{
				var iter = this.MessageHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var messageHistoryItemsItem = x.ObjectsDataSet.GetObject(new MessageHistoryDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (messageHistoryItemsItem == null)
						messageHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as MessageHistoryDataObject;

					x.MessageHistoryItems.Add(messageHistoryItemsItem);
				}
			}

			if (prefetches.Contains("VORSettingHistoryItems") && this.VORSettingHistoryItems.Count > 0)
			{
				var iter = this.VORSettingHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vORSettingHistoryItemsItem = x.ObjectsDataSet.GetObject(new VORSettingHistoryDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vORSettingHistoryItemsItem == null)
						vORSettingHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VORSettingHistoryDataObject;

					x.VORSettingHistoryItems.Add(vORSettingHistoryItemsItem);
				}
			}

			if (prefetches.Contains("VehicleGPSLocations") && this.VehicleGPSLocations.Count > 0)
			{
				var iter = this.VehicleGPSLocations.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleGPSLocationsItem = x.ObjectsDataSet.GetObject(new VehicleGPSDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleGPSLocationsItem == null)
						vehicleGPSLocationsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleGPSDataObject;

					x.VehicleGPSLocations.Add(vehicleGPSLocationsItem);
				}
			}

			if (prefetches.Contains("VehicleLockoutItems") && this.VehicleLockoutItems.Count > 0)
			{
				var iter = this.VehicleLockoutItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleLockoutItemsItem = x.ObjectsDataSet.GetObject(new VehicleLockoutDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleLockoutItemsItem == null)
						vehicleLockoutItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleLockoutDataObject;

					x.VehicleLockoutItems.Add(vehicleLockoutItemsItem);
				}
			}

			if (prefetches.Contains("VehicleBroadcastMessageItems") && this.VehicleBroadcastMessageItems.Count > 0)
			{
				var iter = this.VehicleBroadcastMessageItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleBroadcastMessageItemsItem = x.ObjectsDataSet.GetObject(new VehicleBroadcastMessageDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleBroadcastMessageItemsItem == null)
						vehicleBroadcastMessageItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleBroadcastMessageDataObject;

					x.VehicleBroadcastMessageItems.Add(vehicleBroadcastMessageItemsItem);
				}
			}

			if (prefetches.Contains("DepartmentChecklist") && this.DepartmentChecklist != null)
			{
				var departmentChecklist = x.ObjectsDataSet.GetObject(new DepartmentChecklistDataObject((System.Guid)this.DepartmentChecklist.Id) { IsNew = false });

				if (departmentChecklist == null)
					departmentChecklist = this.DepartmentChecklist.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DepartmentChecklistDataObject;

				x.SetDepartmentChecklistValue(departmentChecklist);
			}

			if (prefetches.Contains("Sessions") && this.Sessions.Count > 0)
			{
				var iter = this.Sessions.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var sessionsItem = x.ObjectsDataSet.GetObject(new SessionDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (sessionsItem == null)
						sessionsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SessionDataObject;

					x.Sessions.Add(sessionsItem);
				}
			}

			if (prefetches.Contains("Department") && this.Department != null)
			{
				var department = x.ObjectsDataSet.GetObject(new DepartmentDataObject((System.Guid)this.Department.Id) { IsNew = false });

				if (department == null)
					department = this.Department.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DepartmentDataObject;

				x.SetDepartmentValue(department);
			}

			if (prefetches.Contains("Site") && this.Site != null)
			{
				var site = x.ObjectsDataSet.GetObject(new SiteDataObject((System.Guid)this.Site.Id) { IsNew = false });

				if (site == null)
					site = this.Site.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SiteDataObject;

				x.SetSiteValue(site);
			}

			if (prefetches.Contains("OnDemandSettings") && this.OnDemandSettings != null)
			{
				var onDemandSettings = x.ObjectsDataSet.GetObject(new OnDemandSettingsDataObject((System.Guid)this.OnDemandSettings.Id) { IsNew = false });

				if (onDemandSettings == null)
					onDemandSettings = this.OnDemandSettings.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as OnDemandSettingsDataObject;

				x.SetOnDemandSettingsValue(onDemandSettings);
			}

			if (prefetches.Contains("SlamcoreDeviceHistoryItems") && this.SlamcoreDeviceHistoryItems.Count > 0)
			{
				var iter = this.SlamcoreDeviceHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var slamcoreDeviceHistoryItemsItem = x.ObjectsDataSet.GetObject(new SlamcoreDeviceHistoryDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (slamcoreDeviceHistoryItemsItem == null)
						slamcoreDeviceHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SlamcoreDeviceHistoryDataObject;

					x.SlamcoreDeviceHistoryItems.Add(slamcoreDeviceHistoryItemsItem);
				}
			}

			if (prefetches.Contains("Model") && this.Model != null)
			{
				var model = x.ObjectsDataSet.GetObject(new ModelDataObject((System.Guid)this.Model.Id) { IsNew = false });

				if (model == null)
					model = this.Model.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ModelDataObject;

				x.SetModelValue(model);
			}

			if (prefetches.Contains("Driver") && this.Driver != null)
			{
				var driver = x.ObjectsDataSet.GetObject(new DriverDataObject((System.Guid)this.Driver.Id) { IsNew = false });

				if (driver == null)
					driver = this.Driver.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DriverDataObject;

				x.SetDriverValue(driver);
			}

			if (prefetches.Contains("VehicleLastGPSLocationView") && this.VehicleLastGPSLocationView != null)
			{
				var vehicleLastGPSLocationView = x.ObjectsDataSet.GetObject(new VehicleLastGPSLocationViewDataObject((System.Guid)this.VehicleLastGPSLocationView.VehicleId) { IsNew = false });

				if (vehicleLastGPSLocationView == null)
					vehicleLastGPSLocationView = this.VehicleLastGPSLocationView.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleLastGPSLocationViewDataObject;

				x.SetVehicleLastGPSLocationViewValue(vehicleLastGPSLocationView);
			}

			if (prefetches.Contains("Customer") && this.Customer != null)
			{
				var customer = x.ObjectsDataSet.GetObject(new CustomerDataObject((System.Guid)this.Customer.Id) { IsNew = false });

				if (customer == null)
					customer = this.Customer.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerDataObject;

				x.SetCustomerValue(customer);
			}

			if (prefetches.Contains("NetworkSettingsItems") && this.NetworkSettingsItems.Count > 0)
			{
				var iter = this.NetworkSettingsItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var networkSettingsItemsItem = x.ObjectsDataSet.GetObject(new NetworkSettingsDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (networkSettingsItemsItem == null)
						networkSettingsItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as NetworkSettingsDataObject;

					x.NetworkSettingsItems.Add(networkSettingsItemsItem);
				}
			}

			if (prefetches.Contains("VehicleAlertSubscriptionItems") && this.VehicleAlertSubscriptionItems.Count > 0)
			{
				var iter = this.VehicleAlertSubscriptionItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleAlertSubscriptionItemsItem = x.ObjectsDataSet.GetObject(new VehicleAlertSubscriptionDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleAlertSubscriptionItemsItem == null)
						vehicleAlertSubscriptionItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleAlertSubscriptionDataObject;

					x.VehicleAlertSubscriptionItems.Add(vehicleAlertSubscriptionItemsItem);
				}
			}

			if (prefetches.Contains("Person") && this.Person != null)
			{
				var person = x.ObjectsDataSet.GetObject(new PersonDataObject((System.Guid)this.Person.Id) { IsNew = false });

				if (person == null)
					person = this.Person.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as PersonDataObject;

				x.SetPersonValue(person);
			}

			if (prefetches.Contains("ImpactsForVehicleViewItems") && this.ImpactsForVehicleViewItems.Count > 0)
			{
				var iter = this.ImpactsForVehicleViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var impactsForVehicleViewItemsItem = x.ObjectsDataSet.GetObject(new ImpactsForVehicleViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (impactsForVehicleViewItemsItem == null)
						impactsForVehicleViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ImpactsForVehicleViewDataObject;

					x.ImpactsForVehicleViewItems.Add(impactsForVehicleViewItemsItem);
				}
			}

			if (prefetches.Contains("ChecklistFailurePerVechicleViewItems") && this.ChecklistFailurePerVechicleViewItems.Count > 0)
			{
				var iter = this.ChecklistFailurePerVechicleViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var checklistFailurePerVechicleViewItemsItem = x.ObjectsDataSet.GetObject(new ChecklistFailurePerVechicleViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (checklistFailurePerVechicleViewItemsItem == null)
						checklistFailurePerVechicleViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ChecklistFailurePerVechicleViewDataObject;

					x.ChecklistFailurePerVechicleViewItems.Add(checklistFailurePerVechicleViewItemsItem);
				}
			}

			if (prefetches.Contains("VehicleOtherSettings") && this.VehicleOtherSettings != null)
			{
				var vehicleOtherSettings = x.ObjectsDataSet.GetObject(new VehicleOtherSettingsDataObject((System.Guid)this.VehicleOtherSettings.Id) { IsNew = false });

				if (vehicleOtherSettings == null)
					vehicleOtherSettings = this.VehicleOtherSettings.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleOtherSettingsDataObject;

				x.SetVehicleOtherSettingsValue(vehicleOtherSettings);
			}

			if (prefetches.Contains("VehicleDiagnostic") && this.VehicleDiagnostic != null)
			{
				var vehicleDiagnostic = x.ObjectsDataSet.GetObject(new VehicleDiagnosticDataObject((System.Guid)this.VehicleDiagnostic.Id) { IsNew = false });

				if (vehicleDiagnostic == null)
					vehicleDiagnostic = this.VehicleDiagnostic.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleDiagnosticDataObject;

				x.SetVehicleDiagnosticValue(vehicleDiagnostic);
			}

			if (prefetches.Contains("VehicleSessionlessImpactItems") && this.VehicleSessionlessImpactItems.Count > 0)
			{
				var iter = this.VehicleSessionlessImpactItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleSessionlessImpactItemsItem = x.ObjectsDataSet.GetObject(new VehicleSessionlessImpactDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleSessionlessImpactItemsItem == null)
						vehicleSessionlessImpactItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleSessionlessImpactDataObject;

					x.VehicleSessionlessImpactItems.Add(vehicleSessionlessImpactItemsItem);
				}
			}

			if (prefetches.Contains("BroadcastMessageHistoryItems") && this.BroadcastMessageHistoryItems.Count > 0)
			{
				var iter = this.BroadcastMessageHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var broadcastMessageHistoryItemsItem = x.ObjectsDataSet.GetObject(new BroadcastMessageHistoryDataObject((System.Int32)iter.Current.MessageId) { IsNew = false });

					if (broadcastMessageHistoryItemsItem == null)
						broadcastMessageHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as BroadcastMessageHistoryDataObject;

					x.BroadcastMessageHistoryItems.Add(broadcastMessageHistoryItemsItem);
				}
			}

			if (prefetches.Contains("OnDemandSessionItems") && this.OnDemandSessionItems.Count > 0)
			{
				var iter = this.OnDemandSessionItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var onDemandSessionItemsItem = x.ObjectsDataSet.GetObject(new OnDemandSessionDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (onDemandSessionItemsItem == null)
						onDemandSessionItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as OnDemandSessionDataObject;

					x.OnDemandSessionItems.Add(onDemandSessionItemsItem);
				}
			}

			if (prefetches.Contains("ServiceSettings") && this.ServiceSettings != null)
			{
				var serviceSettings = x.ObjectsDataSet.GetObject(new ServiceSettingsDataObject((System.Guid)this.ServiceSettings.Id) { IsNew = false });

				if (serviceSettings == null)
					serviceSettings = this.ServiceSettings.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ServiceSettingsDataObject;

				x.SetServiceSettingsValue(serviceSettings);
			}

			if (prefetches.Contains("ModuleHistoryItems") && this.ModuleHistoryItems.Count > 0)
			{
				var iter = this.ModuleHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var moduleHistoryItemsItem = x.ObjectsDataSet.GetObject(new ModuleHistoryDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (moduleHistoryItemsItem == null)
						moduleHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ModuleHistoryDataObject;

					x.ModuleHistoryItems.Add(moduleHistoryItemsItem);
				}
			}

			if (prefetches.Contains("PerVehicleNormalCardAccessItems") && this.PerVehicleNormalCardAccessItems.Count > 0)
			{
				var iter = this.PerVehicleNormalCardAccessItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var perVehicleNormalCardAccessItemsItem = x.ObjectsDataSet.GetObject(new PerVehicleNormalCardAccessDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (perVehicleNormalCardAccessItemsItem == null)
						perVehicleNormalCardAccessItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as PerVehicleNormalCardAccessDataObject;

					x.PerVehicleNormalCardAccessItems.Add(perVehicleNormalCardAccessItemsItem);
				}
			}

		}
		
		// For database filtering, nothing IsNew or IsDirty, but these stubs needed for ORM security predicates to compile
		public virtual bool IsNew => false;
		public virtual bool IsDirty => false;

	}
}