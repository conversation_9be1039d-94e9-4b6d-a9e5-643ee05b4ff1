﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Text;
using System.IO;
using System.Collections.Generic;
using System.Net;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Http;
using System.Threading.Tasks;

using GenerativeObjects.Practices;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;	
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Serialization;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.Settings;
using FleetXQ.Data.DataObjects;
using Newtonsoft.Json;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace FleetXQ.Client.Model.Components
{
    public partial class SlamcorePathHistoryExportComponentService : BaseService, ISlamcorePathHistoryExportComponentService
    {
        /// <summary>
        /// Constructor, with dependencies injection
        /// </summary>
        /// <param name="provider">The injected service provide</param>
        /// <param name="configuration">The injected configuration</param>
        /// <param name="threadContext">The injected thread context</param>
        public SlamcorePathHistoryExportComponentService(IServiceProvider provider, IConfiguration configuration, IThreadContext threadContext) : base(provider, configuration, threadContext)
        {
            _endpointName = "slamcorepathhistoryexportcomponent";
        }

        /// <summary>
        /// ExportAsync component operation
        /// 
        /// </summary>
        /// <param name="filterPredicate"></param>
        /// <param name="filterParameters"></param>
        /// <returns>ExportJobStatus</returns>
        public async Task<ExportJobStatusDataObject> ExportAsync(System.String filterPredicate, System.String filterParameters)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string filterPredicateAsJson_local = JsonConvert.SerializeObject(filterPredicate, _jsonSerializerSettings);

                formData_local.Add("filterPredicate", filterPredicateAsJson_local);
                string filterParametersAsJson_local = JsonConvert.SerializeObject(filterParameters, _jsonSerializerSettings);

                formData_local.Add("filterParameters", filterParametersAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}export";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var container_local = _serviceProvider.GetRequiredService<ExportJobStatusContainer>();
                    JsonConvert.PopulateObject(responseContent_local, container_local, _jsonSerializerSettings);

                    container_local.ObjectsDataSet.EnsureInitialized();
                    container_local.ObjectsDataSet.ReconstructIndexes();
                               
                    return container_local.ExtractExportJobStatus();  
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
    }
}
