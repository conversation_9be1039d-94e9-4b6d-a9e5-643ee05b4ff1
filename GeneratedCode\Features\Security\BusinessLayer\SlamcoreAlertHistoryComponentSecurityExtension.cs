﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

using System;
using System.Linq;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;


using FleetXQ.Feature.Security.Common;
using FleetXQ.BusinessLayer.Components.Server.Extensions;

namespace FleetXQ.BusinessLayer.Components.Server
{
	/// <summary>
	/// class SlamcoreAlertHistoryComponentSecurityExtension
	/// Implements GO Security for the SlamcoreAlertHistoryComponent component
	/// </summary>
	public class SlamcoreAlertHistoryComponentSecurityExtension : ISlamcoreAlertHistoryComponentExtension
	{
 		protected IServiceProvider _serviceProvider;
		protected IConfiguration _configuration;
		protected IAuthentication _authentication;

		public SlamcoreAlertHistoryComponentSecurityExtension(IServiceProvider provider, IConfiguration configuration, IAuthentication authentication)
		{
  			_serviceProvider = provider;
			_configuration = configuration;
			_authentication = authentication;
		}

		/// <summary>
		/// Init()
		/// Attach to component OnBeforeCall extension point
		/// </summary>
		public Task InitAsync(ISlamcoreAlertHistoryComponentSurrogate component)
		{
			(component as SlamcoreAlertHistoryComponentSurrogate).OnBeforeCall += this.OnBeforeCallAsync;
			return Task.CompletedTask;
		}

		/// <summary>
		/// OnBeforeCall()
		/// Check current user role permissions for the proposed component operation call
		/// </summary>
		private async Task OnBeforeCallAsync(ComponentExtensionEventArgs e)
		{
			// Get user claims
			UserClaims claims = await _authentication.GetCurrentUserClaimsAsync();

			// We start by looking at the operation permissions
			// If no decision reached from doing that, it internally ripples up to check the component permissions
			if (await CheckComponentOperationPermissionAsync(claims, e) != PermissionLevel.Authorized)
			{
				// Give authentication opportunity to package up the exception
				await _authentication.ThrowAccessDeniedAsync(new GOServerException("accessDenied", "Access denied to component operation SlamcoreAlertHistoryComponent." + e.OperationName, new ForbiddenAccessException("forbidden access")));
			}
		}
 
		/// <summary>
		/// CheckComponentPermission()
		/// Check current user role permissioned to access the component as a whole
		/// </summary>
		private async Task<PermissionLevel> CheckComponentPermissionAsync(UserClaims claims, ComponentExtensionEventArgs e)
		{
			// If not authenticated and no role assigned to anonymous users
            if (claims.IsAuthenticated == false && claims.Roles.Count == 0)
                return PermissionLevel.Denied;
			
			return await CheckDefaultComponentPermissionAsync(claims, e);
		}

		/// <summary>
		/// CheckDefaultComponentPermission()
		/// Check default user role permission to access the component
		/// </summary>
		private async Task<PermissionLevel> CheckDefaultComponentPermissionAsync(UserClaims claims, ComponentExtensionEventArgs e)
		{
			// Roles "Administrator", "Customer", "DealerAdmin" have default authorized access to all components (can still later be overridden by more specific rules)
            var allowedRoles = new List<string> { "Administrator", "Customer", "DealerAdmin" };
            var authorizedRoles = claims.Roles.Intersect(allowedRoles);

            if (authorizedRoles.Any())
            {
                return PermissionLevel.Authorized;
            }
			
			return PermissionLevel.Denied;
		}

        private async Task<PermissionLevel> CheckComponentOperationPermissionAsync(UserClaims claims, ComponentExtensionEventArgs e)
        {
            switch (e.OperationName)
            {
			}

            // If we get here, it means there is no explicit (override) authorization operation rule for at least one of the user's roles
            // So we go 'up' to the component permissions and try the same logic at component level
            return await CheckComponentPermissionAsync(claims, e);
        }
	}
}

