﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'SlamcorePedestrianDetection'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class SlamcorePedestrianDetectionDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("SlamcoreDeviceId")]
		protected System.Guid _slamcoreDeviceId;
		[JsonProperty ("XPosition")]
		protected System.Decimal _xPosition;
		[JsonProperty ("YPosition")]
		protected System.Decimal _yPosition;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)
		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _slamcoreDevice_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_slamcoreDevice_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public SlamcorePedestrianDetectionDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public SlamcorePedestrianDetectionDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public SlamcorePedestrianDetectionDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public SlamcorePedestrianDetectionDataObject Initialize(SlamcorePedestrianDetectionDataObject template, bool deepCopy)
		{
			this.SetIdValue(template.Id, false, false);
			this.SetSlamcoreDeviceIdValue(template.SlamcoreDeviceId, false, false);
			this.SetXPositionValue(template.XPosition, false, false);
			this.SetYPositionValue(template.YPosition, false, false);
 
			this._slamcoreDevice_NewObjectId = template._slamcoreDevice_NewObjectId;
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual SlamcorePedestrianDetectionDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual SlamcorePedestrianDetectionDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<SlamcorePedestrianDetectionDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var slamcorePedestrianDetectionSource = sourceObject as SlamcorePedestrianDetectionDataObject;

			if (ReferenceEquals(null, slamcorePedestrianDetectionSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetIdValue(slamcorePedestrianDetectionSource.Id, false, false);
			this.SetSlamcoreDeviceIdValue(slamcorePedestrianDetectionSource.SlamcoreDeviceId, false, false);
			this.SetXPositionValue(slamcorePedestrianDetectionSource.XPosition, false, false);
			this.SetYPositionValue(slamcorePedestrianDetectionSource.YPosition, false, false);
			this._slamcoreDevice_NewObjectId = (sourceObject as SlamcorePedestrianDetectionDataObject)._slamcoreDevice_NewObjectId;

			if (deepCopy)
			{
				this.ObjectsDataSet = slamcorePedestrianDetectionSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(slamcorePedestrianDetectionSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as SlamcorePedestrianDetectionDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {
			if (this._slamcoreDevice_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._slamcoreDevice_NewObjectId))
				{
                    this._slamcoreDevice_NewObjectId = null;
				}
                else
				{
					this._slamcoreDevice_NewObjectId = datasetMergingInternalIdMapping[(int) this._slamcoreDevice_NewObjectId];
				}
			}

		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<SlamcoreDeviceDataObject> _slamcoreDeviceService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceDataObject>>();
      public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet)
		{
			SetSlamcoreDeviceValue(valueToSet, true, true);
		}

        public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			SlamcoreDeviceDataObject existing_slamcoreDevice = null ;

			if ( !(ObjectsDataSet == null))
			{
				SlamcoreDeviceDataObject key;

				if (this._slamcoreDevice_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._slamcoreDevice_NewObjectId;			
				}

				existing_slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_slamcoreDevice ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("SlamcoreDevice", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "SlamcorePedestrianDetectionDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_slamcoreDevice_NewObjectId != valueToSet.InternalObjectId)
					{
						_slamcoreDevice_NewObjectId = valueToSet.InternalObjectId;
						_slamcoreDeviceId = valueToSet.Id;
						OnPropertyChanged("SlamcoreDeviceId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_slamcoreDeviceId != valueToSet.Id)
					{
						_slamcoreDevice_NewObjectId = null;

						_slamcoreDeviceId = valueToSet.Id;
						OnPropertyChanged("SlamcoreDeviceId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_slamcoreDeviceId = Guid.Empty;
				OnPropertyChanged("SlamcoreDeviceId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_slamcoreDevice ,valueToSet))
				OnPropertyChanged("SlamcoreDevice", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __slamcoreDeviceSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreDeviceAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreDevice", which is a SlamcoreDeviceDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SlamcoreDeviceDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreDeviceAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreDeviceSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreDeviceAlreadyLazyLoaded || forceReload)
                {
								
					SlamcoreDeviceDataObject slamcoreDevice = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __slamcoreDeviceAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId);
						slamcoreDevice.IsNew = false;
						slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
						if (slamcoreDevice != null)
						{
							return slamcoreDevice;
						}
					}

					slamcoreDevice = await _slamcoreDeviceService.GetAsync(_serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId), parameters : parameters, skipSecurity: skipSecurity);

					SetSlamcoreDeviceValue(slamcoreDevice, false, false);
					__slamcoreDeviceAlreadyLazyLoaded = true;				
		
					slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId);
					slamcoreDevice.IsNew = false;
					slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
                    __slamcoreDeviceAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreDeviceAsync(false);
            }
            finally
            {
                __slamcoreDeviceSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SlamcoreDeviceDataObject SlamcoreDevice 
		{
			get
			{			
				return GetSlamcoreDeviceAsync(true).Result;
			}
			set
			{
				SetSlamcoreDeviceValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreDevice()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcorePedestrianDetectionDataObject") && ObjectsDataSet.RelationsToInclude["SlamcorePedestrianDetectionDataObject"].Contains("SlamcoreDevice");
		}

		public virtual async Task<SlamcoreDeviceDataObject> GetSlamcoreDeviceAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SlamcoreDeviceDataObject slamcoreDevice;

				
			if (_slamcoreDevice_NewObjectId != null)
			{
				slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>();
				slamcoreDevice.IsNew = true;
				slamcoreDevice.InternalObjectId = _slamcoreDevice_NewObjectId;
				slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
			}
			else
			{
				slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this.SlamcoreDeviceId);
				slamcoreDevice.IsNew = false;
				slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
				
				if (allowLazyLoading && slamcoreDevice == null && LazyLoadingEnabled && (!__slamcoreDeviceAlreadyLazyLoaded || forceReload))
				{
					slamcoreDevice = await LoadSlamcoreDeviceAsync(forceReload : forceReload);
				}
			}
				
			return slamcoreDevice;
		}

		public virtual System.Guid SlamcoreDeviceForeignKey
		{
			get { return SlamcoreDeviceId; }
			set 
			{	
				SlamcoreDeviceId = value;
			}
			
		}
		

		public override void ClearLazyLoadFlags()
		{
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadSlamcoreDeviceAsync()) != null)
				result.Add(SlamcoreDevice);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				SlamcoreDevice == other ||
				(other is SlamcoreDeviceDataObject && (SlamcoreDeviceId != default(System.Guid)) && (SlamcoreDeviceId == (other as SlamcoreDeviceDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the SlamcorePedestrianDetection DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetSlamcoreDeviceIdValue(System.Guid valueToSet)
		{
			SetSlamcoreDeviceIdValue(valueToSet, true, true);
		}

		public virtual void SetSlamcoreDeviceIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_slamcoreDeviceId != valueToSet)
			{
				_slamcoreDeviceId = valueToSet;

				OnPropertyChanged("SlamcoreDeviceId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SlamcoreDeviceId property of the SlamcorePedestrianDetection DataObject</summary>
        public virtual System.Guid SlamcoreDeviceId 
		{
			get	{ return _slamcoreDeviceId;}
			
			
			set
			{
				SetSlamcoreDeviceIdValue(value);
			}
		}		
			
			
		public virtual void SetXPositionValue(System.Decimal valueToSet)
		{
			SetXPositionValue(valueToSet, true, true);
		}

		public virtual void SetXPositionValue(System.Decimal valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_xPosition != Math.Round(valueToSet, 17))
			{
				_xPosition = Math.Round(valueToSet, 17);

				OnPropertyChanged("XPosition", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The XPosition property of the SlamcorePedestrianDetection DataObject</summary>
        public virtual System.Decimal XPosition 
		{
			get	{ return _xPosition;}
			
			
			set
			{
				SetXPositionValue(value);
			}
		}		
			
			
		public virtual void SetYPositionValue(System.Decimal valueToSet)
		{
			SetYPositionValue(valueToSet, true, true);
		}

		public virtual void SetYPositionValue(System.Decimal valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_yPosition != Math.Round(valueToSet, 17))
			{
				_yPosition = Math.Round(valueToSet, 17);

				OnPropertyChanged("YPosition", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The YPosition property of the SlamcorePedestrianDetection DataObject</summary>
        public virtual System.Decimal YPosition 
		{
			get	{ return _yPosition;}
			
			
			set
			{
				SetYPositionValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<SlamcorePedestrianDetectionDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is SlamcorePedestrianDetectionDataObject))
				return false;

			var p = (SlamcorePedestrianDetectionDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.SlamcoreDeviceId == p.SlamcoreDeviceId;
			fieldsComparison &= this.XPosition == p.XPosition;
			fieldsComparison &= this.YPosition == p.YPosition;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcorePedestrianDetectionCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public SlamcorePedestrianDetectionCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public SlamcorePedestrianDetectionCollectionContainer()
		{
		}
		
		public SlamcorePedestrianDetectionCollectionContainer Construct(DataObjectCollection<SlamcorePedestrianDetectionDataObject> slamcorePedestrianDetectionItems)
        {
            if (slamcorePedestrianDetectionItems == null)
                return this;
				
			this.PrimaryKeys = slamcorePedestrianDetectionItems.Select(c => c.PrimaryKey).ToList();
            if (slamcorePedestrianDetectionItems.ObjectsDataSet == null)
            {
                slamcorePedestrianDetectionItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = slamcorePedestrianDetectionItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = slamcorePedestrianDetectionItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<SlamcorePedestrianDetectionDataObject> ExtractSlamcorePedestrianDetectionItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<SlamcorePedestrianDetectionDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<SlamcorePedestrianDetectionDataObject>(typeof(SlamcorePedestrianDetectionDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcorePedestrianDetectionContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public SlamcorePedestrianDetectionContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual SlamcorePedestrianDetectionContainer Construct(SlamcorePedestrianDetectionDataObject slamcorePedestrianDetection, bool includeDirtyObjectsOnly = false)
		{
            if (slamcorePedestrianDetection == null)
                return this;

			this.PrimaryKey = slamcorePedestrianDetection.PrimaryKey;
			
            if (slamcorePedestrianDetection.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(slamcorePedestrianDetection);
            }

			if(slamcorePedestrianDetection.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity SlamcorePedestrianDetection", "Unable to set a dataset to the entity. Container may not be initialized", "SlamcorePedestrianDetectionDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : Slamcore Pedestrian Detection");
			}

			if(slamcorePedestrianDetection.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in SlamcorePedestrianDetectionDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "SlamcorePedestrianDetectionDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in Slamcore Pedestrian DetectionDataObject");
			}
			this.InternalObjectId = (int) slamcorePedestrianDetection.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? slamcorePedestrianDetection.ObjectsDataSet.CloneDirtyObjects() : slamcorePedestrianDetection.ObjectsDataSet;

			return this;
		}
		
		public SlamcorePedestrianDetectionDataObject ExtractSlamcorePedestrianDetection()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<SlamcorePedestrianDetectionDataObject>(typeof(SlamcorePedestrianDetectionDataObject), InternalObjectId);
			
			return result;
        }	
	}

}