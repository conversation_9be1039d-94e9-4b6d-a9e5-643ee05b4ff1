﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMSlamcoreAPIKey" 
		table="[SlamcoreAPIKey]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="APIKey" >
            <column name="`APIKey`" sql-type="nvarchar (1000) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="APIKeyDisplay" >
            <column name="`APIKeyDisplay`" sql-type="nvarchar (1000) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 

 

		<one-to-one 
			name="SlamcoreDevice" 
			class="ORMSlamcoreDevice" 
			lazy="no-proxy"
			property-ref = "SlamcoreAPIKey"
		>
		</one-to-one>



    </class> 

</hibernate-mapping>