﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
//////////////////////////////////////////////////////////////////////////////////////////// 

{
  "MainConnectionString": "data source=TO BE DEFINED IN appsettings.ENVIRONNEMENT.json",
  "SecretKey": "FOR DEV ONLY - DON'T USE IN PRODUCTION - TO BE DEFINED IN appsettings.ENVIRONNEMENT.json",
  "Version": 6765,
  "ShowExceptionDetails" :  "false",
  "SwaggerEnabled" : "false",
  "ActivateClientLogging": "false",
  "StorageContainer" : "files",
  // SessionTokenTimeout: lifetime of tokens issued with security provider, in minutes
  "SessionTokenTimeout": 60,
  // TokenExpirationWarningDelay: minutes before token expiration to show warning message to users
  "TokenExpirationWarningDelay": 5,
  // TokenRenewalAdvanceMinutes: minutes before token expiration to trigger automatic renewal
  "TokenRenewalAdvanceMinutes": 5,
	// EmailTokenExpiry: time in minutes after any subscriber token expires
  "EmailTokenExpiry" : 15,
 
  "AnonymousAccessFolders": "Styles,Membership,WebApp,Public",
  // Import settings
  "SkipCalculatedFieldCalculationsDuringImport" : "true",

  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}