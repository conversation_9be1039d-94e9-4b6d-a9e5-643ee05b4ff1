﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using NHibernate;
using NHibernate.Linq;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataProviders.Database
{
    public class SlamcorePedestrianDetectionDataProvider : DatabaseDataProvider<SlamcorePedestrianDetectionDataObject>
    {
		public SlamcorePedestrianDetectionDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<SlamcorePedestrianDetectionDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, INHibernateSessionController nhibernateSessionController) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction, nhibernateSessionController)
		{
		}

	    // filterExpression is used to filter data, when filter is statically known. dynamicFilterExpression is used for dynamic filtering, when filter is not known at compile time. Both can be used at the same time
        protected override async Task<int> DoCountFromDatabaseAsync(
			LambdaExpression filterExpression, 
			string dynamicFilterExpression, 
			object[] dynamicFilterArguments, 
			IObjectsDataSet context, 
			Parameters parameters)
        {
			var query = await QueryAsync(dynamicFilterExpression, dynamicFilterArguments, filterExpression);

			// Put the query in the transaction parameters so that custom data provider OnAfterCount extensions can see the result set
			if (parameters != null)
				 parameters[ParameterKeys.DataProviderCountQuery] = query;

			return await query.CountAsync();
        }

        protected override async Task DoDeleteFromDatabaseAsync(
			SlamcorePedestrianDetectionDataObject entity, 
			LambdaExpression filterExpression,			// no longer used here - handled higher up the stack by dataprovider extensions
			IObjectsDataSet context, 
			Parameters parameters)
        {
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);
			var toDelete = entity.ToORMEntity();
			await session.DeleteAsync(toDelete);
        }

        // securityFilterExpression is for security : check if the user is allowed to read the entity
        protected override async Task<SlamcorePedestrianDetectionDataObject> DoGetFromDatabaseAsync(
			SlamcorePedestrianDetectionDataObject entity, 
			LambdaExpression securityFilterExpression, 
			List<string> includes, 
			IObjectsDataSet context, 
			Parameters parameters)
        {
			var securityFilter = securityFilterExpression as Expression<Func<ORMSlamcorePedestrianDetection, bool>>;

			var query = await QueryAsync(
				e => e.Id == entity.Id, 
				null,
				null,
				securityFilter, 
				throwIfAccessDenied: true);

			// Normal (default generated) behaviour is: 
			// includes not treated by orm but with dispatcher mechanism to allow for proper security and extension calls on all objects
			// But the following allows custom implementations to prefetch associations
			_serviceProvider.GetService<IPrefetch<ORMSlamcorePedestrianDetection>>()?.Fetch(query, includes, parameters);

			var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
			var result = (await query.FirstOrDefaultAsync())?.ToDataObject(dataset, _serviceProvider, _threadContext, _nhibernateSessionController, _dataProviderTransaction) as SlamcorePedestrianDetectionDataObject;

			return result;
        }

        // filterExpression is used to filter data, when filter is statically known. dynamicFilterExpression is used for dynamic filtering, when filter is not known at compile time. Both can be used at the same time
        protected override async Task<DataObjectCollection<SlamcorePedestrianDetectionDataObject>> DoGetCollectionFromDatabaseAsync(
			LambdaExpression filterExpression, 
			string dynamicFilterExpression, 
			object[] dynamicFilterArguments, 
			string orderByPredicate, 
			int pageNumber, 
			int pageSize, 
			List<string> includes, 
			IObjectsDataSet context, 
			Parameters parameters)
        {
			var query = await QueryAsync(dynamicFilterExpression, dynamicFilterArguments, filterExpression);

			if (!String.IsNullOrEmpty(orderByPredicate))
				query = query.OrderBy(orderByPredicate);

			// Normal (default generated) behaviour is: 
			// includes not treated by orm but with dispatcher mechanism to allow for proper security and extension calls on all objects
			// But the following allows custom implementations to prefetch associations
			_serviceProvider.GetService<IPrefetch<ORMSlamcorePedestrianDetection>>()?.Fetch(query, includes, parameters);

			// Do Paging (unless late-paging option is enabled)
			if (!ParameterKeys.IsOptionEnabled(parameters, ParameterKeys.DataProviderGetCollectionLatePaging))
			{
				if (pageNumber != 0 && pageSize > 0)
				{
					query = query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
				}
			}
 
			var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
			var collection = await query.Select(x => x.ToDataObject(dataset, _serviceProvider, _threadContext, _nhibernateSessionController, _dataProviderTransaction)).Cast<SlamcorePedestrianDetectionDataObject>().ToListAsync();
			return new DataObjectCollection<SlamcorePedestrianDetectionDataObject>(collection, dataset);
        }

        protected override async Task<SlamcorePedestrianDetectionDataObject> DoSaveToDatabaseAsync(
			SlamcorePedestrianDetectionDataObject entity, 
			List<string> includes, 
			IObjectsDataSet context,
			Parameters parameters)
        {
			return await base.DoSaveToDatabaseAsync(entity, includes, context, parameters);
        }

		private async Task<IQueryable<ORMSlamcorePedestrianDetection>> QueryAsync(
			string dynamicFilterExpression,
			object[] dynamicFilterArguments,
			LambdaExpression compiledFilter)
		{
			return await QueryAsync(null, dynamicFilterExpression, dynamicFilterArguments, compiledFilter);
		}

		private async Task<IQueryable<ORMSlamcorePedestrianDetection>> QueryAsync(
			Expression<Func<ORMSlamcorePedestrianDetection, bool>> selectPredicate,
			string dynamicFilterExpression,
			object[] dynamicFilterArguments,
			LambdaExpression compiledFilter,
			bool throwIfAccessDenied = false)
		{
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);

			IQueryable<ORMSlamcorePedestrianDetection> query = null;

			if (!String.IsNullOrWhiteSpace(dynamicFilterExpression))
			{
				Expression<Func<ORMSlamcorePedestrianDetection, bool>> dynamicPredicate = null;
 
				query = session.Query<ORMSlamcorePedestrianDetection>();

				if (selectPredicate != null)
					query = query.Where(selectPredicate);

				if (dynamicFilterArguments != null) 
				{
					for (int i = 0; i < dynamicFilterArguments.Length; i++)
					{
						var argType = dynamicFilterArguments[i].GetType();
						if (argType.IsGenericType && argType.GetGenericTypeDefinition() == typeof(NullableType<>))
						{
							dynamicFilterExpression = dynamicFilterExpression.Replace($"@{i}", $"@{i}.Value");
						}
					}
				}

				dynamicPredicate = System.Linq.Dynamic.DynamicExpression.ParseLambda<ORMSlamcorePedestrianDetection, bool>(dynamicFilterExpression, dynamicFilterArguments);

				if (dynamicPredicate != null)
					query = query.Where(dynamicPredicate);
			}
			else
			{
				query = session.Query<ORMSlamcorePedestrianDetection>();

				if (selectPredicate != null)
					query = query.Where(selectPredicate);
			}

			return await ApplyCompileTimeFilterExpressionAsync(query, compiledFilter, throwIfAccessDenied);
		}

		private async Task<IQueryable<ORMSlamcorePedestrianDetection>> ApplyCompileTimeFilterExpressionAsync(
			IQueryable<ORMSlamcorePedestrianDetection> query, 
			LambdaExpression filterExpression, 
			bool throwIfAccessDenied)
		{
			// Apply filter, and if it's a security filter throw exception if no results after applying it
			var filter = filterExpression as Expression<Func<ORMSlamcorePedestrianDetection, bool>>;

			if (filter != null)
			{
				if (query.Any())
				{
					var filtered = query.Where(filter);

					if (!filtered.Any())
					{
						if (throwIfAccessDenied)
						{
							await _authentication.ThrowAccessDeniedAsync(new GOServerException("accessDenied", _authentication.ExplainAccessDenied(EntityAccessEnum.READ, "Slamcore Pedestrian Detection")));
						}
					}

					query = filtered;
				}
			}

			return query;
		}
    }
}
