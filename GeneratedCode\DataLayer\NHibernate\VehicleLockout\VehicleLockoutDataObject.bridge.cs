﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge DataObject -> ORMEntity
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	public partial class VehicleLockoutDataObject
	{
		protected IThreadContext _threadContext => _serviceProvider.GetRequiredService<IThreadContext>();
		protected INHibernateSessionController _nhibernateSessionController => _serviceProvider.GetRequiredService<INHibernateSessionController>();

		public override IORMEntity ToORMEntity()
		{
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);

			var result = new ORMVehicleLockout(); 

			CopyTo(result, session);

			// Ensure no copy of result (instance with same key) in session
			Evict(result, session);

			return result;
		}

		protected void CopyTo(ORMVehicleLockout x, NHibernate.ISession session)
		{
			x.Id = Id;
			x.Note = Note?.Truncate(100);
			x.Comment = Comment?.Truncate(100);
			x.LockoutTime = LockoutTime;
			x.UnlockDateTime = UnlockDateTime;
			x.Reason = (int)Reason;
			x.RealImpact = (int?)RealImpact;
				
			x.GOUser = this.GOUser != null ? session.Load<ORMGOUser>(this.GOUser.Id) : (this.GOUserId != null ? session.Load<ORMGOUser>(this.GOUserId) : null);
			x.GOUserId = this.GOUser != null ? this.GOUser.Id : GOUserId; 
				
			x.Vehicle = this.Vehicle != null ? session.Load<ORMVehicle>(this.Vehicle.Id) : (this.VehicleId != null ? session.Load<ORMVehicle>(this.VehicleId) : null);
			x.VehicleId = this.Vehicle != null ? this.Vehicle.Id : VehicleId; 
				
			x.Session = this.Session != null ? session.Load<ORMSession>(this.Session.Id) : (this.SessionId != null ? session.Load<ORMSession>(this.SessionId) : null);
			x.SessionId = this.Session != null ? this.Session.Id : SessionId; 
				
			x.Driver = this.Driver != null ? session.Load<ORMDriver>(this.Driver.Id) : (this.DriverId != null ? session.Load<ORMDriver>(this.DriverId) : null);
			x.DriverId = this.Driver != null ? this.Driver.Id : DriverId; 
		}
 
		private void Evict(ORMVehicleLockout result, NHibernate.ISession session)
		{
			foreach (var entity in session.GetSessionImplementation().PersistenceContext.EntitiesByKey.Values.ToArray())
			{
				var evictee = entity as ORMVehicleLockout;

				if (evictee != null && evictee.Id == result.Id)
				{
					session.Evict(evictee);
				}
			}
		}

	}
}