﻿{
  "entityName": "All Vehicle Unlocks View",
  "entityNamePlural": "All Vehicle Unlocks Views",   "entityDescription": "This entity represents a All Vehicle Unlocks View",
  "fields": {
    "Dealer": {
        "displayName": "Dealer", 
        "description": "Access group to site"
    },
    "DealerId": {
        "displayName": "DealerId", 
        "description": "Foreign Key"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "LockoutDateTime": {
        "displayName": "LockoutDateTime", 
        "description": "LockoutDateTime"
    },
    "TimezoneAdjustedLockoutDatetime": {
        "displayName": "Timezone Adjusted Lockout Datetime", 
        "description": "Timezone Adjusted Lockout Datetime"
    },
    "TimezoneAdjustedLockoutDatetimeDisplay": {
        "displayName": "TimezoneAdjustedLockoutDatetimeDisplay", 
        "description": "TimezoneAdjustedLockoutDatetimeDisplay"
    },
    "TimezoneAdjustedUnlockDatetime": {
        "displayName": "Timezone Adjusted Unlock Datetime", 
        "description": "Timezone Adjusted Unlock Datetime"
    },
    "TimezoneAdjustedUnlockDatetimeDisplay": {
        "displayName": "TimezoneAdjustedUnlockDatetimeDisplay", 
        "description": "TimezoneAdjustedUnlockDatetimeDisplay"
    },
    "UnlockDateTime": {
        "displayName": "UnlockDateTime", 
        "description": "UnlockDateTime"
    },
    "UnlockedBy": {
        "displayName": "UnlockedBy", 
        "description": "UnlockedBy"
    },
    "VehicleLockout": {
        "displayName": "Vehicle Lockout", 
        "description": "Vehicle Lockout"
    },
    "VehicleLockoutId": {
        "displayName": "VehicleLockoutId", 
        "description": "Foreign Key"
    }
  }
} 