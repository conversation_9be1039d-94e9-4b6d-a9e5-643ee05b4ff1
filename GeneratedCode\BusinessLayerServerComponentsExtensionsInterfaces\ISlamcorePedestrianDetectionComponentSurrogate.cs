﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;


namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// SlamcorePedestrianDetectionComponent Component
	///  
	/// </summary>
	public partial interface ISlamcorePedestrianDetectionComponentSurrogate 
    {
		/// <summary>
        /// Export Method
		///  
		/// </summary>
		/// <param name="filterPredicate"></param>
        /// <returns></returns>		
		/// <param name="filterParameters"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ExportJobStatusDataObject> ExportAsync(System.String filterPredicate, System.String filterParameters, Dictionary<string, object> parameters = null);
	}
}
