﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge ORMEntity -> DataObject
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	[Serializable]
	public class ORMSlamcoreDeviceConnectionView : IORMEntity 
	{
		public virtual System.Guid Id { get; set; }
 
		///
		/// All Persistent non-key fields
		///
		public virtual Nullable<System.Int16> OnlineDevices { get; set; }
		public virtual Nullable<System.Int16> TotalDevices { get; set; }
		public virtual Nullable<System.Int16> Offline { get; set; }
 
		///
		/// PK-Side one-to-one relations
		///
 
		///
		/// All FK-Side Relations
		///
		public virtual ORMSlamcoreDevice SlamcoreDevice { get; set; }
		public virtual Nullable<System.Guid> SlamcoreDeviceId { get; set; }

	
 
		///
		/// PK-Side one-to-many relations
		///
 
		///
		/// Bridge to DataObject
		///
		public virtual IDataObject ToDataObject(IObjectsDataSet dataset, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
            var session = nHibernateSessionController.GetCurrentSession(threadContext);
            session.Evict(this);

			var x = serviceProvider.GetRequiredService<SlamcoreDeviceConnectionViewDataObject>();

 
			SetProperties(x);

			x.IsDirty = x.IsNew = x.IsMarkedForDeletion = false;

			x.ObjectsDataSet = dataset;
			x.ObjectsDataSet.AddObjectIfDoesNotExist(x);

			// Deep-map prefetch relations
			if (PrefetchAssociations.HasPrefetchForEntity("SlamcoreDeviceConnectionView", dataProviderTransaction?.Parameters))
			{
				SetRelations(x, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction);
			}

			return x;
		}

		protected void SetProperties(SlamcoreDeviceConnectionViewDataObject x)
		{
			x.SetIdValue(Id, false, false);
			x.SetOnlineDevicesValue(OnlineDevices, false, false);
			x.SetTotalDevicesValue(TotalDevices, false, false);
			x.SetOfflineValue(Offline, false, false);
			x.SetSlamcoreDeviceIdValue(this.SlamcoreDeviceId, false, false);
		}

		protected void SetRelations(SlamcoreDeviceConnectionViewDataObject x, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
			var prefetches = PrefetchAssociations.Get("SlamcoreDeviceConnectionView", dataProviderTransaction?.Parameters);

			if (prefetches.Contains("SlamcoreDevice") && this.SlamcoreDevice != null)
			{
				var slamcoreDevice = x.ObjectsDataSet.GetObject(new SlamcoreDeviceDataObject((System.Guid)this.SlamcoreDevice.Id) { IsNew = false });

				if (slamcoreDevice == null)
					slamcoreDevice = this.SlamcoreDevice.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SlamcoreDeviceDataObject;

				x.SetSlamcoreDeviceValue(slamcoreDevice);
			}

		}
		
		// For database filtering, nothing IsNew or IsDirty, but these stubs needed for ORM security predicates to compile
		public virtual bool IsNew => false;
		public virtual bool IsDirty => false;

	}
}