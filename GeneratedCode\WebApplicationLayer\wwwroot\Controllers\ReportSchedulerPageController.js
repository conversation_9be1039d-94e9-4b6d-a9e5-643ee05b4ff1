﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

(function () {
	// 
	FleetXQ.Web.Controllers.ReportSchedulerPageController = function(applicationController) {
		var self = this;
		this.subscriptions = [];
		// store all subscriptions in this array, to unsubscribe on release.
		
		this._objectType = "ReportSchedulerPage";

		this.applicationController = applicationController;
		this.ObjectsDataSet = applicationController.ObjectsDataSet;
		this.contextId = [this.applicationController.getNextContextId()];
		this.customController = undefined;

		// Integrate custom code if any
		if (FleetXQ.Web.Controllers.ReportSchedulerPageControllerCustom !== undefined) {
		    this.customController = new FleetXQ.Web.Controllers.ReportSchedulerPageControllerCustom(self);
		};

		// Initialize View Models and Data Stores
		
			this.ReportSubscriptionGrid1ViewModel = new FleetXQ.Web.ViewModels.ReportSubscriptionGrid1ViewModel(this, $("#ReportSubscriptionGrid1"), null, null, this.contextId);		
		this.ReportSubscriptionGrid1ViewModel.StatusData.ShowTitle(false);		
		this.ReportSubscriptionGrid1ViewModel.include = "auto-include-id-4d042bf5-2013-45be-8de9-2574f1ccec32-94c0accd-d3a9-4c7d-ab5d-eee588fcef95";	
		this.customPageTitle = ko.observable(null);

		this.iconToUse = ko.observable(null);

		this.pageTitle = ko.pureComputed (function() {
			if (self.customPageTitle()) {
				return self.customPageTitle();
			}
			return "Report Scheduler";
		});

		this.pageTitleParams = ko.observable({});
		
		this.showGoBackBadge = ko.observable(false);
		this.goBackBadgeLabel = ko.observable("pages/ReportSchedulerPage:navigation.BackNavigationBadge");
		this.showItemCountBadge = ko.observable(false);
		this.isLoading = ko.observable(false);
		this.itemCount = ko.pureComputed(function() {
			return null;
		}); 
		this.goBack = function () {
			if(self.IsInEditMode && self.IsInEditMode()) {
				if(!confirm(FleetXQ.Web.Messages.i18n.t('messages.unsavedChanges')))
					return;
				
				// Cancel edit mode on current page controller
				if (self.CancelEdit) {
					self.CancelEdit(false);
				}					
			}

};

		this.subscriptions.push(this.ReportSubscriptionGrid1ViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnReportSubscriptionGrid1ViewModelIsBusyChanged(newValue); }));
		
		this.IsInEditMode = function() {

			return  (self.ReportSubscriptionGrid1ViewModel.StatusData.DisplayMode && self.ReportSubscriptionGrid1ViewModel.StatusData.DisplayMode() == 'edit');
		};

		this.CancelEdit = function(isCommandCall) {
			if (self.ReportSubscriptionGrid1ViewModel.CancelEdit) {
				self.ReportSubscriptionGrid1ViewModel.CancelEdit(isCommandCall);
			}
		};

		// Events Handlers
		this.OnReportSubscriptionGrid1ViewModelIsBusyChanged = function (newValue) {
		};

		this.initialize = function() {
			if (FleetXQ.Web.Controllers.Custom && FleetXQ.Web.Controllers.Custom.getPageTitle) {
				self.customPageTitle(FleetXQ.Web.Controllers.Custom.getPageTitle(self));
			}

	
			// Call custom initialize if defined
			if (self.customController !== undefined && self.customController.initialize !== undefined) {
			    self.customController.initialize();
			}
		// Initial data load for all source elements (no dependencies)
			if (!GO.Filter.hasUrlFilter(self.ReportSubscriptionGrid1ViewModel.FILTER_NAME, self.ReportSubscriptionGrid1ViewModel)) {
				self.ReportSubscriptionGrid1ViewModel.LoadReportSubscriptionObjectCollection();
			}
		};
		this.initialize();



		this.release = function() {
			// unsubscribe
			for(var i = 0;i <  self.subscriptions.length;i++) {
				self.subscriptions[i].dispose();
			}
			self.subscriptions = [];
			self.ObjectsDataSet.cleanContext(self.contextId);
			
			self.ReportSubscriptionGrid1ViewModel.release();
			self.ReportSubscriptionGrid1ViewModel = null;
		};


		return self;
	};		
	
	if (window.ApplicationSourceHandler)
		window.ApplicationSourceHandler.onSourceLoaded("/Controllers/ReportSchedulerPageController.js");
} ());
