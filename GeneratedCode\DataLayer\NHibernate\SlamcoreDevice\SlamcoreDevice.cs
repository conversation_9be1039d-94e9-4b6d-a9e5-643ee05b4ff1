﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge ORMEntity -> DataObject
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	[Serializable]
	public class ORMSlamcoreDevice : IORMEntity 
	{
		public virtual System.Guid Id { get; set; }
 
		///
		/// All Persistent non-key fields
		///
		public virtual System.String IPAddress { get; set; }
		public virtual System.String Name { get; set; }
		public virtual System.String SerialNo { get; set; }
		public virtual Nullable<System.DateTime> LastConnectedDateTime { get; set; }
		public virtual System.Int32 Status { get; set; }
		public virtual System.Int32 UpdateRate { get; set; }
 
		///
		/// PK-Side one-to-one relations
		///
 
		///
		/// All FK-Side Relations
		///
		public virtual ORMSlamcoreAwareAuthenticationDetails SlamcoreAwareAuthenticationDetails { get; set; }
		public virtual Nullable<System.Guid> SlamcoreAwareAuthenticationDetailsId { get; set; }

		public virtual ORMVehicle Vehicle { get; set; }
		public virtual Nullable<System.Guid> VehicleId { get; set; }

		public virtual ORMSlamcoreAPIKey SlamcoreAPIKey { get; set; }
		public virtual Nullable<System.Guid> SlamcoreAPIKeyId { get; set; }

		public virtual ORMCustomer Customer { get; set; }
		public virtual System.Guid CustomerId { get; set; }

	
 
		///
		/// PK-Side one-to-many relations
		///
		public virtual IList<ORMSlamcoreDeviceHistory> SlamcoreDeviceHistoryItems { get; set; } = new List<ORMSlamcoreDeviceHistory>(); 
		public virtual IList<ORMSlamcorePedestrianDetection> SlamcorePedestrianDetectionItems { get; set; } = new List<ORMSlamcorePedestrianDetection>(); 
		public virtual IList<ORMSlamcoreDeviceConnectionView> SlamcoreDeviceConnectionViewItems { get; set; } = new List<ORMSlamcoreDeviceConnectionView>(); 
		public virtual IList<ORMVehicleSlamcoreLocationHistory> VehicleSlamcoreLocationHistoryItems { get; set; } = new List<ORMVehicleSlamcoreLocationHistory>(); 
 
		///
		/// Bridge to DataObject
		///
		public virtual IDataObject ToDataObject(IObjectsDataSet dataset, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
            var session = nHibernateSessionController.GetCurrentSession(threadContext);
            session.Evict(this);

			var x = serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>();

 
			SetProperties(x);

			x.IsDirty = x.IsNew = x.IsMarkedForDeletion = false;

			x.ObjectsDataSet = dataset;
			x.ObjectsDataSet.AddObjectIfDoesNotExist(x);

			// Deep-map prefetch relations
			if (PrefetchAssociations.HasPrefetchForEntity("SlamcoreDevice", dataProviderTransaction?.Parameters))
			{
				SetRelations(x, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction);
			}

			return x;
		}

		protected void SetProperties(SlamcoreDeviceDataObject x)
		{
			x.SetIdValue(Id, false, false);
			x.SetIPAddressValue(IPAddress, false, false);
			x.SetNameValue(Name, false, false);
			x.SetSerialNoValue(SerialNo, false, false);
			x.SetLastConnectedDateTimeValue(LastConnectedDateTime, false, false);
			x.SetStatusValue((SlamcoreStatusEnum)Status, false, false);
			x.SetUpdateRateValue((SlamcoreUpdateRateEnum)UpdateRate, false, false);
			x.SetSlamcoreAwareAuthenticationDetailsIdValue(this.SlamcoreAwareAuthenticationDetailsId, false, false);
			x.SetVehicleIdValue(this.VehicleId, false, false);
			x.SetSlamcoreAPIKeyIdValue(this.SlamcoreAPIKeyId, false, false);
			x.SetCustomerIdValue(this.CustomerId, false, false);
		}

		protected void SetRelations(SlamcoreDeviceDataObject x, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
			var prefetches = PrefetchAssociations.Get("SlamcoreDevice", dataProviderTransaction?.Parameters);

			if (prefetches.Contains("SlamcoreAPIKey") && this.SlamcoreAPIKey != null)
			{
				var slamcoreAPIKey = x.ObjectsDataSet.GetObject(new SlamcoreAPIKeyDataObject((System.Guid)this.SlamcoreAPIKey.Id) { IsNew = false });

				if (slamcoreAPIKey == null)
					slamcoreAPIKey = this.SlamcoreAPIKey.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SlamcoreAPIKeyDataObject;

				x.SetSlamcoreAPIKeyValue(slamcoreAPIKey);
			}

			if (prefetches.Contains("SlamcorePedestrianDetectionItems") && this.SlamcorePedestrianDetectionItems.Count > 0)
			{
				var iter = this.SlamcorePedestrianDetectionItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var slamcorePedestrianDetectionItemsItem = x.ObjectsDataSet.GetObject(new SlamcorePedestrianDetectionDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (slamcorePedestrianDetectionItemsItem == null)
						slamcorePedestrianDetectionItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SlamcorePedestrianDetectionDataObject;

					x.SlamcorePedestrianDetectionItems.Add(slamcorePedestrianDetectionItemsItem);
				}
			}

			if (prefetches.Contains("SlamcoreAwareAuthenticationDetails") && this.SlamcoreAwareAuthenticationDetails != null)
			{
				var slamcoreAwareAuthenticationDetails = x.ObjectsDataSet.GetObject(new SlamcoreAwareAuthenticationDetailsDataObject((System.Guid)this.SlamcoreAwareAuthenticationDetails.Id) { IsNew = false });

				if (slamcoreAwareAuthenticationDetails == null)
					slamcoreAwareAuthenticationDetails = this.SlamcoreAwareAuthenticationDetails.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SlamcoreAwareAuthenticationDetailsDataObject;

				x.SetSlamcoreAwareAuthenticationDetailsValue(slamcoreAwareAuthenticationDetails);
			}

			if (prefetches.Contains("SlamcoreDeviceConnectionViewItems") && this.SlamcoreDeviceConnectionViewItems.Count > 0)
			{
				var iter = this.SlamcoreDeviceConnectionViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var slamcoreDeviceConnectionViewItemsItem = x.ObjectsDataSet.GetObject(new SlamcoreDeviceConnectionViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (slamcoreDeviceConnectionViewItemsItem == null)
						slamcoreDeviceConnectionViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SlamcoreDeviceConnectionViewDataObject;

					x.SlamcoreDeviceConnectionViewItems.Add(slamcoreDeviceConnectionViewItemsItem);
				}
			}

			if (prefetches.Contains("VehicleSlamcoreLocationHistoryItems") && this.VehicleSlamcoreLocationHistoryItems.Count > 0)
			{
				var iter = this.VehicleSlamcoreLocationHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleSlamcoreLocationHistoryItemsItem = x.ObjectsDataSet.GetObject(new VehicleSlamcoreLocationHistoryDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleSlamcoreLocationHistoryItemsItem == null)
						vehicleSlamcoreLocationHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleSlamcoreLocationHistoryDataObject;

					x.VehicleSlamcoreLocationHistoryItems.Add(vehicleSlamcoreLocationHistoryItemsItem);
				}
			}

			if (prefetches.Contains("SlamcoreDeviceHistoryItems") && this.SlamcoreDeviceHistoryItems.Count > 0)
			{
				var iter = this.SlamcoreDeviceHistoryItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var slamcoreDeviceHistoryItemsItem = x.ObjectsDataSet.GetObject(new SlamcoreDeviceHistoryDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (slamcoreDeviceHistoryItemsItem == null)
						slamcoreDeviceHistoryItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SlamcoreDeviceHistoryDataObject;

					x.SlamcoreDeviceHistoryItems.Add(slamcoreDeviceHistoryItemsItem);
				}
			}

			if (prefetches.Contains("Vehicle") && this.Vehicle != null)
			{
				var vehicle = x.ObjectsDataSet.GetObject(new VehicleDataObject((System.Guid)this.Vehicle.Id) { IsNew = false });

				if (vehicle == null)
					vehicle = this.Vehicle.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleDataObject;

				x.SetVehicleValue(vehicle);
			}

			if (prefetches.Contains("Customer") && this.Customer != null)
			{
				var customer = x.ObjectsDataSet.GetObject(new CustomerDataObject((System.Guid)this.Customer.Id) { IsNew = false });

				if (customer == null)
					customer = this.Customer.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerDataObject;

				x.SetCustomerValue(customer);
			}

		}
		
		// For database filtering, nothing IsNew or IsDirty, but these stubs needed for ORM security predicates to compile
		public virtual bool IsNew => false;
		public virtual bool IsDirty => false;

	}
}