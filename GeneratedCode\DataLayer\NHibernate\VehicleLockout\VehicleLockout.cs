﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge ORMEntity -> DataObject
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	[Serializable]
	public class ORMVehicleLockout : IORMEntity 
	{
		public virtual System.Guid Id { get; set; }
 
		///
		/// All Persistent non-key fields
		///
		public virtual System.String Note { get; set; }
		public virtual System.String Comment { get; set; }
		public virtual System.DateTime LockoutTime { get; set; }
		public virtual System.DateTime UnlockDateTime { get; set; }
		public virtual System.Int32 Reason { get; set; }
		public virtual Nullable<System.Int32> RealImpact { get; set; }
 
		///
		/// PK-Side one-to-one relations
		///
 
		///
		/// All FK-Side Relations
		///
		public virtual ORMGOUser GOUser { get; set; }
		public virtual Nullable<System.Guid> GOUserId { get; set; }

		public virtual ORMVehicle Vehicle { get; set; }
		public virtual Nullable<System.Guid> VehicleId { get; set; }

		public virtual ORMSession Session { get; set; }
		public virtual Nullable<System.Guid> SessionId { get; set; }

		public virtual ORMDriver Driver { get; set; }
		public virtual Nullable<System.Guid> DriverId { get; set; }

	
 
		///
		/// PK-Side one-to-many relations
		///
		// public virtual IList<ORMAllVehicleUnlocksView> AllVehicleUnlocksViewItems { get; set; } = new List<ORMAllVehicleUnlocksView>();AllVehicleUnlocksView is not mapped to the database 
 
		///
		/// Bridge to DataObject
		///
		public virtual IDataObject ToDataObject(IObjectsDataSet dataset, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
            var session = nHibernateSessionController.GetCurrentSession(threadContext);
            session.Evict(this);

			var x = serviceProvider.GetRequiredService<VehicleLockoutDataObject>();

 
			SetProperties(x);

			x.IsDirty = x.IsNew = x.IsMarkedForDeletion = false;

			x.ObjectsDataSet = dataset;
			x.ObjectsDataSet.AddObjectIfDoesNotExist(x);

			// Deep-map prefetch relations
			if (PrefetchAssociations.HasPrefetchForEntity("VehicleLockout", dataProviderTransaction?.Parameters))
			{
				SetRelations(x, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction);
			}

			return x;
		}

		protected void SetProperties(VehicleLockoutDataObject x)
		{
			x.SetIdValue(Id, false, false);
			x.SetNoteValue(Note, false, false);
			x.SetCommentValue(Comment, false, false);
			x.SetLockoutTimeValue(LockoutTime, false, false);
			x.SetUnlockDateTimeValue(UnlockDateTime, false, false);
			x.SetReasonValue((LockoutReasonEnum)Reason, false, false);
			x.SetRealImpactValue((Nullable<ImpactLockoutConfirmationEnum>)RealImpact, false, false);
			x.SetGOUserIdValue(this.GOUserId, false, false);
			x.SetVehicleIdValue(this.VehicleId, false, false);
			x.SetSessionIdValue(this.SessionId, false, false);
			x.SetDriverIdValue(this.DriverId, false, false);
		}

		protected void SetRelations(VehicleLockoutDataObject x, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
			var prefetches = PrefetchAssociations.Get("VehicleLockout", dataProviderTransaction?.Parameters);

			if (prefetches.Contains("Session") && this.Session != null)
			{
				var session = x.ObjectsDataSet.GetObject(new SessionDataObject((System.Guid)this.Session.Id) { IsNew = false });

				if (session == null)
					session = this.Session.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SessionDataObject;

				x.SetSessionValue(session);
			}

			if (prefetches.Contains("Driver") && this.Driver != null)
			{
				var driver = x.ObjectsDataSet.GetObject(new DriverDataObject((System.Guid)this.Driver.Id) { IsNew = false });

				if (driver == null)
					driver = this.Driver.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DriverDataObject;

				x.SetDriverValue(driver);
			}

			if (prefetches.Contains("Vehicle") && this.Vehicle != null)
			{
				var vehicle = x.ObjectsDataSet.GetObject(new VehicleDataObject((System.Guid)this.Vehicle.Id) { IsNew = false });

				if (vehicle == null)
					vehicle = this.Vehicle.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleDataObject;

				x.SetVehicleValue(vehicle);
			}

			if (prefetches.Contains("GOUser") && this.GOUser != null)
			{
				var gOUser = x.ObjectsDataSet.GetObject(new GOUserDataObject((System.Guid)this.GOUser.Id) { IsNew = false });

				if (gOUser == null)
					gOUser = this.GOUser.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as GOUserDataObject;

				x.SetGOUserValue(gOUser);
			}

		}
		
		// For database filtering, nothing IsNew or IsDirty, but these stubs needed for ORM security predicates to compile
		public virtual bool IsNew => false;
		public virtual bool IsDirty => false;

	}
}