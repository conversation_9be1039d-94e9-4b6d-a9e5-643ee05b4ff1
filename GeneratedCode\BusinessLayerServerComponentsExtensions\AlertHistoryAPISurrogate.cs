﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Http;

using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Web;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.HostedServices;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
 

using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using Parameters = System.Collections.Generic.Dictionary<string, object>;

namespace FleetXQ.BusinessLayer.Components.Server.Extensions
{
	public partial class AlertHistoryAPISurrogate : IAlertHistoryAPISurrogate
	{
		private readonly IServiceProvider _serviceProvider;

		private readonly IHttpContextAccessor _httpContextAccessor;
		private readonly IAlertHistoryAPI _alertHistoryAPI;
		private readonly IThreadContext _threadContext;
		private readonly IServicePath _servicePath;
		private readonly IUserIdentity _userIdentity;
	
		private readonly IEnumerable<IAlertHistoryAPIExtension> _extensionServices;

		public AlertHistoryAPISurrogate(IServiceProvider provider, IHttpContextAccessor httpContextAccessor, IAlertHistoryAPI alertHistoryAPI, IThreadContext threadContext, IServicePath servicePath, IUserIdentity userIdentity, IEnumerable<IAlertHistoryAPIExtension> extensionServices)
		{
  			_serviceProvider = provider;
			_httpContextAccessor = httpContextAccessor;
			_alertHistoryAPI = alertHistoryAPI;
			_threadContext = threadContext;
			_servicePath = servicePath;
			_userIdentity = userIdentity;
			_extensionServices = extensionServices;
	
		}

		public AlertHistoryAPI ComponentClass { get { return _alertHistoryAPI as AlertHistoryAPI; } }

		/// <summary>
		/// The extensions for implementors of IAlertHistoryAPIExtension to attach to
		/// </summary>
		public event Func<ComponentExtensionEventArgs, Task> OnBeforeCall;
		public event Func<ComponentExtensionEventArgs, Task> OnAfterCall;

		/// <summary>
		/// Thread-synchronisation gubbins
		/// </summary>
		private bool _extensionsInitialised;
		private Object _lock = new Object();
			
		private SemaphoreSlim _initializationSemaphore = new SemaphoreSlim(1, 1);

		/// <summary>
		/// InitializeExtensionsAsync()
		/// Find and initialise registered extensions of this interface
		/// Done in a thread-safe manner to allow for possibility that component may have been registered as singleton (controlled lifetime)
		/// </summary>
		private async Task InitializeExtensionsAsync()
		{
			await _initializationSemaphore.WaitAsync();
			try
			{
				// Ensure component extensions have been initialised
				if (_extensionsInitialised)
					return;

				if (_extensionsInitialised)
					return;
					
				foreach (var registration in _extensionServices)
				{
					await registration.InitAsync(this);
				}

				_extensionsInitialised = true;
			}
			finally
			{
				_initializationSemaphore.Release();
			}
		}

		/// <summary>
		/// Surrogate implementation of the Acknowledge operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> AcknowledgeAsync(System.Guid alertHistoryId, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "Acknowledge" });
			}
			var result = await ComponentClass.AcknowledgeAsync(alertHistoryId, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "Acknowledge" });
			}

			return result;
		}

		/// <summary>
		/// Surrogate implementation of the Resolve operation which provides OnBeforeCall and OnAfterCall extension points to the underlying (real) component call
		/// </summary>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> ResolveAsync(System.Guid alertHistoryId, Dictionary<string, object> parameters = null) 
		{
			await InitializeExtensionsAsync();

			if (OnBeforeCall != null)
			{
				await OnBeforeCall(new ComponentExtensionEventArgs() { OperationName = "Resolve" });
			}
			var result = await ComponentClass.ResolveAsync(alertHistoryId, parameters);
 
			if (OnAfterCall != null)
			{
				await OnAfterCall(new ComponentExtensionEventArgs() { OperationName = "Resolve" });
			}

			return result;
		}

	}
}
