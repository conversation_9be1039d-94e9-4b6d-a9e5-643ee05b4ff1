﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Features.Security
{
	/* 
		Summary of the permissioning logic:
		User is in at least one authorizing role for the resource => authorised
		All User roles are denied access to the resource => denied
		Else the default access setting for the resource is used. (so e.g. if no rules for this role, or if not all roles explicitly denied, then the default is used)
	*/
    public class VehicleAuthorizations : IVehicleAuthorizations
    {
		public VehicleAuthorizations(IServiceProvider provider, IEntityAuthorizationCache entityAuthorizationCache)
		{
  			_serviceProvider = provider;
			_entityAuthorizationCache = entityAuthorizationCache;
		}

		private readonly IServiceProvider _serviceProvider;
		private readonly IEntityAuthorizationCache _entityAuthorizationCache;

		public string EntityDisplayName => "Vehicle";

		public async Task<PermissionLevel> CanCreateAsync(IDataObject entity, UserClaims claims, IAuthorizations authorizations, ValueWrapper<string> messageWrapper, ValueWrapper<SecurityPredicate> predicateWrapper)
        {
			messageWrapper.Value = null;
			predicateWrapper.Value = null;
			int deniedRoleCount = 0;

			PermissionLevel result = PermissionLevel.NotSet;

			// fully authorised roles = all roles where the default rule is Authorized and there is no datafilter override
			var fullyAuthorizedRoles = new List<string> { "Administrator" };
			if (claims.Roles.Intersect(fullyAuthorizedRoles).Any())
			{
				return PermissionLevel.Authorized;
			}
 
			foreach (string role in claims.Roles)
			{
				if (role == "Customer")
				{
					Expression<Func<ORMVehicle, bool>> expr = v =>
(
        (((claims as AppUserClaims).WAL == null || (claims as AppUserClaims).WAL == 0) && v.DepartmentId == (claims as AppUserClaims).DepartmentId) || // Department Level
        ((claims as AppUserClaims).WAL == 1 && v.SiteId == (claims as AppUserClaims).SiteId) || // Site Level
        ((claims as AppUserClaims).WAL == 2 && v.CustomerId == (claims as AppUserClaims).CustomerId) || // Customer Level - can access all sites
        ((claims as AppUserClaims).WAL == 3 && (claims as AppUserClaims).AllowedSiteIds.Contains(v.SiteId) && ((claims as AppUserClaims).CanCreateVehicle == null || (claims as AppUserClaims).CanCreateVehicle.Value)) || // Access Group Level
        ((claims as AppUserClaims).WAL == 4 && v.SiteId == (claims as AppUserClaims).SiteId && (claims as AppUserClaims).AllowedDepartmentNames != null && (claims as AppUserClaims).AllowedDepartmentNames.Contains(v.Department.Name)) // Department Names Level
);
					Expression<Func<VehicleDataObject, bool>> datasetExpression = v =>
(
        (((claims as AppUserClaims).WAL == null || (claims as AppUserClaims).WAL == 0) && v.DepartmentId == (claims as AppUserClaims).DepartmentId) || // Department Level
        ((claims as AppUserClaims).WAL == 1 && v.SiteId == (claims as AppUserClaims).SiteId) || // Site Level
        ((claims as AppUserClaims).WAL == 2 && v.CustomerId == (claims as AppUserClaims).CustomerId) || // Customer Level - can access all sites
        ((claims as AppUserClaims).WAL == 3 && (claims as AppUserClaims).AllowedSiteIds.Contains(v.SiteId) && ((claims as AppUserClaims).CanCreateVehicle == null || (claims as AppUserClaims).CanCreateVehicle.Value)) || // Access Group Level
        ((claims as AppUserClaims).WAL == 4 && v.SiteId == (claims as AppUserClaims).SiteId && (claims as AppUserClaims).AllowedDepartmentNames != null && (claims as AppUserClaims).AllowedDepartmentNames.Contains(v.Department.Name)) // Department Names Level
);

					if (predicateWrapper.Value == null)
					{
						predicateWrapper.Value = new SecurityPredicate
						(
							filter: expr,
							datasetFilter: datasetExpression,
							message: "",
							includes: "",
							evaluateDataset: true,
							evaluateDatabase: true 
						);
					}
					else
					{
						predicateWrapper.Value.Filter = ((Expression<Func<ORMVehicle, bool>>)predicateWrapper.Value.Filter).Or(expr);
						predicateWrapper.Value.DatasetFilter = ((Expression<Func<VehicleDataObject, bool>>)predicateWrapper.Value.DatasetFilter).Or(datasetExpression);
						predicateWrapper.Value.AddIncludes("");
					}

					if (await authorizations.IsAuthorizedAsync(entity, predicateWrapper.Value, EntityAccessEnum.CREATE, messageWrapper))
					{
						return PermissionLevel.Authorized;
					}
					else
					{
						++deniedRoleCount;
					}
				}
				if (role == "DealerAdmin")
				{
					Expression<Func<ORMVehicle, bool>> expr = v => v.Customer.DealerId == (claims as AppUserClaims).DealerId && ((claims as AppUserClaims).AllowedCustomerId == null || (claims as AppUserClaims).AllowedCustomerId.Contains(v.CustomerId));
					Expression<Func<VehicleDataObject, bool>> datasetExpression = v => v.Customer.DealerId == (claims as AppUserClaims).DealerId && ((claims as AppUserClaims).AllowedCustomerId == null || (claims as AppUserClaims).AllowedCustomerId.Contains(v.CustomerId));

					if (predicateWrapper.Value == null)
					{
						predicateWrapper.Value = new SecurityPredicate
						(
							filter: expr,
							datasetFilter: datasetExpression,
							message: "",
							includes: "Customer",
							evaluateDataset: true,
							evaluateDatabase: true 
						);
					}
					else
					{
						predicateWrapper.Value.Filter = ((Expression<Func<ORMVehicle, bool>>)predicateWrapper.Value.Filter).Or(expr);
						predicateWrapper.Value.DatasetFilter = ((Expression<Func<VehicleDataObject, bool>>)predicateWrapper.Value.DatasetFilter).Or(datasetExpression);
						predicateWrapper.Value.AddIncludes("Customer");
					}

					if (await authorizations.IsAuthorizedAsync(entity, predicateWrapper.Value, EntityAccessEnum.CREATE, messageWrapper))
					{
						return PermissionLevel.Authorized;
					}
					else
					{
						++deniedRoleCount;
					}
				}
			}

			// all roles denied?
			if (deniedRoleCount == claims.Roles.Count())
			{
				return PermissionLevel.Denied;
			}

			return result;
		}

		public async Task<PermissionLevel> CanReadAsync(IDataObject entity, UserClaims claims, IAuthorizations authorizations, ValueWrapper<string> messageWrapper, ValueWrapper<SecurityPredicate> predicateWrapper)
        {
			messageWrapper.Value = null;
			predicateWrapper.Value = null;
			int deniedRoleCount = 0;

			PermissionLevel result = PermissionLevel.NotSet;

			// fully authorised roles = all roles where the default rule is Authorized and there is no datafilter override
			var fullyAuthorizedRoles = new List<string> { "Administrator" };
			if (claims.Roles.Intersect(fullyAuthorizedRoles).Any())
			{
				return PermissionLevel.Authorized;
			}
 
			foreach (string role in claims.Roles)
			{
				if (role == "Customer")
				{
					Expression<Func<ORMVehicle, bool>> expr = v =>
(
        (((claims as AppUserClaims).WAL == null || (claims as AppUserClaims).WAL == 0) && v.DepartmentId == (claims as AppUserClaims).DepartmentId) || // Department Level
        ((claims as AppUserClaims).WAL == 1 && v.SiteId == (claims as AppUserClaims).SiteId) || // Site Level
        ((claims as AppUserClaims).WAL == 2 && v.CustomerId == (claims as AppUserClaims).CustomerId) || // Customer Level - can access all sites
        ((claims as AppUserClaims).WAL == 3 && (claims as AppUserClaims).AllowedSiteIds.Contains(v.SiteId) && ((claims as AppUserClaims).CanViewVehicle == null || (claims as AppUserClaims).CanViewVehicle.Value)) || // Access Group Level
        ((claims as AppUserClaims).WAL == 4 && v.SiteId == (claims as AppUserClaims).SiteId && (claims as AppUserClaims).AllowedDepartmentNames != null && (claims as AppUserClaims).AllowedDepartmentNames.Contains(v.Department.Name)) // Department Names Level
);
					Expression<Func<VehicleDataObject, bool>> datasetExpression = v =>
(
        (((claims as AppUserClaims).WAL == null || (claims as AppUserClaims).WAL == 0) && v.DepartmentId == (claims as AppUserClaims).DepartmentId) || // Department Level
        ((claims as AppUserClaims).WAL == 1 && v.SiteId == (claims as AppUserClaims).SiteId) || // Site Level
        ((claims as AppUserClaims).WAL == 2 && v.CustomerId == (claims as AppUserClaims).CustomerId) || // Customer Level - can access all sites
        ((claims as AppUserClaims).WAL == 3 && (claims as AppUserClaims).AllowedSiteIds.Contains(v.SiteId) && ((claims as AppUserClaims).CanViewVehicle == null || (claims as AppUserClaims).CanViewVehicle.Value)) || // Access Group Level
        ((claims as AppUserClaims).WAL == 4 && v.SiteId == (claims as AppUserClaims).SiteId && (claims as AppUserClaims).AllowedDepartmentNames != null && (claims as AppUserClaims).AllowedDepartmentNames.Contains(v.Department.Name)) // Department Names Level
);

					if (predicateWrapper.Value == null)
					{
						predicateWrapper.Value = new SecurityPredicate
						(
							filter: expr,
							datasetFilter: datasetExpression,
							message: "",
							includes: "",
							evaluateDataset: true,
							evaluateDatabase: true 
						);
					}
					else
					{
						predicateWrapper.Value.Filter = ((Expression<Func<ORMVehicle, bool>>)predicateWrapper.Value.Filter).Or(expr);
						predicateWrapper.Value.DatasetFilter = ((Expression<Func<VehicleDataObject, bool>>)predicateWrapper.Value.DatasetFilter).Or(datasetExpression);
						predicateWrapper.Value.AddIncludes("");
					}

					// Note we don't apply any predicate (security filter) here, but instead prefer the dataprovider to handle it
					// This is because during a read, one entity instance may be denied, but many others allowed - so we can't decide the 'result' here (because we are just one instance)
					result = PermissionLevel.Authorized;
				}
				if (role == "DealerAdmin")
				{
					Expression<Func<ORMVehicle, bool>> expr = v => v.Customer.DealerId == (claims as AppUserClaims).DealerId && ((claims as AppUserClaims).AllowedCustomerId == null || (claims as AppUserClaims).AllowedCustomerId.Contains(v.CustomerId));
					Expression<Func<VehicleDataObject, bool>> datasetExpression = v => v.Customer.DealerId == (claims as AppUserClaims).DealerId && ((claims as AppUserClaims).AllowedCustomerId == null || (claims as AppUserClaims).AllowedCustomerId.Contains(v.CustomerId));

					if (predicateWrapper.Value == null)
					{
						predicateWrapper.Value = new SecurityPredicate
						(
							filter: expr,
							datasetFilter: datasetExpression,
							message: "",
							includes: "Customer",
							evaluateDataset: true,
							evaluateDatabase: true 
						);
					}
					else
					{
						predicateWrapper.Value.Filter = ((Expression<Func<ORMVehicle, bool>>)predicateWrapper.Value.Filter).Or(expr);
						predicateWrapper.Value.DatasetFilter = ((Expression<Func<VehicleDataObject, bool>>)predicateWrapper.Value.DatasetFilter).Or(datasetExpression);
						predicateWrapper.Value.AddIncludes("Customer");
					}

					// Note we don't apply any predicate (security filter) here, but instead prefer the dataprovider to handle it
					// This is because during a read, one entity instance may be denied, but many others allowed - so we can't decide the 'result' here (because we are just one instance)
					result = PermissionLevel.Authorized;
				}
			}

			// all roles denied?
			if (deniedRoleCount == claims.Roles.Count())
			{
				return PermissionLevel.Denied;
			}

			return result;
		}

		public async Task<PermissionLevel> CanUpdateAsync(IDataObject entity, UserClaims claims, IAuthorizations authorizations, ValueWrapper<string> messageWrapper, ValueWrapper<SecurityPredicate> predicateWrapper)
        {
			messageWrapper.Value = null;
			predicateWrapper.Value = null;
			int deniedRoleCount = 0;

			PermissionLevel result = PermissionLevel.NotSet;

			// fully authorised roles = all roles where the default rule is Authorized and there is no datafilter override
			var fullyAuthorizedRoles = new List<string> { "Administrator" };
			if (claims.Roles.Intersect(fullyAuthorizedRoles).Any())
			{
				return PermissionLevel.Authorized;
			}
 
			foreach (string role in claims.Roles)
			{
				if (role == "Customer")
				{
					Expression<Func<ORMVehicle, bool>> expr = v =>
(
        (((claims as AppUserClaims).WAL == null || (claims as AppUserClaims).WAL == 0) && v.DepartmentId == (claims as AppUserClaims).DepartmentId) || // Department Level
        ((claims as AppUserClaims).WAL == 1 && v.SiteId == (claims as AppUserClaims).SiteId) || // Site Level
        ((claims as AppUserClaims).WAL == 2 && v.CustomerId == (claims as AppUserClaims).CustomerId) || // Customer Level - can access all sites
        ((claims as AppUserClaims).WAL == 3 && (claims as AppUserClaims).AllowedSiteIds.Contains(v.SiteId) && ((claims as AppUserClaims).CanEditVehicle == null || (claims as AppUserClaims).CanEditVehicle.Value)) || // Access Group Level
        ((claims as AppUserClaims).WAL == 4 && v.SiteId == (claims as AppUserClaims).SiteId && (claims as AppUserClaims).AllowedDepartmentNames != null && (claims as AppUserClaims).AllowedDepartmentNames.Contains(v.Department.Name)) // Department Names Level
);
					Expression<Func<VehicleDataObject, bool>> datasetExpression = v =>
(
        (((claims as AppUserClaims).WAL == null || (claims as AppUserClaims).WAL == 0) && v.DepartmentId == (claims as AppUserClaims).DepartmentId) || // Department Level
        ((claims as AppUserClaims).WAL == 1 && v.SiteId == (claims as AppUserClaims).SiteId) || // Site Level
        ((claims as AppUserClaims).WAL == 2 && v.CustomerId == (claims as AppUserClaims).CustomerId) || // Customer Level - can access all sites
        ((claims as AppUserClaims).WAL == 3 && (claims as AppUserClaims).AllowedSiteIds.Contains(v.SiteId) && ((claims as AppUserClaims).CanEditVehicle == null || (claims as AppUserClaims).CanEditVehicle.Value)) || // Access Group Level
        ((claims as AppUserClaims).WAL == 4 && v.SiteId == (claims as AppUserClaims).SiteId && (claims as AppUserClaims).AllowedDepartmentNames != null && (claims as AppUserClaims).AllowedDepartmentNames.Contains(v.Department.Name)) // Department Names Level
);

					if (predicateWrapper.Value == null)
					{
						predicateWrapper.Value = new SecurityPredicate
						(
							filter: expr,
							datasetFilter: datasetExpression,
							message: "",
							includes: "",
							evaluateDataset: true,
							evaluateDatabase: true 
						);
					}
					else
					{
						predicateWrapper.Value.Filter = ((Expression<Func<ORMVehicle, bool>>)predicateWrapper.Value.Filter).Or(expr);
						predicateWrapper.Value.DatasetFilter = ((Expression<Func<VehicleDataObject, bool>>)predicateWrapper.Value.DatasetFilter).Or(datasetExpression);
						predicateWrapper.Value.AddIncludes("");
					}

					if (await authorizations.IsAuthorizedAsync(entity, predicateWrapper.Value, EntityAccessEnum.UPDATE, messageWrapper))
					{
						return PermissionLevel.Authorized;
					}
					else
					{
						++deniedRoleCount;
					}
				}
				if (role == "DealerAdmin")
				{
					Expression<Func<ORMVehicle, bool>> expr = v => v.Customer.DealerId == (claims as AppUserClaims).DealerId && ((claims as AppUserClaims).AllowedCustomerId == null || (claims as AppUserClaims).AllowedCustomerId.Contains(v.CustomerId));
					Expression<Func<VehicleDataObject, bool>> datasetExpression = v => v.Customer.DealerId == (claims as AppUserClaims).DealerId && ((claims as AppUserClaims).AllowedCustomerId == null || (claims as AppUserClaims).AllowedCustomerId.Contains(v.CustomerId));

					if (predicateWrapper.Value == null)
					{
						predicateWrapper.Value = new SecurityPredicate
						(
							filter: expr,
							datasetFilter: datasetExpression,
							message: "",
							includes: "Customer",
							evaluateDataset: true,
							evaluateDatabase: true 
						);
					}
					else
					{
						predicateWrapper.Value.Filter = ((Expression<Func<ORMVehicle, bool>>)predicateWrapper.Value.Filter).Or(expr);
						predicateWrapper.Value.DatasetFilter = ((Expression<Func<VehicleDataObject, bool>>)predicateWrapper.Value.DatasetFilter).Or(datasetExpression);
						predicateWrapper.Value.AddIncludes("Customer");
					}

					if (await authorizations.IsAuthorizedAsync(entity, predicateWrapper.Value, EntityAccessEnum.UPDATE, messageWrapper))
					{
						return PermissionLevel.Authorized;
					}
					else
					{
						++deniedRoleCount;
					}
				}
			}

			// all roles denied?
			if (deniedRoleCount == claims.Roles.Count())
			{
				return PermissionLevel.Denied;
			}

			return result;
		}

		public async Task<PermissionLevel> CanDeleteAsync(IDataObject entity, UserClaims claims, IAuthorizations authorizations, ValueWrapper<string> messageWrapper, ValueWrapper<SecurityPredicate> predicateWrapper)
        {
			messageWrapper.Value = null;
			predicateWrapper.Value = null;
			// Roles "Customer", "DealerAdmin" are denied DELETE access to Vehicle 
			var deniedRoles = new List<string> { "Customer", "DealerAdmin" };
			int deniedRoleCount = 0;

			PermissionLevel result = PermissionLevel.NotSet;

			// fully authorised roles = all roles where the default rule is Authorized and there is no datafilter override
			var fullyAuthorizedRoles = new List<string> { "Administrator" };
			if (claims.Roles.Intersect(fullyAuthorizedRoles).Any())
			{
				return PermissionLevel.Authorized;
			}
 
			foreach (string role in claims.Roles)
			{
				if (deniedRoles.Contains(role))
				{
					++deniedRoleCount;
				}

			}

			// all roles denied?
			if (deniedRoleCount == claims.Roles.Count())
			{
				return PermissionLevel.Denied;
			}

			return result;
		}

	}
}
