﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;

using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DeleteHandlers
{
	public partial class SlamcoreAPIKeyDeleteHandler : DeleteHandlerBase<SlamcoreAPIKeyDataObject>
	{
		
        public SlamcoreAPIKeyDeleteHandler(IServiceProvider serviceProvider, IDataProvider<SlamcoreAPIKeyDataObject> tentityDataProvider, ITransactionProvider transactionProvider, IEntityDataProvider entityDataProvider) : base(serviceProvider, tentityDataProvider, transactionProvider, entityDataProvider)
        {
        }

		public override async Task RippleDeleteAsync(SlamcoreAPIKeyDataObject instance, Parameters parameters, DataProviderDeleteSettings settings)
		{
			// Set resync flag initially so that if any processing is done, it's on the latest copy of the data
			NeedResync = true;

			// SlamcoreAPIKey.SlamcoreDevice (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSlamcoreDeviceAsync(parameters, skipSecurity: true);
				
				// We are the PK side entity in a OneToOne relationship, and the FK reference to us is optional.
				// So delete can proceed, but first we need to clear down the FK reference to us.
				var item = instance.SlamcoreDevice;				
				if (item != null && item.SlamcoreAPIKey != null)
				{
					item.SlamcoreAPIKey = null; 
					await SaveAsync(item);
				}
			}
		}
	}
}