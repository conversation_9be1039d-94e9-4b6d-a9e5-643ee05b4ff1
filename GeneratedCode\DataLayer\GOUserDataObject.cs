﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'GOUser'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class GOUserDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("AllowedDepartmentNames")]
		protected System.String _allowedDepartmentNames;
		[JsonProperty ("Blocked")]
		protected System.Boolean _blocked;
		[JsonProperty ("DealerAdmin")]
		protected Nullable<System.Boolean> _dealerAdmin;
		[JsonProperty ("DealerId")]
		protected Nullable<System.Guid> _dealerId;
		[JsonProperty ("EmailAddress")]
		protected System.String _emailAddress;
		[JsonProperty ("EmailChangeValidationInProgress")]
		protected System.Boolean _emailChangeValidationInProgress;
		[JsonProperty ("EmailValidated")]
		protected System.Boolean _emailValidated;
		[JsonProperty ("ExternalUserId")]
		protected System.String _externalUserId;
		[JsonProperty ("FirstName")]
		protected System.String _firstName;
		[JsonProperty ("FullName")]
		protected System.String _fullName;
		[JsonProperty ("GORoleName")]
		protected System.String _gORoleName;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("LastName")]
		protected System.String _lastName;
		[JsonProperty ("NewEmailAddress")]
		protected System.String _newEmailAddress;
		[JsonProperty ("NewEmailValidated")]
		protected Nullable<System.Boolean> _newEmailValidated;
		[JsonProperty ("Password")]
		protected System.String _password;
		[JsonProperty ("PasswordExpiry")]
		protected Nullable<System.DateTime> _passwordExpiry;
		[JsonProperty("PasswordExpiry_WithTimezoneOffset")]
		protected System.DateTimeOffset? _passwordExpiry_WithTimezoneOffset;
		[JsonProperty ("PreferredLocale")]
		protected Nullable<LocaleEnum> _preferredLocale;
		[JsonProperty ("PreferredLocaleString")]
		protected System.String _preferredLocaleString;
		[JsonProperty ("Unregistered")]
		protected System.Boolean _unregistered;
		[JsonProperty ("UserName")]
		protected System.String _userName;
		[JsonProperty ("UserValidated")]
		protected System.Boolean _userValidated;
		[JsonProperty ("WebsiteAccessLevel")]
		protected WebsiteAccessLevelEnum _websiteAccessLevel;
		[JsonProperty ("WebsiteAccessLevelValue")]
		protected Nullable<System.Int16> _websiteAccessLevelValue;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)




		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _dealer_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_dealer_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }




		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _gORole_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_gORole_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }












		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public GOUserDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
			SetBlockedValue(false, false, false);
			SetEmailValidatedValue(false, false, false);
			SetUnregisteredValue(false, false, false);
			SetNewEmailValidatedValue(false, false, false);
			SetUserValidatedValue(false, false, false);
			SetDealerAdminValue(false, false, false);
			SetEmailChangeValidationInProgressValue(false, false, false);
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public GOUserDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public GOUserDataObject Initialize(System.Guid id)
		{
			this._id = id;
			SetBlockedValue(false, false, false);
			SetEmailValidatedValue(false, false, false);
			SetUnregisteredValue(false, false, false);
			SetNewEmailValidatedValue(false, false, false);
			SetUserValidatedValue(false, false, false);
			SetDealerAdminValue(false, false, false);
			SetEmailChangeValidationInProgressValue(false, false, false);
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public GOUserDataObject Initialize(GOUserDataObject template, bool deepCopy)
		{
			this.SetPasswordExpiryValue(template.PasswordExpiry, false, false);
			this._passwordExpiry_WithTimezoneOffset = template._passwordExpiry_WithTimezoneOffset;
			this.SetAllowedDepartmentNamesValue(template.AllowedDepartmentNames, false, false);
			this.SetBlockedValue(template.Blocked, false, false);
			this.SetDealerAdminValue(template.DealerAdmin, false, false);
			this.SetDealerIdValue(template.DealerId, false, false);
			this.SetEmailAddressValue(template.EmailAddress, false, false);
			this.SetEmailChangeValidationInProgressValue(template.EmailChangeValidationInProgress, false, false);
			this.SetEmailValidatedValue(template.EmailValidated, false, false);
			this.SetExternalUserIdValue(template.ExternalUserId, false, false);
			this.SetFirstNameValue(template.FirstName, false, false);
			this.SetFullNameValue(template.FullName, false, false);
			this.SetGORoleNameValue(template.GORoleName, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetLastNameValue(template.LastName, false, false);
			this.SetNewEmailAddressValue(template.NewEmailAddress, false, false);
			this.SetNewEmailValidatedValue(template.NewEmailValidated, false, false);
			this.SetPasswordValue(template.Password, false, false);
			this.SetPreferredLocaleValue(template.PreferredLocale, false, false);
			this.SetPreferredLocaleStringValue(template.PreferredLocaleString, false, false);
			this.SetUnregisteredValue(template.Unregistered, false, false);
			this.SetUserNameValue(template.UserName, false, false);
			this.SetUserValidatedValue(template.UserValidated, false, false);
			this.SetWebsiteAccessLevelValue(template.WebsiteAccessLevel, false, false);
			this.SetWebsiteAccessLevelValueValue(template.WebsiteAccessLevelValue, false, false);
 
 
 
 
 
			this._dealer_NewObjectId = template._dealer_NewObjectId;
 
 
 
			this._gORole_NewObjectId = template._gORole_NewObjectId;
 
 
 
 
 
 
 
 
 
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual GOUserDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual GOUserDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<GOUserDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var gOUserSource = sourceObject as GOUserDataObject;

			if (ReferenceEquals(null, gOUserSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetAllowedDepartmentNamesValue(gOUserSource.AllowedDepartmentNames, false, false);
			this.SetBlockedValue(gOUserSource.Blocked, false, false);
			this.SetDealerAdminValue(gOUserSource.DealerAdmin, false, false);
			this.SetDealerIdValue(gOUserSource.DealerId, false, false);
			this.SetEmailAddressValue(gOUserSource.EmailAddress, false, false);
			this.SetEmailChangeValidationInProgressValue(gOUserSource.EmailChangeValidationInProgress, false, false);
			this.SetEmailValidatedValue(gOUserSource.EmailValidated, false, false);
			this.SetExternalUserIdValue(gOUserSource.ExternalUserId, false, false);
			this.SetFirstNameValue(gOUserSource.FirstName, false, false);
			this.SetFullNameValue(gOUserSource.FullName, false, false);
			this.SetGORoleNameValue(gOUserSource.GORoleName, false, false);
			this.SetIdValue(gOUserSource.Id, false, false);
			this.SetLastNameValue(gOUserSource.LastName, false, false);
			this.SetNewEmailAddressValue(gOUserSource.NewEmailAddress, false, false);
			this.SetNewEmailValidatedValue(gOUserSource.NewEmailValidated, false, false);
			this.SetPasswordValue(gOUserSource.Password, false, false);
			this.SetPasswordExpiryValue(gOUserSource.PasswordExpiry, false, false);
			this.SetPreferredLocaleValue(gOUserSource.PreferredLocale, false, false);
			this.SetPreferredLocaleStringValue(gOUserSource.PreferredLocaleString, false, false);
			this.SetUnregisteredValue(gOUserSource.Unregistered, false, false);
			this.SetUserNameValue(gOUserSource.UserName, false, false);
			this.SetUserValidatedValue(gOUserSource.UserValidated, false, false);
			this.SetWebsiteAccessLevelValue(gOUserSource.WebsiteAccessLevel, false, false);
			this.SetWebsiteAccessLevelValueValue(gOUserSource.WebsiteAccessLevelValue, false, false);




			this._dealer_NewObjectId = (sourceObject as GOUserDataObject)._dealer_NewObjectId;



			this._gORole_NewObjectId = (sourceObject as GOUserDataObject)._gORole_NewObjectId;











			if (deepCopy)
			{
				this.ObjectsDataSet = gOUserSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(gOUserSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as GOUserDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {




			if (this._dealer_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._dealer_NewObjectId))
				{
                    this._dealer_NewObjectId = null;
				}
                else
				{
					this._dealer_NewObjectId = datasetMergingInternalIdMapping[(int) this._dealer_NewObjectId];
				}
			}



			if (this._gORole_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._gORole_NewObjectId))
				{
                    this._gORole_NewObjectId = null;
				}
                else
				{
					this._gORole_NewObjectId = datasetMergingInternalIdMapping[(int) this._gORole_NewObjectId];
				}
			}











		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<AlertSubscriptionDataObject> _alertSubscriptionService => _serviceProvider.GetRequiredService<IDataProvider<AlertSubscriptionDataObject>>();

		private readonly SemaphoreSlim __alertSubscriptionItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __alertSubscriptionItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "AlertSubscriptionItems", which is a collection of AlertSubscriptionDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of AlertSubscriptionDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<AlertSubscriptionDataObject>> LoadAlertSubscriptionItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadAlertSubscriptionItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<AlertSubscriptionDataObject>> LoadAlertSubscriptionItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __alertSubscriptionItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__alertSubscriptionItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "GOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _alertSubscriptionService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __alertSubscriptionItemsAlreadyLazyLoaded = true;
                }

                return await GetAlertSubscriptionItemsAsync(false);
            }
            finally
            {
                __alertSubscriptionItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<AlertSubscriptionDataObject> AlertSubscriptionItems 
		{
			get
			{			
				return GetAlertSubscriptionItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeAlertSubscriptionItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("AlertSubscriptionItems");
		}

		public virtual async Task<DataObjectCollection<AlertSubscriptionDataObject>> GetAlertSubscriptionItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__alertSubscriptionItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadAlertSubscriptionItemsAsync(forceReload : forceReload);
			}
			var alertSubscriptionItems = ObjectsDataSet.GetRelatedObjects<AlertSubscriptionDataObject>(this, "AlertSubscriptionItems");							
			alertSubscriptionItems.CollectionChanged += new NotifyCollectionChangedEventHandler(AlertSubscriptionItems_CollectionChanged);
				
			return alertSubscriptionItems;
		}

        private void AlertSubscriptionItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as AlertSubscriptionDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : AlertSubscription", "GOUserDataObject.AlertSubscriptionItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add AlertSubscriptionDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUser_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.GOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.GOUserId == default(Nullable<System.Guid>))
							relatedObj.GOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as AlertSubscriptionDataObject).GOUser = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CustomerAuditDataObject> _customerAuditService => _serviceProvider.GetRequiredService<IDataProvider<CustomerAuditDataObject>>();

		private readonly SemaphoreSlim __customerAuditItemsCreatedSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerAuditItemsCreatedAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerAuditItemsCreated", which is a collection of CustomerAuditDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CustomerAuditDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CustomerAuditDataObject>> LoadCustomerAuditItemsCreatedAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerAuditItemsCreatedAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CustomerAuditDataObject>> LoadCustomerAuditItemsCreatedAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerAuditItemsCreatedSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerAuditItemsCreatedAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CreatedBy == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _customerAuditService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __customerAuditItemsCreatedAlreadyLazyLoaded = true;
                }

                return await GetCustomerAuditItemsCreatedAsync(false);
            }
            finally
            {
                __customerAuditItemsCreatedSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CustomerAuditDataObject> CustomerAuditItemsCreated 
		{
			get
			{			
				return GetCustomerAuditItemsCreatedAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCustomerAuditItemsCreated()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("CustomerAuditItemsCreated");
		}

		public virtual async Task<DataObjectCollection<CustomerAuditDataObject>> GetCustomerAuditItemsCreatedAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__customerAuditItemsCreatedAlreadyLazyLoaded || forceReload) )
			{
				await LoadCustomerAuditItemsCreatedAsync(forceReload : forceReload);
			}
			var customerAuditItemsCreated = ObjectsDataSet.GetRelatedObjects<CustomerAuditDataObject>(this, "CustomerAuditItemsCreated");							
			customerAuditItemsCreated.CollectionChanged += new NotifyCollectionChangedEventHandler(CustomerAuditItemsCreated_CollectionChanged);
				
			return customerAuditItemsCreated;
		}

        private void CustomerAuditItemsCreated_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CustomerAuditDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CustomerAudit", "GOUserDataObject.CustomerAuditItemsCreated_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add CustomerAuditDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUserWhoCreatedThisCustomer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CreatedBy = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CreatedBy == default(Nullable<System.Guid>))
							relatedObj.CreatedBy = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CustomerAuditDataObject).GOUserWhoCreatedThisCustomer = null;
                    // }
                    break;
            }            
        }


		private readonly SemaphoreSlim __customerAuditItemsDeletedSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerAuditItemsDeletedAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerAuditItemsDeleted", which is a collection of CustomerAuditDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CustomerAuditDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CustomerAuditDataObject>> LoadCustomerAuditItemsDeletedAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerAuditItemsDeletedAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CustomerAuditDataObject>> LoadCustomerAuditItemsDeletedAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerAuditItemsDeletedSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerAuditItemsDeletedAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "DeletedBy == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _customerAuditService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __customerAuditItemsDeletedAlreadyLazyLoaded = true;
                }

                return await GetCustomerAuditItemsDeletedAsync(false);
            }
            finally
            {
                __customerAuditItemsDeletedSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CustomerAuditDataObject> CustomerAuditItemsDeleted 
		{
			get
			{			
				return GetCustomerAuditItemsDeletedAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCustomerAuditItemsDeleted()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("CustomerAuditItemsDeleted");
		}

		public virtual async Task<DataObjectCollection<CustomerAuditDataObject>> GetCustomerAuditItemsDeletedAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__customerAuditItemsDeletedAlreadyLazyLoaded || forceReload) )
			{
				await LoadCustomerAuditItemsDeletedAsync(forceReload : forceReload);
			}
			var customerAuditItemsDeleted = ObjectsDataSet.GetRelatedObjects<CustomerAuditDataObject>(this, "CustomerAuditItemsDeleted");							
			customerAuditItemsDeleted.CollectionChanged += new NotifyCollectionChangedEventHandler(CustomerAuditItemsDeleted_CollectionChanged);
				
			return customerAuditItemsDeleted;
		}

        private void CustomerAuditItemsDeleted_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CustomerAuditDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CustomerAudit", "GOUserDataObject.CustomerAuditItemsDeleted_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add CustomerAuditDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUserWhoDeletedThisCustomer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.DeletedBy = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.DeletedBy == default(Nullable<System.Guid>))
							relatedObj.DeletedBy = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CustomerAuditDataObject).GOUserWhoDeletedThisCustomer = null;
                    // }
                    break;
            }            
        }


		private readonly SemaphoreSlim __customerAuditItemsModifiedSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerAuditItemsModifiedAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerAuditItemsModified", which is a collection of CustomerAuditDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CustomerAuditDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CustomerAuditDataObject>> LoadCustomerAuditItemsModifiedAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerAuditItemsModifiedAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CustomerAuditDataObject>> LoadCustomerAuditItemsModifiedAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerAuditItemsModifiedSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerAuditItemsModifiedAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "LastModifiedBy == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _customerAuditService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __customerAuditItemsModifiedAlreadyLazyLoaded = true;
                }

                return await GetCustomerAuditItemsModifiedAsync(false);
            }
            finally
            {
                __customerAuditItemsModifiedSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CustomerAuditDataObject> CustomerAuditItemsModified 
		{
			get
			{			
				return GetCustomerAuditItemsModifiedAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCustomerAuditItemsModified()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("CustomerAuditItemsModified");
		}

		public virtual async Task<DataObjectCollection<CustomerAuditDataObject>> GetCustomerAuditItemsModifiedAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__customerAuditItemsModifiedAlreadyLazyLoaded || forceReload) )
			{
				await LoadCustomerAuditItemsModifiedAsync(forceReload : forceReload);
			}
			var customerAuditItemsModified = ObjectsDataSet.GetRelatedObjects<CustomerAuditDataObject>(this, "CustomerAuditItemsModified");							
			customerAuditItemsModified.CollectionChanged += new NotifyCollectionChangedEventHandler(CustomerAuditItemsModified_CollectionChanged);
				
			return customerAuditItemsModified;
		}

        private void CustomerAuditItemsModified_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CustomerAuditDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CustomerAudit", "GOUserDataObject.CustomerAuditItemsModified_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add CustomerAuditDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUserWhoModifiedThisCustomer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.LastModifiedBy = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.LastModifiedBy == default(Nullable<System.Guid>))
							relatedObj.LastModifiedBy = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CustomerAuditDataObject).GOUserWhoModifiedThisCustomer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DealerDataObject> _dealerService => _serviceProvider.GetRequiredService<IDataProvider<DealerDataObject>>();
      public virtual void SetDealerValue(DealerDataObject valueToSet)
		{
			SetDealerValue(valueToSet, true, true);
		}

        public virtual void SetDealerValue(DealerDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DealerDataObject existing_dealer = null ;

			if ( !(this.DealerId == null || ObjectsDataSet == null))
			{
				DealerDataObject key;

				if (this._dealer_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DealerDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._dealer_NewObjectId;			
				}

				existing_dealer = (DealerDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_dealer ,valueToSet))
            {
                if (valueToSet == null)
                {
					_dealer_NewObjectId = null;
					_dealerId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Dealer", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "GOUserDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_dealer_NewObjectId != valueToSet.InternalObjectId)
					{
						_dealer_NewObjectId = valueToSet.InternalObjectId;
						_dealerId = valueToSet.Id;
						OnPropertyChanged("DealerId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_dealerId != valueToSet.Id)
					{
						_dealer_NewObjectId = null;

						_dealerId = valueToSet.Id;
						OnPropertyChanged("DealerId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_dealer_NewObjectId = null;
					_dealerId = null;
					
				OnPropertyChanged("DealerId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_dealer ,valueToSet))
				OnPropertyChanged("Dealer", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __dealerSemaphore = new SemaphoreSlim(1, 1);
		private bool __dealerAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Dealer", which is a DealerDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DealerDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DealerDataObject> LoadDealerAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDealerAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DealerDataObject> LoadDealerAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __dealerSemaphore.WaitAsync();
			
	        try
            {
                if (!__dealerAlreadyLazyLoaded || forceReload)
                {
								
					if (this.DealerId == null)
					{
						return null;
					}
				
					DealerDataObject dealer = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __dealerAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						dealer = _serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId);
						dealer.IsNew = false;
						dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
						if (dealer != null)
						{
							return dealer;
						}
					}

					dealer = await _dealerService.GetAsync(_serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId), parameters : parameters, skipSecurity: skipSecurity);

					SetDealerValue(dealer, false, false);
					__dealerAlreadyLazyLoaded = true;				
		
					dealer = _serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId);
					dealer.IsNew = false;
					dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
                    __dealerAlreadyLazyLoaded = true;
                }

                return await GetDealerAsync(false);
            }
            finally
            {
                __dealerSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DealerDataObject Dealer 
		{
			get
			{			
				return GetDealerAsync(true).Result;
			}
			set
			{
				SetDealerValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDealer()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("Dealer");
		}

		public virtual async Task<DealerDataObject> GetDealerAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DealerDataObject dealer;

				
			if (_dealer_NewObjectId != null)
			{
				dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
				dealer.IsNew = true;
				dealer.InternalObjectId = _dealer_NewObjectId;
				dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
			}
			else
			{
				if (this.DealerId == null)
					return null;
				if (DealerId == null)
					dealer = null;
				else
				dealer = _serviceProvider.GetRequiredService<DealerDataObject>().Initialize((System.Guid)this.DealerId);
				dealer.IsNew = false;
				dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
				
				if (allowLazyLoading && dealer == null && LazyLoadingEnabled && (!__dealerAlreadyLazyLoaded || forceReload))
				{
					dealer = await LoadDealerAsync(forceReload : forceReload);
				}
			}
				
			return dealer;
		}

		public virtual Nullable<System.Guid> DealerForeignKey
		{
			get { return DealerId; }
			set 
			{	
				DealerId = value;
			}
			
		}
		

		protected IDataProvider<DealerDriverDataObject> _dealerDriverService => _serviceProvider.GetRequiredService<IDataProvider<DealerDriverDataObject>>();
      public virtual void SetDealerDriverValue(DealerDriverDataObject valueToSet)
		{
			SetDealerDriverValue(valueToSet, true, true);
		}

        public virtual void SetDealerDriverValue(DealerDriverDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<DealerDriverDataObject>(this, "DealerDriver");
			var existing_dealerDriver = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("DealerDriver", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._gOUser_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.GOUser = this;
					valueToSet.GOUserId = this.Id;
				}			
			}
			else  if (existing_dealerDriver != null)
            {
                ObjectsDataSet.RemoveObject(existing_dealerDriver);
            }
			if (!ReferenceEquals(existing_dealerDriver ,valueToSet))
				OnPropertyChanged("DealerDriver", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __dealerDriverSemaphore = new SemaphoreSlim(1, 1);
		private bool __dealerDriverAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DealerDriver", which is a DealerDriverDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DealerDriverDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DealerDriverDataObject> LoadDealerDriverAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDealerDriverAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DealerDriverDataObject> LoadDealerDriverAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __dealerDriverSemaphore.WaitAsync();
			
	        try
            {
                if (!__dealerDriverAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data DealerDriver for the current entity. The DataObjects doesn't have an ObjectsDataSet", "GOUserObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var dealerDriver = (this.ObjectsDataSet as ObjectsDataSet).DealerDriverObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).DealerDriverObjects.Where(item => item.Value.GOUserId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(dealerDriver, null))
					{
						var filterPredicate = "GOUserId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						dealerDriver = (await _dealerDriverService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetDealerDriverValue(dealerDriver, false, false);
						__dealerDriverAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a dealerDriver, but relation field not set, encourage it to get set by removing and re-adding the dealerDriver 
					if (dealerDriver != null && this.DealerDriver == null)
					{
						this.ObjectsDataSet.RemoveObject(dealerDriver);
						this.ObjectsDataSet.AddObject(dealerDriver);
					}			
                    __dealerDriverAlreadyLazyLoaded = true;
                }

                return await GetDealerDriverAsync(false);
            }
            finally
            {
                __dealerDriverSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DealerDriverDataObject DealerDriver 
		{
			get
			{			
				return GetDealerDriverAsync(true).Result;
			}
			set
			{
				SetDealerDriverValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDealerDriver()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("DealerDriver");
		}

		public virtual async Task<DealerDriverDataObject> GetDealerDriverAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DealerDriverDataObject dealerDriver;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<DealerDriverDataObject>(this, "DealerDriver");
               	dealerDriver = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && dealerDriver == null && LazyLoadingEnabled && (!__dealerDriverAlreadyLazyLoaded || forceReload))
				{
					dealerDriver = await LoadDealerDriverAsync(forceReload : forceReload);
				}
			}
				
			return dealerDriver;
		}


		protected IDataProvider<ExportJobStatusDataObject> _exportJobStatusService => _serviceProvider.GetRequiredService<IDataProvider<ExportJobStatusDataObject>>();

		private readonly SemaphoreSlim __exportJobStatusItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __exportJobStatusItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ExportJobStatusItems", which is a collection of ExportJobStatusDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ExportJobStatusDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ExportJobStatusDataObject>> LoadExportJobStatusItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadExportJobStatusItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ExportJobStatusDataObject>> LoadExportJobStatusItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __exportJobStatusItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__exportJobStatusItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "GOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _exportJobStatusService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __exportJobStatusItemsAlreadyLazyLoaded = true;
                }

                return await GetExportJobStatusItemsAsync(false);
            }
            finally
            {
                __exportJobStatusItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ExportJobStatusDataObject> ExportJobStatusItems 
		{
			get
			{			
				return GetExportJobStatusItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeExportJobStatusItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("ExportJobStatusItems");
		}

		public virtual async Task<DataObjectCollection<ExportJobStatusDataObject>> GetExportJobStatusItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__exportJobStatusItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadExportJobStatusItemsAsync(forceReload : forceReload);
			}
			var exportJobStatusItems = ObjectsDataSet.GetRelatedObjects<ExportJobStatusDataObject>(this, "ExportJobStatusItems");							
			exportJobStatusItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ExportJobStatusItems_CollectionChanged);
				
			return exportJobStatusItems;
		}

        private void ExportJobStatusItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ExportJobStatusDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ExportJobStatus", "GOUserDataObject.ExportJobStatusItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add ExportJobStatusDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUser_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.GOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.GOUserId == default(Nullable<System.Guid>))
							relatedObj.GOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ExportJobStatusDataObject).GOUser = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<GORoleDataObject> _gORoleService => _serviceProvider.GetRequiredService<IDataProvider<GORoleDataObject>>();
      public virtual void SetGORoleValue(GORoleDataObject valueToSet)
		{
			SetGORoleValue(valueToSet, true, true);
		}

        public virtual void SetGORoleValue(GORoleDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			GORoleDataObject existing_gORole = null ;

			if ( !(this.GORoleName == null || ObjectsDataSet == null))
			{
				GORoleDataObject key;

				if (this._gORole_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<GORoleDataObject>().Initialize((System.String)this.GORoleName);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<GORoleDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._gORole_NewObjectId;			
				}

				existing_gORole = (GORoleDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_gORole ,valueToSet))
            {
                if (valueToSet == null)
                {
					_gORole_NewObjectId = null;
					_gORoleName = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("GORole", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "GOUserDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_gORole_NewObjectId != valueToSet.InternalObjectId)
					{
						_gORole_NewObjectId = valueToSet.InternalObjectId;
						_gORoleName = valueToSet.Name;
						OnPropertyChanged("GORoleName",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_gORoleName != valueToSet.Name)
					{
						_gORole_NewObjectId = null;

						_gORoleName = valueToSet.Name;
						OnPropertyChanged("GORoleName",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_gORole_NewObjectId = null;
					_gORoleName = null;
					
				OnPropertyChanged("GORoleName",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_gORole ,valueToSet))
				OnPropertyChanged("GORole", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __gORoleSemaphore = new SemaphoreSlim(1, 1);
		private bool __gORoleAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "GORole", which is a GORoleDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a GORoleDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<GORoleDataObject> LoadGORoleAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadGORoleAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<GORoleDataObject> LoadGORoleAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __gORoleSemaphore.WaitAsync();
			
	        try
            {
                if (!__gORoleAlreadyLazyLoaded || forceReload)
                {
								
					if (this.GORoleName == null)
					{
						return null;
					}
				
					GORoleDataObject gORole = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __gORoleAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						gORole = _serviceProvider.GetRequiredService<GORoleDataObject>().Initialize((System.String)this.GORoleName);
						gORole.IsNew = false;
						gORole = (GORoleDataObject)ObjectsDataSet.GetObject(gORole);
						if (gORole != null)
						{
							return gORole;
						}
					}

					gORole = await _gORoleService.GetAsync(_serviceProvider.GetRequiredService<GORoleDataObject>().Initialize((System.String)this.GORoleName), parameters : parameters, skipSecurity: skipSecurity);

					SetGORoleValue(gORole, false, false);
					__gORoleAlreadyLazyLoaded = true;				
		
					gORole = _serviceProvider.GetRequiredService<GORoleDataObject>().Initialize((System.String)this.GORoleName);
					gORole.IsNew = false;
					gORole = (GORoleDataObject)ObjectsDataSet.GetObject(gORole);
                    __gORoleAlreadyLazyLoaded = true;
                }

                return await GetGORoleAsync(false);
            }
            finally
            {
                __gORoleSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual GORoleDataObject GORole 
		{
			get
			{			
				return GetGORoleAsync(true).Result;
			}
			set
			{
				SetGORoleValue(value);
			}
		}
		
		public virtual bool ShouldSerializeGORole()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("GORole");
		}

		public virtual async Task<GORoleDataObject> GetGORoleAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			GORoleDataObject gORole;

				
			if (_gORole_NewObjectId != null)
			{
				gORole = _serviceProvider.GetRequiredService<GORoleDataObject>();
				gORole.IsNew = true;
				gORole.InternalObjectId = _gORole_NewObjectId;
				gORole = (GORoleDataObject)ObjectsDataSet.GetObject(gORole);
			}
			else
			{
				if (this.GORoleName == null)
					return null;
				if (GORoleName == null)
					gORole = null;
				else
				gORole = _serviceProvider.GetRequiredService<GORoleDataObject>().Initialize((System.String)this.GORoleName);
				gORole.IsNew = false;
				gORole = (GORoleDataObject)ObjectsDataSet.GetObject(gORole);
				
				if (allowLazyLoading && gORole == null && LazyLoadingEnabled && (!__gORoleAlreadyLazyLoaded || forceReload))
				{
					gORole = await LoadGORoleAsync(forceReload : forceReload);
				}
			}
				
			return gORole;
		}

		public virtual System.String GORoleForeignKey
		{
			get { return GORoleName; }
			set 
			{	
				GORoleName = value;
			}
			
		}
		

		protected IDataProvider<GOUserDepartmentDataObject> _gOUserDepartmentService => _serviceProvider.GetRequiredService<IDataProvider<GOUserDepartmentDataObject>>();

		private readonly SemaphoreSlim __gOUserDepartmentItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __gOUserDepartmentItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "GOUserDepartmentItems", which is a collection of GOUserDepartmentDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of GOUserDepartmentDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<GOUserDepartmentDataObject>> LoadGOUserDepartmentItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadGOUserDepartmentItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<GOUserDepartmentDataObject>> LoadGOUserDepartmentItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __gOUserDepartmentItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__gOUserDepartmentItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "GOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _gOUserDepartmentService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __gOUserDepartmentItemsAlreadyLazyLoaded = true;
                }

                return await GetGOUserDepartmentItemsAsync(false);
            }
            finally
            {
                __gOUserDepartmentItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<GOUserDepartmentDataObject> GOUserDepartmentItems 
		{
			get
			{			
				return GetGOUserDepartmentItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeGOUserDepartmentItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("GOUserDepartmentItems");
		}

		public virtual async Task<DataObjectCollection<GOUserDepartmentDataObject>> GetGOUserDepartmentItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__gOUserDepartmentItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadGOUserDepartmentItemsAsync(forceReload : forceReload);
			}
			var gOUserDepartmentItems = ObjectsDataSet.GetRelatedObjects<GOUserDepartmentDataObject>(this, "GOUserDepartmentItems");							
			gOUserDepartmentItems.CollectionChanged += new NotifyCollectionChangedEventHandler(GOUserDepartmentItems_CollectionChanged);
				
			return gOUserDepartmentItems;
		}

        private void GOUserDepartmentItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as GOUserDepartmentDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : GOUserDepartment", "GOUserDataObject.GOUserDepartmentItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add GOUserDepartmentDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUser_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.GOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.GOUserId == default(System.Guid))
							relatedObj.GOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as GOUserDepartmentDataObject).GOUser = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<GoUserToCustomerDataObject> _goUserToCustomerService => _serviceProvider.GetRequiredService<IDataProvider<GoUserToCustomerDataObject>>();

		private readonly SemaphoreSlim __goUserToCustomerItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __goUserToCustomerItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "GoUserToCustomerItems", which is a collection of GoUserToCustomerDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of GoUserToCustomerDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<GoUserToCustomerDataObject>> LoadGoUserToCustomerItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadGoUserToCustomerItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<GoUserToCustomerDataObject>> LoadGoUserToCustomerItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __goUserToCustomerItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__goUserToCustomerItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "GOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _goUserToCustomerService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __goUserToCustomerItemsAlreadyLazyLoaded = true;
                }

                return await GetGoUserToCustomerItemsAsync(false);
            }
            finally
            {
                __goUserToCustomerItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<GoUserToCustomerDataObject> GoUserToCustomerItems 
		{
			get
			{			
				return GetGoUserToCustomerItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeGoUserToCustomerItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("GoUserToCustomerItems");
		}

		public virtual async Task<DataObjectCollection<GoUserToCustomerDataObject>> GetGoUserToCustomerItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__goUserToCustomerItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadGoUserToCustomerItemsAsync(forceReload : forceReload);
			}
			var goUserToCustomerItems = ObjectsDataSet.GetRelatedObjects<GoUserToCustomerDataObject>(this, "GoUserToCustomerItems");							
			goUserToCustomerItems.CollectionChanged += new NotifyCollectionChangedEventHandler(GoUserToCustomerItems_CollectionChanged);
				
			return goUserToCustomerItems;
		}

        private void GoUserToCustomerItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as GoUserToCustomerDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : GoUserToCustomer", "GOUserDataObject.GoUserToCustomerItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add GoUserToCustomerDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUser_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.GOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.GOUserId == default(System.Guid))
							relatedObj.GOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as GoUserToCustomerDataObject).GOUser = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<MessageHistoryDataObject> _messageHistoryService => _serviceProvider.GetRequiredService<IDataProvider<MessageHistoryDataObject>>();

		private readonly SemaphoreSlim __messageHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __messageHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "MessageHistoryItems", which is a collection of MessageHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of MessageHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<MessageHistoryDataObject>> LoadMessageHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadMessageHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<MessageHistoryDataObject>> LoadMessageHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __messageHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__messageHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "GOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _messageHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __messageHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetMessageHistoryItemsAsync(false);
            }
            finally
            {
                __messageHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<MessageHistoryDataObject> MessageHistoryItems 
		{
			get
			{			
				return GetMessageHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeMessageHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("MessageHistoryItems");
		}

		public virtual async Task<DataObjectCollection<MessageHistoryDataObject>> GetMessageHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__messageHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadMessageHistoryItemsAsync(forceReload : forceReload);
			}
			var messageHistoryItems = ObjectsDataSet.GetRelatedObjects<MessageHistoryDataObject>(this, "MessageHistoryItems");							
			messageHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(MessageHistoryItems_CollectionChanged);
				
			return messageHistoryItems;
		}

        private void MessageHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as MessageHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : MessageHistory", "GOUserDataObject.MessageHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add MessageHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUser_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.GOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.GOUserId == default(System.Guid))
							relatedObj.GOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as MessageHistoryDataObject).GOUser = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<PersonDataObject> _personService => _serviceProvider.GetRequiredService<IDataProvider<PersonDataObject>>();
      public virtual void SetPersonValue(PersonDataObject valueToSet)
		{
			SetPersonValue(valueToSet, true, true);
		}

        public virtual void SetPersonValue(PersonDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<PersonDataObject>(this, "Person");
			var existing_person = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Person", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._gOUser_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.GOUser = this;
					valueToSet.GOUserId = this.Id;
				}			
			}
			else  if (existing_person != null)
            {
                ObjectsDataSet.RemoveObject(existing_person);
            }
			if (!ReferenceEquals(existing_person ,valueToSet))
				OnPropertyChanged("Person", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __personSemaphore = new SemaphoreSlim(1, 1);
		private bool __personAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Person", which is a PersonDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a PersonDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<PersonDataObject> LoadPersonAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPersonAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<PersonDataObject> LoadPersonAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __personSemaphore.WaitAsync();
			
	        try
            {
                if (!__personAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data Person for the current entity. The DataObjects doesn't have an ObjectsDataSet", "GOUserObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var person = (this.ObjectsDataSet as ObjectsDataSet).PersonObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).PersonObjects.Where(item => item.Value.GOUserId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(person, null))
					{
						var filterPredicate = "GOUserId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						person = (await _personService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetPersonValue(person, false, false);
						__personAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a person, but relation field not set, encourage it to get set by removing and re-adding the person 
					if (person != null && this.Person == null)
					{
						this.ObjectsDataSet.RemoveObject(person);
						this.ObjectsDataSet.AddObject(person);
					}			
                    __personAlreadyLazyLoaded = true;
                }

                return await GetPersonAsync(false);
            }
            finally
            {
                __personSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual PersonDataObject Person 
		{
			get
			{			
				return GetPersonAsync(true).Result;
			}
			set
			{
				SetPersonValue(value);
			}
		}
		
		public virtual bool ShouldSerializePerson()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("Person");
		}

		public virtual async Task<PersonDataObject> GetPersonAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			PersonDataObject person;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<PersonDataObject>(this, "Person");
               	person = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && person == null && LazyLoadingEnabled && (!__personAlreadyLazyLoaded || forceReload))
				{
					person = await LoadPersonAsync(forceReload : forceReload);
				}
			}
				
			return person;
		}


		protected IDataProvider<ReportSubscriptionDataObject> _reportSubscriptionService => _serviceProvider.GetRequiredService<IDataProvider<ReportSubscriptionDataObject>>();

		private readonly SemaphoreSlim __reportSubscriptionItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __reportSubscriptionItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ReportSubscriptionItems", which is a collection of ReportSubscriptionDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ReportSubscriptionDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ReportSubscriptionDataObject>> LoadReportSubscriptionItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadReportSubscriptionItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ReportSubscriptionDataObject>> LoadReportSubscriptionItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __reportSubscriptionItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__reportSubscriptionItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "GOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _reportSubscriptionService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __reportSubscriptionItemsAlreadyLazyLoaded = true;
                }

                return await GetReportSubscriptionItemsAsync(false);
            }
            finally
            {
                __reportSubscriptionItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ReportSubscriptionDataObject> ReportSubscriptionItems 
		{
			get
			{			
				return GetReportSubscriptionItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeReportSubscriptionItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("ReportSubscriptionItems");
		}

		public virtual async Task<DataObjectCollection<ReportSubscriptionDataObject>> GetReportSubscriptionItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__reportSubscriptionItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadReportSubscriptionItemsAsync(forceReload : forceReload);
			}
			var reportSubscriptionItems = ObjectsDataSet.GetRelatedObjects<ReportSubscriptionDataObject>(this, "ReportSubscriptionItems");							
			reportSubscriptionItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ReportSubscriptionItems_CollectionChanged);
				
			return reportSubscriptionItems;
		}

        private void ReportSubscriptionItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ReportSubscriptionDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ReportSubscription", "GOUserDataObject.ReportSubscriptionItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add ReportSubscriptionDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUser_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.GOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.GOUserId == default(Nullable<System.Guid>))
							relatedObj.GOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ReportSubscriptionDataObject).GOUser = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<RevisionDataObject> _revisionService => _serviceProvider.GetRequiredService<IDataProvider<RevisionDataObject>>();

		private readonly SemaphoreSlim __revisionItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __revisionItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "RevisionItems", which is a collection of RevisionDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of RevisionDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<RevisionDataObject>> LoadRevisionItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadRevisionItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<RevisionDataObject>> LoadRevisionItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __revisionItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__revisionItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "fkGOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _revisionService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __revisionItemsAlreadyLazyLoaded = true;
                }

                return await GetRevisionItemsAsync(false);
            }
            finally
            {
                __revisionItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<RevisionDataObject> RevisionItems 
		{
			get
			{			
				return GetRevisionItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeRevisionItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("RevisionItems");
		}

		public virtual async Task<DataObjectCollection<RevisionDataObject>> GetRevisionItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__revisionItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadRevisionItemsAsync(forceReload : forceReload);
			}
			var revisionItems = ObjectsDataSet.GetRelatedObjects<RevisionDataObject>(this, "RevisionItems");							
			revisionItems.CollectionChanged += new NotifyCollectionChangedEventHandler(RevisionItems_CollectionChanged);
				
			return revisionItems;
		}

        private void RevisionItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as RevisionDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : Revision", "GOUserDataObject.RevisionItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add RevisionDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUser_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.fkGOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.fkGOUserId == default(Nullable<System.Guid>))
							relatedObj.fkGOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as RevisionDataObject).GOUser = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<TagDataObject> _tagService => _serviceProvider.GetRequiredService<IDataProvider<TagDataObject>>();
      public virtual void SetTagValue(TagDataObject valueToSet)
		{
			SetTagValue(valueToSet, true, true);
		}

        public virtual void SetTagValue(TagDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<TagDataObject>(this, "Tag");
			var existing_tag = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Tag", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._gOUser_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.GOUser = this;
					valueToSet.Author = this.Id;
				}			
			}
			else  if (existing_tag != null)
            {
                ObjectsDataSet.RemoveObject(existing_tag);
            }
			if (!ReferenceEquals(existing_tag ,valueToSet))
				OnPropertyChanged("Tag", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __tagSemaphore = new SemaphoreSlim(1, 1);
		private bool __tagAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Tag", which is a TagDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a TagDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<TagDataObject> LoadTagAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadTagAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<TagDataObject> LoadTagAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __tagSemaphore.WaitAsync();
			
	        try
            {
                if (!__tagAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data Tag for the current entity. The DataObjects doesn't have an ObjectsDataSet", "GOUserObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var tag = (this.ObjectsDataSet as ObjectsDataSet).TagObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).TagObjects.Where(item => item.Value.Author == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(tag, null))
					{
						var filterPredicate = "Author == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						tag = (await _tagService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetTagValue(tag, false, false);
						__tagAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a tag, but relation field not set, encourage it to get set by removing and re-adding the tag 
					if (tag != null && this.Tag == null)
					{
						this.ObjectsDataSet.RemoveObject(tag);
						this.ObjectsDataSet.AddObject(tag);
					}			
                    __tagAlreadyLazyLoaded = true;
                }

                return await GetTagAsync(false);
            }
            finally
            {
                __tagSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual TagDataObject Tag 
		{
			get
			{			
				return GetTagAsync(true).Result;
			}
			set
			{
				SetTagValue(value);
			}
		}
		
		public virtual bool ShouldSerializeTag()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("Tag");
		}

		public virtual async Task<TagDataObject> GetTagAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			TagDataObject tag;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<TagDataObject>(this, "Tag");
               	tag = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && tag == null && LazyLoadingEnabled && (!__tagAlreadyLazyLoaded || forceReload))
				{
					tag = await LoadTagAsync(forceReload : forceReload);
				}
			}
				
			return tag;
		}


		protected IDataProvider<GOUserGroupDataObject> _gOUserGroupService => _serviceProvider.GetRequiredService<IDataProvider<GOUserGroupDataObject>>();

		private readonly SemaphoreSlim __userGroupItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __userGroupItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "UserGroupItems", which is a collection of GOUserGroupDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of GOUserGroupDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<GOUserGroupDataObject>> LoadUserGroupItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadUserGroupItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<GOUserGroupDataObject>> LoadUserGroupItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __userGroupItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__userGroupItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "GOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _gOUserGroupService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __userGroupItemsAlreadyLazyLoaded = true;
                }

                return await GetUserGroupItemsAsync(false);
            }
            finally
            {
                __userGroupItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<GOUserGroupDataObject> UserGroupItems 
		{
			get
			{			
				return GetUserGroupItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeUserGroupItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("UserGroupItems");
		}

		public virtual async Task<DataObjectCollection<GOUserGroupDataObject>> GetUserGroupItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__userGroupItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadUserGroupItemsAsync(forceReload : forceReload);
			}
			var userGroupItems = ObjectsDataSet.GetRelatedObjects<GOUserGroupDataObject>(this, "UserGroupItems");							
			userGroupItems.CollectionChanged += new NotifyCollectionChangedEventHandler(UserGroupItems_CollectionChanged);
				
			return userGroupItems;
		}

        private void UserGroupItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as GOUserGroupDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : GOUserGroup", "GOUserDataObject.UserGroupItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add GOUserGroupDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._user_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.GOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.GOUserId == default(System.Guid))
							relatedObj.GOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as GOUserGroupDataObject).User = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<GOUserRoleDataObject> _gOUserRoleService => _serviceProvider.GetRequiredService<IDataProvider<GOUserRoleDataObject>>();

		private readonly SemaphoreSlim __userRoleItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __userRoleItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "UserRoleItems", which is a collection of GOUserRoleDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of GOUserRoleDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<GOUserRoleDataObject>> LoadUserRoleItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadUserRoleItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<GOUserRoleDataObject>> LoadUserRoleItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __userRoleItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__userRoleItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "GOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _gOUserRoleService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __userRoleItemsAlreadyLazyLoaded = true;
                }

                return await GetUserRoleItemsAsync(false);
            }
            finally
            {
                __userRoleItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<GOUserRoleDataObject> UserRoleItems 
		{
			get
			{			
				return GetUserRoleItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeUserRoleItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("UserRoleItems");
		}

		public virtual async Task<DataObjectCollection<GOUserRoleDataObject>> GetUserRoleItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__userRoleItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadUserRoleItemsAsync(forceReload : forceReload);
			}
			var userRoleItems = ObjectsDataSet.GetRelatedObjects<GOUserRoleDataObject>(this, "UserRoleItems");							
			userRoleItems.CollectionChanged += new NotifyCollectionChangedEventHandler(UserRoleItems_CollectionChanged);
				
			return userRoleItems;
		}

        private void UserRoleItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as GOUserRoleDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : GOUserRole", "GOUserDataObject.UserRoleItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add GOUserRoleDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._user_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.GOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.GOUserId == default(System.Guid))
							relatedObj.GOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as GOUserRoleDataObject).User = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleLockoutDataObject> _vehicleLockoutService => _serviceProvider.GetRequiredService<IDataProvider<VehicleLockoutDataObject>>();

		private readonly SemaphoreSlim __vehicleLockoutItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleLockoutItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleLockoutItems", which is a collection of VehicleLockoutDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleLockoutDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> LoadVehicleLockoutItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleLockoutItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> LoadVehicleLockoutItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleLockoutItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleLockoutItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "GOUserId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleLockoutService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleLockoutItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleLockoutItemsAsync(false);
            }
            finally
            {
                __vehicleLockoutItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleLockoutDataObject> VehicleLockoutItems 
		{
			get
			{			
				return GetVehicleLockoutItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleLockoutItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("GOUserDataObject") && ObjectsDataSet.RelationsToInclude["GOUserDataObject"].Contains("VehicleLockoutItems");
		}

		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> GetVehicleLockoutItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleLockoutItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleLockoutItemsAsync(forceReload : forceReload);
			}
			var vehicleLockoutItems = ObjectsDataSet.GetRelatedObjects<VehicleLockoutDataObject>(this, "VehicleLockoutItems");							
			vehicleLockoutItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleLockoutItems_CollectionChanged);
				
			return vehicleLockoutItems;
		}

        private void VehicleLockoutItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleLockoutDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleLockout", "GOUserDataObject.VehicleLockoutItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of GOUserDataObject throw an exception while trying to add VehicleLockoutDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._gOUser_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.GOUserId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.GOUserId == default(Nullable<System.Guid>))
							relatedObj.GOUserId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleLockoutDataObject).GOUser = null;
                    // }
                    break;
            }            
        }

		public override void ClearLazyLoadFlags()
		{
			__alertSubscriptionItemsAlreadyLazyLoaded = false;
			__customerAuditItemsCreatedAlreadyLazyLoaded = false;
			__customerAuditItemsDeletedAlreadyLazyLoaded = false;
			__customerAuditItemsModifiedAlreadyLazyLoaded = false;
			__exportJobStatusItemsAlreadyLazyLoaded = false;
			__gOUserDepartmentItemsAlreadyLazyLoaded = false;
			__goUserToCustomerItemsAlreadyLazyLoaded = false;
			__messageHistoryItemsAlreadyLazyLoaded = false;
			__reportSubscriptionItemsAlreadyLazyLoaded = false;
			__revisionItemsAlreadyLazyLoaded = false;
			__userGroupItemsAlreadyLazyLoaded = false;
			__userRoleItemsAlreadyLazyLoaded = false;
			__vehicleLockoutItemsAlreadyLazyLoaded = false;
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadDealerAsync()) != null)
				result.Add(Dealer);
			if ((await LoadGORoleAsync()) != null)
				result.Add(GORole);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadAlertSubscriptionItemsAsync()) != null)
				result.AddRange(AlertSubscriptionItems);
			if ((await LoadCustomerAuditItemsCreatedAsync()) != null)
				result.AddRange(CustomerAuditItemsCreated);
			if ((await LoadCustomerAuditItemsDeletedAsync()) != null)
				result.AddRange(CustomerAuditItemsDeleted);
			if ((await LoadCustomerAuditItemsModifiedAsync()) != null)
				result.AddRange(CustomerAuditItemsModified);
			if ((await LoadDealerDriverAsync()) != null)
				result.Add(DealerDriver);
			if ((await LoadExportJobStatusItemsAsync()) != null)
				result.AddRange(ExportJobStatusItems);
			if ((await LoadGOUserDepartmentItemsAsync()) != null)
				result.AddRange(GOUserDepartmentItems);
			if ((await LoadGoUserToCustomerItemsAsync()) != null)
				result.AddRange(GoUserToCustomerItems);
			if ((await LoadMessageHistoryItemsAsync()) != null)
				result.AddRange(MessageHistoryItems);
			if ((await LoadPersonAsync()) != null)
				result.Add(Person);
			if ((await LoadReportSubscriptionItemsAsync()) != null)
				result.AddRange(ReportSubscriptionItems);
			if ((await LoadRevisionItemsAsync()) != null)
				result.AddRange(RevisionItems);
			if ((await LoadTagAsync()) != null)
				result.Add(Tag);
			if ((await LoadUserGroupItemsAsync()) != null)
				result.AddRange(UserGroupItems);
			if ((await LoadUserRoleItemsAsync()) != null)
				result.AddRange(UserRoleItems);
			if ((await LoadVehicleLockoutItemsAsync()) != null)
				result.AddRange(VehicleLockoutItems);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				Dealer == other ||
				(other is DealerDataObject && (DealerId != default(Nullable<System.Guid>)) && (DealerId == (other as DealerDataObject).Id)) || 
				GORole == other ||
				(other is GORoleDataObject && (GORoleName != default(System.String)) && (GORoleName == (other as GORoleDataObject).Name)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetAllowedDepartmentNamesValue(System.String valueToSet)
		{
			SetAllowedDepartmentNamesValue(valueToSet, true, true);
		}

		public virtual void SetAllowedDepartmentNamesValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_allowedDepartmentNames != valueToSet)
			{
				_allowedDepartmentNames = valueToSet;

				OnPropertyChanged("AllowedDepartmentNames", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The AllowedDepartmentNames property of the GOUser DataObject</summary>
        public virtual System.String AllowedDepartmentNames 
		{
			get	{ return String.IsNullOrEmpty(_allowedDepartmentNames) ? null : _allowedDepartmentNames; }
			
			
			set
			{
				SetAllowedDepartmentNamesValue(value);
			}
		}		
			
			
		public virtual void SetBlockedValue(System.Boolean valueToSet)
		{
			SetBlockedValue(valueToSet, true, true);
		}

		public virtual void SetBlockedValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_blocked != valueToSet)
			{
				_blocked = valueToSet;

				OnPropertyChanged("Blocked", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Blocked property of the GOUser DataObject</summary>
        public virtual System.Boolean Blocked 
		{
			get	{ return _blocked;}
			
			
			set
			{
				SetBlockedValue(value);
			}
		}		
			
			
		public virtual void SetDealerAdminValue(Nullable<System.Boolean> valueToSet)
		{
			SetDealerAdminValue(valueToSet, true, true);
		}

		public virtual void SetDealerAdminValue(Nullable<System.Boolean> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_dealerAdmin != valueToSet)
			{
				_dealerAdmin = valueToSet;

				OnPropertyChanged("DealerAdmin", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Dealer Admin property of the GOUser DataObject</summary>
        public virtual Nullable<System.Boolean> DealerAdmin 
		{
			get	{ return _dealerAdmin;}
			
			
			set
			{
				SetDealerAdminValue(value);
			}
		}		
			
			
		public virtual void SetDealerIdValue(Nullable<System.Guid> valueToSet)
		{
			SetDealerIdValue(valueToSet, true, true);
		}

		public virtual void SetDealerIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_dealerId != valueToSet)
			{
				_dealerId = valueToSet;

				OnPropertyChanged("DealerId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DealerId property of the GOUser DataObject</summary>
        public virtual Nullable<System.Guid> DealerId 
		{
			get	{ return _dealerId;}
			
			
			set
			{
				SetDealerIdValue(value);
			}
		}		
			
			
		public virtual void SetEmailAddressValue(System.String valueToSet)
		{
			SetEmailAddressValue(valueToSet, true, true);
		}

		public virtual void SetEmailAddressValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_emailAddress != valueToSet)
			{
				_emailAddress = valueToSet;

				OnPropertyChanged("EmailAddress", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Email property of the GOUser DataObject</summary>
        public virtual System.String EmailAddress 
		{
			get	{ return _emailAddress; }
			
			
			set
			{
				SetEmailAddressValue(value);
			}
		}		
			
			
		public virtual void SetEmailChangeValidationInProgressValue(System.Boolean valueToSet)
		{
			SetEmailChangeValidationInProgressValue(valueToSet, true, true);
		}

		public virtual void SetEmailChangeValidationInProgressValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_emailChangeValidationInProgress != valueToSet)
			{
				_emailChangeValidationInProgress = valueToSet;

				OnPropertyChanged("EmailChangeValidationInProgress", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Email change validation in progress property of the GOUser DataObject</summary>
        public virtual System.Boolean EmailChangeValidationInProgress 
		{
			get	{ return _emailChangeValidationInProgress;}
			
			
			set
			{
				SetEmailChangeValidationInProgressValue(value);
			}
		}		
			
			
		public virtual void SetEmailValidatedValue(System.Boolean valueToSet)
		{
			SetEmailValidatedValue(valueToSet, true, true);
		}

		public virtual void SetEmailValidatedValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_emailValidated != valueToSet)
			{
				_emailValidated = valueToSet;

				OnPropertyChanged("EmailValidated", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Email Verified? property of the GOUser DataObject</summary>
        public virtual System.Boolean EmailValidated 
		{
			get	{ return _emailValidated;}
			
			
			set
			{
				SetEmailValidatedValue(value);
			}
		}		
			
			
		public virtual void SetExternalUserIdValue(System.String valueToSet)
		{
			SetExternalUserIdValue(valueToSet, true, true);
		}

		public virtual void SetExternalUserIdValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_externalUserId != valueToSet)
			{
				_externalUserId = valueToSet;

				OnPropertyChanged("ExternalUserId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The ExternalUserId property of the GOUser DataObject</summary>
        public virtual System.String ExternalUserId 
		{
			get	{ return String.IsNullOrEmpty(_externalUserId) ? null : _externalUserId; }
			
			
			set
			{
				SetExternalUserIdValue(value);
			}
		}		
			
			
		public virtual void SetFirstNameValue(System.String valueToSet)
		{
			SetFirstNameValue(valueToSet, true, true);
		}

		public virtual void SetFirstNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_firstName != valueToSet)
			{
				_firstName = valueToSet;

				OnPropertyChanged("FirstName", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The First Name property of the GOUser DataObject</summary>
        public virtual System.String FirstName 
		{
			get	{ return String.IsNullOrEmpty(_firstName) ? null : _firstName; }
			
			
			set
			{
				SetFirstNameValue(value);
			}
		}		
			
			
		public virtual void SetFullNameValue(System.String valueToSet)
		{
			SetFullNameValue(valueToSet, true, true);
		}

		public virtual void SetFullNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_fullName != valueToSet)
			{
				_fullName = valueToSet;

				OnPropertyChanged("FullName", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Full Name property of the GOUser DataObject</summary>
        public virtual System.String FullName 
		{
			get	{ return String.IsNullOrEmpty(_fullName) ? null : _fullName; }
			
			
			set
			{
				SetFullNameValue(value);
			}
		}		
			
			
		public virtual void SetGORoleNameValue(System.String valueToSet)
		{
			SetGORoleNameValue(valueToSet, true, true);
		}

		public virtual void SetGORoleNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_gORoleName != valueToSet)
			{
				_gORoleName = valueToSet;

				// GORoleName is a FK. Setting its value should result in a event
				OnPropertyChanged("GORole", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("GORoleName", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The GORoleName property of the GOUser DataObject</summary>
        public virtual System.String GORoleName 
		{
			get	{ return String.IsNullOrEmpty(_gORoleName) ? null : _gORoleName; }
			
			
			set
			{
				SetGORoleNameValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the GOUser DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetLastNameValue(System.String valueToSet)
		{
			SetLastNameValue(valueToSet, true, true);
		}

		public virtual void SetLastNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_lastName != valueToSet)
			{
				_lastName = valueToSet;

				OnPropertyChanged("LastName", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Surname property of the GOUser DataObject</summary>
        public virtual System.String LastName 
		{
			get	{ return String.IsNullOrEmpty(_lastName) ? null : _lastName; }
			
			
			set
			{
				SetLastNameValue(value);
			}
		}		
			
			
		public virtual void SetNewEmailAddressValue(System.String valueToSet)
		{
			SetNewEmailAddressValue(valueToSet, true, true);
		}

		public virtual void SetNewEmailAddressValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_newEmailAddress != valueToSet)
			{
				_newEmailAddress = valueToSet;

				OnPropertyChanged("NewEmailAddress", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The New Email property of the GOUser DataObject</summary>
        public virtual System.String NewEmailAddress 
		{
			get	{ return String.IsNullOrEmpty(_newEmailAddress) ? null : _newEmailAddress; }
			
			
			set
			{
				SetNewEmailAddressValue(value);
			}
		}		
			
			
		public virtual void SetNewEmailValidatedValue(Nullable<System.Boolean> valueToSet)
		{
			SetNewEmailValidatedValue(valueToSet, true, true);
		}

		public virtual void SetNewEmailValidatedValue(Nullable<System.Boolean> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_newEmailValidated != valueToSet)
			{
				_newEmailValidated = valueToSet;

				OnPropertyChanged("NewEmailValidated", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The New Email Verified? property of the GOUser DataObject</summary>
        public virtual Nullable<System.Boolean> NewEmailValidated 
		{
			get	{ return _newEmailValidated;}
			
			
			set
			{
				SetNewEmailValidatedValue(value);
			}
		}		
			
			
		public virtual void SetPasswordValue(System.String valueToSet)
		{
			SetPasswordValue(valueToSet, true, true);
		}

		public virtual void SetPasswordValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_password != valueToSet)
			{
				_password = valueToSet;

				OnPropertyChanged("Password", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Password property of the GOUser DataObject</summary>
        public virtual System.String Password 
		{
			get	{ return _password; }
			
			
			set
			{
				SetPasswordValue(value);
			}
		}		
			
			
		public virtual void SetPasswordExpiryValue(Nullable<System.DateTime> valueToSet)
		{
			SetPasswordExpiryValue(valueToSet, true, true);
		}

		public virtual void SetPasswordExpiryValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_passwordExpiry != null)
				{
					_passwordExpiry = null;
					OnPropertyChanged("PasswordExpiry", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_passwordExpiry != DateTime.MinValue.ToUniversalTime())
				{
					_passwordExpiry = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("PasswordExpiry", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_passwordExpiry != DateTime.MaxValue.ToUniversalTime())
				{
					_passwordExpiry = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("PasswordExpiry", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_passwordExpiry != valueToSet ||
                (_passwordExpiry != null && ((DateTime)_passwordExpiry).Kind == DateTimeKind.Unspecified))
			{
				_passwordExpiry = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("PasswordExpiry", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Password Expiry property of the GOUser DataObject</summary>
        public virtual Nullable<System.DateTime> PasswordExpiry 
		{
			get	{ return _passwordExpiry;}
			
			
			set
			{
				SetPasswordExpiryValue(value);
			}
		}		
			
			
		public virtual void SetPreferredLocaleValue(Nullable<LocaleEnum> valueToSet)
		{
			SetPreferredLocaleValue(valueToSet, true, true);
		}

		public virtual void SetPreferredLocaleValue(Nullable<LocaleEnum> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_preferredLocale != valueToSet)
			{
				_preferredLocale = valueToSet;

				OnPropertyChanged("PreferredLocale", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PreferredLocaleDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The PreferredLocale property of the GOUser DataObject</summary>
        public virtual Nullable<LocaleEnum> PreferredLocale 
		{
			get	{ return _preferredLocale;}
			
			
			set
			{
				SetPreferredLocaleValue(value);
			}
		}		
      public virtual string PreferredLocaleDisplayString
		{
			get
			{
				if (PreferredLocale == null)
					return "-";

				return PreferredLocaleEnumDisplayNameCollection.Where(v => v.Value == PreferredLocale).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<LocaleEnum>> PreferredLocaleEnumDisplayNameCollection
	    {
	        get
	        {
                return LocaleEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetPreferredLocaleStringValue(System.String valueToSet)
		{
			SetPreferredLocaleStringValue(valueToSet, true, true);
		}

		public virtual void SetPreferredLocaleStringValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_preferredLocaleString != valueToSet)
			{
				_preferredLocaleString = valueToSet;

				OnPropertyChanged("PreferredLocaleString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The PreferredLocaleString property of the GOUser DataObject</summary>
        public virtual System.String PreferredLocaleString 
		{
			get	{ return String.IsNullOrEmpty(_preferredLocaleString) ? null : _preferredLocaleString; }
			
			
			set
			{
				SetPreferredLocaleStringValue(value);
			}
		}		
			
			
		public virtual void SetUnregisteredValue(System.Boolean valueToSet)
		{
			SetUnregisteredValue(valueToSet, true, true);
		}

		public virtual void SetUnregisteredValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_unregistered != valueToSet)
			{
				_unregistered = valueToSet;

				OnPropertyChanged("Unregistered", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Unregistered property of the GOUser DataObject</summary>
        public virtual System.Boolean Unregistered 
		{
			get	{ return _unregistered;}
			
			
			set
			{
				SetUnregisteredValue(value);
			}
		}		
			
			
		public virtual void SetUserNameValue(System.String valueToSet)
		{
			SetUserNameValue(valueToSet, true, true);
		}

		public virtual void SetUserNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_userName != valueToSet)
			{
				_userName = valueToSet;

				OnPropertyChanged("UserName", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The User Name property of the GOUser DataObject</summary>
        public virtual System.String UserName 
		{
			get	{ return _userName; }
			
			
			set
			{
				SetUserNameValue(value);
			}
		}		
			
			
		public virtual void SetUserValidatedValue(System.Boolean valueToSet)
		{
			SetUserValidatedValue(valueToSet, true, true);
		}

		public virtual void SetUserValidatedValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_userValidated != valueToSet)
			{
				_userValidated = valueToSet;

				OnPropertyChanged("UserValidated", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Admin Approved? property of the GOUser DataObject</summary>
        public virtual System.Boolean UserValidated 
		{
			get	{ return _userValidated;}
			
			
			set
			{
				SetUserValidatedValue(value);
			}
		}		
			
			
		public virtual void SetWebsiteAccessLevelValue(WebsiteAccessLevelEnum valueToSet)
		{
			SetWebsiteAccessLevelValue(valueToSet, true, true);
		}

		public virtual void SetWebsiteAccessLevelValue(WebsiteAccessLevelEnum valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_websiteAccessLevel != valueToSet)
			{
				_websiteAccessLevel = valueToSet;

				OnPropertyChanged("WebsiteAccessLevel", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("WebsiteAccessLevelDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Website Access Level property of the GOUser DataObject</summary>
        public virtual WebsiteAccessLevelEnum WebsiteAccessLevel 
		{
			get	{ return _websiteAccessLevel;}
			
			
			set
			{
				SetWebsiteAccessLevelValue(value);
			}
		}		
      public virtual string WebsiteAccessLevelDisplayString
		{
			get
			{
				return WebsiteAccessLevelEnumDisplayNameCollection.Where(v => v.Value == WebsiteAccessLevel).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<WebsiteAccessLevelEnum>> WebsiteAccessLevelEnumDisplayNameCollection
	    {
	        get
	        {
                return WebsiteAccessLevelEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetWebsiteAccessLevelValueValue(Nullable<System.Int16> valueToSet)
		{
			SetWebsiteAccessLevelValueValue(valueToSet, true, true);
		}

		public virtual void SetWebsiteAccessLevelValueValue(Nullable<System.Int16> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_websiteAccessLevelValue != valueToSet)
			{
				_websiteAccessLevelValue = valueToSet;

				OnPropertyChanged("WebsiteAccessLevelValue", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The WebsiteAccessLevelValue property of the GOUser DataObject</summary>
        public virtual Nullable<System.Int16> WebsiteAccessLevelValue 
		{
			get	{ return _websiteAccessLevelValue;}
			
			
			set
			{
				SetWebsiteAccessLevelValueValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
			var _alertSubscriptionItems = GetAlertSubscriptionItemsAsync(false).Result;
			if (_alertSubscriptionItems != null)
            {
                foreach (var item in _alertSubscriptionItems)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUser.", propertyName), callers);                    
                }
            }
			var _customerAuditItemsCreated = GetCustomerAuditItemsCreatedAsync(false).Result;
			if (_customerAuditItemsCreated != null)
            {
                foreach (var item in _customerAuditItemsCreated)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUserWhoCreatedThisCustomer.", propertyName), callers);                    
                }
            }
			var _customerAuditItemsDeleted = GetCustomerAuditItemsDeletedAsync(false).Result;
			if (_customerAuditItemsDeleted != null)
            {
                foreach (var item in _customerAuditItemsDeleted)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUserWhoDeletedThisCustomer.", propertyName), callers);                    
                }
            }
			var _customerAuditItemsModified = GetCustomerAuditItemsModifiedAsync(false).Result;
			if (_customerAuditItemsModified != null)
            {
                foreach (var item in _customerAuditItemsModified)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUserWhoModifiedThisCustomer.", propertyName), callers);                    
                }
            }
			var dealerDriver = GetDealerDriverAsync(false).Result;
			if (dealerDriver != null && this.IsDirty)
            {
				dealerDriver.NotifyPropertyChanged("GOUser." + propertyName, callers);
			}
			var _exportJobStatusItems = GetExportJobStatusItemsAsync(false).Result;
			if (_exportJobStatusItems != null)
            {
                foreach (var item in _exportJobStatusItems)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUser.", propertyName), callers);                    
                }
            }
			var _gOUserDepartmentItems = GetGOUserDepartmentItemsAsync(false).Result;
			if (_gOUserDepartmentItems != null)
            {
                foreach (var item in _gOUserDepartmentItems)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUser.", propertyName), callers);                    
                }
            }
			var _goUserToCustomerItems = GetGoUserToCustomerItemsAsync(false).Result;
			if (_goUserToCustomerItems != null)
            {
                foreach (var item in _goUserToCustomerItems)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUser.", propertyName), callers);                    
                }
            }
			var _messageHistoryItems = GetMessageHistoryItemsAsync(false).Result;
			if (_messageHistoryItems != null)
            {
                foreach (var item in _messageHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUser.", propertyName), callers);                    
                }
            }
			var person = GetPersonAsync(false).Result;
			if (person != null && this.IsDirty)
            {
				person.NotifyPropertyChanged("GOUser." + propertyName, callers);
			}
			var _reportSubscriptionItems = GetReportSubscriptionItemsAsync(false).Result;
			if (_reportSubscriptionItems != null)
            {
                foreach (var item in _reportSubscriptionItems)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUser.", propertyName), callers);                    
                }
            }
			var _revisionItems = GetRevisionItemsAsync(false).Result;
			if (_revisionItems != null)
            {
                foreach (var item in _revisionItems)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUser.", propertyName), callers);                    
                }
            }
			var tag = GetTagAsync(false).Result;
			if (tag != null && this.IsDirty)
            {
				tag.NotifyPropertyChanged("GOUser." + propertyName, callers);
			}
			var _userGroupItems = GetUserGroupItemsAsync(false).Result;
			if (_userGroupItems != null)
            {
                foreach (var item in _userGroupItems)
                {
                    item.NotifyPropertyChanged(String.Concat("User.", propertyName), callers);                    
                }
            }
			var _userRoleItems = GetUserRoleItemsAsync(false).Result;
			if (_userRoleItems != null)
            {
                foreach (var item in _userRoleItems)
                {
                    item.NotifyPropertyChanged(String.Concat("User.", propertyName), callers);                    
                }
            }
			var _vehicleLockoutItems = GetVehicleLockoutItemsAsync(false).Result;
			if (_vehicleLockoutItems != null)
            {
                foreach (var item in _vehicleLockoutItems)
                {
                    item.NotifyPropertyChanged(String.Concat("GOUser.", propertyName), callers);                    
                }
            }
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<GOUserDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is GOUserDataObject))
				return false;

			var p = (GOUserDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.AllowedDepartmentNames == p.AllowedDepartmentNames;
			fieldsComparison &= this.Blocked == p.Blocked;
			fieldsComparison &= this.DealerAdmin == p.DealerAdmin;
			fieldsComparison &= this.DealerId == p.DealerId;
			fieldsComparison &= this.EmailAddress == p.EmailAddress;
			fieldsComparison &= this.EmailChangeValidationInProgress == p.EmailChangeValidationInProgress;
			fieldsComparison &= this.EmailValidated == p.EmailValidated;
			fieldsComparison &= this.ExternalUserId == p.ExternalUserId;
			fieldsComparison &= this.FirstName == p.FirstName;
			fieldsComparison &= this.FullName == p.FullName;
			fieldsComparison &= this.GORoleName == p.GORoleName;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.LastName == p.LastName;
			fieldsComparison &= this.NewEmailAddress == p.NewEmailAddress;
			fieldsComparison &= this.NewEmailValidated == p.NewEmailValidated;
			fieldsComparison &= this.Password == p.Password;
			fieldsComparison &= this.PasswordExpiry == p.PasswordExpiry;
			fieldsComparison &= this.PreferredLocale == p.PreferredLocale;
			fieldsComparison &= this.PreferredLocaleString == p.PreferredLocaleString;
			fieldsComparison &= this.Unregistered == p.Unregistered;
			fieldsComparison &= this.UserName == p.UserName;
			fieldsComparison &= this.UserValidated == p.UserValidated;
			fieldsComparison &= this.WebsiteAccessLevel == p.WebsiteAccessLevel;
			fieldsComparison &= this.WebsiteAccessLevelValue == p.WebsiteAccessLevelValue;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// PasswordExpiry is a local datetime: Convert to UTC for server-side handling and storing
			if (this._passwordExpiry_WithTimezoneOffset != null)
			{
				this.PasswordExpiry = ((DateTimeOffset)this._passwordExpiry_WithTimezoneOffset).UtcDateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class GOUserCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public GOUserCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public GOUserCollectionContainer()
		{
		}
		
		public GOUserCollectionContainer Construct(DataObjectCollection<GOUserDataObject> gOUserItems)
        {
            if (gOUserItems == null)
                return this;
				
			this.PrimaryKeys = gOUserItems.Select(c => c.PrimaryKey).ToList();
            if (gOUserItems.ObjectsDataSet == null)
            {
                gOUserItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = gOUserItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = gOUserItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<GOUserDataObject> ExtractGOUserItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<GOUserDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<GOUserDataObject>(typeof(GOUserDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class GOUserContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public GOUserContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual GOUserContainer Construct(GOUserDataObject gOUser, bool includeDirtyObjectsOnly = false)
		{
            if (gOUser == null)
                return this;

			this.PrimaryKey = gOUser.PrimaryKey;
			
            if (gOUser.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(gOUser);
            }

			if(gOUser.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity GOUser", "Unable to set a dataset to the entity. Container may not be initialized", "GOUserDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : GOUser");
			}

			if(gOUser.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in GOUserDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "GOUserDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in GOUserDataObject");
			}
			this.InternalObjectId = (int) gOUser.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? gOUser.ObjectsDataSet.CloneDirtyObjects() : gOUser.ObjectsDataSet;

			return this;
		}
		
		public GOUserDataObject ExtractGOUser()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<GOUserDataObject>(typeof(GOUserDataObject), InternalObjectId);
			
			return result;
        }	
	}

}