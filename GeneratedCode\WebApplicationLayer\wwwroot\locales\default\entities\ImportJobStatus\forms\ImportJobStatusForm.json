﻿{
  "title": "Import Job Details",
  "description": "Automatically generated entity for Import Export feature. This entity represents an Import job status (succeeded, failed, in progress...)",
  "formFields": {
    "Name": {
        "label": "Import Name",
        "description": "Import Name"
    },
    "Start": {
        "label": "Date Start",
        "description": "Date Start"
    },
    "End": {
        "label": "Date End",
        "description": "Date End"
    },
    "TaskStatus": {
        "label": "Status",
        "description": "Status"
    },
    "Progress": {
        "label": "Percent Completed",
        "description": "Percent Completed"
    },
    "FileToUpload": {
        "label": "Imported File",
        "description": "Imported File"
    },
    "FileToUploadFileSize": {
        "label": "Imported File Size (kb)",
        "description": "Imported File Size (kb)"
    },
    "ImportJobLogItems": {
        "label": "Import Job LogItems",
        "description": "Import Job LogItems"
    }
  },
  "messages": {
    "noDataMessage": "No Import Job Status data available"
  },
  "formButtons": {
    "CreateNew": "New ",
    "CancelEdit": "Cancel",
    "PreOperationalChecklistImport": "PreOperationalChecklistImport",
    "VehicleOtherSettingsImport": "VehicleOtherSettingsImport",
    "GeneralLicenseImport": "GeneralLicenseImport",
    "SiteImport": "Sites",
    "VehilceImport": "Vehicles",
    "DepartmentImport": "Departments",
    "VehilceImport1": "VehilceImport",
    "LicenseByModelImport": "LicenseByModelImport",
    "PersonImport": "PersonImport",
    "VehicleAccessImport1": "VehicleAccessImport",
    "SupervisorAccessImport": "SupervisorAccessImport",
    "DriverImport": "DriverImport",
    "CardImport": "CardImport",
    "SpareModuleImport": "SpareModuleImport",
    "UpdateVehicleLastServiceDateImport": "UpdateVehicleLastServiceDateImport",
    "CustomerImport": "Customers",
    "GeneralLicenseImport2": "GeneralLicenseImport",
    "SlamcoreAlertHistory": "SlamcoreAlertHistory",
    "PreopXLSXImport": "PreopXLSXImport",
    "GeneralLicenseImport1": "GeneralLicenseImport",
    "VehicleAccessImport": "VehicleAccessImport",
    "ChecklistSettingImport": "ChecklistSettingImport",
    "DealerCategoryImport": "DealerCategoryImport"
  }
} 