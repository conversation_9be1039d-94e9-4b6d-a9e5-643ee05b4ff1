﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge DataObject -> ORMEntity
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	public partial class CustomerFeatureSubscriptionDataObject
	{
		protected IThreadContext _threadContext => _serviceProvider.GetRequiredService<IThreadContext>();
		protected INHibernateSessionController _nhibernateSessionController => _serviceProvider.GetRequiredService<INHibernateSessionController>();

		public override IORMEntity ToORMEntity()
		{
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);

			var result = new ORMCustomerFeatureSubscription(); 

			CopyTo(result, session);

			// Ensure no copy of result (instance with same key) in session
			Evict(result, session);

			return result;
		}

		protected void CopyTo(ORMCustomerFeatureSubscription x, NHibernate.ISession session)
		{
			x.Id = Id;
			x.Description = Description?.Truncate(100);
			x.Name = Name?.Truncate(50);
			x.CanAccessSlamcore = CanAccessSlamcore;
			x.IsEnabled = IsEnabled;
			x.IsTagged = IsTagged;
			x.HasAdditionalHardwaresAccess = HasAdditionalHardwaresAccess;
		}
 
		private void Evict(ORMCustomerFeatureSubscription result, NHibernate.ISession session)
		{
			foreach (var entity in session.GetSessionImplementation().PersistenceContext.EntitiesByKey.Values.ToArray())
			{
				var evictee = entity as ORMCustomerFeatureSubscription;

				if (evictee != null && evictee.Id == result.Id)
				{
					session.Evict(evictee);
				}
			}
		}

	}
}