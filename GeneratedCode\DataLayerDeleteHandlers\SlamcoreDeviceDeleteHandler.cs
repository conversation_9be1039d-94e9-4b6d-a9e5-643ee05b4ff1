﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;

using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DeleteHandlers
{
	public partial class SlamcoreDeviceDeleteHandler : DeleteHandlerBase<SlamcoreDeviceDataObject>
	{
		
        public SlamcoreDeviceDeleteHandler(IServiceProvider serviceProvider, IDataProvider<SlamcoreDeviceDataObject> tentityDataProvider, ITransactionProvider transactionProvider, IEntityDataProvider entityDataProvider) : base(serviceProvider, tentityDataProvider, transactionProvider, entityDataProvider)
        {
        }

		public override async Task RippleDeleteAsync(SlamcoreDeviceDataObject instance, Parameters parameters, DataProviderDeleteSettings settings)
		{
			// Set resync flag initially so that if any processing is done, it's on the latest copy of the data
			NeedResync = true;

			// SlamcoreDevice.SlamcoreAPIKey (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSlamcoreAPIKeyAsync(parameters, skipSecurity: true);
				var item = instance.SlamcoreAPIKey;
				
	
				// Clear instance.SlamcoreAPIKey reference so that item can be deleted from the db without FK constraint blocking it
				// i.e. We are the FK side and need to clear our reference to PK side so that PK side not blocked by us
				if (item != null)
				{
					instance.SlamcoreAPIKey = null;
					await SaveAsync(instance);
			
					await DeleteAsync(item, parameters, settings, instance);
				}
			}
			// SlamcoreDevice.SlamcoreAwareAuthenticationDetails (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSlamcoreAwareAuthenticationDetailsAsync(parameters, skipSecurity: true);
				var item = instance.SlamcoreAwareAuthenticationDetails;
				
	
				// Clear instance.SlamcoreAwareAuthenticationDetails reference so that item can be deleted from the db without FK constraint blocking it
				// i.e. We are the FK side and need to clear our reference to PK side so that PK side not blocked by us
				if (item != null)
				{
					instance.SlamcoreAwareAuthenticationDetails = null;
					await SaveAsync(instance);
			
					await DeleteAsync(item, parameters, settings, instance);
				}
			}
			// SlamcoreDevice.SlamcoreDeviceHistoryItems (Protected) (i.e. Unable to delete SlamcoreDevice instances because SlamcoreDeviceHistoryItems.SlamcoreDevice is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSlamcoreDeviceHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.SlamcoreDeviceHistoryItems);
			}
			// SlamcoreDevice.SlamcorePedestrianDetectionItems (Protected) (i.e. Unable to delete SlamcoreDevice instances because SlamcorePedestrianDetectionItems.SlamcoreDevice is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSlamcorePedestrianDetectionItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.SlamcorePedestrianDetectionItems);
			}
			// SlamcoreDevice.Vehicle (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleAsync(parameters, skipSecurity: true);
				// We are the FK side entity and the PK side is optional, so no action required here (because when we're deleted, any reference(s) to Vehicle(s) is (are) deleted with us)	
			}
			// SlamcoreDevice.VehicleSlamcoreLocationHistoryItems (Protected) (i.e. Unable to delete SlamcoreDevice instances because VehicleSlamcoreLocationHistoryItems.SlamcoreDevice is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleSlamcoreLocationHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.VehicleSlamcoreLocationHistoryItems);
			}
		}
	}
}