﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Data.DataProviders.Dispatchers
{
    public class SessionDataProviderDispatcher : IDataProviderDispatcher<SessionDataObject>
    {
		protected IServiceProvider _serviceProvider;
		
		public SessionDataProviderDispatcher(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
		
		protected IDataProvider<DriverDataObject> driverDataProvider => _serviceProvider.GetService<IDataProvider<DriverDataObject>>();
		protected IDataProvider<ChecklistResultDataObject> checklistResultDataProvider => _serviceProvider.GetService<IDataProvider<ChecklistResultDataObject>>();
		protected IDataProvider<VehicleGPSDataObject> vehicleGPSDataProvider => _serviceProvider.GetService<IDataProvider<VehicleGPSDataObject>>();
		protected IDataProvider<DetailedSessionViewDataObject> detailedSessionViewDataProvider => _serviceProvider.GetService<IDataProvider<DetailedSessionViewDataObject>>();
		protected IDataProvider<DetailedVORSessionStoreProcedureDataObject> detailedVORSessionStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<DetailedVORSessionStoreProcedureDataObject>>();
		protected IDataProvider<PSTATDetailsDataObject> pSTATDetailsDataProvider => _serviceProvider.GetService<IDataProvider<PSTATDetailsDataObject>>();
		protected IDataProvider<VehicleDataObject> vehicleDataProvider => _serviceProvider.GetService<IDataProvider<VehicleDataObject>>();
		protected IDataProvider<VehicleLockoutDataObject> vehicleLockoutDataProvider => _serviceProvider.GetService<IDataProvider<VehicleLockoutDataObject>>();
		protected IDataProvider<VehicleLastGPSLocationViewDataObject> vehicleLastGPSLocationViewDataProvider => _serviceProvider.GetService<IDataProvider<VehicleLastGPSLocationViewDataObject>>();
		protected IDataProvider<GPSHistoryDataObject> gPSHistoryDataProvider => _serviceProvider.GetService<IDataProvider<GPSHistoryDataObject>>();
		protected IDataProvider<ImpactDataObject> impactDataProvider => _serviceProvider.GetService<IDataProvider<ImpactDataObject>>();
		protected IDataProvider<SessionDetailsDataObject> sessionDetailsDataProvider => _serviceProvider.GetService<IDataProvider<SessionDetailsDataObject>>();

        public async Task DispatchForEntityAsync(SessionDataObject entity, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("Session", parameters);

            foreach (var include in includes)
            {
	
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
                  case "driver":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Driver"))
									break;

								try
								{
									var objectToFetch = await driverDataProvider.GetAsync(new DriverDataObject(entity.DriverId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "checklistresults":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ChecklistResults"))
									break;

								try
								{
									var objectToFetch = await checklistResultDataProvider.GetCollectionAsync(null, "SessionId1 == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclegpsitems":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleGPSItems"))
									break;

								try
								{
									var objectToFetch = await vehicleGPSDataProvider.GetCollectionAsync(null, "SessionId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "detailedsessionviewitems":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DetailedSessionViewItems"))
									break;

								try
								{
									var objectToFetch = await detailedSessionViewDataProvider.GetCollectionAsync(null, "SessionId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "detailedvorsessionstoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DetailedVORSessionStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await detailedVORSessionStoreProcedureDataProvider.GetCollectionAsync(null, "SessionId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "pstatdetailsitems":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("PSTATDetailsItems"))
									break;

								try
								{
									var objectToFetch = await pSTATDetailsDataProvider.GetCollectionAsync(null, "SessionId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehicle":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Vehicle"))
									break;

								try
								{
									var objectToFetch = await vehicleDataProvider.GetAsync(new VehicleDataObject(entity.VehicleId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclelockouts":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleLockouts"))
									break;

								try
								{
									var objectToFetch = await vehicleLockoutDataProvider.GetCollectionAsync(null, "SessionId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclelastgpslocationview":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleLastGPSLocationView"))
									break;

								try
								{
									var objectToFetch = await vehicleLastGPSLocationViewDataProvider.GetCollectionAsync(null, "SessionId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "gpshistoryitems":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("GPSHistoryItems"))
									break;

								try
								{
									var objectToFetch = await gPSHistoryDataProvider.GetCollectionAsync(null, "SessionId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "impacts":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Impacts"))
									break;

								try
								{
									var objectToFetch = await impactDataProvider.GetCollectionAsync(null, "SessionId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "sessiondetailsitems":
							{
								// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("SessionDetailsItems"))
									break;

								try
								{
									var objectToFetch = await sessionDetailsDataProvider.GetCollectionAsync(null, "SessionId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
						default:
							throw new ApplicationException("Session Entity has no relation named " + relation);
					}
          }        
		}

        public async Task DispatchForEntityCollectionAsync(IEnumerable<SessionDataObject> entities, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("Session", parameters);

            foreach (var include in includes)
            {
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
						case "driver":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Driver"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.DriverId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await driverDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "checklistresults":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ChecklistResults"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await checklistResultDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId1))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclegpsitems":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleGPSItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleGPSDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "detailedsessionviewitems":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DetailedSessionViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await detailedSessionViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "detailedvorsessionstoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DetailedVORSessionStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await detailedVORSessionStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "pstatdetailsitems":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("PSTATDetailsItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await pSTATDetailsDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehicle":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Vehicle"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.VehicleId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclelockouts":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleLockouts"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleLockoutDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclelastgpslocationview":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleLastGPSLocationView"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleLastGPSLocationViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "gpshistoryitems":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("GPSHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await gPSHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "impacts":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Impacts"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await impactDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "sessiondetailsitems":
                        {
							// custom code can implement IPrefetch<ORMSession> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("SessionDetailsItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await sessionDetailsDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.SessionId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
                  default:
                        throw new ApplicationException("Session Entity has no relation named " + relation);
					}
            }        
        }
	}
}