﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMSlamcoreAwareAuthenticationDetails" 
		table="[SlamcoreAwareAuthenticationDetails]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="Password" >
            <column name="`Password`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="PasswordDisplay" >
            <column name="`PasswordDisplay`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="Username" >
            <column name="`Username`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="UsernameDisplay" >
            <column name="`UsernameDisplay`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 

 

		<one-to-one 
			name="SlamcoreDevice" 
			class="ORMSlamcoreDevice" 
			lazy="no-proxy"
			property-ref = "SlamcoreAwareAuthenticationDetails"
		>
		</one-to-one>



    </class> 

</hibernate-mapping>