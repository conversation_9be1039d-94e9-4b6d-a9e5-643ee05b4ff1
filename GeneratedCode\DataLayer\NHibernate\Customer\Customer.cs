﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge ORMEntity -> DataObject
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	[Serializable]
	public class ORMCustomer : IORMEntity 
	{
		public virtual System.Guid Id { get; set; }
 
		///
		/// All Persistent non-key fields
		///
		public virtual Nullable<System.Int32> CustomerLogoFileSize { get; set; }
		public virtual System.String ConnectionString { get; set; }
		public virtual System.String Addess { get; set; }
		public virtual System.String ContactNumber { get; set; }
		public virtual System.String ContractNumber { get; set; }
		public virtual System.String Prefix { get; set; }
		public virtual System.String Description { get; set; }
		public virtual System.String Email { get; set; }
		public virtual System.String PreferredLocaleString { get; set; }
		public virtual System.String CompanyName { get; set; }
		public virtual System.String CustomerLogoInternalName { get; set; }
		public virtual Nullable<System.DateTime> ContractDate { get; set; }
		public virtual Nullable<System.DateTime> DeletedAtUtc { get; set; }
		public virtual System.Boolean Active { get; set; }
		public virtual Nullable<System.Boolean> DealerCustomer { get; set; }
		public virtual Nullable<System.Int32> PreferredLocale { get; set; }
		public virtual System.String CustomerLogo { get; set; }
 
		///
		/// PK-Side one-to-one relations
		///
		public virtual ORMCustomerAudit CustomerAudit { get; set; } 
 
		///
		/// All FK-Side Relations
		///
		public virtual ORMContactPersonInformation ContactPersonInformation { get; set; }
		public virtual Nullable<System.Guid> ContactPersonInformationId { get; set; }

		public virtual ORMCountry Country { get; set; }
		public virtual System.Guid CountryId { get; set; }

		public virtual ORMCustomerFeatureSubscription CustomerFeatureSubscription { get; set; }
		public virtual Nullable<System.Guid> CustomerFeatureSubscriptionId { get; set; }

		public virtual ORMDealer Dealer { get; set; }
		public virtual System.Guid DealerId { get; set; }

	
 
		///
		/// PK-Side one-to-many relations
		///
		public virtual IList<ORMDriver> DriverItems { get; set; } = new List<ORMDriver>(); 
		// public virtual IList<ORMVehicleUtilizationLastTwelveHoursStoreProcedure> VehicleUtilizationLastTwelveHoursStoreProcedureItems { get; set; } = new List<ORMVehicleUtilizationLastTwelveHoursStoreProcedure>();VehicleUtilizationLastTwelveHoursStoreProcedure is not mapped to the database 
		// public virtual IList<ORMTodaysImpactStoreProcedure> TodaysImpactStoreProcedureItems { get; set; } = new List<ORMTodaysImpactStoreProcedure>();TodaysImpactStoreProcedure is not mapped to the database 
		public virtual IList<ORMCurrentDriverStatusChartView> CurrentDriverStatusChartViewItems { get; set; } = new List<ORMCurrentDriverStatusChartView>(); 
		public virtual IList<ORMCustomerPreOperationalChecklistTemplate> CustomerPreOperationalChecklistTemplateItems { get; set; } = new List<ORMCustomerPreOperationalChecklistTemplate>(); 
		public virtual IList<ORMCustomerToPersonView> CustomerToPersonViewItems { get; set; } = new List<ORMCustomerToPersonView>(); 
		// public virtual IList<ORMVehicleUtilizationLastTwelveHoursView> VehicleUtilizationLastTwelveHoursViewItems { get; set; } = new List<ORMVehicleUtilizationLastTwelveHoursView>();VehicleUtilizationLastTwelveHoursView is not mapped to the database 
		// public virtual IList<ORMIncompletedChecklistView> IncompletedChecklistViewItems { get; set; } = new List<ORMIncompletedChecklistView>();IncompletedChecklistView is not mapped to the database 
		// public virtual IList<ORMDriverLicenseExpiryView> DriverLicenseExpiryViewItems { get; set; } = new List<ORMDriverLicenseExpiryView>();DriverLicenseExpiryView is not mapped to the database 
		public virtual IList<ORMCustomerSnapshot> CustomerSnapshotItems { get; set; } = new List<ORMCustomerSnapshot>(); 
		// public virtual IList<ORMDashboardVehicleCardStoreProcedure> DashboardVehicleCardStoreProcedureItems { get; set; } = new List<ORMDashboardVehicleCardStoreProcedure>();DashboardVehicleCardStoreProcedure is not mapped to the database 
		// public virtual IList<ORMTodaysPreopCheckStoreProcedure> TodaysPreopCheckStoreProcedureItems { get; set; } = new List<ORMTodaysPreopCheckStoreProcedure>();TodaysPreopCheckStoreProcedure is not mapped to the database 
		public virtual IList<ORMAccessGroup> AccessGroupItems { get; set; } = new List<ORMAccessGroup>(); 
		public virtual IList<ORMCustomerSSODetail> CustomerSSODetailItems { get; set; } = new List<ORMCustomerSSODetail>(); 
		// public virtual IList<ORMDashboardDriverCardStoreProcedure> DashboardDriverCardStoreProcedureItems { get; set; } = new List<ORMDashboardDriverCardStoreProcedure>();DashboardDriverCardStoreProcedure is not mapped to the database 
		public virtual IList<ORMDashboardDriverCardView> DashboardCardViewItems { get; set; } = new List<ORMDashboardDriverCardView>(); 
		public virtual IList<ORMDashboardFilter> DashboardFilterItems { get; set; } = new List<ORMDashboardFilter>(); 
		public virtual IList<ORMVehicle> VehicleItems { get; set; } = new List<ORMVehicle>(); 
		// public virtual IList<ORMImpactFrequencyPerWeekMonthView> ImpactFrequencyPerWeekMonthViewItems { get; set; } = new List<ORMImpactFrequencyPerWeekMonthView>();ImpactFrequencyPerWeekMonthView is not mapped to the database 
		// public virtual IList<ORMTodaysPreopCheckView> TodaysPreopCheckViewItems { get; set; } = new List<ORMTodaysPreopCheckView>();TodaysPreopCheckView is not mapped to the database 
		// public virtual IList<ORMChecklistStatusView> ChecklistStatusViewItems { get; set; } = new List<ORMChecklistStatusView>();ChecklistStatusView is not mapped to the database 
		public virtual IList<ORMSite> Sites { get; set; } = new List<ORMSite>(); 
		public virtual IList<ORMEmailGroups> EmailGroupsItems { get; set; } = new List<ORMEmailGroups>(); 
		public virtual IList<ORMCustomerModel> CustomerModelItems { get; set; } = new List<ORMCustomerModel>(); 
		// public virtual IList<ORMImpactFrequencyPerWeekDayView> ImpactFrequencyPerWeekDayViewItems { get; set; } = new List<ORMImpactFrequencyPerWeekDayView>();ImpactFrequencyPerWeekDayView is not mapped to the database 
		public virtual IList<ORMCustomerToModel> CustomerToModelItems { get; set; } = new List<ORMCustomerToModel>(); 
		public virtual IList<ORMDepartment> DepartmentItems { get; set; } = new List<ORMDepartment>(); 
		public virtual IList<ORMDashboardVehicleCardView> DashboardVehicleCardViewItems { get; set; } = new List<ORMDashboardVehicleCardView>(); 
		public virtual IList<ORMGoUserToCustomer> GoUserToCustomerItems { get; set; } = new List<ORMGoUserToCustomer>(); 
		public virtual IList<ORMCurrentVehicleStatusChartView> CurrentVehicleStatusChartViewItems { get; set; } = new List<ORMCurrentVehicleStatusChartView>(); 
		public virtual IList<ORMPerson> PersonItems { get; set; } = new List<ORMPerson>(); 
		public virtual IList<ORMSlamcoreDevice> SlamcoreDeviceItems { get; set; } = new List<ORMSlamcoreDevice>(); 
		// public virtual IList<ORMImpactFrequencyPerTimeSlotView> ImpactFrequencyPerTimeSlotViewItems { get; set; } = new List<ORMImpactFrequencyPerTimeSlotView>();ImpactFrequencyPerTimeSlotView is not mapped to the database 
		// public virtual IList<ORMLoggedHoursVersusSeatHoursView> LoggedHoursVersusSeatHoursViewItems { get; set; } = new List<ORMLoggedHoursVersusSeatHoursView>();LoggedHoursVersusSeatHoursView is not mapped to the database 
		// public virtual IList<ORMDriverLicenseExpiryStoreProcedure> DriverLicenseExpiryStoreProcedureItems { get; set; } = new List<ORMDriverLicenseExpiryStoreProcedure>();DriverLicenseExpiryStoreProcedure is not mapped to the database 
		// public virtual IList<ORMTodaysImpactView> TodaysImpactViewItems { get; set; } = new List<ORMTodaysImpactView>();TodaysImpactView is not mapped to the database 
 
		///
		/// Bridge to DataObject
		///
		public virtual IDataObject ToDataObject(IObjectsDataSet dataset, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
            var session = nHibernateSessionController.GetCurrentSession(threadContext);
            session.Evict(this);

			var x = serviceProvider.GetRequiredService<CustomerDataObject>();

 
			SetProperties(x);

			x.IsDirty = x.IsNew = x.IsMarkedForDeletion = false;

			x.ObjectsDataSet = dataset;
			x.ObjectsDataSet.AddObjectIfDoesNotExist(x);

			// Deep-map prefetch relations
			if (PrefetchAssociations.HasPrefetchForEntity("Customer", dataProviderTransaction?.Parameters))
			{
				SetRelations(x, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction);
			}

			return x;
		}

		protected void SetProperties(CustomerDataObject x)
		{
			x.SetIdValue(Id, false, false);
			x.SetCustomerLogoFileSizeValue(CustomerLogoFileSize, false, false);
			x.SetConnectionStringValue(ConnectionString, false, false);
			x.SetAddessValue(Addess, false, false);
			x.SetContactNumberValue(ContactNumber, false, false);
			x.SetContractNumberValue(ContractNumber, false, false);
			x.SetPrefixValue(Prefix, false, false);
			x.SetDescriptionValue(Description, false, false);
			x.SetEmailValue(Email, false, false);
			x.SetPreferredLocaleStringValue(PreferredLocaleString, false, false);
			x.SetCompanyNameValue(CompanyName, false, false);
			x.SetCustomerLogoInternalNameValue(CustomerLogoInternalName, false, false);
			x.SetContractDateValue(ContractDate, false, false);
			x.SetDeletedAtUtcValue(DeletedAtUtc, false, false);
			x.SetActiveValue(Active, false, false);
			x.SetDealerCustomerValue(DealerCustomer, false, false);
			x.SetPreferredLocaleValue((Nullable<LocaleEnum>)PreferredLocale, false, false);
			x.SetCustomerLogoValue(CustomerLogo, false, false);
			x.SetContactPersonInformationIdValue(this.ContactPersonInformationId, false, false);
			x.SetCountryIdValue(this.CountryId, false, false);
			x.SetCustomerFeatureSubscriptionIdValue(this.CustomerFeatureSubscriptionId, false, false);
			x.SetDealerIdValue(this.DealerId, false, false);
		}

		protected void SetRelations(CustomerDataObject x, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
			var prefetches = PrefetchAssociations.Get("Customer", dataProviderTransaction?.Parameters);

			if (prefetches.Contains("DriverItems") && this.DriverItems.Count > 0)
			{
				var iter = this.DriverItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var driverItemsItem = x.ObjectsDataSet.GetObject(new DriverDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (driverItemsItem == null)
						driverItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DriverDataObject;

					x.DriverItems.Add(driverItemsItem);
				}
			}

			if (prefetches.Contains("CustomerAudit") && this.CustomerAudit != null)
			{
				var customerAudit = x.ObjectsDataSet.GetObject(new CustomerAuditDataObject((System.Guid)this.CustomerAudit.fkCustomerId) { IsNew = false });

				if (customerAudit == null)
					customerAudit = this.CustomerAudit.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerAuditDataObject;

				x.SetCustomerAuditValue(customerAudit);
			}

			if (prefetches.Contains("DashboardCardViewItems") && this.DashboardCardViewItems.Count > 0)
			{
				var iter = this.DashboardCardViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var dashboardCardViewItemsItem = x.ObjectsDataSet.GetObject(new DashboardDriverCardViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (dashboardCardViewItemsItem == null)
						dashboardCardViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DashboardDriverCardViewDataObject;

					x.DashboardCardViewItems.Add(dashboardCardViewItemsItem);
				}
			}

			if (prefetches.Contains("Country") && this.Country != null)
			{
				var country = x.ObjectsDataSet.GetObject(new CountryDataObject((System.Guid)this.Country.Id) { IsNew = false });

				if (country == null)
					country = this.Country.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CountryDataObject;

				x.SetCountryValue(country);
			}

			if (prefetches.Contains("ContactPersonInformation") && this.ContactPersonInformation != null)
			{
				var contactPersonInformation = x.ObjectsDataSet.GetObject(new ContactPersonInformationDataObject((System.Guid)this.ContactPersonInformation.Id) { IsNew = false });

				if (contactPersonInformation == null)
					contactPersonInformation = this.ContactPersonInformation.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as ContactPersonInformationDataObject;

				x.SetContactPersonInformationValue(contactPersonInformation);
			}

			if (prefetches.Contains("EmailGroupsItems") && this.EmailGroupsItems.Count > 0)
			{
				var iter = this.EmailGroupsItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var emailGroupsItemsItem = x.ObjectsDataSet.GetObject(new EmailGroupsDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (emailGroupsItemsItem == null)
						emailGroupsItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as EmailGroupsDataObject;

					x.EmailGroupsItems.Add(emailGroupsItemsItem);
				}
			}

			if (prefetches.Contains("Sites") && this.Sites.Count > 0)
			{
				var iter = this.Sites.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var sitesItem = x.ObjectsDataSet.GetObject(new SiteDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (sitesItem == null)
						sitesItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SiteDataObject;

					x.Sites.Add(sitesItem);
				}
			}

			if (prefetches.Contains("Dealer") && this.Dealer != null)
			{
				var dealer = x.ObjectsDataSet.GetObject(new DealerDataObject((System.Guid)this.Dealer.Id) { IsNew = false });

				if (dealer == null)
					dealer = this.Dealer.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DealerDataObject;

				x.SetDealerValue(dealer);
			}

			if (prefetches.Contains("DashboardFilterItems") && this.DashboardFilterItems.Count > 0)
			{
				var iter = this.DashboardFilterItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var dashboardFilterItemsItem = x.ObjectsDataSet.GetObject(new DashboardFilterDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (dashboardFilterItemsItem == null)
						dashboardFilterItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DashboardFilterDataObject;

					x.DashboardFilterItems.Add(dashboardFilterItemsItem);
				}
			}

			if (prefetches.Contains("CustomerPreOperationalChecklistTemplateItems") && this.CustomerPreOperationalChecklistTemplateItems.Count > 0)
			{
				var iter = this.CustomerPreOperationalChecklistTemplateItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var customerPreOperationalChecklistTemplateItemsItem = x.ObjectsDataSet.GetObject(new CustomerPreOperationalChecklistTemplateDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (customerPreOperationalChecklistTemplateItemsItem == null)
						customerPreOperationalChecklistTemplateItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerPreOperationalChecklistTemplateDataObject;

					x.CustomerPreOperationalChecklistTemplateItems.Add(customerPreOperationalChecklistTemplateItemsItem);
				}
			}

			if (prefetches.Contains("CustomerToModelItems") && this.CustomerToModelItems.Count > 0)
			{
				var iter = this.CustomerToModelItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var customerToModelItemsItem = x.ObjectsDataSet.GetObject(new CustomerToModelDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (customerToModelItemsItem == null)
						customerToModelItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerToModelDataObject;

					x.CustomerToModelItems.Add(customerToModelItemsItem);
				}
			}

			if (prefetches.Contains("CustomerFeatureSubscription") && this.CustomerFeatureSubscription != null)
			{
				var customerFeatureSubscription = x.ObjectsDataSet.GetObject(new CustomerFeatureSubscriptionDataObject((System.Guid)this.CustomerFeatureSubscription.Id) { IsNew = false });

				if (customerFeatureSubscription == null)
					customerFeatureSubscription = this.CustomerFeatureSubscription.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerFeatureSubscriptionDataObject;

				x.SetCustomerFeatureSubscriptionValue(customerFeatureSubscription);
			}

			if (prefetches.Contains("CustomerSSODetailItems") && this.CustomerSSODetailItems.Count > 0)
			{
				var iter = this.CustomerSSODetailItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var customerSSODetailItemsItem = x.ObjectsDataSet.GetObject(new CustomerSSODetailDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (customerSSODetailItemsItem == null)
						customerSSODetailItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerSSODetailDataObject;

					x.CustomerSSODetailItems.Add(customerSSODetailItemsItem);
				}
			}

			if (prefetches.Contains("SlamcoreDeviceItems") && this.SlamcoreDeviceItems.Count > 0)
			{
				var iter = this.SlamcoreDeviceItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var slamcoreDeviceItemsItem = x.ObjectsDataSet.GetObject(new SlamcoreDeviceDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (slamcoreDeviceItemsItem == null)
						slamcoreDeviceItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SlamcoreDeviceDataObject;

					x.SlamcoreDeviceItems.Add(slamcoreDeviceItemsItem);
				}
			}

			if (prefetches.Contains("CustomerToPersonViewItems") && this.CustomerToPersonViewItems.Count > 0)
			{
				var iter = this.CustomerToPersonViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var customerToPersonViewItemsItem = x.ObjectsDataSet.GetObject(new CustomerToPersonViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (customerToPersonViewItemsItem == null)
						customerToPersonViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerToPersonViewDataObject;

					x.CustomerToPersonViewItems.Add(customerToPersonViewItemsItem);
				}
			}

			if (prefetches.Contains("AccessGroupItems") && this.AccessGroupItems.Count > 0)
			{
				var iter = this.AccessGroupItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var accessGroupItemsItem = x.ObjectsDataSet.GetObject(new AccessGroupDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (accessGroupItemsItem == null)
						accessGroupItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as AccessGroupDataObject;

					x.AccessGroupItems.Add(accessGroupItemsItem);
				}
			}

			if (prefetches.Contains("CustomerModelItems") && this.CustomerModelItems.Count > 0)
			{
				var iter = this.CustomerModelItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var customerModelItemsItem = x.ObjectsDataSet.GetObject(new CustomerModelDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (customerModelItemsItem == null)
						customerModelItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerModelDataObject;

					x.CustomerModelItems.Add(customerModelItemsItem);
				}
			}

			if (prefetches.Contains("CustomerSnapshotItems") && this.CustomerSnapshotItems.Count > 0)
			{
				var iter = this.CustomerSnapshotItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var customerSnapshotItemsItem = x.ObjectsDataSet.GetObject(new CustomerSnapshotDataObject((System.Int32)iter.Current.SnapshotId) { IsNew = false });

					if (customerSnapshotItemsItem == null)
						customerSnapshotItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CustomerSnapshotDataObject;

					x.CustomerSnapshotItems.Add(customerSnapshotItemsItem);
				}
			}

			if (prefetches.Contains("DepartmentItems") && this.DepartmentItems.Count > 0)
			{
				var iter = this.DepartmentItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var departmentItemsItem = x.ObjectsDataSet.GetObject(new DepartmentDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (departmentItemsItem == null)
						departmentItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DepartmentDataObject;

					x.DepartmentItems.Add(departmentItemsItem);
				}
			}

			if (prefetches.Contains("GoUserToCustomerItems") && this.GoUserToCustomerItems.Count > 0)
			{
				var iter = this.GoUserToCustomerItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var goUserToCustomerItemsItem = x.ObjectsDataSet.GetObject(new GoUserToCustomerDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (goUserToCustomerItemsItem == null)
						goUserToCustomerItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as GoUserToCustomerDataObject;

					x.GoUserToCustomerItems.Add(goUserToCustomerItemsItem);
				}
			}

			if (prefetches.Contains("CurrentVehicleStatusChartViewItems") && this.CurrentVehicleStatusChartViewItems.Count > 0)
			{
				var iter = this.CurrentVehicleStatusChartViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var currentVehicleStatusChartViewItemsItem = x.ObjectsDataSet.GetObject(new CurrentVehicleStatusChartViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (currentVehicleStatusChartViewItemsItem == null)
						currentVehicleStatusChartViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CurrentVehicleStatusChartViewDataObject;

					x.CurrentVehicleStatusChartViewItems.Add(currentVehicleStatusChartViewItemsItem);
				}
			}

			if (prefetches.Contains("VehicleItems") && this.VehicleItems.Count > 0)
			{
				var iter = this.VehicleItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var vehicleItemsItem = x.ObjectsDataSet.GetObject(new VehicleDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (vehicleItemsItem == null)
						vehicleItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as VehicleDataObject;

					x.VehicleItems.Add(vehicleItemsItem);
				}
			}

			if (prefetches.Contains("CurrentDriverStatusChartViewItems") && this.CurrentDriverStatusChartViewItems.Count > 0)
			{
				var iter = this.CurrentDriverStatusChartViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var currentDriverStatusChartViewItemsItem = x.ObjectsDataSet.GetObject(new CurrentDriverStatusChartViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (currentDriverStatusChartViewItemsItem == null)
						currentDriverStatusChartViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as CurrentDriverStatusChartViewDataObject;

					x.CurrentDriverStatusChartViewItems.Add(currentDriverStatusChartViewItemsItem);
				}
			}

			if (prefetches.Contains("DashboardVehicleCardViewItems") && this.DashboardVehicleCardViewItems.Count > 0)
			{
				var iter = this.DashboardVehicleCardViewItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var dashboardVehicleCardViewItemsItem = x.ObjectsDataSet.GetObject(new DashboardVehicleCardViewDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (dashboardVehicleCardViewItemsItem == null)
						dashboardVehicleCardViewItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as DashboardVehicleCardViewDataObject;

					x.DashboardVehicleCardViewItems.Add(dashboardVehicleCardViewItemsItem);
				}
			}

			if (prefetches.Contains("PersonItems") && this.PersonItems.Count > 0)
			{
				var iter = this.PersonItems.GetEnumerator();
				
				while (iter.MoveNext())
				{
					var personItemsItem = x.ObjectsDataSet.GetObject(new PersonDataObject((System.Guid)iter.Current.Id) { IsNew = false });

					if (personItemsItem == null)
						personItemsItem = iter.Current.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as PersonDataObject;

					x.PersonItems.Add(personItemsItem);
				}
			}

		}
		
		// For database filtering, nothing IsNew or IsDirty, but these stubs needed for ORM security predicates to compile
		public virtual bool IsNew => false;
		public virtual bool IsDirty => false;

	}
}