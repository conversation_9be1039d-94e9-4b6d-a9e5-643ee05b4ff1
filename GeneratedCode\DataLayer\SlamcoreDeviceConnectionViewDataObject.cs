﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'SlamcoreDeviceConnectionView'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class SlamcoreDeviceConnectionViewDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("Offline")]
		protected Nullable<System.Int16> _offline;
		[JsonProperty ("OnlineDevices")]
		protected Nullable<System.Int16> _onlineDevices;
		[JsonProperty ("SlamcoreDeviceId")]
		protected Nullable<System.Guid> _slamcoreDeviceId;
		[JsonProperty ("TotalDevices")]
		protected Nullable<System.Int16> _totalDevices;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)
		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _slamcoreDevice_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_slamcoreDevice_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public SlamcoreDeviceConnectionViewDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public SlamcoreDeviceConnectionViewDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public SlamcoreDeviceConnectionViewDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public SlamcoreDeviceConnectionViewDataObject Initialize(SlamcoreDeviceConnectionViewDataObject template, bool deepCopy)
		{
			this.SetIdValue(template.Id, false, false);
			this.SetOfflineValue(template.Offline, false, false);
			this.SetOnlineDevicesValue(template.OnlineDevices, false, false);
			this.SetSlamcoreDeviceIdValue(template.SlamcoreDeviceId, false, false);
			this.SetTotalDevicesValue(template.TotalDevices, false, false);
 
			this._slamcoreDevice_NewObjectId = template._slamcoreDevice_NewObjectId;
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual SlamcoreDeviceConnectionViewDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual SlamcoreDeviceConnectionViewDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<SlamcoreDeviceConnectionViewDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var slamcoreDeviceConnectionViewSource = sourceObject as SlamcoreDeviceConnectionViewDataObject;

			if (ReferenceEquals(null, slamcoreDeviceConnectionViewSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetIdValue(slamcoreDeviceConnectionViewSource.Id, false, false);
			this.SetOfflineValue(slamcoreDeviceConnectionViewSource.Offline, false, false);
			this.SetOnlineDevicesValue(slamcoreDeviceConnectionViewSource.OnlineDevices, false, false);
			this.SetSlamcoreDeviceIdValue(slamcoreDeviceConnectionViewSource.SlamcoreDeviceId, false, false);
			this.SetTotalDevicesValue(slamcoreDeviceConnectionViewSource.TotalDevices, false, false);
			this._slamcoreDevice_NewObjectId = (sourceObject as SlamcoreDeviceConnectionViewDataObject)._slamcoreDevice_NewObjectId;

			if (deepCopy)
			{
				this.ObjectsDataSet = slamcoreDeviceConnectionViewSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(slamcoreDeviceConnectionViewSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as SlamcoreDeviceConnectionViewDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {
			if (this._slamcoreDevice_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._slamcoreDevice_NewObjectId))
				{
                    this._slamcoreDevice_NewObjectId = null;
				}
                else
				{
					this._slamcoreDevice_NewObjectId = datasetMergingInternalIdMapping[(int) this._slamcoreDevice_NewObjectId];
				}
			}

		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<SlamcoreDeviceDataObject> _slamcoreDeviceService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceDataObject>>();
      public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet)
		{
			SetSlamcoreDeviceValue(valueToSet, true, true);
		}

        public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			SlamcoreDeviceDataObject existing_slamcoreDevice = null ;

			if ( !(this.SlamcoreDeviceId == null || ObjectsDataSet == null))
			{
				SlamcoreDeviceDataObject key;

				if (this._slamcoreDevice_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize((System.Guid)this.SlamcoreDeviceId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._slamcoreDevice_NewObjectId;			
				}

				existing_slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_slamcoreDevice ,valueToSet))
            {
                if (valueToSet == null)
                {
					_slamcoreDevice_NewObjectId = null;
					_slamcoreDeviceId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("SlamcoreDevice", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "SlamcoreDeviceConnectionViewDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_slamcoreDevice_NewObjectId != valueToSet.InternalObjectId)
					{
						_slamcoreDevice_NewObjectId = valueToSet.InternalObjectId;
						_slamcoreDeviceId = valueToSet.Id;
						OnPropertyChanged("SlamcoreDeviceId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_slamcoreDeviceId != valueToSet.Id)
					{
						_slamcoreDevice_NewObjectId = null;

						_slamcoreDeviceId = valueToSet.Id;
						OnPropertyChanged("SlamcoreDeviceId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_slamcoreDevice_NewObjectId = null;
					_slamcoreDeviceId = null;
					
				OnPropertyChanged("SlamcoreDeviceId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_slamcoreDevice ,valueToSet))
				OnPropertyChanged("SlamcoreDevice", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __slamcoreDeviceSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreDeviceAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreDevice", which is a SlamcoreDeviceDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SlamcoreDeviceDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreDeviceAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreDeviceSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreDeviceAlreadyLazyLoaded || forceReload)
                {
								
					if (this.SlamcoreDeviceId == null)
					{
						return null;
					}
				
					SlamcoreDeviceDataObject slamcoreDevice = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __slamcoreDeviceAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize((System.Guid)this.SlamcoreDeviceId);
						slamcoreDevice.IsNew = false;
						slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
						if (slamcoreDevice != null)
						{
							return slamcoreDevice;
						}
					}

					slamcoreDevice = await _slamcoreDeviceService.GetAsync(_serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize((System.Guid)this.SlamcoreDeviceId), parameters : parameters, skipSecurity: skipSecurity);

					SetSlamcoreDeviceValue(slamcoreDevice, false, false);
					__slamcoreDeviceAlreadyLazyLoaded = true;				
		
					slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize((System.Guid)this.SlamcoreDeviceId);
					slamcoreDevice.IsNew = false;
					slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
                    __slamcoreDeviceAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreDeviceAsync(false);
            }
            finally
            {
                __slamcoreDeviceSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SlamcoreDeviceDataObject SlamcoreDevice 
		{
			get
			{			
				return GetSlamcoreDeviceAsync(true).Result;
			}
			set
			{
				SetSlamcoreDeviceValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreDevice()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreDeviceConnectionViewDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreDeviceConnectionViewDataObject"].Contains("SlamcoreDevice");
		}

		public virtual async Task<SlamcoreDeviceDataObject> GetSlamcoreDeviceAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SlamcoreDeviceDataObject slamcoreDevice;

				
			if (_slamcoreDevice_NewObjectId != null)
			{
				slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>();
				slamcoreDevice.IsNew = true;
				slamcoreDevice.InternalObjectId = _slamcoreDevice_NewObjectId;
				slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
			}
			else
			{
				if (this.SlamcoreDeviceId == null)
					return null;
				if (SlamcoreDeviceId == null)
					slamcoreDevice = null;
				else
				slamcoreDevice = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize((System.Guid)this.SlamcoreDeviceId);
				slamcoreDevice.IsNew = false;
				slamcoreDevice = (SlamcoreDeviceDataObject)ObjectsDataSet.GetObject(slamcoreDevice);
				
				if (allowLazyLoading && slamcoreDevice == null && LazyLoadingEnabled && (!__slamcoreDeviceAlreadyLazyLoaded || forceReload))
				{
					slamcoreDevice = await LoadSlamcoreDeviceAsync(forceReload : forceReload);
				}
			}
				
			return slamcoreDevice;
		}

		public virtual Nullable<System.Guid> SlamcoreDeviceForeignKey
		{
			get { return SlamcoreDeviceId; }
			set 
			{	
				SlamcoreDeviceId = value;
			}
			
		}
		

		public override void ClearLazyLoadFlags()
		{
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadSlamcoreDeviceAsync()) != null)
				result.Add(SlamcoreDevice);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				SlamcoreDevice == other ||
				(other is SlamcoreDeviceDataObject && (SlamcoreDeviceId != default(Nullable<System.Guid>)) && (SlamcoreDeviceId == (other as SlamcoreDeviceDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the SlamcoreDeviceConnectionView DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetOfflineValue(Nullable<System.Int16> valueToSet)
		{
			SetOfflineValue(valueToSet, true, true);
		}

		public virtual void SetOfflineValue(Nullable<System.Int16> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_offline != valueToSet)
			{
				_offline = valueToSet;

				OnPropertyChanged("Offline", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Offline Devices property of the SlamcoreDeviceConnectionView DataObject</summary>
        public virtual Nullable<System.Int16> Offline 
		{
			get	{ return _offline;}
			
			
			set
			{
				SetOfflineValue(value);
			}
		}		
			
		
		/// <summary> The Offline Devices With Label property of the SlamcoreDeviceConnectionView DataObject</summary>
        public virtual System.String OfflineDevicesWithLabel 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return (((Offline == null) || (Offline != null && (Offline.Value == 0))) ? "<div class=\"slamcore-total-devices-container\"><p class=\"slamcore-no-offline-devices\">No Offline Devices</p></div>" : ("<div class=\"slamcore-total-devices-container\"><p class=\"slamcore-offline-devices\"><span class=\"slamcore-offline-devices-span\">" + (Offline != null ? Offline.Value.ToString() : "") + "</span> Offline Devices</p></div>"));				
			}
			
		}		
			
			
		public virtual void SetOnlineDevicesValue(Nullable<System.Int16> valueToSet)
		{
			SetOnlineDevicesValue(valueToSet, true, true);
		}

		public virtual void SetOnlineDevicesValue(Nullable<System.Int16> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_onlineDevices != valueToSet)
			{
				_onlineDevices = valueToSet;

				OnPropertyChanged("OnlineDevices", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Online Devices property of the SlamcoreDeviceConnectionView DataObject</summary>
        public virtual Nullable<System.Int16> OnlineDevices 
		{
			get	{ return _onlineDevices;}
			
			
			set
			{
				SetOnlineDevicesValue(value);
			}
		}		
			
		
		/// <summary> The Online Devices With Label property of the SlamcoreDeviceConnectionView DataObject</summary>
        public virtual System.String OnlineDevicesWithLabel 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return (((OnlineDevices == null) || (OnlineDevices != null && (OnlineDevices.Value == 0))) ? "<div class=\"slamcore-total-devices-container\"><p class=\"slamcore-no-online-devices\">No Online Devices</p></div>" : ("<div class=\"slamcore-total-devices-container\"><p class=\"slamcore-online-devices\"><span class=\"slamcore-online-devices-span\">" + (OnlineDevices != null ? OnlineDevices.Value.ToString() : "") + "</span> Online Devices</p></div>"));				
			}
			
		}		
			
			
		public virtual void SetSlamcoreDeviceIdValue(Nullable<System.Guid> valueToSet)
		{
			SetSlamcoreDeviceIdValue(valueToSet, true, true);
		}

		public virtual void SetSlamcoreDeviceIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_slamcoreDeviceId != valueToSet)
			{
				_slamcoreDeviceId = valueToSet;

				// SlamcoreDeviceId is a FK. Setting its value should result in a event
				OnPropertyChanged("SlamcoreDevice", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("SlamcoreDeviceId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SlamcoreDeviceId property of the SlamcoreDeviceConnectionView DataObject</summary>
        public virtual Nullable<System.Guid> SlamcoreDeviceId 
		{
			get	{ return _slamcoreDeviceId;}
			
			
			set
			{
				SetSlamcoreDeviceIdValue(value);
			}
		}		
			
			
		public virtual void SetTotalDevicesValue(Nullable<System.Int16> valueToSet)
		{
			SetTotalDevicesValue(valueToSet, true, true);
		}

		public virtual void SetTotalDevicesValue(Nullable<System.Int16> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_totalDevices != valueToSet)
			{
				_totalDevices = valueToSet;

				OnPropertyChanged("TotalDevices", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Total Devices property of the SlamcoreDeviceConnectionView DataObject</summary>
        public virtual Nullable<System.Int16> TotalDevices 
		{
			get	{ return _totalDevices;}
			
			
			set
			{
				SetTotalDevicesValue(value);
			}
		}		
			
		
		/// <summary> The Total Devices with Label property of the SlamcoreDeviceConnectionView DataObject</summary>
        public virtual System.String TotalDevicesWithLabel 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return (((TotalDevices == null) || (TotalDevices != null && (TotalDevices.Value == 0))) ? "<div class=\"slamcore-total-devices-container\"><p class=\"slamcore-total-devices-span\">No Devices</p></div>" : ("<div class=\"slamcore-total-devices-container\"><p class=\"slamcore-total-devices-span\"><span>" + (TotalDevices != null ? TotalDevices.Value.ToString() : "") + "</span> Total Devices</p></div>"));				
			}
			
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			if (propertyName == "Offline")
			{
				OnPropertyChanged("OfflineDevicesWithLabel", true, dirtyHandlerOn);
			}

			if (propertyName == "OnlineDevices")
			{
				OnPropertyChanged("OnlineDevicesWithLabel", true, dirtyHandlerOn);
			}

			if (propertyName == "TotalDevices")
			{
				OnPropertyChanged("TotalDevicesWithLabel", true, dirtyHandlerOn);
			}

			
			// Push the notification to related objects
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<SlamcoreDeviceConnectionViewDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is SlamcoreDeviceConnectionViewDataObject))
				return false;

			var p = (SlamcoreDeviceConnectionViewDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.Offline == p.Offline;
			fieldsComparison &= this.OnlineDevices == p.OnlineDevices;
			fieldsComparison &= this.SlamcoreDeviceId == p.SlamcoreDeviceId;
			fieldsComparison &= this.TotalDevices == p.TotalDevices;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreDeviceConnectionViewCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public SlamcoreDeviceConnectionViewCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public SlamcoreDeviceConnectionViewCollectionContainer()
		{
		}
		
		public SlamcoreDeviceConnectionViewCollectionContainer Construct(DataObjectCollection<SlamcoreDeviceConnectionViewDataObject> slamcoreDeviceConnectionViewItems)
        {
            if (slamcoreDeviceConnectionViewItems == null)
                return this;
				
			this.PrimaryKeys = slamcoreDeviceConnectionViewItems.Select(c => c.PrimaryKey).ToList();
            if (slamcoreDeviceConnectionViewItems.ObjectsDataSet == null)
            {
                slamcoreDeviceConnectionViewItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = slamcoreDeviceConnectionViewItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = slamcoreDeviceConnectionViewItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<SlamcoreDeviceConnectionViewDataObject> ExtractSlamcoreDeviceConnectionViewItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<SlamcoreDeviceConnectionViewDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<SlamcoreDeviceConnectionViewDataObject>(typeof(SlamcoreDeviceConnectionViewDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreDeviceConnectionViewContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public SlamcoreDeviceConnectionViewContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual SlamcoreDeviceConnectionViewContainer Construct(SlamcoreDeviceConnectionViewDataObject slamcoreDeviceConnectionView, bool includeDirtyObjectsOnly = false)
		{
            if (slamcoreDeviceConnectionView == null)
                return this;

			this.PrimaryKey = slamcoreDeviceConnectionView.PrimaryKey;
			
            if (slamcoreDeviceConnectionView.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(slamcoreDeviceConnectionView);
            }

			if(slamcoreDeviceConnectionView.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity SlamcoreDeviceConnectionView", "Unable to set a dataset to the entity. Container may not be initialized", "SlamcoreDeviceConnectionViewDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : Slamcore Device Connection View");
			}

			if(slamcoreDeviceConnectionView.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in SlamcoreDeviceConnectionViewDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "SlamcoreDeviceConnectionViewDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in Slamcore Device Connection ViewDataObject");
			}
			this.InternalObjectId = (int) slamcoreDeviceConnectionView.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? slamcoreDeviceConnectionView.ObjectsDataSet.CloneDirtyObjects() : slamcoreDeviceConnectionView.ObjectsDataSet;

			return this;
		}
		
		public SlamcoreDeviceConnectionViewDataObject ExtractSlamcoreDeviceConnectionView()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<SlamcoreDeviceConnectionViewDataObject>(typeof(SlamcoreDeviceConnectionViewDataObject), InternalObjectId);
			
			return result;
        }	
	}

}