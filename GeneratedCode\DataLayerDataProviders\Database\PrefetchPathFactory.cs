﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
/*using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using System.IO;
using System.Xml;
using System.Reflection;
using FleetXQ.Data;
using FleetXQ.Data.Linq;

namespace FleetXQ.Data.DataProviders.Database
{    
	public static class PrefetchPathFactory
    {

		// Lock Used during the initialization of PathNodes.
		private static Object _lockObject = new Object();

        private struct PathNode
        {
            public string ParentEntityName;
            public string EntityName;
            public string PathName;

            public List<PathNode> PathNodes { get; set; }
        }

        private static Dictionary<string, Dictionary<string, PathNode>> _pathNodes;

        private static Dictionary<string, Dictionary<string, PathNode>> PathNodes
        {
            get
            {
				lock(_lockObject)
				{
					if (_pathNodes == null)
					{

                    _pathNodes = new Dictionary<string,Dictionary<string,PathNode>>();
					_pathNodes.Add ( "checklistdetail", new Dictionary<string,PathNode>());

					_pathNodes["checklistdetail"].Add( "preoperationalchecklist", new PathNode { EntityName = "PreOperationalChecklist", PathName = "PreOperationalChecklist" });
					_pathNodes["checklistdetail"].Add( "checklistresults", new PathNode { EntityName = "ChecklistResult", PathName = "ChecklistResults" });
					_pathNodes.Add ( "slamcoreawareauthenticationdetails", new Dictionary<string,PathNode>());

					_pathNodes["slamcoreawareauthenticationdetails"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDevice", PathName = "SlamcoreDevice" });
					_pathNodes.Add ( "persontomodelvehiclemasteraccessview", new Dictionary<string,PathNode>());

					_pathNodes["persontomodelvehiclemasteraccessview"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["persontomodelvehiclemasteraccessview"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes.Add ( "vehiclelockout", new Dictionary<string,PathNode>());

					_pathNodes["vehiclelockout"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes["vehiclelockout"].Add( "allvehicleunlocksviewitems", new PathNode { EntityName = "AllVehicleUnlocksView", PathName = "AllVehicleUnlocksViewItems" });
					_pathNodes["vehiclelockout"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes["vehiclelockout"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes["vehiclelockout"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "revision", new Dictionary<string,PathNode>());

					_pathNodes["revision"].Add( "snapshotitems", new PathNode { EntityName = "Snapshot", PathName = "SnapshotItems" });
					_pathNodes["revision"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes["revision"].Add( "tagitems", new PathNode { EntityName = "Tag", PathName = "TagItems" });
					_pathNodes.Add ( "alert", new Dictionary<string,PathNode>());

					_pathNodes["alert"].Add( "alertsubscriptions", new PathNode { EntityName = "AlertSubscription", PathName = "AlertSubscriptions" });
					_pathNodes.Add ( "websiteuser", new Dictionary<string,PathNode>());

					_pathNodes["websiteuser"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["websiteuser"].Add( "websiteroles", new PathNode { EntityName = "WebsiteRole", PathName = "WebsiteRoles" });
					_pathNodes.Add ( "driveraccessabusefilter", new Dictionary<string,PathNode>());

					_pathNodes["driveraccessabusefilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["driveraccessabusefilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["driveraccessabusefilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "alllicenseexpiryview", new Dictionary<string,PathNode>());

					_pathNodes["alllicenseexpiryview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["alllicenseexpiryview"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes.Add ( "driverlicenseexpiryview", new Dictionary<string,PathNode>());

					_pathNodes["driverlicenseexpiryview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["driverlicenseexpiryview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["driverlicenseexpiryview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["driverlicenseexpiryview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "contactpersoninformation", new Dictionary<string,PathNode>());

					_pathNodes["contactpersoninformation"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "checklistsettings", new Dictionary<string,PathNode>());

					_pathNodes["checklistsettings"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "vehiclediagnostic", new Dictionary<string,PathNode>());

					_pathNodes["vehiclediagnostic"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "gouser2fa", new Dictionary<string,PathNode>());

					_pathNodes["gouser2fa"].Add( "gorole", new PathNode { EntityName = "GORole", PathName = "GORole" });
					_pathNodes["gouser2fa"].Add( "gousertocustomeritems", new PathNode { EntityName = "GoUserToCustomer", PathName = "GoUserToCustomerItems" });
					_pathNodes["gouser2fa"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["gouser2fa"].Add( "messagehistoryitems", new PathNode { EntityName = "MessageHistory", PathName = "MessageHistoryItems" });
					_pathNodes["gouser2fa"].Add( "usergroupitems", new PathNode { EntityName = "GOUserGroup", PathName = "UserGroupItems" });
					_pathNodes["gouser2fa"].Add( "customeraudititemscreated", new PathNode { EntityName = "CustomerAudit", PathName = "CustomerAuditItemsCreated" });
					_pathNodes["gouser2fa"].Add( "reportsubscriptionitems", new PathNode { EntityName = "ReportSubscription", PathName = "ReportSubscriptionItems" });
					_pathNodes["gouser2fa"].Add( "userroleitems", new PathNode { EntityName = "GOUserRole", PathName = "UserRoleItems" });
					_pathNodes["gouser2fa"].Add( "customeraudititemsdeleted", new PathNode { EntityName = "CustomerAudit", PathName = "CustomerAuditItemsDeleted" });
					_pathNodes["gouser2fa"].Add( "tag", new PathNode { EntityName = "Tag", PathName = "Tag" });
					_pathNodes["gouser2fa"].Add( "exportjobstatusitems", new PathNode { EntityName = "ExportJobStatus", PathName = "ExportJobStatusItems" });
					_pathNodes["gouser2fa"].Add( "revisionitems", new PathNode { EntityName = "Revision", PathName = "RevisionItems" });
					_pathNodes["gouser2fa"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["gouser2fa"].Add( "gouserdepartmentitems", new PathNode { EntityName = "GOUserDepartment", PathName = "GOUserDepartmentItems" });
					_pathNodes["gouser2fa"].Add( "customeraudititemsmodified", new PathNode { EntityName = "CustomerAudit", PathName = "CustomerAuditItemsModified" });
					_pathNodes["gouser2fa"].Add( "dealerdriver", new PathNode { EntityName = "DealerDriver", PathName = "DealerDriver" });
					_pathNodes["gouser2fa"].Add( "vehiclelockoutitems", new PathNode { EntityName = "VehicleLockout", PathName = "VehicleLockoutItems" });
					_pathNodes["gouser2fa"].Add( "alertsubscriptionitems", new PathNode { EntityName = "AlertSubscription", PathName = "AlertSubscriptionItems" });
					_pathNodes.Add ( "vehiclegps", new Dictionary<string,PathNode>());

					_pathNodes["vehiclegps"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes["vehiclegps"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "module", new Dictionary<string,PathNode>());

					_pathNodes["module"].Add( "modulehistoryitems", new PathNode { EntityName = "ModuleHistory", PathName = "ModuleHistoryItems" });
					_pathNodes["module"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["module"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "persontopervehiclemasteraccessview", new Dictionary<string,PathNode>());

					_pathNodes["persontopervehiclemasteraccessview"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["persontopervehiclemasteraccessview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "accessgroup", new Dictionary<string,PathNode>());

					_pathNodes["accessgroup"].Add( "accessgroupstosites", new PathNode { EntityName = "AccessGroupToSite", PathName = "AccessGroupsToSites" });
					_pathNodes["accessgroup"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["accessgroup"].Add( "personitems", new PathNode { EntityName = "Person", PathName = "PersonItems" });
					_pathNodes.Add ( "checkliststatusview", new Dictionary<string,PathNode>());

					_pathNodes["checkliststatusview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["checkliststatusview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["checkliststatusview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["checkliststatusview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "currentstatuscombinedview", new Dictionary<string,PathNode>());

					_pathNodes["currentstatuscombinedview"].Add( "currentstatusdriverviewitems", new PathNode { EntityName = "CurrentStatusDriverView", PathName = "CurrentStatusDriverViewItems" });
					_pathNodes["currentstatuscombinedview"].Add( "currentstatusvehicleviewitems", new PathNode { EntityName = "CurrentStatusVehicleView", PathName = "CurrentStatusVehicleViewItems" });
					_pathNodes.Add ( "currentvehiclestatuschartview", new Dictionary<string,PathNode>());

					_pathNodes["currentvehiclestatuschartview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["currentvehiclestatuschartview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["currentvehiclestatuschartview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["currentvehiclestatuschartview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes.Add ( "dealerfeaturesubscription", new Dictionary<string,PathNode>());

					_pathNodes["dealerfeaturesubscription"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "messagehistory", new Dictionary<string,PathNode>());

					_pathNodes["messagehistory"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["messagehistory"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes["messagehistory"].Add( "allmessagehistorystoreprocedureitems", new PathNode { EntityName = "AllMessageHistoryStoreProcedure", PathName = "AllMessageHistoryStoreProcedureItems" });
					_pathNodes.Add ( "accessgrouptosite", new Dictionary<string,PathNode>());

					_pathNodes["accessgrouptosite"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["accessgrouptosite"].Add( "accessgroup", new PathNode { EntityName = "AccessGroup", PathName = "AccessGroup" });
					_pathNodes.Add ( "alldriveraccessabusestoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["alldriveraccessabusestoreprocedure"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["alldriveraccessabusestoreprocedure"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes.Add ( "customertomodel", new Dictionary<string,PathNode>());

					_pathNodes["customertomodel"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes["customertomodel"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "allemailsubscriptionstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["allemailsubscriptionstoreprocedure"].Add( "reportsubscription", new PathNode { EntityName = "ReportSubscription", PathName = "ReportSubscription" });
					_pathNodes.Add ( "dashboardvehiclecardstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["dashboardvehiclecardstoreprocedure"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["dashboardvehiclecardstoreprocedure"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["dashboardvehiclecardstoreprocedure"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["dashboardvehiclecardstoreprocedure"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "region", new Dictionary<string,PathNode>());

					_pathNodes["region"].Add( "dealers", new PathNode { EntityName = "Dealer", PathName = "Dealers" });
					_pathNodes.Add ( "model", new Dictionary<string,PathNode>());

					_pathNodes["model"].Add( "licensesbymodel", new PathNode { EntityName = "LicenseByModel", PathName = "LicensesByModel" });
					_pathNodes["model"].Add( "modelvehiclenormalcardaccessitems", new PathNode { EntityName = "ModelVehicleNormalCardAccess", PathName = "ModelVehicleNormalCardAccessItems" });
					_pathNodes["model"].Add( "sitechecklists", new PathNode { EntityName = "DepartmentChecklist", PathName = "SiteChecklists" });
					_pathNodes["model"].Add( "vehicles", new PathNode { EntityName = "Vehicle", PathName = "Vehicles" });
					_pathNodes["model"].Add( "vehiclespermodelreportitems", new PathNode { EntityName = "VehiclesPerModelReport", PathName = "VehiclesPerModelReportItems" });
					_pathNodes["model"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["model"].Add( "modelvehiclemastercardaccessitems", new PathNode { EntityName = "ModelVehicleMasterCardAccess", PathName = "ModelVehicleMasterCardAccessItems" });
					_pathNodes["model"].Add( "customertomodelitems", new PathNode { EntityName = "CustomerToModel", PathName = "CustomerToModelItems" });
					_pathNodes["model"].Add( "persontomodelvehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToModelVehicleMasterAccessView", PathName = "PersonToModelVehicleMasterAccessViewItems" });
					_pathNodes["model"].Add( "persontomodelvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToModelVehicleNormalAccessView", PathName = "PersonToModelVehicleNormalAccessViewItems" });
					_pathNodes["model"].Add( "customermodelitems", new PathNode { EntityName = "CustomerModel", PathName = "CustomerModelItems" });
					_pathNodes.Add ( "allvorsessionspervehiclestoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["allvorsessionspervehiclestoreprocedure"].Add( "vorreportcombinedview", new PathNode { EntityName = "VORReportCombinedView", PathName = "VORReportCombinedView" });
					_pathNodes["allvorsessionspervehiclestoreprocedure"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "slamcoredeviceconnectionview", new Dictionary<string,PathNode>());

					_pathNodes["slamcoredeviceconnectionview"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDevice", PathName = "SlamcoreDevice" });
					_pathNodes.Add ( "allimpactsview", new Dictionary<string,PathNode>());

					_pathNodes["allimpactsview"].Add( "impact", new PathNode { EntityName = "Impact", PathName = "Impact" });
					_pathNodes["allimpactsview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "customer", new Dictionary<string,PathNode>());

					_pathNodes["customer"].Add( "dashboardvehiclecardstoreprocedureitems", new PathNode { EntityName = "DashboardVehicleCardStoreProcedure", PathName = "DashboardVehicleCardStoreProcedureItems" });
					_pathNodes["customer"].Add( "driverlicenseexpiryviewitems", new PathNode { EntityName = "DriverLicenseExpiryView", PathName = "DriverLicenseExpiryViewItems" });
					_pathNodes["customer"].Add( "todayspreopcheckstoreprocedureitems", new PathNode { EntityName = "TodaysPreopCheckStoreProcedure", PathName = "TodaysPreopCheckStoreProcedureItems" });
					_pathNodes["customer"].Add( "impactfrequencyperweekdayviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekDayView", PathName = "ImpactFrequencyPerWeekDayViewItems" });
					_pathNodes["customer"].Add( "currentdriverstatuschartviewitems", new PathNode { EntityName = "CurrentDriverStatusChartView", PathName = "CurrentDriverStatusChartViewItems" });
					_pathNodes["customer"].Add( "slamcoredeviceitems", new PathNode { EntityName = "SlamcoreDevice", PathName = "SlamcoreDeviceItems" });
					_pathNodes["customer"].Add( "customersnapshotitems", new PathNode { EntityName = "CustomerSnapshot", PathName = "CustomerSnapshotItems" });
					_pathNodes["customer"].Add( "driveritems", new PathNode { EntityName = "Driver", PathName = "DriverItems" });
					_pathNodes["customer"].Add( "personitems", new PathNode { EntityName = "Person", PathName = "PersonItems" });
					_pathNodes["customer"].Add( "dashboardcardviewitems", new PathNode { EntityName = "DashboardDriverCardView", PathName = "DashboardCardViewItems" });
					_pathNodes["customer"].Add( "customertomodelitems", new PathNode { EntityName = "CustomerToModel", PathName = "CustomerToModelItems" });
					_pathNodes["customer"].Add( "customertopersonviewitems", new PathNode { EntityName = "CustomerToPersonView", PathName = "CustomerToPersonViewItems" });
					_pathNodes["customer"].Add( "customermodelitems", new PathNode { EntityName = "CustomerModel", PathName = "CustomerModelItems" });
					_pathNodes["customer"].Add( "vehicleutilizationlasttwelvehoursviewitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursView", PathName = "VehicleUtilizationLastTwelveHoursViewItems" });
					_pathNodes["customer"].Add( "accessgroupitems", new PathNode { EntityName = "AccessGroup", PathName = "AccessGroupItems" });
					_pathNodes["customer"].Add( "dashboarddrivercardstoreprocedureitems", new PathNode { EntityName = "DashboardDriverCardStoreProcedure", PathName = "DashboardDriverCardStoreProcedureItems" });
					_pathNodes["customer"].Add( "todayspreopcheckviewitems", new PathNode { EntityName = "TodaysPreopCheckView", PathName = "TodaysPreopCheckViewItems" });
					_pathNodes["customer"].Add( "customerfeaturesubscription", new PathNode { EntityName = "CustomerFeatureSubscription", PathName = "CustomerFeatureSubscription" });
					_pathNodes["customer"].Add( "todaysimpactstoreprocedureitems", new PathNode { EntityName = "TodaysImpactStoreProcedure", PathName = "TodaysImpactStoreProcedureItems" });
					_pathNodes["customer"].Add( "currentvehiclestatuschartviewitems", new PathNode { EntityName = "CurrentVehicleStatusChartView", PathName = "CurrentVehicleStatusChartViewItems" });
					_pathNodes["customer"].Add( "dashboardfilteritems", new PathNode { EntityName = "DashboardFilter", PathName = "DashboardFilterItems" });
					_pathNodes["customer"].Add( "todaysimpactviewitems", new PathNode { EntityName = "TodaysImpactView", PathName = "TodaysImpactViewItems" });
					_pathNodes["customer"].Add( "emailgroupsitems", new PathNode { EntityName = "EmailGroups", PathName = "EmailGroupsItems" });
					_pathNodes["customer"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["customer"].Add( "customerssodetailitems", new PathNode { EntityName = "CustomerSSODetail", PathName = "CustomerSSODetailItems" });
					_pathNodes["customer"].Add( "impactfrequencyperweekmonthviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekMonthView", PathName = "ImpactFrequencyPerWeekMonthViewItems" });
					_pathNodes["customer"].Add( "incompletedchecklistviewitems", new PathNode { EntityName = "IncompletedChecklistView", PathName = "IncompletedChecklistViewItems" });
					_pathNodes["customer"].Add( "country", new PathNode { EntityName = "Country", PathName = "Country" });
					_pathNodes["customer"].Add( "departmentitems", new PathNode { EntityName = "Department", PathName = "DepartmentItems" });
					_pathNodes["customer"].Add( "contactpersoninformation", new PathNode { EntityName = "ContactPersonInformation", PathName = "ContactPersonInformation" });
					_pathNodes["customer"].Add( "gousertocustomeritems", new PathNode { EntityName = "GoUserToCustomer", PathName = "GoUserToCustomerItems" });
					_pathNodes["customer"].Add( "vehicleitems", new PathNode { EntityName = "Vehicle", PathName = "VehicleItems" });
					_pathNodes["customer"].Add( "customeraudit", new PathNode { EntityName = "CustomerAudit", PathName = "CustomerAudit" });
					_pathNodes["customer"].Add( "customerpreoperationalchecklisttemplateitems", new PathNode { EntityName = "CustomerPreOperationalChecklistTemplate", PathName = "CustomerPreOperationalChecklistTemplateItems" });
					_pathNodes["customer"].Add( "dashboardvehiclecardviewitems", new PathNode { EntityName = "DashboardVehicleCardView", PathName = "DashboardVehicleCardViewItems" });
					_pathNodes["customer"].Add( "checkliststatusviewitems", new PathNode { EntityName = "ChecklistStatusView", PathName = "ChecklistStatusViewItems" });
					_pathNodes["customer"].Add( "loggedhoursversusseathoursviewitems", new PathNode { EntityName = "LoggedHoursVersusSeatHoursView", PathName = "LoggedHoursVersusSeatHoursViewItems" });
					_pathNodes["customer"].Add( "vehicleutilizationlasttwelvehoursstoreprocedureitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursStoreProcedure", PathName = "VehicleUtilizationLastTwelveHoursStoreProcedureItems" });
					_pathNodes["customer"].Add( "impactfrequencypertimeslotviewitems", new PathNode { EntityName = "ImpactFrequencyPerTimeSlotView", PathName = "ImpactFrequencyPerTimeSlotViewItems" });
					_pathNodes["customer"].Add( "sites", new PathNode { EntityName = "Site", PathName = "Sites" });
					_pathNodes["customer"].Add( "driverlicenseexpirystoreprocedureitems", new PathNode { EntityName = "DriverLicenseExpiryStoreProcedure", PathName = "DriverLicenseExpiryStoreProcedureItems" });
					_pathNodes.Add ( "departmentchecklist", new Dictionary<string,PathNode>());

					_pathNodes["departmentchecklist"].Add( "preoperationalchecklists", new PathNode { EntityName = "PreOperationalChecklist", PathName = "PreOperationalChecklists" });
					_pathNodes["departmentchecklist"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["departmentchecklist"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes.Add ( "modulehistory", new Dictionary<string,PathNode>());

					_pathNodes["modulehistory"].Add( "module", new PathNode { EntityName = "Module", PathName = "Module" });
					_pathNodes["modulehistory"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "country", new Dictionary<string,PathNode>());

					_pathNodes["country"].Add( "customers", new PathNode { EntityName = "Customer", PathName = "Customers" });
					_pathNodes.Add ( "allchecklistresultview", new Dictionary<string,PathNode>());

					_pathNodes["allchecklistresultview"].Add( "checklistresult", new PathNode { EntityName = "ChecklistResult", PathName = "ChecklistResult" });
					_pathNodes["allchecklistresultview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "gpshistory", new Dictionary<string,PathNode>());

					_pathNodes["gpshistory"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes.Add ( "impactsforvehicleview", new Dictionary<string,PathNode>());

					_pathNodes["impactsforvehicleview"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes["impactsforvehicleview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["impactsforvehicleview"].Add( "impact", new PathNode { EntityName = "Impact", PathName = "Impact" });
					_pathNodes.Add ( "allvehicleunlocksview", new Dictionary<string,PathNode>());

					_pathNodes["allvehicleunlocksview"].Add( "vehiclelockout", new PathNode { EntityName = "VehicleLockout", PathName = "VehicleLockout" });
					_pathNodes["allvehicleunlocksview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "slamcorepedestriandetection", new Dictionary<string,PathNode>());

					_pathNodes["slamcorepedestriandetection"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDevice", PathName = "SlamcoreDevice" });
					_pathNodes.Add ( "impactfrequencyperweekmonthview", new Dictionary<string,PathNode>());

					_pathNodes["impactfrequencyperweekmonthview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["impactfrequencyperweekmonthview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["impactfrequencyperweekmonthview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["impactfrequencyperweekmonthview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "vehicleutilizationlasttwelvehoursview", new Dictionary<string,PathNode>());

					_pathNodes["vehicleutilizationlasttwelvehoursview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["vehicleutilizationlasttwelvehoursview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["vehicleutilizationlasttwelvehoursview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["vehicleutilizationlasttwelvehoursview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "iofield", new Dictionary<string,PathNode>());

					_pathNodes["iofield"].Add( "pstatdetailsitems", new PathNode { EntityName = "PSTATDetails", PathName = "PSTATDetailsItems" });
					_pathNodes["iofield"].Add( "sessiondetailsitems", new PathNode { EntityName = "SessionDetails", PathName = "SessionDetailsItems" });
					_pathNodes.Add ( "licencedetail", new Dictionary<string,PathNode>());

					_pathNodes["licencedetail"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes.Add ( "pedestriandetectionhistoryfilter", new Dictionary<string,PathNode>());

					_pathNodes["pedestriandetectionhistoryfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["pedestriandetectionhistoryfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["pedestriandetectionhistoryfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "maindashboardfilter", new Dictionary<string,PathNode>());

					_pathNodes["maindashboardfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["maindashboardfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["maindashboardfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "allvehiclecalibrationstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["allvehiclecalibrationstoreprocedure"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "cardtocardaccess", new Dictionary<string,PathNode>());

					_pathNodes["cardtocardaccess"].Add( "cardaccesslevel", new PathNode { EntityName = "Permission", PathName = "CardAccessLevel" });
					_pathNodes["cardtocardaccess"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes.Add ( "zonecoordinates", new Dictionary<string,PathNode>());

					_pathNodes["zonecoordinates"].Add( "floorzones", new PathNode { EntityName = "FloorZones", PathName = "FloorZones" });
					_pathNodes.Add ( "snapshot", new Dictionary<string,PathNode>());

					_pathNodes["snapshot"].Add( "revision", new PathNode { EntityName = "Revision", PathName = "Revision" });
					_pathNodes.Add ( "generalproductivityreportfilter", new Dictionary<string,PathNode>());

					_pathNodes["generalproductivityreportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["generalproductivityreportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["generalproductivityreportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "departmentvehiclenormalcardaccess", new Dictionary<string,PathNode>());

					_pathNodes["departmentvehiclenormalcardaccess"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["departmentvehiclenormalcardaccess"].Add( "cardaccesslevel", new PathNode { EntityName = "Permission", PathName = "CardAccessLevel" });
					_pathNodes["departmentvehiclenormalcardaccess"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes.Add ( "customertopersonview", new Dictionary<string,PathNode>());

					_pathNodes["customertopersonview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["customertopersonview"].Add( "emailgroupstopersonitems", new PathNode { EntityName = "EmailGroupsToPerson", PathName = "EmailGroupsToPersonItems" });
					_pathNodes["customertopersonview"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes.Add ( "vehicleslamcorelocationhistory", new Dictionary<string,PathNode>());

					_pathNodes["vehicleslamcorelocationhistory"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDevice", PathName = "SlamcoreDevice" });
					_pathNodes.Add ( "permission", new Dictionary<string,PathNode>());

					_pathNodes["permission"].Add( "cardtocardaccessitems", new PathNode { EntityName = "CardToCardAccess", PathName = "CardToCardAccessItems" });
					_pathNodes.Add ( "sessiondetails", new Dictionary<string,PathNode>());

					_pathNodes["sessiondetails"].Add( "iofield", new PathNode { EntityName = "IOFIELD", PathName = "IOFIELD" });
					_pathNodes["sessiondetails"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes.Add ( "gorole", new Dictionary<string,PathNode>());

					_pathNodes["gorole"].Add( "gouseritems", new PathNode { EntityName = "GOUser", PathName = "GOUserItems" });
					_pathNodes["gorole"].Add( "userroleitems", new PathNode { EntityName = "GOUserRole", PathName = "UserRoleItems" });
					_pathNodes["gorole"].Add( "grouproleitems", new PathNode { EntityName = "GOGroupRole", PathName = "GroupRoleItems" });
					_pathNodes.Add ( "generalproductivitypervehicleview", new Dictionary<string,PathNode>());

					_pathNodes["generalproductivitypervehicleview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["generalproductivitypervehicleview"].Add( "generalproductivityview", new PathNode { EntityName = "GeneralProductivityView", PathName = "GeneralProductivityView" });
					_pathNodes["generalproductivitypervehicleview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "vehicle", new Dictionary<string,PathNode>());

					_pathNodes["vehicle"].Add( "unitunutilisationstoreprocedureitems", new PathNode { EntityName = "UnitUtilisationStoreProcedure", PathName = "UnitUnutilisationStoreProcedureItems" });
					_pathNodes["vehicle"].Add( "broadcastmessagehistoryitems", new PathNode { EntityName = "BroadcastMessageHistory", PathName = "BroadcastMessageHistoryItems" });
					_pathNodes["vehicle"].Add( "sessions", new PathNode { EntityName = "Session", PathName = "Sessions" });
					_pathNodes["vehicle"].Add( "slamcoredevicehistoryitems", new PathNode { EntityName = "SlamcoreDeviceHistory", PathName = "SlamcoreDeviceHistoryItems" });
					_pathNodes["vehicle"].Add( "departmentchecklist", new PathNode { EntityName = "DepartmentChecklist", PathName = "DepartmentChecklist" });
					_pathNodes["vehicle"].Add( "firmware", new PathNode { EntityName = "Firmware", PathName = "Firmware" });
					_pathNodes["vehicle"].Add( "vehicleothersettings", new PathNode { EntityName = "VehicleOtherSettings", PathName = "VehicleOtherSettings" });
					_pathNodes["vehicle"].Add( "vehiclelastgpslocationview", new PathNode { EntityName = "VehicleLastGPSLocationView", PathName = "VehicleLastGPSLocationView" });
					_pathNodes["vehicle"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["vehicle"].Add( "servicesettings", new PathNode { EntityName = "ServiceSettings", PathName = "ServiceSettings" });
					_pathNodes["vehicle"].Add( "vehiclesupervisorsviewitems", new PathNode { EntityName = "VehicleSupervisorsView", PathName = "VehicleSupervisorsViewItems" });
					_pathNodes["vehicle"].Add( "allvehiclecalibrationstoreprocedureitems", new PathNode { EntityName = "AllVehicleCalibrationStoreProcedure", PathName = "AllVehicleCalibrationStoreProcedureItems" });
					_pathNodes["vehicle"].Add( "persontopervehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToPerVehicleMasterAccessView", PathName = "PersonToPerVehicleMasterAccessViewItems" });
					_pathNodes["vehicle"].Add( "inspection", new PathNode { EntityName = "Inspection", PathName = "Inspection" });
					_pathNodes["vehicle"].Add( "ondemandsessionitems", new PathNode { EntityName = "OnDemandSession", PathName = "OnDemandSessionItems" });
					_pathNodes["vehicle"].Add( "pedestriandetectionhistoryitems", new PathNode { EntityName = "PedestrianDetectionHistory", PathName = "PedestrianDetectionHistoryItems" });
					_pathNodes["vehicle"].Add( "messagehistoryitems", new PathNode { EntityName = "MessageHistory", PathName = "MessageHistoryItems" });
					_pathNodes["vehicle"].Add( "vorsettinghistoryitems", new PathNode { EntityName = "VORSettingHistory", PathName = "VORSettingHistoryItems" });
					_pathNodes["vehicle"].Add( "vehiclecardaccesses", new PathNode { EntityName = "PerVehicleMasterCardAccess", PathName = "VehicleCardAccesses" });
					_pathNodes["vehicle"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["vehicle"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes["vehicle"].Add( "vehiclealertsubscriptionitems", new PathNode { EntityName = "VehicleAlertSubscription", PathName = "VehicleAlertSubscriptionItems" });
					_pathNodes["vehicle"].Add( "vehiclelockoutitems", new PathNode { EntityName = "VehicleLockout", PathName = "VehicleLockoutItems" });
					_pathNodes["vehicle"].Add( "generalproductivitypervehicleviewitems", new PathNode { EntityName = "GeneralProductivityPerVehicleView", PathName = "GeneralProductivityPerVehicleViewItems" });
					_pathNodes["vehicle"].Add( "vehicletopreopcheckilstitems", new PathNode { EntityName = "VehicleToPreOpChecklistView", PathName = "VehicleToPreOpCheckilstItems" });
					_pathNodes["vehicle"].Add( "vehiclesessionlessimpactitems", new PathNode { EntityName = "VehicleSessionlessImpact", PathName = "VehicleSessionlessImpactItems" });
					_pathNodes["vehicle"].Add( "currentstatusvehicleviewitems", new PathNode { EntityName = "CurrentStatusVehicleView", PathName = "CurrentStatusVehicleViewItems" });
					_pathNodes["vehicle"].Add( "networksettingsitems", new PathNode { EntityName = "NetworkSettings", PathName = "NetworkSettingsItems" });
					_pathNodes["vehicle"].Add( "ondemandsettings", new PathNode { EntityName = "OnDemandSettings", PathName = "OnDemandSettings" });
					_pathNodes["vehicle"].Add( "module", new PathNode { EntityName = "Module", PathName = "Module" });
					_pathNodes["vehicle"].Add( "detailedsessionviewitems", new PathNode { EntityName = "DetailedSessionView", PathName = "DetailedSessionViewItems" });
					_pathNodes["vehicle"].Add( "checklistsettings", new PathNode { EntityName = "ChecklistSettings", PathName = "ChecklistSettings" });
					_pathNodes["vehicle"].Add( "vehiclediagnostic", new PathNode { EntityName = "VehicleDiagnostic", PathName = "VehicleDiagnostic" });
					_pathNodes["vehicle"].Add( "vehiclebroadcastmessageitems", new PathNode { EntityName = "VehicleBroadcastMessage", PathName = "VehicleBroadcastMessageItems" });
					_pathNodes["vehicle"].Add( "vehiclegpslocations", new PathNode { EntityName = "VehicleGPS", PathName = "VehicleGPSLocations" });
					_pathNodes["vehicle"].Add( "vehiclehiredehirehistoryitems", new PathNode { EntityName = "VehicleHireDehireHistory", PathName = "VehicleHireDehireHistoryItems" });
					_pathNodes["vehicle"].Add( "modulehistoryitems", new PathNode { EntityName = "ModuleHistory", PathName = "ModuleHistoryItems" });
					_pathNodes["vehicle"].Add( "unitutilisationstoreprocedureitems", new PathNode { EntityName = "UnitUnutilisationStoreProcedure", PathName = "UnitUtilisationStoreProcedureItems" });
					_pathNodes["vehicle"].Add( "detailedvorsessionstoreprocedureitems", new PathNode { EntityName = "DetailedVORSessionStoreProcedure", PathName = "DetailedVORSessionStoreProcedureItems" });
					_pathNodes["vehicle"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["vehicle"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["vehicle"].Add( "pervehiclenormalcardaccessitems", new PathNode { EntityName = "PerVehicleNormalCardAccess", PathName = "PerVehicleNormalCardAccessItems" });
					_pathNodes["vehicle"].Add( "impactsforvehicleviewitems", new PathNode { EntityName = "ImpactsForVehicleView", PathName = "ImpactsForVehicleViewItems" });
					_pathNodes["vehicle"].Add( "vehicleproficiencyviewitems", new PathNode { EntityName = "VehicleProficiencyView", PathName = "VehicleProficiencyViewItems" });
					_pathNodes["vehicle"].Add( "checklistfailurepervechicleviewitems", new PathNode { EntityName = "ChecklistFailurePerVechicleView", PathName = "ChecklistFailurePerVechicleViewItems" });
					_pathNodes["vehicle"].Add( "persontopervehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToPerVehicleNormalAccessView", PathName = "PersonToPerVehicleNormalAccessViewItems" });
					_pathNodes["vehicle"].Add( "canrule", new PathNode { EntityName = "Canrule", PathName = "Canrule" });
					_pathNodes["vehicle"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes["vehicle"].Add( "allvorsessionspervehiclestoreprocedureitems", new PathNode { EntityName = "AllVORSessionsPerVehicleStoreProcedure", PathName = "AllVORSessionsPerVehicleStoreProcedureItems" });
					_pathNodes.Add ( "generalproductivityperdriverviewlatest", new Dictionary<string,PathNode>());

					_pathNodes["generalproductivityperdriverviewlatest"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes["generalproductivityperdriverviewlatest"].Add( "generalproductivityview", new PathNode { EntityName = "GeneralProductivityView", PathName = "GeneralProductivityView" });
					_pathNodes["generalproductivityperdriverviewlatest"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "driver", new Dictionary<string,PathNode>());

					_pathNodes["driver"].Add( "currentstatusdriverviewitems", new PathNode { EntityName = "CurrentStatusDriverView", PathName = "CurrentStatusDriverViewItems" });
					_pathNodes["driver"].Add( "vehiclelockouts", new PathNode { EntityName = "VehicleLockout", PathName = "VehicleLockouts" });
					_pathNodes["driver"].Add( "ondemandsessionitems", new PathNode { EntityName = "OnDemandSession", PathName = "OnDemandSessionItems" });
					_pathNodes["driver"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["driver"].Add( "pedestriandetectionhistoryitems", new PathNode { EntityName = "PedestrianDetectionHistory", PathName = "PedestrianDetectionHistoryItems" });
					_pathNodes["driver"].Add( "detailedsessionviewitems", new PathNode { EntityName = "DetailedSessionView", PathName = "DetailedSessionViewItems" });
					_pathNodes["driver"].Add( "generalproductivityperdriverviewlatestitems", new PathNode { EntityName = "GeneralProductivityPerDriverViewLatest", PathName = "GeneralProductivityPerDriverViewLatestItems" });
					_pathNodes["driver"].Add( "generallicence", new PathNode { EntityName = "LicenceDetail", PathName = "GeneralLicence" });
					_pathNodes["driver"].Add( "impactsforvehicleviewitems", new PathNode { EntityName = "ImpactsForVehicleView", PathName = "ImpactsForVehicleViewItems" });
					_pathNodes["driver"].Add( "alllicenseexpiryviewitems", new PathNode { EntityName = "AllLicenseExpiryView", PathName = "AllLicenseExpiryViewItems" });
					_pathNodes["driver"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["driver"].Add( "licensesbymodel", new PathNode { EntityName = "LicenseByModel", PathName = "LicensesByModel" });
					_pathNodes["driver"].Add( "driverproficiencyviewitems", new PathNode { EntityName = "DriverProficiencyView", PathName = "DriverProficiencyViewItems" });
					_pathNodes["driver"].Add( "sessions", new PathNode { EntityName = "Session", PathName = "Sessions" });
					_pathNodes["driver"].Add( "alldriveraccessabusestoreprocedureitems", new PathNode { EntityName = "AllDriverAccessAbuseStoreProcedure", PathName = "AllDriverAccessAbuseStoreProcedureItems" });
					_pathNodes["driver"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes["driver"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["driver"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["driver"].Add( "broadcastmessagehistoryitems", new PathNode { EntityName = "BroadcastMessageHistory", PathName = "BroadcastMessageHistoryItems" });
					_pathNodes.Add ( "emailgroupstoperson", new Dictionary<string,PathNode>());

					_pathNodes["emailgroupstoperson"].Add( "emailgroups", new PathNode { EntityName = "EmailGroups", PathName = "EmailGroups" });
					_pathNodes["emailgroupstoperson"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["emailgroupstoperson"].Add( "customertopersonview", new PathNode { EntityName = "CustomerToPersonView", PathName = "CustomerToPersonView" });
					_pathNodes.Add ( "gogrouprole", new Dictionary<string,PathNode>());

					_pathNodes["gogrouprole"].Add( "role", new PathNode { EntityName = "GORole", PathName = "Role" });
					_pathNodes["gogrouprole"].Add( "group", new PathNode { EntityName = "GOGroup", PathName = "Group" });
					_pathNodes.Add ( "modelvehiclemastercardaccess", new Dictionary<string,PathNode>());

					_pathNodes["modelvehiclemastercardaccess"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes["modelvehiclemastercardaccess"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes["modelvehiclemastercardaccess"].Add( "pervehiclemastercardaccessitems", new PathNode { EntityName = "PerVehicleMasterCardAccess", PathName = "PerVehicleMasterCardAccessItems" });
					_pathNodes.Add ( "emailsubscriptionreportfilter", new Dictionary<string,PathNode>());

					_pathNodes["emailsubscriptionreportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["emailsubscriptionreportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["emailsubscriptionreportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "vehicleproficiencyview", new Dictionary<string,PathNode>());

					_pathNodes["vehicleproficiencyview"].Add( "proficiencycombinedview", new PathNode { EntityName = "ProficiencyCombinedView", PathName = "ProficiencyCombinedView" });
					_pathNodes["vehicleproficiencyview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["vehicleproficiencyview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "dashboarddrivercardview", new Dictionary<string,PathNode>());

					_pathNodes["dashboarddrivercardview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["dashboarddrivercardview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["dashboarddrivercardview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["dashboarddrivercardview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes.Add ( "driverproficiencyview", new Dictionary<string,PathNode>());

					_pathNodes["driverproficiencyview"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes["driverproficiencyview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["driverproficiencyview"].Add( "proficiencycombinedview", new PathNode { EntityName = "ProficiencyCombinedView", PathName = "ProficiencyCombinedView" });
					_pathNodes.Add ( "alerthistory", new Dictionary<string,PathNode>());

					_pathNodes["alerthistory"].Add( "alert", new PathNode { EntityName = "Alert", PathName = "Alert" });
					_pathNodes["alerthistory"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["alerthistory"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes.Add ( "dashboardfilter", new Dictionary<string,PathNode>());

					_pathNodes["dashboardfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["dashboardfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["dashboardfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "vorreportfilter", new Dictionary<string,PathNode>());

					_pathNodes["vorreportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["vorreportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["vorreportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "ondemandauthorisationfilter", new Dictionary<string,PathNode>());

					_pathNodes["ondemandauthorisationfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["ondemandauthorisationfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["ondemandauthorisationfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "websiterole", new Dictionary<string,PathNode>());

					_pathNodes["websiterole"].Add( "websiteuser", new PathNode { EntityName = "WebsiteUser", PathName = "WebsiteUser" });
					_pathNodes.Add ( "gouserrole", new Dictionary<string,PathNode>());

					_pathNodes["gouserrole"].Add( "role", new PathNode { EntityName = "GORole", PathName = "Role" });
					_pathNodes["gouserrole"].Add( "user", new PathNode { EntityName = "GOUser", PathName = "User" });
					_pathNodes.Add ( "generalproductivityview", new Dictionary<string,PathNode>());

					_pathNodes["generalproductivityview"].Add( "unitutilisationstoreprocedureitems", new PathNode { EntityName = "UnitUtilisationStoreProcedure", PathName = "UnitUtilisationStoreProcedureItems" });
					_pathNodes["generalproductivityview"].Add( "generalproductivitypervehicleviewitems", new PathNode { EntityName = "GeneralProductivityPerVehicleView", PathName = "GeneralProductivityPerVehicleViewItems" });
					_pathNodes["generalproductivityview"].Add( "unitunutilisationstoreprocedureitems", new PathNode { EntityName = "UnitUnutilisationStoreProcedure", PathName = "UnitUnutilisationStoreProcedureItems" });
					_pathNodes["generalproductivityview"].Add( "generalproductivityperdriverviewlatestitems", new PathNode { EntityName = "GeneralProductivityPerDriverViewLatest", PathName = "GeneralProductivityPerDriverViewLatestItems" });
					_pathNodes.Add ( "inspection", new Dictionary<string,PathNode>());

					_pathNodes["inspection"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "impact", new Dictionary<string,PathNode>());

					_pathNodes["impact"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes["impact"].Add( "allimpactsviewitems", new PathNode { EntityName = "AllImpactsView", PathName = "AllImpactsViewItems" });
					_pathNodes["impact"].Add( "impactsforvehicleview", new PathNode { EntityName = "ImpactsForVehicleView", PathName = "ImpactsForVehicleView" });
					_pathNodes.Add ( "persontodepartmentvehiclemasteraccessview", new Dictionary<string,PathNode>());

					_pathNodes["persontodepartmentvehiclemasteraccessview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["persontodepartmentvehiclemasteraccessview"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes.Add ( "currentstatusvehicleview", new Dictionary<string,PathNode>());

					_pathNodes["currentstatusvehicleview"].Add( "currentstatuscombinedview", new PathNode { EntityName = "CurrentStatusCombinedView", PathName = "CurrentStatusCombinedView" });
					_pathNodes["currentstatusvehicleview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["currentstatusvehicleview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "customermodel", new Dictionary<string,PathNode>());

					_pathNodes["customermodel"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["customermodel"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes.Add ( "tag", new Dictionary<string,PathNode>());

					_pathNodes["tag"].Add( "revision", new PathNode { EntityName = "Revision", PathName = "Revision" });
					_pathNodes["tag"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes.Add ( "vehiclebroadcastmessage", new Dictionary<string,PathNode>());

					_pathNodes["vehiclebroadcastmessage"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["vehiclebroadcastmessage"].Add( "broadcastmessage", new PathNode { EntityName = "BroadcastMessage", PathName = "BroadcastMessage" });
					_pathNodes.Add ( "floorplan", new Dictionary<string,PathNode>());

					_pathNodes["floorplan"].Add( "sitefloorplanitems", new PathNode { EntityName = "SiteFloorPlan", PathName = "SiteFloorPlanItems" });
					_pathNodes["floorplan"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["floorplan"].Add( "floorzonesitems", new PathNode { EntityName = "FloorZones", PathName = "FloorZonesItems" });
					_pathNodes.Add ( "todayspreopcheckview", new Dictionary<string,PathNode>());

					_pathNodes["todayspreopcheckview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["todayspreopcheckview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["todayspreopcheckview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["todayspreopcheckview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes.Add ( "modelvehiclenormalcardaccess", new Dictionary<string,PathNode>());

					_pathNodes["modelvehiclenormalcardaccess"].Add( "cardaccesslevel", new PathNode { EntityName = "Permission", PathName = "CardAccessLevel" });
					_pathNodes["modelvehiclenormalcardaccess"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["modelvehiclenormalcardaccess"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes["modelvehiclenormalcardaccess"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes.Add ( "sitevehiclenormalcardaccess", new Dictionary<string,PathNode>());

					_pathNodes["sitevehiclenormalcardaccess"].Add( "pervehiclemastercardaccessitems", new PathNode { EntityName = "PerVehicleMasterCardAccess", PathName = "PerVehicleMasterCardAccessItems" });
					_pathNodes["sitevehiclenormalcardaccess"].Add( "cardaccesslevel", new PathNode { EntityName = "Permission", PathName = "CardAccessLevel" });
					_pathNodes["sitevehiclenormalcardaccess"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["sitevehiclenormalcardaccess"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes.Add ( "importjobstatus", new Dictionary<string,PathNode>());

					_pathNodes["importjobstatus"].Add( "importjobbatch", new PathNode { EntityName = "ImportJobBatch", PathName = "ImportJobBatch" });
					_pathNodes["importjobstatus"].Add( "importjoblogitems", new PathNode { EntityName = "ImportJobLog", PathName = "ImportJobLogItems" });
					_pathNodes.Add ( "vehicleothersettings", new Dictionary<string,PathNode>());

					_pathNodes["vehicleothersettings"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "dashboardfiltermorefields", new Dictionary<string,PathNode>());

					_pathNodes["dashboardfiltermorefields"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["dashboardfiltermorefields"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["dashboardfiltermorefields"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "vehiclesupervisorsview", new Dictionary<string,PathNode>());

					_pathNodes["vehiclesupervisorsview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["vehiclesupervisorsview"].Add( "pervehiclenormalcardaccess", new PathNode { EntityName = "PerVehicleNormalCardAccess", PathName = "PerVehicleNormalCardAccess" });
					_pathNodes.Add ( "exportjobstatus", new Dictionary<string,PathNode>());

					_pathNodes["exportjobstatus"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes.Add ( "timezone", new Dictionary<string,PathNode>());

					_pathNodes["timezone"].Add( "sites", new PathNode { EntityName = "Site", PathName = "Sites" });
					_pathNodes.Add ( "hiredehirereportfilter", new Dictionary<string,PathNode>());

					_pathNodes["hiredehirereportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["hiredehirereportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["hiredehirereportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "loggedhoursversusseathoursview", new Dictionary<string,PathNode>());

					_pathNodes["loggedhoursversusseathoursview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["loggedhoursversusseathoursview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["loggedhoursversusseathoursview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["loggedhoursversusseathoursview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "personallocation", new Dictionary<string,PathNode>());

					_pathNodes["personallocation"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["personallocation"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["personallocation"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes.Add ( "broadcastmessage", new Dictionary<string,PathNode>());

					_pathNodes["broadcastmessage"].Add( "vehiclebroadcastmessageitems", new PathNode { EntityName = "VehicleBroadcastMessage", PathName = "VehicleBroadcastMessageItems" });
					_pathNodes.Add ( "vorreportcombinedview", new Dictionary<string,PathNode>());

					_pathNodes["vorreportcombinedview"].Add( "allvorsessionspervehiclestoreprocedureitems", new PathNode { EntityName = "AllVORSessionsPerVehicleStoreProcedure", PathName = "AllVORSessionsPerVehicleStoreProcedureItems" });
					_pathNodes["vorreportcombinedview"].Add( "allvorstatusstoreprocedureitems", new PathNode { EntityName = "AllVORStatusStoreProcedure", PathName = "AllVORStatusStoreProcedureItems" });
					_pathNodes.Add ( "unitutilisationstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["unitutilisationstoreprocedure"].Add( "generalproductivityview", new PathNode { EntityName = "GeneralProductivityView", PathName = "GeneralProductivityView" });
					_pathNodes["unitutilisationstoreprocedure"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "checklistresult", new Dictionary<string,PathNode>());

					_pathNodes["checklistresult"].Add( "checklistanswerdetails", new PathNode { EntityName = "ChecklistDetail", PathName = "ChecklistAnswerDetails" });
					_pathNodes["checklistresult"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes["checklistresult"].Add( "allchecklistresultviewitems", new PathNode { EntityName = "AllChecklistResultView", PathName = "AllChecklistResultViewItems" });
					_pathNodes.Add ( "unitunutilisationstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["unitunutilisationstoreprocedure"].Add( "generalproductivityview", new PathNode { EntityName = "GeneralProductivityView", PathName = "GeneralProductivityView" });
					_pathNodes["unitunutilisationstoreprocedure"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "vehiclealertsubscription", new Dictionary<string,PathNode>());

					_pathNodes["vehiclealertsubscription"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["vehiclealertsubscription"].Add( "alertsubscription", new PathNode { EntityName = "AlertSubscription", PathName = "AlertSubscription" });
					_pathNodes.Add ( "ondemandauthorisationstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["ondemandauthorisationstoreprocedure"].Add( "ondemandsession", new PathNode { EntityName = "OnDemandSession", PathName = "OnDemandSession" });
					_pathNodes.Add ( "impactfrequencypertimeslotview", new Dictionary<string,PathNode>());

					_pathNodes["impactfrequencypertimeslotview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["impactfrequencypertimeslotview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["impactfrequencypertimeslotview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["impactfrequencypertimeslotview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "persontositevehiclenormalaccessview", new Dictionary<string,PathNode>());

					_pathNodes["persontositevehiclenormalaccessview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["persontositevehiclenormalaccessview"].Add( "cardaccesslevel", new PathNode { EntityName = "Permission", PathName = "CardAccessLevel" });
					_pathNodes["persontositevehiclenormalaccessview"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes.Add ( "session", new Dictionary<string,PathNode>());

					_pathNodes["session"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes["session"].Add( "checklistresults", new PathNode { EntityName = "ChecklistResult", PathName = "ChecklistResults" });
					_pathNodes["session"].Add( "vehiclegpsitems", new PathNode { EntityName = "VehicleGPS", PathName = "VehicleGPSItems" });
					_pathNodes["session"].Add( "detailedsessionviewitems", new PathNode { EntityName = "DetailedSessionView", PathName = "DetailedSessionViewItems" });
					_pathNodes["session"].Add( "detailedvorsessionstoreprocedureitems", new PathNode { EntityName = "DetailedVORSessionStoreProcedure", PathName = "DetailedVORSessionStoreProcedureItems" });
					_pathNodes["session"].Add( "pstatdetailsitems", new PathNode { EntityName = "PSTATDetails", PathName = "PSTATDetailsItems" });
					_pathNodes["session"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["session"].Add( "vehiclelockouts", new PathNode { EntityName = "VehicleLockout", PathName = "VehicleLockouts" });
					_pathNodes["session"].Add( "vehiclelastgpslocationview", new PathNode { EntityName = "VehicleLastGPSLocationView", PathName = "VehicleLastGPSLocationView" });
					_pathNodes["session"].Add( "gpshistoryitems", new PathNode { EntityName = "GPSHistory", PathName = "GPSHistoryItems" });
					_pathNodes["session"].Add( "impacts", new PathNode { EntityName = "Impact", PathName = "Impacts" });
					_pathNodes["session"].Add( "sessiondetailsitems", new PathNode { EntityName = "SessionDetails", PathName = "SessionDetailsItems" });
					_pathNodes.Add ( "slamcoredevicefilter", new Dictionary<string,PathNode>());

					_pathNodes["slamcoredevicefilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["slamcoredevicefilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["slamcoredevicefilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "dealer", new Dictionary<string,PathNode>());

					_pathNodes["dealer"].Add( "todaysimpactviewitems", new PathNode { EntityName = "TodaysImpactView", PathName = "TodaysImpactViewItems" });
					_pathNodes["dealer"].Add( "dealerconfiguration", new PathNode { EntityName = "DealerConfiguration", PathName = "DealerConfiguration" });
					_pathNodes["dealer"].Add( "allusersummarystoreprocedureitems", new PathNode { EntityName = "AllUserSummaryStoreProcedure", PathName = "AllUserSummaryStoreProcedureItems" });
					_pathNodes["dealer"].Add( "region", new PathNode { EntityName = "Region", PathName = "Region" });
					_pathNodes["dealer"].Add( "driverlicenseexpiryviewitems", new PathNode { EntityName = "DriverLicenseExpiryView", PathName = "DriverLicenseExpiryViewItems" });
					_pathNodes["dealer"].Add( "checkliststatusviewitems", new PathNode { EntityName = "ChecklistStatusView", PathName = "ChecklistStatusViewItems" });
					_pathNodes["dealer"].Add( "featuresubscriptionsfilteritems", new PathNode { EntityName = "FeatureSubscriptionsFilter", PathName = "FeatureSubscriptionsFilterItems" });
					_pathNodes["dealer"].Add( "moduleitems", new PathNode { EntityName = "Module", PathName = "ModuleItems" });
					_pathNodes["dealer"].Add( "alllicenseexpiryviewitems", new PathNode { EntityName = "AllLicenseExpiryView", PathName = "AllLicenseExpiryViewItems" });
					_pathNodes["dealer"].Add( "allimpactsviewitems", new PathNode { EntityName = "AllImpactsView", PathName = "AllImpactsViewItems" });
					_pathNodes["dealer"].Add( "impactfrequencyperweekdayviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekDayView", PathName = "ImpactFrequencyPerWeekDayViewItems" });
					_pathNodes["dealer"].Add( "currentvehiclestatuschartviewitems", new PathNode { EntityName = "CurrentVehicleStatusChartView", PathName = "CurrentVehicleStatusChartViewItems" });
					_pathNodes["dealer"].Add( "loggedhoursversusseathoursviewitems", new PathNode { EntityName = "LoggedHoursVersusSeatHoursView", PathName = "LoggedHoursVersusSeatHoursViewItems" });
					_pathNodes["dealer"].Add( "todaysimpactstoreprocedureitems", new PathNode { EntityName = "TodaysImpactStoreProcedure", PathName = "TodaysImpactStoreProcedureItems" });
					_pathNodes["dealer"].Add( "vehicleutilizationlasttwelvehoursstoreprocedureitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursStoreProcedure", PathName = "VehicleUtilizationLastTwelveHoursStoreProcedureItems" });
					_pathNodes["dealer"].Add( "allvehicleunlocksviewitems", new PathNode { EntityName = "AllVehicleUnlocksView", PathName = "AllVehicleUnlocksViewItems" });
					_pathNodes["dealer"].Add( "dashboardvehiclecardstoreprocedureitems", new PathNode { EntityName = "DashboardVehicleCardStoreProcedure", PathName = "DashboardVehicleCardStoreProcedureItems" });
					_pathNodes["dealer"].Add( "generalproductivitypervehicleviewitems", new PathNode { EntityName = "GeneralProductivityPerVehicleView", PathName = "GeneralProductivityPerVehicleViewItems" });
					_pathNodes["dealer"].Add( "dashboardcardviewitems", new PathNode { EntityName = "DashboardDriverCardView", PathName = "DashboardCardViewItems" });
					_pathNodes["dealer"].Add( "todayspreopcheckstoreprocedureitems", new PathNode { EntityName = "TodaysPreopCheckStoreProcedure", PathName = "TodaysPreopCheckStoreProcedureItems" });
					_pathNodes["dealer"].Add( "currentstatusvehicleviewitems", new PathNode { EntityName = "CurrentStatusVehicleView", PathName = "CurrentStatusVehicleViewItems" });
					_pathNodes["dealer"].Add( "gouseritems", new PathNode { EntityName = "GOUser", PathName = "GOUserItems" });
					_pathNodes["dealer"].Add( "generalproductivityperdriverviewlatestitems", new PathNode { EntityName = "GeneralProductivityPerDriverViewLatest", PathName = "GeneralProductivityPerDriverViewLatestItems" });
					_pathNodes["dealer"].Add( "alldriveraccessabusestoreprocedureitems", new PathNode { EntityName = "AllDriverAccessAbuseStoreProcedure", PathName = "AllDriverAccessAbuseStoreProcedureItems" });
					_pathNodes["dealer"].Add( "driverlicenseexpirystoreprocedureitems", new PathNode { EntityName = "DriverLicenseExpiryStoreProcedure", PathName = "DriverLicenseExpiryStoreProcedureItems" });
					_pathNodes["dealer"].Add( "driverproficiencyviewitems", new PathNode { EntityName = "DriverProficiencyView", PathName = "DriverProficiencyViewItems" });
					_pathNodes["dealer"].Add( "currentstatusdriverviewitems", new PathNode { EntityName = "CurrentStatusDriverView", PathName = "CurrentStatusDriverViewItems" });
					_pathNodes["dealer"].Add( "incompletedchecklistviewitems", new PathNode { EntityName = "IncompletedChecklistView", PathName = "IncompletedChecklistViewItems" });
					_pathNodes["dealer"].Add( "currentdriverstatuschartviewitems", new PathNode { EntityName = "CurrentDriverStatusChartView", PathName = "CurrentDriverStatusChartViewItems" });
					_pathNodes["dealer"].Add( "impactfrequencyperweekmonthviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekMonthView", PathName = "ImpactFrequencyPerWeekMonthViewItems" });
					_pathNodes["dealer"].Add( "modelitems", new PathNode { EntityName = "Model", PathName = "ModelItems" });
					_pathNodes["dealer"].Add( "dashboarddrivercardstoreprocedureitems", new PathNode { EntityName = "DashboardDriverCardStoreProcedure", PathName = "DashboardDriverCardStoreProcedureItems" });
					_pathNodes["dealer"].Add( "allchecklistresultviewitems", new PathNode { EntityName = "AllChecklistResultView", PathName = "AllChecklistResultViewItems" });
					_pathNodes["dealer"].Add( "vehicleproficiencyviewitems", new PathNode { EntityName = "VehicleProficiencyView", PathName = "VehicleProficiencyViewItems" });
					_pathNodes["dealer"].Add( "todayspreopcheckviewitems", new PathNode { EntityName = "TodaysPreopCheckView", PathName = "TodaysPreopCheckViewItems" });
					_pathNodes["dealer"].Add( "impactfrequencypertimeslotviewitems", new PathNode { EntityName = "ImpactFrequencyPerTimeSlotView", PathName = "ImpactFrequencyPerTimeSlotViewItems" });
					_pathNodes["dealer"].Add( "dashboardvehiclecardviewitems", new PathNode { EntityName = "DashboardVehicleCardView", PathName = "DashboardVehicleCardViewItems" });
					_pathNodes["dealer"].Add( "vehicleutilizationlasttwelvehoursviewitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursView", PathName = "VehicleUtilizationLastTwelveHoursViewItems" });
					_pathNodes["dealer"].Add( "customers", new PathNode { EntityName = "Customer", PathName = "Customers" });
					_pathNodes["dealer"].Add( "dealerfeaturesubscription", new PathNode { EntityName = "DealerFeatureSubscription", PathName = "DealerFeatureSubscription" });
					_pathNodes.Add ( "gouser", new Dictionary<string,PathNode>());

					_pathNodes["gouser"].Add( "gorole", new PathNode { EntityName = "GORole", PathName = "GORole" });
					_pathNodes["gouser"].Add( "gousertocustomeritems", new PathNode { EntityName = "GoUserToCustomer", PathName = "GoUserToCustomerItems" });
					_pathNodes["gouser"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["gouser"].Add( "messagehistoryitems", new PathNode { EntityName = "MessageHistory", PathName = "MessageHistoryItems" });
					_pathNodes["gouser"].Add( "usergroupitems", new PathNode { EntityName = "GOUserGroup", PathName = "UserGroupItems" });
					_pathNodes["gouser"].Add( "customeraudititemscreated", new PathNode { EntityName = "CustomerAudit", PathName = "CustomerAuditItemsCreated" });
					_pathNodes["gouser"].Add( "reportsubscriptionitems", new PathNode { EntityName = "ReportSubscription", PathName = "ReportSubscriptionItems" });
					_pathNodes["gouser"].Add( "userroleitems", new PathNode { EntityName = "GOUserRole", PathName = "UserRoleItems" });
					_pathNodes["gouser"].Add( "customeraudititemsdeleted", new PathNode { EntityName = "CustomerAudit", PathName = "CustomerAuditItemsDeleted" });
					_pathNodes["gouser"].Add( "tag", new PathNode { EntityName = "Tag", PathName = "Tag" });
					_pathNodes["gouser"].Add( "exportjobstatusitems", new PathNode { EntityName = "ExportJobStatus", PathName = "ExportJobStatusItems" });
					_pathNodes["gouser"].Add( "revisionitems", new PathNode { EntityName = "Revision", PathName = "RevisionItems" });
					_pathNodes["gouser"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["gouser"].Add( "gouserdepartmentitems", new PathNode { EntityName = "GOUserDepartment", PathName = "GOUserDepartmentItems" });
					_pathNodes["gouser"].Add( "customeraudititemsmodified", new PathNode { EntityName = "CustomerAudit", PathName = "CustomerAuditItemsModified" });
					_pathNodes["gouser"].Add( "dealerdriver", new PathNode { EntityName = "DealerDriver", PathName = "DealerDriver" });
					_pathNodes["gouser"].Add( "vehiclelockoutitems", new PathNode { EntityName = "VehicleLockout", PathName = "VehicleLockoutItems" });
					_pathNodes["gouser"].Add( "alertsubscriptionitems", new PathNode { EntityName = "AlertSubscription", PathName = "AlertSubscriptionItems" });
					_pathNodes.Add ( "servicesettings", new Dictionary<string,PathNode>());

					_pathNodes["servicesettings"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "updatefirmwarerequest", new Dictionary<string,PathNode>());

					_pathNodes["updatefirmwarerequest"].Add( "firmware", new PathNode { EntityName = "Firmware", PathName = "Firmware" });
					_pathNodes.Add ( "slamcoreapikey", new Dictionary<string,PathNode>());

					_pathNodes["slamcoreapikey"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDevice", PathName = "SlamcoreDevice" });
					_pathNodes.Add ( "card", new Dictionary<string,PathNode>());

					_pathNodes["card"].Add( "modelvehiclenormalcardaccessitems", new PathNode { EntityName = "ModelVehicleNormalCardAccess", PathName = "ModelVehicleNormalCardAccessItems" });
					_pathNodes["card"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes["card"].Add( "sitevehiclenormalcardaccessitems", new PathNode { EntityName = "SiteVehicleNormalCardAccess", PathName = "SiteVehicleNormalCardAccessItems" });
					_pathNodes["card"].Add( "modelvehiclemastercardaccessitems", new PathNode { EntityName = "ModelVehicleMasterCardAccess", PathName = "ModelVehicleMasterCardAccessItems" });
					_pathNodes["card"].Add( "departmentvehiclenormalcardaccessitems", new PathNode { EntityName = "DepartmentVehicleNormalCardAccess", PathName = "DepartmentVehicleNormalCardAccessItems" });
					_pathNodes["card"].Add( "dealerdriver", new PathNode { EntityName = "DealerDriver", PathName = "DealerDriver" });
					_pathNodes["card"].Add( "cardtocardaccessitems", new PathNode { EntityName = "CardToCardAccess", PathName = "CardToCardAccessItems" });
					_pathNodes["card"].Add( "pervehiclenormalcardaccessitems", new PathNode { EntityName = "PerVehicleNormalCardAccess", PathName = "PerVehicleNormalCardAccessItems" });
					_pathNodes["card"].Add( "departmentvehiclemastercardaccessitems", new PathNode { EntityName = "DepartmentVehicleMasterCardAccess", PathName = "DepartmentVehicleMasterCardAccessItems" });
					_pathNodes["card"].Add( "sitevehiclemastercardaccessitems", new PathNode { EntityName = "SiteVehicleMasterCardAccess", PathName = "SiteVehicleMasterCardAccessItems" });
					_pathNodes["card"].Add( "pervehiclemastercardaccessitems", new PathNode { EntityName = "PerVehicleMasterCardAccess", PathName = "PerVehicleMasterCardAccessItems" });
					_pathNodes.Add ( "gouserdepartment", new Dictionary<string,PathNode>());

					_pathNodes["gouserdepartment"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes["gouserdepartment"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes.Add ( "canrule", new Dictionary<string,PathNode>());

					_pathNodes["canrule"].Add( "canruledetailsitems", new PathNode { EntityName = "CanruleDetails", PathName = "CanruleDetailsItems" });
					_pathNodes["canrule"].Add( "vehicles", new PathNode { EntityName = "Vehicle", PathName = "Vehicles" });
					_pathNodes.Add ( "impactfrequencyperweekdayview", new Dictionary<string,PathNode>());

					_pathNodes["impactfrequencyperweekdayview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["impactfrequencyperweekdayview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["impactfrequencyperweekdayview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["impactfrequencyperweekdayview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes.Add ( "slamcoredevicehistory", new Dictionary<string,PathNode>());

					_pathNodes["slamcoredevicehistory"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["slamcoredevicehistory"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDevice", PathName = "SlamcoreDevice" });
					_pathNodes.Add ( "vehiclehiredehirehistory", new Dictionary<string,PathNode>());

					_pathNodes["vehiclehiredehirehistory"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["vehiclehiredehirehistory"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes.Add ( "checklistfailurepervechicleview", new Dictionary<string,PathNode>());

					_pathNodes["checklistfailurepervechicleview"].Add( "preoperationalchecklist", new PathNode { EntityName = "PreOperationalChecklist", PathName = "PreOperationalChecklist" });
					_pathNodes["checklistfailurepervechicleview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "impactreportfilter", new Dictionary<string,PathNode>());

					_pathNodes["impactreportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["impactreportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["impactreportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "incompletedchecklistview", new Dictionary<string,PathNode>());

					_pathNodes["incompletedchecklistview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["incompletedchecklistview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["incompletedchecklistview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["incompletedchecklistview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "reportsubscription", new Dictionary<string,PathNode>());

					_pathNodes["reportsubscription"].Add( "reporttype", new PathNode { EntityName = "ReportType", PathName = "ReportType" });
					_pathNodes["reportsubscription"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes["reportsubscription"].Add( "emailgroups", new PathNode { EntityName = "EmailGroups", PathName = "EmailGroups" });
					_pathNodes["reportsubscription"].Add( "allemailsubscriptionstoreprocedureitems", new PathNode { EntityName = "AllEmailSubscriptionStoreProcedure", PathName = "AllEmailSubscriptionStoreProcedureItems" });
					_pathNodes["reportsubscription"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["reportsubscription"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["reportsubscription"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["reportsubscription"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes.Add ( "vehiclelastgpslocationview", new Dictionary<string,PathNode>());

					_pathNodes["vehiclelastgpslocationview"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes["vehiclelastgpslocationview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "pstatdetails", new Dictionary<string,PathNode>());

					_pathNodes["pstatdetails"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes["pstatdetails"].Add( "iofield", new PathNode { EntityName = "IOFIELD", PathName = "IOFIELD" });
					_pathNodes.Add ( "licenseexpiryreportfilter", new Dictionary<string,PathNode>());

					_pathNodes["licenseexpiryreportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["licenseexpiryreportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["licenseexpiryreportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "floorzones", new Dictionary<string,PathNode>());

					_pathNodes["floorzones"].Add( "zonecoordinatesitems", new PathNode { EntityName = "ZoneCoordinates", PathName = "ZoneCoordinatesItems" });
					_pathNodes["floorzones"].Add( "floorplan", new PathNode { EntityName = "FloorPlan", PathName = "FloorPlan" });
					_pathNodes.Add ( "canruledetails", new Dictionary<string,PathNode>());

					_pathNodes["canruledetails"].Add( "canrule", new PathNode { EntityName = "Canrule", PathName = "Canrule" });
					_pathNodes.Add ( "driverlicenseexpirystoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["driverlicenseexpirystoreprocedure"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["driverlicenseexpirystoreprocedure"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["driverlicenseexpirystoreprocedure"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["driverlicenseexpirystoreprocedure"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "departmentvehiclemastercardaccess", new Dictionary<string,PathNode>());

					_pathNodes["departmentvehiclemastercardaccess"].Add( "pervehiclemastercardaccessitems", new PathNode { EntityName = "PerVehicleMasterCardAccess", PathName = "PerVehicleMasterCardAccessItems" });
					_pathNodes["departmentvehiclemastercardaccess"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes["departmentvehiclemastercardaccess"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes.Add ( "vehicleutilizationlasttwelvehoursstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["vehicleutilizationlasttwelvehoursstoreprocedure"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["vehicleutilizationlasttwelvehoursstoreprocedure"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["vehicleutilizationlasttwelvehoursstoreprocedure"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["vehicleutilizationlasttwelvehoursstoreprocedure"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "checklistfailureview", new Dictionary<string,PathNode>());

					_pathNodes["checklistfailureview"].Add( "preoperationalchecklist", new PathNode { EntityName = "PreOperationalChecklist", PathName = "PreOperationalChecklist" });
					_pathNodes.Add ( "personchecklistlanguagesettings", new Dictionary<string,PathNode>());

					_pathNodes["personchecklistlanguagesettings"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes.Add ( "vehiclesessionlessimpact", new Dictionary<string,PathNode>());

					_pathNodes["vehiclesessionlessimpact"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "licensebymodel", new Dictionary<string,PathNode>());

					_pathNodes["licensebymodel"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes["licensebymodel"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes.Add ( "allmessagehistorystoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["allmessagehistorystoreprocedure"].Add( "messagehistory", new PathNode { EntityName = "MessageHistory", PathName = "MessageHistory" });
					_pathNodes.Add ( "proficiencycombinedview", new Dictionary<string,PathNode>());

					_pathNodes["proficiencycombinedview"].Add( "vehicleproficiencyviewitems", new PathNode { EntityName = "VehicleProficiencyView", PathName = "VehicleProficiencyViewItems" });
					_pathNodes["proficiencycombinedview"].Add( "driverproficiencyviewitems", new PathNode { EntityName = "DriverProficiencyView", PathName = "DriverProficiencyViewItems" });
					_pathNodes.Add ( "customersnapshot", new Dictionary<string,PathNode>());

					_pathNodes["customersnapshot"].Add( "revision", new PathNode { EntityName = "Revision", PathName = "Revision" });
					_pathNodes["customersnapshot"].Add( "customerbeingchangetracked", new PathNode { EntityName = "Customer", PathName = "CustomerBeingChangeTracked" });
					_pathNodes.Add ( "gogroup", new Dictionary<string,PathNode>());

					_pathNodes["gogroup"].Add( "grouproleitems", new PathNode { EntityName = "GOGroupRole", PathName = "GroupRoleItems" });
					_pathNodes["gogroup"].Add( "usergroupitems", new PathNode { EntityName = "GOUserGroup", PathName = "UserGroupItems" });
					_pathNodes.Add ( "vehiclespermodelreport", new Dictionary<string,PathNode>());

					_pathNodes["vehiclespermodelreport"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes.Add ( "todaysimpactstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["todaysimpactstoreprocedure"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["todaysimpactstoreprocedure"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["todaysimpactstoreprocedure"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["todaysimpactstoreprocedure"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes.Add ( "broadcastmessagehistoryfilter", new Dictionary<string,PathNode>());

					_pathNodes["broadcastmessagehistoryfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["broadcastmessagehistoryfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["broadcastmessagehistoryfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "dealerdriver", new Dictionary<string,PathNode>());

					_pathNodes["dealerdriver"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes["dealerdriver"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes.Add ( "emailgroups", new Dictionary<string,PathNode>());

					_pathNodes["emailgroups"].Add( "emailgroupstopersonitems", new PathNode { EntityName = "EmailGroupsToPerson", PathName = "EmailGroupsToPersonItems" });
					_pathNodes["emailgroups"].Add( "reportsubscriptions", new PathNode { EntityName = "ReportSubscription", PathName = "ReportSubscriptions" });
					_pathNodes["emailgroups"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "preoperationalchecklist", new Dictionary<string,PathNode>());

					_pathNodes["preoperationalchecklist"].Add( "checklistanswersdetails", new PathNode { EntityName = "ChecklistDetail", PathName = "ChecklistAnswersDetails" });
					_pathNodes["preoperationalchecklist"].Add( "vehicletopreopcheckilstitems", new PathNode { EntityName = "VehicleToPreOpChecklistView", PathName = "VehicleToPreOpCheckilstItems" });
					_pathNodes["preoperationalchecklist"].Add( "sitechecklist", new PathNode { EntityName = "DepartmentChecklist", PathName = "SiteChecklist" });
					_pathNodes["preoperationalchecklist"].Add( "checklistfailurepervechicleviewitems", new PathNode { EntityName = "ChecklistFailurePerVechicleView", PathName = "ChecklistFailurePerVechicleViewItems" });
					_pathNodes["preoperationalchecklist"].Add( "checklistfailureview", new PathNode { EntityName = "ChecklistFailureView", PathName = "ChecklistFailureView" });
					_pathNodes.Add ( "synchronizationstatusreportfilter", new Dictionary<string,PathNode>());

					_pathNodes["synchronizationstatusreportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["synchronizationstatusreportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["synchronizationstatusreportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "slamcoredevice", new Dictionary<string,PathNode>());

					_pathNodes["slamcoredevice"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["slamcoredevice"].Add( "slamcorepedestriandetectionitems", new PathNode { EntityName = "SlamcorePedestrianDetection", PathName = "SlamcorePedestrianDetectionItems" });
					_pathNodes["slamcoredevice"].Add( "slamcoreapikey", new PathNode { EntityName = "SlamcoreAPIKey", PathName = "SlamcoreAPIKey" });
					_pathNodes["slamcoredevice"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["slamcoredevice"].Add( "vehicleslamcorelocationhistoryitems", new PathNode { EntityName = "VehicleSlamcoreLocationHistory", PathName = "VehicleSlamcoreLocationHistoryItems" });
					_pathNodes["slamcoredevice"].Add( "slamcoreawareauthenticationdetails", new PathNode { EntityName = "SlamcoreAwareAuthenticationDetails", PathName = "SlamcoreAwareAuthenticationDetails" });
					_pathNodes["slamcoredevice"].Add( "slamcoredevicehistoryitems", new PathNode { EntityName = "SlamcoreDeviceHistory", PathName = "SlamcoreDeviceHistoryItems" });
					_pathNodes["slamcoredevice"].Add( "slamcoredeviceconnectionviewitems", new PathNode { EntityName = "SlamcoreDeviceConnectionView", PathName = "SlamcoreDeviceConnectionViewItems" });
					_pathNodes.Add ( "site", new Dictionary<string,PathNode>());

					_pathNodes["site"].Add( "impactfrequencyperweekmonthviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekMonthView", PathName = "ImpactFrequencyPerWeekMonthViewItems" });
					_pathNodes["site"].Add( "vehicleutilizationlasttwelvehoursstoreprocedureitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursStoreProcedure", PathName = "VehicleUtilizationLastTwelveHoursStoreProcedureItems" });
					_pathNodes["site"].Add( "currentvehiclestatuschartviewitems", new PathNode { EntityName = "CurrentVehicleStatusChartView", PathName = "CurrentVehicleStatusChartViewItems" });
					_pathNodes["site"].Add( "sitefloorplanitems", new PathNode { EntityName = "SiteFloorPlan", PathName = "SiteFloorPlanItems" });
					_pathNodes["site"].Add( "checkliststatusviewitems", new PathNode { EntityName = "ChecklistStatusView", PathName = "ChecklistStatusViewItems" });
					_pathNodes["site"].Add( "driverlicenseexpirystoreprocedureitems", new PathNode { EntityName = "DriverLicenseExpiryStoreProcedure", PathName = "DriverLicenseExpiryStoreProcedureItems" });
					_pathNodes["site"].Add( "floorplanitems", new PathNode { EntityName = "FloorPlan", PathName = "FloorPlanItems" });
					_pathNodes["site"].Add( "personallocationitems", new PathNode { EntityName = "PersonAllocation", PathName = "PersonAllocationItems" });
					_pathNodes["site"].Add( "timezone", new PathNode { EntityName = "Timezone", PathName = "Timezone" });
					_pathNodes["site"].Add( "todaysimpactstoreprocedureitems", new PathNode { EntityName = "TodaysImpactStoreProcedure", PathName = "TodaysImpactStoreProcedureItems" });
					_pathNodes["site"].Add( "impactfrequencyperweekdayviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekDayView", PathName = "ImpactFrequencyPerWeekDayViewItems" });
					_pathNodes["site"].Add( "dashboardfilteritems", new PathNode { EntityName = "DashboardFilter", PathName = "DashboardFilterItems" });
					_pathNodes["site"].Add( "driveritems", new PathNode { EntityName = "Driver", PathName = "DriverItems" });
					_pathNodes["site"].Add( "sitevehiclemastercardaccessitems", new PathNode { EntityName = "SiteVehicleMasterCardAccess", PathName = "SiteVehicleMasterCardAccessItems" });
					_pathNodes["site"].Add( "dashboardvehiclecardviewitems", new PathNode { EntityName = "DashboardVehicleCardView", PathName = "DashboardVehicleCardViewItems" });
					_pathNodes["site"].Add( "todayspreopcheckviewitems", new PathNode { EntityName = "TodaysPreopCheckView", PathName = "TodaysPreopCheckViewItems" });
					_pathNodes["site"].Add( "impactfrequencypertimeslotviewitems", new PathNode { EntityName = "ImpactFrequencyPerTimeSlotView", PathName = "ImpactFrequencyPerTimeSlotViewItems" });
					_pathNodes["site"].Add( "dashboardcardviewitems", new PathNode { EntityName = "DashboardDriverCardView", PathName = "DashboardCardViewItems" });
					_pathNodes["site"].Add( "currentdriverstatuschartviewitems", new PathNode { EntityName = "CurrentDriverStatusChartView", PathName = "CurrentDriverStatusChartViewItems" });
					_pathNodes["site"].Add( "persontositevehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToSiteVehicleNormalAccessView", PathName = "PersonToSiteVehicleNormalAccessViewItems" });
					_pathNodes["site"].Add( "incompletedchecklistviewitems", new PathNode { EntityName = "IncompletedChecklistView", PathName = "IncompletedChecklistViewItems" });
					_pathNodes["site"].Add( "dashboardvehiclecardstoreprocedureitems", new PathNode { EntityName = "DashboardVehicleCardStoreProcedure", PathName = "DashboardVehicleCardStoreProcedureItems" });
					_pathNodes["site"].Add( "persontositevehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToSiteVehicleMasterAccessView", PathName = "PersonToSiteVehicleMasterAccessViewItems" });
					_pathNodes["site"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["site"].Add( "vehicleutilizationlasttwelvehoursviewitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursView", PathName = "VehicleUtilizationLastTwelveHoursViewItems" });
					_pathNodes["site"].Add( "departmentitems", new PathNode { EntityName = "Department", PathName = "DepartmentItems" });
					_pathNodes["site"].Add( "driverlicenseexpiryviewitems", new PathNode { EntityName = "DriverLicenseExpiryView", PathName = "DriverLicenseExpiryViewItems" });
					_pathNodes["site"].Add( "todayspreopcheckstoreprocedureitems", new PathNode { EntityName = "TodaysPreopCheckStoreProcedure", PathName = "TodaysPreopCheckStoreProcedureItems" });
					_pathNodes["site"].Add( "loggedhoursversusseathoursviewitems", new PathNode { EntityName = "LoggedHoursVersusSeatHoursView", PathName = "LoggedHoursVersusSeatHoursViewItems" });
					_pathNodes["site"].Add( "accessgrouptositeitems", new PathNode { EntityName = "AccessGroupToSite", PathName = "AccessGroupToSiteItems" });
					_pathNodes["site"].Add( "personitems", new PathNode { EntityName = "Person", PathName = "PersonItems" });
					_pathNodes["site"].Add( "dashboarddrivercardstoreprocedureitems", new PathNode { EntityName = "DashboardDriverCardStoreProcedure", PathName = "DashboardDriverCardStoreProcedureItems" });
					_pathNodes["site"].Add( "todaysimpactviewitems", new PathNode { EntityName = "TodaysImpactView", PathName = "TodaysImpactViewItems" });
					_pathNodes["site"].Add( "vehicleitems", new PathNode { EntityName = "Vehicle", PathName = "VehicleItems" });
					_pathNodes["site"].Add( "sitevehiclenormalcardaccessitems", new PathNode { EntityName = "SiteVehicleNormalCardAccess", PathName = "SiteVehicleNormalCardAccessItems" });
					_pathNodes.Add ( "alertsubscription", new Dictionary<string,PathNode>());

					_pathNodes["alertsubscription"].Add( "alert", new PathNode { EntityName = "Alert", PathName = "Alert" });
					_pathNodes["alertsubscription"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes["alertsubscription"].Add( "vehiclealertsubscriptionitems", new PathNode { EntityName = "VehicleAlertSubscription", PathName = "VehicleAlertSubscriptionItems" });
					_pathNodes["alertsubscription"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes.Add ( "customeraudit", new Dictionary<string,PathNode>());

					_pathNodes["customeraudit"].Add( "revisiondeleted", new PathNode { EntityName = "Revision", PathName = "RevisionDeleted" });
					_pathNodes["customeraudit"].Add( "revisionlastmodified", new PathNode { EntityName = "Revision", PathName = "RevisionLastModified" });
					_pathNodes["customeraudit"].Add( "revisioncreated", new PathNode { EntityName = "Revision", PathName = "RevisionCreated" });
					_pathNodes["customeraudit"].Add( "gouserwhodeletedthiscustomer", new PathNode { EntityName = "GOUser", PathName = "GOUserWhoDeletedThisCustomer" });
					_pathNodes["customeraudit"].Add( "gouserwhocreatedthiscustomer", new PathNode { EntityName = "GOUser", PathName = "GOUserWhoCreatedThisCustomer" });
					_pathNodes["customeraudit"].Add( "gouserwhomodifiedthiscustomer", new PathNode { EntityName = "GOUser", PathName = "GOUserWhoModifiedThisCustomer" });
					_pathNodes["customeraudit"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "pervehiclenormalcardaccess", new Dictionary<string,PathNode>());

					_pathNodes["pervehiclenormalcardaccess"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes["pervehiclenormalcardaccess"].Add( "vehiclesupervisorsviewitems", new PathNode { EntityName = "VehicleSupervisorsView", PathName = "VehicleSupervisorsViewItems" });
					_pathNodes["pervehiclenormalcardaccess"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["pervehiclenormalcardaccess"].Add( "cardaccesslevel", new PathNode { EntityName = "Permission", PathName = "CardAccessLevel" });
					_pathNodes.Add ( "pedestriandetectionhistory", new Dictionary<string,PathNode>());

					_pathNodes["pedestriandetectionhistory"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes["pedestriandetectionhistory"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "persontopervehiclenormalaccessview", new Dictionary<string,PathNode>());

					_pathNodes["persontopervehiclenormalaccessview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["persontopervehiclenormalaccessview"].Add( "cardaccesslevel", new PathNode { EntityName = "Permission", PathName = "CardAccessLevel" });
					_pathNodes["persontopervehiclenormalaccessview"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes.Add ( "dashboarddrivercardstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["dashboarddrivercardstoreprocedure"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["dashboarddrivercardstoreprocedure"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["dashboarddrivercardstoreprocedure"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["dashboarddrivercardstoreprocedure"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes.Add ( "todaysimpactview", new Dictionary<string,PathNode>());

					_pathNodes["todaysimpactview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["todaysimpactview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["todaysimpactview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["todaysimpactview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "detailedsessionview", new Dictionary<string,PathNode>());

					_pathNodes["detailedsessionview"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes["detailedsessionview"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes["detailedsessionview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "importjoblog", new Dictionary<string,PathNode>());

					_pathNodes["importjoblog"].Add( "importjobstatus", new PathNode { EntityName = "ImportJobStatus", PathName = "ImportJobStatus" });
					_pathNodes["importjoblog"].Add( "importjobbatch", new PathNode { EntityName = "ImportJobBatch", PathName = "ImportJobBatch" });
					_pathNodes.Add ( "proficiencyreportfilter", new Dictionary<string,PathNode>());

					_pathNodes["proficiencyreportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["proficiencyreportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["proficiencyreportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "ondemandsession", new Dictionary<string,PathNode>());

					_pathNodes["ondemandsession"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["ondemandsession"].Add( "ondemandauthorisationstoreprocedureitems", new PathNode { EntityName = "OnDemandAuthorisationStoreProcedure", PathName = "OnDemandAuthorisationStoreProcedureItems" });
					_pathNodes["ondemandsession"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes.Add ( "allvorstatusstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["allvorstatusstoreprocedure"].Add( "vorreportcombinedview", new PathNode { EntityName = "VORReportCombinedView", PathName = "VORReportCombinedView" });
					_pathNodes["allvorstatusstoreprocedure"].Add( "vorsettinghistory", new PathNode { EntityName = "VORSettingHistory", PathName = "VORSettingHistory" });
					_pathNodes.Add ( "vorsettinghistory", new Dictionary<string,PathNode>());

					_pathNodes["vorsettinghistory"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["vorsettinghistory"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["vorsettinghistory"].Add( "allvorstatusstoreprocedureitems", new PathNode { EntityName = "AllVORStatusStoreProcedure", PathName = "AllVORStatusStoreProcedureItems" });
					_pathNodes.Add ( "allusersummarystoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["allusersummarystoreprocedure"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["allusersummarystoreprocedure"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "customerssodetail", new Dictionary<string,PathNode>());

					_pathNodes["customerssodetail"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "allvehiclecalibrationfilter", new Dictionary<string,PathNode>());

					_pathNodes["allvehiclecalibrationfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["allvehiclecalibrationfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["allvehiclecalibrationfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "departmenthoursettings", new Dictionary<string,PathNode>());

					_pathNodes["departmenthoursettings"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes.Add ( "dashboardvehiclecardview", new Dictionary<string,PathNode>());

					_pathNodes["dashboardvehiclecardview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["dashboardvehiclecardview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["dashboardvehiclecardview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["dashboardvehiclecardview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes.Add ( "currentstatusdriverview", new Dictionary<string,PathNode>());

					_pathNodes["currentstatusdriverview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["currentstatusdriverview"].Add( "currentstatuscombinedview", new PathNode { EntityName = "CurrentStatusCombinedView", PathName = "CurrentStatusCombinedView" });
					_pathNodes["currentstatusdriverview"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes.Add ( "reporttype", new Dictionary<string,PathNode>());

					_pathNodes["reporttype"].Add( "reportsubscriptions", new PathNode { EntityName = "ReportSubscription", PathName = "ReportSubscriptions" });
					_pathNodes.Add ( "sitevehiclemastercardaccess", new Dictionary<string,PathNode>());

					_pathNodes["sitevehiclemastercardaccess"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["sitevehiclemastercardaccess"].Add( "card", new PathNode { EntityName = "Card", PathName = "Card" });
					_pathNodes.Add ( "persontodepartmentvehiclenormalaccessview", new Dictionary<string,PathNode>());

					_pathNodes["persontodepartmentvehiclenormalaccessview"].Add( "cardaccesslevel", new PathNode { EntityName = "Permission", PathName = "CardAccessLevel" });
					_pathNodes["persontodepartmentvehiclenormalaccessview"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["persontodepartmentvehiclenormalaccessview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes.Add ( "gousergroup", new Dictionary<string,PathNode>());

					_pathNodes["gousergroup"].Add( "user", new PathNode { EntityName = "GOUser", PathName = "User" });
					_pathNodes["gousergroup"].Add( "group", new PathNode { EntityName = "GOGroup", PathName = "Group" });
					_pathNodes.Add ( "customerfeaturesubscription", new Dictionary<string,PathNode>());

					_pathNodes["customerfeaturesubscription"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "vehicletopreopchecklistview", new Dictionary<string,PathNode>());

					_pathNodes["vehicletopreopchecklistview"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["vehicletopreopchecklistview"].Add( "preoperationalchecklist", new PathNode { EntityName = "PreOperationalChecklist", PathName = "PreOperationalChecklist" });
					_pathNodes.Add ( "persontositevehiclemasteraccessview", new Dictionary<string,PathNode>());

					_pathNodes["persontositevehiclemasteraccessview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["persontositevehiclemasteraccessview"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes.Add ( "dealerconfiguration", new Dictionary<string,PathNode>());

					_pathNodes["dealerconfiguration"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes.Add ( "todayspreopcheckstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["todayspreopcheckstoreprocedure"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["todayspreopcheckstoreprocedure"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["todayspreopcheckstoreprocedure"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["todayspreopcheckstoreprocedure"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "customerpreoperationalchecklisttemplate", new Dictionary<string,PathNode>());

					_pathNodes["customerpreoperationalchecklisttemplate"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "currentdriverstatuschartview", new Dictionary<string,PathNode>());

					_pathNodes["currentdriverstatuschartview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["currentdriverstatuschartview"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["currentdriverstatuschartview"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["currentdriverstatuschartview"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "preopreportfilter", new Dictionary<string,PathNode>());

					_pathNodes["preopreportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["preopreportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["preopreportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "sitefloorplan", new Dictionary<string,PathNode>());

					_pathNodes["sitefloorplan"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["sitefloorplan"].Add( "floorplan", new PathNode { EntityName = "FloorPlan", PathName = "FloorPlan" });
					_pathNodes.Add ( "broadcastmessagehistory", new Dictionary<string,PathNode>());

					_pathNodes["broadcastmessagehistory"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["broadcastmessagehistory"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes.Add ( "gousertocustomer", new Dictionary<string,PathNode>());

					_pathNodes["gousertocustomer"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["gousertocustomer"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes.Add ( "importjobbatch", new Dictionary<string,PathNode>());

					_pathNodes["importjobbatch"].Add( "importjobstatusitems", new PathNode { EntityName = "ImportJobStatus", PathName = "ImportJobStatusItems" });
					_pathNodes["importjobbatch"].Add( "importjoblogitems", new PathNode { EntityName = "ImportJobLog", PathName = "ImportJobLogItems" });
					_pathNodes.Add ( "person", new Dictionary<string,PathNode>());

					_pathNodes["person"].Add( "persontopervehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToPerVehicleMasterAccessView", PathName = "PersonToPerVehicleMasterAccessViewItems" });
					_pathNodes["person"].Add( "accessgroup", new PathNode { EntityName = "AccessGroup", PathName = "AccessGroup" });
					_pathNodes["person"].Add( "alertsubscriptions", new PathNode { EntityName = "AlertSubscription", PathName = "AlertSubscriptions" });
					_pathNodes["person"].Add( "personallocationitems", new PathNode { EntityName = "PersonAllocation", PathName = "PersonAllocationItems" });
					_pathNodes["person"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["person"].Add( "reportsubscriptionitems", new PathNode { EntityName = "ReportSubscription", PathName = "ReportSubscriptionItems" });
					_pathNodes["person"].Add( "persontodepartmentvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToDepartmentVehicleNormalAccessView", PathName = "PersonToDepartmentVehicleNormalAccessViewItems" });
					_pathNodes["person"].Add( "customertopersonviewitems", new PathNode { EntityName = "CustomerToPersonView", PathName = "CustomerToPersonViewItems" });
					_pathNodes["person"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["person"].Add( "websiteuser", new PathNode { EntityName = "WebsiteUser", PathName = "WebsiteUser" });
					_pathNodes["person"].Add( "persontodepartmentvehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToDepartmentVehicleMasterAccessView", PathName = "PersonToDepartmentVehicleMasterAccessViewItems" });
					_pathNodes["person"].Add( "persontopervehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToPerVehicleNormalAccessView", PathName = "PersonToPerVehicleNormalAccessViewItems" });
					_pathNodes["person"].Add( "vorsettinghistoryitems", new PathNode { EntityName = "VORSettingHistory", PathName = "VORSettingHistoryItems" });
					_pathNodes["person"].Add( "personchecklistlanguagesettings", new PathNode { EntityName = "PersonChecklistLanguageSettings", PathName = "PersonChecklistLanguageSettings" });
					_pathNodes["person"].Add( "allusersummarystoreprocedureitems", new PathNode { EntityName = "AllUserSummaryStoreProcedure", PathName = "AllUserSummaryStoreProcedureItems" });
					_pathNodes["person"].Add( "persontomodelvehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToModelVehicleMasterAccessView", PathName = "PersonToModelVehicleMasterAccessViewItems" });
					_pathNodes["person"].Add( "persontositevehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToSiteVehicleNormalAccessView", PathName = "PersonToSiteVehicleNormalAccessViewItems" });
					_pathNodes["person"].Add( "emailgroupstopersonitems", new PathNode { EntityName = "EmailGroupsToPerson", PathName = "EmailGroupsToPersonItems" });
					_pathNodes["person"].Add( "persontositevehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToSiteVehicleMasterAccessView", PathName = "PersonToSiteVehicleMasterAccessViewItems" });
					_pathNodes["person"].Add( "persontomodelvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToModelVehicleNormalAccessView", PathName = "PersonToModelVehicleNormalAccessViewItems" });
					_pathNodes["person"].Add( "gouser", new PathNode { EntityName = "GOUser", PathName = "GOUser" });
					_pathNodes["person"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["person"].Add( "vehicleitems", new PathNode { EntityName = "Vehicle", PathName = "VehicleItems" });
					_pathNodes["person"].Add( "driver", new PathNode { EntityName = "Driver", PathName = "Driver" });
					_pathNodes.Add ( "detailedvorsessionstoreprocedure", new Dictionary<string,PathNode>());

					_pathNodes["detailedvorsessionstoreprocedure"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["detailedvorsessionstoreprocedure"].Add( "session", new PathNode { EntityName = "Session", PathName = "Session" });
					_pathNodes.Add ( "persontomodelvehiclenormalaccessview", new Dictionary<string,PathNode>());

					_pathNodes["persontomodelvehiclenormalaccessview"].Add( "model", new PathNode { EntityName = "Model", PathName = "Model" });
					_pathNodes["persontomodelvehiclenormalaccessview"].Add( "person", new PathNode { EntityName = "Person", PathName = "Person" });
					_pathNodes["persontomodelvehiclenormalaccessview"].Add( "cardaccesslevel", new PathNode { EntityName = "Permission", PathName = "CardAccessLevel" });
					_pathNodes["persontomodelvehiclenormalaccessview"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes.Add ( "networksettings", new Dictionary<string,PathNode>());

					_pathNodes["networksettings"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes.Add ( "department", new Dictionary<string,PathNode>());

					_pathNodes["department"].Add( "currentvehiclestatuschartviewitems", new PathNode { EntityName = "CurrentVehicleStatusChartView", PathName = "CurrentVehicleStatusChartViewItems" });
					_pathNodes["department"].Add( "incompletedchecklistviewitems", new PathNode { EntityName = "IncompletedChecklistView", PathName = "IncompletedChecklistViewItems" });
					_pathNodes["department"].Add( "personitems", new PathNode { EntityName = "Person", PathName = "PersonItems" });
					_pathNodes["department"].Add( "vehicleutilizationlasttwelvehoursstoreprocedureitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursStoreProcedure", PathName = "VehicleUtilizationLastTwelveHoursStoreProcedureItems" });
					_pathNodes["department"].Add( "driveritems", new PathNode { EntityName = "Driver", PathName = "DriverItems" });
					_pathNodes["department"].Add( "todayspreopcheckstoreprocedureitems", new PathNode { EntityName = "TodaysPreopCheckStoreProcedure", PathName = "TodaysPreopCheckStoreProcedureItems" });
					_pathNodes["department"].Add( "departmentvehiclemastercardaccessitems", new PathNode { EntityName = "DepartmentVehicleMasterCardAccess", PathName = "DepartmentVehicleMasterCardAccessItems" });
					_pathNodes["department"].Add( "departmentvehiclenormalcardaccessitems", new PathNode { EntityName = "DepartmentVehicleNormalCardAccess", PathName = "DepartmentVehicleNormalCardAccessItems" });
					_pathNodes["department"].Add( "todayspreopcheckviewitems", new PathNode { EntityName = "TodaysPreopCheckView", PathName = "TodaysPreopCheckViewItems" });
					_pathNodes["department"].Add( "vehiclehiredehirehistoryitems", new PathNode { EntityName = "VehicleHireDehireHistory", PathName = "VehicleHireDehireHistoryItems" });
					_pathNodes["department"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["department"].Add( "todaysimpactstoreprocedureitems", new PathNode { EntityName = "TodaysImpactStoreProcedure", PathName = "TodaysImpactStoreProcedureItems" });
					_pathNodes["department"].Add( "vehicleutilizationlasttwelvehoursviewitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursView", PathName = "VehicleUtilizationLastTwelveHoursViewItems" });
					_pathNodes["department"].Add( "currentdriverstatuschartviewitems", new PathNode { EntityName = "CurrentDriverStatusChartView", PathName = "CurrentDriverStatusChartViewItems" });
					_pathNodes["department"].Add( "dashboardfilteritems", new PathNode { EntityName = "DashboardFilter", PathName = "DashboardFilterItems" });
					_pathNodes["department"].Add( "impactfrequencypertimeslotviewitems", new PathNode { EntityName = "ImpactFrequencyPerTimeSlotView", PathName = "ImpactFrequencyPerTimeSlotViewItems" });
					_pathNodes["department"].Add( "checkliststatusviewitems", new PathNode { EntityName = "ChecklistStatusView", PathName = "ChecklistStatusViewItems" });
					_pathNodes["department"].Add( "impactfrequencyperweekdayviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekDayView", PathName = "ImpactFrequencyPerWeekDayViewItems" });
					_pathNodes["department"].Add( "driverlicenseexpiryviewitems", new PathNode { EntityName = "DriverLicenseExpiryView", PathName = "DriverLicenseExpiryViewItems" });
					_pathNodes["department"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes["department"].Add( "persontomodelvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToModelVehicleNormalAccessView", PathName = "PersonToModelVehicleNormalAccessViewItems" });
					_pathNodes["department"].Add( "dashboardvehiclecardstoreprocedureitems", new PathNode { EntityName = "DashboardVehicleCardStoreProcedure", PathName = "DashboardVehicleCardStoreProcedureItems" });
					_pathNodes["department"].Add( "persontodepartmentvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToDepartmentVehicleNormalAccessView", PathName = "PersonToDepartmentVehicleNormalAccessViewItems" });
					_pathNodes["department"].Add( "departmenthoursettings", new PathNode { EntityName = "DepartmentHourSettings", PathName = "DepartmentHourSettings" });
					_pathNodes["department"].Add( "personallocationitems", new PathNode { EntityName = "PersonAllocation", PathName = "PersonAllocationItems" });
					_pathNodes["department"].Add( "sitechecklistitems", new PathNode { EntityName = "DepartmentChecklist", PathName = "SiteChecklistItems" });
					_pathNodes["department"].Add( "impactfrequencyperweekmonthviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekMonthView", PathName = "ImpactFrequencyPerWeekMonthViewItems" });
					_pathNodes["department"].Add( "driverlicenseexpirystoreprocedureitems", new PathNode { EntityName = "DriverLicenseExpiryStoreProcedure", PathName = "DriverLicenseExpiryStoreProcedureItems" });
					_pathNodes["department"].Add( "dashboardvehiclecardviewitems", new PathNode { EntityName = "DashboardVehicleCardView", PathName = "DashboardVehicleCardViewItems" });
					_pathNodes["department"].Add( "todaysimpactviewitems", new PathNode { EntityName = "TodaysImpactView", PathName = "TodaysImpactViewItems" });
					_pathNodes["department"].Add( "persontodepartmentvehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToDepartmentVehicleMasterAccessView", PathName = "PersonToDepartmentVehicleMasterAccessViewItems" });
					_pathNodes["department"].Add( "vehicles", new PathNode { EntityName = "Vehicle", PathName = "Vehicles" });
					_pathNodes["department"].Add( "loggedhoursversusseathoursviewitems", new PathNode { EntityName = "LoggedHoursVersusSeatHoursView", PathName = "LoggedHoursVersusSeatHoursViewItems" });
					_pathNodes["department"].Add( "dashboarddrivercardstoreprocedureitems", new PathNode { EntityName = "DashboardDriverCardStoreProcedure", PathName = "DashboardDriverCardStoreProcedureItems" });
					_pathNodes["department"].Add( "dashboardcardviewitems", new PathNode { EntityName = "DashboardDriverCardView", PathName = "DashboardCardViewItems" });
					_pathNodes.Add ( "machineunlockreportfilter", new Dictionary<string,PathNode>());

					_pathNodes["machineunlockreportfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["machineunlockreportfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["machineunlockreportfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "pervehiclemastercardaccess", new Dictionary<string,PathNode>());

					_pathNodes["pervehiclemastercardaccess"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					_pathNodes["pervehiclemastercardaccess"].Add( "sitevehiclenormalcardaccess", new PathNode { EntityName = "SiteVehicleNormalCardAccess", PathName = "SiteVehicleNormalCardAccess" });
					_pathNodes["pervehiclemastercardaccess"].Add( "modelvehiclemastercardaccess", new PathNode { EntityName = "ModelVehicleMasterCardAccess", PathName = "ModelVehicleMasterCardAccess" });
					_pathNodes["pervehiclemastercardaccess"].Add( "departmentvehiclemastercardaccess", new PathNode { EntityName = "DepartmentVehicleMasterCardAccess", PathName = "DepartmentVehicleMasterCardAccess" });
					_pathNodes["pervehiclemastercardaccess"].Add( "card1", new PathNode { EntityName = "Card", PathName = "Card1" });
					_pathNodes.Add ( "featuresubscriptionsfilter", new Dictionary<string,PathNode>());

					_pathNodes["featuresubscriptionsfilter"].Add( "department", new PathNode { EntityName = "Department", PathName = "Department" });
					_pathNodes["featuresubscriptionsfilter"].Add( "dealer", new PathNode { EntityName = "Dealer", PathName = "Dealer" });
					_pathNodes["featuresubscriptionsfilter"].Add( "site", new PathNode { EntityName = "Site", PathName = "Site" });
					_pathNodes["featuresubscriptionsfilter"].Add( "customer", new PathNode { EntityName = "Customer", PathName = "Customer" });
					_pathNodes.Add ( "ondemandsettings", new Dictionary<string,PathNode>());

					_pathNodes["ondemandsettings"].Add( "vehicle", new PathNode { EntityName = "Vehicle", PathName = "Vehicle" });
					}
				}

                return _pathNodes;
            }
        }

		private static Dictionary<string,string> _entityRelations = new Dictionary<string,string>()
        {
          { "checklistdetail", "PreOperationalChecklist, ChecklistResults" },
          { "slamcoreawareauthenticationdetails", "SlamcoreDevice" },
          { "persontomodelvehiclemasteraccessview", "Model, Person" },
          { "vehiclelockout", "AllVehicleUnlocksViewItems, Session, Driver, Vehicle, GOUser" },
          { "revision", "SnapshotItems, GOUser, TagItems" },
          { "alert", "AlertSubscriptions" },
          { "websiteuser", "Person, WebsiteRoles" },
          { "alllicenseexpiryview", "Dealer, Driver" },
          { "driverlicenseexpiryview", "Department, Site, Dealer, Customer" },
          { "contactpersoninformation", "Customer" },
          { "checklistsettings", "Vehicle" },
          { "vehiclediagnostic", "Vehicle" },
          { "vehiclegps", "Vehicle, Session" },
          { "module", "Vehicle, Dealer, ModuleHistoryItems" },
          { "persontopervehiclemasteraccessview", "Person, Vehicle" },
          { "accessgroup", "AccessGroupsToSites, Customer, PersonItems" },
          { "checkliststatusview", "Site, Customer, Department, Dealer" },
          { "currentstatuscombinedview", "CurrentStatusDriverViewItems, CurrentStatusVehicleViewItems" },
          { "currentvehiclestatuschartview", "Customer, Department, Site, Dealer" },
          { "dealerfeaturesubscription", "Dealer" },
          { "messagehistory", "GOUser, Vehicle, AllMessageHistoryStoreProcedureItems" },
          { "accessgrouptosite", "Site, AccessGroup" },
          { "alldriveraccessabusestoreprocedure", "Dealer, Driver" },
          { "customertomodel", "Customer, Model" },
          { "allemailsubscriptionstoreprocedure", "ReportSubscription" },
          { "dashboardvehiclecardstoreprocedure", "Site, Dealer, Department, Customer" },
          { "region", "Dealers" },
          { "model", "Vehicles, SiteChecklists, CustomerModelItems, VehiclesPerModelReportItems, Dealer, PersonToModelVehicleNormalAccessViewItems, CustomerToModelItems, ModelVehicleNormalCardAccessItems, ModelVehicleMasterCardAccessItems, LicensesByModel, PersonToModelVehicleMasterAccessViewItems" },
          { "allvorsessionspervehiclestoreprocedure", "Vehicle, VORReportCombinedView" },
          { "slamcoredeviceconnectionview", "SlamcoreDevice" },
          { "allimpactsview", "Dealer, Impact" },
          { "customer", "DriverItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, CustomerAudit, VehicleUtilizationLastTwelveHoursViewItems, DashboardCardViewItems, ChecklistStatusViewItems, Country, ContactPersonInformation, EmailGroupsItems, DashboardDriverCardStoreProcedureItems, Sites, Dealer, TodaysPreopCheckStoreProcedureItems, DashboardFilterItems, CustomerPreOperationalChecklistTemplateItems, CustomerToModelItems, ImpactFrequencyPerWeekMonthViewItems, CustomerFeatureSubscription, CustomerSSODetailItems, SlamcoreDeviceItems, TodaysPreopCheckViewItems, CustomerToPersonViewItems, TodaysImpactStoreProcedureItems, DriverLicenseExpiryViewItems, AccessGroupItems, CustomerModelItems, ImpactFrequencyPerTimeSlotViewItems, ImpactFrequencyPerWeekDayViewItems, CustomerSnapshotItems, LoggedHoursVersusSeatHoursViewItems, DepartmentItems, DriverLicenseExpiryStoreProcedureItems, GoUserToCustomerItems, IncompletedChecklistViewItems, CurrentVehicleStatusChartViewItems, VehicleItems, CurrentDriverStatusChartViewItems, DashboardVehicleCardStoreProcedureItems, DashboardVehicleCardViewItems, TodaysImpactViewItems, PersonItems" },
          { "departmentchecklist", "Model, PreOperationalChecklists, Department" },
          { "modulehistory", "Vehicle, Module" },
          { "country", "Customers" },
          { "allchecklistresultview", "Dealer, ChecklistResult" },
          { "gpshistory", "Session" },
          { "impactsforvehicleview", "Vehicle, Impact, Driver" },
          { "allvehicleunlocksview", "VehicleLockout, Dealer" },
          { "slamcorepedestriandetection", "SlamcoreDevice" },
          { "impactfrequencyperweekmonthview", "Dealer, Department, Customer, Site" },
          { "vehicleutilizationlasttwelvehoursview", "Department, Customer, Dealer, Site" },
          { "iofield", "PSTATDetailsItems, SessionDetailsItems" },
          { "licencedetail", "Driver" },
          { "allvehiclecalibrationstoreprocedure", "Vehicle" },
          { "cardtocardaccess", "Card, CardAccessLevel" },
          { "zonecoordinates", "FloorZones" },
          { "snapshot", "Revision" },
          { "departmentvehiclenormalcardaccess", "Card, Department, CardAccessLevel" },
          { "customertopersonview", "Customer, EmailGroupsToPersonItems, Person" },
          { "vehicleslamcorelocationhistory", "SlamcoreDevice" },
          { "permission", "CardToCardAccessItems" },
          { "sessiondetails", "IOFIELD, Session" },
          { "gorole", "GOUserItems, GroupRoleItems, UserRoleItems" },
          { "generalproductivitypervehicleview", "Vehicle, GeneralProductivityView, Dealer" },
          { "vehicle", "Module, VehicleToPreOpCheckilstItems, CurrentStatusVehicleViewItems, PedestrianDetectionHistoryItems, ChecklistSettings, DetailedVORSessionStoreProcedureItems, VehicleHireDehireHistoryItems, PersonToPerVehicleMasterAccessViewItems, VehicleSupervisorsViewItems, GeneralProductivityPerVehicleViewItems, DetailedSessionViewItems, VehicleCardAccesses, Inspection, Canrule, PersonToPerVehicleNormalAccessViewItems, Firmware, MessageHistoryItems, VORSettingHistoryItems, VehicleGPSLocations, VehicleLockoutItems, VehicleBroadcastMessageItems, DepartmentChecklist, Sessions, Department, UnitUnutilisationStoreProcedureItems, Site, OnDemandSettings, SlamcoreDeviceHistoryItems, Model, AllVORSessionsPerVehicleStoreProcedureItems, Driver, VehicleLastGPSLocationView, Customer, NetworkSettingsItems, VehicleAlertSubscriptionItems, Person, ImpactsForVehicleViewItems, ChecklistFailurePerVechicleViewItems, VehicleProficiencyViewItems, VehicleOtherSettings, AllVehicleCalibrationStoreProcedureItems, VehicleDiagnostic, VehicleSessionlessImpactItems, BroadcastMessageHistoryItems, OnDemandSessionItems, UnitUtilisationStoreProcedureItems, ServiceSettings, ModuleHistoryItems, PerVehicleNormalCardAccessItems" },
          { "generalproductivityperdriverviewlatest", "GeneralProductivityView, Driver, Dealer" },
          { "driver", "VehicleLockouts, BroadcastMessageHistoryItems, ImpactsForVehicleViewItems, Person, LicensesByModel, AllLicenseExpiryViewItems, DetailedSessionViewItems, Customer, AllDriverAccessAbuseStoreProcedureItems, OnDemandSessionItems, Card, Department, PedestrianDetectionHistoryItems, GeneralLicence, CurrentStatusDriverViewItems, GeneralProductivityPerDriverViewLatestItems, DriverProficiencyViewItems, Site, Sessions" },
          { "emailgroupstoperson", "EmailGroups, Person, CustomerToPersonView" },
          { "gogrouprole", "Group, Role" },
          { "modelvehiclemastercardaccess", "PerVehicleMasterCardAccessItems, Model, Card" },
          { "vehicleproficiencyview", "Vehicle, Dealer, ProficiencyCombinedView" },
          { "dashboarddrivercardview", "Site, Department, Customer, Dealer" },
          { "driverproficiencyview", "ProficiencyCombinedView, Dealer, Driver" },
          { "alerthistory", "Vehicle, Driver, Alert" },
          { "dashboardfilter", "Site, Customer, Department" },
          { "websiterole", "WebsiteUser" },
          { "gouserrole", "Role, User" },
          { "generalproductivityview", "GeneralProductivityPerVehicleViewItems, UnitUnutilisationStoreProcedureItems, UnitUtilisationStoreProcedureItems, GeneralProductivityPerDriverViewLatestItems" },
          { "inspection", "Vehicle" },
          { "impact", "Session, ImpactsForVehicleView, AllImpactsViewItems" },
          { "persontodepartmentvehiclemasteraccessview", "Department, Person" },
          { "currentstatusvehicleview", "Dealer, CurrentStatusCombinedView, Vehicle" },
          { "customermodel", "Model, Customer" },
          { "tag", "GOUser, Revision" },
          { "vehiclebroadcastmessage", "Vehicle, BroadcastMessage" },
          { "floorplan", "FloorZonesItems, SiteFloorPlanItems, Site" },
          { "todayspreopcheckview", "Site, Customer, Dealer, Department" },
          { "modelvehiclenormalcardaccess", "Card, CardAccessLevel, Department, Model" },
          { "sitevehiclenormalcardaccess", "CardAccessLevel, Site, Card, PerVehicleMasterCardAccessItems" },
          { "importjobstatus", "ImportJobBatch, ImportJobLogItems" },
          { "vehicleothersettings", "Vehicle" },
          { "vehiclesupervisorsview", "Vehicle, PerVehicleNormalCardAccess" },
          { "exportjobstatus", "GOUser" },
          { "timezone", "Sites" },
          { "loggedhoursversusseathoursview", "Customer, Site, Dealer, Department" },
          { "personallocation", "Site, Department, Person" },
          { "broadcastmessage", "VehicleBroadcastMessageItems" },
          { "vorreportcombinedview", "AllVORStatusStoreProcedureItems, AllVORSessionsPerVehicleStoreProcedureItems" },
          { "unitutilisationstoreprocedure", "GeneralProductivityView, Vehicle" },
          { "checklistresult", "Session, ChecklistAnswerDetails, AllChecklistResultViewItems" },
          { "unitunutilisationstoreprocedure", "Vehicle, GeneralProductivityView" },
          { "vehiclealertsubscription", "Vehicle, AlertSubscription" },
          { "ondemandauthorisationstoreprocedure", "OnDemandSession" },
          { "impactfrequencypertimeslotview", "Site, Dealer, Customer, Department" },
          { "persontositevehiclenormalaccessview", "CardAccessLevel, Person, Site" },
          { "session", "PSTATDetailsItems, VehicleLockouts, Vehicle, Driver, SessionDetailsItems, VehicleGPSItems, GPSHistoryItems, DetailedVORSessionStoreProcedureItems, Impacts, VehicleLastGPSLocationView, DetailedSessionViewItems, ChecklistResults" },
          { "dealer", "DealerFeatureSubscription, CurrentVehicleStatusChartViewItems, GeneralProductivityPerDriverViewLatestItems, TodaysImpactStoreProcedureItems, AllLicenseExpiryViewItems, AllVehicleUnlocksViewItems, ImpactFrequencyPerTimeSlotViewItems, ModuleItems, GOUserItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, TodaysImpactViewItems, DriverProficiencyViewItems, IncompletedChecklistViewItems, AllImpactsViewItems, ModelItems, AllDriverAccessAbuseStoreProcedureItems, DashboardDriverCardStoreProcedureItems, DashboardVehicleCardStoreProcedureItems, VehicleUtilizationLastTwelveHoursViewItems, DashboardCardViewItems, CurrentDriverStatusChartViewItems, DealerConfiguration, Customers, VehicleProficiencyViewItems, ChecklistStatusViewItems, DriverLicenseExpiryViewItems, LoggedHoursVersusSeatHoursViewItems, TodaysPreopCheckViewItems, CurrentStatusDriverViewItems, ImpactFrequencyPerWeekMonthViewItems, AllUserSummaryStoreProcedureItems, AllChecklistResultViewItems, TodaysPreopCheckStoreProcedureItems, CurrentStatusVehicleViewItems, GeneralProductivityPerVehicleViewItems, FeatureSubscriptionsFilterItems, DashboardVehicleCardViewItems, DriverLicenseExpiryStoreProcedureItems, Region, ImpactFrequencyPerWeekDayViewItems" },
          { "gouser", "UserGroupItems, UserRoleItems, CustomerAuditItemsCreated, DealerDriver, Person, CustomerAuditItemsDeleted, VehicleLockoutItems, MessageHistoryItems, RevisionItems, ExportJobStatusItems, ReportSubscriptionItems, AlertSubscriptionItems, CustomerAuditItemsModified, GOUserDepartmentItems, GORole, Tag, GoUserToCustomerItems, Dealer" },
          { "servicesettings", "Vehicle" },
          { "updatefirmwarerequest", "Firmware" },
          { "slamcoreapikey", "SlamcoreDevice" },
          { "card", "SiteVehicleNormalCardAccessItems, DepartmentVehicleMasterCardAccessItems, PerVehicleNormalCardAccessItems, CardToCardAccessItems, ModelVehicleMasterCardAccessItems, DealerDriver, DepartmentVehicleNormalCardAccessItems, PerVehicleMasterCardAccessItems, SiteVehicleMasterCardAccessItems, Driver, ModelVehicleNormalCardAccessItems" },
          { "gouserdepartment", "GOUser, Department" },
          { "canrule", "Vehicles, CanruleDetailsItems" },
          { "impactfrequencyperweekdayview", "Dealer, Site, Customer, Department" },
          { "slamcoredevicehistory", "SlamcoreDevice, Vehicle" },
          { "vehiclehiredehirehistory", "Vehicle, Department" },
          { "checklistfailurepervechicleview", "PreOperationalChecklist, Vehicle" },
          { "incompletedchecklistview", "Customer, Site, Department, Dealer" },
          { "reportsubscription", "GOUser, Department, Person, ReportType, EmailGroups, Site, Customer, AllEmailSubscriptionStoreProcedureItems" },
          { "vehiclelastgpslocationview", "Session, Vehicle" },
          { "pstatdetails", "Session, IOFIELD" },
          { "floorzones", "FloorPlan, ZoneCoordinatesItems" },
          { "canruledetails", "Canrule" },
          { "driverlicenseexpirystoreprocedure", "Dealer, Customer, Department, Site" },
          { "departmentvehiclemastercardaccess", "PerVehicleMasterCardAccessItems, Card, Department" },
          { "vehicleutilizationlasttwelvehoursstoreprocedure", "Customer, Department, Site, Dealer" },
          { "checklistfailureview", "PreOperationalChecklist" },
          { "personchecklistlanguagesettings", "Person" },
          { "vehiclesessionlessimpact", "Vehicle" },
          { "licensebymodel", "Driver, Model" },
          { "allmessagehistorystoreprocedure", "MessageHistory" },
          { "proficiencycombinedview", "VehicleProficiencyViewItems, DriverProficiencyViewItems" },
          { "customersnapshot", "CustomerBeingChangeTracked" },
          { "gogroup", "GroupRoleItems, UserGroupItems" },
          { "vehiclespermodelreport", "Model" },
          { "todaysimpactstoreprocedure", "Site, Dealer, Customer, Department" },
          { "dealerdriver", "Card, GOUser" },
          { "emailgroups", "EmailGroupsToPersonItems, Customer, ReportSubscriptions" },
          { "preoperationalchecklist", "ChecklistFailureView, VehicleToPreOpCheckilstItems, ChecklistAnswersDetails, ChecklistFailurePerVechicleViewItems, SiteChecklist" },
          { "slamcoredevice", "SlamcoreAPIKey, SlamcorePedestrianDetectionItems, SlamcoreAwareAuthenticationDetails, SlamcoreDeviceConnectionViewItems, VehicleSlamcoreLocationHistoryItems, SlamcoreDeviceHistoryItems, Vehicle, Customer" },
          { "site", "CurrentVehicleStatusChartViewItems, TodaysImpactStoreProcedureItems, ImpactFrequencyPerWeekMonthViewItems, SiteVehicleMasterCardAccessItems, ImpactFrequencyPerWeekDayViewItems, DepartmentItems, TodaysPreopCheckViewItems, CurrentDriverStatusChartViewItems, DashboardDriverCardStoreProcedureItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, DashboardFilterItems, DriverItems, SiteFloorPlanItems, DashboardVehicleCardStoreProcedureItems, ImpactFrequencyPerTimeSlotViewItems, AccessGroupToSiteItems, IncompletedChecklistViewItems, PersonItems, TodaysPreopCheckStoreProcedureItems, DashboardCardViewItems, PersonToSiteVehicleMasterAccessViewItems, Timezone, TodaysImpactViewItems, PersonAllocationItems, FloorPlanItems, Customer, PersonToSiteVehicleNormalAccessViewItems, VehicleItems, VehicleUtilizationLastTwelveHoursViewItems, SiteVehicleNormalCardAccessItems, LoggedHoursVersusSeatHoursViewItems, DriverLicenseExpiryViewItems, ChecklistStatusViewItems, DashboardVehicleCardViewItems, DriverLicenseExpiryStoreProcedureItems" },
          { "alertsubscription", "GOUser, Alert, Person, VehicleAlertSubscriptionItems" },
          { "customeraudit", "GOUserWhoDeletedThisCustomer, RevisionCreated, GOUserWhoModifiedThisCustomer, RevisionDeleted, Customer, RevisionLastModified, GOUserWhoCreatedThisCustomer" },
          { "pervehiclenormalcardaccess", "Card, Vehicle, CardAccessLevel, VehicleSupervisorsViewItems" },
          { "pedestriandetectionhistory", "Driver, Vehicle" },
          { "persontopervehiclenormalaccessview", "Person, CardAccessLevel, Vehicle" },
          { "dashboarddrivercardstoreprocedure", "Customer, Site, Department, Dealer" },
          { "todaysimpactview", "Department, Site, Customer, Dealer" },
          { "detailedsessionview", "Vehicle, Driver, Session" },
          { "importjoblog", "ImportJobStatus, ImportJobBatch" },
          { "ondemandsession", "Driver, OnDemandAuthorisationStoreProcedureItems, Vehicle" },
          { "allvorstatusstoreprocedure", "VORReportCombinedView, VORSettingHistory" },
          { "vorsettinghistory", "Vehicle, AllVORStatusStoreProcedureItems, Person" },
          { "allusersummarystoreprocedure", "Dealer, Person" },
          { "customerssodetail", "Customer" },
          { "departmenthoursettings", "Department" },
          { "dashboardvehiclecardview", "Dealer, Department, Site, Customer" },
          { "currentstatusdriverview", "Driver, CurrentStatusCombinedView, Dealer" },
          { "reporttype", "ReportSubscriptions" },
          { "sitevehiclemastercardaccess", "Site, Card" },
          { "persontodepartmentvehiclenormalaccessview", "Person, CardAccessLevel, Department" },
          { "gousergroup", "User, Group" },
          { "customerfeaturesubscription", "Customer" },
          { "vehicletopreopchecklistview", "PreOperationalChecklist, Vehicle" },
          { "persontositevehiclemasteraccessview", "Person, Site" },
          { "dealerconfiguration", "Dealer" },
          { "todayspreopcheckstoreprocedure", "Dealer, Site, Customer, Department" },
          { "customerpreoperationalchecklisttemplate", "Customer" },
          { "currentdriverstatuschartview", "Department, Site, Dealer, Customer" },
          { "sitefloorplan", "Site, FloorPlan" },
          { "broadcastmessagehistory", "Driver, Vehicle" },
          { "gousertocustomer", "GOUser, Customer" },
          { "importjobbatch", "ImportJobLogItems, ImportJobStatusItems" },
          { "person", "Site, Customer, PersonChecklistLanguageSettings, PersonAllocationItems, PersonToSiteVehicleNormalAccessViewItems, ReportSubscriptionItems, GOUser, AllUserSummaryStoreProcedureItems, AlertSubscriptions, VORSettingHistoryItems, PersonToDepartmentVehicleMasterAccessViewItems, Driver, PersonToPerVehicleNormalAccessViewItems, PersonToModelVehicleNormalAccessViewItems, AccessGroup, WebsiteUser, Department, PersonToModelVehicleMasterAccessViewItems, PersonToDepartmentVehicleNormalAccessViewItems, EmailGroupsToPersonItems, PersonToSiteVehicleMasterAccessViewItems, PersonToPerVehicleMasterAccessViewItems, CustomerToPersonViewItems, VehicleItems" },
          { "detailedvorsessionstoreprocedure", "Session, Vehicle" },
          { "persontomodelvehiclenormalaccessview", "Model, Department, Person, CardAccessLevel" },
          { "networksettings", "Vehicle" },
          { "department", "DashboardVehicleCardViewItems, DriverItems, CurrentDriverStatusChartViewItems, IncompletedChecklistViewItems, PersonToDepartmentVehicleNormalAccessViewItems, VehicleUtilizationLastTwelveHoursViewItems, DepartmentVehicleNormalCardAccessItems, DepartmentHourSettings, SiteChecklistItems, PersonToDepartmentVehicleMasterAccessViewItems, Vehicles, TodaysImpactViewItems, ImpactFrequencyPerTimeSlotViewItems, PersonAllocationItems, PersonToModelVehicleNormalAccessViewItems, DashboardDriverCardStoreProcedureItems, Site, ImpactFrequencyPerWeekDayViewItems, DashboardVehicleCardStoreProcedureItems, DriverLicenseExpiryViewItems, TodaysPreopCheckViewItems, ImpactFrequencyPerWeekMonthViewItems, VehicleHireDehireHistoryItems, TodaysImpactStoreProcedureItems, DriverLicenseExpiryStoreProcedureItems, ChecklistStatusViewItems, DashboardFilterItems, TodaysPreopCheckStoreProcedureItems, PersonItems, DashboardCardViewItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, Customer, CurrentVehicleStatusChartViewItems, LoggedHoursVersusSeatHoursViewItems, DepartmentVehicleMasterCardAccessItems" },
          { "pervehiclemastercardaccess", "DepartmentVehicleMasterCardAccess, Card1, ModelVehicleMasterCardAccess, Vehicle, SiteVehicleNormalCardAccess" },
          { "featuresubscriptionsfilter", "Dealer" },
          { "ondemandsettings", "Vehicle" },
      };

        private static Dictionary<string, string> EntityRelations 
		{
			get
			{
				return _entityRelations;
			}
		}
		
		private static List<string> DatabaseEntities = new List<string>()
        {
			"ChecklistDetail", "SlamcoreAwareAuthenticationDetails", "FeatureSubscriptionTemplate", "PersonToModelVehicleMasterAccessView", "VehicleLockout", "Revision", "Alert", "WebsiteUser", "DriverAccessAbuseFilter", "ContactPersonInformation", "ChecklistSettings", "VehicleDiagnostic", "GOUser2FA", "VehicleGPS", "Module", "PersonToPerVehicleMasterAccessView", "AccessGroup", "Help", "CurrentStatusCombinedView", "CurrentVehicleStatusChartView", "DealerFeatureSubscription", "MessageHistory", "AccessGroupToSite", "CustomerToModel", "Region", "Model", "SlamcoreDeviceConnectionView", "Customer", "DepartmentChecklist", "ModuleHistory", "Country", "GPSHistory", "ImpactsForVehicleView", "SlamcorePedestrianDetection", "IOFIELD", "Email", "LicenceDetail", "PedestrianDetectionHistoryFilter", "GOLoginHistory", "MainDashboardFilter", "AccessGroupTemplate", "CardToCardAccess", "ZoneCoordinates", "Snapshot", "GOTask", "GeneralProductivityReportFilter", "DepartmentVehicleNormalCardAccess", "CustomerToPersonView", "VehicleSlamcoreLocationHistory", "Permission", "SessionDetails", "Vehicle", "Driver", "EmailGroupsToPerson", "GOGroupRole", "ModelVehicleMasterCardAccess", "EmailSubscriptionReportFilter", "DashboardDriverCardView", "GOSecurityTokens", "AlertHistory", "DashboardFilter", "VORReportFilter", "OnDemandAuthorisationFilter", "WebsiteRole", "GOUserRole", "GeneralProductivityView", "Inspection", "Impact", "PersonToDepartmentVehicleMasterAccessView", "CurrentStatusVehicleView", "CustomerModel", "Tag", "VehicleBroadcastMessage", "FloorPlan", "ModelVehicleNormalCardAccess", "SiteVehicleNormalCardAccess", "ImportJobStatus", "VehicleOtherSettings", "DashboardFilterMoreFields", "VehicleSupervisorsView", "ExportJobStatus", "Timezone", "HireDeHireReportFilter", "PersonAllocation", "BroadcastMessage", "VORReportCombinedView", "ChecklistResult", "VehicleAlertSubscription", "PersonToSiteVehicleNormalAccessView", "Session", "SlamcoreDeviceFilter", "Dealer", "GOUser", "ServiceSettings", "UpdateFirmwareRequest", "SlamcoreAPIKey", "Firmware", "Card", "GOUserDepartment", "Canrule", "SlamcoreDeviceHistory", "VehicleHireDehireHistory", "ChecklistFailurePerVechicleView", "ImpactReportFilter", "ReportSubscription", "VehicleLastGPSLocationView", "PSTATDetails", "LicenseExpiryReportFilter", "FloorZones", "CanruleDetails", "VehicleHireDehireSynchronizationOptions", "DepartmentVehicleMasterCardAccess", "ChecklistFailureView", "PersonChecklistLanguageSettings", "VehicleSessionlessImpact", "LicenseByModel", "IoTDeviceMessageCache", "ProficiencyCombinedView", "CustomerSnapshot", "GOGroup", "VehiclesPerModelReport", "BroadcastMessageHistoryFilter", "DealerDriver", "EmailGroups", "PreOperationalChecklist", "SynchronizationStatusReportFilter", "SlamcoreDevice", "Site", "AlertSubscription", "CustomerAudit", "PerVehicleNormalCardAccess", "PedestrianDetectionHistory", "PersonToPerVehicleNormalAccessView", "ImportJobLog", "ProficiencyReportFilter", "OnDemandSession", "VORSettingHistory", "CustomerSSODetail", "UploadLogoRequest", "AllVehicleCalibrationFilter", "DepartmentHourSettings", "CategoryTemplate", "DashboardVehicleCardView", "CurrentStatusDriverView", "ReportType", "SiteVehicleMasterCardAccess", "PersonToDepartmentVehicleNormalAccessView", "GOUserGroup", "CustomerFeatureSubscription", "VehicleToPreOpChecklistView", "PersonToSiteVehicleMasterAccessView", "DealerConfiguration", "CustomerPreOperationalChecklistTemplate", "CurrentDriverStatusChartView", "GOChangeDelta", "PreOpReportFilter", "SiteFloorPlan", "BroadcastMessageHistory", "GoUserToCustomer", "ImportJobBatch", "Person", "PersonToModelVehicleNormalAccessView", "NetworkSettings", "Department", "MachineUnlockReportFilter", "PerVehicleMasterCardAccess", "UnitUtilisationCombinedView", "FeatureSubscriptionsFilter", "OnDemandSettings" 
        };

		private static Dictionary<string, string> EntityNames = new Dictionary<string, string> { { "accessgroup", "AccessGroup" }, { "accessgrouptemplate", "AccessGroupTemplate" }, { "accessgrouptosite", "AccessGroupToSite" }, { "alert", "Alert" }, { "alerthistory", "AlertHistory" }, { "alertsubscription", "AlertSubscription" }, { "alertsummarystoreprocedure", "AlertSummaryStoreProcedure" }, { "allchecklistresultview", "AllChecklistResultView" }, { "alldriveraccessabusestoreprocedure", "AllDriverAccessAbuseStoreProcedure" }, { "allemailsubscriptionstoreprocedure", "AllEmailSubscriptionStoreProcedure" }, { "allimpactsview", "AllImpactsView" }, { "alllicenseexpiryview", "AllLicenseExpiryView" }, { "allmessagehistorystoreprocedure", "AllMessageHistoryStoreProcedure" }, { "allusersummarystoreprocedure", "AllUserSummaryStoreProcedure" }, { "allvehiclecalibrationfilter", "AllVehicleCalibrationFilter" }, { "allvehiclecalibrationstoreprocedure", "AllVehicleCalibrationStoreProcedure" }, { "allvehicleunlocksview", "AllVehicleUnlocksView" }, { "allvorsessionspervehiclestoreprocedure", "AllVORSessionsPerVehicleStoreProcedure" }, { "allvorstatusstoreprocedure", "AllVORStatusStoreProcedure" }, { "broadcastmessage", "BroadcastMessage" }, { "broadcastmessagehistory", "BroadcastMessageHistory" }, { "broadcastmessagehistoryfilter", "BroadcastMessageHistoryFilter" }, { "canrule", "Canrule" }, { "canruledetails", "CanruleDetails" }, { "card", "Card" }, { "cardtocardaccess", "CardToCardAccess" }, { "categorytemplate", "CategoryTemplate" }, { "checklistdetail", "ChecklistDetail" }, { "checklistfailurepervechicleview", "ChecklistFailurePerVechicleView" }, { "checklistfailureview", "ChecklistFailureView" }, { "checklistresult", "ChecklistResult" }, { "checklistsettings", "ChecklistSettings" }, { "checkliststatusview", "ChecklistStatusView" }, { "contactpersoninformation", "ContactPersonInformation" }, { "country", "Country" }, { "currentdriverstatuschartview", "CurrentDriverStatusChartView" }, { "currentstatuscombinedview", "CurrentStatusCombinedView" }, { "currentstatusdriverview", "CurrentStatusDriverView" }, { "currentstatusvehicleview", "CurrentStatusVehicleView" }, { "currentvehiclestatuschartview", "CurrentVehicleStatusChartView" }, { "customer", "Customer" }, { "customeraudit", "CustomerAudit" }, { "customerfeaturesubscription", "CustomerFeatureSubscription" }, { "customermodel", "CustomerModel" }, { "customerpreoperationalchecklisttemplate", "CustomerPreOperationalChecklistTemplate" }, { "customersnapshot", "CustomerSnapshot" }, { "customerssodetail", "CustomerSSODetail" }, { "customertomodel", "CustomerToModel" }, { "customertopersonview", "CustomerToPersonView" }, { "dashboarddrivercardstoreprocedure", "DashboardDriverCardStoreProcedure" }, { "dashboarddrivercardview", "DashboardDriverCardView" }, { "dashboardfilter", "DashboardFilter" }, { "dashboardfiltermorefields", "DashboardFilterMoreFields" }, { "dashboardvehiclecardstoreprocedure", "DashboardVehicleCardStoreProcedure" }, { "dashboardvehiclecardview", "DashboardVehicleCardView" }, { "dealer", "Dealer" }, { "dealerconfiguration", "DealerConfiguration" }, { "dealerdriver", "DealerDriver" }, { "dealerfeaturesubscription", "DealerFeatureSubscription" }, { "department", "Department" }, { "departmentchecklist", "DepartmentChecklist" }, { "departmenthoursettings", "DepartmentHourSettings" }, { "departmentvehiclemastercardaccess", "DepartmentVehicleMasterCardAccess" }, { "departmentvehiclenormalcardaccess", "DepartmentVehicleNormalCardAccess" }, { "detailedsessionview", "DetailedSessionView" }, { "detailedvorsessionstoreprocedure", "DetailedVORSessionStoreProcedure" }, { "driver", "Driver" }, { "driveraccessabusefilter", "DriverAccessAbuseFilter" }, { "driverlicenseexpirystoreprocedure", "DriverLicenseExpiryStoreProcedure" }, { "driverlicenseexpiryview", "DriverLicenseExpiryView" }, { "driverproficiencyview", "DriverProficiencyView" }, { "email", "Email" }, { "emailgroups", "EmailGroups" }, { "emailgroupstoperson", "EmailGroupsToPerson" }, { "emailsubscriptionreportfilter", "EmailSubscriptionReportFilter" }, { "exportjobstatus", "ExportJobStatus" }, { "featuresubscriptionsfilter", "FeatureSubscriptionsFilter" }, { "featuresubscriptiontemplate", "FeatureSubscriptionTemplate" }, { "firmware", "Firmware" }, { "floorplan", "FloorPlan" }, { "floorzones", "FloorZones" }, { "generalproductivityperdriverviewlatest", "GeneralProductivityPerDriverViewLatest" }, { "generalproductivitypervehicleview", "GeneralProductivityPerVehicleView" }, { "generalproductivityreportfilter", "GeneralProductivityReportFilter" }, { "generalproductivityview", "GeneralProductivityView" }, { "go2faconfiguration", "GO2FAConfiguration" }, { "gochangedelta", "GOChangeDelta" }, { "gogroup", "GOGroup" }, { "gogrouprole", "GOGroupRole" }, { "gologinhistory", "GOLoginHistory" }, { "gorole", "GORole" }, { "gosecuritytokens", "GOSecurityTokens" }, { "gotask", "GOTask" }, { "gouser", "GOUser" }, { "gouser2fa", "GOUser2FA" }, { "gouserdepartment", "GOUserDepartment" }, { "gousergroup", "GOUserGroup" }, { "gouserrole", "GOUserRole" }, { "gousertocustomer", "GoUserToCustomer" }, { "gpshistory", "GPSHistory" }, { "help", "Help" }, { "hiredehirereportfilter", "HireDeHireReportFilter" }, { "impact", "Impact" }, { "impactfrequencypertimeslotview", "ImpactFrequencyPerTimeSlotView" }, { "impactfrequencyperweekdayview", "ImpactFrequencyPerWeekDayView" }, { "impactfrequencyperweekmonthview", "ImpactFrequencyPerWeekMonthView" }, { "impactreportfilter", "ImpactReportFilter" }, { "impactsforvehicleview", "ImpactsForVehicleView" }, { "importjobbatch", "ImportJobBatch" }, { "importjoblog", "ImportJobLog" }, { "importjobstatus", "ImportJobStatus" }, { "incompletedchecklistview", "IncompletedChecklistView" }, { "inspection", "Inspection" }, { "iofield", "IOFIELD" }, { "iotdevicemessagecache", "IoTDeviceMessageCache" }, { "licencedetail", "LicenceDetail" }, { "licensebymodel", "LicenseByModel" }, { "licenseexpiryreportfilter", "LicenseExpiryReportFilter" }, { "loggedhoursversusseathoursview", "LoggedHoursVersusSeatHoursView" }, { "machineunlockreportfilter", "MachineUnlockReportFilter" }, { "maindashboardfilter", "MainDashboardFilter" }, { "messagehistory", "MessageHistory" }, { "model", "Model" }, { "modelvehiclemastercardaccess", "ModelVehicleMasterCardAccess" }, { "modelvehiclenormalcardaccess", "ModelVehicleNormalCardAccess" }, { "module", "Module" }, { "modulehistory", "ModuleHistory" }, { "networksettings", "NetworkSettings" }, { "ondemandauthorisationfilter", "OnDemandAuthorisationFilter" }, { "ondemandauthorisationstoreprocedure", "OnDemandAuthorisationStoreProcedure" }, { "ondemandsession", "OnDemandSession" }, { "ondemandsettings", "OnDemandSettings" }, { "pedestriandetectionhistory", "PedestrianDetectionHistory" }, { "pedestriandetectionhistoryfilter", "PedestrianDetectionHistoryFilter" }, { "permission", "Permission" }, { "person", "Person" }, { "personallocation", "PersonAllocation" }, { "personchecklistlanguagesettings", "PersonChecklistLanguageSettings" }, { "persontodepartmentvehiclemasteraccessview", "PersonToDepartmentVehicleMasterAccessView" }, { "persontodepartmentvehiclenormalaccessview", "PersonToDepartmentVehicleNormalAccessView" }, { "persontomodelvehiclemasteraccessview", "PersonToModelVehicleMasterAccessView" }, { "persontomodelvehiclenormalaccessview", "PersonToModelVehicleNormalAccessView" }, { "persontopervehiclemasteraccessview", "PersonToPerVehicleMasterAccessView" }, { "persontopervehiclenormalaccessview", "PersonToPerVehicleNormalAccessView" }, { "persontositevehiclemasteraccessview", "PersonToSiteVehicleMasterAccessView" }, { "persontositevehiclenormalaccessview", "PersonToSiteVehicleNormalAccessView" }, { "pervehiclemastercardaccess", "PerVehicleMasterCardAccess" }, { "pervehiclenormalcardaccess", "PerVehicleNormalCardAccess" }, { "preoperationalchecklist", "PreOperationalChecklist" }, { "preopreportfilter", "PreOpReportFilter" }, { "proficiencycombinedview", "ProficiencyCombinedView" }, { "proficiencyreportfilter", "ProficiencyReportFilter" }, { "pstatdetails", "PSTATDetails" }, { "region", "Region" }, { "reportsubscription", "ReportSubscription" }, { "reporttype", "ReportType" }, { "revision", "Revision" }, { "servicesettings", "ServiceSettings" }, { "session", "Session" }, { "sessiondetails", "SessionDetails" }, { "site", "Site" }, { "sitefloorplan", "SiteFloorPlan" }, { "sitevehiclemastercardaccess", "SiteVehicleMasterCardAccess" }, { "sitevehiclenormalcardaccess", "SiteVehicleNormalCardAccess" }, { "slamcoreapikey", "SlamcoreAPIKey" }, { "slamcoreawareauthenticationdetails", "SlamcoreAwareAuthenticationDetails" }, { "slamcoredevice", "SlamcoreDevice" }, { "slamcoredeviceconnectionview", "SlamcoreDeviceConnectionView" }, { "slamcoredevicefilter", "SlamcoreDeviceFilter" }, { "slamcoredevicehistory", "SlamcoreDeviceHistory" }, { "slamcorepedestriandetection", "SlamcorePedestrianDetection" }, { "snapshot", "Snapshot" }, { "synchronizationstatusreportfilter", "SynchronizationStatusReportFilter" }, { "tag", "Tag" }, { "timezone", "Timezone" }, { "todaysimpactstoreprocedure", "TodaysImpactStoreProcedure" }, { "todaysimpactview", "TodaysImpactView" }, { "todayspreopcheckstoreprocedure", "TodaysPreopCheckStoreProcedure" }, { "todayspreopcheckview", "TodaysPreopCheckView" }, { "unitsummaryreport", "UnitSummaryReport" }, { "unitsummarystoreprocedure", "UnitSummaryStoreProcedure" }, { "unitunutilisationstoreprocedure", "UnitUnutilisationStoreProcedure" }, { "unitutilisationcombinedview", "UnitUtilisationCombinedView" }, { "unitutilisationstoreprocedure", "UnitUtilisationStoreProcedure" }, { "updatefirmwarerequest", "UpdateFirmwareRequest" }, { "uploadlogorequest", "UploadLogoRequest" }, { "vehicle", "Vehicle" }, { "vehiclealertsubscription", "VehicleAlertSubscription" }, { "vehiclebroadcastmessage", "VehicleBroadcastMessage" }, { "vehiclediagnostic", "VehicleDiagnostic" }, { "vehiclegps", "VehicleGPS" }, { "vehiclehiredehirehistory", "VehicleHireDehireHistory" }, { "vehiclehiredehiresynchronizationoptions", "VehicleHireDehireSynchronizationOptions" }, { "vehiclelastgpslocationview", "VehicleLastGPSLocationView" }, { "vehiclelockout", "VehicleLockout" }, { "vehicleothersettings", "VehicleOtherSettings" }, { "vehicleproficiencyview", "VehicleProficiencyView" }, { "vehiclesessionlessimpact", "VehicleSessionlessImpact" }, { "vehicleslamcorelocationhistory", "VehicleSlamcoreLocationHistory" }, { "vehiclespermodelreport", "VehiclesPerModelReport" }, { "vehiclesupervisorsview", "VehicleSupervisorsView" }, { "vehicletopreopchecklistview", "VehicleToPreOpChecklistView" }, { "vehicleutilizationlasttwelvehoursstoreprocedure", "VehicleUtilizationLastTwelveHoursStoreProcedure" }, { "vehicleutilizationlasttwelvehoursview", "VehicleUtilizationLastTwelveHoursView" }, { "vorreportcombinedview", "VORReportCombinedView" }, { "vorreportfilter", "VORReportFilter" }, { "vorsettinghistory", "VORSettingHistory" }, { "websiterole", "WebsiteRole" }, { "websiteuser", "WebsiteUser" }, { "zonecoordinates", "ZoneCoordinates" } };

        private static PathNode GetPathNode(string entityName, string relationName)
        {
            bool hierarchicalRelation = relationName.Contains(":");
            entityName = hierarchicalRelation ? relationName.Split(':')[0].ToLower() : entityName.ToLower(); 
            //TODO : test concreteEntityName is sub type of entityName
            relationName = hierarchicalRelation ? relationName.Split(':')[1].ToLower() : relationName.ToLower(); 

            if (!PathNodes.ContainsKey(entityName))
                throw new ArgumentException(String.Format("{0} entity has no relation named {1}. Possible relations are : {2}", entityName, relationName, "none"));

            if (!PathNodes[entityName].ContainsKey(relationName))
                throw new ArgumentException(String.Format("{0} entity has no relation named {1}. Possible relations are : {2}", entityName, relationName, EntityRelations[entityName]));

            var result = PathNodes[entityName][relationName];
            result.ParentEntityName = EntityNames[entityName];

            return result;
        }

        public static List<IPathEdge> ConstructPrefetchPath(string entityname, List<string> includeList)
        {
			if (includeList == null) 
            {
				return null;
            }

			var unhandledIncludes = new List<string>();

            var pathNodeGraph = new PathNode { EntityName = entityname, ParentEntityName = entityname, PathName = null, PathNodes = new List<PathNode>() };
            
            foreach (var include in includeList)
            {
                var currentEntity = entityname;
                var currentPathNodeList = pathNodeGraph.PathNodes;
				int count = 0;
				var includeparts = include.Split('.');

                foreach (var includepart in includeparts)
                {
                    var pathNode = GetPathNode(currentEntity, includepart);

                    if (!DatabaseEntities.Contains(pathNode.EntityName))
                    {
                        // not a entity with database datasource => prefetch path cannot be covered    

                        //HACK GO-756: we only now how to dispatch unhandled prefetch paths of level 0 ... should be possible at any level. Workaround if at level > 0 : we say the full include is not covered
                        // which mean some data will be retrieved more than once from database
                        
                        string unhandledInclude = String.Join(".", includeparts.Skip(/*HACK : should skip count* / 0));
                        unhandledIncludes.Add(unhandledInclude);
                        break;
                    }

                    if (currentPathNodeList.Where(n => n.EntityName == pathNode.EntityName && n.PathName == pathNode.PathName).Any())
                    {
                        currentPathNodeList = currentPathNodeList.Where(n => n.EntityName == pathNode.EntityName && n.PathName == pathNode.PathName).Single().PathNodes;
                    }
                    else
                    {
                        pathNode.PathNodes = new List<PathNode>();
                        currentPathNodeList.Add(pathNode);
                        currentPathNodeList = pathNode.PathNodes;
                    }
                    
                    currentEntity = pathNode.EntityName;
                }

            }
                
            includeList.Clear();
            includeList.AddRange(unhandledIncludes);

            return ConstructPrefetchPath(pathNodeGraph);
        }


        private static List<IPathEdge> ConstructPrefetchPath(PathNode pathNodeGraph)
        {
            var pathEdges = new List<IPathEdge>();

            {
                foreach (var pathNode in pathNodeGraph.PathNodes)
                {
                    var nonDatabaseData = false; //TODO
                    // Ignore nonDatabaseData data prefetch 
                    if (nonDatabaseData) continue;

                    var pathEdge = CreatePathEdge(pathNode.ParentEntityName, pathNode.EntityName, pathNode.PathName, ConstructPrefetchPath(pathNode));
                    pathEdges.Add(pathEdge);
                }
            }
            
            return pathEdges;        
        }


        private static IPathEdge CreatePathEdge(string rootEntityName, string prefetchEntityName, string path, List<IPathEdge> pathEdges) 
        {
            var prefetchEntityType = (EntityType)(Enum.Parse(typeof(EntityType),String.Concat(prefetchEntityName,"Entity")));
            var typeInfo = Type.GetType("FleetXQ.Data.EntityClasses." + rootEntityName + "Entity, FleetXQ.Data");
            var prefetchPathPropertyInfo = typeInfo.GetProperty(String.Concat("PrefetchPath", path), BindingFlags.FlattenHierarchy | BindingFlags.Public | BindingFlags.Static);
            var prefetchPath = (IPrefetchPathElementCore)prefetchPathPropertyInfo.GetValue(null, null);

            switch (prefetchEntityType)
            {
				case EntityType.AccessGroupEntity:
					return new PathEdge<AccessGroupEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AccessGroupTemplateEntity:
					return new PathEdge<AccessGroupTemplateEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AccessGroupToSiteEntity:
					return new PathEdge<AccessGroupToSiteEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AlertEntity:
					return new PathEdge<AlertEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AlertHistoryEntity:
					return new PathEdge<AlertHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AlertSubscriptionEntity:
					return new PathEdge<AlertSubscriptionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AlertSummaryStoreProcedureEntity:
					return new PathEdge<AlertSummaryStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllChecklistResultViewEntity:
					return new PathEdge<AllChecklistResultViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllDriverAccessAbuseStoreProcedureEntity:
					return new PathEdge<AllDriverAccessAbuseStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllEmailSubscriptionStoreProcedureEntity:
					return new PathEdge<AllEmailSubscriptionStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllImpactsViewEntity:
					return new PathEdge<AllImpactsViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllLicenseExpiryViewEntity:
					return new PathEdge<AllLicenseExpiryViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllMessageHistoryStoreProcedureEntity:
					return new PathEdge<AllMessageHistoryStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllUserSummaryStoreProcedureEntity:
					return new PathEdge<AllUserSummaryStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllVehicleCalibrationFilterEntity:
					return new PathEdge<AllVehicleCalibrationFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllVehicleCalibrationStoreProcedureEntity:
					return new PathEdge<AllVehicleCalibrationStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllVehicleUnlocksViewEntity:
					return new PathEdge<AllVehicleUnlocksViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllVORSessionsPerVehicleStoreProcedureEntity:
					return new PathEdge<AllVORSessionsPerVehicleStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.AllVORStatusStoreProcedureEntity:
					return new PathEdge<AllVORStatusStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.BroadcastMessageEntity:
					return new PathEdge<BroadcastMessageEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.BroadcastMessageHistoryEntity:
					return new PathEdge<BroadcastMessageHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.BroadcastMessageHistoryFilterEntity:
					return new PathEdge<BroadcastMessageHistoryFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CanruleEntity:
					return new PathEdge<CanruleEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CanruleDetailsEntity:
					return new PathEdge<CanruleDetailsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CardEntity:
					return new PathEdge<CardEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CardToCardAccessEntity:
					return new PathEdge<CardToCardAccessEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CategoryTemplateEntity:
					return new PathEdge<CategoryTemplateEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ChecklistDetailEntity:
					return new PathEdge<ChecklistDetailEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ChecklistFailurePerVechicleViewEntity:
					return new PathEdge<ChecklistFailurePerVechicleViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ChecklistFailureViewEntity:
					return new PathEdge<ChecklistFailureViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ChecklistResultEntity:
					return new PathEdge<ChecklistResultEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ChecklistSettingsEntity:
					return new PathEdge<ChecklistSettingsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ChecklistStatusViewEntity:
					return new PathEdge<ChecklistStatusViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ContactPersonInformationEntity:
					return new PathEdge<ContactPersonInformationEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CountryEntity:
					return new PathEdge<CountryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CurrentDriverStatusChartViewEntity:
					return new PathEdge<CurrentDriverStatusChartViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CurrentStatusCombinedViewEntity:
					return new PathEdge<CurrentStatusCombinedViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CurrentStatusDriverViewEntity:
					return new PathEdge<CurrentStatusDriverViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CurrentStatusVehicleViewEntity:
					return new PathEdge<CurrentStatusVehicleViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CurrentVehicleStatusChartViewEntity:
					return new PathEdge<CurrentVehicleStatusChartViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CustomerEntity:
					return new PathEdge<CustomerEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CustomerFeatureSubscriptionEntity:
					return new PathEdge<CustomerFeatureSubscriptionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CustomerModelEntity:
					return new PathEdge<CustomerModelEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CustomerPreOperationalChecklistTemplateEntity:
					return new PathEdge<CustomerPreOperationalChecklistTemplateEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CustomerSSODetailEntity:
					return new PathEdge<CustomerSSODetailEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CustomerToModelEntity:
					return new PathEdge<CustomerToModelEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CustomerToPersonViewEntity:
					return new PathEdge<CustomerToPersonViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DashboardDriverCardStoreProcedureEntity:
					return new PathEdge<DashboardDriverCardStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DashboardDriverCardViewEntity:
					return new PathEdge<DashboardDriverCardViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DashboardFilterEntity:
					return new PathEdge<DashboardFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DashboardFilterMoreFieldsEntity:
					return new PathEdge<DashboardFilterMoreFieldsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DashboardVehicleCardStoreProcedureEntity:
					return new PathEdge<DashboardVehicleCardStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DashboardVehicleCardViewEntity:
					return new PathEdge<DashboardVehicleCardViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DealerEntity:
					return new PathEdge<DealerEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DealerConfigurationEntity:
					return new PathEdge<DealerConfigurationEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DealerDriverEntity:
					return new PathEdge<DealerDriverEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DealerFeatureSubscriptionEntity:
					return new PathEdge<DealerFeatureSubscriptionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DepartmentEntity:
					return new PathEdge<DepartmentEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DepartmentChecklistEntity:
					return new PathEdge<DepartmentChecklistEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DepartmentHourSettingsEntity:
					return new PathEdge<DepartmentHourSettingsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DepartmentVehicleMasterCardAccessEntity:
					return new PathEdge<DepartmentVehicleMasterCardAccessEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DepartmentVehicleNormalCardAccessEntity:
					return new PathEdge<DepartmentVehicleNormalCardAccessEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DetailedSessionViewEntity:
					return new PathEdge<DetailedSessionViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DetailedVORSessionStoreProcedureEntity:
					return new PathEdge<DetailedVORSessionStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DriverEntity:
					return new PathEdge<DriverEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DriverAccessAbuseFilterEntity:
					return new PathEdge<DriverAccessAbuseFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DriverLicenseExpiryStoreProcedureEntity:
					return new PathEdge<DriverLicenseExpiryStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DriverLicenseExpiryViewEntity:
					return new PathEdge<DriverLicenseExpiryViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.DriverProficiencyViewEntity:
					return new PathEdge<DriverProficiencyViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.EmailEntity:
					return new PathEdge<EmailEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.EmailGroupsEntity:
					return new PathEdge<EmailGroupsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.EmailGroupsToPersonEntity:
					return new PathEdge<EmailGroupsToPersonEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.EmailSubscriptionReportFilterEntity:
					return new PathEdge<EmailSubscriptionReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.FeatureSubscriptionsFilterEntity:
					return new PathEdge<FeatureSubscriptionsFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.FeatureSubscriptionTemplateEntity:
					return new PathEdge<FeatureSubscriptionTemplateEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.FirmwareEntity:
					return new PathEdge<FirmwareEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.FloorPlanEntity:
					return new PathEdge<FloorPlanEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.FloorZonesEntity:
					return new PathEdge<FloorZonesEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GeneralProductivityPerDriverViewLatestEntity:
					return new PathEdge<GeneralProductivityPerDriverViewLatestEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GeneralProductivityPerVehicleViewEntity:
					return new PathEdge<GeneralProductivityPerVehicleViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GeneralProductivityReportFilterEntity:
					return new PathEdge<GeneralProductivityReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GeneralProductivityViewEntity:
					return new PathEdge<GeneralProductivityViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CustomerAuditEntity:
					return new PathEdge<CustomerAuditEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.CustomerSnapshotEntity:
					return new PathEdge<CustomerSnapshotEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOChangeDeltaEntity:
					return new PathEdge<GOChangeDeltaEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.RevisionEntity:
					return new PathEdge<RevisionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SnapshotEntity:
					return new PathEdge<SnapshotEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.TagEntity:
					return new PathEdge<TagEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GO2FAConfigurationEntity:
					return new PathEdge<GO2FAConfigurationEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOGroupEntity:
					return new PathEdge<GOGroupEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOGroupRoleEntity:
					return new PathEdge<GOGroupRoleEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOLoginHistoryEntity:
					return new PathEdge<GOLoginHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GORoleEntity:
					return new PathEdge<GORoleEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOUserEntity:
					return new PathEdge<GOUserEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOUser2FAEntity:
					return new PathEdge<GOUser2FAEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOUserGroupEntity:
					return new PathEdge<GOUserGroupEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOUserRoleEntity:
					return new PathEdge<GOUserRoleEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOSecurityTokensEntity:
					return new PathEdge<GOSecurityTokensEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOTaskEntity:
					return new PathEdge<GOTaskEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GOUserDepartmentEntity:
					return new PathEdge<GOUserDepartmentEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GoUserToCustomerEntity:
					return new PathEdge<GoUserToCustomerEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.GPSHistoryEntity:
					return new PathEdge<GPSHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.HelpEntity:
					return new PathEdge<HelpEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.HireDeHireReportFilterEntity:
					return new PathEdge<HireDeHireReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ImpactEntity:
					return new PathEdge<ImpactEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ImpactFrequencyPerTimeSlotViewEntity:
					return new PathEdge<ImpactFrequencyPerTimeSlotViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ImpactFrequencyPerWeekDayViewEntity:
					return new PathEdge<ImpactFrequencyPerWeekDayViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ImpactFrequencyPerWeekMonthViewEntity:
					return new PathEdge<ImpactFrequencyPerWeekMonthViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ImpactReportFilterEntity:
					return new PathEdge<ImpactReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ImpactsForVehicleViewEntity:
					return new PathEdge<ImpactsForVehicleViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ExportJobStatusEntity:
					return new PathEdge<ExportJobStatusEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ImportJobBatchEntity:
					return new PathEdge<ImportJobBatchEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ImportJobLogEntity:
					return new PathEdge<ImportJobLogEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ImportJobStatusEntity:
					return new PathEdge<ImportJobStatusEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.IncompletedChecklistViewEntity:
					return new PathEdge<IncompletedChecklistViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.InspectionEntity:
					return new PathEdge<InspectionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.IOFIELDEntity:
					return new PathEdge<IOFIELDEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.IoTDeviceMessageCacheEntity:
					return new PathEdge<IoTDeviceMessageCacheEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.LicenceDetailEntity:
					return new PathEdge<LicenceDetailEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.LicenseByModelEntity:
					return new PathEdge<LicenseByModelEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.LicenseExpiryReportFilterEntity:
					return new PathEdge<LicenseExpiryReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.LoggedHoursVersusSeatHoursViewEntity:
					return new PathEdge<LoggedHoursVersusSeatHoursViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.MachineUnlockReportFilterEntity:
					return new PathEdge<MachineUnlockReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.MainDashboardFilterEntity:
					return new PathEdge<MainDashboardFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.MessageHistoryEntity:
					return new PathEdge<MessageHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ModelEntity:
					return new PathEdge<ModelEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ModelVehicleMasterCardAccessEntity:
					return new PathEdge<ModelVehicleMasterCardAccessEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ModelVehicleNormalCardAccessEntity:
					return new PathEdge<ModelVehicleNormalCardAccessEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ModuleEntity:
					return new PathEdge<ModuleEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ModuleHistoryEntity:
					return new PathEdge<ModuleHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.NetworkSettingsEntity:
					return new PathEdge<NetworkSettingsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.OnDemandAuthorisationFilterEntity:
					return new PathEdge<OnDemandAuthorisationFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.OnDemandAuthorisationStoreProcedureEntity:
					return new PathEdge<OnDemandAuthorisationStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.OnDemandSessionEntity:
					return new PathEdge<OnDemandSessionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.OnDemandSettingsEntity:
					return new PathEdge<OnDemandSettingsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PedestrianDetectionHistoryEntity:
					return new PathEdge<PedestrianDetectionHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PedestrianDetectionHistoryFilterEntity:
					return new PathEdge<PedestrianDetectionHistoryFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PermissionEntity:
					return new PathEdge<PermissionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonEntity:
					return new PathEdge<PersonEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonAllocationEntity:
					return new PathEdge<PersonAllocationEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonChecklistLanguageSettingsEntity:
					return new PathEdge<PersonChecklistLanguageSettingsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonToDepartmentVehicleMasterAccessViewEntity:
					return new PathEdge<PersonToDepartmentVehicleMasterAccessViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonToDepartmentVehicleNormalAccessViewEntity:
					return new PathEdge<PersonToDepartmentVehicleNormalAccessViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonToModelVehicleMasterAccessViewEntity:
					return new PathEdge<PersonToModelVehicleMasterAccessViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonToModelVehicleNormalAccessViewEntity:
					return new PathEdge<PersonToModelVehicleNormalAccessViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonToPerVehicleMasterAccessViewEntity:
					return new PathEdge<PersonToPerVehicleMasterAccessViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonToPerVehicleNormalAccessViewEntity:
					return new PathEdge<PersonToPerVehicleNormalAccessViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonToSiteVehicleMasterAccessViewEntity:
					return new PathEdge<PersonToSiteVehicleMasterAccessViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PersonToSiteVehicleNormalAccessViewEntity:
					return new PathEdge<PersonToSiteVehicleNormalAccessViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PerVehicleMasterCardAccessEntity:
					return new PathEdge<PerVehicleMasterCardAccessEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PerVehicleNormalCardAccessEntity:
					return new PathEdge<PerVehicleNormalCardAccessEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PreOperationalChecklistEntity:
					return new PathEdge<PreOperationalChecklistEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PreOpReportFilterEntity:
					return new PathEdge<PreOpReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ProficiencyCombinedViewEntity:
					return new PathEdge<ProficiencyCombinedViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ProficiencyReportFilterEntity:
					return new PathEdge<ProficiencyReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.PSTATDetailsEntity:
					return new PathEdge<PSTATDetailsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.RegionEntity:
					return new PathEdge<RegionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ReportSubscriptionEntity:
					return new PathEdge<ReportSubscriptionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ReportTypeEntity:
					return new PathEdge<ReportTypeEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ServiceSettingsEntity:
					return new PathEdge<ServiceSettingsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SessionEntity:
					return new PathEdge<SessionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SessionDetailsEntity:
					return new PathEdge<SessionDetailsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SiteEntity:
					return new PathEdge<SiteEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SiteFloorPlanEntity:
					return new PathEdge<SiteFloorPlanEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SiteVehicleMasterCardAccessEntity:
					return new PathEdge<SiteVehicleMasterCardAccessEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SiteVehicleNormalCardAccessEntity:
					return new PathEdge<SiteVehicleNormalCardAccessEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SlamcoreAPIKeyEntity:
					return new PathEdge<SlamcoreAPIKeyEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SlamcoreAwareAuthenticationDetailsEntity:
					return new PathEdge<SlamcoreAwareAuthenticationDetailsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SlamcoreDeviceEntity:
					return new PathEdge<SlamcoreDeviceEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SlamcoreDeviceConnectionViewEntity:
					return new PathEdge<SlamcoreDeviceConnectionViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SlamcoreDeviceFilterEntity:
					return new PathEdge<SlamcoreDeviceFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SlamcoreDeviceHistoryEntity:
					return new PathEdge<SlamcoreDeviceHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SlamcorePedestrianDetectionEntity:
					return new PathEdge<SlamcorePedestrianDetectionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.SynchronizationStatusReportFilterEntity:
					return new PathEdge<SynchronizationStatusReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.TimezoneEntity:
					return new PathEdge<TimezoneEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.TodaysImpactStoreProcedureEntity:
					return new PathEdge<TodaysImpactStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.TodaysImpactViewEntity:
					return new PathEdge<TodaysImpactViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.TodaysPreopCheckStoreProcedureEntity:
					return new PathEdge<TodaysPreopCheckStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.TodaysPreopCheckViewEntity:
					return new PathEdge<TodaysPreopCheckViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.UnitSummaryReportEntity:
					return new PathEdge<UnitSummaryReportEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.UnitSummaryStoreProcedureEntity:
					return new PathEdge<UnitSummaryStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.UnitUnutilisationStoreProcedureEntity:
					return new PathEdge<UnitUnutilisationStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.UnitUtilisationCombinedViewEntity:
					return new PathEdge<UnitUtilisationCombinedViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.UnitUtilisationStoreProcedureEntity:
					return new PathEdge<UnitUtilisationStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.UpdateFirmwareRequestEntity:
					return new PathEdge<UpdateFirmwareRequestEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.UploadLogoRequestEntity:
					return new PathEdge<UploadLogoRequestEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleEntity:
					return new PathEdge<VehicleEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleAlertSubscriptionEntity:
					return new PathEdge<VehicleAlertSubscriptionEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleBroadcastMessageEntity:
					return new PathEdge<VehicleBroadcastMessageEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleDiagnosticEntity:
					return new PathEdge<VehicleDiagnosticEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleGPSEntity:
					return new PathEdge<VehicleGPSEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleHireDehireHistoryEntity:
					return new PathEdge<VehicleHireDehireHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleHireDehireSynchronizationOptionsEntity:
					return new PathEdge<VehicleHireDehireSynchronizationOptionsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleLastGPSLocationViewEntity:
					return new PathEdge<VehicleLastGPSLocationViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleLockoutEntity:
					return new PathEdge<VehicleLockoutEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleOtherSettingsEntity:
					return new PathEdge<VehicleOtherSettingsEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleProficiencyViewEntity:
					return new PathEdge<VehicleProficiencyViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleSessionlessImpactEntity:
					return new PathEdge<VehicleSessionlessImpactEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleSlamcoreLocationHistoryEntity:
					return new PathEdge<VehicleSlamcoreLocationHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehiclesPerModelReportEntity:
					return new PathEdge<VehiclesPerModelReportEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleSupervisorsViewEntity:
					return new PathEdge<VehicleSupervisorsViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleToPreOpChecklistViewEntity:
					return new PathEdge<VehicleToPreOpChecklistViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleUtilizationLastTwelveHoursStoreProcedureEntity:
					return new PathEdge<VehicleUtilizationLastTwelveHoursStoreProcedureEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VehicleUtilizationLastTwelveHoursViewEntity:
					return new PathEdge<VehicleUtilizationLastTwelveHoursViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VORReportCombinedViewEntity:
					return new PathEdge<VORReportCombinedViewEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VORReportFilterEntity:
					return new PathEdge<VORReportFilterEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.VORSettingHistoryEntity:
					return new PathEdge<VORSettingHistoryEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.WebsiteRoleEntity:
					return new PathEdge<WebsiteRoleEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.WebsiteUserEntity:
					return new PathEdge<WebsiteUserEntity>(prefetchPath, pathEdges.ToArray());
				case EntityType.ZoneCoordinatesEntity:
					return new PathEdge<ZoneCoordinatesEntity>(prefetchPath, pathEdges.ToArray());
            }
			
			return null;
		}
	}
}*/