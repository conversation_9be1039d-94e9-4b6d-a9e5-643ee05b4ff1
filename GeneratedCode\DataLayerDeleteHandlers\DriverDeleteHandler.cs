﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;

using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DeleteHandlers
{
	public partial class DriverDeleteHandler : DeleteHandlerBase<DriverDataObject>
	{
		
        public DriverDeleteHandler(IServiceProvider serviceProvider, IDataProvider<DriverDataObject> tentityDataProvider, ITransactionProvider transactionProvider, IEntityDataProvider entityDataProvider) : base(serviceProvider, tentityDataProvider, transactionProvider, entityDataProvider)
        {
        }

		public override async Task RippleDeleteAsync(DriverDataObject instance, Parameters parameters, DataProviderDeleteSettings settings)
		{
			// Set resync flag initially so that if any processing is done, it's on the latest copy of the data
			NeedResync = true;

			// Unidrectional backwards reference from AlertHistory.Driver (ProtectedBackReference)
			{
				instance = await ResyncAsync(instance);				
				var db = _serviceProvider.GetRequiredService<IDataProvider<AlertHistoryDataObject>>();
				var links = await db.GetCollectionAsync(null, "DriverId == @0", new object[] { instance.Id }, parameters: parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteBackReference", instance, links);
			}
			// Driver.BroadcastMessageHistoryItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadBroadcastMessageHistoryItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.BroadcastMessageHistoryItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// Driver.Card (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadCardAsync(parameters, skipSecurity: true);
				var item = instance.Card;
				
	
				// Clear instance.Card reference so that item can be deleted from the db without FK constraint blocking it
				// i.e. We are the FK side and need to clear our reference to PK side so that PK side not blocked by us
				if (item != null)
				{
					instance.Card = null;
					await SaveAsync(instance);
			
					await DeleteAsync(item, parameters, settings, instance);
				}
			}
			// Driver.GeneralLicence (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadGeneralLicenceAsync(parameters, skipSecurity: true);
				var item = instance.GeneralLicence;
				
	
				// Clear instance.GeneralLicence reference so that item can be deleted from the db without FK constraint blocking it
				// i.e. We are the FK side and need to clear our reference to PK side so that PK side not blocked by us
				if (item != null)
				{
					instance.GeneralLicence = null;
					await SaveAsync(instance);
			
					await DeleteAsync(item, parameters, settings, instance);
				}
			}
			// Driver.LicensesByModel (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadLicensesByModelAsync(parameters, skipSecurity: true);
				foreach (var item in instance.LicensesByModel)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// Driver.OnDemandSessionItems (Protected) (i.e. Unable to delete Driver instances because OnDemandSessionItems.Driver is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadOnDemandSessionItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.OnDemandSessionItems);
			}
			// Driver.PedestrianDetectionHistoryItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadPedestrianDetectionHistoryItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.PedestrianDetectionHistoryItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// Driver.Person (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadPersonAsync(parameters, skipSecurity: true);
				
				// We are the PK side entity in a OneToOne relationship, and the FK reference to us is optional.
				// So delete can proceed, but first we need to clear down the FK reference to us.
				var item = instance.Person;				
				if (item != null && item.Driver != null)
				{
					item.Driver = null; 
					await SaveAsync(item);
				}
			}
			// Driver.Sessions (Protected) (i.e. Unable to delete Driver instances because Sessions.Driver is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSessionsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.Sessions);
			}
			// Unidrectional backwards reference from Vehicle.Driver (BackReference)
			{
				instance = await ResyncAsync(instance);				
				var db = _serviceProvider.GetRequiredService<IDataProvider<VehicleDataObject>>();
				var links = await db.GetCollectionAsync(null, "DriverId == @0", new object[] { instance.Id }, parameters: parameters, skipSecurity: true);
				if (links.Any())
				{
					// instance is optional from point of view of the linked Vehicles => we're allowed to clear down the references to allow the delete to proceed
					foreach (var item in links)
					{
						item.DriverId = null;
						await SaveAsync(item);
					}
				}
			}
			// Driver.VehicleLockouts (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleLockoutsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.VehicleLockouts)			
				{					
					if (item.Driver != null)
					{	
						item.Driver = null; 	
						await SaveAsync(item);							
					}
				}
			}
		}
	}
}