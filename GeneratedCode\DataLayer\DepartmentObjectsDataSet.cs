﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class DepartmentObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _departmentObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all Department objects for current dataset
		private ConcurrentDictionary< int, DepartmentDataObject> _departmentObjects = new ConcurrentDictionary< int, DepartmentDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<DepartmentDataObject> _mergedDataObjects;

		private ConcurrentQueue<DepartmentDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<DepartmentDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> DepartmentObjectInternalIds
		{ 
			get { return _departmentObjectInternalIds; }
			set { _departmentObjectInternalIds = value; }
		}
		
		// Collection holding all Department objects for current dataset
		[JsonProperty("DepartmentObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, DepartmentDataObject> DepartmentObjects
		{ 
			get { return _departmentObjects; }
			set { _departmentObjects = value; }
		}
		
		
 
		
 
		
 
		
 
		
 
		
 
		// Index to quickly find all Department with a given customer foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Customer_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		// Index to quickly find all Department with a given departmentHourSettings foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> DepartmentHourSettings_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		// Index to quickly find all Department with a given site foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Site_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public DepartmentObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public DepartmentObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<DepartmentObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.DepartmentObjects)
			{
                var cloneObject = (DepartmentDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.DepartmentObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.DepartmentObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.DepartmentObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			foreach(var fkKeyValue in this.Customer_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Customer_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Customer_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.DepartmentHourSettings_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "DepartmentObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.DepartmentHourSettings_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.DepartmentHourSettings_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Site_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Site_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Site_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<DepartmentObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.DepartmentObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (DepartmentDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.DepartmentObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.DepartmentObjectInternalIds
				.Where(o => this.DepartmentObjects[o.Value].IsDirty || this.DepartmentObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.DepartmentObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var department in DepartmentObjects.Values)
			{
				yield return department; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the DepartmentObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "DepartmentObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = DepartmentObjects.TryAdd(newInternalId, (DepartmentDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (DepartmentObjectInternalIds.ContainsKey(((DepartmentDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = DepartmentObjectInternalIds.TryRemove(((DepartmentDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = DepartmentObjectInternalIds.TryAdd(((DepartmentDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as DepartmentDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "DepartmentDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

	 
	 
	 
	 
	 
	 
			// Update the Customer FK Index 
			if (!Customer_FKIndex.ContainsKey((objectToAdd as DepartmentDataObject).CustomerId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Customer_FKIndex.TryAdd((objectToAdd as DepartmentDataObject).CustomerId, new List<int>());
				}
			}
				
			if (!Customer_FKIndex[(objectToAdd as DepartmentDataObject).CustomerId].Contains(newInternalId))
				Customer_FKIndex[(objectToAdd as DepartmentDataObject).CustomerId].Add(newInternalId);

            CustomerDataObject relatedCustomer;
            if ((objectToAdd as DepartmentDataObject)._customer_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as DepartmentDataObject)._customer_NewObjectId;

	            relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToAdd as DepartmentDataObject).CustomerId) { IsNew = false });
            }

			if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
                relatedCustomer.NotifyPropertyChanged("DepartmentItems", new SeenObjectCollection());
			
	 
	 
	 
	 
	 
	 
			// Update the DepartmentHourSettings FK Index 
			if ((objectToAdd as DepartmentDataObject).DepartmentHourSettingsId != null)
			{
				if (!DepartmentHourSettings_FKIndex.ContainsKey((System.Guid)(objectToAdd as DepartmentDataObject).DepartmentHourSettingsId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = DepartmentHourSettings_FKIndex.TryAdd((System.Guid)(objectToAdd as DepartmentDataObject).DepartmentHourSettingsId, new List<int>());
					}
				}
				
				if (!DepartmentHourSettings_FKIndex[(System.Guid)(objectToAdd as DepartmentDataObject).DepartmentHourSettingsId].Contains(newInternalId))	
					DepartmentHourSettings_FKIndex[(System.Guid)(objectToAdd as DepartmentDataObject).DepartmentHourSettingsId].Add(newInternalId);

	            DepartmentHourSettingsDataObject relatedDepartmentHourSettings;
	            if ((objectToAdd as DepartmentDataObject)._departmentHourSettings_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<DepartmentHourSettingsDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as DepartmentDataObject)._departmentHourSettings_NewObjectId;

	                relatedDepartmentHourSettings = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedDepartmentHourSettings = _rootObjectDataSet.GetObject(new DepartmentHourSettingsDataObject((System.Guid)(objectToAdd as DepartmentDataObject).DepartmentHourSettingsId) { IsNew = false });
	            }

	            if (relatedDepartmentHourSettings != null && this.RootObjectDataSet.NotifyChanges)
	                relatedDepartmentHourSettings.NotifyPropertyChanged("Department", new SeenObjectCollection());
			}
			
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
			// Update the Site FK Index 
			if (!Site_FKIndex.ContainsKey((objectToAdd as DepartmentDataObject).SiteId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Site_FKIndex.TryAdd((objectToAdd as DepartmentDataObject).SiteId, new List<int>());
				}
			}
				
			if (!Site_FKIndex[(objectToAdd as DepartmentDataObject).SiteId].Contains(newInternalId))
				Site_FKIndex[(objectToAdd as DepartmentDataObject).SiteId].Add(newInternalId);

            SiteDataObject relatedSite;
            if ((objectToAdd as DepartmentDataObject)._site_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<SiteDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as DepartmentDataObject)._site_NewObjectId;

	            relatedSite = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedSite = _rootObjectDataSet.GetObject(new SiteDataObject((objectToAdd as DepartmentDataObject).SiteId) { IsNew = false });
            }

			if (relatedSite != null && this.RootObjectDataSet.NotifyChanges)
                relatedSite.NotifyPropertyChanged("DepartmentItems", new SeenObjectCollection());
			
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (DepartmentObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as DepartmentDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "DepartmentObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = DepartmentObjectInternalIds.ContainsKey((objectToRemove as DepartmentDataObject).PrimaryKey) ? (int?) DepartmentObjectInternalIds[(objectToRemove as DepartmentDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				DepartmentDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = DepartmentObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = DepartmentObjectInternalIds.TryRemove((objectToRemove as DepartmentDataObject).PrimaryKey, out idvalue);
					}
				}
				
		 
		 
		 
		 
		 
		 
			// Delete the Customer FK Index 
				if (Customer_FKIndex.ContainsKey((objectToRemove as DepartmentDataObject).CustomerId) && Customer_FKIndex[(objectToRemove as DepartmentDataObject).CustomerId].Contains((int)objectToRemoveInternalId))
				{
					Customer_FKIndex[(objectToRemove as DepartmentDataObject).CustomerId].Remove((int)objectToRemoveInternalId);

					if (!Customer_FKIndex[(objectToRemove as DepartmentDataObject).CustomerId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Customer_FKIndex.TryRemove((objectToRemove as DepartmentDataObject).CustomerId, out outvalue);
						}
					}
				}
				
				CustomerDataObject relatedCustomer;
	            if ((objectToRemove as DepartmentDataObject)._customer_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as DepartmentDataObject)._customer_NewObjectId;

					relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToRemove as DepartmentDataObject).CustomerId) { IsNew = false });
	            }

	            if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
	                relatedCustomer.NotifyPropertyChanged("DepartmentItems", new SeenObjectCollection());
				
		 
		 
		 
		 
		 
		 
			// Delete the DepartmentHourSettings FK Index 
				if ((objectToRemove as DepartmentDataObject).DepartmentHourSettingsId != null)
				{
					if (DepartmentHourSettings_FKIndex.ContainsKey((System.Guid)(objectToRemove as DepartmentDataObject).DepartmentHourSettingsId) && DepartmentHourSettings_FKIndex[(System.Guid)(objectToRemove as DepartmentDataObject).DepartmentHourSettingsId].Contains((int)objectToRemoveInternalId))
					{
						DepartmentHourSettings_FKIndex[(System.Guid)(objectToRemove as DepartmentDataObject).DepartmentHourSettingsId].Remove((int)objectToRemoveInternalId);

						if (!DepartmentHourSettings_FKIndex[(System.Guid)(objectToRemove as DepartmentDataObject).DepartmentHourSettingsId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = DepartmentHourSettings_FKIndex.TryRemove((System.Guid)(objectToRemove as DepartmentDataObject).DepartmentHourSettingsId, out outvalue);
							}
						}
					}

					DepartmentHourSettingsDataObject relatedDepartmentHourSettings;
		            if ((objectToRemove as DepartmentDataObject)._departmentHourSettings_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<DepartmentHourSettingsDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as DepartmentDataObject)._departmentHourSettings_NewObjectId;

						relatedDepartmentHourSettings = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedDepartmentHourSettings = _rootObjectDataSet.GetObject(new DepartmentHourSettingsDataObject((System.Guid)(objectToRemove as DepartmentDataObject).DepartmentHourSettingsId) { IsNew = false });
		            }

		            if (relatedDepartmentHourSettings != null && this.RootObjectDataSet.NotifyChanges)
		                relatedDepartmentHourSettings.NotifyPropertyChanged("Department", new SeenObjectCollection());
					
				}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			// Delete the Site FK Index 
				if (Site_FKIndex.ContainsKey((objectToRemove as DepartmentDataObject).SiteId) && Site_FKIndex[(objectToRemove as DepartmentDataObject).SiteId].Contains((int)objectToRemoveInternalId))
				{
					Site_FKIndex[(objectToRemove as DepartmentDataObject).SiteId].Remove((int)objectToRemoveInternalId);

					if (!Site_FKIndex[(objectToRemove as DepartmentDataObject).SiteId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Site_FKIndex.TryRemove((objectToRemove as DepartmentDataObject).SiteId, out outvalue);
						}
					}
				}
				
				SiteDataObject relatedSite;
	            if ((objectToRemove as DepartmentDataObject)._site_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<SiteDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as DepartmentDataObject)._site_NewObjectId;

					relatedSite = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedSite = _rootObjectDataSet.GetObject(new SiteDataObject((objectToRemove as DepartmentDataObject).SiteId) { IsNew = false });
	            }

	            if (relatedSite != null && this.RootObjectDataSet.NotifyChanges)
	                relatedSite.NotifyPropertyChanged("DepartmentItems", new SeenObjectCollection());
				
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return DepartmentObjects.ContainsKey(internalObjectId) ? DepartmentObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as DepartmentDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "DepartmentObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = DepartmentObjectInternalIds.ContainsKey((objectToGet as DepartmentDataObject).PrimaryKey) ? (int?) DepartmentObjectInternalIds[(objectToGet as DepartmentDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return DepartmentObjects.ContainsKey((int)objectToGetInternalId) ? DepartmentObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return DepartmentObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return DepartmentObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		 
		 
		 
		
		public IEnumerable<DepartmentDataObject> GetDepartmentItemsForCustomer(CustomerDataObject customerInstance) 
		{
			if (customerInstance.IsNew)
            {
			
              return DepartmentObjects.Where(o => o.Value._customer_NewObjectId != null && o.Value._customer_NewObjectId == customerInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Customer_FKIndex.ContainsKey(customerInstance.Id))
			{
				return Customer_FKIndex[customerInstance.Id].Where(e => DepartmentObjects.ContainsKey(e)).Select(e => DepartmentObjects[e]);
			}
			
			return new DataObjectCollection<DepartmentDataObject>();
		}
		 
		 
		 
		 
		 
		 
		
		public IEnumerable<DepartmentDataObject> GetDepartmentForDepartmentHourSettings(DepartmentHourSettingsDataObject departmentHourSettingsInstance) 
		{
			if (departmentHourSettingsInstance.IsNew)
            {
			
              return DepartmentObjects.Where(o => o.Value._departmentHourSettings_NewObjectId != null && o.Value._departmentHourSettings_NewObjectId == departmentHourSettingsInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (DepartmentHourSettings_FKIndex.ContainsKey(departmentHourSettingsInstance.Id))
			{
				return DepartmentHourSettings_FKIndex[departmentHourSettingsInstance.Id].Where(e => DepartmentObjects.ContainsKey(e)).Select(e => DepartmentObjects[e]);
			}
			
			return new DataObjectCollection<DepartmentDataObject>();
		}
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		
		public IEnumerable<DepartmentDataObject> GetDepartmentItemsForSite(SiteDataObject siteInstance) 
		{
			if (siteInstance.IsNew)
            {
			
              return DepartmentObjects.Where(o => o.Value._site_NewObjectId != null && o.Value._site_NewObjectId == siteInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Site_FKIndex.ContainsKey(siteInstance.Id))
			{
				return Site_FKIndex[siteInstance.Id].Where(e => DepartmentObjects.ContainsKey(e)).Select(e => DepartmentObjects[e]);
			}
			
			return new DataObjectCollection<DepartmentDataObject>();
		}
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
			if (relationName == "ChecklistStatusViewItems")
            {
				IEnumerable< ChecklistStatusViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ChecklistStatusViewObjectsDataSet.GetChecklistStatusViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CurrentDriverStatusChartViewItems")
            {
				IEnumerable< CurrentDriverStatusChartViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CurrentDriverStatusChartViewObjectsDataSet.GetCurrentDriverStatusChartViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CurrentVehicleStatusChartViewItems")
            {
				IEnumerable< CurrentVehicleStatusChartViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CurrentVehicleStatusChartViewObjectsDataSet.GetCurrentVehicleStatusChartViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "DashboardCardViewItems")
            {
				IEnumerable< DashboardDriverCardViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardDriverCardViewObjectsDataSet.GetDashboardCardViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardDriverCardStoreProcedureItems")
            {
				IEnumerable< DashboardDriverCardStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardDriverCardStoreProcedureObjectsDataSet.GetDashboardDriverCardStoreProcedureItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardFilterItems")
            {
				IEnumerable< DashboardFilterDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject);
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.DriverAccessAbuseFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.PedestrianDetectionHistoryFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.MainDashboardFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.GeneralProductivityReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.EmailSubscriptionReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.VORReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.OnDemandAuthorisationFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.DashboardFilterMoreFieldsObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.HireDeHireReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.SlamcoreDeviceFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.ImpactReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.LicenseExpiryReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.BroadcastMessageHistoryFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.SynchronizationStatusReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.ProficiencyReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.AllVehicleCalibrationFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.PreOpReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.MachineUnlockReportFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.FeatureSubscriptionsFilterObjectsDataSet.GetDashboardFilterItemsForDepartment(rootObject as DepartmentDataObject).Cast<DashboardFilterDataObject>());
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardVehicleCardStoreProcedureItems")
            {
				IEnumerable< DashboardVehicleCardStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardVehicleCardStoreProcedureObjectsDataSet.GetDashboardVehicleCardStoreProcedureItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardVehicleCardViewItems")
            {
				IEnumerable< DashboardVehicleCardViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardVehicleCardViewObjectsDataSet.GetDashboardVehicleCardViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "DepartmentVehicleMasterCardAccessItems")
            {
				IEnumerable< DepartmentVehicleMasterCardAccessDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DepartmentVehicleMasterCardAccessObjectsDataSet.GetDepartmentVehicleMasterCardAccessItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DepartmentVehicleNormalCardAccessItems")
            {
				IEnumerable< DepartmentVehicleNormalCardAccessDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DepartmentVehicleNormalCardAccessObjectsDataSet.GetDepartmentVehicleNormalCardAccessItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DriverItems")
            {
				IEnumerable< DriverDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DriverObjectsDataSet.GetDriverItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DriverLicenseExpiryStoreProcedureItems")
            {
				IEnumerable< DriverLicenseExpiryStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DriverLicenseExpiryStoreProcedureObjectsDataSet.GetDriverLicenseExpiryStoreProcedureItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DriverLicenseExpiryViewItems")
            {
				IEnumerable< DriverLicenseExpiryViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DriverLicenseExpiryViewObjectsDataSet.GetDriverLicenseExpiryViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactFrequencyPerTimeSlotViewItems")
            {
				IEnumerable< ImpactFrequencyPerTimeSlotViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactFrequencyPerTimeSlotViewObjectsDataSet.GetImpactFrequencyPerTimeSlotViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactFrequencyPerWeekDayViewItems")
            {
				IEnumerable< ImpactFrequencyPerWeekDayViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactFrequencyPerWeekDayViewObjectsDataSet.GetImpactFrequencyPerWeekDayViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactFrequencyPerWeekMonthViewItems")
            {
				IEnumerable< ImpactFrequencyPerWeekMonthViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactFrequencyPerWeekMonthViewObjectsDataSet.GetImpactFrequencyPerWeekMonthViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "IncompletedChecklistViewItems")
            {
				IEnumerable< IncompletedChecklistViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.IncompletedChecklistViewObjectsDataSet.GetIncompletedChecklistViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "LoggedHoursVersusSeatHoursViewItems")
            {
				IEnumerable< LoggedHoursVersusSeatHoursViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.LoggedHoursVersusSeatHoursViewObjectsDataSet.GetLoggedHoursVersusSeatHoursViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonAllocationItems")
            {
				IEnumerable< PersonAllocationDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonAllocationObjectsDataSet.GetPersonAllocationItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonItems")
            {
				IEnumerable< PersonDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonObjectsDataSet.GetPersonItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonToDepartmentVehicleMasterAccessViewItems")
            {
				IEnumerable< PersonToDepartmentVehicleMasterAccessViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonToDepartmentVehicleMasterAccessViewObjectsDataSet.GetPersonToDepartmentVehicleMasterAccessViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonToDepartmentVehicleNormalAccessViewItems")
            {
				IEnumerable< PersonToDepartmentVehicleNormalAccessViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonToDepartmentVehicleNormalAccessViewObjectsDataSet.GetPersonToDepartmentVehicleNormalAccessViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonToModelVehicleNormalAccessViewItems")
            {
				IEnumerable< PersonToModelVehicleNormalAccessViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonToModelVehicleNormalAccessViewObjectsDataSet.GetPersonToModelVehicleNormalAccessViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "SiteChecklistItems")
            {
				IEnumerable< DepartmentChecklistDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DepartmentChecklistObjectsDataSet.GetSiteChecklistItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysImpactStoreProcedureItems")
            {
				IEnumerable< TodaysImpactStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysImpactStoreProcedureObjectsDataSet.GetTodaysImpactStoreProcedureItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysImpactViewItems")
            {
				IEnumerable< TodaysImpactViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysImpactViewObjectsDataSet.GetTodaysImpactViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysPreopCheckStoreProcedureItems")
            {
				IEnumerable< TodaysPreopCheckStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysPreopCheckStoreProcedureObjectsDataSet.GetTodaysPreopCheckStoreProcedureItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysPreopCheckViewItems")
            {
				IEnumerable< TodaysPreopCheckViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysPreopCheckViewObjectsDataSet.GetTodaysPreopCheckViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleHireDehireHistoryItems")
            {
				IEnumerable< VehicleHireDehireHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleHireDehireHistoryObjectsDataSet.GetVehicleHireDehireHistoryItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "Vehicles")
            {
				IEnumerable< VehicleDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleObjectsDataSet.GetVehiclesForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleUtilizationLastTwelveHoursStoreProcedureItems")
            {
				IEnumerable< VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleUtilizationLastTwelveHoursStoreProcedureObjectsDataSet.GetVehicleUtilizationLastTwelveHoursStoreProcedureItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleUtilizationLastTwelveHoursViewItems")
            {
				IEnumerable< VehicleUtilizationLastTwelveHoursViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleUtilizationLastTwelveHoursViewObjectsDataSet.GetVehicleUtilizationLastTwelveHoursViewItemsForDepartment(rootObject as DepartmentDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var DepartmentDataSet = dataSetToMerge as DepartmentObjectsDataSet;
				if(DepartmentDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in DepartmentDataSet.DepartmentObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "DepartmentObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as DepartmentDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
		 
		 
		 
		 
		 
		 
			// Reconstruct the Customer FK Index 
			Customer_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in DepartmentObjects.Values)
			{
				if (item.CustomerId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.CustomerId;	

				if (!Customer_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Customer_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "DepartmentObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Customer_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
			// Reconstruct the DepartmentHourSettings FK Index 
			DepartmentHourSettings_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in DepartmentObjects.Values)
			{
				if (item.DepartmentHourSettingsId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.DepartmentHourSettingsId;	

				if (!DepartmentHourSettings_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = DepartmentHourSettings_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "DepartmentObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				DepartmentHourSettings_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			// Reconstruct the Site FK Index 
			Site_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in DepartmentObjects.Values)
			{
				if (item.SiteId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.SiteId;	

				if (!Site_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Site_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "DepartmentObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Site_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (DepartmentObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}