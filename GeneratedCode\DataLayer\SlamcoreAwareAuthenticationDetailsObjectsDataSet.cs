﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreAwareAuthenticationDetailsObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _slamcoreAwareAuthenticationDetailsObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all SlamcoreAwareAuthenticationDetails objects for current dataset
		private ConcurrentDictionary< int, SlamcoreAwareAuthenticationDetailsDataObject> _slamcoreAwareAuthenticationDetailsObjects = new ConcurrentDictionary< int, SlamcoreAwareAuthenticationDetailsDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<SlamcoreAwareAuthenticationDetailsDataObject> _mergedDataObjects;

		private ConcurrentQueue<SlamcoreAwareAuthenticationDetailsDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<SlamcoreAwareAuthenticationDetailsDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> SlamcoreAwareAuthenticationDetailsObjectInternalIds
		{ 
			get { return _slamcoreAwareAuthenticationDetailsObjectInternalIds; }
			set { _slamcoreAwareAuthenticationDetailsObjectInternalIds = value; }
		}
		
		// Collection holding all SlamcoreAwareAuthenticationDetails objects for current dataset
		[JsonProperty("SlamcoreAwareAuthenticationDetailsObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, SlamcoreAwareAuthenticationDetailsDataObject> SlamcoreAwareAuthenticationDetailsObjects
		{ 
			get { return _slamcoreAwareAuthenticationDetailsObjects; }
			set { _slamcoreAwareAuthenticationDetailsObjects = value; }
		}
		
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public SlamcoreAwareAuthenticationDetailsObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public SlamcoreAwareAuthenticationDetailsObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SlamcoreAwareAuthenticationDetailsObjects)
			{
                var cloneObject = (SlamcoreAwareAuthenticationDetailsDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcoreAwareAuthenticationDetailsObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SlamcoreAwareAuthenticationDetailsObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcoreAwareAuthenticationDetailsObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<SlamcoreAwareAuthenticationDetailsObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SlamcoreAwareAuthenticationDetailsObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (SlamcoreAwareAuthenticationDetailsDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.SlamcoreAwareAuthenticationDetailsObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SlamcoreAwareAuthenticationDetailsObjectInternalIds
				.Where(o => this.SlamcoreAwareAuthenticationDetailsObjects[o.Value].IsDirty || this.SlamcoreAwareAuthenticationDetailsObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcoreAwareAuthenticationDetailsObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var slamcoreAwareAuthenticationDetails in SlamcoreAwareAuthenticationDetailsObjects.Values)
			{
				yield return slamcoreAwareAuthenticationDetails; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the SlamcoreAwareAuthenticationDetailsObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "SlamcoreAwareAuthenticationDetailsObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcoreAwareAuthenticationDetailsObjects.TryAdd(newInternalId, (SlamcoreAwareAuthenticationDetailsDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (SlamcoreAwareAuthenticationDetailsObjectInternalIds.ContainsKey(((SlamcoreAwareAuthenticationDetailsDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = SlamcoreAwareAuthenticationDetailsObjectInternalIds.TryRemove(((SlamcoreAwareAuthenticationDetailsDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcoreAwareAuthenticationDetailsObjectInternalIds.TryAdd(((SlamcoreAwareAuthenticationDetailsDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as SlamcoreAwareAuthenticationDetailsDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "SlamcoreAwareAuthenticationDetailsDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (SlamcoreAwareAuthenticationDetailsObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as SlamcoreAwareAuthenticationDetailsDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "SlamcoreAwareAuthenticationDetailsObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = SlamcoreAwareAuthenticationDetailsObjectInternalIds.ContainsKey((objectToRemove as SlamcoreAwareAuthenticationDetailsDataObject).PrimaryKey) ? (int?) SlamcoreAwareAuthenticationDetailsObjectInternalIds[(objectToRemove as SlamcoreAwareAuthenticationDetailsDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				SlamcoreAwareAuthenticationDetailsDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcoreAwareAuthenticationDetailsObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = SlamcoreAwareAuthenticationDetailsObjectInternalIds.TryRemove((objectToRemove as SlamcoreAwareAuthenticationDetailsDataObject).PrimaryKey, out idvalue);
					}
				}
				
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return SlamcoreAwareAuthenticationDetailsObjects.ContainsKey(internalObjectId) ? SlamcoreAwareAuthenticationDetailsObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as SlamcoreAwareAuthenticationDetailsDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "SlamcoreAwareAuthenticationDetailsObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = SlamcoreAwareAuthenticationDetailsObjectInternalIds.ContainsKey((objectToGet as SlamcoreAwareAuthenticationDetailsDataObject).PrimaryKey) ? (int?) SlamcoreAwareAuthenticationDetailsObjectInternalIds[(objectToGet as SlamcoreAwareAuthenticationDetailsDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return SlamcoreAwareAuthenticationDetailsObjects.ContainsKey((int)objectToGetInternalId) ? SlamcoreAwareAuthenticationDetailsObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return SlamcoreAwareAuthenticationDetailsObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return SlamcoreAwareAuthenticationDetailsObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
			if (relationName == "SlamcoreDevice")
            {
				IEnumerable< SlamcoreDeviceDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SlamcoreDeviceObjectsDataSet.GetSlamcoreDeviceForSlamcoreAwareAuthenticationDetails(rootObject as SlamcoreAwareAuthenticationDetailsDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var SlamcoreAwareAuthenticationDetailsDataSet = dataSetToMerge as SlamcoreAwareAuthenticationDetailsObjectsDataSet;
				if(SlamcoreAwareAuthenticationDetailsDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in SlamcoreAwareAuthenticationDetailsDataSet.SlamcoreAwareAuthenticationDetailsObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "SlamcoreAwareAuthenticationDetailsObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as SlamcoreAwareAuthenticationDetailsDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (SlamcoreAwareAuthenticationDetailsObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}