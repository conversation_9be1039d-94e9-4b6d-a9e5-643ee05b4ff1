﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Features.Security
{
	/* 
		Summary of the permissioning logic:
		User is in at least one authorizing role for the resource => authorised
		All User roles are denied access to the resource => denied
		Else the default access setting for the resource is used. (so e.g. if no rules for this role, or if not all roles explicitly denied, then the default is used)
	*/
    public class SlamcoreDeviceConnectionViewAuthorizations : ISlamcoreDeviceConnectionViewAuthorizations
    {
		public SlamcoreDeviceConnectionViewAuthorizations(IServiceProvider provider, IEntityAuthorizationCache entityAuthorizationCache)
		{
  			_serviceProvider = provider;
			_entityAuthorizationCache = entityAuthorizationCache;
		}

		private readonly IServiceProvider _serviceProvider;
		private readonly IEntityAuthorizationCache _entityAuthorizationCache;

		public string EntityDisplayName => "Slamcore Device Connection View";

		public async Task<PermissionLevel> CanCreateAsync(IDataObject entity, UserClaims claims, IAuthorizations authorizations, ValueWrapper<string> messageWrapper, ValueWrapper<SecurityPredicate> predicateWrapper)
        {
			messageWrapper.Value = null;
			predicateWrapper.Value = null;
			return PermissionLevel.NotSet;
		}

		public async Task<PermissionLevel> CanReadAsync(IDataObject entity, UserClaims claims, IAuthorizations authorizations, ValueWrapper<string> messageWrapper, ValueWrapper<SecurityPredicate> predicateWrapper)
        {
			messageWrapper.Value = null;
			predicateWrapper.Value = null;
			return PermissionLevel.NotSet;
		}

		public async Task<PermissionLevel> CanUpdateAsync(IDataObject entity, UserClaims claims, IAuthorizations authorizations, ValueWrapper<string> messageWrapper, ValueWrapper<SecurityPredicate> predicateWrapper)
        {
			messageWrapper.Value = null;
			predicateWrapper.Value = null;
			return PermissionLevel.NotSet;
		}

		public async Task<PermissionLevel> CanDeleteAsync(IDataObject entity, UserClaims claims, IAuthorizations authorizations, ValueWrapper<string> messageWrapper, ValueWrapper<SecurityPredicate> predicateWrapper)
        {
			messageWrapper.Value = null;
			predicateWrapper.Value = null;
			return PermissionLevel.NotSet;
		}

	}
}
