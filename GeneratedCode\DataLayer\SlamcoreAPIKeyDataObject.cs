﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'SlamcoreAPIKey'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class SlamcoreAPIKeyDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("APIKey")]
		protected System.String _aPIKey;
		[JsonProperty ("APIKeyDisplay")]
		protected System.String _aPIKeyDisplay;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)

		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public SlamcoreAPIKeyDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public SlamcoreAPIKeyDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public SlamcoreAPIKeyDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public SlamcoreAPIKeyDataObject Initialize(SlamcoreAPIKeyDataObject template, bool deepCopy)
		{
			this.SetAPIKeyValue(template.APIKey, false, false);
			this.SetAPIKeyDisplayValue(template.APIKeyDisplay, false, false);
			this.SetIdValue(template.Id, false, false);
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual SlamcoreAPIKeyDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual SlamcoreAPIKeyDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var slamcoreAPIKeySource = sourceObject as SlamcoreAPIKeyDataObject;

			if (ReferenceEquals(null, slamcoreAPIKeySource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetAPIKeyValue(slamcoreAPIKeySource.APIKey, false, false);
			this.SetAPIKeyDisplayValue(slamcoreAPIKeySource.APIKeyDisplay, false, false);
			this.SetIdValue(slamcoreAPIKeySource.Id, false, false);

			if (deepCopy)
			{
				this.ObjectsDataSet = slamcoreAPIKeySource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(slamcoreAPIKeySource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as SlamcoreAPIKeyDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {

		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<SlamcoreDeviceDataObject> _slamcoreDeviceService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceDataObject>>();
      public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet)
		{
			SetSlamcoreDeviceValue(valueToSet, true, true);
		}

        public virtual void SetSlamcoreDeviceValue(SlamcoreDeviceDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<SlamcoreDeviceDataObject>(this, "SlamcoreDevice");
			var existing_slamcoreDevice = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("SlamcoreDevice", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._slamcoreAPIKey_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.SlamcoreAPIKey = this;
					valueToSet.SlamcoreAPIKeyId = this.Id;
				}			
			}
			else  if (existing_slamcoreDevice != null)
            {
                ObjectsDataSet.RemoveObject(existing_slamcoreDevice);
            }
			if (!ReferenceEquals(existing_slamcoreDevice ,valueToSet))
				OnPropertyChanged("SlamcoreDevice", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __slamcoreDeviceSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreDeviceAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreDevice", which is a SlamcoreDeviceDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SlamcoreDeviceDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreDeviceAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SlamcoreDeviceDataObject> LoadSlamcoreDeviceAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreDeviceSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreDeviceAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data SlamcoreDevice for the current entity. The DataObjects doesn't have an ObjectsDataSet", "SlamcoreAPIKeyObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var slamcoreDevice = (this.ObjectsDataSet as ObjectsDataSet).SlamcoreDeviceObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).SlamcoreDeviceObjects.Where(item => item.Value.SlamcoreAPIKeyId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(slamcoreDevice, null))
					{
						var filterPredicate = "SlamcoreAPIKeyId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						slamcoreDevice = (await _slamcoreDeviceService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetSlamcoreDeviceValue(slamcoreDevice, false, false);
						__slamcoreDeviceAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a slamcoreDevice, but relation field not set, encourage it to get set by removing and re-adding the slamcoreDevice 
					if (slamcoreDevice != null && this.SlamcoreDevice == null)
					{
						this.ObjectsDataSet.RemoveObject(slamcoreDevice);
						this.ObjectsDataSet.AddObject(slamcoreDevice);
					}			
                    __slamcoreDeviceAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreDeviceAsync(false);
            }
            finally
            {
                __slamcoreDeviceSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SlamcoreDeviceDataObject SlamcoreDevice 
		{
			get
			{			
				return GetSlamcoreDeviceAsync(true).Result;
			}
			set
			{
				SetSlamcoreDeviceValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreDevice()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreAPIKeyDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreAPIKeyDataObject"].Contains("SlamcoreDevice");
		}

		public virtual async Task<SlamcoreDeviceDataObject> GetSlamcoreDeviceAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SlamcoreDeviceDataObject slamcoreDevice;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<SlamcoreDeviceDataObject>(this, "SlamcoreDevice");
               	slamcoreDevice = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && slamcoreDevice == null && LazyLoadingEnabled && (!__slamcoreDeviceAlreadyLazyLoaded || forceReload))
				{
					slamcoreDevice = await LoadSlamcoreDeviceAsync(forceReload : forceReload);
				}
			}
				
			return slamcoreDevice;
		}


		public override void ClearLazyLoadFlags()
		{
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadSlamcoreDeviceAsync()) != null)
				result.Add(SlamcoreDevice);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return false;
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetAPIKeyValue(System.String valueToSet)
		{
			SetAPIKeyValue(valueToSet, true, true);
		}

		public virtual void SetAPIKeyValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_aPIKey != valueToSet)
			{
				_aPIKey = valueToSet;

				OnPropertyChanged("APIKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The APIKey property of the SlamcoreAPIKey DataObject</summary>
        public virtual System.String APIKey 
		{
			get	{ return String.IsNullOrEmpty(_aPIKey) ? null : _aPIKey; }
			
			
			set
			{
				SetAPIKeyValue(value);
			}
		}		
			
			
		public virtual void SetAPIKeyDisplayValue(System.String valueToSet)
		{
			SetAPIKeyDisplayValue(valueToSet, true, true);
		}

		public virtual void SetAPIKeyDisplayValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_aPIKeyDisplay != valueToSet)
			{
				_aPIKeyDisplay = valueToSet;

				OnPropertyChanged("APIKeyDisplay", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The APIKeyDisplay property of the SlamcoreAPIKey DataObject</summary>
        public virtual System.String APIKeyDisplay 
		{
			get	{ return String.IsNullOrEmpty(_aPIKeyDisplay) ? null : _aPIKeyDisplay; }
			
			
			set
			{
				SetAPIKeyDisplayValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the SlamcoreAPIKey DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
			var slamcoreDevice = GetSlamcoreDeviceAsync(false).Result;
			if (slamcoreDevice != null && this.IsDirty)
            {
				slamcoreDevice.NotifyPropertyChanged("SlamcoreAPIKey." + propertyName, callers);
			}
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is SlamcoreAPIKeyDataObject))
				return false;

			var p = (SlamcoreAPIKeyDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.APIKey == p.APIKey;
			fieldsComparison &= this.APIKeyDisplay == p.APIKeyDisplay;
			fieldsComparison &= this.Id == p.Id;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreAPIKeyCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public SlamcoreAPIKeyCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public SlamcoreAPIKeyCollectionContainer()
		{
		}
		
		public SlamcoreAPIKeyCollectionContainer Construct(DataObjectCollection<SlamcoreAPIKeyDataObject> slamcoreAPIKeyItems)
        {
            if (slamcoreAPIKeyItems == null)
                return this;
				
			this.PrimaryKeys = slamcoreAPIKeyItems.Select(c => c.PrimaryKey).ToList();
            if (slamcoreAPIKeyItems.ObjectsDataSet == null)
            {
                slamcoreAPIKeyItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = slamcoreAPIKeyItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = slamcoreAPIKeyItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<SlamcoreAPIKeyDataObject> ExtractSlamcoreAPIKeyItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<SlamcoreAPIKeyDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<SlamcoreAPIKeyDataObject>(typeof(SlamcoreAPIKeyDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreAPIKeyContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public SlamcoreAPIKeyContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual SlamcoreAPIKeyContainer Construct(SlamcoreAPIKeyDataObject slamcoreAPIKey, bool includeDirtyObjectsOnly = false)
		{
            if (slamcoreAPIKey == null)
                return this;

			this.PrimaryKey = slamcoreAPIKey.PrimaryKey;
			
            if (slamcoreAPIKey.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(slamcoreAPIKey);
            }

			if(slamcoreAPIKey.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity SlamcoreAPIKey", "Unable to set a dataset to the entity. Container may not be initialized", "SlamcoreAPIKeyDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : SlamcoreAPIKey");
			}

			if(slamcoreAPIKey.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in SlamcoreAPIKeyDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "SlamcoreAPIKeyDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in SlamcoreAPIKeyDataObject");
			}
			this.InternalObjectId = (int) slamcoreAPIKey.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? slamcoreAPIKey.ObjectsDataSet.CloneDirtyObjects() : slamcoreAPIKey.ObjectsDataSet;

			return this;
		}
		
		public SlamcoreAPIKeyDataObject ExtractSlamcoreAPIKey()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<SlamcoreAPIKeyDataObject>(typeof(SlamcoreAPIKeyDataObject), InternalObjectId);
			
			return result;
        }	
	}

}