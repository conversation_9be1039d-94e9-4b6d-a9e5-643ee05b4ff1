﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices;

using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Serialization;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.ClientLayer;
using FleetXQ.BusinessLayer;
using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.Components.Client;
using FleetXQ.Data.DataObjects;	
using FleetXQ.Data.DataObjects.Factories;
using GenerativeObjects.Practices.LayerSupportClasses;	
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;	
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Services.Email;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using FleetXQ.Feature.Security.Common;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Client.Model
{
    public static class ServiceCollectionExtensions
    {		

		public static void AddGenerativeObjectsSupport(this Microsoft.Extensions.DependencyInjection.IServiceCollection services)
		{
			services.AddTransient<IObjectsDataSet, ObjectsDataSet>();
			services.AddSingleton<IEntityModelCache,  EntityModelCache>();
			services.AddScoped<IHttpUtilities, HttpUtilities>();
			services.AddSingleton<IApiFilterArgumentBuilder, ApiFilterArgumentBuilder>();

			// Include registration of all server Data Objects and containers
			services.AddTransient<ChecklistDetailDataObject, ChecklistDetailDataObject>();
			services.AddTransient<ChecklistDetailContainer, ChecklistDetailContainer>();
			services.AddTransient<ChecklistDetailCollectionContainer, ChecklistDetailCollectionContainer>();
			services.AddTransient<ChecklistDetailObjectsDataSet, ChecklistDetailObjectsDataSet>();
			services.AddTransient<SlamcoreAwareAuthenticationDetailsDataObject, SlamcoreAwareAuthenticationDetailsDataObject>();
			services.AddTransient<SlamcoreAwareAuthenticationDetailsContainer, SlamcoreAwareAuthenticationDetailsContainer>();
			services.AddTransient<SlamcoreAwareAuthenticationDetailsCollectionContainer, SlamcoreAwareAuthenticationDetailsCollectionContainer>();
			services.AddTransient<SlamcoreAwareAuthenticationDetailsObjectsDataSet, SlamcoreAwareAuthenticationDetailsObjectsDataSet>();
			services.AddTransient<FeatureSubscriptionTemplateDataObject, FeatureSubscriptionTemplateDataObject>();
			services.AddTransient<FeatureSubscriptionTemplateContainer, FeatureSubscriptionTemplateContainer>();
			services.AddTransient<FeatureSubscriptionTemplateCollectionContainer, FeatureSubscriptionTemplateCollectionContainer>();
			services.AddTransient<FeatureSubscriptionTemplateObjectsDataSet, FeatureSubscriptionTemplateObjectsDataSet>();
			services.AddTransient<PersonToModelVehicleMasterAccessViewDataObject, PersonToModelVehicleMasterAccessViewDataObject>();
			services.AddTransient<PersonToModelVehicleMasterAccessViewContainer, PersonToModelVehicleMasterAccessViewContainer>();
			services.AddTransient<PersonToModelVehicleMasterAccessViewCollectionContainer, PersonToModelVehicleMasterAccessViewCollectionContainer>();
			services.AddTransient<PersonToModelVehicleMasterAccessViewObjectsDataSet, PersonToModelVehicleMasterAccessViewObjectsDataSet>();
			services.AddTransient<VehicleLockoutDataObject, VehicleLockoutDataObject>();
			services.AddTransient<VehicleLockoutContainer, VehicleLockoutContainer>();
			services.AddTransient<VehicleLockoutCollectionContainer, VehicleLockoutCollectionContainer>();
			services.AddTransient<VehicleLockoutObjectsDataSet, VehicleLockoutObjectsDataSet>();
			services.AddTransient<RevisionDataObject, RevisionDataObject>();
			services.AddTransient<RevisionContainer, RevisionContainer>();
			services.AddTransient<RevisionCollectionContainer, RevisionCollectionContainer>();
			services.AddTransient<RevisionObjectsDataSet, RevisionObjectsDataSet>();
			services.AddTransient<AlertDataObject, AlertDataObject>();
			services.AddTransient<AlertContainer, AlertContainer>();
			services.AddTransient<AlertCollectionContainer, AlertCollectionContainer>();
			services.AddTransient<AlertObjectsDataSet, AlertObjectsDataSet>();
			services.AddTransient<WebsiteUserDataObject, WebsiteUserDataObject>();
			services.AddTransient<WebsiteUserContainer, WebsiteUserContainer>();
			services.AddTransient<WebsiteUserCollectionContainer, WebsiteUserCollectionContainer>();
			services.AddTransient<WebsiteUserObjectsDataSet, WebsiteUserObjectsDataSet>();
			services.AddTransient<DriverAccessAbuseFilterDataObject, DriverAccessAbuseFilterDataObject>();
			services.AddTransient<DriverAccessAbuseFilterContainer, DriverAccessAbuseFilterContainer>();
			services.AddTransient<DriverAccessAbuseFilterCollectionContainer, DriverAccessAbuseFilterCollectionContainer>();
			services.AddTransient<DriverAccessAbuseFilterObjectsDataSet, DriverAccessAbuseFilterObjectsDataSet>();
			services.AddTransient<AllLicenseExpiryViewDataObject, AllLicenseExpiryViewDataObject>();
			services.AddTransient<AllLicenseExpiryViewContainer, AllLicenseExpiryViewContainer>();
			services.AddTransient<AllLicenseExpiryViewCollectionContainer, AllLicenseExpiryViewCollectionContainer>();
			services.AddTransient<AllLicenseExpiryViewObjectsDataSet, AllLicenseExpiryViewObjectsDataSet>();
			services.AddTransient<DriverLicenseExpiryViewDataObject, DriverLicenseExpiryViewDataObject>();
			services.AddTransient<DriverLicenseExpiryViewContainer, DriverLicenseExpiryViewContainer>();
			services.AddTransient<DriverLicenseExpiryViewCollectionContainer, DriverLicenseExpiryViewCollectionContainer>();
			services.AddTransient<DriverLicenseExpiryViewObjectsDataSet, DriverLicenseExpiryViewObjectsDataSet>();
			services.AddTransient<ContactPersonInformationDataObject, ContactPersonInformationDataObject>();
			services.AddTransient<ContactPersonInformationContainer, ContactPersonInformationContainer>();
			services.AddTransient<ContactPersonInformationCollectionContainer, ContactPersonInformationCollectionContainer>();
			services.AddTransient<ContactPersonInformationObjectsDataSet, ContactPersonInformationObjectsDataSet>();
			services.AddTransient<ChecklistSettingsDataObject, ChecklistSettingsDataObject>();
			services.AddTransient<ChecklistSettingsContainer, ChecklistSettingsContainer>();
			services.AddTransient<ChecklistSettingsCollectionContainer, ChecklistSettingsCollectionContainer>();
			services.AddTransient<ChecklistSettingsObjectsDataSet, ChecklistSettingsObjectsDataSet>();
			services.AddTransient<VehicleDiagnosticDataObject, VehicleDiagnosticDataObject>();
			services.AddTransient<VehicleDiagnosticContainer, VehicleDiagnosticContainer>();
			services.AddTransient<VehicleDiagnosticCollectionContainer, VehicleDiagnosticCollectionContainer>();
			services.AddTransient<VehicleDiagnosticObjectsDataSet, VehicleDiagnosticObjectsDataSet>();
			services.AddTransient<GOUser2FADataObject, GOUser2FADataObject>();
			services.AddTransient<GOUser2FAContainer, GOUser2FAContainer>();
			services.AddTransient<GOUser2FACollectionContainer, GOUser2FACollectionContainer>();
			services.AddTransient<GOUser2FAObjectsDataSet, GOUser2FAObjectsDataSet>();
			services.AddTransient<VehicleGPSDataObject, VehicleGPSDataObject>();
			services.AddTransient<VehicleGPSContainer, VehicleGPSContainer>();
			services.AddTransient<VehicleGPSCollectionContainer, VehicleGPSCollectionContainer>();
			services.AddTransient<VehicleGPSObjectsDataSet, VehicleGPSObjectsDataSet>();
			services.AddTransient<ModuleDataObject, ModuleDataObject>();
			services.AddTransient<ModuleContainer, ModuleContainer>();
			services.AddTransient<ModuleCollectionContainer, ModuleCollectionContainer>();
			services.AddTransient<ModuleObjectsDataSet, ModuleObjectsDataSet>();
			services.AddTransient<PersonToPerVehicleMasterAccessViewDataObject, PersonToPerVehicleMasterAccessViewDataObject>();
			services.AddTransient<PersonToPerVehicleMasterAccessViewContainer, PersonToPerVehicleMasterAccessViewContainer>();
			services.AddTransient<PersonToPerVehicleMasterAccessViewCollectionContainer, PersonToPerVehicleMasterAccessViewCollectionContainer>();
			services.AddTransient<PersonToPerVehicleMasterAccessViewObjectsDataSet, PersonToPerVehicleMasterAccessViewObjectsDataSet>();
			services.AddTransient<AccessGroupDataObject, AccessGroupDataObject>();
			services.AddTransient<AccessGroupContainer, AccessGroupContainer>();
			services.AddTransient<AccessGroupCollectionContainer, AccessGroupCollectionContainer>();
			services.AddTransient<AccessGroupObjectsDataSet, AccessGroupObjectsDataSet>();
			services.AddTransient<HelpDataObject, HelpDataObject>();
			services.AddTransient<HelpContainer, HelpContainer>();
			services.AddTransient<HelpCollectionContainer, HelpCollectionContainer>();
			services.AddTransient<HelpObjectsDataSet, HelpObjectsDataSet>();
			services.AddTransient<ChecklistStatusViewDataObject, ChecklistStatusViewDataObject>();
			services.AddTransient<ChecklistStatusViewContainer, ChecklistStatusViewContainer>();
			services.AddTransient<ChecklistStatusViewCollectionContainer, ChecklistStatusViewCollectionContainer>();
			services.AddTransient<ChecklistStatusViewObjectsDataSet, ChecklistStatusViewObjectsDataSet>();
			services.AddTransient<CurrentStatusCombinedViewDataObject, CurrentStatusCombinedViewDataObject>();
			services.AddTransient<CurrentStatusCombinedViewContainer, CurrentStatusCombinedViewContainer>();
			services.AddTransient<CurrentStatusCombinedViewCollectionContainer, CurrentStatusCombinedViewCollectionContainer>();
			services.AddTransient<CurrentStatusCombinedViewObjectsDataSet, CurrentStatusCombinedViewObjectsDataSet>();
			services.AddTransient<CurrentVehicleStatusChartViewDataObject, CurrentVehicleStatusChartViewDataObject>();
			services.AddTransient<CurrentVehicleStatusChartViewContainer, CurrentVehicleStatusChartViewContainer>();
			services.AddTransient<CurrentVehicleStatusChartViewCollectionContainer, CurrentVehicleStatusChartViewCollectionContainer>();
			services.AddTransient<CurrentVehicleStatusChartViewObjectsDataSet, CurrentVehicleStatusChartViewObjectsDataSet>();
			services.AddTransient<DealerFeatureSubscriptionDataObject, DealerFeatureSubscriptionDataObject>();
			services.AddTransient<DealerFeatureSubscriptionContainer, DealerFeatureSubscriptionContainer>();
			services.AddTransient<DealerFeatureSubscriptionCollectionContainer, DealerFeatureSubscriptionCollectionContainer>();
			services.AddTransient<DealerFeatureSubscriptionObjectsDataSet, DealerFeatureSubscriptionObjectsDataSet>();
			services.AddTransient<MessageHistoryDataObject, MessageHistoryDataObject>();
			services.AddTransient<MessageHistoryContainer, MessageHistoryContainer>();
			services.AddTransient<MessageHistoryCollectionContainer, MessageHistoryCollectionContainer>();
			services.AddTransient<MessageHistoryObjectsDataSet, MessageHistoryObjectsDataSet>();
			services.AddTransient<AccessGroupToSiteDataObject, AccessGroupToSiteDataObject>();
			services.AddTransient<AccessGroupToSiteContainer, AccessGroupToSiteContainer>();
			services.AddTransient<AccessGroupToSiteCollectionContainer, AccessGroupToSiteCollectionContainer>();
			services.AddTransient<AccessGroupToSiteObjectsDataSet, AccessGroupToSiteObjectsDataSet>();
			services.AddTransient<AllDriverAccessAbuseStoreProcedureDataObject, AllDriverAccessAbuseStoreProcedureDataObject>();
			services.AddTransient<AllDriverAccessAbuseStoreProcedureContainer, AllDriverAccessAbuseStoreProcedureContainer>();
			services.AddTransient<AllDriverAccessAbuseStoreProcedureCollectionContainer, AllDriverAccessAbuseStoreProcedureCollectionContainer>();
			services.AddTransient<AllDriverAccessAbuseStoreProcedureObjectsDataSet, AllDriverAccessAbuseStoreProcedureObjectsDataSet>();
			services.AddTransient<CustomerToModelDataObject, CustomerToModelDataObject>();
			services.AddTransient<CustomerToModelContainer, CustomerToModelContainer>();
			services.AddTransient<CustomerToModelCollectionContainer, CustomerToModelCollectionContainer>();
			services.AddTransient<CustomerToModelObjectsDataSet, CustomerToModelObjectsDataSet>();
			services.AddTransient<AllEmailSubscriptionStoreProcedureDataObject, AllEmailSubscriptionStoreProcedureDataObject>();
			services.AddTransient<AllEmailSubscriptionStoreProcedureContainer, AllEmailSubscriptionStoreProcedureContainer>();
			services.AddTransient<AllEmailSubscriptionStoreProcedureCollectionContainer, AllEmailSubscriptionStoreProcedureCollectionContainer>();
			services.AddTransient<AllEmailSubscriptionStoreProcedureObjectsDataSet, AllEmailSubscriptionStoreProcedureObjectsDataSet>();
			services.AddTransient<AlertSummaryStoreProcedureDataObject, AlertSummaryStoreProcedureDataObject>();
			services.AddTransient<AlertSummaryStoreProcedureContainer, AlertSummaryStoreProcedureContainer>();
			services.AddTransient<AlertSummaryStoreProcedureCollectionContainer, AlertSummaryStoreProcedureCollectionContainer>();
			services.AddTransient<AlertSummaryStoreProcedureObjectsDataSet, AlertSummaryStoreProcedureObjectsDataSet>();
			services.AddTransient<DashboardVehicleCardStoreProcedureDataObject, DashboardVehicleCardStoreProcedureDataObject>();
			services.AddTransient<DashboardVehicleCardStoreProcedureContainer, DashboardVehicleCardStoreProcedureContainer>();
			services.AddTransient<DashboardVehicleCardStoreProcedureCollectionContainer, DashboardVehicleCardStoreProcedureCollectionContainer>();
			services.AddTransient<DashboardVehicleCardStoreProcedureObjectsDataSet, DashboardVehicleCardStoreProcedureObjectsDataSet>();
			services.AddTransient<RegionDataObject, RegionDataObject>();
			services.AddTransient<RegionContainer, RegionContainer>();
			services.AddTransient<RegionCollectionContainer, RegionCollectionContainer>();
			services.AddTransient<RegionObjectsDataSet, RegionObjectsDataSet>();
			services.AddTransient<ModelDataObject, ModelDataObject>();
			services.AddTransient<ModelContainer, ModelContainer>();
			services.AddTransient<ModelCollectionContainer, ModelCollectionContainer>();
			services.AddTransient<ModelObjectsDataSet, ModelObjectsDataSet>();
			services.AddTransient<AllVORSessionsPerVehicleStoreProcedureDataObject, AllVORSessionsPerVehicleStoreProcedureDataObject>();
			services.AddTransient<AllVORSessionsPerVehicleStoreProcedureContainer, AllVORSessionsPerVehicleStoreProcedureContainer>();
			services.AddTransient<AllVORSessionsPerVehicleStoreProcedureCollectionContainer, AllVORSessionsPerVehicleStoreProcedureCollectionContainer>();
			services.AddTransient<AllVORSessionsPerVehicleStoreProcedureObjectsDataSet, AllVORSessionsPerVehicleStoreProcedureObjectsDataSet>();
			services.AddTransient<SlamcoreDeviceConnectionViewDataObject, SlamcoreDeviceConnectionViewDataObject>();
			services.AddTransient<SlamcoreDeviceConnectionViewContainer, SlamcoreDeviceConnectionViewContainer>();
			services.AddTransient<SlamcoreDeviceConnectionViewCollectionContainer, SlamcoreDeviceConnectionViewCollectionContainer>();
			services.AddTransient<SlamcoreDeviceConnectionViewObjectsDataSet, SlamcoreDeviceConnectionViewObjectsDataSet>();
			services.AddTransient<AllImpactsViewDataObject, AllImpactsViewDataObject>();
			services.AddTransient<AllImpactsViewContainer, AllImpactsViewContainer>();
			services.AddTransient<AllImpactsViewCollectionContainer, AllImpactsViewCollectionContainer>();
			services.AddTransient<AllImpactsViewObjectsDataSet, AllImpactsViewObjectsDataSet>();
			services.AddTransient<CustomerDataObject, CustomerDataObject>();
			services.AddTransient<CustomerContainer, CustomerContainer>();
			services.AddTransient<CustomerCollectionContainer, CustomerCollectionContainer>();
			services.AddTransient<CustomerObjectsDataSet, CustomerObjectsDataSet>();
			services.AddTransient<DepartmentChecklistDataObject, DepartmentChecklistDataObject>();
			services.AddTransient<DepartmentChecklistContainer, DepartmentChecklistContainer>();
			services.AddTransient<DepartmentChecklistCollectionContainer, DepartmentChecklistCollectionContainer>();
			services.AddTransient<DepartmentChecklistObjectsDataSet, DepartmentChecklistObjectsDataSet>();
			services.AddTransient<ModuleHistoryDataObject, ModuleHistoryDataObject>();
			services.AddTransient<ModuleHistoryContainer, ModuleHistoryContainer>();
			services.AddTransient<ModuleHistoryCollectionContainer, ModuleHistoryCollectionContainer>();
			services.AddTransient<ModuleHistoryObjectsDataSet, ModuleHistoryObjectsDataSet>();
			services.AddTransient<CountryDataObject, CountryDataObject>();
			services.AddTransient<CountryContainer, CountryContainer>();
			services.AddTransient<CountryCollectionContainer, CountryCollectionContainer>();
			services.AddTransient<CountryObjectsDataSet, CountryObjectsDataSet>();
			services.AddTransient<AllChecklistResultViewDataObject, AllChecklistResultViewDataObject>();
			services.AddTransient<AllChecklistResultViewContainer, AllChecklistResultViewContainer>();
			services.AddTransient<AllChecklistResultViewCollectionContainer, AllChecklistResultViewCollectionContainer>();
			services.AddTransient<AllChecklistResultViewObjectsDataSet, AllChecklistResultViewObjectsDataSet>();
			services.AddTransient<GPSHistoryDataObject, GPSHistoryDataObject>();
			services.AddTransient<GPSHistoryContainer, GPSHistoryContainer>();
			services.AddTransient<GPSHistoryCollectionContainer, GPSHistoryCollectionContainer>();
			services.AddTransient<GPSHistoryObjectsDataSet, GPSHistoryObjectsDataSet>();
			services.AddTransient<ImpactsForVehicleViewDataObject, ImpactsForVehicleViewDataObject>();
			services.AddTransient<ImpactsForVehicleViewContainer, ImpactsForVehicleViewContainer>();
			services.AddTransient<ImpactsForVehicleViewCollectionContainer, ImpactsForVehicleViewCollectionContainer>();
			services.AddTransient<ImpactsForVehicleViewObjectsDataSet, ImpactsForVehicleViewObjectsDataSet>();
			services.AddTransient<AllVehicleUnlocksViewDataObject, AllVehicleUnlocksViewDataObject>();
			services.AddTransient<AllVehicleUnlocksViewContainer, AllVehicleUnlocksViewContainer>();
			services.AddTransient<AllVehicleUnlocksViewCollectionContainer, AllVehicleUnlocksViewCollectionContainer>();
			services.AddTransient<AllVehicleUnlocksViewObjectsDataSet, AllVehicleUnlocksViewObjectsDataSet>();
			services.AddTransient<SlamcorePedestrianDetectionDataObject, SlamcorePedestrianDetectionDataObject>();
			services.AddTransient<SlamcorePedestrianDetectionContainer, SlamcorePedestrianDetectionContainer>();
			services.AddTransient<SlamcorePedestrianDetectionCollectionContainer, SlamcorePedestrianDetectionCollectionContainer>();
			services.AddTransient<SlamcorePedestrianDetectionObjectsDataSet, SlamcorePedestrianDetectionObjectsDataSet>();
			services.AddTransient<ImpactFrequencyPerWeekMonthViewDataObject, ImpactFrequencyPerWeekMonthViewDataObject>();
			services.AddTransient<ImpactFrequencyPerWeekMonthViewContainer, ImpactFrequencyPerWeekMonthViewContainer>();
			services.AddTransient<ImpactFrequencyPerWeekMonthViewCollectionContainer, ImpactFrequencyPerWeekMonthViewCollectionContainer>();
			services.AddTransient<ImpactFrequencyPerWeekMonthViewObjectsDataSet, ImpactFrequencyPerWeekMonthViewObjectsDataSet>();
			services.AddTransient<VehicleUtilizationLastTwelveHoursViewDataObject, VehicleUtilizationLastTwelveHoursViewDataObject>();
			services.AddTransient<VehicleUtilizationLastTwelveHoursViewContainer, VehicleUtilizationLastTwelveHoursViewContainer>();
			services.AddTransient<VehicleUtilizationLastTwelveHoursViewCollectionContainer, VehicleUtilizationLastTwelveHoursViewCollectionContainer>();
			services.AddTransient<VehicleUtilizationLastTwelveHoursViewObjectsDataSet, VehicleUtilizationLastTwelveHoursViewObjectsDataSet>();
			services.AddTransient<IOFIELDDataObject, IOFIELDDataObject>();
			services.AddTransient<IOFIELDContainer, IOFIELDContainer>();
			services.AddTransient<IOFIELDCollectionContainer, IOFIELDCollectionContainer>();
			services.AddTransient<IOFIELDObjectsDataSet, IOFIELDObjectsDataSet>();
			services.AddTransient<EmailDataObject, EmailDataObject>();
			services.AddTransient<EmailContainer, EmailContainer>();
			services.AddTransient<EmailCollectionContainer, EmailCollectionContainer>();
			services.AddTransient<EmailObjectsDataSet, EmailObjectsDataSet>();
			services.AddTransient<LicenceDetailDataObject, LicenceDetailDataObject>();
			services.AddTransient<LicenceDetailContainer, LicenceDetailContainer>();
			services.AddTransient<LicenceDetailCollectionContainer, LicenceDetailCollectionContainer>();
			services.AddTransient<LicenceDetailObjectsDataSet, LicenceDetailObjectsDataSet>();
			services.AddTransient<PedestrianDetectionHistoryFilterDataObject, PedestrianDetectionHistoryFilterDataObject>();
			services.AddTransient<PedestrianDetectionHistoryFilterContainer, PedestrianDetectionHistoryFilterContainer>();
			services.AddTransient<PedestrianDetectionHistoryFilterCollectionContainer, PedestrianDetectionHistoryFilterCollectionContainer>();
			services.AddTransient<PedestrianDetectionHistoryFilterObjectsDataSet, PedestrianDetectionHistoryFilterObjectsDataSet>();
			services.AddTransient<GOLoginHistoryDataObject, GOLoginHistoryDataObject>();
			services.AddTransient<GOLoginHistoryContainer, GOLoginHistoryContainer>();
			services.AddTransient<GOLoginHistoryCollectionContainer, GOLoginHistoryCollectionContainer>();
			services.AddTransient<GOLoginHistoryObjectsDataSet, GOLoginHistoryObjectsDataSet>();
			services.AddTransient<MainDashboardFilterDataObject, MainDashboardFilterDataObject>();
			services.AddTransient<MainDashboardFilterContainer, MainDashboardFilterContainer>();
			services.AddTransient<MainDashboardFilterCollectionContainer, MainDashboardFilterCollectionContainer>();
			services.AddTransient<MainDashboardFilterObjectsDataSet, MainDashboardFilterObjectsDataSet>();
			services.AddTransient<AllVehicleCalibrationStoreProcedureDataObject, AllVehicleCalibrationStoreProcedureDataObject>();
			services.AddTransient<AllVehicleCalibrationStoreProcedureContainer, AllVehicleCalibrationStoreProcedureContainer>();
			services.AddTransient<AllVehicleCalibrationStoreProcedureCollectionContainer, AllVehicleCalibrationStoreProcedureCollectionContainer>();
			services.AddTransient<AllVehicleCalibrationStoreProcedureObjectsDataSet, AllVehicleCalibrationStoreProcedureObjectsDataSet>();
			services.AddTransient<AccessGroupTemplateDataObject, AccessGroupTemplateDataObject>();
			services.AddTransient<AccessGroupTemplateContainer, AccessGroupTemplateContainer>();
			services.AddTransient<AccessGroupTemplateCollectionContainer, AccessGroupTemplateCollectionContainer>();
			services.AddTransient<AccessGroupTemplateObjectsDataSet, AccessGroupTemplateObjectsDataSet>();
			services.AddTransient<CardToCardAccessDataObject, CardToCardAccessDataObject>();
			services.AddTransient<CardToCardAccessContainer, CardToCardAccessContainer>();
			services.AddTransient<CardToCardAccessCollectionContainer, CardToCardAccessCollectionContainer>();
			services.AddTransient<CardToCardAccessObjectsDataSet, CardToCardAccessObjectsDataSet>();
			services.AddTransient<ZoneCoordinatesDataObject, ZoneCoordinatesDataObject>();
			services.AddTransient<ZoneCoordinatesContainer, ZoneCoordinatesContainer>();
			services.AddTransient<ZoneCoordinatesCollectionContainer, ZoneCoordinatesCollectionContainer>();
			services.AddTransient<ZoneCoordinatesObjectsDataSet, ZoneCoordinatesObjectsDataSet>();
			services.AddTransient<SnapshotDataObject, SnapshotDataObject>();
			services.AddTransient<SnapshotContainer, SnapshotContainer>();
			services.AddTransient<SnapshotCollectionContainer, SnapshotCollectionContainer>();
			services.AddTransient<SnapshotObjectsDataSet, SnapshotObjectsDataSet>();
			services.AddTransient<GOTaskDataObject, GOTaskDataObject>();
			services.AddTransient<GOTaskContainer, GOTaskContainer>();
			services.AddTransient<GOTaskCollectionContainer, GOTaskCollectionContainer>();
			services.AddTransient<GOTaskObjectsDataSet, GOTaskObjectsDataSet>();
			services.AddTransient<GeneralProductivityReportFilterDataObject, GeneralProductivityReportFilterDataObject>();
			services.AddTransient<GeneralProductivityReportFilterContainer, GeneralProductivityReportFilterContainer>();
			services.AddTransient<GeneralProductivityReportFilterCollectionContainer, GeneralProductivityReportFilterCollectionContainer>();
			services.AddTransient<GeneralProductivityReportFilterObjectsDataSet, GeneralProductivityReportFilterObjectsDataSet>();
			services.AddTransient<DepartmentVehicleNormalCardAccessDataObject, DepartmentVehicleNormalCardAccessDataObject>();
			services.AddTransient<DepartmentVehicleNormalCardAccessContainer, DepartmentVehicleNormalCardAccessContainer>();
			services.AddTransient<DepartmentVehicleNormalCardAccessCollectionContainer, DepartmentVehicleNormalCardAccessCollectionContainer>();
			services.AddTransient<DepartmentVehicleNormalCardAccessObjectsDataSet, DepartmentVehicleNormalCardAccessObjectsDataSet>();
			services.AddTransient<CustomerToPersonViewDataObject, CustomerToPersonViewDataObject>();
			services.AddTransient<CustomerToPersonViewContainer, CustomerToPersonViewContainer>();
			services.AddTransient<CustomerToPersonViewCollectionContainer, CustomerToPersonViewCollectionContainer>();
			services.AddTransient<CustomerToPersonViewObjectsDataSet, CustomerToPersonViewObjectsDataSet>();
			services.AddTransient<VehicleSlamcoreLocationHistoryDataObject, VehicleSlamcoreLocationHistoryDataObject>();
			services.AddTransient<VehicleSlamcoreLocationHistoryContainer, VehicleSlamcoreLocationHistoryContainer>();
			services.AddTransient<VehicleSlamcoreLocationHistoryCollectionContainer, VehicleSlamcoreLocationHistoryCollectionContainer>();
			services.AddTransient<VehicleSlamcoreLocationHistoryObjectsDataSet, VehicleSlamcoreLocationHistoryObjectsDataSet>();
			services.AddTransient<PermissionDataObject, PermissionDataObject>();
			services.AddTransient<PermissionContainer, PermissionContainer>();
			services.AddTransient<PermissionCollectionContainer, PermissionCollectionContainer>();
			services.AddTransient<PermissionObjectsDataSet, PermissionObjectsDataSet>();
			services.AddTransient<SessionDetailsDataObject, SessionDetailsDataObject>();
			services.AddTransient<SessionDetailsContainer, SessionDetailsContainer>();
			services.AddTransient<SessionDetailsCollectionContainer, SessionDetailsCollectionContainer>();
			services.AddTransient<SessionDetailsObjectsDataSet, SessionDetailsObjectsDataSet>();
			services.AddTransient<GORoleDataObject, GORoleDataObject>();
			services.AddTransient<GORoleContainer, GORoleContainer>();
			services.AddTransient<GORoleCollectionContainer, GORoleCollectionContainer>();
			services.AddTransient<GORoleObjectsDataSet, GORoleObjectsDataSet>();
			services.AddTransient<GeneralProductivityPerVehicleViewDataObject, GeneralProductivityPerVehicleViewDataObject>();
			services.AddTransient<GeneralProductivityPerVehicleViewContainer, GeneralProductivityPerVehicleViewContainer>();
			services.AddTransient<GeneralProductivityPerVehicleViewCollectionContainer, GeneralProductivityPerVehicleViewCollectionContainer>();
			services.AddTransient<GeneralProductivityPerVehicleViewObjectsDataSet, GeneralProductivityPerVehicleViewObjectsDataSet>();
			services.AddTransient<VehicleDataObject, VehicleDataObject>();
			services.AddTransient<VehicleContainer, VehicleContainer>();
			services.AddTransient<VehicleCollectionContainer, VehicleCollectionContainer>();
			services.AddTransient<VehicleObjectsDataSet, VehicleObjectsDataSet>();
			services.AddTransient<GeneralProductivityPerDriverViewLatestDataObject, GeneralProductivityPerDriverViewLatestDataObject>();
			services.AddTransient<GeneralProductivityPerDriverViewLatestContainer, GeneralProductivityPerDriverViewLatestContainer>();
			services.AddTransient<GeneralProductivityPerDriverViewLatestCollectionContainer, GeneralProductivityPerDriverViewLatestCollectionContainer>();
			services.AddTransient<GeneralProductivityPerDriverViewLatestObjectsDataSet, GeneralProductivityPerDriverViewLatestObjectsDataSet>();
			services.AddTransient<DriverDataObject, DriverDataObject>();
			services.AddTransient<DriverContainer, DriverContainer>();
			services.AddTransient<DriverCollectionContainer, DriverCollectionContainer>();
			services.AddTransient<DriverObjectsDataSet, DriverObjectsDataSet>();
			services.AddTransient<EmailGroupsToPersonDataObject, EmailGroupsToPersonDataObject>();
			services.AddTransient<EmailGroupsToPersonContainer, EmailGroupsToPersonContainer>();
			services.AddTransient<EmailGroupsToPersonCollectionContainer, EmailGroupsToPersonCollectionContainer>();
			services.AddTransient<EmailGroupsToPersonObjectsDataSet, EmailGroupsToPersonObjectsDataSet>();
			services.AddTransient<GOGroupRoleDataObject, GOGroupRoleDataObject>();
			services.AddTransient<GOGroupRoleContainer, GOGroupRoleContainer>();
			services.AddTransient<GOGroupRoleCollectionContainer, GOGroupRoleCollectionContainer>();
			services.AddTransient<GOGroupRoleObjectsDataSet, GOGroupRoleObjectsDataSet>();
			services.AddTransient<ModelVehicleMasterCardAccessDataObject, ModelVehicleMasterCardAccessDataObject>();
			services.AddTransient<ModelVehicleMasterCardAccessContainer, ModelVehicleMasterCardAccessContainer>();
			services.AddTransient<ModelVehicleMasterCardAccessCollectionContainer, ModelVehicleMasterCardAccessCollectionContainer>();
			services.AddTransient<ModelVehicleMasterCardAccessObjectsDataSet, ModelVehicleMasterCardAccessObjectsDataSet>();
			services.AddTransient<EmailSubscriptionReportFilterDataObject, EmailSubscriptionReportFilterDataObject>();
			services.AddTransient<EmailSubscriptionReportFilterContainer, EmailSubscriptionReportFilterContainer>();
			services.AddTransient<EmailSubscriptionReportFilterCollectionContainer, EmailSubscriptionReportFilterCollectionContainer>();
			services.AddTransient<EmailSubscriptionReportFilterObjectsDataSet, EmailSubscriptionReportFilterObjectsDataSet>();
			services.AddTransient<VehicleProficiencyViewDataObject, VehicleProficiencyViewDataObject>();
			services.AddTransient<VehicleProficiencyViewContainer, VehicleProficiencyViewContainer>();
			services.AddTransient<VehicleProficiencyViewCollectionContainer, VehicleProficiencyViewCollectionContainer>();
			services.AddTransient<VehicleProficiencyViewObjectsDataSet, VehicleProficiencyViewObjectsDataSet>();
			services.AddTransient<DashboardDriverCardViewDataObject, DashboardDriverCardViewDataObject>();
			services.AddTransient<DashboardDriverCardViewContainer, DashboardDriverCardViewContainer>();
			services.AddTransient<DashboardDriverCardViewCollectionContainer, DashboardDriverCardViewCollectionContainer>();
			services.AddTransient<DashboardDriverCardViewObjectsDataSet, DashboardDriverCardViewObjectsDataSet>();
			services.AddTransient<DriverProficiencyViewDataObject, DriverProficiencyViewDataObject>();
			services.AddTransient<DriverProficiencyViewContainer, DriverProficiencyViewContainer>();
			services.AddTransient<DriverProficiencyViewCollectionContainer, DriverProficiencyViewCollectionContainer>();
			services.AddTransient<DriverProficiencyViewObjectsDataSet, DriverProficiencyViewObjectsDataSet>();
			services.AddTransient<GOSecurityTokensDataObject, GOSecurityTokensDataObject>();
			services.AddTransient<GOSecurityTokensContainer, GOSecurityTokensContainer>();
			services.AddTransient<GOSecurityTokensCollectionContainer, GOSecurityTokensCollectionContainer>();
			services.AddTransient<GOSecurityTokensObjectsDataSet, GOSecurityTokensObjectsDataSet>();
			services.AddTransient<AlertHistoryDataObject, AlertHistoryDataObject>();
			services.AddTransient<AlertHistoryContainer, AlertHistoryContainer>();
			services.AddTransient<AlertHistoryCollectionContainer, AlertHistoryCollectionContainer>();
			services.AddTransient<AlertHistoryObjectsDataSet, AlertHistoryObjectsDataSet>();
			services.AddTransient<DashboardFilterDataObject, DashboardFilterDataObject>();
			services.AddTransient<DashboardFilterContainer, DashboardFilterContainer>();
			services.AddTransient<DashboardFilterCollectionContainer, DashboardFilterCollectionContainer>();
			services.AddTransient<DashboardFilterObjectsDataSet, DashboardFilterObjectsDataSet>();
			services.AddTransient<VORReportFilterDataObject, VORReportFilterDataObject>();
			services.AddTransient<VORReportFilterContainer, VORReportFilterContainer>();
			services.AddTransient<VORReportFilterCollectionContainer, VORReportFilterCollectionContainer>();
			services.AddTransient<VORReportFilterObjectsDataSet, VORReportFilterObjectsDataSet>();
			services.AddTransient<OnDemandAuthorisationFilterDataObject, OnDemandAuthorisationFilterDataObject>();
			services.AddTransient<OnDemandAuthorisationFilterContainer, OnDemandAuthorisationFilterContainer>();
			services.AddTransient<OnDemandAuthorisationFilterCollectionContainer, OnDemandAuthorisationFilterCollectionContainer>();
			services.AddTransient<OnDemandAuthorisationFilterObjectsDataSet, OnDemandAuthorisationFilterObjectsDataSet>();
			services.AddTransient<WebsiteRoleDataObject, WebsiteRoleDataObject>();
			services.AddTransient<WebsiteRoleContainer, WebsiteRoleContainer>();
			services.AddTransient<WebsiteRoleCollectionContainer, WebsiteRoleCollectionContainer>();
			services.AddTransient<WebsiteRoleObjectsDataSet, WebsiteRoleObjectsDataSet>();
			services.AddTransient<GOUserRoleDataObject, GOUserRoleDataObject>();
			services.AddTransient<GOUserRoleContainer, GOUserRoleContainer>();
			services.AddTransient<GOUserRoleCollectionContainer, GOUserRoleCollectionContainer>();
			services.AddTransient<GOUserRoleObjectsDataSet, GOUserRoleObjectsDataSet>();
			services.AddTransient<GeneralProductivityViewDataObject, GeneralProductivityViewDataObject>();
			services.AddTransient<GeneralProductivityViewContainer, GeneralProductivityViewContainer>();
			services.AddTransient<GeneralProductivityViewCollectionContainer, GeneralProductivityViewCollectionContainer>();
			services.AddTransient<GeneralProductivityViewObjectsDataSet, GeneralProductivityViewObjectsDataSet>();
			services.AddTransient<InspectionDataObject, InspectionDataObject>();
			services.AddTransient<InspectionContainer, InspectionContainer>();
			services.AddTransient<InspectionCollectionContainer, InspectionCollectionContainer>();
			services.AddTransient<InspectionObjectsDataSet, InspectionObjectsDataSet>();
			services.AddTransient<ImpactDataObject, ImpactDataObject>();
			services.AddTransient<ImpactContainer, ImpactContainer>();
			services.AddTransient<ImpactCollectionContainer, ImpactCollectionContainer>();
			services.AddTransient<ImpactObjectsDataSet, ImpactObjectsDataSet>();
			services.AddTransient<PersonToDepartmentVehicleMasterAccessViewDataObject, PersonToDepartmentVehicleMasterAccessViewDataObject>();
			services.AddTransient<PersonToDepartmentVehicleMasterAccessViewContainer, PersonToDepartmentVehicleMasterAccessViewContainer>();
			services.AddTransient<PersonToDepartmentVehicleMasterAccessViewCollectionContainer, PersonToDepartmentVehicleMasterAccessViewCollectionContainer>();
			services.AddTransient<PersonToDepartmentVehicleMasterAccessViewObjectsDataSet, PersonToDepartmentVehicleMasterAccessViewObjectsDataSet>();
			services.AddTransient<CurrentStatusVehicleViewDataObject, CurrentStatusVehicleViewDataObject>();
			services.AddTransient<CurrentStatusVehicleViewContainer, CurrentStatusVehicleViewContainer>();
			services.AddTransient<CurrentStatusVehicleViewCollectionContainer, CurrentStatusVehicleViewCollectionContainer>();
			services.AddTransient<CurrentStatusVehicleViewObjectsDataSet, CurrentStatusVehicleViewObjectsDataSet>();
			services.AddTransient<CustomerModelDataObject, CustomerModelDataObject>();
			services.AddTransient<CustomerModelContainer, CustomerModelContainer>();
			services.AddTransient<CustomerModelCollectionContainer, CustomerModelCollectionContainer>();
			services.AddTransient<CustomerModelObjectsDataSet, CustomerModelObjectsDataSet>();
			services.AddTransient<TagDataObject, TagDataObject>();
			services.AddTransient<TagContainer, TagContainer>();
			services.AddTransient<TagCollectionContainer, TagCollectionContainer>();
			services.AddTransient<TagObjectsDataSet, TagObjectsDataSet>();
			services.AddTransient<VehicleBroadcastMessageDataObject, VehicleBroadcastMessageDataObject>();
			services.AddTransient<VehicleBroadcastMessageContainer, VehicleBroadcastMessageContainer>();
			services.AddTransient<VehicleBroadcastMessageCollectionContainer, VehicleBroadcastMessageCollectionContainer>();
			services.AddTransient<VehicleBroadcastMessageObjectsDataSet, VehicleBroadcastMessageObjectsDataSet>();
			services.AddTransient<FloorPlanDataObject, FloorPlanDataObject>();
			services.AddTransient<FloorPlanContainer, FloorPlanContainer>();
			services.AddTransient<FloorPlanCollectionContainer, FloorPlanCollectionContainer>();
			services.AddTransient<FloorPlanObjectsDataSet, FloorPlanObjectsDataSet>();
			services.AddTransient<TodaysPreopCheckViewDataObject, TodaysPreopCheckViewDataObject>();
			services.AddTransient<TodaysPreopCheckViewContainer, TodaysPreopCheckViewContainer>();
			services.AddTransient<TodaysPreopCheckViewCollectionContainer, TodaysPreopCheckViewCollectionContainer>();
			services.AddTransient<TodaysPreopCheckViewObjectsDataSet, TodaysPreopCheckViewObjectsDataSet>();
			services.AddTransient<ModelVehicleNormalCardAccessDataObject, ModelVehicleNormalCardAccessDataObject>();
			services.AddTransient<ModelVehicleNormalCardAccessContainer, ModelVehicleNormalCardAccessContainer>();
			services.AddTransient<ModelVehicleNormalCardAccessCollectionContainer, ModelVehicleNormalCardAccessCollectionContainer>();
			services.AddTransient<ModelVehicleNormalCardAccessObjectsDataSet, ModelVehicleNormalCardAccessObjectsDataSet>();
			services.AddTransient<SiteVehicleNormalCardAccessDataObject, SiteVehicleNormalCardAccessDataObject>();
			services.AddTransient<SiteVehicleNormalCardAccessContainer, SiteVehicleNormalCardAccessContainer>();
			services.AddTransient<SiteVehicleNormalCardAccessCollectionContainer, SiteVehicleNormalCardAccessCollectionContainer>();
			services.AddTransient<SiteVehicleNormalCardAccessObjectsDataSet, SiteVehicleNormalCardAccessObjectsDataSet>();
			services.AddTransient<ImportJobStatusDataObject, ImportJobStatusDataObject>();
			services.AddTransient<ImportJobStatusContainer, ImportJobStatusContainer>();
			services.AddTransient<ImportJobStatusCollectionContainer, ImportJobStatusCollectionContainer>();
			services.AddTransient<ImportJobStatusObjectsDataSet, ImportJobStatusObjectsDataSet>();
			services.AddTransient<VehicleOtherSettingsDataObject, VehicleOtherSettingsDataObject>();
			services.AddTransient<VehicleOtherSettingsContainer, VehicleOtherSettingsContainer>();
			services.AddTransient<VehicleOtherSettingsCollectionContainer, VehicleOtherSettingsCollectionContainer>();
			services.AddTransient<VehicleOtherSettingsObjectsDataSet, VehicleOtherSettingsObjectsDataSet>();
			services.AddTransient<DashboardFilterMoreFieldsDataObject, DashboardFilterMoreFieldsDataObject>();
			services.AddTransient<DashboardFilterMoreFieldsContainer, DashboardFilterMoreFieldsContainer>();
			services.AddTransient<DashboardFilterMoreFieldsCollectionContainer, DashboardFilterMoreFieldsCollectionContainer>();
			services.AddTransient<DashboardFilterMoreFieldsObjectsDataSet, DashboardFilterMoreFieldsObjectsDataSet>();
			services.AddTransient<VehicleSupervisorsViewDataObject, VehicleSupervisorsViewDataObject>();
			services.AddTransient<VehicleSupervisorsViewContainer, VehicleSupervisorsViewContainer>();
			services.AddTransient<VehicleSupervisorsViewCollectionContainer, VehicleSupervisorsViewCollectionContainer>();
			services.AddTransient<VehicleSupervisorsViewObjectsDataSet, VehicleSupervisorsViewObjectsDataSet>();
			services.AddTransient<ExportJobStatusDataObject, ExportJobStatusDataObject>();
			services.AddTransient<ExportJobStatusContainer, ExportJobStatusContainer>();
			services.AddTransient<ExportJobStatusCollectionContainer, ExportJobStatusCollectionContainer>();
			services.AddTransient<ExportJobStatusObjectsDataSet, ExportJobStatusObjectsDataSet>();
			services.AddTransient<TimezoneDataObject, TimezoneDataObject>();
			services.AddTransient<TimezoneContainer, TimezoneContainer>();
			services.AddTransient<TimezoneCollectionContainer, TimezoneCollectionContainer>();
			services.AddTransient<TimezoneObjectsDataSet, TimezoneObjectsDataSet>();
			services.AddTransient<HireDeHireReportFilterDataObject, HireDeHireReportFilterDataObject>();
			services.AddTransient<HireDeHireReportFilterContainer, HireDeHireReportFilterContainer>();
			services.AddTransient<HireDeHireReportFilterCollectionContainer, HireDeHireReportFilterCollectionContainer>();
			services.AddTransient<HireDeHireReportFilterObjectsDataSet, HireDeHireReportFilterObjectsDataSet>();
			services.AddTransient<LoggedHoursVersusSeatHoursViewDataObject, LoggedHoursVersusSeatHoursViewDataObject>();
			services.AddTransient<LoggedHoursVersusSeatHoursViewContainer, LoggedHoursVersusSeatHoursViewContainer>();
			services.AddTransient<LoggedHoursVersusSeatHoursViewCollectionContainer, LoggedHoursVersusSeatHoursViewCollectionContainer>();
			services.AddTransient<LoggedHoursVersusSeatHoursViewObjectsDataSet, LoggedHoursVersusSeatHoursViewObjectsDataSet>();
			services.AddTransient<PersonAllocationDataObject, PersonAllocationDataObject>();
			services.AddTransient<PersonAllocationContainer, PersonAllocationContainer>();
			services.AddTransient<PersonAllocationCollectionContainer, PersonAllocationCollectionContainer>();
			services.AddTransient<PersonAllocationObjectsDataSet, PersonAllocationObjectsDataSet>();
			services.AddTransient<BroadcastMessageDataObject, BroadcastMessageDataObject>();
			services.AddTransient<BroadcastMessageContainer, BroadcastMessageContainer>();
			services.AddTransient<BroadcastMessageCollectionContainer, BroadcastMessageCollectionContainer>();
			services.AddTransient<BroadcastMessageObjectsDataSet, BroadcastMessageObjectsDataSet>();
			services.AddTransient<VORReportCombinedViewDataObject, VORReportCombinedViewDataObject>();
			services.AddTransient<VORReportCombinedViewContainer, VORReportCombinedViewContainer>();
			services.AddTransient<VORReportCombinedViewCollectionContainer, VORReportCombinedViewCollectionContainer>();
			services.AddTransient<VORReportCombinedViewObjectsDataSet, VORReportCombinedViewObjectsDataSet>();
			services.AddTransient<UnitUtilisationStoreProcedureDataObject, UnitUtilisationStoreProcedureDataObject>();
			services.AddTransient<UnitUtilisationStoreProcedureContainer, UnitUtilisationStoreProcedureContainer>();
			services.AddTransient<UnitUtilisationStoreProcedureCollectionContainer, UnitUtilisationStoreProcedureCollectionContainer>();
			services.AddTransient<UnitUtilisationStoreProcedureObjectsDataSet, UnitUtilisationStoreProcedureObjectsDataSet>();
			services.AddTransient<ChecklistResultDataObject, ChecklistResultDataObject>();
			services.AddTransient<ChecklistResultContainer, ChecklistResultContainer>();
			services.AddTransient<ChecklistResultCollectionContainer, ChecklistResultCollectionContainer>();
			services.AddTransient<ChecklistResultObjectsDataSet, ChecklistResultObjectsDataSet>();
			services.AddTransient<UnitUnutilisationStoreProcedureDataObject, UnitUnutilisationStoreProcedureDataObject>();
			services.AddTransient<UnitUnutilisationStoreProcedureContainer, UnitUnutilisationStoreProcedureContainer>();
			services.AddTransient<UnitUnutilisationStoreProcedureCollectionContainer, UnitUnutilisationStoreProcedureCollectionContainer>();
			services.AddTransient<UnitUnutilisationStoreProcedureObjectsDataSet, UnitUnutilisationStoreProcedureObjectsDataSet>();
			services.AddTransient<VehicleAlertSubscriptionDataObject, VehicleAlertSubscriptionDataObject>();
			services.AddTransient<VehicleAlertSubscriptionContainer, VehicleAlertSubscriptionContainer>();
			services.AddTransient<VehicleAlertSubscriptionCollectionContainer, VehicleAlertSubscriptionCollectionContainer>();
			services.AddTransient<VehicleAlertSubscriptionObjectsDataSet, VehicleAlertSubscriptionObjectsDataSet>();
			services.AddTransient<OnDemandAuthorisationStoreProcedureDataObject, OnDemandAuthorisationStoreProcedureDataObject>();
			services.AddTransient<OnDemandAuthorisationStoreProcedureContainer, OnDemandAuthorisationStoreProcedureContainer>();
			services.AddTransient<OnDemandAuthorisationStoreProcedureCollectionContainer, OnDemandAuthorisationStoreProcedureCollectionContainer>();
			services.AddTransient<OnDemandAuthorisationStoreProcedureObjectsDataSet, OnDemandAuthorisationStoreProcedureObjectsDataSet>();
			services.AddTransient<ImpactFrequencyPerTimeSlotViewDataObject, ImpactFrequencyPerTimeSlotViewDataObject>();
			services.AddTransient<ImpactFrequencyPerTimeSlotViewContainer, ImpactFrequencyPerTimeSlotViewContainer>();
			services.AddTransient<ImpactFrequencyPerTimeSlotViewCollectionContainer, ImpactFrequencyPerTimeSlotViewCollectionContainer>();
			services.AddTransient<ImpactFrequencyPerTimeSlotViewObjectsDataSet, ImpactFrequencyPerTimeSlotViewObjectsDataSet>();
			services.AddTransient<PersonToSiteVehicleNormalAccessViewDataObject, PersonToSiteVehicleNormalAccessViewDataObject>();
			services.AddTransient<PersonToSiteVehicleNormalAccessViewContainer, PersonToSiteVehicleNormalAccessViewContainer>();
			services.AddTransient<PersonToSiteVehicleNormalAccessViewCollectionContainer, PersonToSiteVehicleNormalAccessViewCollectionContainer>();
			services.AddTransient<PersonToSiteVehicleNormalAccessViewObjectsDataSet, PersonToSiteVehicleNormalAccessViewObjectsDataSet>();
			services.AddTransient<SessionDataObject, SessionDataObject>();
			services.AddTransient<SessionContainer, SessionContainer>();
			services.AddTransient<SessionCollectionContainer, SessionCollectionContainer>();
			services.AddTransient<SessionObjectsDataSet, SessionObjectsDataSet>();
			services.AddTransient<SlamcoreDeviceFilterDataObject, SlamcoreDeviceFilterDataObject>();
			services.AddTransient<SlamcoreDeviceFilterContainer, SlamcoreDeviceFilterContainer>();
			services.AddTransient<SlamcoreDeviceFilterCollectionContainer, SlamcoreDeviceFilterCollectionContainer>();
			services.AddTransient<SlamcoreDeviceFilterObjectsDataSet, SlamcoreDeviceFilterObjectsDataSet>();
			services.AddTransient<DealerDataObject, DealerDataObject>();
			services.AddTransient<DealerContainer, DealerContainer>();
			services.AddTransient<DealerCollectionContainer, DealerCollectionContainer>();
			services.AddTransient<DealerObjectsDataSet, DealerObjectsDataSet>();
			services.AddTransient<GOUserDataObject, GOUserDataObject>();
			services.AddTransient<GOUserContainer, GOUserContainer>();
			services.AddTransient<GOUserCollectionContainer, GOUserCollectionContainer>();
			services.AddTransient<GOUserObjectsDataSet, GOUserObjectsDataSet>();
			services.AddTransient<ServiceSettingsDataObject, ServiceSettingsDataObject>();
			services.AddTransient<ServiceSettingsContainer, ServiceSettingsContainer>();
			services.AddTransient<ServiceSettingsCollectionContainer, ServiceSettingsCollectionContainer>();
			services.AddTransient<ServiceSettingsObjectsDataSet, ServiceSettingsObjectsDataSet>();
			services.AddTransient<UpdateFirmwareRequestDataObject, UpdateFirmwareRequestDataObject>();
			services.AddTransient<UpdateFirmwareRequestContainer, UpdateFirmwareRequestContainer>();
			services.AddTransient<UpdateFirmwareRequestCollectionContainer, UpdateFirmwareRequestCollectionContainer>();
			services.AddTransient<UpdateFirmwareRequestObjectsDataSet, UpdateFirmwareRequestObjectsDataSet>();
			services.AddTransient<SlamcoreAPIKeyDataObject, SlamcoreAPIKeyDataObject>();
			services.AddTransient<SlamcoreAPIKeyContainer, SlamcoreAPIKeyContainer>();
			services.AddTransient<SlamcoreAPIKeyCollectionContainer, SlamcoreAPIKeyCollectionContainer>();
			services.AddTransient<SlamcoreAPIKeyObjectsDataSet, SlamcoreAPIKeyObjectsDataSet>();
			services.AddTransient<FirmwareDataObject, FirmwareDataObject>();
			services.AddTransient<FirmwareContainer, FirmwareContainer>();
			services.AddTransient<FirmwareCollectionContainer, FirmwareCollectionContainer>();
			services.AddTransient<FirmwareObjectsDataSet, FirmwareObjectsDataSet>();
			services.AddTransient<CardDataObject, CardDataObject>();
			services.AddTransient<CardContainer, CardContainer>();
			services.AddTransient<CardCollectionContainer, CardCollectionContainer>();
			services.AddTransient<CardObjectsDataSet, CardObjectsDataSet>();
			services.AddTransient<GOUserDepartmentDataObject, GOUserDepartmentDataObject>();
			services.AddTransient<GOUserDepartmentContainer, GOUserDepartmentContainer>();
			services.AddTransient<GOUserDepartmentCollectionContainer, GOUserDepartmentCollectionContainer>();
			services.AddTransient<GOUserDepartmentObjectsDataSet, GOUserDepartmentObjectsDataSet>();
			services.AddTransient<CanruleDataObject, CanruleDataObject>();
			services.AddTransient<CanruleContainer, CanruleContainer>();
			services.AddTransient<CanruleCollectionContainer, CanruleCollectionContainer>();
			services.AddTransient<CanruleObjectsDataSet, CanruleObjectsDataSet>();
			services.AddTransient<ImpactFrequencyPerWeekDayViewDataObject, ImpactFrequencyPerWeekDayViewDataObject>();
			services.AddTransient<ImpactFrequencyPerWeekDayViewContainer, ImpactFrequencyPerWeekDayViewContainer>();
			services.AddTransient<ImpactFrequencyPerWeekDayViewCollectionContainer, ImpactFrequencyPerWeekDayViewCollectionContainer>();
			services.AddTransient<ImpactFrequencyPerWeekDayViewObjectsDataSet, ImpactFrequencyPerWeekDayViewObjectsDataSet>();
			services.AddTransient<SlamcoreDeviceHistoryDataObject, SlamcoreDeviceHistoryDataObject>();
			services.AddTransient<SlamcoreDeviceHistoryContainer, SlamcoreDeviceHistoryContainer>();
			services.AddTransient<SlamcoreDeviceHistoryCollectionContainer, SlamcoreDeviceHistoryCollectionContainer>();
			services.AddTransient<SlamcoreDeviceHistoryObjectsDataSet, SlamcoreDeviceHistoryObjectsDataSet>();
			services.AddTransient<VehicleHireDehireHistoryDataObject, VehicleHireDehireHistoryDataObject>();
			services.AddTransient<VehicleHireDehireHistoryContainer, VehicleHireDehireHistoryContainer>();
			services.AddTransient<VehicleHireDehireHistoryCollectionContainer, VehicleHireDehireHistoryCollectionContainer>();
			services.AddTransient<VehicleHireDehireHistoryObjectsDataSet, VehicleHireDehireHistoryObjectsDataSet>();
			services.AddTransient<ChecklistFailurePerVechicleViewDataObject, ChecklistFailurePerVechicleViewDataObject>();
			services.AddTransient<ChecklistFailurePerVechicleViewContainer, ChecklistFailurePerVechicleViewContainer>();
			services.AddTransient<ChecklistFailurePerVechicleViewCollectionContainer, ChecklistFailurePerVechicleViewCollectionContainer>();
			services.AddTransient<ChecklistFailurePerVechicleViewObjectsDataSet, ChecklistFailurePerVechicleViewObjectsDataSet>();
			services.AddTransient<ImpactReportFilterDataObject, ImpactReportFilterDataObject>();
			services.AddTransient<ImpactReportFilterContainer, ImpactReportFilterContainer>();
			services.AddTransient<ImpactReportFilterCollectionContainer, ImpactReportFilterCollectionContainer>();
			services.AddTransient<ImpactReportFilterObjectsDataSet, ImpactReportFilterObjectsDataSet>();
			services.AddTransient<IncompletedChecklistViewDataObject, IncompletedChecklistViewDataObject>();
			services.AddTransient<IncompletedChecklistViewContainer, IncompletedChecklistViewContainer>();
			services.AddTransient<IncompletedChecklistViewCollectionContainer, IncompletedChecklistViewCollectionContainer>();
			services.AddTransient<IncompletedChecklistViewObjectsDataSet, IncompletedChecklistViewObjectsDataSet>();
			services.AddTransient<ReportSubscriptionDataObject, ReportSubscriptionDataObject>();
			services.AddTransient<ReportSubscriptionContainer, ReportSubscriptionContainer>();
			services.AddTransient<ReportSubscriptionCollectionContainer, ReportSubscriptionCollectionContainer>();
			services.AddTransient<ReportSubscriptionObjectsDataSet, ReportSubscriptionObjectsDataSet>();
			services.AddTransient<VehicleLastGPSLocationViewDataObject, VehicleLastGPSLocationViewDataObject>();
			services.AddTransient<VehicleLastGPSLocationViewContainer, VehicleLastGPSLocationViewContainer>();
			services.AddTransient<VehicleLastGPSLocationViewCollectionContainer, VehicleLastGPSLocationViewCollectionContainer>();
			services.AddTransient<VehicleLastGPSLocationViewObjectsDataSet, VehicleLastGPSLocationViewObjectsDataSet>();
			services.AddTransient<PSTATDetailsDataObject, PSTATDetailsDataObject>();
			services.AddTransient<PSTATDetailsContainer, PSTATDetailsContainer>();
			services.AddTransient<PSTATDetailsCollectionContainer, PSTATDetailsCollectionContainer>();
			services.AddTransient<PSTATDetailsObjectsDataSet, PSTATDetailsObjectsDataSet>();
			services.AddTransient<LicenseExpiryReportFilterDataObject, LicenseExpiryReportFilterDataObject>();
			services.AddTransient<LicenseExpiryReportFilterContainer, LicenseExpiryReportFilterContainer>();
			services.AddTransient<LicenseExpiryReportFilterCollectionContainer, LicenseExpiryReportFilterCollectionContainer>();
			services.AddTransient<LicenseExpiryReportFilterObjectsDataSet, LicenseExpiryReportFilterObjectsDataSet>();
			services.AddTransient<FloorZonesDataObject, FloorZonesDataObject>();
			services.AddTransient<FloorZonesContainer, FloorZonesContainer>();
			services.AddTransient<FloorZonesCollectionContainer, FloorZonesCollectionContainer>();
			services.AddTransient<FloorZonesObjectsDataSet, FloorZonesObjectsDataSet>();
			services.AddTransient<CanruleDetailsDataObject, CanruleDetailsDataObject>();
			services.AddTransient<CanruleDetailsContainer, CanruleDetailsContainer>();
			services.AddTransient<CanruleDetailsCollectionContainer, CanruleDetailsCollectionContainer>();
			services.AddTransient<CanruleDetailsObjectsDataSet, CanruleDetailsObjectsDataSet>();
			services.AddTransient<VehicleHireDehireSynchronizationOptionsDataObject, VehicleHireDehireSynchronizationOptionsDataObject>();
			services.AddTransient<VehicleHireDehireSynchronizationOptionsContainer, VehicleHireDehireSynchronizationOptionsContainer>();
			services.AddTransient<VehicleHireDehireSynchronizationOptionsCollectionContainer, VehicleHireDehireSynchronizationOptionsCollectionContainer>();
			services.AddTransient<VehicleHireDehireSynchronizationOptionsObjectsDataSet, VehicleHireDehireSynchronizationOptionsObjectsDataSet>();
			services.AddTransient<DriverLicenseExpiryStoreProcedureDataObject, DriverLicenseExpiryStoreProcedureDataObject>();
			services.AddTransient<DriverLicenseExpiryStoreProcedureContainer, DriverLicenseExpiryStoreProcedureContainer>();
			services.AddTransient<DriverLicenseExpiryStoreProcedureCollectionContainer, DriverLicenseExpiryStoreProcedureCollectionContainer>();
			services.AddTransient<DriverLicenseExpiryStoreProcedureObjectsDataSet, DriverLicenseExpiryStoreProcedureObjectsDataSet>();
			services.AddTransient<UnitSummaryReportDataObject, UnitSummaryReportDataObject>();
			services.AddTransient<UnitSummaryReportContainer, UnitSummaryReportContainer>();
			services.AddTransient<UnitSummaryReportCollectionContainer, UnitSummaryReportCollectionContainer>();
			services.AddTransient<UnitSummaryReportObjectsDataSet, UnitSummaryReportObjectsDataSet>();
			services.AddTransient<DepartmentVehicleMasterCardAccessDataObject, DepartmentVehicleMasterCardAccessDataObject>();
			services.AddTransient<DepartmentVehicleMasterCardAccessContainer, DepartmentVehicleMasterCardAccessContainer>();
			services.AddTransient<DepartmentVehicleMasterCardAccessCollectionContainer, DepartmentVehicleMasterCardAccessCollectionContainer>();
			services.AddTransient<DepartmentVehicleMasterCardAccessObjectsDataSet, DepartmentVehicleMasterCardAccessObjectsDataSet>();
			services.AddTransient<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject, VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>();
			services.AddTransient<VehicleUtilizationLastTwelveHoursStoreProcedureContainer, VehicleUtilizationLastTwelveHoursStoreProcedureContainer>();
			services.AddTransient<VehicleUtilizationLastTwelveHoursStoreProcedureCollectionContainer, VehicleUtilizationLastTwelveHoursStoreProcedureCollectionContainer>();
			services.AddTransient<VehicleUtilizationLastTwelveHoursStoreProcedureObjectsDataSet, VehicleUtilizationLastTwelveHoursStoreProcedureObjectsDataSet>();
			services.AddTransient<ChecklistFailureViewDataObject, ChecklistFailureViewDataObject>();
			services.AddTransient<ChecklistFailureViewContainer, ChecklistFailureViewContainer>();
			services.AddTransient<ChecklistFailureViewCollectionContainer, ChecklistFailureViewCollectionContainer>();
			services.AddTransient<ChecklistFailureViewObjectsDataSet, ChecklistFailureViewObjectsDataSet>();
			services.AddTransient<PersonChecklistLanguageSettingsDataObject, PersonChecklistLanguageSettingsDataObject>();
			services.AddTransient<PersonChecklistLanguageSettingsContainer, PersonChecklistLanguageSettingsContainer>();
			services.AddTransient<PersonChecklistLanguageSettingsCollectionContainer, PersonChecklistLanguageSettingsCollectionContainer>();
			services.AddTransient<PersonChecklistLanguageSettingsObjectsDataSet, PersonChecklistLanguageSettingsObjectsDataSet>();
			services.AddTransient<VehicleSessionlessImpactDataObject, VehicleSessionlessImpactDataObject>();
			services.AddTransient<VehicleSessionlessImpactContainer, VehicleSessionlessImpactContainer>();
			services.AddTransient<VehicleSessionlessImpactCollectionContainer, VehicleSessionlessImpactCollectionContainer>();
			services.AddTransient<VehicleSessionlessImpactObjectsDataSet, VehicleSessionlessImpactObjectsDataSet>();
			services.AddTransient<LicenseByModelDataObject, LicenseByModelDataObject>();
			services.AddTransient<LicenseByModelContainer, LicenseByModelContainer>();
			services.AddTransient<LicenseByModelCollectionContainer, LicenseByModelCollectionContainer>();
			services.AddTransient<LicenseByModelObjectsDataSet, LicenseByModelObjectsDataSet>();
			services.AddTransient<AllMessageHistoryStoreProcedureDataObject, AllMessageHistoryStoreProcedureDataObject>();
			services.AddTransient<AllMessageHistoryStoreProcedureContainer, AllMessageHistoryStoreProcedureContainer>();
			services.AddTransient<AllMessageHistoryStoreProcedureCollectionContainer, AllMessageHistoryStoreProcedureCollectionContainer>();
			services.AddTransient<AllMessageHistoryStoreProcedureObjectsDataSet, AllMessageHistoryStoreProcedureObjectsDataSet>();
			services.AddTransient<IoTDeviceMessageCacheDataObject, IoTDeviceMessageCacheDataObject>();
			services.AddTransient<IoTDeviceMessageCacheContainer, IoTDeviceMessageCacheContainer>();
			services.AddTransient<IoTDeviceMessageCacheCollectionContainer, IoTDeviceMessageCacheCollectionContainer>();
			services.AddTransient<IoTDeviceMessageCacheObjectsDataSet, IoTDeviceMessageCacheObjectsDataSet>();
			services.AddTransient<ProficiencyCombinedViewDataObject, ProficiencyCombinedViewDataObject>();
			services.AddTransient<ProficiencyCombinedViewContainer, ProficiencyCombinedViewContainer>();
			services.AddTransient<ProficiencyCombinedViewCollectionContainer, ProficiencyCombinedViewCollectionContainer>();
			services.AddTransient<ProficiencyCombinedViewObjectsDataSet, ProficiencyCombinedViewObjectsDataSet>();
			services.AddTransient<CustomerSnapshotDataObject, CustomerSnapshotDataObject>();
			services.AddTransient<CustomerSnapshotContainer, CustomerSnapshotContainer>();
			services.AddTransient<CustomerSnapshotCollectionContainer, CustomerSnapshotCollectionContainer>();
			services.AddTransient<CustomerSnapshotObjectsDataSet, CustomerSnapshotObjectsDataSet>();
			services.AddTransient<GOGroupDataObject, GOGroupDataObject>();
			services.AddTransient<GOGroupContainer, GOGroupContainer>();
			services.AddTransient<GOGroupCollectionContainer, GOGroupCollectionContainer>();
			services.AddTransient<GOGroupObjectsDataSet, GOGroupObjectsDataSet>();
			services.AddTransient<VehiclesPerModelReportDataObject, VehiclesPerModelReportDataObject>();
			services.AddTransient<VehiclesPerModelReportContainer, VehiclesPerModelReportContainer>();
			services.AddTransient<VehiclesPerModelReportCollectionContainer, VehiclesPerModelReportCollectionContainer>();
			services.AddTransient<VehiclesPerModelReportObjectsDataSet, VehiclesPerModelReportObjectsDataSet>();
			services.AddTransient<TodaysImpactStoreProcedureDataObject, TodaysImpactStoreProcedureDataObject>();
			services.AddTransient<TodaysImpactStoreProcedureContainer, TodaysImpactStoreProcedureContainer>();
			services.AddTransient<TodaysImpactStoreProcedureCollectionContainer, TodaysImpactStoreProcedureCollectionContainer>();
			services.AddTransient<TodaysImpactStoreProcedureObjectsDataSet, TodaysImpactStoreProcedureObjectsDataSet>();
			services.AddTransient<BroadcastMessageHistoryFilterDataObject, BroadcastMessageHistoryFilterDataObject>();
			services.AddTransient<BroadcastMessageHistoryFilterContainer, BroadcastMessageHistoryFilterContainer>();
			services.AddTransient<BroadcastMessageHistoryFilterCollectionContainer, BroadcastMessageHistoryFilterCollectionContainer>();
			services.AddTransient<BroadcastMessageHistoryFilterObjectsDataSet, BroadcastMessageHistoryFilterObjectsDataSet>();
			services.AddTransient<DealerDriverDataObject, DealerDriverDataObject>();
			services.AddTransient<DealerDriverContainer, DealerDriverContainer>();
			services.AddTransient<DealerDriverCollectionContainer, DealerDriverCollectionContainer>();
			services.AddTransient<DealerDriverObjectsDataSet, DealerDriverObjectsDataSet>();
			services.AddTransient<EmailGroupsDataObject, EmailGroupsDataObject>();
			services.AddTransient<EmailGroupsContainer, EmailGroupsContainer>();
			services.AddTransient<EmailGroupsCollectionContainer, EmailGroupsCollectionContainer>();
			services.AddTransient<EmailGroupsObjectsDataSet, EmailGroupsObjectsDataSet>();
			services.AddTransient<PreOperationalChecklistDataObject, PreOperationalChecklistDataObject>();
			services.AddTransient<PreOperationalChecklistContainer, PreOperationalChecklistContainer>();
			services.AddTransient<PreOperationalChecklistCollectionContainer, PreOperationalChecklistCollectionContainer>();
			services.AddTransient<PreOperationalChecklistObjectsDataSet, PreOperationalChecklistObjectsDataSet>();
			services.AddTransient<SynchronizationStatusReportFilterDataObject, SynchronizationStatusReportFilterDataObject>();
			services.AddTransient<SynchronizationStatusReportFilterContainer, SynchronizationStatusReportFilterContainer>();
			services.AddTransient<SynchronizationStatusReportFilterCollectionContainer, SynchronizationStatusReportFilterCollectionContainer>();
			services.AddTransient<SynchronizationStatusReportFilterObjectsDataSet, SynchronizationStatusReportFilterObjectsDataSet>();
			services.AddTransient<SlamcoreDeviceDataObject, SlamcoreDeviceDataObject>();
			services.AddTransient<SlamcoreDeviceContainer, SlamcoreDeviceContainer>();
			services.AddTransient<SlamcoreDeviceCollectionContainer, SlamcoreDeviceCollectionContainer>();
			services.AddTransient<SlamcoreDeviceObjectsDataSet, SlamcoreDeviceObjectsDataSet>();
			services.AddTransient<SiteDataObject, SiteDataObject>();
			services.AddTransient<SiteContainer, SiteContainer>();
			services.AddTransient<SiteCollectionContainer, SiteCollectionContainer>();
			services.AddTransient<SiteObjectsDataSet, SiteObjectsDataSet>();
			services.AddTransient<AlertSubscriptionDataObject, AlertSubscriptionDataObject>();
			services.AddTransient<AlertSubscriptionContainer, AlertSubscriptionContainer>();
			services.AddTransient<AlertSubscriptionCollectionContainer, AlertSubscriptionCollectionContainer>();
			services.AddTransient<AlertSubscriptionObjectsDataSet, AlertSubscriptionObjectsDataSet>();
			services.AddTransient<CustomerAuditDataObject, CustomerAuditDataObject>();
			services.AddTransient<CustomerAuditContainer, CustomerAuditContainer>();
			services.AddTransient<CustomerAuditCollectionContainer, CustomerAuditCollectionContainer>();
			services.AddTransient<CustomerAuditObjectsDataSet, CustomerAuditObjectsDataSet>();
			services.AddTransient<PerVehicleNormalCardAccessDataObject, PerVehicleNormalCardAccessDataObject>();
			services.AddTransient<PerVehicleNormalCardAccessContainer, PerVehicleNormalCardAccessContainer>();
			services.AddTransient<PerVehicleNormalCardAccessCollectionContainer, PerVehicleNormalCardAccessCollectionContainer>();
			services.AddTransient<PerVehicleNormalCardAccessObjectsDataSet, PerVehicleNormalCardAccessObjectsDataSet>();
			services.AddTransient<PedestrianDetectionHistoryDataObject, PedestrianDetectionHistoryDataObject>();
			services.AddTransient<PedestrianDetectionHistoryContainer, PedestrianDetectionHistoryContainer>();
			services.AddTransient<PedestrianDetectionHistoryCollectionContainer, PedestrianDetectionHistoryCollectionContainer>();
			services.AddTransient<PedestrianDetectionHistoryObjectsDataSet, PedestrianDetectionHistoryObjectsDataSet>();
			services.AddTransient<PersonToPerVehicleNormalAccessViewDataObject, PersonToPerVehicleNormalAccessViewDataObject>();
			services.AddTransient<PersonToPerVehicleNormalAccessViewContainer, PersonToPerVehicleNormalAccessViewContainer>();
			services.AddTransient<PersonToPerVehicleNormalAccessViewCollectionContainer, PersonToPerVehicleNormalAccessViewCollectionContainer>();
			services.AddTransient<PersonToPerVehicleNormalAccessViewObjectsDataSet, PersonToPerVehicleNormalAccessViewObjectsDataSet>();
			services.AddTransient<DashboardDriverCardStoreProcedureDataObject, DashboardDriverCardStoreProcedureDataObject>();
			services.AddTransient<DashboardDriverCardStoreProcedureContainer, DashboardDriverCardStoreProcedureContainer>();
			services.AddTransient<DashboardDriverCardStoreProcedureCollectionContainer, DashboardDriverCardStoreProcedureCollectionContainer>();
			services.AddTransient<DashboardDriverCardStoreProcedureObjectsDataSet, DashboardDriverCardStoreProcedureObjectsDataSet>();
			services.AddTransient<TodaysImpactViewDataObject, TodaysImpactViewDataObject>();
			services.AddTransient<TodaysImpactViewContainer, TodaysImpactViewContainer>();
			services.AddTransient<TodaysImpactViewCollectionContainer, TodaysImpactViewCollectionContainer>();
			services.AddTransient<TodaysImpactViewObjectsDataSet, TodaysImpactViewObjectsDataSet>();
			services.AddTransient<DetailedSessionViewDataObject, DetailedSessionViewDataObject>();
			services.AddTransient<DetailedSessionViewContainer, DetailedSessionViewContainer>();
			services.AddTransient<DetailedSessionViewCollectionContainer, DetailedSessionViewCollectionContainer>();
			services.AddTransient<DetailedSessionViewObjectsDataSet, DetailedSessionViewObjectsDataSet>();
			services.AddTransient<ImportJobLogDataObject, ImportJobLogDataObject>();
			services.AddTransient<ImportJobLogContainer, ImportJobLogContainer>();
			services.AddTransient<ImportJobLogCollectionContainer, ImportJobLogCollectionContainer>();
			services.AddTransient<ImportJobLogObjectsDataSet, ImportJobLogObjectsDataSet>();
			services.AddTransient<ProficiencyReportFilterDataObject, ProficiencyReportFilterDataObject>();
			services.AddTransient<ProficiencyReportFilterContainer, ProficiencyReportFilterContainer>();
			services.AddTransient<ProficiencyReportFilterCollectionContainer, ProficiencyReportFilterCollectionContainer>();
			services.AddTransient<ProficiencyReportFilterObjectsDataSet, ProficiencyReportFilterObjectsDataSet>();
			services.AddTransient<OnDemandSessionDataObject, OnDemandSessionDataObject>();
			services.AddTransient<OnDemandSessionContainer, OnDemandSessionContainer>();
			services.AddTransient<OnDemandSessionCollectionContainer, OnDemandSessionCollectionContainer>();
			services.AddTransient<OnDemandSessionObjectsDataSet, OnDemandSessionObjectsDataSet>();
			services.AddTransient<AllVORStatusStoreProcedureDataObject, AllVORStatusStoreProcedureDataObject>();
			services.AddTransient<AllVORStatusStoreProcedureContainer, AllVORStatusStoreProcedureContainer>();
			services.AddTransient<AllVORStatusStoreProcedureCollectionContainer, AllVORStatusStoreProcedureCollectionContainer>();
			services.AddTransient<AllVORStatusStoreProcedureObjectsDataSet, AllVORStatusStoreProcedureObjectsDataSet>();
			services.AddTransient<VORSettingHistoryDataObject, VORSettingHistoryDataObject>();
			services.AddTransient<VORSettingHistoryContainer, VORSettingHistoryContainer>();
			services.AddTransient<VORSettingHistoryCollectionContainer, VORSettingHistoryCollectionContainer>();
			services.AddTransient<VORSettingHistoryObjectsDataSet, VORSettingHistoryObjectsDataSet>();
			services.AddTransient<AllUserSummaryStoreProcedureDataObject, AllUserSummaryStoreProcedureDataObject>();
			services.AddTransient<AllUserSummaryStoreProcedureContainer, AllUserSummaryStoreProcedureContainer>();
			services.AddTransient<AllUserSummaryStoreProcedureCollectionContainer, AllUserSummaryStoreProcedureCollectionContainer>();
			services.AddTransient<AllUserSummaryStoreProcedureObjectsDataSet, AllUserSummaryStoreProcedureObjectsDataSet>();
			services.AddTransient<CustomerSSODetailDataObject, CustomerSSODetailDataObject>();
			services.AddTransient<CustomerSSODetailContainer, CustomerSSODetailContainer>();
			services.AddTransient<CustomerSSODetailCollectionContainer, CustomerSSODetailCollectionContainer>();
			services.AddTransient<CustomerSSODetailObjectsDataSet, CustomerSSODetailObjectsDataSet>();
			services.AddTransient<UploadLogoRequestDataObject, UploadLogoRequestDataObject>();
			services.AddTransient<UploadLogoRequestContainer, UploadLogoRequestContainer>();
			services.AddTransient<UploadLogoRequestCollectionContainer, UploadLogoRequestCollectionContainer>();
			services.AddTransient<UploadLogoRequestObjectsDataSet, UploadLogoRequestObjectsDataSet>();
			services.AddTransient<AllVehicleCalibrationFilterDataObject, AllVehicleCalibrationFilterDataObject>();
			services.AddTransient<AllVehicleCalibrationFilterContainer, AllVehicleCalibrationFilterContainer>();
			services.AddTransient<AllVehicleCalibrationFilterCollectionContainer, AllVehicleCalibrationFilterCollectionContainer>();
			services.AddTransient<AllVehicleCalibrationFilterObjectsDataSet, AllVehicleCalibrationFilterObjectsDataSet>();
			services.AddTransient<DepartmentHourSettingsDataObject, DepartmentHourSettingsDataObject>();
			services.AddTransient<DepartmentHourSettingsContainer, DepartmentHourSettingsContainer>();
			services.AddTransient<DepartmentHourSettingsCollectionContainer, DepartmentHourSettingsCollectionContainer>();
			services.AddTransient<DepartmentHourSettingsObjectsDataSet, DepartmentHourSettingsObjectsDataSet>();
			services.AddTransient<CategoryTemplateDataObject, CategoryTemplateDataObject>();
			services.AddTransient<CategoryTemplateContainer, CategoryTemplateContainer>();
			services.AddTransient<CategoryTemplateCollectionContainer, CategoryTemplateCollectionContainer>();
			services.AddTransient<CategoryTemplateObjectsDataSet, CategoryTemplateObjectsDataSet>();
			services.AddTransient<DashboardVehicleCardViewDataObject, DashboardVehicleCardViewDataObject>();
			services.AddTransient<DashboardVehicleCardViewContainer, DashboardVehicleCardViewContainer>();
			services.AddTransient<DashboardVehicleCardViewCollectionContainer, DashboardVehicleCardViewCollectionContainer>();
			services.AddTransient<DashboardVehicleCardViewObjectsDataSet, DashboardVehicleCardViewObjectsDataSet>();
			services.AddTransient<CurrentStatusDriverViewDataObject, CurrentStatusDriverViewDataObject>();
			services.AddTransient<CurrentStatusDriverViewContainer, CurrentStatusDriverViewContainer>();
			services.AddTransient<CurrentStatusDriverViewCollectionContainer, CurrentStatusDriverViewCollectionContainer>();
			services.AddTransient<CurrentStatusDriverViewObjectsDataSet, CurrentStatusDriverViewObjectsDataSet>();
			services.AddTransient<UnitSummaryStoreProcedureDataObject, UnitSummaryStoreProcedureDataObject>();
			services.AddTransient<UnitSummaryStoreProcedureContainer, UnitSummaryStoreProcedureContainer>();
			services.AddTransient<UnitSummaryStoreProcedureCollectionContainer, UnitSummaryStoreProcedureCollectionContainer>();
			services.AddTransient<UnitSummaryStoreProcedureObjectsDataSet, UnitSummaryStoreProcedureObjectsDataSet>();
			services.AddTransient<ReportTypeDataObject, ReportTypeDataObject>();
			services.AddTransient<ReportTypeContainer, ReportTypeContainer>();
			services.AddTransient<ReportTypeCollectionContainer, ReportTypeCollectionContainer>();
			services.AddTransient<ReportTypeObjectsDataSet, ReportTypeObjectsDataSet>();
			services.AddTransient<SiteVehicleMasterCardAccessDataObject, SiteVehicleMasterCardAccessDataObject>();
			services.AddTransient<SiteVehicleMasterCardAccessContainer, SiteVehicleMasterCardAccessContainer>();
			services.AddTransient<SiteVehicleMasterCardAccessCollectionContainer, SiteVehicleMasterCardAccessCollectionContainer>();
			services.AddTransient<SiteVehicleMasterCardAccessObjectsDataSet, SiteVehicleMasterCardAccessObjectsDataSet>();
			services.AddTransient<PersonToDepartmentVehicleNormalAccessViewDataObject, PersonToDepartmentVehicleNormalAccessViewDataObject>();
			services.AddTransient<PersonToDepartmentVehicleNormalAccessViewContainer, PersonToDepartmentVehicleNormalAccessViewContainer>();
			services.AddTransient<PersonToDepartmentVehicleNormalAccessViewCollectionContainer, PersonToDepartmentVehicleNormalAccessViewCollectionContainer>();
			services.AddTransient<PersonToDepartmentVehicleNormalAccessViewObjectsDataSet, PersonToDepartmentVehicleNormalAccessViewObjectsDataSet>();
			services.AddTransient<GOUserGroupDataObject, GOUserGroupDataObject>();
			services.AddTransient<GOUserGroupContainer, GOUserGroupContainer>();
			services.AddTransient<GOUserGroupCollectionContainer, GOUserGroupCollectionContainer>();
			services.AddTransient<GOUserGroupObjectsDataSet, GOUserGroupObjectsDataSet>();
			services.AddTransient<CustomerFeatureSubscriptionDataObject, CustomerFeatureSubscriptionDataObject>();
			services.AddTransient<CustomerFeatureSubscriptionContainer, CustomerFeatureSubscriptionContainer>();
			services.AddTransient<CustomerFeatureSubscriptionCollectionContainer, CustomerFeatureSubscriptionCollectionContainer>();
			services.AddTransient<CustomerFeatureSubscriptionObjectsDataSet, CustomerFeatureSubscriptionObjectsDataSet>();
			services.AddTransient<VehicleToPreOpChecklistViewDataObject, VehicleToPreOpChecklistViewDataObject>();
			services.AddTransient<VehicleToPreOpChecklistViewContainer, VehicleToPreOpChecklistViewContainer>();
			services.AddTransient<VehicleToPreOpChecklistViewCollectionContainer, VehicleToPreOpChecklistViewCollectionContainer>();
			services.AddTransient<VehicleToPreOpChecklistViewObjectsDataSet, VehicleToPreOpChecklistViewObjectsDataSet>();
			services.AddTransient<PersonToSiteVehicleMasterAccessViewDataObject, PersonToSiteVehicleMasterAccessViewDataObject>();
			services.AddTransient<PersonToSiteVehicleMasterAccessViewContainer, PersonToSiteVehicleMasterAccessViewContainer>();
			services.AddTransient<PersonToSiteVehicleMasterAccessViewCollectionContainer, PersonToSiteVehicleMasterAccessViewCollectionContainer>();
			services.AddTransient<PersonToSiteVehicleMasterAccessViewObjectsDataSet, PersonToSiteVehicleMasterAccessViewObjectsDataSet>();
			services.AddTransient<DealerConfigurationDataObject, DealerConfigurationDataObject>();
			services.AddTransient<DealerConfigurationContainer, DealerConfigurationContainer>();
			services.AddTransient<DealerConfigurationCollectionContainer, DealerConfigurationCollectionContainer>();
			services.AddTransient<DealerConfigurationObjectsDataSet, DealerConfigurationObjectsDataSet>();
			services.AddTransient<TodaysPreopCheckStoreProcedureDataObject, TodaysPreopCheckStoreProcedureDataObject>();
			services.AddTransient<TodaysPreopCheckStoreProcedureContainer, TodaysPreopCheckStoreProcedureContainer>();
			services.AddTransient<TodaysPreopCheckStoreProcedureCollectionContainer, TodaysPreopCheckStoreProcedureCollectionContainer>();
			services.AddTransient<TodaysPreopCheckStoreProcedureObjectsDataSet, TodaysPreopCheckStoreProcedureObjectsDataSet>();
			services.AddTransient<CustomerPreOperationalChecklistTemplateDataObject, CustomerPreOperationalChecklistTemplateDataObject>();
			services.AddTransient<CustomerPreOperationalChecklistTemplateContainer, CustomerPreOperationalChecklistTemplateContainer>();
			services.AddTransient<CustomerPreOperationalChecklistTemplateCollectionContainer, CustomerPreOperationalChecklistTemplateCollectionContainer>();
			services.AddTransient<CustomerPreOperationalChecklistTemplateObjectsDataSet, CustomerPreOperationalChecklistTemplateObjectsDataSet>();
			services.AddTransient<CurrentDriverStatusChartViewDataObject, CurrentDriverStatusChartViewDataObject>();
			services.AddTransient<CurrentDriverStatusChartViewContainer, CurrentDriverStatusChartViewContainer>();
			services.AddTransient<CurrentDriverStatusChartViewCollectionContainer, CurrentDriverStatusChartViewCollectionContainer>();
			services.AddTransient<CurrentDriverStatusChartViewObjectsDataSet, CurrentDriverStatusChartViewObjectsDataSet>();
			services.AddTransient<GOChangeDeltaDataObject, GOChangeDeltaDataObject>();
			services.AddTransient<GOChangeDeltaContainer, GOChangeDeltaContainer>();
			services.AddTransient<GOChangeDeltaCollectionContainer, GOChangeDeltaCollectionContainer>();
			services.AddTransient<GOChangeDeltaObjectsDataSet, GOChangeDeltaObjectsDataSet>();
			services.AddTransient<GO2FAConfigurationDataObject, GO2FAConfigurationDataObject>();
			services.AddTransient<GO2FAConfigurationContainer, GO2FAConfigurationContainer>();
			services.AddTransient<GO2FAConfigurationCollectionContainer, GO2FAConfigurationCollectionContainer>();
			services.AddTransient<GO2FAConfigurationObjectsDataSet, GO2FAConfigurationObjectsDataSet>();
			services.AddTransient<PreOpReportFilterDataObject, PreOpReportFilterDataObject>();
			services.AddTransient<PreOpReportFilterContainer, PreOpReportFilterContainer>();
			services.AddTransient<PreOpReportFilterCollectionContainer, PreOpReportFilterCollectionContainer>();
			services.AddTransient<PreOpReportFilterObjectsDataSet, PreOpReportFilterObjectsDataSet>();
			services.AddTransient<SiteFloorPlanDataObject, SiteFloorPlanDataObject>();
			services.AddTransient<SiteFloorPlanContainer, SiteFloorPlanContainer>();
			services.AddTransient<SiteFloorPlanCollectionContainer, SiteFloorPlanCollectionContainer>();
			services.AddTransient<SiteFloorPlanObjectsDataSet, SiteFloorPlanObjectsDataSet>();
			services.AddTransient<BroadcastMessageHistoryDataObject, BroadcastMessageHistoryDataObject>();
			services.AddTransient<BroadcastMessageHistoryContainer, BroadcastMessageHistoryContainer>();
			services.AddTransient<BroadcastMessageHistoryCollectionContainer, BroadcastMessageHistoryCollectionContainer>();
			services.AddTransient<BroadcastMessageHistoryObjectsDataSet, BroadcastMessageHistoryObjectsDataSet>();
			services.AddTransient<GoUserToCustomerDataObject, GoUserToCustomerDataObject>();
			services.AddTransient<GoUserToCustomerContainer, GoUserToCustomerContainer>();
			services.AddTransient<GoUserToCustomerCollectionContainer, GoUserToCustomerCollectionContainer>();
			services.AddTransient<GoUserToCustomerObjectsDataSet, GoUserToCustomerObjectsDataSet>();
			services.AddTransient<ImportJobBatchDataObject, ImportJobBatchDataObject>();
			services.AddTransient<ImportJobBatchContainer, ImportJobBatchContainer>();
			services.AddTransient<ImportJobBatchCollectionContainer, ImportJobBatchCollectionContainer>();
			services.AddTransient<ImportJobBatchObjectsDataSet, ImportJobBatchObjectsDataSet>();
			services.AddTransient<PersonDataObject, PersonDataObject>();
			services.AddTransient<PersonContainer, PersonContainer>();
			services.AddTransient<PersonCollectionContainer, PersonCollectionContainer>();
			services.AddTransient<PersonObjectsDataSet, PersonObjectsDataSet>();
			services.AddTransient<DetailedVORSessionStoreProcedureDataObject, DetailedVORSessionStoreProcedureDataObject>();
			services.AddTransient<DetailedVORSessionStoreProcedureContainer, DetailedVORSessionStoreProcedureContainer>();
			services.AddTransient<DetailedVORSessionStoreProcedureCollectionContainer, DetailedVORSessionStoreProcedureCollectionContainer>();
			services.AddTransient<DetailedVORSessionStoreProcedureObjectsDataSet, DetailedVORSessionStoreProcedureObjectsDataSet>();
			services.AddTransient<PersonToModelVehicleNormalAccessViewDataObject, PersonToModelVehicleNormalAccessViewDataObject>();
			services.AddTransient<PersonToModelVehicleNormalAccessViewContainer, PersonToModelVehicleNormalAccessViewContainer>();
			services.AddTransient<PersonToModelVehicleNormalAccessViewCollectionContainer, PersonToModelVehicleNormalAccessViewCollectionContainer>();
			services.AddTransient<PersonToModelVehicleNormalAccessViewObjectsDataSet, PersonToModelVehicleNormalAccessViewObjectsDataSet>();
			services.AddTransient<NetworkSettingsDataObject, NetworkSettingsDataObject>();
			services.AddTransient<NetworkSettingsContainer, NetworkSettingsContainer>();
			services.AddTransient<NetworkSettingsCollectionContainer, NetworkSettingsCollectionContainer>();
			services.AddTransient<NetworkSettingsObjectsDataSet, NetworkSettingsObjectsDataSet>();
			services.AddTransient<DepartmentDataObject, DepartmentDataObject>();
			services.AddTransient<DepartmentContainer, DepartmentContainer>();
			services.AddTransient<DepartmentCollectionContainer, DepartmentCollectionContainer>();
			services.AddTransient<DepartmentObjectsDataSet, DepartmentObjectsDataSet>();
			services.AddTransient<MachineUnlockReportFilterDataObject, MachineUnlockReportFilterDataObject>();
			services.AddTransient<MachineUnlockReportFilterContainer, MachineUnlockReportFilterContainer>();
			services.AddTransient<MachineUnlockReportFilterCollectionContainer, MachineUnlockReportFilterCollectionContainer>();
			services.AddTransient<MachineUnlockReportFilterObjectsDataSet, MachineUnlockReportFilterObjectsDataSet>();
			services.AddTransient<PerVehicleMasterCardAccessDataObject, PerVehicleMasterCardAccessDataObject>();
			services.AddTransient<PerVehicleMasterCardAccessContainer, PerVehicleMasterCardAccessContainer>();
			services.AddTransient<PerVehicleMasterCardAccessCollectionContainer, PerVehicleMasterCardAccessCollectionContainer>();
			services.AddTransient<PerVehicleMasterCardAccessObjectsDataSet, PerVehicleMasterCardAccessObjectsDataSet>();
			services.AddTransient<UnitUtilisationCombinedViewDataObject, UnitUtilisationCombinedViewDataObject>();
			services.AddTransient<UnitUtilisationCombinedViewContainer, UnitUtilisationCombinedViewContainer>();
			services.AddTransient<UnitUtilisationCombinedViewCollectionContainer, UnitUtilisationCombinedViewCollectionContainer>();
			services.AddTransient<UnitUtilisationCombinedViewObjectsDataSet, UnitUtilisationCombinedViewObjectsDataSet>();
			services.AddTransient<FeatureSubscriptionsFilterDataObject, FeatureSubscriptionsFilterDataObject>();
			services.AddTransient<FeatureSubscriptionsFilterContainer, FeatureSubscriptionsFilterContainer>();
			services.AddTransient<FeatureSubscriptionsFilterCollectionContainer, FeatureSubscriptionsFilterCollectionContainer>();
			services.AddTransient<FeatureSubscriptionsFilterObjectsDataSet, FeatureSubscriptionsFilterObjectsDataSet>();
			services.AddTransient<OnDemandSettingsDataObject, OnDemandSettingsDataObject>();
			services.AddTransient<OnDemandSettingsContainer, OnDemandSettingsContainer>();
			services.AddTransient<OnDemandSettingsCollectionContainer, OnDemandSettingsCollectionContainer>();
			services.AddTransient<OnDemandSettingsObjectsDataSet, OnDemandSettingsObjectsDataSet>();
            
			services.AddScoped<IDataObjectFactory<ChecklistDetailDataObject>, ChecklistDetailFactory>();
			services.AddScoped<IDataObjectFactory<SlamcoreAwareAuthenticationDetailsDataObject>, SlamcoreAwareAuthenticationDetailsFactory>();
			services.AddScoped<IDataObjectFactory<FeatureSubscriptionTemplateDataObject>, FeatureSubscriptionTemplateFactory>();
			services.AddScoped<IDataObjectFactory<PersonToModelVehicleMasterAccessViewDataObject>, PersonToModelVehicleMasterAccessViewFactory>();
			services.AddScoped<IDataObjectFactory<VehicleLockoutDataObject>, VehicleLockoutFactory>();
			services.AddScoped<IDataObjectFactory<RevisionDataObject>, RevisionFactory>();
			services.AddScoped<IDataObjectFactory<AlertDataObject>, AlertFactory>();
			services.AddScoped<IDataObjectFactory<WebsiteUserDataObject>, WebsiteUserFactory>();
			services.AddScoped<IDataObjectFactory<DriverAccessAbuseFilterDataObject>, DriverAccessAbuseFilterFactory>();
			services.AddScoped<IDataObjectFactory<AllLicenseExpiryViewDataObject>, AllLicenseExpiryViewFactory>();
			services.AddScoped<IDataObjectFactory<DriverLicenseExpiryViewDataObject>, DriverLicenseExpiryViewFactory>();
			services.AddScoped<IDataObjectFactory<ContactPersonInformationDataObject>, ContactPersonInformationFactory>();
			services.AddScoped<IDataObjectFactory<ChecklistSettingsDataObject>, ChecklistSettingsFactory>();
			services.AddScoped<IDataObjectFactory<VehicleDiagnosticDataObject>, VehicleDiagnosticFactory>();
			services.AddScoped<IDataObjectFactory<GOUser2FADataObject>, GOUser2FAFactory>();
			services.AddScoped<IDataObjectFactory<VehicleGPSDataObject>, VehicleGPSFactory>();
			services.AddScoped<IDataObjectFactory<ModuleDataObject>, ModuleFactory>();
			services.AddScoped<IDataObjectFactory<PersonToPerVehicleMasterAccessViewDataObject>, PersonToPerVehicleMasterAccessViewFactory>();
			services.AddScoped<IDataObjectFactory<AccessGroupDataObject>, AccessGroupFactory>();
			services.AddScoped<IDataObjectFactory<HelpDataObject>, HelpFactory>();
			services.AddScoped<IDataObjectFactory<ChecklistStatusViewDataObject>, ChecklistStatusViewFactory>();
			services.AddScoped<IDataObjectFactory<CurrentStatusCombinedViewDataObject>, CurrentStatusCombinedViewFactory>();
			services.AddScoped<IDataObjectFactory<CurrentVehicleStatusChartViewDataObject>, CurrentVehicleStatusChartViewFactory>();
			services.AddScoped<IDataObjectFactory<DealerFeatureSubscriptionDataObject>, DealerFeatureSubscriptionFactory>();
			services.AddScoped<IDataObjectFactory<MessageHistoryDataObject>, MessageHistoryFactory>();
			services.AddScoped<IDataObjectFactory<AccessGroupToSiteDataObject>, AccessGroupToSiteFactory>();
			services.AddScoped<IDataObjectFactory<AllDriverAccessAbuseStoreProcedureDataObject>, AllDriverAccessAbuseStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<CustomerToModelDataObject>, CustomerToModelFactory>();
			services.AddScoped<IDataObjectFactory<AllEmailSubscriptionStoreProcedureDataObject>, AllEmailSubscriptionStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<AlertSummaryStoreProcedureDataObject>, AlertSummaryStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<DashboardVehicleCardStoreProcedureDataObject>, DashboardVehicleCardStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<RegionDataObject>, RegionFactory>();
			services.AddScoped<IDataObjectFactory<ModelDataObject>, ModelFactory>();
			services.AddScoped<IDataObjectFactory<AllVORSessionsPerVehicleStoreProcedureDataObject>, AllVORSessionsPerVehicleStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<SlamcoreDeviceConnectionViewDataObject>, SlamcoreDeviceConnectionViewFactory>();
			services.AddScoped<IDataObjectFactory<AllImpactsViewDataObject>, AllImpactsViewFactory>();
			services.AddScoped<IDataObjectFactory<CustomerDataObject>, CustomerFactory>();
			services.AddScoped<IDataObjectFactory<DepartmentChecklistDataObject>, DepartmentChecklistFactory>();
			services.AddScoped<IDataObjectFactory<ModuleHistoryDataObject>, ModuleHistoryFactory>();
			services.AddScoped<IDataObjectFactory<CountryDataObject>, CountryFactory>();
			services.AddScoped<IDataObjectFactory<AllChecklistResultViewDataObject>, AllChecklistResultViewFactory>();
			services.AddScoped<IDataObjectFactory<GPSHistoryDataObject>, GPSHistoryFactory>();
			services.AddScoped<IDataObjectFactory<ImpactsForVehicleViewDataObject>, ImpactsForVehicleViewFactory>();
			services.AddScoped<IDataObjectFactory<AllVehicleUnlocksViewDataObject>, AllVehicleUnlocksViewFactory>();
			services.AddScoped<IDataObjectFactory<SlamcorePedestrianDetectionDataObject>, SlamcorePedestrianDetectionFactory>();
			services.AddScoped<IDataObjectFactory<ImpactFrequencyPerWeekMonthViewDataObject>, ImpactFrequencyPerWeekMonthViewFactory>();
			services.AddScoped<IDataObjectFactory<VehicleUtilizationLastTwelveHoursViewDataObject>, VehicleUtilizationLastTwelveHoursViewFactory>();
			services.AddScoped<IDataObjectFactory<IOFIELDDataObject>, IOFIELDFactory>();
			services.AddScoped<IDataObjectFactory<EmailDataObject>, EmailFactory>();
			services.AddScoped<IDataObjectFactory<LicenceDetailDataObject>, LicenceDetailFactory>();
			services.AddScoped<IDataObjectFactory<PedestrianDetectionHistoryFilterDataObject>, PedestrianDetectionHistoryFilterFactory>();
			services.AddScoped<IDataObjectFactory<GOLoginHistoryDataObject>, GOLoginHistoryFactory>();
			services.AddScoped<IDataObjectFactory<MainDashboardFilterDataObject>, MainDashboardFilterFactory>();
			services.AddScoped<IDataObjectFactory<AllVehicleCalibrationStoreProcedureDataObject>, AllVehicleCalibrationStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<AccessGroupTemplateDataObject>, AccessGroupTemplateFactory>();
			services.AddScoped<IDataObjectFactory<CardToCardAccessDataObject>, CardToCardAccessFactory>();
			services.AddScoped<IDataObjectFactory<ZoneCoordinatesDataObject>, ZoneCoordinatesFactory>();
			services.AddScoped<IDataObjectFactory<SnapshotDataObject>, SnapshotFactory>();
			services.AddScoped<IDataObjectFactory<GOTaskDataObject>, GOTaskFactory>();
			services.AddScoped<IDataObjectFactory<GeneralProductivityReportFilterDataObject>, GeneralProductivityReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<DepartmentVehicleNormalCardAccessDataObject>, DepartmentVehicleNormalCardAccessFactory>();
			services.AddScoped<IDataObjectFactory<CustomerToPersonViewDataObject>, CustomerToPersonViewFactory>();
			services.AddScoped<IDataObjectFactory<VehicleSlamcoreLocationHistoryDataObject>, VehicleSlamcoreLocationHistoryFactory>();
			services.AddScoped<IDataObjectFactory<PermissionDataObject>, PermissionFactory>();
			services.AddScoped<IDataObjectFactory<SessionDetailsDataObject>, SessionDetailsFactory>();
			services.AddScoped<IDataObjectFactory<GORoleDataObject>, GORoleFactory>();
			services.AddScoped<IDataObjectFactory<GeneralProductivityPerVehicleViewDataObject>, GeneralProductivityPerVehicleViewFactory>();
			services.AddScoped<IDataObjectFactory<VehicleDataObject>, VehicleFactory>();
			services.AddScoped<IDataObjectFactory<GeneralProductivityPerDriverViewLatestDataObject>, GeneralProductivityPerDriverViewLatestFactory>();
			services.AddScoped<IDataObjectFactory<DriverDataObject>, DriverFactory>();
			services.AddScoped<IDataObjectFactory<EmailGroupsToPersonDataObject>, EmailGroupsToPersonFactory>();
			services.AddScoped<IDataObjectFactory<GOGroupRoleDataObject>, GOGroupRoleFactory>();
			services.AddScoped<IDataObjectFactory<ModelVehicleMasterCardAccessDataObject>, ModelVehicleMasterCardAccessFactory>();
			services.AddScoped<IDataObjectFactory<EmailSubscriptionReportFilterDataObject>, EmailSubscriptionReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<VehicleProficiencyViewDataObject>, VehicleProficiencyViewFactory>();
			services.AddScoped<IDataObjectFactory<DashboardDriverCardViewDataObject>, DashboardDriverCardViewFactory>();
			services.AddScoped<IDataObjectFactory<DriverProficiencyViewDataObject>, DriverProficiencyViewFactory>();
			services.AddScoped<IDataObjectFactory<GOSecurityTokensDataObject>, GOSecurityTokensFactory>();
			services.AddScoped<IDataObjectFactory<AlertHistoryDataObject>, AlertHistoryFactory>();
			services.AddScoped<IDataObjectFactory<DashboardFilterDataObject>, DashboardFilterFactory>();
			services.AddScoped<IDataObjectFactory<VORReportFilterDataObject>, VORReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<OnDemandAuthorisationFilterDataObject>, OnDemandAuthorisationFilterFactory>();
			services.AddScoped<IDataObjectFactory<WebsiteRoleDataObject>, WebsiteRoleFactory>();
			services.AddScoped<IDataObjectFactory<GOUserRoleDataObject>, GOUserRoleFactory>();
			services.AddScoped<IDataObjectFactory<GeneralProductivityViewDataObject>, GeneralProductivityViewFactory>();
			services.AddScoped<IDataObjectFactory<InspectionDataObject>, InspectionFactory>();
			services.AddScoped<IDataObjectFactory<ImpactDataObject>, ImpactFactory>();
			services.AddScoped<IDataObjectFactory<PersonToDepartmentVehicleMasterAccessViewDataObject>, PersonToDepartmentVehicleMasterAccessViewFactory>();
			services.AddScoped<IDataObjectFactory<CurrentStatusVehicleViewDataObject>, CurrentStatusVehicleViewFactory>();
			services.AddScoped<IDataObjectFactory<CustomerModelDataObject>, CustomerModelFactory>();
			services.AddScoped<IDataObjectFactory<TagDataObject>, TagFactory>();
			services.AddScoped<IDataObjectFactory<VehicleBroadcastMessageDataObject>, VehicleBroadcastMessageFactory>();
			services.AddScoped<IDataObjectFactory<FloorPlanDataObject>, FloorPlanFactory>();
			services.AddScoped<IDataObjectFactory<TodaysPreopCheckViewDataObject>, TodaysPreopCheckViewFactory>();
			services.AddScoped<IDataObjectFactory<ModelVehicleNormalCardAccessDataObject>, ModelVehicleNormalCardAccessFactory>();
			services.AddScoped<IDataObjectFactory<SiteVehicleNormalCardAccessDataObject>, SiteVehicleNormalCardAccessFactory>();
			services.AddScoped<IDataObjectFactory<ImportJobStatusDataObject>, ImportJobStatusFactory>();
			services.AddScoped<IDataObjectFactory<VehicleOtherSettingsDataObject>, VehicleOtherSettingsFactory>();
			services.AddScoped<IDataObjectFactory<DashboardFilterMoreFieldsDataObject>, DashboardFilterMoreFieldsFactory>();
			services.AddScoped<IDataObjectFactory<VehicleSupervisorsViewDataObject>, VehicleSupervisorsViewFactory>();
			services.AddScoped<IDataObjectFactory<ExportJobStatusDataObject>, ExportJobStatusFactory>();
			services.AddScoped<IDataObjectFactory<TimezoneDataObject>, TimezoneFactory>();
			services.AddScoped<IDataObjectFactory<HireDeHireReportFilterDataObject>, HireDeHireReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<LoggedHoursVersusSeatHoursViewDataObject>, LoggedHoursVersusSeatHoursViewFactory>();
			services.AddScoped<IDataObjectFactory<PersonAllocationDataObject>, PersonAllocationFactory>();
			services.AddScoped<IDataObjectFactory<BroadcastMessageDataObject>, BroadcastMessageFactory>();
			services.AddScoped<IDataObjectFactory<VORReportCombinedViewDataObject>, VORReportCombinedViewFactory>();
			services.AddScoped<IDataObjectFactory<UnitUtilisationStoreProcedureDataObject>, UnitUtilisationStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<ChecklistResultDataObject>, ChecklistResultFactory>();
			services.AddScoped<IDataObjectFactory<UnitUnutilisationStoreProcedureDataObject>, UnitUnutilisationStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<VehicleAlertSubscriptionDataObject>, VehicleAlertSubscriptionFactory>();
			services.AddScoped<IDataObjectFactory<OnDemandAuthorisationStoreProcedureDataObject>, OnDemandAuthorisationStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<ImpactFrequencyPerTimeSlotViewDataObject>, ImpactFrequencyPerTimeSlotViewFactory>();
			services.AddScoped<IDataObjectFactory<PersonToSiteVehicleNormalAccessViewDataObject>, PersonToSiteVehicleNormalAccessViewFactory>();
			services.AddScoped<IDataObjectFactory<SessionDataObject>, SessionFactory>();
			services.AddScoped<IDataObjectFactory<SlamcoreDeviceFilterDataObject>, SlamcoreDeviceFilterFactory>();
			services.AddScoped<IDataObjectFactory<DealerDataObject>, DealerFactory>();
			services.AddScoped<IDataObjectFactory<GOUserDataObject>, GOUserFactory>();
			services.AddScoped<IDataObjectFactory<ServiceSettingsDataObject>, ServiceSettingsFactory>();
			services.AddScoped<IDataObjectFactory<UpdateFirmwareRequestDataObject>, UpdateFirmwareRequestFactory>();
			services.AddScoped<IDataObjectFactory<SlamcoreAPIKeyDataObject>, SlamcoreAPIKeyFactory>();
			services.AddScoped<IDataObjectFactory<FirmwareDataObject>, FirmwareFactory>();
			services.AddScoped<IDataObjectFactory<CardDataObject>, CardFactory>();
			services.AddScoped<IDataObjectFactory<GOUserDepartmentDataObject>, GOUserDepartmentFactory>();
			services.AddScoped<IDataObjectFactory<CanruleDataObject>, CanruleFactory>();
			services.AddScoped<IDataObjectFactory<ImpactFrequencyPerWeekDayViewDataObject>, ImpactFrequencyPerWeekDayViewFactory>();
			services.AddScoped<IDataObjectFactory<SlamcoreDeviceHistoryDataObject>, SlamcoreDeviceHistoryFactory>();
			services.AddScoped<IDataObjectFactory<VehicleHireDehireHistoryDataObject>, VehicleHireDehireHistoryFactory>();
			services.AddScoped<IDataObjectFactory<ChecklistFailurePerVechicleViewDataObject>, ChecklistFailurePerVechicleViewFactory>();
			services.AddScoped<IDataObjectFactory<ImpactReportFilterDataObject>, ImpactReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<IncompletedChecklistViewDataObject>, IncompletedChecklistViewFactory>();
			services.AddScoped<IDataObjectFactory<ReportSubscriptionDataObject>, ReportSubscriptionFactory>();
			services.AddScoped<IDataObjectFactory<VehicleLastGPSLocationViewDataObject>, VehicleLastGPSLocationViewFactory>();
			services.AddScoped<IDataObjectFactory<PSTATDetailsDataObject>, PSTATDetailsFactory>();
			services.AddScoped<IDataObjectFactory<LicenseExpiryReportFilterDataObject>, LicenseExpiryReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<FloorZonesDataObject>, FloorZonesFactory>();
			services.AddScoped<IDataObjectFactory<CanruleDetailsDataObject>, CanruleDetailsFactory>();
			services.AddScoped<IDataObjectFactory<VehicleHireDehireSynchronizationOptionsDataObject>, VehicleHireDehireSynchronizationOptionsFactory>();
			services.AddScoped<IDataObjectFactory<DriverLicenseExpiryStoreProcedureDataObject>, DriverLicenseExpiryStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<UnitSummaryReportDataObject>, UnitSummaryReportFactory>();
			services.AddScoped<IDataObjectFactory<DepartmentVehicleMasterCardAccessDataObject>, DepartmentVehicleMasterCardAccessFactory>();
			services.AddScoped<IDataObjectFactory<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>, VehicleUtilizationLastTwelveHoursStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<ChecklistFailureViewDataObject>, ChecklistFailureViewFactory>();
			services.AddScoped<IDataObjectFactory<PersonChecklistLanguageSettingsDataObject>, PersonChecklistLanguageSettingsFactory>();
			services.AddScoped<IDataObjectFactory<VehicleSessionlessImpactDataObject>, VehicleSessionlessImpactFactory>();
			services.AddScoped<IDataObjectFactory<LicenseByModelDataObject>, LicenseByModelFactory>();
			services.AddScoped<IDataObjectFactory<AllMessageHistoryStoreProcedureDataObject>, AllMessageHistoryStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<IoTDeviceMessageCacheDataObject>, IoTDeviceMessageCacheFactory>();
			services.AddScoped<IDataObjectFactory<ProficiencyCombinedViewDataObject>, ProficiencyCombinedViewFactory>();
			services.AddScoped<IDataObjectFactory<CustomerSnapshotDataObject>, CustomerSnapshotFactory>();
			services.AddScoped<IDataObjectFactory<GOGroupDataObject>, GOGroupFactory>();
			services.AddScoped<IDataObjectFactory<VehiclesPerModelReportDataObject>, VehiclesPerModelReportFactory>();
			services.AddScoped<IDataObjectFactory<TodaysImpactStoreProcedureDataObject>, TodaysImpactStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<BroadcastMessageHistoryFilterDataObject>, BroadcastMessageHistoryFilterFactory>();
			services.AddScoped<IDataObjectFactory<DealerDriverDataObject>, DealerDriverFactory>();
			services.AddScoped<IDataObjectFactory<EmailGroupsDataObject>, EmailGroupsFactory>();
			services.AddScoped<IDataObjectFactory<PreOperationalChecklistDataObject>, PreOperationalChecklistFactory>();
			services.AddScoped<IDataObjectFactory<SynchronizationStatusReportFilterDataObject>, SynchronizationStatusReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<SlamcoreDeviceDataObject>, SlamcoreDeviceFactory>();
			services.AddScoped<IDataObjectFactory<SiteDataObject>, SiteFactory>();
			services.AddScoped<IDataObjectFactory<AlertSubscriptionDataObject>, AlertSubscriptionFactory>();
			services.AddScoped<IDataObjectFactory<CustomerAuditDataObject>, CustomerAuditFactory>();
			services.AddScoped<IDataObjectFactory<PerVehicleNormalCardAccessDataObject>, PerVehicleNormalCardAccessFactory>();
			services.AddScoped<IDataObjectFactory<PedestrianDetectionHistoryDataObject>, PedestrianDetectionHistoryFactory>();
			services.AddScoped<IDataObjectFactory<PersonToPerVehicleNormalAccessViewDataObject>, PersonToPerVehicleNormalAccessViewFactory>();
			services.AddScoped<IDataObjectFactory<DashboardDriverCardStoreProcedureDataObject>, DashboardDriverCardStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<TodaysImpactViewDataObject>, TodaysImpactViewFactory>();
			services.AddScoped<IDataObjectFactory<DetailedSessionViewDataObject>, DetailedSessionViewFactory>();
			services.AddScoped<IDataObjectFactory<ImportJobLogDataObject>, ImportJobLogFactory>();
			services.AddScoped<IDataObjectFactory<ProficiencyReportFilterDataObject>, ProficiencyReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<OnDemandSessionDataObject>, OnDemandSessionFactory>();
			services.AddScoped<IDataObjectFactory<AllVORStatusStoreProcedureDataObject>, AllVORStatusStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<VORSettingHistoryDataObject>, VORSettingHistoryFactory>();
			services.AddScoped<IDataObjectFactory<AllUserSummaryStoreProcedureDataObject>, AllUserSummaryStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<CustomerSSODetailDataObject>, CustomerSSODetailFactory>();
			services.AddScoped<IDataObjectFactory<UploadLogoRequestDataObject>, UploadLogoRequestFactory>();
			services.AddScoped<IDataObjectFactory<AllVehicleCalibrationFilterDataObject>, AllVehicleCalibrationFilterFactory>();
			services.AddScoped<IDataObjectFactory<DepartmentHourSettingsDataObject>, DepartmentHourSettingsFactory>();
			services.AddScoped<IDataObjectFactory<CategoryTemplateDataObject>, CategoryTemplateFactory>();
			services.AddScoped<IDataObjectFactory<DashboardVehicleCardViewDataObject>, DashboardVehicleCardViewFactory>();
			services.AddScoped<IDataObjectFactory<CurrentStatusDriverViewDataObject>, CurrentStatusDriverViewFactory>();
			services.AddScoped<IDataObjectFactory<UnitSummaryStoreProcedureDataObject>, UnitSummaryStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<ReportTypeDataObject>, ReportTypeFactory>();
			services.AddScoped<IDataObjectFactory<SiteVehicleMasterCardAccessDataObject>, SiteVehicleMasterCardAccessFactory>();
			services.AddScoped<IDataObjectFactory<PersonToDepartmentVehicleNormalAccessViewDataObject>, PersonToDepartmentVehicleNormalAccessViewFactory>();
			services.AddScoped<IDataObjectFactory<GOUserGroupDataObject>, GOUserGroupFactory>();
			services.AddScoped<IDataObjectFactory<CustomerFeatureSubscriptionDataObject>, CustomerFeatureSubscriptionFactory>();
			services.AddScoped<IDataObjectFactory<VehicleToPreOpChecklistViewDataObject>, VehicleToPreOpChecklistViewFactory>();
			services.AddScoped<IDataObjectFactory<PersonToSiteVehicleMasterAccessViewDataObject>, PersonToSiteVehicleMasterAccessViewFactory>();
			services.AddScoped<IDataObjectFactory<DealerConfigurationDataObject>, DealerConfigurationFactory>();
			services.AddScoped<IDataObjectFactory<TodaysPreopCheckStoreProcedureDataObject>, TodaysPreopCheckStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<CustomerPreOperationalChecklistTemplateDataObject>, CustomerPreOperationalChecklistTemplateFactory>();
			services.AddScoped<IDataObjectFactory<CurrentDriverStatusChartViewDataObject>, CurrentDriverStatusChartViewFactory>();
			services.AddScoped<IDataObjectFactory<GOChangeDeltaDataObject>, GOChangeDeltaFactory>();
			services.AddScoped<IDataObjectFactory<GO2FAConfigurationDataObject>, GO2FAConfigurationFactory>();
			services.AddScoped<IDataObjectFactory<PreOpReportFilterDataObject>, PreOpReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<SiteFloorPlanDataObject>, SiteFloorPlanFactory>();
			services.AddScoped<IDataObjectFactory<BroadcastMessageHistoryDataObject>, BroadcastMessageHistoryFactory>();
			services.AddScoped<IDataObjectFactory<GoUserToCustomerDataObject>, GoUserToCustomerFactory>();
			services.AddScoped<IDataObjectFactory<ImportJobBatchDataObject>, ImportJobBatchFactory>();
			services.AddScoped<IDataObjectFactory<PersonDataObject>, PersonFactory>();
			services.AddScoped<IDataObjectFactory<DetailedVORSessionStoreProcedureDataObject>, DetailedVORSessionStoreProcedureFactory>();
			services.AddScoped<IDataObjectFactory<PersonToModelVehicleNormalAccessViewDataObject>, PersonToModelVehicleNormalAccessViewFactory>();
			services.AddScoped<IDataObjectFactory<NetworkSettingsDataObject>, NetworkSettingsFactory>();
			services.AddScoped<IDataObjectFactory<DepartmentDataObject>, DepartmentFactory>();
			services.AddScoped<IDataObjectFactory<MachineUnlockReportFilterDataObject>, MachineUnlockReportFilterFactory>();
			services.AddScoped<IDataObjectFactory<PerVehicleMasterCardAccessDataObject>, PerVehicleMasterCardAccessFactory>();
			services.AddScoped<IDataObjectFactory<UnitUtilisationCombinedViewDataObject>, UnitUtilisationCombinedViewFactory>();
			services.AddScoped<IDataObjectFactory<FeatureSubscriptionsFilterDataObject>, FeatureSubscriptionsFilterFactory>();
			services.AddScoped<IDataObjectFactory<OnDemandSettingsDataObject>, OnDemandSettingsFactory>();
           
			//  Resolve same ThreadContext instance (per thread) when caller resolves either IUserIdentity or IThreadContext
            // cf. test 
	        services.AddScoped<ThreadContext>();
	        services.AddScoped<IThreadContext>(x => x.GetRequiredService<ThreadContext>());
	        services.AddScoped<IUserIdentity>(x => x.GetRequiredService<ThreadContext>());

          services.AddScoped<IAuthentication, Authentication>();
			services.AddScoped<IAuthenticationService, AuthenticationService>();
			services.AddScoped<IDataProvider<ChecklistDetailDataObject>, ChecklistDetailService>();
			services.AddScoped<IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject>, SlamcoreAwareAuthenticationDetailsService>();
			services.AddScoped<IDataProvider<FeatureSubscriptionTemplateDataObject>, FeatureSubscriptionTemplateService>();
			services.AddScoped<IDataProvider<PersonToModelVehicleMasterAccessViewDataObject>, PersonToModelVehicleMasterAccessViewService>();
			services.AddScoped<IDataProvider<VehicleLockoutDataObject>, VehicleLockoutService>();
			services.AddScoped<IDataProvider<RevisionDataObject>, RevisionService>();
			services.AddScoped<IDataProvider<AlertDataObject>, AlertService>();
			services.AddScoped<IDataProvider<WebsiteUserDataObject>, WebsiteUserService>();
			services.AddScoped<IDataProvider<DriverAccessAbuseFilterDataObject>, DriverAccessAbuseFilterService>();
			services.AddScoped<IDataProvider<AllLicenseExpiryViewDataObject>, AllLicenseExpiryViewService>();
			services.AddScoped<IDataProvider<DriverLicenseExpiryViewDataObject>, DriverLicenseExpiryViewService>();
			services.AddScoped<IDataProvider<ContactPersonInformationDataObject>, ContactPersonInformationService>();
			services.AddScoped<IDataProvider<ChecklistSettingsDataObject>, ChecklistSettingsService>();
			services.AddScoped<IDataProvider<VehicleDiagnosticDataObject>, VehicleDiagnosticService>();
			services.AddScoped<IDataProvider<GOUser2FADataObject>, GOUser2FAService>();
			services.AddScoped<IDataProvider<VehicleGPSDataObject>, VehicleGPSService>();
			services.AddScoped<IDataProvider<ModuleDataObject>, ModuleService>();
			services.AddScoped<IDataProvider<PersonToPerVehicleMasterAccessViewDataObject>, PersonToPerVehicleMasterAccessViewService>();
			services.AddScoped<IDataProvider<AccessGroupDataObject>, AccessGroupService>();
			services.AddScoped<IDataProvider<HelpDataObject>, HelpService>();
			services.AddScoped<IDataProvider<ChecklistStatusViewDataObject>, ChecklistStatusViewService>();
			services.AddScoped<IDataProvider<CurrentStatusCombinedViewDataObject>, CurrentStatusCombinedViewService>();
			services.AddScoped<IDataProvider<CurrentVehicleStatusChartViewDataObject>, CurrentVehicleStatusChartViewService>();
			services.AddScoped<IDataProvider<DealerFeatureSubscriptionDataObject>, DealerFeatureSubscriptionService>();
			services.AddScoped<IDataProvider<MessageHistoryDataObject>, MessageHistoryService>();
			services.AddScoped<IDataProvider<AccessGroupToSiteDataObject>, AccessGroupToSiteService>();
			services.AddScoped<IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject>, AllDriverAccessAbuseStoreProcedureService>();
			services.AddScoped<IDataProvider<CustomerToModelDataObject>, CustomerToModelService>();
			services.AddScoped<IDataProvider<AllEmailSubscriptionStoreProcedureDataObject>, AllEmailSubscriptionStoreProcedureService>();
			services.AddScoped<IDataProvider<AlertSummaryStoreProcedureDataObject>, AlertSummaryStoreProcedureService>();
			services.AddScoped<IDataProvider<DashboardVehicleCardStoreProcedureDataObject>, DashboardVehicleCardStoreProcedureService>();
			services.AddScoped<IDataProvider<RegionDataObject>, RegionService>();
			services.AddScoped<IDataProvider<ModelDataObject>, ModelService>();
			services.AddScoped<IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>, AllVORSessionsPerVehicleStoreProcedureService>();
			services.AddScoped<IDataProvider<SlamcoreDeviceConnectionViewDataObject>, SlamcoreDeviceConnectionViewService>();
			services.AddScoped<IDataProvider<AllImpactsViewDataObject>, AllImpactsViewService>();
			services.AddScoped<IDataProvider<CustomerDataObject>, CustomerService>();
			services.AddScoped<IDataProvider<DepartmentChecklistDataObject>, DepartmentChecklistService>();
			services.AddScoped<IDataProvider<ModuleHistoryDataObject>, ModuleHistoryService>();
			services.AddScoped<IDataProvider<CountryDataObject>, CountryService>();
			services.AddScoped<IDataProvider<AllChecklistResultViewDataObject>, AllChecklistResultViewService>();
			services.AddScoped<IDataProvider<GPSHistoryDataObject>, GPSHistoryService>();
			services.AddScoped<IDataProvider<ImpactsForVehicleViewDataObject>, ImpactsForVehicleViewService>();
			services.AddScoped<IDataProvider<AllVehicleUnlocksViewDataObject>, AllVehicleUnlocksViewService>();
			services.AddScoped<IDataProvider<SlamcorePedestrianDetectionDataObject>, SlamcorePedestrianDetectionService>();
			services.AddScoped<IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject>, ImpactFrequencyPerWeekMonthViewService>();
			services.AddScoped<IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject>, VehicleUtilizationLastTwelveHoursViewService>();
			services.AddScoped<IDataProvider<IOFIELDDataObject>, IOFIELDService>();
			services.AddScoped<IDataProvider<EmailDataObject>, EmailService>();
			services.AddScoped<IDataProvider<LicenceDetailDataObject>, LicenceDetailService>();
			services.AddScoped<IDataProvider<PedestrianDetectionHistoryFilterDataObject>, PedestrianDetectionHistoryFilterService>();
			services.AddScoped<IDataProvider<GOLoginHistoryDataObject>, GOLoginHistoryService>();
			services.AddScoped<IDataProvider<MainDashboardFilterDataObject>, MainDashboardFilterService>();
			services.AddScoped<IDataProvider<AllVehicleCalibrationStoreProcedureDataObject>, AllVehicleCalibrationStoreProcedureService>();
			services.AddScoped<IDataProvider<AccessGroupTemplateDataObject>, AccessGroupTemplateService>();
			services.AddScoped<IDataProvider<CardToCardAccessDataObject>, CardToCardAccessService>();
			services.AddScoped<IDataProvider<ZoneCoordinatesDataObject>, ZoneCoordinatesService>();
			services.AddScoped<IDataProvider<SnapshotDataObject>, SnapshotService>();
			services.AddScoped<IDataProvider<GOTaskDataObject>, GOTaskService>();
			services.AddScoped<IDataProvider<GeneralProductivityReportFilterDataObject>, GeneralProductivityReportFilterService>();
			services.AddScoped<IDataProvider<DepartmentVehicleNormalCardAccessDataObject>, DepartmentVehicleNormalCardAccessService>();
			services.AddScoped<IDataProvider<CustomerToPersonViewDataObject>, CustomerToPersonViewService>();
			services.AddScoped<IDataProvider<VehicleSlamcoreLocationHistoryDataObject>, VehicleSlamcoreLocationHistoryService>();
			services.AddScoped<IDataProvider<PermissionDataObject>, PermissionService>();
			services.AddScoped<IDataProvider<SessionDetailsDataObject>, SessionDetailsService>();
			services.AddScoped<IDataProvider<GORoleDataObject>, GORoleService>();
			services.AddScoped<IDataProvider<GeneralProductivityPerVehicleViewDataObject>, GeneralProductivityPerVehicleViewService>();
			services.AddScoped<IDataProvider<VehicleDataObject>, VehicleService>();
			services.AddScoped<IDataProvider<GeneralProductivityPerDriverViewLatestDataObject>, GeneralProductivityPerDriverViewLatestService>();
			services.AddScoped<IDataProvider<DriverDataObject>, DriverService>();
			services.AddScoped<IDataProvider<EmailGroupsToPersonDataObject>, EmailGroupsToPersonService>();
			services.AddScoped<IDataProvider<GOGroupRoleDataObject>, GOGroupRoleService>();
			services.AddScoped<IDataProvider<ModelVehicleMasterCardAccessDataObject>, ModelVehicleMasterCardAccessService>();
			services.AddScoped<IDataProvider<EmailSubscriptionReportFilterDataObject>, EmailSubscriptionReportFilterService>();
			services.AddScoped<IDataProvider<VehicleProficiencyViewDataObject>, VehicleProficiencyViewService>();
			services.AddScoped<IDataProvider<DashboardDriverCardViewDataObject>, DashboardDriverCardViewService>();
			services.AddScoped<IDataProvider<DriverProficiencyViewDataObject>, DriverProficiencyViewService>();
			services.AddScoped<IDataProvider<GOSecurityTokensDataObject>, GOSecurityTokensService>();
			services.AddScoped<IDataProvider<AlertHistoryDataObject>, AlertHistoryService>();
			services.AddScoped<IDataProvider<DashboardFilterDataObject>, DashboardFilterService>();
			services.AddScoped<IDataProvider<VORReportFilterDataObject>, VORReportFilterService>();
			services.AddScoped<IDataProvider<OnDemandAuthorisationFilterDataObject>, OnDemandAuthorisationFilterService>();
			services.AddScoped<IDataProvider<WebsiteRoleDataObject>, WebsiteRoleService>();
			services.AddScoped<IDataProvider<GOUserRoleDataObject>, GOUserRoleService>();
			services.AddScoped<IDataProvider<GeneralProductivityViewDataObject>, GeneralProductivityViewService>();
			services.AddScoped<IDataProvider<InspectionDataObject>, InspectionService>();
			services.AddScoped<IDataProvider<ImpactDataObject>, ImpactService>();
			services.AddScoped<IDataProvider<PersonToDepartmentVehicleMasterAccessViewDataObject>, PersonToDepartmentVehicleMasterAccessViewService>();
			services.AddScoped<IDataProvider<CurrentStatusVehicleViewDataObject>, CurrentStatusVehicleViewService>();
			services.AddScoped<IDataProvider<CustomerModelDataObject>, CustomerModelService>();
			services.AddScoped<IDataProvider<TagDataObject>, TagService>();
			services.AddScoped<IDataProvider<VehicleBroadcastMessageDataObject>, VehicleBroadcastMessageService>();
			services.AddScoped<IDataProvider<FloorPlanDataObject>, FloorPlanService>();
			services.AddScoped<IDataProvider<TodaysPreopCheckViewDataObject>, TodaysPreopCheckViewService>();
			services.AddScoped<IDataProvider<ModelVehicleNormalCardAccessDataObject>, ModelVehicleNormalCardAccessService>();
			services.AddScoped<IDataProvider<SiteVehicleNormalCardAccessDataObject>, SiteVehicleNormalCardAccessService>();
			services.AddScoped<IDataProvider<ImportJobStatusDataObject>, ImportJobStatusService>();
			services.AddScoped<IDataProvider<VehicleOtherSettingsDataObject>, VehicleOtherSettingsService>();
			services.AddScoped<IDataProvider<DashboardFilterMoreFieldsDataObject>, DashboardFilterMoreFieldsService>();
			services.AddScoped<IDataProvider<VehicleSupervisorsViewDataObject>, VehicleSupervisorsViewService>();
			services.AddScoped<IDataProvider<ExportJobStatusDataObject>, ExportJobStatusService>();
			services.AddScoped<IDataProvider<TimezoneDataObject>, TimezoneService>();
			services.AddScoped<IDataProvider<HireDeHireReportFilterDataObject>, HireDeHireReportFilterService>();
			services.AddScoped<IDataProvider<LoggedHoursVersusSeatHoursViewDataObject>, LoggedHoursVersusSeatHoursViewService>();
			services.AddScoped<IDataProvider<PersonAllocationDataObject>, PersonAllocationService>();
			services.AddScoped<IDataProvider<BroadcastMessageDataObject>, BroadcastMessageService>();
			services.AddScoped<IDataProvider<VORReportCombinedViewDataObject>, VORReportCombinedViewService>();
			services.AddScoped<IDataProvider<UnitUtilisationStoreProcedureDataObject>, UnitUtilisationStoreProcedureService>();
			services.AddScoped<IDataProvider<ChecklistResultDataObject>, ChecklistResultService>();
			services.AddScoped<IDataProvider<UnitUnutilisationStoreProcedureDataObject>, UnitUnutilisationStoreProcedureService>();
			services.AddScoped<IDataProvider<VehicleAlertSubscriptionDataObject>, VehicleAlertSubscriptionService>();
			services.AddScoped<IDataProvider<OnDemandAuthorisationStoreProcedureDataObject>, OnDemandAuthorisationStoreProcedureService>();
			services.AddScoped<IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject>, ImpactFrequencyPerTimeSlotViewService>();
			services.AddScoped<IDataProvider<PersonToSiteVehicleNormalAccessViewDataObject>, PersonToSiteVehicleNormalAccessViewService>();
			services.AddScoped<IDataProvider<SessionDataObject>, SessionService>();
			services.AddScoped<IDataProvider<SlamcoreDeviceFilterDataObject>, SlamcoreDeviceFilterService>();
			services.AddScoped<IDataProvider<DealerDataObject>, DealerService>();
			services.AddScoped<IDataProvider<GOUserDataObject>, GOUserService>();
			services.AddScoped<IDataProvider<ServiceSettingsDataObject>, ServiceSettingsService>();
			services.AddScoped<IDataProvider<UpdateFirmwareRequestDataObject>, UpdateFirmwareRequestService>();
			services.AddScoped<IDataProvider<SlamcoreAPIKeyDataObject>, SlamcoreAPIKeyService>();
			services.AddScoped<IDataProvider<FirmwareDataObject>, FirmwareService>();
			services.AddScoped<IDataProvider<CardDataObject>, CardService>();
			services.AddScoped<IDataProvider<GOUserDepartmentDataObject>, GOUserDepartmentService>();
			services.AddScoped<IDataProvider<CanruleDataObject>, CanruleService>();
			services.AddScoped<IDataProvider<ImpactFrequencyPerWeekDayViewDataObject>, ImpactFrequencyPerWeekDayViewService>();
			services.AddScoped<IDataProvider<SlamcoreDeviceHistoryDataObject>, SlamcoreDeviceHistoryService>();
			services.AddScoped<IDataProvider<VehicleHireDehireHistoryDataObject>, VehicleHireDehireHistoryService>();
			services.AddScoped<IDataProvider<ChecklistFailurePerVechicleViewDataObject>, ChecklistFailurePerVechicleViewService>();
			services.AddScoped<IDataProvider<ImpactReportFilterDataObject>, ImpactReportFilterService>();
			services.AddScoped<IDataProvider<IncompletedChecklistViewDataObject>, IncompletedChecklistViewService>();
			services.AddScoped<IDataProvider<ReportSubscriptionDataObject>, ReportSubscriptionService>();
			services.AddScoped<IDataProvider<VehicleLastGPSLocationViewDataObject>, VehicleLastGPSLocationViewService>();
			services.AddScoped<IDataProvider<PSTATDetailsDataObject>, PSTATDetailsService>();
			services.AddScoped<IDataProvider<LicenseExpiryReportFilterDataObject>, LicenseExpiryReportFilterService>();
			services.AddScoped<IDataProvider<FloorZonesDataObject>, FloorZonesService>();
			services.AddScoped<IDataProvider<CanruleDetailsDataObject>, CanruleDetailsService>();
			services.AddScoped<IDataProvider<VehicleHireDehireSynchronizationOptionsDataObject>, VehicleHireDehireSynchronizationOptionsService>();
			services.AddScoped<IDataProvider<DriverLicenseExpiryStoreProcedureDataObject>, DriverLicenseExpiryStoreProcedureService>();
			services.AddScoped<IDataProvider<UnitSummaryReportDataObject>, UnitSummaryReportService>();
			services.AddScoped<IDataProvider<DepartmentVehicleMasterCardAccessDataObject>, DepartmentVehicleMasterCardAccessService>();
			services.AddScoped<IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>, VehicleUtilizationLastTwelveHoursStoreProcedureService>();
			services.AddScoped<IDataProvider<ChecklistFailureViewDataObject>, ChecklistFailureViewService>();
			services.AddScoped<IDataProvider<PersonChecklistLanguageSettingsDataObject>, PersonChecklistLanguageSettingsService>();
			services.AddScoped<IDataProvider<VehicleSessionlessImpactDataObject>, VehicleSessionlessImpactService>();
			services.AddScoped<IDataProvider<LicenseByModelDataObject>, LicenseByModelService>();
			services.AddScoped<IDataProvider<AllMessageHistoryStoreProcedureDataObject>, AllMessageHistoryStoreProcedureService>();
			services.AddScoped<IDataProvider<IoTDeviceMessageCacheDataObject>, IoTDeviceMessageCacheService>();
			services.AddScoped<IDataProvider<ProficiencyCombinedViewDataObject>, ProficiencyCombinedViewService>();
			services.AddScoped<IDataProvider<CustomerSnapshotDataObject>, CustomerSnapshotService>();
			services.AddScoped<IDataProvider<GOGroupDataObject>, GOGroupService>();
			services.AddScoped<IDataProvider<VehiclesPerModelReportDataObject>, VehiclesPerModelReportService>();
			services.AddScoped<IDataProvider<TodaysImpactStoreProcedureDataObject>, TodaysImpactStoreProcedureService>();
			services.AddScoped<IDataProvider<BroadcastMessageHistoryFilterDataObject>, BroadcastMessageHistoryFilterService>();
			services.AddScoped<IDataProvider<DealerDriverDataObject>, DealerDriverService>();
			services.AddScoped<IDataProvider<EmailGroupsDataObject>, EmailGroupsService>();
			services.AddScoped<IDataProvider<PreOperationalChecklistDataObject>, PreOperationalChecklistService>();
			services.AddScoped<IDataProvider<SynchronizationStatusReportFilterDataObject>, SynchronizationStatusReportFilterService>();
			services.AddScoped<IDataProvider<SlamcoreDeviceDataObject>, SlamcoreDeviceService>();
			services.AddScoped<IDataProvider<SiteDataObject>, SiteService>();
			services.AddScoped<IDataProvider<AlertSubscriptionDataObject>, AlertSubscriptionService>();
			services.AddScoped<IDataProvider<CustomerAuditDataObject>, CustomerAuditService>();
			services.AddScoped<IDataProvider<PerVehicleNormalCardAccessDataObject>, PerVehicleNormalCardAccessService>();
			services.AddScoped<IDataProvider<PedestrianDetectionHistoryDataObject>, PedestrianDetectionHistoryService>();
			services.AddScoped<IDataProvider<PersonToPerVehicleNormalAccessViewDataObject>, PersonToPerVehicleNormalAccessViewService>();
			services.AddScoped<IDataProvider<DashboardDriverCardStoreProcedureDataObject>, DashboardDriverCardStoreProcedureService>();
			services.AddScoped<IDataProvider<TodaysImpactViewDataObject>, TodaysImpactViewService>();
			services.AddScoped<IDataProvider<DetailedSessionViewDataObject>, DetailedSessionViewService>();
			services.AddScoped<IDataProvider<ImportJobLogDataObject>, ImportJobLogService>();
			services.AddScoped<IDataProvider<ProficiencyReportFilterDataObject>, ProficiencyReportFilterService>();
			services.AddScoped<IDataProvider<OnDemandSessionDataObject>, OnDemandSessionService>();
			services.AddScoped<IDataProvider<AllVORStatusStoreProcedureDataObject>, AllVORStatusStoreProcedureService>();
			services.AddScoped<IDataProvider<VORSettingHistoryDataObject>, VORSettingHistoryService>();
			services.AddScoped<IDataProvider<AllUserSummaryStoreProcedureDataObject>, AllUserSummaryStoreProcedureService>();
			services.AddScoped<IDataProvider<CustomerSSODetailDataObject>, CustomerSSODetailService>();
			services.AddScoped<IDataProvider<UploadLogoRequestDataObject>, UploadLogoRequestService>();
			services.AddScoped<IDataProvider<AllVehicleCalibrationFilterDataObject>, AllVehicleCalibrationFilterService>();
			services.AddScoped<IDataProvider<DepartmentHourSettingsDataObject>, DepartmentHourSettingsService>();
			services.AddScoped<IDataProvider<CategoryTemplateDataObject>, CategoryTemplateService>();
			services.AddScoped<IDataProvider<DashboardVehicleCardViewDataObject>, DashboardVehicleCardViewService>();
			services.AddScoped<IDataProvider<CurrentStatusDriverViewDataObject>, CurrentStatusDriverViewService>();
			services.AddScoped<IDataProvider<UnitSummaryStoreProcedureDataObject>, UnitSummaryStoreProcedureService>();
			services.AddScoped<IDataProvider<ReportTypeDataObject>, ReportTypeService>();
			services.AddScoped<IDataProvider<SiteVehicleMasterCardAccessDataObject>, SiteVehicleMasterCardAccessService>();
			services.AddScoped<IDataProvider<PersonToDepartmentVehicleNormalAccessViewDataObject>, PersonToDepartmentVehicleNormalAccessViewService>();
			services.AddScoped<IDataProvider<GOUserGroupDataObject>, GOUserGroupService>();
			services.AddScoped<IDataProvider<CustomerFeatureSubscriptionDataObject>, CustomerFeatureSubscriptionService>();
			services.AddScoped<IDataProvider<VehicleToPreOpChecklistViewDataObject>, VehicleToPreOpChecklistViewService>();
			services.AddScoped<IDataProvider<PersonToSiteVehicleMasterAccessViewDataObject>, PersonToSiteVehicleMasterAccessViewService>();
			services.AddScoped<IDataProvider<DealerConfigurationDataObject>, DealerConfigurationService>();
			services.AddScoped<IDataProvider<TodaysPreopCheckStoreProcedureDataObject>, TodaysPreopCheckStoreProcedureService>();
			services.AddScoped<IDataProvider<CustomerPreOperationalChecklistTemplateDataObject>, CustomerPreOperationalChecklistTemplateService>();
			services.AddScoped<IDataProvider<CurrentDriverStatusChartViewDataObject>, CurrentDriverStatusChartViewService>();
			services.AddScoped<IDataProvider<GOChangeDeltaDataObject>, GOChangeDeltaService>();
			services.AddScoped<IDataProvider<GO2FAConfigurationDataObject>, GO2FAConfigurationService>();
			services.AddScoped<IDataProvider<PreOpReportFilterDataObject>, PreOpReportFilterService>();
			services.AddScoped<IDataProvider<SiteFloorPlanDataObject>, SiteFloorPlanService>();
			services.AddScoped<IDataProvider<BroadcastMessageHistoryDataObject>, BroadcastMessageHistoryService>();
			services.AddScoped<IDataProvider<GoUserToCustomerDataObject>, GoUserToCustomerService>();
			services.AddScoped<IDataProvider<ImportJobBatchDataObject>, ImportJobBatchService>();
			services.AddScoped<IDataProvider<PersonDataObject>, PersonService>();
			services.AddScoped<IDataProvider<DetailedVORSessionStoreProcedureDataObject>, DetailedVORSessionStoreProcedureService>();
			services.AddScoped<IDataProvider<PersonToModelVehicleNormalAccessViewDataObject>, PersonToModelVehicleNormalAccessViewService>();
			services.AddScoped<IDataProvider<NetworkSettingsDataObject>, NetworkSettingsService>();
			services.AddScoped<IDataProvider<DepartmentDataObject>, DepartmentService>();
			services.AddScoped<IDataProvider<MachineUnlockReportFilterDataObject>, MachineUnlockReportFilterService>();
			services.AddScoped<IDataProvider<PerVehicleMasterCardAccessDataObject>, PerVehicleMasterCardAccessService>();
			services.AddScoped<IDataProvider<UnitUtilisationCombinedViewDataObject>, UnitUtilisationCombinedViewService>();
			services.AddScoped<IDataProvider<FeatureSubscriptionsFilterDataObject>, FeatureSubscriptionsFilterService>();
			services.AddScoped<IDataProvider<OnDemandSettingsDataObject>, OnDemandSettingsService>();
			
			// EntityDataProvider: Given an entity instance, get its corresponding entity data provider
			services.AddScoped<IEntityDataProvider, DatabaseEntityProvider>();

			services.AddSingleton<ISettingsProvider, SettingsProvider>(x =>
			{
				var settings = new SettingsProvider(); 
				settings["WrapForPolicyInjection"] = false; 
				settings["RootNamespace"] = "FleetXQ";            
				settings["StorageProvider"] = "FileSystemStorageProvider";
				return settings;
			});   

			services.AddSingleton(typeof(ILogEngine), typeof(LogEngine));

			// Framework Services
			{
				services.AddTransient<IMailbox, OutlookMailbox>();
				services.AddTransient<ISMTPClient, SMTPClient>();
			}

			services.AddTransient<DataObjectConverter>();
			services.AddTransient<ObjectsDataSetConverter>();

			// Register custom types if any
			CustomContainer.RegisterCustomTypes(services);
		}
    }
}
