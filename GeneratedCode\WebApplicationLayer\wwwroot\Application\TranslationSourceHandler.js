﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
(function (global) {
    FleetXQ.Web.Application.TranslationSourceHandler = function () {
        var self = this;
        
        // Keeps track of loaded translation namespaces
        this.loadedNamespaces = {};
        
        // Global namespaces that should be loaded for all pages
        this.globalRequiredNamespaces = [
			'common'
		];
        
        // Define required namespaces by element (matching your JS structure)
        this.requiredNamespacesByElement = {
            "AccessGroupTemplatesPage-Page": [
                'pages/AccessGroupTemplatesPage',
                'entities/AccessGroupTemplate/AccessGroupTemplate',
                'entities/AccessGroupTemplate/grids/AccessGroupTemplateGrid'
            ],
            "AlertReportPage-Page": [
                'pages/AlertReportPage',
                'entities/AlertHistory/AlertHistory',
                'entities/Vehicle/Vehicle',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Alert/Alert',
                'entities/AlertHistory/grids/AlertHistoryGrid'
            ],
            "BroadcastMessageReportPage-Page": [
                'pages/BroadcastMessageReportPage',
                'entities/BroadcastMessageHistory/BroadcastMessageHistory',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Vehicle/Vehicle',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/BroadcastMessageHistoryFilter/BroadcastMessageHistoryFilter',
                'entities/BroadcastMessageHistory/grids/BroadcastMessageHistoryGrid',
                'entities/BroadcastMessageHistoryFilter/forms/BroadcastMessageHistoryFilterForm'
            ],
            "ChecklistResultAnswersPagePage-Page": [
                'pages/ChecklistResultAnswersPagePage',
                'entities/ChecklistResult/ChecklistResult',
                'entities/ChecklistDetail/ChecklistDetail',
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/ChecklistResult/forms/ChecklistResultForm',
                'entities/ChecklistDetail/grids/ChecklistDetailGrid'
            ],
            "ConnectionStatusDashboardPage-Page": [
                'pages/ConnectionStatusDashboardPage',
                'entities/SlamcoreDeviceFilter/SlamcoreDeviceFilter',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/SlamcoreDevice/SlamcoreDevice',
                'entities/SlamcoreDeviceConnectionView/SlamcoreDeviceConnectionView',
                'entities/SlamcoreDeviceFilter/forms/SlamcoreDeviceFilterForm',
                'entities/SlamcoreDevice/grids/SlamcoreDeviceGrid1',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm1',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm3',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm2'
            ],
            "CountryItemsPage-Page": [
                'pages/CountryItemsPage',
                'entities/Country/Country',
                'entities/Country/grids/CountryGrid',
                'entities/Country/filters/CountryFilter'
            ],
            "CountryPage-Page": [
                'pages/CountryPage',
                'entities/Country/Country',
                'entities/Customer/Customer',
                'entities/Country/forms/CountryForm',
                'entities/Customer/grids/CustomerGrid',
                'entities/Customer/filters/CustomerFilter'
            ],
            "CurrentStatusReportPage-Page": [
                'pages/CurrentStatusReportPage',
                'entities/DashboardFilter/DashboardFilter',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/CurrentVehicleStatusChartView/CurrentVehicleStatusChartView',
                'entities/CurrentStatusCombinedView/CurrentStatusCombinedView',
                'entities/CurrentStatusDriverView/CurrentStatusDriverView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Email/Email',
                'entities/CurrentStatusVehicleView/CurrentStatusVehicleView',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/DashboardFilter/forms/DashboardFilterForm',
                'entities/CurrentStatusCombinedView/forms/CurrentStatusCombinedViewForm',
                'entities/CurrentStatusDriverView/grids/CurrentStatusDriverViewGrid',
                'entities/CurrentStatusVehicleView/grids/CurrentStatusVehicleViewGrid'
            ],
            "CustomerItemsPage-Page": [
                'pages/CustomerItemsPage',
                'entities/Customer/Customer',
                'entities/Customer/grids/CustomerGrid',
                'entities/Customer/filters/CustomerFilter'
            ],
            "CustomerPage-Page": [
                'pages/CustomerPage',
                'entities/Customer/Customer',
                'entities/ContactPersonInformation/ContactPersonInformation',
                'entities/Country/Country',
                'entities/EmailGroups/EmailGroups',
                'entities/CustomerModel/CustomerModel',
                'entities/Model/Model',
                'entities/Site/Site',
                'entities/Timezone/Timezone',
                'entities/AccessGroup/AccessGroup',
                'entities/CustomerPreOperationalChecklistTemplate/CustomerPreOperationalChecklistTemplate',
                'entities/Customer/forms/CustomerForm',
                'entities/Customer/forms/CustomerForm2',
                'entities/Customer/forms/CustomerForm1',
                'entities/ContactPersonInformation/forms/ContactPersonInformationForm1',
                'entities/EmailGroups/grids/EmailGroupsGrid',
                'entities/CustomerModel/grids/CustomerModelGrid',
                'entities/Site/grids/SiteGrid1',
                'entities/AccessGroup/grids/AccessGroupGrid',
                'entities/CustomerPreOperationalChecklistTemplate/grids/CustomerPreOperationalChecklistTemplateGrid'
            ],
            "CustomerSSOPage-Page": [
                'pages/CustomerSSOPage',
                'entities/CustomerSSODetail/CustomerSSODetail',
                'entities/Customer/Customer',
                'entities/CustomerSSODetail/grids/CustomerSSODetailGrid'
            ],
            "DashboardImpactReportPage-Page": [
                'pages/DashboardImpactReportPage',
                'entities/AllImpactsView/AllImpactsView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Impact/Impact',
                'entities/Session/Session',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Card/Card',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/Email/Email',
                'entities/ImpactReportFilter/ImpactReportFilter',
                'entities/AllImpactsView/grids/AllImpactsViewGrid',
                'entities/ImpactReportFilter/forms/ImpactReportFilterForm'
            ],
            "DashboardPreOpReportPage-Page": [
                'pages/DashboardPreOpReportPage',
                'entities/PreOpReportFilter/PreOpReportFilter',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/AllChecklistResultView/AllChecklistResultView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/ChecklistResult/ChecklistResult',
                'entities/Session/Session',
                'entities/Vehicle/Vehicle',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Model/Model',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/PreOpReportFilter/forms/PreOpReportFilterForm',
                'entities/AllChecklistResultView/grids/AllChecklistResultViewGrid1'
            ],
            "DashboardtobedeletedPage-Page": [
                'pages/DashboardtobedeletedPage',
                'entities/DashboardDriverCardStoreProcedure/DashboardDriverCardStoreProcedure',
                'entities/DashboardVehicleCardStoreProcedure/DashboardVehicleCardStoreProcedure',
                'entities/TodaysPreopCheckView/TodaysPreopCheckView',
                'entities/DriverLicenseExpiryView/DriverLicenseExpiryView',
                'entities/TodaysImpactView/TodaysImpactView',
                'entities/MainDashboardFilter/MainDashboardFilter',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/VehicleUtilizationLastTwelveHoursView/VehicleUtilizationLastTwelveHoursView',
                'entities/DashboardDriverCardStoreProcedure/forms/DashboardDriverCardStoreProcedureForm',
                'entities/DashboardVehicleCardStoreProcedure/forms/DashboardVehicleCardStoreProcedureForm1',
                'entities/DashboardVehicleCardStoreProcedure/forms/DashboardVehicleCardStoreProcedureForm',
                'entities/MainDashboardFilter/forms/MainDashboardFilterForm'
            ],
            "DashboardVehicleUtilizationReportPage-Page": [
                'pages/DashboardVehicleUtilizationReportPage',
                'entities/GeneralProductivityReportFilter/GeneralProductivityReportFilter',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/GeneralProductivityView/GeneralProductivityView',
                'entities/UnitUnutilisationStoreProcedure/UnitUnutilisationStoreProcedure',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/GeneralProductivityPerDriverViewLatest/GeneralProductivityPerDriverViewLatest',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Email/Email',
                'entities/GeneralProductivityPerVehicleView/GeneralProductivityPerVehicleView',
                'entities/UnitUtilisationStoreProcedure/UnitUtilisationStoreProcedure',
                'entities/GeneralProductivityReportFilter/forms/GeneralProductivityReportFilterForm',
                'entities/GeneralProductivityView/forms/GeneralProductivityViewForm',
                'entities/UnitUnutilisationStoreProcedure/grids/UnitUnutilisationStoreProcedureGrid',
                'entities/GeneralProductivityPerDriverViewLatest/grids/GeneralProductivityPerDriverViewGrid',
                'entities/GeneralProductivityPerVehicleView/grids/GeneralProductivityPerVehicleViewGrid',
                'entities/UnitUtilisationStoreProcedure/grids/UnitUtilisationStoreProcedureGrid'
            ],
            "DataExportPagePage-Page": [
                'pages/DataExportPagePage',
                'entities/AlertHistory/AlertHistory',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Vehicle/Vehicle',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/VehicleSlamcoreLocationHistory/VehicleSlamcoreLocationHistory',
                'entities/SlamcoreDevice/SlamcoreDevice',
                'entities/SlamcorePedestrianDetection/SlamcorePedestrianDetection',
                'entities/AlertHistory/grids/AlertHistoryGrid1',
                'entities/VehicleSlamcoreLocationHistory/grids/VehicleSlamcoreLocationHistoryGrid',
                'entities/ExportJobStatus/grids/ExportJobStatusGrid2',
                'entities/VehicleSlamcoreLocationHistory/grids/VehicleSlamcoreLocationHistoryGrid1',
                'entities/SlamcoreDevice/grids/SlamcoreDeviceGrid2',
                'entities/SlamcorePedestrianDetection/grids/SlamcorePedestrianDetectionGrid'
            ],
            "DeactivateUserPage-Page": [
                'pages/DeactivateUserPage',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/GOUser/grids/ActiveUsersGrid',
                'entities/GOUser/grids/DeactivatedUsersGrid'
            ],
            "DealerItemsPage-Page": [
                'pages/DealerItemsPage',
                'entities/Dealer/Dealer',
                'entities/Region/Region',
                'entities/Dealer/grids/DealerGrid1'
            ],
            "DealerPage-Page": [
                'pages/DealerPage',
                'entities/Dealer/Dealer',
                'entities/Region/Region',
                'entities/Customer/Customer',
                'entities/Model/Model',
                'entities/Module/Module',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/Dealer/forms/DealerForm1',
                'entities/Dealer/forms/DealerForm',
                'entities/Dealer/forms/DealerForm2',
                'entities/Customer/grids/CustomerGrid',
                'entities/Customer/filters/CustomerFilter',
                'entities/Model/grids/ModelGrid1',
                'entities/Module/grids/ModuleGrid1',
                'entities/Module/filters/SpareModuleFilter',
                'entities/GOUser/grids/GOUserGrid1'
            ],
            "DepartmentItemsPage-Page": [
                'pages/DepartmentItemsPage',
                'entities/Department/Department',
                'entities/Department/grids/DepartmentGrid'
            ],
            "DevicesPage-Page": [
                'pages/DevicesPage',
                'entities/SlamcoreDevice/SlamcoreDevice',
                'entities/Vehicle/Vehicle',
                'entities/SlamcoreDevice/grids/SlamcoreDeviceGrid'
            ],
            "DriverAccessAbuseReportPage-Page": [
                'pages/DriverAccessAbuseReportPage',
                'entities/DriverAccessAbuseFilter/DriverAccessAbuseFilter',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/AllDriverAccessAbuseStoreProcedure/AllDriverAccessAbuseStoreProcedure',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/DriverAccessAbuseFilter/forms/DriverAccessAbuseFilterForm',
                'entities/AllDriverAccessAbuseStoreProcedure/grids/AllDriverAccessAbuseStoreProcedureGrid'
            ],
            "DriverPagePage-Page": [
                'pages/DriverPagePage',
                'entities/Driver/Driver',
                'entities/Driver/grids/DriverGrid'
            ],
            "EmailSubscriptionReportPage-Page": [
                'pages/EmailSubscriptionReportPage',
                'entities/EmailSubscriptionReportFilter/EmailSubscriptionReportFilter',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/AllEmailSubscriptionStoreProcedure/AllEmailSubscriptionStoreProcedure',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Person/Person',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/ReportType/ReportType',
                'entities/EmailSubscriptionReportFilter/forms/EmailSubscriptionReportFilterForm',
                'entities/AllEmailSubscriptionStoreProcedure/grids/AllEmailSubscriptionStoreProcedureGrid'
            ],
            "ExportJobPage-Page": [
                'pages/ExportJobPage',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/ExportJobStatus/grids/ExportJobStatusGrid',
                'entities/ExportJobStatus/forms/ExportJobStatusForm'
            ],
            "ExportPage-Page": [
                'pages/ExportPage',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/ExportJobStatus/grids/ExportJobStatusGrid1'
            ],
            "FeatureSubscriptionsPage-Page": [
                'pages/FeatureSubscriptionsPage',
                'entities/Dealer/Dealer',
                'entities/Dealer/grids/DealerGrid'
            ],
            "FirmwaresPagePage-Page": [
                'pages/FirmwaresPagePage',
                'entities/Firmware/Firmware',
                'entities/Firmware/grids/FirmwareGrid'
            ],
            "FleetDashboardPage-Page": [
                'pages/FleetDashboardPage',
                'entities/DashboardDriverCardStoreProcedure/DashboardDriverCardStoreProcedure',
                'entities/DashboardVehicleCardStoreProcedure/DashboardVehicleCardStoreProcedure',
                'entities/TodaysPreopCheckView/TodaysPreopCheckView',
                'entities/DriverLicenseExpiryView/DriverLicenseExpiryView',
                'entities/TodaysImpactView/TodaysImpactView',
                'entities/MainDashboardFilter/MainDashboardFilter',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/VehicleUtilizationLastTwelveHoursView/VehicleUtilizationLastTwelveHoursView',
                'entities/DashboardDriverCardStoreProcedure/forms/DashboardDriverCardStoreProcedureForm',
                'entities/DashboardVehicleCardStoreProcedure/forms/DashboardVehicleCardStoreProcedureForm1',
                'entities/DashboardVehicleCardStoreProcedure/forms/DashboardVehicleCardStoreProcedureForm',
                'entities/MainDashboardFilter/forms/MainDashboardFilterForm'
            ],
            "FloorPlanManagementPage-Page": [
                'pages/FloorPlanManagementPage',
                'entities/Site/Site',
                'entities/Site/grids/SiteGrid'
            ],
            "FloorPlanPagePage-Page": [
                'pages/FloorPlanPagePage',
                'entities/Site/Site',
                'entities/FloorPlan/FloorPlan',
                'entities/Site/forms/SiteForm3',
                'entities/FloorPlan/lists/FloorPlanList',
                'entities/FloorPlan/forms/FloorPlanForm'
            ],
            "GeneralProductivityReportPagePage-Page": [
                'pages/GeneralProductivityReportPagePage',
                'entities/GeneralProductivityReportFilter/GeneralProductivityReportFilter',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/LoggedHoursVersusSeatHoursView/LoggedHoursVersusSeatHoursView',
                'entities/GeneralProductivityView/GeneralProductivityView',
                'entities/UnitUnutilisationStoreProcedure/UnitUnutilisationStoreProcedure',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/GeneralProductivityPerDriverViewLatest/GeneralProductivityPerDriverViewLatest',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Email/Email',
                'entities/GeneralProductivityPerVehicleView/GeneralProductivityPerVehicleView',
                'entities/UnitUtilisationStoreProcedure/UnitUtilisationStoreProcedure',
                'entities/GeneralProductivityReportFilter/forms/GeneralProductivityReportFilterForm',
                'entities/GeneralProductivityView/forms/GeneralProductivityViewForm',
                'entities/UnitUnutilisationStoreProcedure/grids/UnitUnutilisationStoreProcedureGrid',
                'entities/GeneralProductivityPerDriverViewLatest/grids/GeneralProductivityPerDriverViewGrid',
                'entities/GeneralProductivityPerVehicleView/grids/GeneralProductivityPerVehicleViewGrid',
                'entities/UnitUtilisationStoreProcedure/grids/UnitUtilisationStoreProcedureGrid'
            ],
            "GO2FAConfigurationPage-Page": [
                'pages/GO2FAConfigurationPage',
                'entities/GO2FAConfiguration/GO2FAConfiguration',
                'entities/GO2FAConfiguration/forms/GO2FAConfigurationForm'
            ],
            "HelpPage-Page": [
                'pages/HelpPage'
            ],
            "ImpactReportPage-Page": [
                'pages/ImpactReportPage',
                'entities/AllImpactsView/AllImpactsView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Impact/Impact',
                'entities/Session/Session',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Card/Card',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/Email/Email',
                'entities/ImpactFrequencyPerWeekMonthView/ImpactFrequencyPerWeekMonthView',
                'entities/ImpactReportFilter/ImpactReportFilter',
                'entities/ImpactFrequencyPerWeekDayView/ImpactFrequencyPerWeekDayView',
                'entities/ImpactFrequencyPerTimeSlotView/ImpactFrequencyPerTimeSlotView',
                'entities/AllImpactsView/grids/AllImpactsViewGrid',
                'entities/ImpactReportFilter/forms/ImpactReportFilterForm'
            ],
            "ImportJobPage-Page": [
                'pages/ImportJobPage',
                'entities/ImportJobStatus/ImportJobStatus',
                'entities/ImportJobBatch/ImportJobBatch',
                'entities/ImportJobLog/ImportJobLog',
                'entities/ImportJobStatus/forms/ImportJobStatusForm',
                'entities/ImportJobLog/grids/ImportJobLogGrid',
                'entities/ImportJobStatus/grids/ImportJobStatusGrid'
            ],
            "LicenseExpiryReportPage-Page": [
                'pages/LicenseExpiryReportPage',
                'entities/LicenseExpiryReportFilter/LicenseExpiryReportFilter',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/AllLicenseExpiryView/AllLicenseExpiryView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Email/Email',
                'entities/LicenseExpiryReportFilter/forms/LicenseExpiryReportFilterForm',
                'entities/AllLicenseExpiryView/grids/AllLicenseExpiryViewGrid'
            ],
            "LiveMapPage-Page": [
                'pages/LiveMapPage',
                'entities/SiteFloorPlan/SiteFloorPlan',
                'entities/FloorPlan/FloorPlan',
                'entities/Site/Site',
                'entities/SiteFloorPlan/forms/SiteFloorPlanForm1'
            ],
            "MachineUnlockReportPage-Page": [
                'pages/MachineUnlockReportPage',
                'entities/MachineUnlockReportFilter/MachineUnlockReportFilter',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/AllVehicleUnlocksView/AllVehicleUnlocksView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/VehicleLockout/VehicleLockout',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/Session/Session',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Email/Email',
                'entities/MachineUnlockReportFilter/forms/MachineUnlockReportFilterForm',
                'entities/AllVehicleUnlocksView/grids/AllVehicleUnlocksViewGrid'
            ],
            "ModelItemsPage-Page": [
                'pages/ModelItemsPage',
                'entities/CategoryTemplate/CategoryTemplate',
                'entities/CategoryTemplate/grids/ModelGrid',
                'entities/CategoryTemplate/filters/ModelFilter'
            ],
            "ModelPage-Page": [
                'pages/ModelPage',
                'entities/Model/Model',
                'entities/Vehicle/Vehicle',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Module/Module',
                'entities/BroadcastMessage/BroadcastMessage',
                'entities/UploadLogoRequest/UploadLogoRequest',
                'entities/ImportJobStatus/ImportJobStatus',
                'entities/Model/forms/ModelForm',
                'entities/Vehicle/grids/VehilceGrid',
                'entities/Vehicle/filters/VehicleFilter',
                'entities/VehiclesPerModelReport/VehiclesPerModelReport'
            ],
            "ModuleDetailsPagePage-Page": [
                'pages/ModuleDetailsPagePage',
                'entities/Module/Module',
                'entities/Module/grids/ModuleGrid',
                'entities/Module/filters/ModuleFilter'
            ],
            "ModulePagePage-Page": [
                'pages/ModulePagePage',
                'entities/Module/Module',
                'entities/Module/forms/SensorCalibrationForm'
            ],
            "MyaccountPage-Page": [
                'pages/MyaccountPage',
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/GOUserForm1'
            ],
            "OnDemandAuthorisationReportPage-Page": [
                'pages/OnDemandAuthorisationReportPage',
                'entities/OnDemandAuthorisationFilter/OnDemandAuthorisationFilter',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/OnDemandAuthorisationStoreProcedure/OnDemandAuthorisationStoreProcedure',
                'entities/OnDemandSession/OnDemandSession',
                'entities/Vehicle/Vehicle',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/OnDemandAuthorisationFilter/forms/OnDemandAuthorisationFilterForm',
                'entities/OnDemandAuthorisationStoreProcedure/grids/OnDemandAuthorisationStoreProcedureGrid'
            ],
            "PathAnalysisViewPage-Page": [
                'pages/PathAnalysisViewPage',
                'entities/SiteFloorPlan/SiteFloorPlan',
                'entities/FloorPlan/FloorPlan',
                'entities/Site/Site',
                'entities/SiteFloorPlan/forms/SiteFloorPlanForm1'
            ],
            "PedestrianDetectionReportPage-Page": [
                'pages/PedestrianDetectionReportPage',
                'entities/PedestrianDetectionHistory/PedestrianDetectionHistory',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Vehicle/Vehicle',
                'entities/Department/Department',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Site/Site',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/Email/Email',
                'entities/PedestrianDetectionHistoryFilter/PedestrianDetectionHistoryFilter',
                'entities/PedestrianDetectionHistory/grids/PedestrianDetectionHistoryGrid',
                'entities/PedestrianDetectionHistoryFilter/forms/PedestrianDetectionHistoryFilterForm'
            ],
            "PersonDetailPagePage-Page": [
                'pages/PersonDetailPagePage',
                'entities/Person/Person',
                'entities/Customer/Customer',
                'entities/EmailGroups/EmailGroups',
                'entities/GOUser/GOUser',
                'entities/GOUserDepartment/GOUserDepartment',
                'entities/Driver/Driver',
                'entities/Card/Card',
                'entities/LicenseByModel/LicenseByModel',
                'entities/Model/Model',
                'entities/LicenceDetail/LicenceDetail',
                'entities/PersonChecklistLanguageSettings/PersonChecklistLanguageSettings',
                'entities/AccessGroup/AccessGroup',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/PersonToSiteVehicleNormalAccessView/PersonToSiteVehicleNormalAccessView',
                'entities/PersonToModelVehicleNormalAccessView/PersonToModelVehicleNormalAccessView',
                'entities/PersonToDepartmentVehicleNormalAccessView/PersonToDepartmentVehicleNormalAccessView',
                'entities/PersonToPerVehicleNormalAccessView/PersonToPerVehicleNormalAccessView',
                'entities/ReportSubscription/ReportSubscription',
                'entities/ReportType/ReportType',
                'entities/AlertSubscription/AlertSubscription',
                'entities/Alert/Alert',
                'entities/GOUser2FA/GOUser2FA',
                'entities/Person/forms/PersonForm',
                'entities/PersonChecklistLanguageSettings/forms/PersonChecklistLanguageSettingsForm',
                'entities/Person/forms/PersonWebsiteAccessForm',
                'entities/GOUser/forms/GOUserForm',
                'entities/Card/forms/CardDetailsForm',
                'entities/Driver/forms/DriverLicensesForm',
                'entities/Person/forms/LicenseActiveForm',
                'entities/LicenceDetail/forms/LicenceDetailCardForm',
                'entities/LicenseByModel/lists/LicenseByModelList',
                'entities/LicenseByModel/forms/LicenseByModelForm',
                'entities/Person/forms/PersonDetailsHeaderForm',
                'entities/Person/forms/PersonVehicleAccessForm',
                'entities/PersonToModelVehicleNormalAccessView/lists/PersonToModelVehicleNormalAccessViewList',
                'entities/PersonToModelVehicleNormalAccessView/forms/PersonToModelVehicleNormalAccessViewForm',
                'entities/PersonToSiteVehicleNormalAccessView/lists/PersonToSiteVehicleNormalAccessViewList',
                'entities/PersonToSiteVehicleNormalAccessView/forms/PersonToSiteVehicleNormalAccessViewForm',
                'entities/PersonToDepartmentVehicleNormalAccessView/lists/PersonToDepartmentVehicleNormalAccessViewList',
                'entities/PersonToDepartmentVehicleNormalAccessView/forms/PersonToDepartmentVehicleNormalAccessViewForm',
                'entities/PersonToPerVehicleNormalAccessView/lists/PersonToPerVehicleNormalAccessViewList',
                'entities/PersonToPerVehicleNormalAccessView/forms/PersonToPerVehicleNormalAccessViewForm',
                'entities/Person/forms/PersonAlertSubscriptionsForm',
                'entities/ReportSubscription/grids/ReportSubscriptionGrid',
                'entities/AlertSubscription/grids/AlertSubscriptionItemsGrid',
                'entities/Person/forms/SupervisorVehicleAccessForm',
                'entities/PersonToModelVehicleNormalAccessView/lists/PersonToModelVehicleMasterAccessViewList',
                'entities/PersonToModelVehicleNormalAccessView/forms/PersonToModelVehicleMasterAccessViewForm',
                'entities/PersonToPerVehicleNormalAccessView/lists/PersonToPerVehicleMasterAccessViewList',
                'entities/PersonToPerVehicleNormalAccessView/forms/PersonToPerVehicleMasterAccessViewForm',
                'entities/PersonToSiteVehicleNormalAccessView/lists/PersonToSiteVehicleMasterAccessViewList',
                'entities/PersonToSiteVehicleNormalAccessView/forms/PersonToSiteVehicleMasterAccessViewForm',
                'entities/PersonToDepartmentVehicleNormalAccessView/lists/PersonToDepartmentVehicleMasterAccessViewList',
                'entities/PersonToDepartmentVehicleNormalAccessView/forms/PersonToDepartmentVehicleMasterAccessViewForm',
                'entities/Person/forms/PersonInformationForm',
                'entities/Person/forms/SupervisorAuthorization',
                'entities/EmailGroups/grids/EmailGroupsGrid',
                'entities/GOUserDepartment/grids/GOUserDepartmentGrid'
            ],
            "PersonItemsPage-Page": [
                'pages/PersonItemsPage',
                'entities/Person/Person',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Driver/Driver',
                'entities/Card/Card',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/ImportJobStatus/ImportJobStatus',
                'entities/Person/grids/PersonGrid',
                'entities/Person/filters/PersonFilter'
            ],
            "PreOpCheckReportPage-Page": [
                'pages/PreOpCheckReportPage',
                'entities/PreOpReportFilter/PreOpReportFilter',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/AllChecklistResultView/AllChecklistResultView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/ChecklistResult/ChecklistResult',
                'entities/Session/Session',
                'entities/Vehicle/Vehicle',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Model/Model',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/IncompletedChecklistView/IncompletedChecklistView',
                'entities/ChecklistStatusView/ChecklistStatusView',
                'entities/PreOpReportFilter/forms/PreOpReportFilterForm',
                'entities/AllChecklistResultView/grids/AllChecklistResultViewGrid1'
            ],
            "ProficiencyReportPage-Page": [
                'pages/ProficiencyReportPage',
                'entities/ProficiencyCombinedView/ProficiencyCombinedView',
                'entities/DriverProficiencyView/DriverProficiencyView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/VehicleProficiencyView/VehicleProficiencyView',
                'entities/Vehicle/Vehicle',
                'entities/ProficiencyReportFilter/ProficiencyReportFilter',
                'entities/ProficiencyCombinedView/forms/ProficiencyCombinedViewForm',
                'entities/DriverProficiencyView/grids/DriverProficiencyViewGrid',
                'entities/VehicleProficiencyView/grids/VehicleProficiencyViewGrid',
                'entities/ProficiencyReportFilter/forms/ProficiencyReportFilterForm'
            ],
            "RegionItemsPage-Page": [
                'pages/RegionItemsPage',
                'entities/Region/Region',
                'entities/Region/grids/RegionGrid',
                'entities/Region/filters/RegionFilter'
            ],
            "RegionPage-Page": [
                'pages/RegionPage',
                'entities/Region/Region',
                'entities/Region/forms/RegionForm'
            ],
            "ReportSchedulerPage-Page": [
                'pages/ReportSchedulerPage',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/ReportSubscription/grids/ReportSubscriptionGrid1'
            ],
            "ServiceCheckReportPage-Page": [
                'pages/ServiceCheckReportPage',
                'entities/ServiceSettings/ServiceSettings',
                'entities/Vehicle/Vehicle',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/DashboardFilter/DashboardFilter',
                'entities/ServiceSettings/forms/ServiceSettingsForm1',
                'entities/Vehicle/grids/VehicleGrid',
                'entities/DashboardFilter/forms/DashboardFilterForm'
            ],
            "ServiceSettingsDetailsPage-Page": [
                'pages/ServiceSettingsDetailsPage',
                'entities/ServiceSettings/ServiceSettings',
                'entities/ServiceSettings/forms/ServiceSettingsForm'
            ],
            "ServiceSettingsPagePage-Page": [
                'pages/ServiceSettingsPagePage',
                'entities/ServiceSettings/ServiceSettings',
                'entities/ServiceSettings/grids/ServiceSettingsGrid'
            ],
            "SessionDetailsPagePage-Page": [
                'pages/SessionDetailsPagePage',
                'entities/Session/Session',
                'entities/Session/forms/SessionForm'
            ],
            "SuperAdminPage-Page": [
                'pages/SuperAdminPage',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/Dealer/Dealer',
                'entities/GOUserRole/GOUserRole',
                'entities/GORole/GORole',
                'entities/GOUser/grids/ActiveUsersGrid',
                'entities/GOUser/forms/GOUserForm2',
                'entities/GOUserRole/grids/GOUserRoleGrid'
            ],
            "SynchronizationStatusReportPage-Page": [
                'pages/SynchronizationStatusReportPage',
                'entities/AllMessageHistoryStoreProcedure/AllMessageHistoryStoreProcedure',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/MessageHistory/MessageHistory',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/Person/Person',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/SynchronizationStatusReportFilter/SynchronizationStatusReportFilter',
                'entities/AllMessageHistoryStoreProcedure/grids/AllMessageHistoryStoreProcedureGrid',
                'entities/SynchronizationStatusReportFilter/forms/SynchronizationStatusReportFilterForm'
            ],
            "TimezoneItemsPage-Page": [
                'pages/TimezoneItemsPage',
                'entities/Timezone/Timezone',
                'entities/Timezone/grids/TimezoneGrid',
                'entities/Timezone/filters/TimezoneFilter'
            ],
            "TimezonePage-Page": [
                'pages/TimezonePage',
                'entities/Timezone/Timezone',
                'entities/Timezone/forms/TimezoneForm'
            ],
            "UserEmailAlertSummaryReportPage-Page": [
                'pages/UserEmailAlertSummaryReportPage',
                'entities/DashboardFilter/DashboardFilter',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/AlertSubscription/AlertSubscription',
                'entities/Person/Person',
                'entities/Alert/Alert',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/DashboardFilter/forms/DashboardFilterForm',
                'entities/AlertSubscription/grids/AlertSubscriptionGrid'
            ],
            "UserSummaryReportPage-Page": [
                'pages/UserSummaryReportPage',
                'entities/DashboardFilter/DashboardFilter',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/AllUserSummaryStoreProcedure/AllUserSummaryStoreProcedure',
                'entities/Person/Person',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/AccessGroup/AccessGroup',
                'entities/DashboardFilter/forms/DashboardFilterForm',
                'entities/AllUserSummaryStoreProcedure/grids/AllUserSummaryStoreProcedureGrid'
            ],
            "VehicleCalibrationReportPage-Page": [
                'pages/VehicleCalibrationReportPage',
                'entities/AllVehicleCalibrationFilter/AllVehicleCalibrationFilter',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/AllVehicleCalibrationStoreProcedure/AllVehicleCalibrationStoreProcedure',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Vehicle/Vehicle',
                'entities/Module/Module',
                'entities/Model/Model',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/AllVehicleCalibrationFilter/forms/AllVehicleCalibrationFilterForm',
                'entities/AllVehicleCalibrationStoreProcedure/grids/AllVehicleCalibrationStoreProcedureGridAdmin',
                'entities/AllVehicleCalibrationStoreProcedure/grids/AllVehicleCalibrationStoreProcedureGridCustomer'
            ],
            "VehicleDetailsPagePage-Page": [
                'pages/VehicleDetailsPagePage',
                'entities/Vehicle/Vehicle',
                'entities/ServiceSettings/ServiceSettings',
                'entities/ChecklistSettings/ChecklistSettings',
                'entities/DepartmentChecklist/DepartmentChecklist',
                'entities/VehicleToPreOpChecklistView/VehicleToPreOpChecklistView',
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Department/Department',
                'entities/Model/Model',
                'entities/Inspection/Inspection',
                'entities/Module/Module',
                'entities/Site/Site',
                'entities/Canrule/Canrule',
                'entities/Customer/Customer',
                'entities/VehicleOtherSettings/VehicleOtherSettings',
                'entities/NetworkSettings/NetworkSettings',
                'entities/Vehicle/forms/VehicleForm',
                'entities/ServiceSettings/forms/VehicleServiceSettings',
                'entities/Vehicle/forms/VehicleChecklistForm',
                'entities/ChecklistSettings/forms/ChecklistSettingsForm',
                'entities/DepartmentChecklist/forms/DepartmentChecklistForm',
                'entities/VehicleToPreOpChecklistView/grids/VehicleToPreOpChecklistGrid',
                'entities/VehicleToPreOpChecklistView/filters/VehicleToPreOpChecklistViewFilter',
                'entities/Inspection/forms/InspectionForm',
                'entities/Vehicle/forms/ImpactSettings',
                'entities/Module/forms/AmberImpactForm',
                'entities/Module/forms/ModuleForm',
                'entities/Module/forms/RedImpactForm',
                'entities/Module/forms/GForceRequiredToCauseImpacts',
                'entities/Module/forms/SensorCalibrationForm',
                'entities/Vehicle/forms/VehicleInformationForm',
                'entities/Module/forms/ModuleForm2',
                'entities/Vehicle/forms/VehicleForm1',
                'entities/VehicleOtherSettings/forms/VehicleOtherSettingsForm',
                'entities/Vehicle/forms/VehicleNetworkSettingsForm',
                'entities/NetworkSettings/grids/NetworkSettingsGrid'
            ],
            "VehicleHireDehireReportPage-Page": [
                'pages/VehicleHireDehireReportPage',
                'entities/VehicleHireDehireHistory/VehicleHireDehireHistory',
                'entities/Vehicle/Vehicle',
                'entities/Module/Module',
                'entities/HireDeHireReportFilter/HireDeHireReportFilter',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/VehicleHireDehireHistory/grids/VehicleHireDehireHistoryGrid',
                'entities/HireDeHireReportFilter/forms/HireDeHireReportFilterForm'
            ],
            "VehiclesGPSReportPage-Page": [
                'pages/VehiclesGPSReportPage',
                'entities/Vehicle/Vehicle',
                'entities/VehicleGPS/VehicleGPS',
                'entities/Model/Model',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/VehicleLastGPSLocationView/VehicleLastGPSLocationView',
                'entities/Module/Module',
                'entities/Vehicle/grids/VehiclesGPSLocationsGrid',
                'entities/Vehicle/filters/VehicleFilter'
            ],
            "VehilceItemsPage-Page": [
                'pages/VehilceItemsPage',
                'entities/Vehicle/Vehicle',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Model/Model',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Module/Module',
                'entities/BroadcastMessage/BroadcastMessage',
                'entities/UploadLogoRequest/UploadLogoRequest',
                'entities/ImportJobStatus/ImportJobStatus',
                'entities/Vehicle/grids/VehilceGrid',
                'entities/Vehicle/filters/VehicleFilter'
            ],
            "VORReportPage-Page": [
                'pages/VORReportPage',
                'entities/VORReportCombinedView/VORReportCombinedView',
                'entities/AllVORSessionsPerVehicleStoreProcedure/AllVORSessionsPerVehicleStoreProcedure',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Vehicle/Vehicle',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/AllVORStatusStoreProcedure/AllVORStatusStoreProcedure',
                'entities/VORSettingHistory/VORSettingHistory',
                'entities/Person/Person',
                'entities/VORReportFilter/VORReportFilter',
                'entities/VORReportCombinedView/forms/VORReportCombinedViewForm',
                'entities/AllVORSessionsPerVehicleStoreProcedure/grids/AllVORSessionsPerVehicleStoreProcedureGrid',
                'entities/AllVORStatusStoreProcedure/grids/AllVORStatusStoreProcedureGrid',
                'entities/VORReportFilter/forms/VORReportFilterForm'
            ],
            "WebsiteuserPage1-Page": [
                'pages/WebsiteuserPage1',
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/GOUserForm'
            ],
            "WebsiteusersPage-Page": [
                'pages/WebsiteusersPage',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/GOUser/grids/GOUserGrid'
            ],
            "AccessGroupForm-Form": [
                'entities/AccessGroup/AccessGroup',
                'entities/AccessGroup/forms/AccessGroupForm',
                'entities/AccessGroupToSite/AccessGroupToSite',
                'entities/Site/Site',
                'entities/AccessGroup/forms/UsersAccessGroupForm',
                'entities/AccessGroup/forms/AccessGroupForm2',
                'entities/AccessGroup/forms/AccessGroupForm3',
                'entities/AccessGroup/forms/AccessGroupForm1',
                'entities/AccessGroupToSite/grids/AccessGroupToSiteGrid'
            ],
            "AccessGroupForm1-Form": [
                'entities/AccessGroup/AccessGroup',
                'entities/AccessGroup/forms/AccessGroupForm1'
            ],
            "AccessGroupForm2-Form": [
                'entities/AccessGroup/AccessGroup',
                'entities/AccessGroup/forms/AccessGroupForm2'
            ],
            "AccessGroupForm3-Form": [
                'entities/AccessGroup/AccessGroup',
                'entities/AccessGroup/forms/AccessGroupForm3'
            ],
            "AccessGroupTemplateForm-Form": [
                'entities/AccessGroupTemplate/AccessGroupTemplate',
                'entities/AccessGroupTemplate/forms/AccessGroupTemplateForm',
                'entities/AccessGroupTemplate/forms/ReportsAccessGroupTemplateForm',
                'entities/AccessGroupTemplate/forms/CustomerAccessGroupTemplateForm',
                'entities/AccessGroupTemplate/forms/VehiclesAccessGroupTemplateForm',
                'entities/AccessGroupTemplate/forms/UsersAccessGroupTemplateForm'
            ],
            "AccessGroupToSiteForm-Form": [
                'entities/AccessGroupToSite/AccessGroupToSite',
                'entities/AccessGroupToSite/forms/AccessGroupToSiteForm',
                'entities/Site/Site'
            ],
            "AddCustomerCategoryForm-Form": [
                'entities/CustomerModel/CustomerModel',
                'entities/CustomerModel/forms/AddCustomerCategoryForm',
                'entities/Model/Model',
                'entities/Model/grids/SelectDealerCategoryGrid',
                'entities/Model/filters/ModelFilter1'
            ],
            "AddVehicleAlertSubscription-Form": [
                'entities/VehicleAlertSubscription/VehicleAlertSubscription',
                'entities/VehicleAlertSubscription/forms/AddVehicleAlertSubscription',
                'entities/Vehicle/Vehicle',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Module/Module',
                'entities/Vehicle/grids/SelectVehiclesForAlertGrid',
                'entities/Vehicle/filters/SelectVehicleForAlertFilter'
            ],
            "AlertSubscriptionForm-Form": [
                'entities/AlertSubscription/AlertSubscription',
                'entities/AlertSubscription/forms/AlertSubscriptionForm',
                'entities/Alert/Alert',
                'entities/VehicleAlertSubscription/VehicleAlertSubscription',
                'entities/Vehicle/Vehicle',
                'entities/Module/Module',
                'entities/VehicleAlertSubscription/grids/VehicleAlertItemsGrid'
            ],
            "AlertSubscriptionForm1-Form": [
                'entities/AlertSubscription/AlertSubscription',
                'entities/AlertSubscription/forms/AlertSubscriptionForm1',
                'entities/VehicleAlertSubscription/VehicleAlertSubscription',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/Module/Module',
                'entities/Person/Person',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/VehicleAlertSubscription/grids/VehicleAlertSubscriptionGrid'
            ],
            "AllChecklistResultViewForm-Form": [
                'entities/AllChecklistResultView/AllChecklistResultView',
                'entities/AllChecklistResultView/forms/AllChecklistResultViewForm',
                'entities/ChecklistResult/ChecklistResult',
                'entities/ChecklistDetail/ChecklistDetail',
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/ChecklistDetail/grids/ChecklistDetailGrid'
            ],
            "AllVehicleCalibrationFilterForm-Form": [
                'entities/AllVehicleCalibrationFilter/AllVehicleCalibrationFilter',
                'entities/AllVehicleCalibrationFilter/forms/AllVehicleCalibrationFilterForm',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer'
            ],
            "AllVehicleUnlocksViewForm-Form": [
                'entities/AllVehicleUnlocksView/AllVehicleUnlocksView',
                'entities/AllVehicleUnlocksView/forms/AllVehicleUnlocksViewForm',
                'entities/VehicleLockout/VehicleLockout',
                'entities/VehicleLockout/forms/VehicleLockoutForm'
            ],
            "AmberImpactForm-Form": [
                'entities/Module/Module',
                'entities/Module/forms/AmberImpactForm'
            ],
            "ApplyAccessGroupTemplateForm-Form": [
                'entities/AccessGroupTemplate/AccessGroupTemplate',
                'entities/AccessGroupTemplate/forms/ApplyAccessGroupTemplateForm',
                'entities/Customer/Customer',
                'entities/Customer/grids/SelectCustomersGrid'
            ],
            "ApplyCategoryToCustomersForm-Form": [
                'entities/Model/Model',
                'entities/Model/forms/ApplyCategoryToCustomersForm',
                'entities/Customer/Customer',
                'entities/Customer/grids/SelectCustomersGrid'
            ],
            "BroadcastMessageForm-Form": [
                'entities/BroadcastMessage/BroadcastMessage',
                'entities/BroadcastMessage/forms/BroadcastMessageForm',
                'entities/UploadLogoRequest/UploadLogoRequest',
                'entities/Vehicle/Vehicle',
                'entities/Module/Module',
                'entities/Vehicle/grids/SelectVehiclesForBroadcastMessageGrid'
            ],
            "BroadcastMessageHistoryFilterForm-Form": [
                'entities/BroadcastMessageHistoryFilter/BroadcastMessageHistoryFilter',
                'entities/BroadcastMessageHistoryFilter/forms/BroadcastMessageHistoryFilterForm',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site'
            ],
            "BulkUpdateFirmwareForm-Form": [
                'entities/UpdateFirmwareRequest/UpdateFirmwareRequest',
                'entities/UpdateFirmwareRequest/forms/BulkUpdateFirmwareForm',
                'entities/Firmware/Firmware',
                'entities/Vehicle/Vehicle',
                'entities/Module/Module',
                'entities/Vehicle/grids/SelectVehiclesForFirmwareUpdate'
            ],
            "CardDetailsCreateNewForm-Form": [
                'entities/Card/Card',
                'entities/Card/forms/CardDetailsCreateNewForm'
            ],
            "CardDetailsForm-Form": [
                'entities/Card/Card',
                'entities/Card/forms/CardDetailsForm'
            ],
            "ChecklistDetailForm-Form": [
                'entities/ChecklistDetail/ChecklistDetail',
                'entities/ChecklistDetail/forms/ChecklistDetailForm',
                'entities/PreOperationalChecklist/PreOperationalChecklist'
            ],
            "ChecklistResultForm-Form": [
                'entities/ChecklistResult/ChecklistResult',
                'entities/ChecklistResult/forms/ChecklistResultForm',
                'entities/ChecklistDetail/ChecklistDetail',
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/ChecklistDetail/grids/ChecklistDetailGrid'
            ],
            "ChecklistSettingsForm-Form": [
                'entities/ChecklistSettings/ChecklistSettings',
                'entities/ChecklistSettings/forms/ChecklistSettingsForm'
            ],
            "ContactPersonInformationForm-Form": [
                'entities/ContactPersonInformation/ContactPersonInformation',
                'entities/ContactPersonInformation/forms/ContactPersonInformationForm'
            ],
            "ContactPersonInformationForm1-Form": [
                'entities/ContactPersonInformation/ContactPersonInformation',
                'entities/ContactPersonInformation/forms/ContactPersonInformationForm1'
            ],
            "CopyAccessGroupForm-Form": [
                'entities/AccessGroup/AccessGroup',
                'entities/AccessGroup/forms/CopyAccessGroupForm',
                'entities/Customer/Customer',
                'entities/Customer/grids/SelectCustomersGrid'
            ],
            "CopyDriverAccessSettingsForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/CopyDriverAccessSettingsForm',
                'entities/Customer/Customer',
                'entities/Driver/Driver',
                'entities/Site/Site',
                'entities/Driver/grids/SelectDriverGrid',
                'entities/Driver/filters/SelectDriverFilter'
            ],
            "CountryCreateNewForm-Form": [
                'entities/Country/Country',
                'entities/Country/forms/CountryCreateNewForm'
            ],
            "CountryForm-Form": [
                'entities/Country/Country',
                'entities/Country/forms/CountryForm',
                'entities/Customer/Customer',
                'entities/Customer/grids/CustomerGrid',
                'entities/Customer/filters/CustomerFilter'
            ],
            "CreateNewDealerUserForm-Form": [
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/CreateNewDealerUserForm'
            ],
            "CurrentStatusCombinedViewForm-Form": [
                'entities/CurrentStatusCombinedView/CurrentStatusCombinedView',
                'entities/CurrentStatusCombinedView/forms/CurrentStatusCombinedViewForm',
                'entities/CurrentStatusDriverView/CurrentStatusDriverView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Email/Email',
                'entities/CurrentStatusVehicleView/CurrentStatusVehicleView',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/CurrentStatusDriverView/grids/CurrentStatusDriverViewGrid',
                'entities/CurrentStatusVehicleView/grids/CurrentStatusVehicleViewGrid'
            ],
            "CustomerAccessGroupTemplateForm-Form": [
                'entities/AccessGroupTemplate/AccessGroupTemplate',
                'entities/AccessGroupTemplate/forms/CustomerAccessGroupTemplateForm'
            ],
            "CustomerCreateNewForm-Form": [
                'entities/Customer/Customer',
                'entities/Customer/forms/CustomerCreateNewForm',
                'entities/Country/Country',
                'entities/Dealer/Dealer'
            ],
            "CustomerFeatureSubscriptionForm-Form": [
                'entities/CustomerFeatureSubscription/CustomerFeatureSubscription',
                'entities/CustomerFeatureSubscription/forms/CustomerFeatureSubscriptionForm'
            ],
            "CustomerForm-Form": [
                'entities/Customer/Customer',
                'entities/Customer/forms/CustomerForm',
                'entities/ContactPersonInformation/ContactPersonInformation',
                'entities/Country/Country',
                'entities/EmailGroups/EmailGroups',
                'entities/CustomerModel/CustomerModel',
                'entities/Model/Model',
                'entities/Site/Site',
                'entities/Timezone/Timezone',
                'entities/AccessGroup/AccessGroup',
                'entities/CustomerPreOperationalChecklistTemplate/CustomerPreOperationalChecklistTemplate',
                'entities/Customer/forms/CustomerForm2',
                'entities/Customer/forms/CustomerForm1',
                'entities/ContactPersonInformation/forms/ContactPersonInformationForm1',
                'entities/EmailGroups/grids/EmailGroupsGrid',
                'entities/CustomerModel/grids/CustomerModelGrid',
                'entities/Site/grids/SiteGrid1',
                'entities/AccessGroup/grids/AccessGroupGrid',
                'entities/CustomerPreOperationalChecklistTemplate/grids/CustomerPreOperationalChecklistTemplateGrid'
            ],
            "CustomerForm1-Form": [
                'entities/Customer/Customer',
                'entities/Customer/forms/CustomerForm1',
                'entities/Country/Country'
            ],
            "CustomerForm2-Form": [
                'entities/Customer/Customer',
                'entities/Customer/forms/CustomerForm2',
                'entities/ContactPersonInformation/ContactPersonInformation'
            ],
            "CustomerForm3-Form": [
                'entities/Customer/Customer',
                'entities/Customer/forms/CustomerForm3',
                'entities/CustomerFeatureSubscription/CustomerFeatureSubscription',
                'entities/CustomerFeatureSubscription/forms/CustomerFeatureSubscriptionForm'
            ],
            "CustomerForm4-Form": [
                'entities/Customer/Customer',
                'entities/Customer/forms/CustomerForm4',
                'entities/CustomerFeatureSubscription/CustomerFeatureSubscription'
            ],
            "CustomerModelForm-Form": [
                'entities/CustomerModel/CustomerModel',
                'entities/CustomerModel/forms/CustomerModelForm',
                'entities/Model/Model'
            ],
            "CustomerPreOperationalChecklistTemplateForm-Form": [
                'entities/CustomerPreOperationalChecklistTemplate/CustomerPreOperationalChecklistTemplate',
                'entities/CustomerPreOperationalChecklistTemplate/forms/CustomerPreOperationalChecklistTemplateForm'
            ],
            "CustomerPreOperationalChecklistTemplateForm1-Form": [
                'entities/CustomerPreOperationalChecklistTemplate/CustomerPreOperationalChecklistTemplate',
                'entities/CustomerPreOperationalChecklistTemplate/forms/CustomerPreOperationalChecklistTemplateForm1'
            ],
            "CustomerPreOperationalChecklistTemplateForm2-Form": [
                'entities/CustomerPreOperationalChecklistTemplate/CustomerPreOperationalChecklistTemplate',
                'entities/CustomerPreOperationalChecklistTemplate/forms/CustomerPreOperationalChecklistTemplateForm2',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/CustomerModel/CustomerModel',
                'entities/Model/Model',
                'entities/DepartmentChecklist/DepartmentChecklist',
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/CustomerModel/grids/CustomerModelGrid1'
            ],
            "CustomerSSODetailForm-Form": [
                'entities/CustomerSSODetail/CustomerSSODetail',
                'entities/CustomerSSODetail/forms/CustomerSSODetailForm',
                'entities/Customer/Customer'
            ],
            "CustomerToModelForm-Form": [
                'entities/CustomerToModel/CustomerToModel',
                'entities/CustomerToModel/forms/CustomerToModelForm',
                'entities/Model/Model'
            ],
            "DashboardCardViewForm-Form": [
                'entities/DashboardDriverCardView/DashboardDriverCardView',
                'entities/DashboardDriverCardView/forms/DashboardCardViewForm',
                'entities/DashboardVehicleCardView/DashboardVehicleCardView',
                'entities/DashboardVehicleCardView/forms/DashboardCardViewForm2',
                'entities/DashboardDriverCardView/forms/DashboardCardViewForm1',
                'entities/DashboardVehicleCardView/forms/DashboardCardViewForm3'
            ],
            "DashboardCardViewForm1-Form": [
                'entities/DashboardDriverCardView/DashboardDriverCardView',
                'entities/DashboardDriverCardView/forms/DashboardCardViewForm1'
            ],
            "DashboardCardViewForm2-Form": [
                'entities/DashboardVehicleCardView/DashboardVehicleCardView',
                'entities/DashboardVehicleCardView/forms/DashboardCardViewForm2'
            ],
            "DashboardCardViewForm3-Form": [
                'entities/DashboardVehicleCardView/DashboardVehicleCardView',
                'entities/DashboardVehicleCardView/forms/DashboardCardViewForm3'
            ],
            "DashboardDriverCardStoreProcedureForm-Form": [
                'entities/DashboardDriverCardStoreProcedure/DashboardDriverCardStoreProcedure',
                'entities/DashboardDriverCardStoreProcedure/forms/DashboardDriverCardStoreProcedureForm'
            ],
            "DashboardFilterForm-Form": [
                'entities/DashboardFilter/DashboardFilter',
                'entities/DashboardFilter/forms/DashboardFilterForm',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/Department/Department'
            ],
            "DashboardVehicleCardStoreProcedureForm-Form": [
                'entities/DashboardVehicleCardStoreProcedure/DashboardVehicleCardStoreProcedure',
                'entities/DashboardVehicleCardStoreProcedure/forms/DashboardVehicleCardStoreProcedureForm'
            ],
            "DashboardVehicleCardStoreProcedureForm1-Form": [
                'entities/DashboardVehicleCardStoreProcedure/DashboardVehicleCardStoreProcedure',
                'entities/DashboardVehicleCardStoreProcedure/forms/DashboardVehicleCardStoreProcedureForm1'
            ],
            "DealerCreateNewForm-Form": [
                'entities/Dealer/Dealer',
                'entities/Dealer/forms/DealerCreateNewForm',
                'entities/Region/Region'
            ],
            "DealerDriverForm-Form": [
                'entities/DealerDriver/DealerDriver',
                'entities/DealerDriver/forms/DealerDriverForm'
            ],
            "DealerFeatureSubscriptionForm-Form": [
                'entities/DealerFeatureSubscription/DealerFeatureSubscription',
                'entities/DealerFeatureSubscription/forms/DealerFeatureSubscriptionForm'
            ],
            "DealerForm-Form": [
                'entities/Dealer/Dealer',
                'entities/Dealer/forms/DealerForm',
                'entities/Region/Region'
            ],
            "DealerForm1-Form": [
                'entities/Dealer/Dealer',
                'entities/Dealer/forms/DealerForm1',
                'entities/Region/Region',
                'entities/Customer/Customer',
                'entities/Model/Model',
                'entities/Module/Module',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/Dealer/forms/DealerForm',
                'entities/Dealer/forms/DealerForm2',
                'entities/Customer/grids/CustomerGrid',
                'entities/Customer/filters/CustomerFilter',
                'entities/Model/grids/ModelGrid1',
                'entities/Module/grids/ModuleGrid1',
                'entities/Module/filters/SpareModuleFilter',
                'entities/GOUser/grids/GOUserGrid1'
            ],
            "DealerForm2-Form": [
                'entities/Dealer/Dealer',
                'entities/Dealer/forms/DealerForm2',
                'entities/Region/Region'
            ],
            "DealerForm3-Form": [
                'entities/Dealer/Dealer',
                'entities/Dealer/forms/DealerForm3',
                'entities/DealerFeatureSubscription/DealerFeatureSubscription',
                'entities/DealerFeatureSubscription/forms/DealerFeatureSubscriptionForm'
            ],
            "DealerForm4-Form": [
                'entities/Dealer/Dealer',
                'entities/Dealer/forms/DealerForm4',
                'entities/Customer/Customer',
                'entities/Customer/grids/SelectCustomersGrid'
            ],
            "DealerUserDetailForm-Form": [
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/DealerUserDetailForm',
                'entities/DealerDriver/DealerDriver',
                'entities/Card/Card',
                'entities/Card/forms/CardDetailsForm',
                'entities/DealerDriver/forms/DealerDriverForm'
            ],
            "DepartmentChecklistForm-Form": [
                'entities/DepartmentChecklist/DepartmentChecklist',
                'entities/DepartmentChecklist/forms/DepartmentChecklistForm'
            ],
            "DepartmentCreateNewForm-Form": [
                'entities/Department/Department',
                'entities/Department/forms/DepartmentCreateNewForm'
            ],
            "DepartmentForm-Form": [
                'entities/Department/Department',
                'entities/Department/forms/DepartmentForm',
                'entities/DepartmentHourSettings/DepartmentHourSettings',
                'entities/DepartmentHourSettings/forms/DepartmentHourSettingsForm'
            ],
            "DepartmentHourSettingsForm-Form": [
                'entities/DepartmentHourSettings/DepartmentHourSettings',
                'entities/DepartmentHourSettings/forms/DepartmentHourSettingsForm'
            ],
            "DriverAccessAbuseFilterForm-Form": [
                'entities/DriverAccessAbuseFilter/DriverAccessAbuseFilter',
                'entities/DriverAccessAbuseFilter/forms/DriverAccessAbuseFilterForm',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer'
            ],
            "DriverForm3-Form": [
                'entities/Card/Card',
                'entities/Card/forms/DriverForm3',
                'entities/SiteVehicleNormalCardAccess/SiteVehicleNormalCardAccess',
                'entities/Site/Site',
                'entities/SiteVehicleNormalCardAccess/grids/SiteVehicleNormalCardAccessGrid'
            ],
            "DriverForm4-Form": [
                'entities/Driver/Driver',
                'entities/Driver/forms/DriverForm4',
                'entities/Card/Card',
                'entities/Card/forms/CardDetailsForm'
            ],
            "DriverLicensesForm-Form": [
                'entities/Driver/Driver',
                'entities/Driver/forms/DriverLicensesForm',
                'entities/Person/Person',
                'entities/LicenceDetail/LicenceDetail',
                'entities/LicenseByModel/LicenseByModel',
                'entities/Model/Model',
                'entities/Person/forms/LicenseActiveForm',
                'entities/LicenceDetail/forms/LicenceDetailCardForm',
                'entities/LicenseByModel/lists/LicenseByModelList',
                'entities/LicenseByModel/forms/LicenseByModelForm'
            ],
            "DriverSessionsForm-Form": [
                'entities/GeneralProductivityPerDriverViewLatest/GeneralProductivityPerDriverViewLatest',
                'entities/GeneralProductivityPerDriverViewLatest/forms/DriverSessionsForm',
                'entities/Driver/Driver',
                'entities/DetailedSessionView/DetailedSessionView',
                'entities/Vehicle/Vehicle',
                'entities/Person/Person',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/DetailedSessionView/grids/DriverDetailedSessionViewGrid'
            ],
            "EditSlamcoreDeviceForm-Form": [
                'entities/SlamcoreDevice/SlamcoreDevice',
                'entities/SlamcoreDevice/forms/EditSlamcoreDeviceForm',
                'entities/Vehicle/Vehicle'
            ],
            "EmailGroupsForm-Form": [
                'entities/EmailGroups/EmailGroups',
                'entities/EmailGroups/forms/EmailGroupsForm'
            ],
            "EmailGroupsForm1-Form": [
                'entities/EmailGroups/EmailGroups',
                'entities/EmailGroups/forms/EmailGroupsForm1',
                'entities/EmailGroupsToPerson/EmailGroupsToPerson',
                'entities/Person/Person',
                'entities/GOUser/GOUser',
                'entities/GOUser2FA/GOUser2FA',
                'entities/ReportSubscription/ReportSubscription',
                'entities/ReportType/ReportType',
                'entities/EmailGroupsToPerson/grids/EmailGroupsToPersonGrid',
                'entities/ReportSubscription/grids/ReportSubscriptionGrid'
            ],
            "EmailGroupsToPersonForm-Form": [
                'entities/EmailGroupsToPerson/EmailGroupsToPerson',
                'entities/EmailGroupsToPerson/forms/EmailGroupsToPersonForm',
                'entities/Person/Person'
            ],
            "EmailSubscriptionReportFilterForm-Form": [
                'entities/EmailSubscriptionReportFilter/EmailSubscriptionReportFilter',
                'entities/EmailSubscriptionReportFilter/forms/EmailSubscriptionReportFilterForm',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/Site/Site'
            ],
            "ExportJobStatusForm-Form": [
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/ExportJobStatus/forms/ExportJobStatusForm'
            ],
            "FeatureSubscriptionsFilterForm-Form": [
                'entities/FeatureSubscriptionsFilter/FeatureSubscriptionsFilter',
                'entities/FeatureSubscriptionsFilter/forms/FeatureSubscriptionsFilterForm',
                'entities/Dealer/Dealer',
                'entities/Customer/Customer'
            ],
            "FirmwareForm-Form": [
                'entities/Firmware/Firmware',
                'entities/Firmware/forms/FirmwareForm'
            ],
            "FirmwareSettings-Form": [
                'entities/Site/Site',
                'entities/Site/forms/FirmwareSettings',
                'entities/Vehicle/Vehicle',
                'entities/Department/Department',
                'entities/Model/Model',
                'entities/Firmware/Firmware',
                'entities/Module/Module',
                'entities/UpdateFirmwareRequest/UpdateFirmwareRequest',
                'entities/Vehicle/grids/VehicleFirmwares',
                'entities/Vehicle/filters/VehicleFirmwareSettingsFilter'
            ],
            "FloorPlanForm-Form": [
                'entities/FloorPlan/FloorPlan',
                'entities/FloorPlan/forms/FloorPlanForm'
            ],
            "GeneralProductivityPerDriverViewLatestForm-Form": [
                'entities/GeneralProductivityPerDriverViewLatest/GeneralProductivityPerDriverViewLatest',
                'entities/GeneralProductivityPerDriverViewLatest/forms/GeneralProductivityPerDriverViewLatestForm',
                'entities/GeneralProductivityView/GeneralProductivityView',
                'entities/GeneralProductivityPerVehicleView/GeneralProductivityPerVehicleView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Vehicle/Vehicle',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/GeneralProductivityPerVehicleView/grids/GeneralProductivityPerVehicleViewGrid',
                'entities/GeneralProductivityPerDriverViewLatest/grids/GeneralProductivityPerDriverViewGrid'
            ],
            "GeneralProductivityReportFilterForm-Form": [
                'entities/GeneralProductivityReportFilter/GeneralProductivityReportFilter',
                'entities/GeneralProductivityReportFilter/forms/GeneralProductivityReportFilterForm',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Department/Department'
            ],
            "GeneralProductivityViewForm-Form": [
                'entities/GeneralProductivityView/GeneralProductivityView',
                'entities/GeneralProductivityView/forms/GeneralProductivityViewForm',
                'entities/UnitUnutilisationStoreProcedure/UnitUnutilisationStoreProcedure',
                'entities/Vehicle/Vehicle',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Model/Model',
                'entities/GeneralProductivityPerDriverViewLatest/GeneralProductivityPerDriverViewLatest',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/Email/Email',
                'entities/GeneralProductivityPerVehicleView/GeneralProductivityPerVehicleView',
                'entities/UnitUtilisationStoreProcedure/UnitUtilisationStoreProcedure',
                'entities/UnitUnutilisationStoreProcedure/grids/UnitUnutilisationStoreProcedureGrid',
                'entities/GeneralProductivityPerDriverViewLatest/grids/GeneralProductivityPerDriverViewGrid',
                'entities/GeneralProductivityPerVehicleView/grids/GeneralProductivityPerVehicleViewGrid',
                'entities/UnitUtilisationStoreProcedure/grids/UnitUtilisationStoreProcedureGrid'
            ],
            "GForceRequiredToCauseImpacts-Form": [
                'entities/Module/Module',
                'entities/Module/forms/GForceRequiredToCauseImpacts'
            ],
            "GO2FAConfigurationForm-Form": [
                'entities/GO2FAConfiguration/GO2FAConfiguration',
                'entities/GO2FAConfiguration/forms/GO2FAConfigurationForm'
            ],
            "GOUserDepartmentForm-Form": [
                'entities/GOUserDepartment/GOUserDepartment',
                'entities/GOUserDepartment/forms/GOUserDepartmentForm',
                'entities/Department/Department'
            ],
            "GOUserForm-Form": [
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/GOUserForm'
            ],
            "GOUserForm1-Form": [
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/GOUserForm1'
            ],
            "GOUserForm2-Form": [
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/GOUserForm2',
                'entities/Dealer/Dealer',
                'entities/GOUserRole/GOUserRole',
                'entities/GORole/GORole',
                'entities/GOUserRole/grids/GOUserRoleGrid'
            ],
            "GOUserForm3-Form": [
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/GOUserForm3',
                'entities/Dealer/Dealer',
                'entities/GOUserRole/GOUserRole',
                'entities/GORole/GORole',
                'entities/GOUserRole/grids/GOUserRoleGrid'
            ],
            "GOUserForm4-Form": [
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/GOUserForm4',
                'entities/DealerDriver/DealerDriver',
                'entities/Card/Card',
                'entities/GoUserToCustomer/GoUserToCustomer',
                'entities/Customer/Customer',
                'entities/Country/Country',
                'entities/GOUser/forms/DealerUserDetailForm',
                'entities/Card/forms/CardDetailsForm',
                'entities/DealerDriver/forms/DealerDriverForm',
                'entities/GoUserToCustomer/grids/GoUserToCustomerGrid'
            ],
            "GOUserRoleForm-Form": [
                'entities/GOUserRole/GOUserRole',
                'entities/GOUserRole/forms/GOUserRoleForm',
                'entities/GORole/GORole'
            ],
            "GoUserToCustomerForm-Form": [
                'entities/GoUserToCustomer/GoUserToCustomer',
                'entities/GoUserToCustomer/forms/GoUserToCustomerForm',
                'entities/Customer/Customer',
                'entities/Country/Country',
                'entities/Customer/grids/SelectCustomersAccessForDealerUserGrid'
            ],
            "HelpForm-Form": [
                'entities/Help/Help',
                'entities/Help/forms/HelpForm'
            ],
            "HireDeHireReportFilterForm-Form": [
                'entities/HireDeHireReportFilter/HireDeHireReportFilter',
                'entities/HireDeHireReportFilter/forms/HireDeHireReportFilterForm',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site'
            ],
            "ImpactForm-Form": [
                'entities/Impact/Impact',
                'entities/Impact/forms/ImpactForm',
                'entities/Session/Session'
            ],
            "ImpactLocationForm-Form": [
                'entities/AllImpactsView/AllImpactsView',
                'entities/AllImpactsView/forms/ImpactLocationForm',
                'entities/Impact/Impact',
                'entities/Session/Session',
                'entities/Vehicle/Vehicle'
            ],
            "ImpactReportFilterForm-Form": [
                'entities/ImpactReportFilter/ImpactReportFilter',
                'entities/ImpactReportFilter/forms/ImpactReportFilterForm',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Department/Department'
            ],
            "ImpactSettings-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/ImpactSettings',
                'entities/Module/Module',
                'entities/Module/forms/AmberImpactForm',
                'entities/Module/forms/ModuleForm',
                'entities/Module/forms/RedImpactForm',
                'entities/Module/forms/GForceRequiredToCauseImpacts',
                'entities/Module/forms/SensorCalibrationForm'
            ],
            "ImportJobLogForm-Form": [
                'entities/ImportJobLog/ImportJobLog',
                'entities/ImportJobLog/forms/ImportJobLogForm'
            ],
            "ImportJobStatusForm-Form": [
                'entities/ImportJobStatus/ImportJobStatus',
                'entities/ImportJobStatus/forms/ImportJobStatusForm',
                'entities/ImportJobBatch/ImportJobBatch',
                'entities/ImportJobLog/ImportJobLog',
                'entities/ImportJobLog/grids/ImportJobLogGrid'
            ],
            "InspectionForm-Form": [
                'entities/Inspection/Inspection',
                'entities/Inspection/forms/InspectionForm'
            ],
            "LicenceDetailCardForm-Form": [
                'entities/LicenceDetail/LicenceDetail',
                'entities/LicenceDetail/forms/LicenceDetailCardForm'
            ],
            "LicenceDetailForm-Form": [
                'entities/LicenceDetail/LicenceDetail',
                'entities/LicenceDetail/forms/LicenceDetailForm'
            ],
            "LicenceDetailForm1-Form": [
                'entities/LicenceDetail/LicenceDetail',
                'entities/LicenceDetail/forms/LicenceDetailForm1'
            ],
            "LicenseActiveForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/LicenseActiveForm'
            ],
            "LicenseByModelForm-Form": [
                'entities/LicenseByModel/LicenseByModel',
                'entities/LicenseByModel/forms/LicenseByModelForm',
                'entities/Model/Model'
            ],
            "LicenseExpiryReportFilterForm-Form": [
                'entities/LicenseExpiryReportFilter/LicenseExpiryReportFilter',
                'entities/LicenseExpiryReportFilter/forms/LicenseExpiryReportFilterForm',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer'
            ],
            "MachineUnlockReportFilterForm-Form": [
                'entities/MachineUnlockReportFilter/MachineUnlockReportFilter',
                'entities/MachineUnlockReportFilter/forms/MachineUnlockReportFilterForm',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Department/Department'
            ],
            "MainDashboardFilterForm-Form": [
                'entities/MainDashboardFilter/MainDashboardFilter',
                'entities/MainDashboardFilter/forms/MainDashboardFilterForm',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer'
            ],
            "ModelCreateNewForm-Form": [
                'entities/CategoryTemplate/CategoryTemplate',
                'entities/CategoryTemplate/forms/ModelCreateNewForm'
            ],
            "ModelForm-Form": [
                'entities/Model/Model',
                'entities/Model/forms/ModelForm',
                'entities/Vehicle/Vehicle',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Module/Module',
                'entities/BroadcastMessage/BroadcastMessage',
                'entities/UploadLogoRequest/UploadLogoRequest',
                'entities/ImportJobStatus/ImportJobStatus',
                'entities/Vehicle/grids/VehilceGrid',
                'entities/Vehicle/filters/VehicleFilter',
                'entities/VehiclesPerModelReport/VehiclesPerModelReport'
            ],
            "ModelForm1-Form": [
                'entities/Model/Model',
                'entities/Model/forms/ModelForm1'
            ],
            "ModelForm2-Form": [
                'entities/Model/Model',
                'entities/Model/forms/ModelForm2',
                'entities/Customer/Customer',
                'entities/Customer/grids/SelectCustomersGrid'
            ],
            "ModuleDetailForm-Form": [
                'entities/Module/Module',
                'entities/Module/forms/ModuleDetailForm',
                'entities/ModuleHistory/ModuleHistory',
                'entities/Vehicle/Vehicle',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/ModuleHistory/grids/ModuleHistoryGrid'
            ],
            "ModuleForm-Form": [
                'entities/Module/Module',
                'entities/Module/forms/ModuleForm'
            ],
            "ModuleForm1-Form": [
                'entities/Module/Module',
                'entities/Module/forms/ModuleForm1'
            ],
            "ModuleForm2-Form": [
                'entities/Module/Module',
                'entities/Module/forms/ModuleForm2'
            ],
            "ModuleForm3-Form": [
                'entities/Module/Module',
                'entities/Module/forms/ModuleForm3'
            ],
            "NetworkSettingsForm-Form": [
                'entities/NetworkSettings/NetworkSettings',
                'entities/NetworkSettings/forms/NetworkSettingsForm'
            ],
            "NewSlamcoreDeviceForm-Form": [
                'entities/SlamcoreDevice/SlamcoreDevice',
                'entities/SlamcoreDevice/forms/NewSlamcoreDeviceForm',
                'entities/Customer/Customer',
                'entities/Vehicle/Vehicle'
            ],
            "NextServiceSettingsForm-Form": [
                'entities/ServiceSettings/ServiceSettings',
                'entities/ServiceSettings/forms/NextServiceSettingsForm'
            ],
            "OnDemandAuthorisationFilterForm-Form": [
                'entities/OnDemandAuthorisationFilter/OnDemandAuthorisationFilter',
                'entities/OnDemandAuthorisationFilter/forms/OnDemandAuthorisationFilterForm',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/Site/Site'
            ],
            "OnDemandSettingsForm-Form": [
                'entities/OnDemandSettings/OnDemandSettings',
                'entities/OnDemandSettings/forms/OnDemandSettingsForm'
            ],
            "OnDemandSettingsForm1-Form": [
                'entities/OnDemandSettings/OnDemandSettings',
                'entities/OnDemandSettings/forms/OnDemandSettingsForm1'
            ],
            "PedestrianDetectionHistoryFilterForm-Form": [
                'entities/PedestrianDetectionHistoryFilter/PedestrianDetectionHistoryFilter',
                'entities/PedestrianDetectionHistoryFilter/forms/PedestrianDetectionHistoryFilterForm',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site'
            ],
            "PermissionForm-Form": [
                'entities/Permission/Permission',
                'entities/Permission/forms/PermissionForm'
            ],
            "PermissionForm1-Form": [
                'entities/Permission/Permission',
                'entities/Permission/forms/PermissionForm1'
            ],
            "PersonAlertSubscriptionsForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/PersonAlertSubscriptionsForm',
                'entities/ReportSubscription/ReportSubscription',
                'entities/ReportType/ReportType',
                'entities/AlertSubscription/AlertSubscription',
                'entities/Alert/Alert',
                'entities/ReportSubscription/grids/ReportSubscriptionGrid',
                'entities/AlertSubscription/grids/AlertSubscriptionItemsGrid'
            ],
            "PersonAllocationForm-Form": [
                'entities/PersonAllocation/PersonAllocation',
                'entities/PersonAllocation/forms/PersonAllocationForm',
                'entities/Department/Department',
                'entities/Site/Site'
            ],
            "PersonChecklistLanguageSettingsForm-Form": [
                'entities/PersonChecklistLanguageSettings/PersonChecklistLanguageSettings',
                'entities/PersonChecklistLanguageSettings/forms/PersonChecklistLanguageSettingsForm'
            ],
            "PersonCreateNewForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/PersonCreateNewForm',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Department/Department'
            ],
            "PersonDetailsHeaderForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/PersonDetailsHeaderForm'
            ],
            "PersonForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/PersonForm',
                'entities/Customer/Customer',
                'entities/EmailGroups/EmailGroups',
                'entities/GOUser/GOUser',
                'entities/GOUserDepartment/GOUserDepartment',
                'entities/Driver/Driver',
                'entities/Card/Card',
                'entities/LicenseByModel/LicenseByModel',
                'entities/Model/Model',
                'entities/LicenceDetail/LicenceDetail',
                'entities/PersonChecklistLanguageSettings/PersonChecklistLanguageSettings',
                'entities/AccessGroup/AccessGroup',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/PersonToSiteVehicleNormalAccessView/PersonToSiteVehicleNormalAccessView',
                'entities/PersonToModelVehicleNormalAccessView/PersonToModelVehicleNormalAccessView',
                'entities/PersonToDepartmentVehicleNormalAccessView/PersonToDepartmentVehicleNormalAccessView',
                'entities/PersonToPerVehicleNormalAccessView/PersonToPerVehicleNormalAccessView',
                'entities/ReportSubscription/ReportSubscription',
                'entities/ReportType/ReportType',
                'entities/AlertSubscription/AlertSubscription',
                'entities/Alert/Alert',
                'entities/GOUser2FA/GOUser2FA',
                'entities/PersonChecklistLanguageSettings/forms/PersonChecklistLanguageSettingsForm',
                'entities/Person/forms/PersonWebsiteAccessForm',
                'entities/GOUser/forms/GOUserForm',
                'entities/Card/forms/CardDetailsForm',
                'entities/Driver/forms/DriverLicensesForm',
                'entities/Person/forms/LicenseActiveForm',
                'entities/LicenceDetail/forms/LicenceDetailCardForm',
                'entities/LicenseByModel/lists/LicenseByModelList',
                'entities/LicenseByModel/forms/LicenseByModelForm',
                'entities/Person/forms/PersonDetailsHeaderForm',
                'entities/Person/forms/PersonVehicleAccessForm',
                'entities/PersonToModelVehicleNormalAccessView/lists/PersonToModelVehicleNormalAccessViewList',
                'entities/PersonToModelVehicleNormalAccessView/forms/PersonToModelVehicleNormalAccessViewForm',
                'entities/PersonToSiteVehicleNormalAccessView/lists/PersonToSiteVehicleNormalAccessViewList',
                'entities/PersonToSiteVehicleNormalAccessView/forms/PersonToSiteVehicleNormalAccessViewForm',
                'entities/PersonToDepartmentVehicleNormalAccessView/lists/PersonToDepartmentVehicleNormalAccessViewList',
                'entities/PersonToDepartmentVehicleNormalAccessView/forms/PersonToDepartmentVehicleNormalAccessViewForm',
                'entities/PersonToPerVehicleNormalAccessView/lists/PersonToPerVehicleNormalAccessViewList',
                'entities/PersonToPerVehicleNormalAccessView/forms/PersonToPerVehicleNormalAccessViewForm',
                'entities/Person/forms/PersonAlertSubscriptionsForm',
                'entities/ReportSubscription/grids/ReportSubscriptionGrid',
                'entities/AlertSubscription/grids/AlertSubscriptionItemsGrid',
                'entities/Person/forms/SupervisorVehicleAccessForm',
                'entities/PersonToModelVehicleNormalAccessView/lists/PersonToModelVehicleMasterAccessViewList',
                'entities/PersonToModelVehicleNormalAccessView/forms/PersonToModelVehicleMasterAccessViewForm',
                'entities/PersonToPerVehicleNormalAccessView/lists/PersonToPerVehicleMasterAccessViewList',
                'entities/PersonToPerVehicleNormalAccessView/forms/PersonToPerVehicleMasterAccessViewForm',
                'entities/PersonToSiteVehicleNormalAccessView/lists/PersonToSiteVehicleMasterAccessViewList',
                'entities/PersonToSiteVehicleNormalAccessView/forms/PersonToSiteVehicleMasterAccessViewForm',
                'entities/PersonToDepartmentVehicleNormalAccessView/lists/PersonToDepartmentVehicleMasterAccessViewList',
                'entities/PersonToDepartmentVehicleNormalAccessView/forms/PersonToDepartmentVehicleMasterAccessViewForm',
                'entities/Person/forms/PersonInformationForm',
                'entities/Person/forms/SupervisorAuthorization',
                'entities/EmailGroups/grids/EmailGroupsGrid',
                'entities/GOUserDepartment/grids/GOUserDepartmentGrid'
            ],
            "PersonInformationForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/PersonInformationForm',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer'
            ],
            "PersonToDepartmentVehicleMasterAccessViewForm-Form": [
                'entities/PersonToDepartmentVehicleNormalAccessView/PersonToDepartmentVehicleNormalAccessView',
                'entities/PersonToDepartmentVehicleNormalAccessView/forms/PersonToDepartmentVehicleMasterAccessViewForm'
            ],
            "PersonToDepartmentVehicleNormalAccessViewForm-Form": [
                'entities/PersonToDepartmentVehicleNormalAccessView/PersonToDepartmentVehicleNormalAccessView',
                'entities/PersonToDepartmentVehicleNormalAccessView/forms/PersonToDepartmentVehicleNormalAccessViewForm'
            ],
            "PersonToModelVehicleMasterAccessViewForm-Form": [
                'entities/PersonToModelVehicleNormalAccessView/PersonToModelVehicleNormalAccessView',
                'entities/PersonToModelVehicleNormalAccessView/forms/PersonToModelVehicleMasterAccessViewForm'
            ],
            "PersonToModelVehicleNormalAccessViewForm-Form": [
                'entities/PersonToModelVehicleNormalAccessView/PersonToModelVehicleNormalAccessView',
                'entities/PersonToModelVehicleNormalAccessView/forms/PersonToModelVehicleNormalAccessViewForm'
            ],
            "PersonToPerVehicleMasterAccessViewForm-Form": [
                'entities/PersonToPerVehicleNormalAccessView/PersonToPerVehicleNormalAccessView',
                'entities/PersonToPerVehicleNormalAccessView/forms/PersonToPerVehicleMasterAccessViewForm'
            ],
            "PersonToPerVehicleNormalAccessViewForm-Form": [
                'entities/PersonToPerVehicleNormalAccessView/PersonToPerVehicleNormalAccessView',
                'entities/PersonToPerVehicleNormalAccessView/forms/PersonToPerVehicleNormalAccessViewForm'
            ],
            "PersonToSiteVehicleMasterAccessViewForm-Form": [
                'entities/PersonToSiteVehicleNormalAccessView/PersonToSiteVehicleNormalAccessView',
                'entities/PersonToSiteVehicleNormalAccessView/forms/PersonToSiteVehicleMasterAccessViewForm'
            ],
            "PersonToSiteVehicleNormalAccessViewForm-Form": [
                'entities/PersonToSiteVehicleNormalAccessView/PersonToSiteVehicleNormalAccessView',
                'entities/PersonToSiteVehicleNormalAccessView/forms/PersonToSiteVehicleNormalAccessViewForm'
            ],
            "PersonVehicleAccessForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/PersonVehicleAccessForm',
                'entities/PersonToSiteVehicleNormalAccessView/PersonToSiteVehicleNormalAccessView',
                'entities/PersonToModelVehicleNormalAccessView/PersonToModelVehicleNormalAccessView',
                'entities/PersonToDepartmentVehicleNormalAccessView/PersonToDepartmentVehicleNormalAccessView',
                'entities/PersonToPerVehicleNormalAccessView/PersonToPerVehicleNormalAccessView',
                'entities/PersonToModelVehicleNormalAccessView/lists/PersonToModelVehicleNormalAccessViewList',
                'entities/PersonToModelVehicleNormalAccessView/forms/PersonToModelVehicleNormalAccessViewForm',
                'entities/PersonToSiteVehicleNormalAccessView/lists/PersonToSiteVehicleNormalAccessViewList',
                'entities/PersonToSiteVehicleNormalAccessView/forms/PersonToSiteVehicleNormalAccessViewForm',
                'entities/PersonToDepartmentVehicleNormalAccessView/lists/PersonToDepartmentVehicleNormalAccessViewList',
                'entities/PersonToDepartmentVehicleNormalAccessView/forms/PersonToDepartmentVehicleNormalAccessViewForm',
                'entities/PersonToPerVehicleNormalAccessView/lists/PersonToPerVehicleNormalAccessViewList',
                'entities/PersonToPerVehicleNormalAccessView/forms/PersonToPerVehicleNormalAccessViewForm'
            ],
            "PersonWebsiteAccessForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/PersonWebsiteAccessForm',
                'entities/AccessGroup/AccessGroup',
                'entities/GOUser/GOUser',
                'entities/GOUser/forms/GOUserForm'
            ],
            "PerVehicleNormalCardAccessForm-Form": [
                'entities/PerVehicleNormalCardAccess/PerVehicleNormalCardAccess',
                'entities/PerVehicleNormalCardAccess/forms/PerVehicleNormalCardAccessForm',
                'entities/Card/Card',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Card/grids/SelectOnDemandUsersGrid'
            ],
            "PreOperationalChecklistForm-Form": [
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/PreOperationalChecklist/forms/PreOperationalChecklistForm'
            ],
            "PreOperationalChecklistForm1-Form": [
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/PreOperationalChecklist/forms/PreOperationalChecklistForm1'
            ],
            "PreOpReportFilterForm-Form": [
                'entities/PreOpReportFilter/PreOpReportFilter',
                'entities/PreOpReportFilter/forms/PreOpReportFilterForm',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/Site/Site'
            ],
            "ProficiencyCombinedViewForm-Form": [
                'entities/ProficiencyCombinedView/ProficiencyCombinedView',
                'entities/ProficiencyCombinedView/forms/ProficiencyCombinedViewForm',
                'entities/DriverProficiencyView/DriverProficiencyView',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/VehicleProficiencyView/VehicleProficiencyView',
                'entities/Vehicle/Vehicle',
                'entities/DriverProficiencyView/grids/DriverProficiencyViewGrid',
                'entities/VehicleProficiencyView/grids/VehicleProficiencyViewGrid'
            ],
            "ProficiencyReportFilterForm-Form": [
                'entities/ProficiencyReportFilter/ProficiencyReportFilter',
                'entities/ProficiencyReportFilter/forms/ProficiencyReportFilterForm',
                'entities/Site/Site',
                'entities/Customer/Customer',
                'entities/Department/Department'
            ],
            "RebootVehicle-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/RebootVehicle'
            ],
            "RedImpactForm-Form": [
                'entities/Module/Module',
                'entities/Module/forms/RedImpactForm'
            ],
            "RegionCreateNewForm-Form": [
                'entities/Region/Region',
                'entities/Region/forms/RegionCreateNewForm'
            ],
            "RegionForm-Form": [
                'entities/Region/Region',
                'entities/Region/forms/RegionForm'
            ],
            "ReportsAccessGroupTemplateForm-Form": [
                'entities/AccessGroupTemplate/AccessGroupTemplate',
                'entities/AccessGroupTemplate/forms/ReportsAccessGroupTemplateForm'
            ],
            "ReportSubscriptionForm-Form": [
                'entities/ReportSubscription/ReportSubscription',
                'entities/ReportSubscription/forms/ReportSubscriptionForm',
                'entities/Customer/Customer',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/ReportType/ReportType'
            ],
            "SendReportForm-Form": [
                'entities/Email/Email',
                'entities/Email/forms/SendReportForm'
            ],
            "SensorCalibrationForm-Form": [
                'entities/Module/Module',
                'entities/Module/forms/SensorCalibrationForm'
            ],
            "ServiceSettingsForm-Form": [
                'entities/ServiceSettings/ServiceSettings',
                'entities/ServiceSettings/forms/ServiceSettingsForm'
            ],
            "ServiceSettingsForm1-Form": [
                'entities/ServiceSettings/ServiceSettings',
                'entities/ServiceSettings/forms/ServiceSettingsForm1',
                'entities/Vehicle/Vehicle',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/Vehicle/grids/VehicleGrid'
            ],
            "SessionForm-Form": [
                'entities/Session/Session',
                'entities/Session/forms/SessionForm'
            ],
            "SiteChecklistForm-Form": [
                'entities/DepartmentChecklist/DepartmentChecklist',
                'entities/DepartmentChecklist/forms/SiteChecklistForm',
                'entities/Model/Model'
            ],
            "SiteChecklistForm1-Form": [
                'entities/DepartmentChecklist/DepartmentChecklist',
                'entities/DepartmentChecklist/forms/SiteChecklistForm1',
                'entities/Model/Model'
            ],
            "SiteCreateNewForm-Form": [
                'entities/Site/Site',
                'entities/Site/forms/SiteCreateNewForm',
                'entities/Timezone/Timezone'
            ],
            "SiteFloorPlanForm1-Form": [
                'entities/SiteFloorPlan/SiteFloorPlan',
                'entities/SiteFloorPlan/forms/SiteFloorPlanForm1',
                'entities/FloorPlan/FloorPlan',
                'entities/Site/Site'
            ],
            "SiteForm-Form": [
                'entities/Site/Site',
                'entities/Site/forms/SiteForm',
                'entities/Vehicle/Vehicle',
                'entities/Department/Department',
                'entities/Model/Model',
                'entities/Firmware/Firmware',
                'entities/Module/Module',
                'entities/UpdateFirmwareRequest/UpdateFirmwareRequest',
                'entities/Site/forms/FirmwareSettings',
                'entities/Vehicle/grids/VehicleFirmwares',
                'entities/Vehicle/filters/VehicleFirmwareSettingsFilter',
                'entities/Site/forms/VehicleForm3',
                'entities/Department/grids/DepartmentGrid'
            ],
            "SiteForm1-Form": [
                'entities/Site/Site',
                'entities/Site/forms/SiteForm1',
                'entities/Timezone/Timezone',
                'entities/Vehicle/Vehicle',
                'entities/Department/Department',
                'entities/Model/Model',
                'entities/Firmware/Firmware',
                'entities/Module/Module',
                'entities/UpdateFirmwareRequest/UpdateFirmwareRequest',
                'entities/Site/forms/SiteForm',
                'entities/Site/forms/FirmwareSettings',
                'entities/Vehicle/grids/VehicleFirmwares',
                'entities/Vehicle/filters/VehicleFirmwareSettingsFilter',
                'entities/Site/forms/VehicleForm3',
                'entities/Department/grids/DepartmentGrid'
            ],
            "SiteForm2-Form": [
                'entities/Site/Site',
                'entities/Site/forms/SiteForm2',
                'entities/FloorPlan/FloorPlan',
                'entities/FloorPlan/lists/FloorPlanList',
                'entities/FloorPlan/forms/FloorPlanForm'
            ],
            "SiteForm3-Form": [
                'entities/Site/Site',
                'entities/Site/forms/SiteForm3',
                'entities/FloorPlan/FloorPlan',
                'entities/FloorPlan/lists/FloorPlanList',
                'entities/FloorPlan/forms/FloorPlanForm'
            ],
            "SlamcoreAccountAuthenticationDetailsForm-Form": [
                'entities/SlamcoreAwareAuthenticationDetails/SlamcoreAwareAuthenticationDetails',
                'entities/SlamcoreAwareAuthenticationDetails/forms/SlamcoreAccountAuthenticationDetailsForm'
            ],
            "SlamcoreAPIKeyForm-Form": [
                'entities/SlamcoreAPIKey/SlamcoreAPIKey',
                'entities/SlamcoreAPIKey/forms/SlamcoreAPIKeyForm'
            ],
            "SlamcoreAwareAuthenticationDetailsForm-Form": [
                'entities/SlamcoreAwareAuthenticationDetails/SlamcoreAwareAuthenticationDetails',
                'entities/SlamcoreAwareAuthenticationDetails/forms/SlamcoreAwareAuthenticationDetailsForm'
            ],
            "SlamcoreDeviceAuthSettingsForm-Form": [
                'entities/SlamcoreDevice/SlamcoreDevice',
                'entities/SlamcoreDevice/forms/SlamcoreDeviceAuthSettingsForm',
                'entities/SlamcoreAPIKey/SlamcoreAPIKey',
                'entities/SlamcoreAwareAuthenticationDetails/SlamcoreAwareAuthenticationDetails',
                'entities/SlamcoreAPIKey/forms/SlamcoreAPIKeyForm',
                'entities/SlamcoreAwareAuthenticationDetails/forms/SlamcoreAwareAuthenticationDetailsForm'
            ],
            "SlamcoreDeviceConnectionViewForm-Form": [
                'entities/SlamcoreDeviceConnectionView/SlamcoreDeviceConnectionView',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm1',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm3',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm2'
            ],
            "SlamcoreDeviceConnectionViewForm1-Form": [
                'entities/SlamcoreDeviceConnectionView/SlamcoreDeviceConnectionView',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm1'
            ],
            "SlamcoreDeviceConnectionViewForm2-Form": [
                'entities/SlamcoreDeviceConnectionView/SlamcoreDeviceConnectionView',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm2'
            ],
            "SlamcoreDeviceConnectionViewForm3-Form": [
                'entities/SlamcoreDeviceConnectionView/SlamcoreDeviceConnectionView',
                'entities/SlamcoreDeviceConnectionView/forms/SlamcoreDeviceConnectionViewForm3'
            ],
            "SlamcoreDeviceFilterForm-Form": [
                'entities/SlamcoreDeviceFilter/SlamcoreDeviceFilter',
                'entities/SlamcoreDeviceFilter/forms/SlamcoreDeviceFilterForm',
                'entities/Customer/Customer',
                'entities/Site/Site'
            ],
            "SubscribeForm-Form": [
                'entities/ReportSubscription/ReportSubscription',
                'entities/ReportSubscription/forms/SubscribeForm',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site'
            ],
            "SupervisorAuthorization-Form": [
                'entities/Person/Person',
                'entities/Person/forms/SupervisorAuthorization'
            ],
            "SupervisorVehicleAccessForm-Form": [
                'entities/Person/Person',
                'entities/Person/forms/SupervisorVehicleAccessForm',
                'entities/PersonToSiteVehicleNormalAccessView/PersonToSiteVehicleNormalAccessView',
                'entities/PersonToModelVehicleNormalAccessView/PersonToModelVehicleNormalAccessView',
                'entities/PersonToDepartmentVehicleNormalAccessView/PersonToDepartmentVehicleNormalAccessView',
                'entities/PersonToPerVehicleNormalAccessView/PersonToPerVehicleNormalAccessView',
                'entities/PersonToModelVehicleNormalAccessView/lists/PersonToModelVehicleMasterAccessViewList',
                'entities/PersonToModelVehicleNormalAccessView/forms/PersonToModelVehicleMasterAccessViewForm',
                'entities/PersonToPerVehicleNormalAccessView/lists/PersonToPerVehicleMasterAccessViewList',
                'entities/PersonToPerVehicleNormalAccessView/forms/PersonToPerVehicleMasterAccessViewForm',
                'entities/PersonToSiteVehicleNormalAccessView/lists/PersonToSiteVehicleMasterAccessViewList',
                'entities/PersonToSiteVehicleNormalAccessView/forms/PersonToSiteVehicleMasterAccessViewForm',
                'entities/PersonToDepartmentVehicleNormalAccessView/lists/PersonToDepartmentVehicleMasterAccessViewList',
                'entities/PersonToDepartmentVehicleNormalAccessView/forms/PersonToDepartmentVehicleMasterAccessViewForm'
            ],
            "SynchronizationStatusReportFilterForm-Form": [
                'entities/SynchronizationStatusReportFilter/SynchronizationStatusReportFilter',
                'entities/SynchronizationStatusReportFilter/forms/SynchronizationStatusReportFilterForm',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Customer/Customer'
            ],
            "TimezoneCreateNewForm-Form": [
                'entities/Timezone/Timezone',
                'entities/Timezone/forms/TimezoneCreateNewForm'
            ],
            "TimezoneForm-Form": [
                'entities/Timezone/Timezone',
                'entities/Timezone/forms/TimezoneForm'
            ],
            "UnlockVehicle-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/UnlockVehicle'
            ],
            "UpdateLastServiceDateForm-Form": [
                'entities/ImportJobStatus/ImportJobStatus',
                'entities/ImportJobStatus/forms/UpdateLastServiceDateForm'
            ],
            "UpdateQuestionOrderForm-Form": [
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/PreOperationalChecklist/forms/UpdateQuestionOrderForm'
            ],
            "UpdateQuestionsOrderForm-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/UpdateQuestionsOrderForm',
                'entities/DepartmentChecklist/DepartmentChecklist',
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/PreOperationalChecklist/lists/QuestionList',
                'entities/PreOperationalChecklist/forms/UpdateQuestionOrderForm'
            ],
            "UpdateVehicleFirmware-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/UpdateVehicleFirmware',
                'entities/Firmware/Firmware'
            ],
            "UploadFileForm-Form": [
                'entities/ImportJobStatus/ImportJobStatus',
                'entities/ImportJobStatus/forms/UploadFileForm'
            ],
            "UploadLogoRequestForm-Form": [
                'entities/UploadLogoRequest/UploadLogoRequest',
                'entities/UploadLogoRequest/forms/UploadLogoRequestForm',
                'entities/BroadcastMessage/BroadcastMessage',
                'entities/Vehicle/Vehicle',
                'entities/Module/Module',
                'entities/Vehicle/grids/SelectVehiclesForLogoUploadGrid'
            ],
            "UsersAccessGroupForm-Form": [
                'entities/AccessGroup/AccessGroup',
                'entities/AccessGroup/forms/UsersAccessGroupForm'
            ],
            "UsersAccessGroupTemplateForm-Form": [
                'entities/AccessGroupTemplate/AccessGroupTemplate',
                'entities/AccessGroupTemplate/forms/UsersAccessGroupTemplateForm'
            ],
            "VehicleBroadcastMessageForm-Form": [
                'entities/VehicleBroadcastMessage/VehicleBroadcastMessage',
                'entities/VehicleBroadcastMessage/forms/VehicleBroadcastMessageForm',
                'entities/Vehicle/Vehicle',
                'entities/Module/Module',
                'entities/Vehicle/grids/SelectVehiclesForBroadcastMessageGrid'
            ],
            "VehicleChecklistForm-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleChecklistForm',
                'entities/ChecklistSettings/ChecklistSettings',
                'entities/DepartmentChecklist/DepartmentChecklist',
                'entities/VehicleToPreOpChecklistView/VehicleToPreOpChecklistView',
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Department/Department',
                'entities/Model/Model',
                'entities/ChecklistSettings/forms/ChecklistSettingsForm',
                'entities/DepartmentChecklist/forms/DepartmentChecklistForm',
                'entities/VehicleToPreOpChecklistView/grids/VehicleToPreOpChecklistGrid',
                'entities/VehicleToPreOpChecklistView/filters/VehicleToPreOpChecklistViewFilter'
            ],
            "VehicleDiagnosticForm-Form": [
                'entities/VehicleDiagnostic/VehicleDiagnostic',
                'entities/VehicleDiagnostic/forms/VehicleDiagnosticForm',
                'entities/Vehicle/Vehicle',
                'entities/Module/Module',
                'entities/Module/forms/GForceRequiredToCauseImpacts',
                'entities/Module/forms/AmberImpactForm',
                'entities/Module/forms/RedImpactForm'
            ],
            "VehicleForm-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleForm',
                'entities/ServiceSettings/ServiceSettings',
                'entities/ChecklistSettings/ChecklistSettings',
                'entities/DepartmentChecklist/DepartmentChecklist',
                'entities/VehicleToPreOpChecklistView/VehicleToPreOpChecklistView',
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Department/Department',
                'entities/Model/Model',
                'entities/Inspection/Inspection',
                'entities/Module/Module',
                'entities/Site/Site',
                'entities/Canrule/Canrule',
                'entities/Customer/Customer',
                'entities/VehicleOtherSettings/VehicleOtherSettings',
                'entities/NetworkSettings/NetworkSettings',
                'entities/ServiceSettings/forms/VehicleServiceSettings',
                'entities/Vehicle/forms/VehicleChecklistForm',
                'entities/ChecklistSettings/forms/ChecklistSettingsForm',
                'entities/DepartmentChecklist/forms/DepartmentChecklistForm',
                'entities/VehicleToPreOpChecklistView/grids/VehicleToPreOpChecklistGrid',
                'entities/VehicleToPreOpChecklistView/filters/VehicleToPreOpChecklistViewFilter',
                'entities/Inspection/forms/InspectionForm',
                'entities/Vehicle/forms/ImpactSettings',
                'entities/Module/forms/AmberImpactForm',
                'entities/Module/forms/ModuleForm',
                'entities/Module/forms/RedImpactForm',
                'entities/Module/forms/GForceRequiredToCauseImpacts',
                'entities/Module/forms/SensorCalibrationForm',
                'entities/Vehicle/forms/VehicleInformationForm',
                'entities/Module/forms/ModuleForm2',
                'entities/Vehicle/forms/VehicleForm1',
                'entities/VehicleOtherSettings/forms/VehicleOtherSettingsForm',
                'entities/Vehicle/forms/VehicleNetworkSettingsForm',
                'entities/NetworkSettings/grids/NetworkSettingsGrid'
            ],
            "VehicleForm1-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleForm1',
                'entities/Model/Model'
            ],
            "VehicleForm2-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleForm2',
                'entities/VehicleSupervisorsView/VehicleSupervisorsView',
                'entities/PerVehicleNormalCardAccess/PerVehicleNormalCardAccess',
                'entities/Card/Card',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/VehicleSupervisorsView/grids/VehicleSupervisorsGrid'
            ],
            "VehicleForm3-Form": [
                'entities/Site/Site',
                'entities/Site/forms/VehicleForm3'
            ],
            "VehicleForm4-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleForm4',
                'entities/VehicleDiagnostic/VehicleDiagnostic',
                'entities/Module/Module',
                'entities/VehicleDiagnostic/forms/VehicleDiagnosticForm',
                'entities/Module/forms/GForceRequiredToCauseImpacts',
                'entities/Module/forms/AmberImpactForm',
                'entities/Module/forms/RedImpactForm'
            ],
            "VehicleForm5-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleForm5',
                'entities/Person/Person',
                'entities/OnDemandSettings/OnDemandSettings',
                'entities/OnDemandSettings/forms/OnDemandSettingsForm1'
            ],
            "VehicleHireDehireForm-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleHireDehireForm',
                'entities/Customer/Customer',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/VehicleHireDehireSynchronizationOptions/VehicleHireDehireSynchronizationOptions',
                'entities/VehicleHireDehireSynchronizationOptions/grids/VehicleHireDehireSynchronizationOptionsGrid'
            ],
            "VehicleInformationForm-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleInformationForm',
                'entities/Model/Model',
                'entities/Module/Module',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Canrule/Canrule',
                'entities/Customer/Customer',
                'entities/Module/forms/ModuleForm2'
            ],
            "VehicleLockoutForm-Form": [
                'entities/VehicleLockout/VehicleLockout',
                'entities/VehicleLockout/forms/VehicleLockoutForm'
            ],
            "VehicleNetworkSettingsForm-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleNetworkSettingsForm',
                'entities/NetworkSettings/NetworkSettings',
                'entities/NetworkSettings/grids/NetworkSettingsGrid'
            ],
            "VehicleOnDemandSettingsForm-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleOnDemandSettingsForm',
                'entities/OnDemandSettings/OnDemandSettings',
                'entities/PerVehicleNormalCardAccess/PerVehicleNormalCardAccess',
                'entities/Card/Card',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/OnDemandSettings/forms/OnDemandSettingsForm',
                'entities/PerVehicleNormalCardAccess/grids/OnDemandCodesGrid'
            ],
            "VehicleOtherSettingsForm-Form": [
                'entities/VehicleOtherSettings/VehicleOtherSettings',
                'entities/VehicleOtherSettings/forms/VehicleOtherSettingsForm'
            ],
            "VehicleRAModuleSwapForm-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehicleRAModuleSwapForm',
                'entities/Customer/Customer',
                'entities/Dealer/Dealer',
                'entities/Module/Module',
                'entities/Module/grids/ModuleGrid',
                'entities/Module/filters/ModuleFilter'
            ],
            "VehiclesAccessGroupTemplateForm-Form": [
                'entities/AccessGroupTemplate/AccessGroupTemplate',
                'entities/AccessGroupTemplate/forms/VehiclesAccessGroupTemplateForm'
            ],
            "VehicleServiceSettings-Form": [
                'entities/ServiceSettings/ServiceSettings',
                'entities/ServiceSettings/forms/VehicleServiceSettings'
            ],
            "VehicleServiceSettingsForm-Form": [
                'entities/ServiceSettings/ServiceSettings',
                'entities/ServiceSettings/forms/VehicleServiceSettingsForm'
            ],
            "VehicleServiceStatusForm-Form": [
                'entities/ServiceSettings/ServiceSettings',
                'entities/ServiceSettings/forms/VehicleServiceStatusForm'
            ],
            "VehicleSessionsForm-Form": [
                'entities/GeneralProductivityPerVehicleView/GeneralProductivityPerVehicleView',
                'entities/GeneralProductivityPerVehicleView/forms/VehicleSessionsForm',
                'entities/Vehicle/Vehicle',
                'entities/DetailedSessionView/DetailedSessionView',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/DetailedSessionView/grids/VehicleDetailedSessionViewGrid'
            ],
            "VehicleToPreOpChecklistViewForm-Form": [
                'entities/VehicleToPreOpChecklistView/VehicleToPreOpChecklistView',
                'entities/VehicleToPreOpChecklistView/forms/VehicleToPreOpChecklistViewForm',
                'entities/PreOperationalChecklist/PreOperationalChecklist',
                'entities/Vehicle/Vehicle',
                'entities/Model/Model',
                'entities/Department/Department',
                'entities/PreOperationalChecklist/forms/PreOperationalChecklistForm1'
            ],
            "VehicleVORSessionsForm-Form": [
                'entities/AllVORSessionsPerVehicleStoreProcedure/AllVORSessionsPerVehicleStoreProcedure',
                'entities/AllVORSessionsPerVehicleStoreProcedure/forms/VehicleVORSessionsForm',
                'entities/Vehicle/Vehicle',
                'entities/DetailedVORSessionStoreProcedure/DetailedVORSessionStoreProcedure',
                'entities/Session/Session',
                'entities/Driver/Driver',
                'entities/Person/Person',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/DetailedVORSessionStoreProcedure/grids/DetailedVORSessionStoreProcedureGrid'
            ],
            "VehilceForm1-Form": [
                'entities/Vehicle/Vehicle',
                'entities/Vehicle/forms/VehilceForm1',
                'entities/Module/Module',
                'entities/Site/Site',
                'entities/Model/Model',
                'entities/Department/Department',
                'entities/Customer/Customer',
                'entities/Canrule/Canrule',
                'entities/Module/forms/ModuleForm2'
            ],
            "VORReportCombinedViewForm-Form": [
                'entities/VORReportCombinedView/VORReportCombinedView',
                'entities/VORReportCombinedView/forms/VORReportCombinedViewForm',
                'entities/AllVORSessionsPerVehicleStoreProcedure/AllVORSessionsPerVehicleStoreProcedure',
                'entities/ExportJobStatus/ExportJobStatus',
                'entities/Vehicle/Vehicle',
                'entities/Department/Department',
                'entities/Site/Site',
                'entities/Email/Email',
                'entities/ReportSubscription/ReportSubscription',
                'entities/Customer/Customer',
                'entities/AllVORStatusStoreProcedure/AllVORStatusStoreProcedure',
                'entities/VORSettingHistory/VORSettingHistory',
                'entities/Person/Person',
                'entities/AllVORSessionsPerVehicleStoreProcedure/grids/AllVORSessionsPerVehicleStoreProcedureGrid',
                'entities/AllVORStatusStoreProcedure/grids/AllVORStatusStoreProcedureGrid'
            ],
            "VORReportFilterForm-Form": [
                'entities/VORReportFilter/VORReportFilter',
                'entities/VORReportFilter/forms/VORReportFilterForm',
                'entities/Site/Site',
                'entities/Department/Department',
                'entities/Customer/Customer'
            ]
        };
        
        // Track currently loading namespaces
        this.currentlyLoading = {};
        this.loadedElements = {};
        
        // Get source for a namespace
        this.getNamespaceSource = function (namespace) {
            if (self.loadedNamespaces[namespace]) {
                GO.log("i18n", "Namespace already loaded: " + namespace);
                self.onNamespaceLoaded(namespace);
                return;
            }
            
            GO.log("i18n", "Loading namespace: " + namespace);
            
            try {
                // Load the namespace using i18next
                FleetXQ.Web.Messages.i18n.loadNamespaces(namespace, function(err) {
                    if (err) {
                        console.error("Error loading namespace: " + namespace, err);
                        // Mark as loaded anyway to prevent infinite loops
                        self.loadedNamespaces[namespace] = true;
                    } else {
                        GO.log("i18n", "Successfully loaded namespace: " + namespace);
                        self.loadedNamespaces[namespace] = true;
                    }
                    self.onNamespaceLoaded(namespace);
                });
            } catch (ex) {
                console.error("Exception loading namespace: " + namespace, ex);
                // Mark as loaded anyway to prevent infinite loops
                self.loadedNamespaces[namespace] = true;
                self.onNamespaceLoaded(namespace);
            }
        };
        
        // Load namespaces for a specific element
        this.ensureNamespacesForElement = function (elementName, elementType, callback) {
            GO.log(elementName, "Ensuring translations are loaded for " + elementName + " (" + elementType + ")");
           
            var key = elementName + "-" + elementType;
            
            // If already loaded, just call the callback
            if (self.loadedElements[key]) {
                GO.log(elementName, "Translations already loaded, calling callback directly");
                if (callback) {
                    setTimeout(function() {
                        callback(elementName);
                    }, 0);
                }
                return;
            }
            
            // Initialize loading state
            self.currentlyLoading[key] = {
                callback: callback || function() {},
                count: 0,
                total: 0
            };
            
            if (elementType === "Global") {
                GO.log(elementName, "Loading global namespaces");
                self.loadNamespaces(self.globalRequiredNamespaces, key);
                return;
            }
            
            // Load element-specific namespaces
            var namespacesToLoad = self.requiredNamespacesByElement[key] || [];
            GO.log(elementName, "Loading " + namespacesToLoad.length + " namespaces for " + key);
            
            if (namespacesToLoad.length === 0) {
                GO.log(elementName, "No namespaces defined for " + key + ", marking as loaded");
                self.loadedElements[key] = true;
                if (callback) {
                    setTimeout(function() {
                        callback(elementName);
                    }, 0);
                }
                return;
            }
            
            self.loadNamespaces(namespacesToLoad, key);
        };
        
        // Load a set of namespaces
        this.loadNamespaces = function(namespaces, key) {
            GO.log(key, "Loading namespaces for: " + key);

            if (!namespaces || namespaces.length === 0) {
                GO.log(key, "No namespaces to load for: " + key);
 
                if (self.currentlyLoading[key]) {
                    self.loadedElements[key] = true;
                    self.currentlyLoading[key].callback(key.split("-")[0]);
                    delete self.currentlyLoading[key];
                } else {
                    GO.log(key, "Warning: currentlyLoading[" + key + "] is undefined in loadNamespaces (empty case)");
                }
                return;
            }
            
            // Count already loaded namespaces
            var alreadyLoaded = 0;
            for (var i = 0; i < namespaces.length; i++) {
                if (self.loadedNamespaces[namespaces[i]]) {
                    alreadyLoaded++;
                }
            }
            
            GO.log(key, "Already loaded namespaces: " + alreadyLoaded + "/" + namespaces.length);

            // If all namespaces are already loaded, call callback immediately
            if (alreadyLoaded === namespaces.length) {
                GO.log(key, "All translations already loaded, continuing to next step");
                self.loadedElements[key] = true;
                if (self.currentlyLoading[key]) {
                    self.currentlyLoading[key].callback(key.split("-")[0]);
                    delete self.currentlyLoading[key];
                } else {
                    GO.log(key, "Warning: currentlyLoading[" + key + "] is undefined in loadNamespaces (all loaded case)");
                }
                return;
            }
            
            // Set the total number of namespaces to load
            if (self.currentlyLoading[key]) {
                self.currentlyLoading[key].count = alreadyLoaded;
                self.currentlyLoading[key].total = namespaces.length;
                
                // Load all required namespaces
                for (var i = 0; i < namespaces.length; i++) {
                    if (!self.loadedNamespaces[namespaces[i]]) {
                        self.getNamespaceSource(namespaces[i]);
                    }
                }
            } else {
                GO.log(key, "Warning: currentlyLoading[" + key + "] is undefined in loadNamespaces (loading case)");
                // Try to recover by marking all namespaces as loaded
                for (var i = 0; i < namespaces.length; i++) {
                    self.loadedNamespaces[namespaces[i]] = true;
                }
            }
        };
        
        // Callback when namespace is loaded
        this.onNamespaceLoaded = function (namespace) {
            GO.log("i18n", "Namespace loaded callback for: " + namespace);

            // Add check for empty currentlyLoading
            if (Object.keys(self.currentlyLoading).length === 0) {
                GO.log("i18n", "No elements currently loading, namespace: " + namespace);
                return;
            }

            for (var key in self.currentlyLoading) {
                var namespaces;
                if (key.endsWith("-Global")) {
                    namespaces = self.globalRequiredNamespaces;
                } else {
                    namespaces = self.requiredNamespacesByElement[key] || [];
                }
                
                // Check if this namespace is required for this element
                if (namespaces.indexOf(namespace) > -1) {
                    if (self.currentlyLoading[key]) {
                        self.currentlyLoading[key].count++;
                        GO.log(key, "Namespace " + namespace + " loaded. Progress: " + 
                               self.currentlyLoading[key].count + "/" + self.currentlyLoading[key].total);
                        
                        // If all namespaces are loaded, call the callback
                        if (self.currentlyLoading[key].count >= self.currentlyLoading[key].total) {
                            GO.log(key, "All translations loaded, continuing to next step");
                            self.loadedElements[key] = true;
                            self.currentlyLoading[key].callback(key.split("-")[0]);
                            delete self.currentlyLoading[key];
                        }
                    } else {
                        GO.log(key, "Warning: currentlyLoading[" + key + "] is undefined in onNamespaceLoaded");
                    }
                }
            }
        };
        
        // Initialize with custom namespaces if available
        this.initialize = function () {
            if (FleetXQ.Web.Application.TranslationSourceHandlerCustom) {
                self.TranslationSourceHandlerCustom = new FleetXQ.Web.Application.TranslationSourceHandlerCustom();
                
                if (self.TranslationSourceHandlerCustom.globalRequiredNamespaces) {
                    for (var i = 0; i < self.TranslationSourceHandlerCustom.globalRequiredNamespaces.length; i++) {
                        self.globalRequiredNamespaces.push(self.TranslationSourceHandlerCustom.globalRequiredNamespaces[i]);
                    }
                }
                
                if (self.TranslationSourceHandlerCustom.requiredNamespacesByElement) {
                    for (var prop in self.TranslationSourceHandlerCustom.requiredNamespacesByElement) {
                        if (!self.requiredNamespacesByElement[prop]) {
                            self.requiredNamespacesByElement[prop] = [];
                        }
                        
                        for (var i = 0; i < self.TranslationSourceHandlerCustom.requiredNamespacesByElement[prop].length; i++) {
                            self.requiredNamespacesByElement[prop].push(
                                self.TranslationSourceHandlerCustom.requiredNamespacesByElement[prop][i]
                            );
                        }
                    }
                }
            }
        };
                
        this.initialize();
    };
}(window));