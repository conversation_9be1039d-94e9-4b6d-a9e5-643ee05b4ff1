﻿{
  "entityName": "Customer",
  "entityNamePlural": "Customers",   "entityDescription": "This entity represents a Customer",
  "fields": {
    "AccessGroupItems": {
        "displayName": "Access group Items", 
        "description": "Access group Items"
    },
    "Active": {
        "displayName": "Active", 
        "description": "Active"
    },
    "Addess": {
        "displayName": "Address", 
        "description": "Address"
    },
    "ChecklistStatusViewItems": {
        "displayName": "Checklist Status View Items", 
        "description": "Checklist Status View Items"
    },
    "CompanyName": {
        "displayName": "Customer Name", 
        "description": "Customer Name"
    },
    "ConnectionString": {
        "displayName": "Connection String", 
        "description": "Connection String"
    },
    "ContactNumber": {
        "displayName": "Mobile Number", 
        "description": "Mobile Number"
    },
    "ContactPersonInformation": {
        "displayName": "Contact Person Information", 
        "description": "Contact Person Information"
    },
    "ContactPersonInformationId": {
        "displayName": "ContactPersonInformationId", 
        "description": "Foreign Key"
    },
    "ContractDate": {
        "displayName": "Contract Date", 
        "description": "Contract Date"
    },
    "ContractNumber": {
        "displayName": "Contract Number", 
        "description": "Contract Number"
    },
    "Country": {
        "displayName": "Country", 
        "description": "Country"
    },
    "CountryId": {
        "displayName": "CountryId", 
        "description": "Foreign Key"
    },
    "CurrentDriverStatusChartViewItems": {
        "displayName": "Current Driver Status Chart View Items", 
        "description": "Current Driver Status Chart View Items"
    },
    "CurrentVehicleStatusChartViewItems": {
        "displayName": "Current Vehicle Status Chart View Items", 
        "description": "Current Vehicle Status Chart View Items"
    },
    "CustomerAudit": {
        "displayName": "CustomerAudit", 
        "description": "CustomerAudit"
    },
    "CustomerFeatureSubscription": {
        "displayName": "CustomerFeatureSubscription", 
        "description": "CustomerFeatureSubscription"
    },
    "CustomerFeatureSubscriptionId": {
        "displayName": "CustomerFeatureSubscriptionId", 
        "description": "Foreign Key"
    },
    "CustomerLogo": {
        "displayName": "Customer Logo", 
        "description": "Customer Logo"
    },
    "CustomerLogoFileSize": {
        "displayName": "Customer Logo File size (bytes)", 
        "description": "Customer Logo File size (bytes)", 
        "validationRules": {
            "26ab719b-45fe-4759-bd98-616fa7805f58" : {
                    "errorMessage": "Value must be a number for the field Customer Logo File size (bytes)"
            }
        }
    },
    "CustomerLogoInternalName": {
        "displayName": "Customer Logo Internal Name", 
        "description": "Customer Logo Internal Name"
    },
    "CustomerLogoUrl": {
        "displayName": "Customer Logo Url", 
        "description": "Customer Logo Url"
    },
    "CustomerModelItems": {
        "displayName": "CustomerModel Items", 
        "description": "CustomerModel"
    },
    "CustomerPreOperationalChecklistTemplateItems": {
        "displayName": "Customer Pre-Operational Checklist Template Items", 
        "description": "Customer Pre-Operational Checklist Template Items"
    },
    "CustomerSnapshotItems": {
        "displayName": "CustomerSnapshotItems", 
        "description": "CustomerSnapshotItems"
    },
    "CustomerSSODetailItems": {
        "displayName": "Customer SSO Detail Items", 
        "description": "Customer SSO Detail Items"
    },
    "CustomerToModelItems": {
        "displayName": "Customer To Model Items", 
        "description": "Customer To Model"
    },
    "CustomerToPersonViewItems": {
        "displayName": "Customer To Person View Items", 
        "description": "Customer To Person View Items"
    },
    "DashboardCardViewItems": {
        "displayName": "Dashboard Card View Items", 
        "description": "Dashboard Card View Items"
    },
    "DashboardDriverCardStoreProcedureItems": {
        "displayName": "Dashboard Driver Card Store Procedure Items", 
        "description": "Dashboard Driver Card Store Procedure Items"
    },
    "DashboardFilterItems": {
        "displayName": "Dashboard filter Items", 
        "description": "Dashboard filter Items"
    },
    "DashboardVehicleCardStoreProcedureItems": {
        "displayName": "Dashboard Vehicle Card Store Procedure Items", 
        "description": "Dashboard Vehicle Card Store Procedure Items"
    },
    "DashboardVehicleCardViewItems": {
        "displayName": "Dashboard Vehicle Card View Items", 
        "description": "Customer Items"
    },
    "Dealer": {
        "displayName": "Dealer", 
        "description": "Dealer"
    },
    "DealerCustomer": {
        "displayName": "Dealer Customer?", 
        "description": "Dealer Customer?"
    },
    "DealerId": {
        "displayName": "DealerId", 
        "description": "Foreign Key"
    },
    "DeletedAtUtc": {
        "displayName": "DeletedAtUtc", 
        "description": "DeletedAtUtc Customer"
    },
    "DepartmentItems": {
        "displayName": "Department Items", 
        "description": "Department"
    },
    "Description": {
        "displayName": "Description", 
        "description": "Description"
    },
    "DriverItems": {
        "displayName": "Driver Items", 
        "description": "Driver"
    },
    "DriverLicenseExpiryStoreProcedureItems": {
        "displayName": "Driver License Expiry Store Procedure Items", 
        "description": "Driver License Expiry Store Procedure Items"
    },
    "DriverLicenseExpiryViewItems": {
        "displayName": "Driver License Expiry View Items", 
        "description": "Driver License Expiry View"
    },
    "Email": {
        "displayName": "Email", 
        "description": "Email"
    },
    "EmailGroupsItems": {
        "displayName": "Email Groups Items", 
        "description": "Email Groups Items"
    },
    "GoUserToCustomerItems": {
        "displayName": "Go User To Customer Items", 
        "description": "GOUser Items"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "ImpactFrequencyPerTimeSlotViewItems": {
        "displayName": "Impact frequency per time slot view Items", 
        "description": "Impact frequency per time slot view Items"
    },
    "ImpactFrequencyPerWeekDayViewItems": {
        "displayName": "Impact frequency per week day view Items", 
        "description": "Impact frequency per week day view Items"
    },
    "ImpactFrequencyPerWeekMonthViewItems": {
        "displayName": "Impact frequency per week month view Items", 
        "description": "Impact frequency per week month view Items"
    },
    "IncompletedChecklistViewItems": {
        "displayName": "Incompleted checklist view Items", 
        "description": "Incompleted checklist view Items"
    },
    "LoggedHoursVersusSeatHoursViewItems": {
        "displayName": "Logged hours versus seat hours view Items", 
        "description": "Logged hours versus seat hours view Items"
    },
    "PersonItems": {
        "displayName": "Person Items", 
        "description": "Person Items"
    },
    "PreferredLocale": {
        "displayName": "PreferredLocale", 
        "description": "PreferredLocale"
    },
    "PreferredLocaleString": {
        "displayName": "PreferredLocaleString", 
        "description": "PreferredLocaleString"
    },
    "Prefix": {
        "displayName": "Prefix", 
        "description": "Prefix"
    },
    "Sites": {
        "displayName": "Sites", 
        "description": "Sites"
    },
    "SitesCount": {
        "displayName": "Sites count", 
        "description": "Sites count", 
        "validationRules": {
            "9262515c-c494-4c66-9df3-9b2b8b1fc825" : {
                    "errorMessage": "Value must be a number for the field Sites count"
            }
        }
    },
    "SitesCountAndLink": {
        "displayName": "SitesCountAndLink", 
        "description": "SitesCountAndLink"
    },
    "SlamcoreDeviceItems": {
        "displayName": "SlamcoreDevice Items", 
        "description": "SlamcoreDevice"
    },
    "TodaysImpactStoreProcedureItems": {
        "displayName": "Todays Impact Store Procedure Items", 
        "description": "Todays Impact Store Procedure Items"
    },
    "TodaysImpactViewItems": {
        "displayName": "Todays Impact View Items", 
        "description": "Todays Impact View Items"
    },
    "TodaysPreopCheckStoreProcedureItems": {
        "displayName": "Todays Preop Check Store Procedure Items", 
        "description": "Todays Preop Check Store Procedure"
    },
    "TodaysPreopCheckViewItems": {
        "displayName": "Todays Preop Check View Items", 
        "description": "Todays Preop Check View Items"
    },
    "VehicleItems": {
        "displayName": "Vehicle Items", 
        "description": "Vehicle Items"
    },
    "VehicleUtilizationLastTwelveHoursStoreProcedureItems": {
        "displayName": "Vehicle Utilization Last Twelve Hours Store Procedure Items", 
        "description": "Vehicle Utilization Last Twelve Hours Store Procedure Items"
    },
    "VehicleUtilizationLastTwelveHoursViewItems": {
        "displayName": "Vehicle Utilization Last Twelve Hours View Items", 
        "description": "Vehicle Utilization Last Twelve Hours View Items"
    }
  }
} 