﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

using GenerativeObjects.Practices;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Storage.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer.Extensions;
using Microsoft.AspNetCore.Antiforgery;

using FleetXQ.Data.DataObjects;

namespace FleetXQ.ServiceLayer.EntityApiControllers
{
    [ApiController]
    [ServiceFilter(typeof(ObjectGraphOrDataSetSerializationFilterAttribute))]
    public class VehicleApiController : EntityApiController<VehicleDataObject, VehicleContainer, VehicleCollectionContainer>
    {
        public VehicleApiController(IServiceProvider serviceProvider, IConfiguration configuration, ISettingsProvider settingsProvider, IDataProviderTransaction dataProviderTransaction, IDataProvider<VehicleDataObject> dataProvider, IDataObjectFactory<VehicleDataObject> dataObjectFactory, IEntityApiExtensionProvider<VehicleDataObject> extensions, IEntityDataProvider entityDataProvider, IApiFilterArgumentBuilder apiFilterArgumentBuilder, IAntiforgery antiforgery) : base(serviceProvider, configuration, settingsProvider, dataProviderTransaction, dataProvider, dataObjectFactory, extensions, entityDataProvider, apiFilterArgumentBuilder, antiforgery) { }

        /// <summary>
        /// Get a Vehicle instance by primary key and return data in dataset format
        /// </summary>
      /// <param name="id">Id of the Vehicle entity to get</param>
      /// <param name="include">list of related data to fetch with the Vehicle, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Vehicle are : UnitUnutilisationStoreProcedureItems, BroadcastMessageHistoryItems, Sessions, SlamcoreDeviceHistoryItems, DepartmentChecklist, Firmware, VehicleOtherSettings, VehicleLastGPSLocationView, Customer, ServiceSettings, VehicleSupervisorsViewItems, AllVehicleCalibrationStoreProcedureItems, PersonToPerVehicleMasterAccessViewItems, Inspection, OnDemandSessionItems, PedestrianDetectionHistoryItems, MessageHistoryItems, VORSettingHistoryItems, VehicleCardAccesses, Site, Driver, VehicleAlertSubscriptionItems, VehicleLockoutItems, GeneralProductivityPerVehicleViewItems, VehicleToPreOpCheckilstItems, VehicleSessionlessImpactItems, CurrentStatusVehicleViewItems, NetworkSettingsItems, OnDemandSettings, Module, DetailedSessionViewItems, ChecklistSettings, VehicleDiagnostic, VehicleBroadcastMessageItems, VehicleGPSLocations, VehicleHireDehireHistoryItems, ModuleHistoryItems, UnitUtilisationStoreProcedureItems, DetailedVORSessionStoreProcedureItems, Department, Person, PerVehicleNormalCardAccessItems, ImpactsForVehicleViewItems, VehicleProficiencyViewItems, ChecklistFailurePerVechicleViewItems, PersonToPerVehicleNormalAccessViewItems, Canrule, Model, AllVORSessionsPerVehicleStoreProcedureItems </param>
        /// <returns>Vehicle entity</returns>
        [HttpGet]
        [Route("dataset/api/vehicle/byid/{id}")]
        public async Task<ActionResult<VehicleContainer>> GetDataSetById(System.Guid id, string include)
        {
            return await DoGetDataSetByIdAsync(new List<string> { id.ToString() }, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Get a Vehicle instance by primary key and return data in json structured format
        /// </summary>
      /// <param name="id">Id of the Vehicle entity to get</param>
      /// <param name="include">list of related data to fetch with the Vehicle, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Vehicle are : UnitUnutilisationStoreProcedureItems, BroadcastMessageHistoryItems, Sessions, SlamcoreDeviceHistoryItems, DepartmentChecklist, Firmware, VehicleOtherSettings, VehicleLastGPSLocationView, Customer, ServiceSettings, VehicleSupervisorsViewItems, AllVehicleCalibrationStoreProcedureItems, PersonToPerVehicleMasterAccessViewItems, Inspection, OnDemandSessionItems, PedestrianDetectionHistoryItems, MessageHistoryItems, VORSettingHistoryItems, VehicleCardAccesses, Site, Driver, VehicleAlertSubscriptionItems, VehicleLockoutItems, GeneralProductivityPerVehicleViewItems, VehicleToPreOpCheckilstItems, VehicleSessionlessImpactItems, CurrentStatusVehicleViewItems, NetworkSettingsItems, OnDemandSettings, Module, DetailedSessionViewItems, ChecklistSettings, VehicleDiagnostic, VehicleBroadcastMessageItems, VehicleGPSLocations, VehicleHireDehireHistoryItems, ModuleHistoryItems, UnitUtilisationStoreProcedureItems, DetailedVORSessionStoreProcedureItems, Department, Person, PerVehicleNormalCardAccessItems, ImpactsForVehicleViewItems, VehicleProficiencyViewItems, ChecklistFailurePerVechicleViewItems, PersonToPerVehicleNormalAccessViewItems, Canrule, Model, AllVORSessionsPerVehicleStoreProcedureItems </param>
        /// <param name="byRef">Setting to define how to handle reference loop. If byRef is true, reference loops are handled by serializing the first occurrence of the object and replacing subsequent occurrences with a reference. If ByRef is false, reference loops are ignored. Default is false when byRef is not specified</param>
        /// <returns>Vehicle entity</returns>
        [HttpGet]
        [Route("api/vehicle/byid/{id}")]
        public async Task<ActionResult<VehicleDataObject>> GetById(System.Guid id, string include, bool? byRef)
        {
            return await DoGetByIdAsync(new List<string> { id.ToString() }, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Get a list of Vehicle instances and return data in dataset format
        /// </summary>
        /// <param name="include">list of related data to fetch with the Vehicle, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Vehicle are : UnitUnutilisationStoreProcedureItems, BroadcastMessageHistoryItems, Sessions, SlamcoreDeviceHistoryItems, DepartmentChecklist, Firmware, VehicleOtherSettings, VehicleLastGPSLocationView, Customer, ServiceSettings, VehicleSupervisorsViewItems, AllVehicleCalibrationStoreProcedureItems, PersonToPerVehicleMasterAccessViewItems, Inspection, OnDemandSessionItems, PedestrianDetectionHistoryItems, MessageHistoryItems, VORSettingHistoryItems, VehicleCardAccesses, Site, Driver, VehicleAlertSubscriptionItems, VehicleLockoutItems, GeneralProductivityPerVehicleViewItems, VehicleToPreOpCheckilstItems, VehicleSessionlessImpactItems, CurrentStatusVehicleViewItems, NetworkSettingsItems, OnDemandSettings, Module, DetailedSessionViewItems, ChecklistSettings, VehicleDiagnostic, VehicleBroadcastMessageItems, VehicleGPSLocations, VehicleHireDehireHistoryItems, ModuleHistoryItems, UnitUtilisationStoreProcedureItems, DetailedVORSessionStoreProcedureItems, Department, Person, PerVehicleNormalCardAccessItems, ImpactsForVehicleViewItems, VehicleProficiencyViewItems, ChecklistFailurePerVechicleViewItems, PersonToPerVehicleNormalAccessViewItems, Canrule, Model, AllVORSessionsPerVehicleStoreProcedureItems </param>
        /// <param name="filter">The filter to apply to the Query. Has to be an expression in C# format and can be referencing Vehicle entity properties. Can reference parameters with the format @X , X being the parameter number, starting at 0.</param>
        /// <param name="filterParameters">Filter parameters (TODO : document parameters format).</param>
        /// <param name="pageNumber">To apply pagination to the query. Page number to get. No pagination applied is not set.</param>
        /// <param name="pageSize">Size of the page to return.</param>
        /// <param name="sortOrder">Sort order for the query, desc for descending order, asc for ascending order.</param>
        /// <param name="sortColumn">Vehicle entity property to use to sort.</param>
        /// <returns>List of Vehicle entities</returns>
        [HttpGet]
        [Route("dataset/api/vehicle/list")]
        public async Task<ActionResult<VehicleCollectionContainer>> GetDataSetCollection(string include, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn)
        {
            //List<string> includes, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn
            return await DoGetDataSetCollectionAsync(include?.Split(',').Select(i => i.Trim()).ToList(), filter, filterParameters, pageNumber, pageSize, sortOrder, sortColumn);
        }

        /// <summary>
        /// Get a list of Vehicle instances and return data in json structured format 
        /// </summary>
        /// <param name="include">list of related data to fetch with the Vehicle, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Vehicle are : UnitUnutilisationStoreProcedureItems, BroadcastMessageHistoryItems, Sessions, SlamcoreDeviceHistoryItems, DepartmentChecklist, Firmware, VehicleOtherSettings, VehicleLastGPSLocationView, Customer, ServiceSettings, VehicleSupervisorsViewItems, AllVehicleCalibrationStoreProcedureItems, PersonToPerVehicleMasterAccessViewItems, Inspection, OnDemandSessionItems, PedestrianDetectionHistoryItems, MessageHistoryItems, VORSettingHistoryItems, VehicleCardAccesses, Site, Driver, VehicleAlertSubscriptionItems, VehicleLockoutItems, GeneralProductivityPerVehicleViewItems, VehicleToPreOpCheckilstItems, VehicleSessionlessImpactItems, CurrentStatusVehicleViewItems, NetworkSettingsItems, OnDemandSettings, Module, DetailedSessionViewItems, ChecklistSettings, VehicleDiagnostic, VehicleBroadcastMessageItems, VehicleGPSLocations, VehicleHireDehireHistoryItems, ModuleHistoryItems, UnitUtilisationStoreProcedureItems, DetailedVORSessionStoreProcedureItems, Department, Person, PerVehicleNormalCardAccessItems, ImpactsForVehicleViewItems, VehicleProficiencyViewItems, ChecklistFailurePerVechicleViewItems, PersonToPerVehicleNormalAccessViewItems, Canrule, Model, AllVORSessionsPerVehicleStoreProcedureItems </param>
        /// <param name="filter">The filter to apply to the Query. Has to be an expression in C# format and can be referencing Vehicle entity properties. Can reference parameters with the format @X , X being the parameter number, starting at 0.</param>
        /// <param name="filterParameters">Filter parameters (TODO : document parameters format).</param>
        /// <param name="pageNumber">To apply pagination to the query. Page number to get. No pagination applied is not set.</param>
        /// <param name="pageSize">Size of the page to return.</param>
        /// <param name="sortOrder">Sort order for the query, desc for descending order, asc for ascending order.</param>
        /// <param name="sortProperty">Vehicle entity property to use to sort.</param>
        /// <param name="byRef">Setting to define how to handle reference loop. If byRef is true, reference loops are handled by serializing the first occurrence of the object and replacing subsequent occurrences with a reference. If ByRef is false, reference loops are ignored. Default is false when byRef is not specified</param>
        /// <returns>List of Vehicle entities</returns>
        [HttpGet]
        [Route("api/vehicle/list")]
        public async Task<ActionResult<DataObjectCollection<VehicleDataObject>>> GetCollection(string include, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn, bool? byRef)
        {
            //List<string> includes, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn
            return await DoGetCollectionAsync(include?.Split(',').Select(i => i.Trim()).ToList(), filter, filterParameters, pageNumber, pageSize, sortOrder, sortColumn);
        }

        /// <summary>
        /// Count the number of Vehicle instances
        /// </summary>
        /// <param name="filter">The filter to apply to the Query. Has to be an expression in C# format and can be referencing Vehicle entity properties. Can reference parameters with the format @X , X being the parameter number, starting at 0.</param>
        /// <param name="filterParameters">Filter parameters (TODO : document parameters format).</param>
        [HttpGet]
        [Route("dataset/api/vehicle/count")]
        public async Task<ActionResult<int>> Count(string filter, string filterParameters)
        {
            return await DoCountAsync(filter, filterParameters);
        }

      /// <summary>
        /// Stream the file associated to the VehicleImage document field of a given Vehicle instance
        /// </summary>
      /// <param name="id">Id of the Vehicle entity</param>
        [HttpGet]
        [Route("api/vehicle/file/{id}/VehicleImage")]
        public async Task<IActionResult> GetVehicleImageFile(System.Guid id)
        {
            return await DoGetFileAsync(new List<string> { id.ToString() }, "VehicleImage");
        }
        /// <summary>
        /// Save (create or update if existing) a given Vehicle instance, and return the saved instance data
        /// </summary>
        /// <param name="entity">Vehicle instance to be saved in json dataset format</param>
        /// <param name="include">list of related data to fetch with the Vehicle saved data, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Vehicle are : UnitUnutilisationStoreProcedureItems, BroadcastMessageHistoryItems, Sessions, SlamcoreDeviceHistoryItems, DepartmentChecklist, Firmware, VehicleOtherSettings, VehicleLastGPSLocationView, Customer, ServiceSettings, VehicleSupervisorsViewItems, AllVehicleCalibrationStoreProcedureItems, PersonToPerVehicleMasterAccessViewItems, Inspection, OnDemandSessionItems, PedestrianDetectionHistoryItems, MessageHistoryItems, VORSettingHistoryItems, VehicleCardAccesses, Site, Driver, VehicleAlertSubscriptionItems, VehicleLockoutItems, GeneralProductivityPerVehicleViewItems, VehicleToPreOpCheckilstItems, VehicleSessionlessImpactItems, CurrentStatusVehicleViewItems, NetworkSettingsItems, OnDemandSettings, Module, DetailedSessionViewItems, ChecklistSettings, VehicleDiagnostic, VehicleBroadcastMessageItems, VehicleGPSLocations, VehicleHireDehireHistoryItems, ModuleHistoryItems, UnitUtilisationStoreProcedureItems, DetailedVORSessionStoreProcedureItems, Department, Person, PerVehicleNormalCardAccessItems, ImpactsForVehicleViewItems, VehicleProficiencyViewItems, ChecklistFailurePerVechicleViewItems, PersonToPerVehicleNormalAccessViewItems, Canrule, Model, AllVORSessionsPerVehicleStoreProcedureItems </param>
        /// <returns>The saved Vehicle, after refetch, in json dataset format</returns>
        [HttpPost]
        [Route("dataset/api/vehicle")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<ActionResult<VehicleContainer>> PostDataSet([FromForm] string entity, [FromForm] string include)
        {
            return await DoPostDataSetAsync(entity, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Save (create or update if existing) a given Vehicle instance, and return the saved instance data
        /// </summary>
        /// <param name="entity">Vehicle instance to be saved in json structured format</param>
        /// <param name="include">list of related data to fetch with the Vehicle saved data, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Vehicle are : UnitUnutilisationStoreProcedureItems, BroadcastMessageHistoryItems, Sessions, SlamcoreDeviceHistoryItems, DepartmentChecklist, Firmware, VehicleOtherSettings, VehicleLastGPSLocationView, Customer, ServiceSettings, VehicleSupervisorsViewItems, AllVehicleCalibrationStoreProcedureItems, PersonToPerVehicleMasterAccessViewItems, Inspection, OnDemandSessionItems, PedestrianDetectionHistoryItems, MessageHistoryItems, VORSettingHistoryItems, VehicleCardAccesses, Site, Driver, VehicleAlertSubscriptionItems, VehicleLockoutItems, GeneralProductivityPerVehicleViewItems, VehicleToPreOpCheckilstItems, VehicleSessionlessImpactItems, CurrentStatusVehicleViewItems, NetworkSettingsItems, OnDemandSettings, Module, DetailedSessionViewItems, ChecklistSettings, VehicleDiagnostic, VehicleBroadcastMessageItems, VehicleGPSLocations, VehicleHireDehireHistoryItems, ModuleHistoryItems, UnitUtilisationStoreProcedureItems, DetailedVORSessionStoreProcedureItems, Department, Person, PerVehicleNormalCardAccessItems, ImpactsForVehicleViewItems, VehicleProficiencyViewItems, ChecklistFailurePerVechicleViewItems, PersonToPerVehicleNormalAccessViewItems, Canrule, Model, AllVORSessionsPerVehicleStoreProcedureItems </param>
        /// <param name="byRef">Setting to define how to handle reference loop. If byRef is true, reference loops are handled by serializing the first occurrence of the object and replacing subsequent occurrences with a reference. If ByRef is false, reference loops are ignored. Default is false when byRef is not specified</param>
        /// <returns>The saved Vehicle, after refetch, in json structured format</returns>
        [HttpPost]
        [Route("api/vehicle")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<ActionResult<VehicleDataObject>> Post([FromForm] string entity, [FromForm] string include, bool? byRef)
        {
            return await DoPostAsync(entity, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Delete a given Vehicle instance
        /// </summary>
        /// <param name="vehicle">Vehicle instance to be deleted in json structured format. Only the primary key information is taken into account, rest ignored</param>
        /// <param name="dry">Dry delete if true, full delete otherwise</param>
        /// <returns>Delete information if dry delete</returns>
        [HttpDelete]
        [Route("dataset/api/vehicle")]
        public async Task<ActionResult<string>> Delete(VehicleDataObject vehicle, bool dry = false)
        {
            return await DoDeleteAsync(vehicle, dry);
        }

        /// <summary>
        /// Delete a given Vehicle instance
        /// </summary>
        /// <param name="vehicle">Vehicle instance to be deleted in json structured format. Only the primary key information is taken into account, rest ignored</param>
      /// <param name="id">Id of the Vehicle entity to delete</param>
      /// <param name="dry">Dry delete if true, full delete otherwise</param>
        /// <returns>Delete information if dry delete</returns>
        [HttpDelete]
        [Route("dataset/api/vehicle/{id}")]
        public async Task<ActionResult<string>> Delete(System.Guid id, bool dry = false)
        {
            return await DoDeleteAsync(new List<string> { id.ToString() }, dry);
        }
    }
}
 