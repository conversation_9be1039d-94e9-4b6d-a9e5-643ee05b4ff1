﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using NHibernate;
using NHibernate.Linq;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataProviders.Database
{
    public class CustomerDataProvider : DatabaseDataProvider<CustomerDataObject>
    {
		public CustomerDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<CustomerDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, INHibernateSessionController nhibernateSessionController) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction, nhibernateSessionController)
		{
		}

	    // filterExpression is used to filter data, when filter is statically known. dynamicFilterExpression is used for dynamic filtering, when filter is not known at compile time. Both can be used at the same time
        protected override async Task<int> DoCountFromDatabaseAsync(
			LambdaExpression filterExpression, 
			string dynamicFilterExpression, 
			object[] dynamicFilterArguments, 
			IObjectsDataSet context, 
			Parameters parameters)
        {
			var query = await QueryAsync(dynamicFilterExpression, dynamicFilterArguments, filterExpression);

			// Put the query in the transaction parameters so that custom data provider OnAfterCount extensions can see the result set
			if (parameters != null)
				 parameters[ParameterKeys.DataProviderCountQuery] = query;

			return await query.CountAsync();
        }

        protected override async Task DoDeleteFromDatabaseAsync(
			CustomerDataObject entity, 
			LambdaExpression filterExpression,			// no longer used here - handled higher up the stack by dataprovider extensions
			IObjectsDataSet context, 
			Parameters parameters)
        {
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);
			var toDelete = entity.ToORMEntity();
			await session.DeleteAsync(toDelete);
        }

        // securityFilterExpression is for security : check if the user is allowed to read the entity
        protected override async Task<CustomerDataObject> DoGetFromDatabaseAsync(
			CustomerDataObject entity, 
			LambdaExpression securityFilterExpression, 
			List<string> includes, 
			IObjectsDataSet context, 
			Parameters parameters)
        {
			var securityFilter = securityFilterExpression as Expression<Func<ORMCustomer, bool>>;

			var query = await QueryAsync(
				e => e.Id == entity.Id, 
				null,
				null,
				securityFilter, 
				throwIfAccessDenied: true);

			// Normal (default generated) behaviour is: 
			// includes not treated by orm but with dispatcher mechanism to allow for proper security and extension calls on all objects
			// But the following allows custom implementations to prefetch associations
			_serviceProvider.GetService<IPrefetch<ORMCustomer>>()?.Fetch(query, includes, parameters);

			var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
			var result = (await query.FirstOrDefaultAsync())?.ToDataObject(dataset, _serviceProvider, _threadContext, _nhibernateSessionController, _dataProviderTransaction) as CustomerDataObject;

			return result;
        }

        // filterExpression is used to filter data, when filter is statically known. dynamicFilterExpression is used for dynamic filtering, when filter is not known at compile time. Both can be used at the same time
        protected override async Task<DataObjectCollection<CustomerDataObject>> DoGetCollectionFromDatabaseAsync(
			LambdaExpression filterExpression, 
			string dynamicFilterExpression, 
			object[] dynamicFilterArguments, 
			string orderByPredicate, 
			int pageNumber, 
			int pageSize, 
			List<string> includes, 
			IObjectsDataSet context, 
			Parameters parameters)
        {
			var query = await QueryAsync(dynamicFilterExpression, dynamicFilterArguments, filterExpression);

			if (!String.IsNullOrEmpty(orderByPredicate))
				query = query.OrderBy(orderByPredicate);

			// Normal (default generated) behaviour is: 
			// includes not treated by orm but with dispatcher mechanism to allow for proper security and extension calls on all objects
			// But the following allows custom implementations to prefetch associations
			_serviceProvider.GetService<IPrefetch<ORMCustomer>>()?.Fetch(query, includes, parameters);

			// Do Paging (unless late-paging option is enabled)
			if (!ParameterKeys.IsOptionEnabled(parameters, ParameterKeys.DataProviderGetCollectionLatePaging))
			{
				if (pageNumber != 0 && pageSize > 0)
				{
					query = query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
				}
			}
 
			var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
			var collection = await query.Select(x => x.ToDataObject(dataset, _serviceProvider, _threadContext, _nhibernateSessionController, _dataProviderTransaction)).Cast<CustomerDataObject>().ToListAsync();
			return new DataObjectCollection<CustomerDataObject>(collection, dataset);
        }

        protected override async Task<CustomerDataObject> DoSaveToDatabaseAsync(
			CustomerDataObject entity, 
			List<string> includes, 
			IObjectsDataSet context,
			Parameters parameters)
        {
			return await base.DoSaveToDatabaseAsync(entity, includes, context, parameters);
        }

		private async Task<IQueryable<ORMCustomer>> QueryAsync(
			string dynamicFilterExpression,
			object[] dynamicFilterArguments,
			LambdaExpression compiledFilter)
		{
			return await QueryAsync(null, dynamicFilterExpression, dynamicFilterArguments, compiledFilter);
		}

		private async Task<IQueryable<ORMCustomer>> QueryAsync(
			Expression<Func<ORMCustomer, bool>> selectPredicate,
			string dynamicFilterExpression,
			object[] dynamicFilterArguments,
			LambdaExpression compiledFilter,
			bool throwIfAccessDenied = false)
		{
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);

			IQueryable<ORMCustomer> query = null;

			if (!String.IsNullOrWhiteSpace(dynamicFilterExpression))
			{
				Expression<Func<ORMCustomer, bool>> dynamicPredicate = null;
				// Special case for OneToOne PK -> FK side filtering, we need to override default inner join with an outer left join in order for the filter to work correctly
				if (dynamicFilterExpression.Contains("CustomerAudit == null"))
				{
					dynamicFilterExpression = dynamicFilterExpression.Replace("CustomerAudit == null", "");

					dynamicPredicate = String.IsNullOrWhiteSpace(dynamicFilterExpression)
						? null
						: System.Linq.Dynamic.DynamicExpression.ParseLambda<ORMCustomer, bool>(dynamicFilterExpression, dynamicFilterArguments);

					var queryOver = session.QueryOver<ORMCustomer>();

					if (selectPredicate != null)
						queryOver = queryOver.Where(selectPredicate);

					if (dynamicPredicate != null)
						queryOver = queryOver.Where(dynamicPredicate);

					query = queryOver.Left.JoinQueryOver<ORMCustomerAudit>(x => x.CustomerAudit)
						.List<ORMCustomer>()
						.Where(x => x.CustomerAudit == null)
						.AsQueryable();

					query = await ApplyCompileTimeFilterExpressionAsync(query, compiledFilter, throwIfAccessDenied);

					return query;
				}
 
				query = session.Query<ORMCustomer>();

				if (selectPredicate != null)
					query = query.Where(selectPredicate);

				if (dynamicFilterArguments != null) 
				{
					for (int i = 0; i < dynamicFilterArguments.Length; i++)
					{
						var argType = dynamicFilterArguments[i].GetType();
						if (argType.IsGenericType && argType.GetGenericTypeDefinition() == typeof(NullableType<>))
						{
							dynamicFilterExpression = dynamicFilterExpression.Replace($"@{i}", $"@{i}.Value");
						}
					}
				}

				dynamicPredicate = System.Linq.Dynamic.DynamicExpression.ParseLambda<ORMCustomer, bool>(dynamicFilterExpression, dynamicFilterArguments);

				if (dynamicPredicate != null)
					query = query.Where(dynamicPredicate);
			}
			else
			{
				query = session.Query<ORMCustomer>();

				if (selectPredicate != null)
					query = query.Where(selectPredicate);
			}

			return await ApplyCompileTimeFilterExpressionAsync(query, compiledFilter, throwIfAccessDenied);
		}

		private async Task<IQueryable<ORMCustomer>> ApplyCompileTimeFilterExpressionAsync(
			IQueryable<ORMCustomer> query, 
			LambdaExpression filterExpression, 
			bool throwIfAccessDenied)
		{
			// Apply filter, and if it's a security filter throw exception if no results after applying it
			var filter = filterExpression as Expression<Func<ORMCustomer, bool>>;

			if (filter != null)
			{
				if (query.Any())
				{
					var filtered = query.Where(filter);

					if (!filtered.Any())
					{
						if (throwIfAccessDenied)
						{
							await _authentication.ThrowAccessDeniedAsync(new GOServerException("accessDenied", _authentication.ExplainAccessDenied(EntityAccessEnum.READ, "Customer")));
						}
					}

					query = filtered;
				}
			}

			return query;
		}
    }
}
