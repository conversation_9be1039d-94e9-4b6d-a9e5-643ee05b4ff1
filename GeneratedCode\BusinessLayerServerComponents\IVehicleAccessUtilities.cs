﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
//////////////////////////////////////////////////////////////////////////////////////////// 
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using FleetXQ.BusinessLayer.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// VehicleAccessUtilities Component
	///  
	/// </summary>
	public partial interface IVehicleAccessUtilities 
    {
		/// <summary>
        /// CopyUserVehicleAccess Method
		/// Copy User Vehicle Access 
		/// </summary>
		/// <param name="personId"></param>
        /// <returns></returns>		
		/// <param name="driverIds">driverIds</param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> CopyUserVehicleAccessAsync(System.Guid personId, System.Guid[] driverIds, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// CreateOnDemandAccesses Method
		///  
		/// </summary>
		/// <param name="cardIds"></param>
        /// <returns></returns>		
		/// <param name="vehicleId"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>> CreateOnDemandAccessesAsync(System.Guid[] cardIds, System.Guid vehicleId, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// DeleteOnDemandAccess Method
		///  
		/// </summary>
		/// <param name="accessId"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> DeleteOnDemandAccessAsync(System.Guid accessId, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// GetAccessesForDepartments Method
		///  
		/// </summary>
		/// <param name="personToSiteAccesses"></param>
        /// <returns></returns>		
		/// <param name="personId"></param>
        /// <returns></returns>		
		/// <param name="permissionLevel"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>> GetAccessesForDepartmentsAsync(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses, System.Guid personId, System.Int32 permissionLevel, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// GetAccessesForModels Method
		///  
		/// </summary>
		/// <param name="personToDepartmentAccesses"></param>
        /// <returns></returns>		
		/// <param name="personId"></param>
        /// <returns></returns>		
		/// <param name="permissionLevel"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>> GetAccessesForModelsAsync(DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, System.Guid personId, System.Int32 permissionLevel, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// GetAccessesForVehicles Method
		///  
		/// </summary>
		/// <param name="personToDepartmentAccesses"></param>
        /// <returns></returns>		
		/// <param name="personToModelAccesses"></param>
        /// <returns></returns>		
		/// <param name="personId"></param>
        /// <returns></returns>		
		/// <param name="permissionLevel"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>> GetAccessesForVehiclesAsync(DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses, System.Guid personId, System.Int32 permissionLevel, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// UpdateAccessesForPerson Method
		///  
		/// </summary>
		/// <param name="personToSiteAccesses"></param>
        /// <returns></returns>		
		/// <param name="personToDepartmentAccesses"></param>
        /// <returns></returns>		
		/// <param name="personToModelAccesses"></param>
        /// <returns></returns>		
		/// <param name="personToVehicleAccesses"></param>
        /// <returns></returns>		
		/// <param name="personId"></param>
        /// <returns></returns>		
		/// <param name="PermissionLevel"></param>
        /// <returns></returns>		
		/// <param name="cascadeAddPermission">true for cascading add permissions. For example : if I add permission to department, cascading will add permission for all models and vehicles in the departments added</param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateAccessesForPersonAsync(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses, DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses, DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses, System.Guid personId, System.Int32 PermissionLevel, System.Boolean cascadeAddPermission, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// UpdateVehicleDepartmentAccessesForPerson Method
		/// Update the department vehicle accesses for a person.  
		/// </summary>
		/// <param name="personId"></param>
        /// <returns></returns>		
		/// <param name="updatedPersonToDepartmentAccesses"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateVehicleDepartmentAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updatedPersonToDepartmentAccesses, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// UpdateVehicleModelAccessesForPerson Method
		/// Update the model vehicle accesses for a person.  
		/// </summary>
		/// <param name="personId"></param>
        /// <returns></returns>		
		/// <param name="updateModelAccesses"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateVehicleModelAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updateModelAccesses, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// UpdateVehiclePerVehicleAccessesForPerson Method
		/// Update the per vehicle accesses for a person. 
		/// </summary>
		/// <param name="personId"></param>
        /// <returns></returns>		
		/// <param name="updatePerVehicleAccesses"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateVehiclePerVehicleAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updatePerVehicleAccesses, Dictionary<string, object> parameters = null);		
		/// <summary>
        /// UpdateVehicleSiteAccessesForPerson Method
		/// Update the sites vehicle accesses for a person.  
		/// </summary>
		/// <param name="personId"></param>
        /// <returns></returns>		
		/// <param name="updatedPersonToSiteAccesses"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> UpdateVehicleSiteAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updatedPersonToSiteAccesses, Dictionary<string, object> parameters = null);		

		/// <summary>
		/// Custom code can get IVehicleAccessUtilities instances from the DI Container
		/// But resolved instances from the container are actually surrogate (proxy) implementation which perform security checks before calling the underlying component class 'raw' implementation.
		/// But sometimes in custom code it is convenient to be able to access the raw component (to call public methods that are not published as part of the web-facing API, for example)
		/// So that's what this ComponentClass getter does i.e. 
		/// Given an IVehicleAccessUtilities  interface instance, IVehicleAccessUtilities.ComponentClass gives you the underlying implementation class.
		/// </summary>
		object ComponentClass { get; }
	}
}
