﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class CustomerObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _customerObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all Customer objects for current dataset
		private ConcurrentDictionary< int, CustomerDataObject> _customerObjects = new ConcurrentDictionary< int, CustomerDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<CustomerDataObject> _mergedDataObjects;

		private ConcurrentQueue<CustomerDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<CustomerDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> CustomerObjectInternalIds
		{ 
			get { return _customerObjectInternalIds; }
			set { _customerObjectInternalIds = value; }
		}
		
		// Collection holding all Customer objects for current dataset
		[JsonProperty("CustomerObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, CustomerDataObject> CustomerObjects
		{ 
			get { return _customerObjects; }
			set { _customerObjects = value; }
		}
		
		
 
		
 
		
 
		// Index to quickly find all Customer with a given contactPersonInformation foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> ContactPersonInformation_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		// Index to quickly find all Customer with a given country foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Country_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
 
		
 
		
 
		// Index to quickly find all Customer with a given customerFeatureSubscription foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> CustomerFeatureSubscription_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		// Index to quickly find all Customer with a given dealer foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Dealer_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public CustomerObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public CustomerObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<CustomerObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.CustomerObjects)
			{
                var cloneObject = (CustomerDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.CustomerObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.CustomerObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.CustomerObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			foreach(var fkKeyValue in this.ContactPersonInformation_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "CustomerObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.ContactPersonInformation_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.ContactPersonInformation_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Country_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Country_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Country_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.CustomerFeatureSubscription_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "CustomerObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.CustomerFeatureSubscription_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.CustomerFeatureSubscription_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Dealer_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Dealer_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Dealer_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<CustomerObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.CustomerObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (CustomerDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.CustomerObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.CustomerObjectInternalIds
				.Where(o => this.CustomerObjects[o.Value].IsDirty || this.CustomerObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.CustomerObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var customer in CustomerObjects.Values)
			{
				yield return customer; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the CustomerObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "CustomerObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = CustomerObjects.TryAdd(newInternalId, (CustomerDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (CustomerObjectInternalIds.ContainsKey(((CustomerDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = CustomerObjectInternalIds.TryRemove(((CustomerDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = CustomerObjectInternalIds.TryAdd(((CustomerDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as CustomerDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "CustomerDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

	 
	 
	 
			// Update the ContactPersonInformation FK Index 
			if ((objectToAdd as CustomerDataObject).ContactPersonInformationId != null)
			{
				if (!ContactPersonInformation_FKIndex.ContainsKey((System.Guid)(objectToAdd as CustomerDataObject).ContactPersonInformationId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = ContactPersonInformation_FKIndex.TryAdd((System.Guid)(objectToAdd as CustomerDataObject).ContactPersonInformationId, new List<int>());
					}
				}
				
				if (!ContactPersonInformation_FKIndex[(System.Guid)(objectToAdd as CustomerDataObject).ContactPersonInformationId].Contains(newInternalId))	
					ContactPersonInformation_FKIndex[(System.Guid)(objectToAdd as CustomerDataObject).ContactPersonInformationId].Add(newInternalId);

	            ContactPersonInformationDataObject relatedContactPersonInformation;
	            if ((objectToAdd as CustomerDataObject)._contactPersonInformation_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<ContactPersonInformationDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as CustomerDataObject)._contactPersonInformation_NewObjectId;

	                relatedContactPersonInformation = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedContactPersonInformation = _rootObjectDataSet.GetObject(new ContactPersonInformationDataObject((System.Guid)(objectToAdd as CustomerDataObject).ContactPersonInformationId) { IsNew = false });
	            }

	            if (relatedContactPersonInformation != null && this.RootObjectDataSet.NotifyChanges)
	                relatedContactPersonInformation.NotifyPropertyChanged("Customer", new SeenObjectCollection());
			}
			
	 
			// Update the Country FK Index 
			if (!Country_FKIndex.ContainsKey((objectToAdd as CustomerDataObject).CountryId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Country_FKIndex.TryAdd((objectToAdd as CustomerDataObject).CountryId, new List<int>());
				}
			}
				
			if (!Country_FKIndex[(objectToAdd as CustomerDataObject).CountryId].Contains(newInternalId))
				Country_FKIndex[(objectToAdd as CustomerDataObject).CountryId].Add(newInternalId);

            CountryDataObject relatedCountry;
            if ((objectToAdd as CustomerDataObject)._country_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<CountryDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as CustomerDataObject)._country_NewObjectId;

	            relatedCountry = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedCountry = _rootObjectDataSet.GetObject(new CountryDataObject((objectToAdd as CustomerDataObject).CountryId) { IsNew = false });
            }

			if (relatedCountry != null && this.RootObjectDataSet.NotifyChanges)
                relatedCountry.NotifyPropertyChanged("Customers", new SeenObjectCollection());
			
	 
	 
	 
	 
			// Update the CustomerFeatureSubscription FK Index 
			if ((objectToAdd as CustomerDataObject).CustomerFeatureSubscriptionId != null)
			{
				if (!CustomerFeatureSubscription_FKIndex.ContainsKey((System.Guid)(objectToAdd as CustomerDataObject).CustomerFeatureSubscriptionId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = CustomerFeatureSubscription_FKIndex.TryAdd((System.Guid)(objectToAdd as CustomerDataObject).CustomerFeatureSubscriptionId, new List<int>());
					}
				}
				
				if (!CustomerFeatureSubscription_FKIndex[(System.Guid)(objectToAdd as CustomerDataObject).CustomerFeatureSubscriptionId].Contains(newInternalId))	
					CustomerFeatureSubscription_FKIndex[(System.Guid)(objectToAdd as CustomerDataObject).CustomerFeatureSubscriptionId].Add(newInternalId);

	            CustomerFeatureSubscriptionDataObject relatedCustomerFeatureSubscription;
	            if ((objectToAdd as CustomerDataObject)._customerFeatureSubscription_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<CustomerFeatureSubscriptionDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as CustomerDataObject)._customerFeatureSubscription_NewObjectId;

	                relatedCustomerFeatureSubscription = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedCustomerFeatureSubscription = _rootObjectDataSet.GetObject(new CustomerFeatureSubscriptionDataObject((System.Guid)(objectToAdd as CustomerDataObject).CustomerFeatureSubscriptionId) { IsNew = false });
	            }

	            if (relatedCustomerFeatureSubscription != null && this.RootObjectDataSet.NotifyChanges)
	                relatedCustomerFeatureSubscription.NotifyPropertyChanged("Customer", new SeenObjectCollection());
			}
			
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
			// Update the Dealer FK Index 
			if (!Dealer_FKIndex.ContainsKey((objectToAdd as CustomerDataObject).DealerId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Dealer_FKIndex.TryAdd((objectToAdd as CustomerDataObject).DealerId, new List<int>());
				}
			}
				
			if (!Dealer_FKIndex[(objectToAdd as CustomerDataObject).DealerId].Contains(newInternalId))
				Dealer_FKIndex[(objectToAdd as CustomerDataObject).DealerId].Add(newInternalId);

            DealerDataObject relatedDealer;
            if ((objectToAdd as CustomerDataObject)._dealer_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<DealerDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as CustomerDataObject)._dealer_NewObjectId;

	            relatedDealer = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedDealer = _rootObjectDataSet.GetObject(new DealerDataObject((objectToAdd as CustomerDataObject).DealerId) { IsNew = false });
            }

			if (relatedDealer != null && this.RootObjectDataSet.NotifyChanges)
                relatedDealer.NotifyPropertyChanged("Customers", new SeenObjectCollection());
			
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (CustomerObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as CustomerDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "CustomerObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = CustomerObjectInternalIds.ContainsKey((objectToRemove as CustomerDataObject).PrimaryKey) ? (int?) CustomerObjectInternalIds[(objectToRemove as CustomerDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				CustomerDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = CustomerObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = CustomerObjectInternalIds.TryRemove((objectToRemove as CustomerDataObject).PrimaryKey, out idvalue);
					}
				}
				
		 
		 
		 
			// Delete the ContactPersonInformation FK Index 
				if ((objectToRemove as CustomerDataObject).ContactPersonInformationId != null)
				{
					if (ContactPersonInformation_FKIndex.ContainsKey((System.Guid)(objectToRemove as CustomerDataObject).ContactPersonInformationId) && ContactPersonInformation_FKIndex[(System.Guid)(objectToRemove as CustomerDataObject).ContactPersonInformationId].Contains((int)objectToRemoveInternalId))
					{
						ContactPersonInformation_FKIndex[(System.Guid)(objectToRemove as CustomerDataObject).ContactPersonInformationId].Remove((int)objectToRemoveInternalId);

						if (!ContactPersonInformation_FKIndex[(System.Guid)(objectToRemove as CustomerDataObject).ContactPersonInformationId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = ContactPersonInformation_FKIndex.TryRemove((System.Guid)(objectToRemove as CustomerDataObject).ContactPersonInformationId, out outvalue);
							}
						}
					}

					ContactPersonInformationDataObject relatedContactPersonInformation;
		            if ((objectToRemove as CustomerDataObject)._contactPersonInformation_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<ContactPersonInformationDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as CustomerDataObject)._contactPersonInformation_NewObjectId;

						relatedContactPersonInformation = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedContactPersonInformation = _rootObjectDataSet.GetObject(new ContactPersonInformationDataObject((System.Guid)(objectToRemove as CustomerDataObject).ContactPersonInformationId) { IsNew = false });
		            }

		            if (relatedContactPersonInformation != null && this.RootObjectDataSet.NotifyChanges)
		                relatedContactPersonInformation.NotifyPropertyChanged("Customer", new SeenObjectCollection());
					
				}			
		 
			// Delete the Country FK Index 
				if (Country_FKIndex.ContainsKey((objectToRemove as CustomerDataObject).CountryId) && Country_FKIndex[(objectToRemove as CustomerDataObject).CountryId].Contains((int)objectToRemoveInternalId))
				{
					Country_FKIndex[(objectToRemove as CustomerDataObject).CountryId].Remove((int)objectToRemoveInternalId);

					if (!Country_FKIndex[(objectToRemove as CustomerDataObject).CountryId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Country_FKIndex.TryRemove((objectToRemove as CustomerDataObject).CountryId, out outvalue);
						}
					}
				}
				
				CountryDataObject relatedCountry;
	            if ((objectToRemove as CustomerDataObject)._country_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<CountryDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as CustomerDataObject)._country_NewObjectId;

					relatedCountry = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedCountry = _rootObjectDataSet.GetObject(new CountryDataObject((objectToRemove as CustomerDataObject).CountryId) { IsNew = false });
	            }

	            if (relatedCountry != null && this.RootObjectDataSet.NotifyChanges)
	                relatedCountry.NotifyPropertyChanged("Customers", new SeenObjectCollection());
				
		 
		 
		 
		 
			// Delete the CustomerFeatureSubscription FK Index 
				if ((objectToRemove as CustomerDataObject).CustomerFeatureSubscriptionId != null)
				{
					if (CustomerFeatureSubscription_FKIndex.ContainsKey((System.Guid)(objectToRemove as CustomerDataObject).CustomerFeatureSubscriptionId) && CustomerFeatureSubscription_FKIndex[(System.Guid)(objectToRemove as CustomerDataObject).CustomerFeatureSubscriptionId].Contains((int)objectToRemoveInternalId))
					{
						CustomerFeatureSubscription_FKIndex[(System.Guid)(objectToRemove as CustomerDataObject).CustomerFeatureSubscriptionId].Remove((int)objectToRemoveInternalId);

						if (!CustomerFeatureSubscription_FKIndex[(System.Guid)(objectToRemove as CustomerDataObject).CustomerFeatureSubscriptionId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = CustomerFeatureSubscription_FKIndex.TryRemove((System.Guid)(objectToRemove as CustomerDataObject).CustomerFeatureSubscriptionId, out outvalue);
							}
						}
					}

					CustomerFeatureSubscriptionDataObject relatedCustomerFeatureSubscription;
		            if ((objectToRemove as CustomerDataObject)._customerFeatureSubscription_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<CustomerFeatureSubscriptionDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as CustomerDataObject)._customerFeatureSubscription_NewObjectId;

						relatedCustomerFeatureSubscription = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedCustomerFeatureSubscription = _rootObjectDataSet.GetObject(new CustomerFeatureSubscriptionDataObject((System.Guid)(objectToRemove as CustomerDataObject).CustomerFeatureSubscriptionId) { IsNew = false });
		            }

		            if (relatedCustomerFeatureSubscription != null && this.RootObjectDataSet.NotifyChanges)
		                relatedCustomerFeatureSubscription.NotifyPropertyChanged("Customer", new SeenObjectCollection());
					
				}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			// Delete the Dealer FK Index 
				if (Dealer_FKIndex.ContainsKey((objectToRemove as CustomerDataObject).DealerId) && Dealer_FKIndex[(objectToRemove as CustomerDataObject).DealerId].Contains((int)objectToRemoveInternalId))
				{
					Dealer_FKIndex[(objectToRemove as CustomerDataObject).DealerId].Remove((int)objectToRemoveInternalId);

					if (!Dealer_FKIndex[(objectToRemove as CustomerDataObject).DealerId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Dealer_FKIndex.TryRemove((objectToRemove as CustomerDataObject).DealerId, out outvalue);
						}
					}
				}
				
				DealerDataObject relatedDealer;
	            if ((objectToRemove as CustomerDataObject)._dealer_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<DealerDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as CustomerDataObject)._dealer_NewObjectId;

					relatedDealer = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedDealer = _rootObjectDataSet.GetObject(new DealerDataObject((objectToRemove as CustomerDataObject).DealerId) { IsNew = false });
	            }

	            if (relatedDealer != null && this.RootObjectDataSet.NotifyChanges)
	                relatedDealer.NotifyPropertyChanged("Customers", new SeenObjectCollection());
				
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return CustomerObjects.ContainsKey(internalObjectId) ? CustomerObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as CustomerDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "CustomerObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = CustomerObjectInternalIds.ContainsKey((objectToGet as CustomerDataObject).PrimaryKey) ? (int?) CustomerObjectInternalIds[(objectToGet as CustomerDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return CustomerObjects.ContainsKey((int)objectToGetInternalId) ? CustomerObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return CustomerObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return CustomerObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		 
		 
		
		public IEnumerable<CustomerDataObject> GetCustomerForContactPersonInformation(ContactPersonInformationDataObject contactPersonInformationInstance) 
		{
			if (contactPersonInformationInstance.IsNew)
            {
			
              return CustomerObjects.Where(o => o.Value._contactPersonInformation_NewObjectId != null && o.Value._contactPersonInformation_NewObjectId == contactPersonInformationInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (ContactPersonInformation_FKIndex.ContainsKey(contactPersonInformationInstance.Id))
			{
				return ContactPersonInformation_FKIndex[contactPersonInformationInstance.Id].Where(e => CustomerObjects.ContainsKey(e)).Select(e => CustomerObjects[e]);
			}
			
			return new DataObjectCollection<CustomerDataObject>();
		}
		 
		
		public IEnumerable<CustomerDataObject> GetCustomersForCountry(CountryDataObject countryInstance) 
		{
			if (countryInstance.IsNew)
            {
			
              return CustomerObjects.Where(o => o.Value._country_NewObjectId != null && o.Value._country_NewObjectId == countryInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Country_FKIndex.ContainsKey(countryInstance.Id))
			{
				return Country_FKIndex[countryInstance.Id].Where(e => CustomerObjects.ContainsKey(e)).Select(e => CustomerObjects[e]);
			}
			
			return new DataObjectCollection<CustomerDataObject>();
		}
		 
		 
		 
		 
		
		public IEnumerable<CustomerDataObject> GetCustomerForCustomerFeatureSubscription(CustomerFeatureSubscriptionDataObject customerFeatureSubscriptionInstance) 
		{
			if (customerFeatureSubscriptionInstance.IsNew)
            {
			
              return CustomerObjects.Where(o => o.Value._customerFeatureSubscription_NewObjectId != null && o.Value._customerFeatureSubscription_NewObjectId == customerFeatureSubscriptionInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (CustomerFeatureSubscription_FKIndex.ContainsKey(customerFeatureSubscriptionInstance.Id))
			{
				return CustomerFeatureSubscription_FKIndex[customerFeatureSubscriptionInstance.Id].Where(e => CustomerObjects.ContainsKey(e)).Select(e => CustomerObjects[e]);
			}
			
			return new DataObjectCollection<CustomerDataObject>();
		}
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		
		public IEnumerable<CustomerDataObject> GetCustomersForDealer(DealerDataObject dealerInstance) 
		{
			if (dealerInstance.IsNew)
            {
			
              return CustomerObjects.Where(o => o.Value._dealer_NewObjectId != null && o.Value._dealer_NewObjectId == dealerInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Dealer_FKIndex.ContainsKey(dealerInstance.Id))
			{
				return Dealer_FKIndex[dealerInstance.Id].Where(e => CustomerObjects.ContainsKey(e)).Select(e => CustomerObjects[e]);
			}
			
			return new DataObjectCollection<CustomerDataObject>();
		}
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
			if (relationName == "AccessGroupItems")
            {
				IEnumerable< AccessGroupDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.AccessGroupObjectsDataSet.GetAccessGroupItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ChecklistStatusViewItems")
            {
				IEnumerable< ChecklistStatusViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ChecklistStatusViewObjectsDataSet.GetChecklistStatusViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
 
			if (relationName == "CurrentDriverStatusChartViewItems")
            {
				IEnumerable< CurrentDriverStatusChartViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CurrentDriverStatusChartViewObjectsDataSet.GetCurrentDriverStatusChartViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CurrentVehicleStatusChartViewItems")
            {
				IEnumerable< CurrentVehicleStatusChartViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CurrentVehicleStatusChartViewObjectsDataSet.GetCurrentVehicleStatusChartViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerAudit")
            {
				IEnumerable< CustomerAuditDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerAuditObjectsDataSet.GetCustomerAuditForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "CustomerModelItems")
            {
				IEnumerable< CustomerModelDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerModelObjectsDataSet.GetCustomerModelItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerPreOperationalChecklistTemplateItems")
            {
				IEnumerable< CustomerPreOperationalChecklistTemplateDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerPreOperationalChecklistTemplateObjectsDataSet.GetCustomerPreOperationalChecklistTemplateItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerSnapshotItems")
            {
				IEnumerable< CustomerSnapshotDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerSnapshotObjectsDataSet.GetCustomerSnapshotItemsForCustomerBeingChangeTracked(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerSSODetailItems")
            {
				IEnumerable< CustomerSSODetailDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerSSODetailObjectsDataSet.GetCustomerSSODetailItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerToModelItems")
            {
				IEnumerable< CustomerToModelDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerToModelObjectsDataSet.GetCustomerToModelItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerToPersonViewItems")
            {
				IEnumerable< CustomerToPersonViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerToPersonViewObjectsDataSet.GetCustomerToPersonViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardCardViewItems")
            {
				IEnumerable< DashboardDriverCardViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardDriverCardViewObjectsDataSet.GetDashboardCardViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardDriverCardStoreProcedureItems")
            {
				IEnumerable< DashboardDriverCardStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardDriverCardStoreProcedureObjectsDataSet.GetDashboardDriverCardStoreProcedureItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardFilterItems")
            {
				IEnumerable< DashboardFilterDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject);
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.DriverAccessAbuseFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.PedestrianDetectionHistoryFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.MainDashboardFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.GeneralProductivityReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.EmailSubscriptionReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.VORReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.OnDemandAuthorisationFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.DashboardFilterMoreFieldsObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.HireDeHireReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.SlamcoreDeviceFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.ImpactReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.LicenseExpiryReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.BroadcastMessageHistoryFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.SynchronizationStatusReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.ProficiencyReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.AllVehicleCalibrationFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.PreOpReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.MachineUnlockReportFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.FeatureSubscriptionsFilterObjectsDataSet.GetDashboardFilterItemsForCustomer(rootObject as CustomerDataObject).Cast<DashboardFilterDataObject>());
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardVehicleCardStoreProcedureItems")
            {
				IEnumerable< DashboardVehicleCardStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardVehicleCardStoreProcedureObjectsDataSet.GetDashboardVehicleCardStoreProcedureItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardVehicleCardViewItems")
            {
				IEnumerable< DashboardVehicleCardViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardVehicleCardViewObjectsDataSet.GetDashboardVehicleCardViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "DepartmentItems")
            {
				IEnumerable< DepartmentDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DepartmentObjectsDataSet.GetDepartmentItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DriverItems")
            {
				IEnumerable< DriverDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DriverObjectsDataSet.GetDriverItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DriverLicenseExpiryStoreProcedureItems")
            {
				IEnumerable< DriverLicenseExpiryStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DriverLicenseExpiryStoreProcedureObjectsDataSet.GetDriverLicenseExpiryStoreProcedureItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DriverLicenseExpiryViewItems")
            {
				IEnumerable< DriverLicenseExpiryViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DriverLicenseExpiryViewObjectsDataSet.GetDriverLicenseExpiryViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "EmailGroupsItems")
            {
				IEnumerable< EmailGroupsDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.EmailGroupsObjectsDataSet.GetEmailGroupsItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "GoUserToCustomerItems")
            {
				IEnumerable< GoUserToCustomerDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GoUserToCustomerObjectsDataSet.GetGoUserToCustomerItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactFrequencyPerTimeSlotViewItems")
            {
				IEnumerable< ImpactFrequencyPerTimeSlotViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactFrequencyPerTimeSlotViewObjectsDataSet.GetImpactFrequencyPerTimeSlotViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactFrequencyPerWeekDayViewItems")
            {
				IEnumerable< ImpactFrequencyPerWeekDayViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactFrequencyPerWeekDayViewObjectsDataSet.GetImpactFrequencyPerWeekDayViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactFrequencyPerWeekMonthViewItems")
            {
				IEnumerable< ImpactFrequencyPerWeekMonthViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactFrequencyPerWeekMonthViewObjectsDataSet.GetImpactFrequencyPerWeekMonthViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "IncompletedChecklistViewItems")
            {
				IEnumerable< IncompletedChecklistViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.IncompletedChecklistViewObjectsDataSet.GetIncompletedChecklistViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "LoggedHoursVersusSeatHoursViewItems")
            {
				IEnumerable< LoggedHoursVersusSeatHoursViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.LoggedHoursVersusSeatHoursViewObjectsDataSet.GetLoggedHoursVersusSeatHoursViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonItems")
            {
				IEnumerable< PersonDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonObjectsDataSet.GetPersonItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "Sites")
            {
				IEnumerable< SiteDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SiteObjectsDataSet.GetSitesForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "SlamcoreDeviceItems")
            {
				IEnumerable< SlamcoreDeviceDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SlamcoreDeviceObjectsDataSet.GetSlamcoreDeviceItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysImpactStoreProcedureItems")
            {
				IEnumerable< TodaysImpactStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysImpactStoreProcedureObjectsDataSet.GetTodaysImpactStoreProcedureItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysImpactViewItems")
            {
				IEnumerable< TodaysImpactViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysImpactViewObjectsDataSet.GetTodaysImpactViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysPreopCheckStoreProcedureItems")
            {
				IEnumerable< TodaysPreopCheckStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysPreopCheckStoreProcedureObjectsDataSet.GetTodaysPreopCheckStoreProcedureItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysPreopCheckViewItems")
            {
				IEnumerable< TodaysPreopCheckViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysPreopCheckViewObjectsDataSet.GetTodaysPreopCheckViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleItems")
            {
				IEnumerable< VehicleDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleObjectsDataSet.GetVehicleItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleUtilizationLastTwelveHoursStoreProcedureItems")
            {
				IEnumerable< VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleUtilizationLastTwelveHoursStoreProcedureObjectsDataSet.GetVehicleUtilizationLastTwelveHoursStoreProcedureItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleUtilizationLastTwelveHoursViewItems")
            {
				IEnumerable< VehicleUtilizationLastTwelveHoursViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleUtilizationLastTwelveHoursViewObjectsDataSet.GetVehicleUtilizationLastTwelveHoursViewItemsForCustomer(rootObject as CustomerDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var CustomerDataSet = dataSetToMerge as CustomerObjectsDataSet;
				if(CustomerDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in CustomerDataSet.CustomerObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "CustomerObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as CustomerDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
		 
		 
		 
			// Reconstruct the ContactPersonInformation FK Index 
			ContactPersonInformation_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in CustomerObjects.Values)
			{
				if (item.ContactPersonInformationId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.ContactPersonInformationId;	

				if (!ContactPersonInformation_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = ContactPersonInformation_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "CustomerObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				ContactPersonInformation_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the Country FK Index 
			Country_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in CustomerObjects.Values)
			{
				if (item.CountryId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.CountryId;	

				if (!Country_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Country_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "CustomerObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Country_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
			// Reconstruct the CustomerFeatureSubscription FK Index 
			CustomerFeatureSubscription_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in CustomerObjects.Values)
			{
				if (item.CustomerFeatureSubscriptionId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.CustomerFeatureSubscriptionId;	

				if (!CustomerFeatureSubscription_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = CustomerFeatureSubscription_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "CustomerObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				CustomerFeatureSubscription_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			// Reconstruct the Dealer FK Index 
			Dealer_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in CustomerObjects.Values)
			{
				if (item.DealerId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.DealerId;	

				if (!Dealer_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Dealer_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "CustomerObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Dealer_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (CustomerObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}