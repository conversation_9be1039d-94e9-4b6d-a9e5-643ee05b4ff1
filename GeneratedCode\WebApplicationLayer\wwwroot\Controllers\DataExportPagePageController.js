﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

(function () {
	// 
	FleetXQ.Web.Controllers.DataExportPagePageController = function(applicationController) {
		var self = this;
		this.subscriptions = [];
		// store all subscriptions in this array, to unsubscribe on release.
		
		this._objectType = "DataExportPagePage";

		this.applicationController = applicationController;
		this.ObjectsDataSet = applicationController.ObjectsDataSet;
		this.contextId = [this.applicationController.getNextContextId()];
		this.customController = undefined;

		// Integrate custom code if any
		if (FleetXQ.Web.Controllers.DataExportPagePageControllerCustom !== undefined) {
		    this.customController = new FleetXQ.Web.Controllers.DataExportPagePageControllerCustom(self);
		};

		// Initialize View Models and Data Stores
		
			this.AlertHistoryGrid1ViewModel = new FleetXQ.Web.ViewModels.AlertHistoryGrid1ViewModel(this, $("#AlertHistoryGrid1"), null, null, this.contextId);		
		this.AlertHistoryGrid1ViewModel.StatusData.ShowTitle(true);		
		this.AlertHistoryGrid1ViewModel.include = "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-3aa74726-06eb-4523-bd76-31689039e46f";	
		
			this.VehicleSlamcoreLocationHistoryGridViewModel = new FleetXQ.Web.ViewModels.VehicleSlamcoreLocationHistoryGridViewModel(this, $("#VehicleSlamcoreLocationHistoryGrid"), null, null, this.contextId);		
		this.VehicleSlamcoreLocationHistoryGridViewModel.StatusData.ShowTitle(true);		
		this.VehicleSlamcoreLocationHistoryGridViewModel.include = "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-b96fddb5-673b-4152-a01a-5b017669d4ce";	
		
			this.ExportJobStatusGrid2ViewModel = new FleetXQ.Web.ViewModels.ExportJobStatusGrid2ViewModel(this, $("#ExportJobStatusGrid2"), null, null, this.contextId);		
		this.ExportJobStatusGrid2ViewModel.StatusData.ShowTitle(false);		
		this.ExportJobStatusGrid2ViewModel.include = "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-f6c1eb1d-719e-4f6d-8dac-652d9cf685e4";	
		
			this.VehicleSlamcoreLocationHistoryGrid1ViewModel = new FleetXQ.Web.ViewModels.VehicleSlamcoreLocationHistoryGrid1ViewModel(this, $("#VehicleSlamcoreLocationHistoryGrid1"), null, null, this.contextId);		
		this.VehicleSlamcoreLocationHistoryGrid1ViewModel.StatusData.ShowTitle(true);		
		this.VehicleSlamcoreLocationHistoryGrid1ViewModel.include = "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-91b72f91-4a77-4671-850c-81afedb76554";	
		
			this.SlamcoreDeviceGrid2ViewModel = new FleetXQ.Web.ViewModels.SlamcoreDeviceGrid2ViewModel(this, $("#SlamcoreDeviceGrid2"), null, null, this.contextId);		
		this.SlamcoreDeviceGrid2ViewModel.StatusData.ShowTitle(true);		
		this.SlamcoreDeviceGrid2ViewModel.include = "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-b40c80bb-1fba-4472-9ab5-8a710249af63";	
		
			this.SlamcorePedestrianDetectionGridViewModel = new FleetXQ.Web.ViewModels.SlamcorePedestrianDetectionGridViewModel(this, $("#SlamcorePedestrianDetectionGrid"), null, null, this.contextId);		
		this.SlamcorePedestrianDetectionGridViewModel.StatusData.ShowTitle(true);		
		this.SlamcorePedestrianDetectionGridViewModel.include = "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-226cf864-e6d5-40a2-b938-9535492e60a8";	
		this.customPageTitle = ko.observable(null);

		this.iconToUse = ko.observable(null);

		this.pageTitle = ko.pureComputed (function() {
			if (self.customPageTitle()) {
				return self.customPageTitle();
			}
			return "Data Export";
		});

		this.pageTitleParams = ko.observable({});
		
		this.showGoBackBadge = ko.observable(false);
		this.goBackBadgeLabel = ko.observable("pages/DataExportPagePage:navigation.BackNavigationBadge");
		this.showItemCountBadge = ko.observable(false);
		this.isLoading = ko.observable(false);
		this.itemCount = ko.pureComputed(function() {
			return null;
		}); 
		this.goBack = function () {
			if(self.IsInEditMode && self.IsInEditMode()) {
				if(!confirm(FleetXQ.Web.Messages.i18n.t('messages.unsavedChanges')))
					return;
				
				// Cancel edit mode on current page controller
				if (self.CancelEdit) {
					self.CancelEdit(false);
				}					
			}

};

		this.subscriptions.push(this.AlertHistoryGrid1ViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnAlertHistoryGrid1ViewModelIsBusyChanged(newValue); }));
		this.subscriptions.push(this.VehicleSlamcoreLocationHistoryGridViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnVehicleSlamcoreLocationHistoryGridViewModelIsBusyChanged(newValue); }));
		this.subscriptions.push(this.ExportJobStatusGrid2ViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnExportJobStatusGrid2ViewModelIsBusyChanged(newValue); }));
		this.subscriptions.push(this.VehicleSlamcoreLocationHistoryGrid1ViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnVehicleSlamcoreLocationHistoryGrid1ViewModelIsBusyChanged(newValue); }));
		this.subscriptions.push(this.SlamcoreDeviceGrid2ViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnSlamcoreDeviceGrid2ViewModelIsBusyChanged(newValue); }));
		this.subscriptions.push(this.SlamcorePedestrianDetectionGridViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnSlamcorePedestrianDetectionGridViewModelIsBusyChanged(newValue); }));
		
		this.IsInEditMode = function() {

			return  (self.AlertHistoryGrid1ViewModel.StatusData.DisplayMode && self.AlertHistoryGrid1ViewModel.StatusData.DisplayMode() == 'edit') ||  (self.VehicleSlamcoreLocationHistoryGridViewModel.StatusData.DisplayMode && self.VehicleSlamcoreLocationHistoryGridViewModel.StatusData.DisplayMode() == 'edit') ||  (self.ExportJobStatusGrid2ViewModel.StatusData.DisplayMode && self.ExportJobStatusGrid2ViewModel.StatusData.DisplayMode() == 'edit') ||  (self.VehicleSlamcoreLocationHistoryGrid1ViewModel.StatusData.DisplayMode && self.VehicleSlamcoreLocationHistoryGrid1ViewModel.StatusData.DisplayMode() == 'edit') ||  (self.SlamcoreDeviceGrid2ViewModel.StatusData.DisplayMode && self.SlamcoreDeviceGrid2ViewModel.StatusData.DisplayMode() == 'edit') ||  (self.SlamcorePedestrianDetectionGridViewModel.StatusData.DisplayMode && self.SlamcorePedestrianDetectionGridViewModel.StatusData.DisplayMode() == 'edit');
		};

		this.CancelEdit = function(isCommandCall) {
			if (self.AlertHistoryGrid1ViewModel.CancelEdit) {
				self.AlertHistoryGrid1ViewModel.CancelEdit(isCommandCall);
			}
			if (self.VehicleSlamcoreLocationHistoryGridViewModel.CancelEdit) {
				self.VehicleSlamcoreLocationHistoryGridViewModel.CancelEdit(isCommandCall);
			}
			if (self.ExportJobStatusGrid2ViewModel.CancelEdit) {
				self.ExportJobStatusGrid2ViewModel.CancelEdit(isCommandCall);
			}
			if (self.VehicleSlamcoreLocationHistoryGrid1ViewModel.CancelEdit) {
				self.VehicleSlamcoreLocationHistoryGrid1ViewModel.CancelEdit(isCommandCall);
			}
			if (self.SlamcoreDeviceGrid2ViewModel.CancelEdit) {
				self.SlamcoreDeviceGrid2ViewModel.CancelEdit(isCommandCall);
			}
			if (self.SlamcorePedestrianDetectionGridViewModel.CancelEdit) {
				self.SlamcorePedestrianDetectionGridViewModel.CancelEdit(isCommandCall);
			}
		};

		// Events Handlers
		this.OnAlertHistoryGrid1ViewModelIsBusyChanged = function (newValue) {
		};

		this.OnVehicleSlamcoreLocationHistoryGridViewModelIsBusyChanged = function (newValue) {
		};

		this.OnExportJobStatusGrid2ViewModelIsBusyChanged = function (newValue) {
		};

		this.OnVehicleSlamcoreLocationHistoryGrid1ViewModelIsBusyChanged = function (newValue) {
		};

		this.OnSlamcoreDeviceGrid2ViewModelIsBusyChanged = function (newValue) {
		};

		this.OnSlamcorePedestrianDetectionGridViewModelIsBusyChanged = function (newValue) {
		};

		this.initialize = function() {
			if (FleetXQ.Web.Controllers.Custom && FleetXQ.Web.Controllers.Custom.getPageTitle) {
				self.customPageTitle(FleetXQ.Web.Controllers.Custom.getPageTitle(self));
			}

	
			// Call custom initialize if defined
			if (self.customController !== undefined && self.customController.initialize !== undefined) {
			    self.customController.initialize();
			}
		// Initial data load for all source elements (no dependencies)
			if (!GO.Filter.hasUrlFilter(self.AlertHistoryGrid1ViewModel.FILTER_NAME, self.AlertHistoryGrid1ViewModel)) {
				self.AlertHistoryGrid1ViewModel.LoadAlertHistoryObjectCollection();
			}
			if (!GO.Filter.hasUrlFilter(self.VehicleSlamcoreLocationHistoryGridViewModel.FILTER_NAME, self.VehicleSlamcoreLocationHistoryGridViewModel)) {
				self.VehicleSlamcoreLocationHistoryGridViewModel.LoadVehicleSlamcoreLocationHistoryObjectCollection();
			}
			if (!GO.Filter.hasUrlFilter(self.ExportJobStatusGrid2ViewModel.FILTER_NAME, self.ExportJobStatusGrid2ViewModel)) {
				self.ExportJobStatusGrid2ViewModel.LoadExportJobStatusObjectCollection();
			}
			if (!GO.Filter.hasUrlFilter(self.VehicleSlamcoreLocationHistoryGrid1ViewModel.FILTER_NAME, self.VehicleSlamcoreLocationHistoryGrid1ViewModel)) {
				self.VehicleSlamcoreLocationHistoryGrid1ViewModel.LoadVehicleSlamcoreLocationHistoryObjectCollection();
			}
			if (!GO.Filter.hasUrlFilter(self.SlamcoreDeviceGrid2ViewModel.FILTER_NAME, self.SlamcoreDeviceGrid2ViewModel)) {
				self.SlamcoreDeviceGrid2ViewModel.LoadSlamcoreDeviceObjectCollection();
			}
			if (!GO.Filter.hasUrlFilter(self.SlamcorePedestrianDetectionGridViewModel.FILTER_NAME, self.SlamcorePedestrianDetectionGridViewModel)) {
				self.SlamcorePedestrianDetectionGridViewModel.LoadSlamcorePedestrianDetectionObjectCollection();
			}
		};
		this.initialize();



		this.release = function() {
			// unsubscribe
			for(var i = 0;i <  self.subscriptions.length;i++) {
				self.subscriptions[i].dispose();
			}
			self.subscriptions = [];
			self.ObjectsDataSet.cleanContext(self.contextId);
			
			self.AlertHistoryGrid1ViewModel.release();
			self.AlertHistoryGrid1ViewModel = null;
			self.VehicleSlamcoreLocationHistoryGridViewModel.release();
			self.VehicleSlamcoreLocationHistoryGridViewModel = null;
			self.ExportJobStatusGrid2ViewModel.release();
			self.ExportJobStatusGrid2ViewModel = null;
			self.VehicleSlamcoreLocationHistoryGrid1ViewModel.release();
			self.VehicleSlamcoreLocationHistoryGrid1ViewModel = null;
			self.SlamcoreDeviceGrid2ViewModel.release();
			self.SlamcoreDeviceGrid2ViewModel = null;
			self.SlamcorePedestrianDetectionGridViewModel.release();
			self.SlamcorePedestrianDetectionGridViewModel = null;
		};


		return self;
	};		
	
	if (window.ApplicationSourceHandler)
		window.ApplicationSourceHandler.onSourceLoaded("/Controllers/DataExportPagePageController.js");
} ());
