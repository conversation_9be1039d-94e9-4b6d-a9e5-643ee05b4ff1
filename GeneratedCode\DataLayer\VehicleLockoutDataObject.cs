﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'VehicleLockout'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class VehicleLockoutDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("Comment")]
		protected System.String _comment;
		[JsonProperty ("DriverId")]
		protected Nullable<System.Guid> _driverId;
		[JsonProperty ("GOUserId")]
		protected Nullable<System.Guid> _gOUserId;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("LockoutTime")]
		protected System.DateTime _lockoutTime;
		[JsonProperty("LockoutTime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _lockoutTime_WithTimezoneOffset;
		[JsonProperty ("Note")]
		protected System.String _note;
		[JsonProperty ("RealImpact")]
		protected Nullable<ImpactLockoutConfirmationEnum> _realImpact;
		[JsonProperty ("Reason")]
		protected LockoutReasonEnum _reason;
		[JsonProperty ("SessionId")]
		protected Nullable<System.Guid> _sessionId;
		[JsonProperty ("UnlockDateTime")]
		protected System.DateTime _unlockDateTime;
		[JsonProperty("UnlockDateTime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _unlockDateTime_WithTimezoneOffset;
		[JsonProperty ("VehicleId")]
		protected Nullable<System.Guid> _vehicleId;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)

		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _driver_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_driver_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _gOUser_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_gOUser_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _session_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_session_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _vehicle_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_vehicle_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public VehicleLockoutDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public VehicleLockoutDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public VehicleLockoutDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public VehicleLockoutDataObject Initialize(VehicleLockoutDataObject template, bool deepCopy)
		{
			this.SetLockoutTimeValue(template.LockoutTime, false, false);
			this._lockoutTime_WithTimezoneOffset = template._lockoutTime_WithTimezoneOffset;
			this.SetUnlockDateTimeValue(template.UnlockDateTime, false, false);
			this._unlockDateTime_WithTimezoneOffset = template._unlockDateTime_WithTimezoneOffset;
			this.SetCommentValue(template.Comment, false, false);
			this.SetDriverIdValue(template.DriverId, false, false);
			this.SetGOUserIdValue(template.GOUserId, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetNoteValue(template.Note, false, false);
			this.SetRealImpactValue(template.RealImpact, false, false);
			this.SetReasonValue(template.Reason, false, false);
			this.SetSessionIdValue(template.SessionId, false, false);
			this.SetVehicleIdValue(template.VehicleId, false, false);
 
 
			this._driver_NewObjectId = template._driver_NewObjectId;
 
			this._gOUser_NewObjectId = template._gOUser_NewObjectId;
 
			this._session_NewObjectId = template._session_NewObjectId;
 
			this._vehicle_NewObjectId = template._vehicle_NewObjectId;
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual VehicleLockoutDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual VehicleLockoutDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<VehicleLockoutDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var vehicleLockoutSource = sourceObject as VehicleLockoutDataObject;

			if (ReferenceEquals(null, vehicleLockoutSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetCommentValue(vehicleLockoutSource.Comment, false, false);
			this.SetDriverIdValue(vehicleLockoutSource.DriverId, false, false);
			this.SetGOUserIdValue(vehicleLockoutSource.GOUserId, false, false);
			this.SetIdValue(vehicleLockoutSource.Id, false, false);
			this.SetLockoutTimeValue(vehicleLockoutSource.LockoutTime, false, false);
			this.SetNoteValue(vehicleLockoutSource.Note, false, false);
			this.SetRealImpactValue(vehicleLockoutSource.RealImpact, false, false);
			this.SetReasonValue(vehicleLockoutSource.Reason, false, false);
			this.SetSessionIdValue(vehicleLockoutSource.SessionId, false, false);
			this.SetUnlockDateTimeValue(vehicleLockoutSource.UnlockDateTime, false, false);
			this.SetVehicleIdValue(vehicleLockoutSource.VehicleId, false, false);

			this._driver_NewObjectId = (sourceObject as VehicleLockoutDataObject)._driver_NewObjectId;

			this._gOUser_NewObjectId = (sourceObject as VehicleLockoutDataObject)._gOUser_NewObjectId;

			this._session_NewObjectId = (sourceObject as VehicleLockoutDataObject)._session_NewObjectId;

			this._vehicle_NewObjectId = (sourceObject as VehicleLockoutDataObject)._vehicle_NewObjectId;

			if (deepCopy)
			{
				this.ObjectsDataSet = vehicleLockoutSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(vehicleLockoutSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as VehicleLockoutDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {

			if (this._driver_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._driver_NewObjectId))
				{
                    this._driver_NewObjectId = null;
				}
                else
				{
					this._driver_NewObjectId = datasetMergingInternalIdMapping[(int) this._driver_NewObjectId];
				}
			}

			if (this._gOUser_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._gOUser_NewObjectId))
				{
                    this._gOUser_NewObjectId = null;
				}
                else
				{
					this._gOUser_NewObjectId = datasetMergingInternalIdMapping[(int) this._gOUser_NewObjectId];
				}
			}

			if (this._session_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._session_NewObjectId))
				{
                    this._session_NewObjectId = null;
				}
                else
				{
					this._session_NewObjectId = datasetMergingInternalIdMapping[(int) this._session_NewObjectId];
				}
			}

			if (this._vehicle_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._vehicle_NewObjectId))
				{
                    this._vehicle_NewObjectId = null;
				}
                else
				{
					this._vehicle_NewObjectId = datasetMergingInternalIdMapping[(int) this._vehicle_NewObjectId];
				}
			}

		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<AllVehicleUnlocksViewDataObject> _allVehicleUnlocksViewService => _serviceProvider.GetRequiredService<IDataProvider<AllVehicleUnlocksViewDataObject>>();

		private readonly SemaphoreSlim __allVehicleUnlocksViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __allVehicleUnlocksViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "AllVehicleUnlocksViewItems", which is a collection of AllVehicleUnlocksViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of AllVehicleUnlocksViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<AllVehicleUnlocksViewDataObject>> LoadAllVehicleUnlocksViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadAllVehicleUnlocksViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<AllVehicleUnlocksViewDataObject>> LoadAllVehicleUnlocksViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __allVehicleUnlocksViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__allVehicleUnlocksViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleLockoutId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _allVehicleUnlocksViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __allVehicleUnlocksViewItemsAlreadyLazyLoaded = true;
                }

                return await GetAllVehicleUnlocksViewItemsAsync(false);
            }
            finally
            {
                __allVehicleUnlocksViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<AllVehicleUnlocksViewDataObject> AllVehicleUnlocksViewItems 
		{
			get
			{			
				return GetAllVehicleUnlocksViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeAllVehicleUnlocksViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleLockoutDataObject") && ObjectsDataSet.RelationsToInclude["VehicleLockoutDataObject"].Contains("AllVehicleUnlocksViewItems");
		}

		public virtual async Task<DataObjectCollection<AllVehicleUnlocksViewDataObject>> GetAllVehicleUnlocksViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__allVehicleUnlocksViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadAllVehicleUnlocksViewItemsAsync(forceReload : forceReload);
			}
			var allVehicleUnlocksViewItems = ObjectsDataSet.GetRelatedObjects<AllVehicleUnlocksViewDataObject>(this, "AllVehicleUnlocksViewItems");							
			allVehicleUnlocksViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(AllVehicleUnlocksViewItems_CollectionChanged);
				
			return allVehicleUnlocksViewItems;
		}

        private void AllVehicleUnlocksViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as AllVehicleUnlocksViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : AllVehicleUnlocksView", "VehicleLockoutDataObject.AllVehicleUnlocksViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleLockoutDataObject throw an exception while trying to add AllVehicleUnlocksViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicleLockout_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleLockoutId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleLockoutId == default(System.Guid))
							relatedObj.VehicleLockoutId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as AllVehicleUnlocksViewDataObject).VehicleLockout = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DriverDataObject> _driverService => _serviceProvider.GetRequiredService<IDataProvider<DriverDataObject>>();
      public virtual void SetDriverValue(DriverDataObject valueToSet)
		{
			SetDriverValue(valueToSet, true, true);
		}

        public virtual void SetDriverValue(DriverDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DriverDataObject existing_driver = null ;

			if ( !(this.DriverId == null || ObjectsDataSet == null))
			{
				DriverDataObject key;

				if (this._driver_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DriverDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._driver_NewObjectId;			
				}

				existing_driver = (DriverDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_driver ,valueToSet))
            {
                if (valueToSet == null)
                {
					_driver_NewObjectId = null;
					_driverId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Driver", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleLockoutDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_driver_NewObjectId != valueToSet.InternalObjectId)
					{
						_driver_NewObjectId = valueToSet.InternalObjectId;
						_driverId = valueToSet.Id;
						OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_driverId != valueToSet.Id)
					{
						_driver_NewObjectId = null;

						_driverId = valueToSet.Id;
						OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_driver_NewObjectId = null;
					_driverId = null;
					
				OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_driver ,valueToSet))
				OnPropertyChanged("Driver", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __driverSemaphore = new SemaphoreSlim(1, 1);
		private bool __driverAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Driver", which is a DriverDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DriverDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DriverDataObject> LoadDriverAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDriverAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DriverDataObject> LoadDriverAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __driverSemaphore.WaitAsync();
			
	        try
            {
                if (!__driverAlreadyLazyLoaded || forceReload)
                {
								
					if (this.DriverId == null)
					{
						return null;
					}
				
					DriverDataObject driver = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __driverAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId);
						driver.IsNew = false;
						driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
						if (driver != null)
						{
							return driver;
						}
					}

					driver = await _driverService.GetAsync(_serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId), parameters : parameters, skipSecurity: skipSecurity);

					SetDriverValue(driver, false, false);
					__driverAlreadyLazyLoaded = true;				
		
					driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId);
					driver.IsNew = false;
					driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
                    __driverAlreadyLazyLoaded = true;
                }

                return await GetDriverAsync(false);
            }
            finally
            {
                __driverSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DriverDataObject Driver 
		{
			get
			{			
				return GetDriverAsync(true).Result;
			}
			set
			{
				SetDriverValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDriver()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleLockoutDataObject") && ObjectsDataSet.RelationsToInclude["VehicleLockoutDataObject"].Contains("Driver");
		}

		public virtual async Task<DriverDataObject> GetDriverAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DriverDataObject driver;

				
			if (_driver_NewObjectId != null)
			{
				driver = _serviceProvider.GetRequiredService<DriverDataObject>();
				driver.IsNew = true;
				driver.InternalObjectId = _driver_NewObjectId;
				driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
			}
			else
			{
				if (this.DriverId == null)
					return null;
				if (DriverId == null)
					driver = null;
				else
				driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId);
				driver.IsNew = false;
				driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
				
				if (allowLazyLoading && driver == null && LazyLoadingEnabled && (!__driverAlreadyLazyLoaded || forceReload))
				{
					driver = await LoadDriverAsync(forceReload : forceReload);
				}
			}
				
			return driver;
		}

		public virtual Nullable<System.Guid> DriverForeignKey
		{
			get { return DriverId; }
			set 
			{	
				DriverId = value;
			}
			
		}
		

		protected IDataProvider<GOUserDataObject> _gOUserService => _serviceProvider.GetRequiredService<IDataProvider<GOUserDataObject>>();
      public virtual void SetGOUserValue(GOUserDataObject valueToSet)
		{
			SetGOUserValue(valueToSet, true, true);
		}

        public virtual void SetGOUserValue(GOUserDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			GOUserDataObject existing_gOUser = null ;

			if ( !(this.GOUserId == null || ObjectsDataSet == null))
			{
				GOUserDataObject key;

				if (this._gOUser_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<GOUserDataObject>().Initialize((System.Guid)this.GOUserId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<GOUserDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._gOUser_NewObjectId;			
				}

				existing_gOUser = (GOUserDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_gOUser ,valueToSet))
            {
                if (valueToSet == null)
                {
					_gOUser_NewObjectId = null;
					_gOUserId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("GOUser", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleLockoutDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_gOUser_NewObjectId != valueToSet.InternalObjectId)
					{
						_gOUser_NewObjectId = valueToSet.InternalObjectId;
						_gOUserId = valueToSet.Id;
						OnPropertyChanged("GOUserId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_gOUserId != valueToSet.Id)
					{
						_gOUser_NewObjectId = null;

						_gOUserId = valueToSet.Id;
						OnPropertyChanged("GOUserId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_gOUser_NewObjectId = null;
					_gOUserId = null;
					
				OnPropertyChanged("GOUserId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_gOUser ,valueToSet))
				OnPropertyChanged("GOUser", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __gOUserSemaphore = new SemaphoreSlim(1, 1);
		private bool __gOUserAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "GOUser", which is a GOUserDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a GOUserDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<GOUserDataObject> LoadGOUserAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadGOUserAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<GOUserDataObject> LoadGOUserAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __gOUserSemaphore.WaitAsync();
			
	        try
            {
                if (!__gOUserAlreadyLazyLoaded || forceReload)
                {
								
					if (this.GOUserId == null)
					{
						return null;
					}
				
					GOUserDataObject gOUser = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __gOUserAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						gOUser = _serviceProvider.GetRequiredService<GOUserDataObject>().Initialize((System.Guid)this.GOUserId);
						gOUser.IsNew = false;
						gOUser = (GOUserDataObject)ObjectsDataSet.GetObject(gOUser);
						if (gOUser != null)
						{
							return gOUser;
						}
					}

					gOUser = await _gOUserService.GetAsync(_serviceProvider.GetRequiredService<GOUserDataObject>().Initialize((System.Guid)this.GOUserId), parameters : parameters, skipSecurity: skipSecurity);

					SetGOUserValue(gOUser, false, false);
					__gOUserAlreadyLazyLoaded = true;				
		
					gOUser = _serviceProvider.GetRequiredService<GOUserDataObject>().Initialize((System.Guid)this.GOUserId);
					gOUser.IsNew = false;
					gOUser = (GOUserDataObject)ObjectsDataSet.GetObject(gOUser);
                    __gOUserAlreadyLazyLoaded = true;
                }

                return await GetGOUserAsync(false);
            }
            finally
            {
                __gOUserSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual GOUserDataObject GOUser 
		{
			get
			{			
				return GetGOUserAsync(true).Result;
			}
			set
			{
				SetGOUserValue(value);
			}
		}
		
		public virtual bool ShouldSerializeGOUser()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleLockoutDataObject") && ObjectsDataSet.RelationsToInclude["VehicleLockoutDataObject"].Contains("GOUser");
		}

		public virtual async Task<GOUserDataObject> GetGOUserAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			GOUserDataObject gOUser;

				
			if (_gOUser_NewObjectId != null)
			{
				gOUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
				gOUser.IsNew = true;
				gOUser.InternalObjectId = _gOUser_NewObjectId;
				gOUser = (GOUserDataObject)ObjectsDataSet.GetObject(gOUser);
			}
			else
			{
				if (this.GOUserId == null)
					return null;
				if (GOUserId == null)
					gOUser = null;
				else
				gOUser = _serviceProvider.GetRequiredService<GOUserDataObject>().Initialize((System.Guid)this.GOUserId);
				gOUser.IsNew = false;
				gOUser = (GOUserDataObject)ObjectsDataSet.GetObject(gOUser);
				
				if (allowLazyLoading && gOUser == null && LazyLoadingEnabled && (!__gOUserAlreadyLazyLoaded || forceReload))
				{
					gOUser = await LoadGOUserAsync(forceReload : forceReload);
				}
			}
				
			return gOUser;
		}

		public virtual Nullable<System.Guid> GOUserForeignKey
		{
			get { return GOUserId; }
			set 
			{	
				GOUserId = value;
			}
			
		}
		

		protected IDataProvider<SessionDataObject> _sessionService => _serviceProvider.GetRequiredService<IDataProvider<SessionDataObject>>();
      public virtual void SetSessionValue(SessionDataObject valueToSet)
		{
			SetSessionValue(valueToSet, true, true);
		}

        public virtual void SetSessionValue(SessionDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			SessionDataObject existing_session = null ;

			if ( !(this.SessionId == null || ObjectsDataSet == null))
			{
				SessionDataObject key;

				if (this._session_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<SessionDataObject>().Initialize((System.Guid)this.SessionId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<SessionDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._session_NewObjectId;			
				}

				existing_session = (SessionDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_session ,valueToSet))
            {
                if (valueToSet == null)
                {
					_session_NewObjectId = null;
					_sessionId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Session", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleLockoutDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_session_NewObjectId != valueToSet.InternalObjectId)
					{
						_session_NewObjectId = valueToSet.InternalObjectId;
						_sessionId = valueToSet.Id;
						OnPropertyChanged("SessionId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_sessionId != valueToSet.Id)
					{
						_session_NewObjectId = null;

						_sessionId = valueToSet.Id;
						OnPropertyChanged("SessionId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_session_NewObjectId = null;
					_sessionId = null;
					
				OnPropertyChanged("SessionId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_session ,valueToSet))
				OnPropertyChanged("Session", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __sessionSemaphore = new SemaphoreSlim(1, 1);
		private bool __sessionAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Session", which is a SessionDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SessionDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SessionDataObject> LoadSessionAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSessionAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SessionDataObject> LoadSessionAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __sessionSemaphore.WaitAsync();
			
	        try
            {
                if (!__sessionAlreadyLazyLoaded || forceReload)
                {
								
					if (this.SessionId == null)
					{
						return null;
					}
				
					SessionDataObject session = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __sessionAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						session = _serviceProvider.GetRequiredService<SessionDataObject>().Initialize((System.Guid)this.SessionId);
						session.IsNew = false;
						session = (SessionDataObject)ObjectsDataSet.GetObject(session);
						if (session != null)
						{
							return session;
						}
					}

					session = await _sessionService.GetAsync(_serviceProvider.GetRequiredService<SessionDataObject>().Initialize((System.Guid)this.SessionId), parameters : parameters, skipSecurity: skipSecurity);

					SetSessionValue(session, false, false);
					__sessionAlreadyLazyLoaded = true;				
		
					session = _serviceProvider.GetRequiredService<SessionDataObject>().Initialize((System.Guid)this.SessionId);
					session.IsNew = false;
					session = (SessionDataObject)ObjectsDataSet.GetObject(session);
                    __sessionAlreadyLazyLoaded = true;
                }

                return await GetSessionAsync(false);
            }
            finally
            {
                __sessionSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SessionDataObject Session 
		{
			get
			{			
				return GetSessionAsync(true).Result;
			}
			set
			{
				SetSessionValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSession()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleLockoutDataObject") && ObjectsDataSet.RelationsToInclude["VehicleLockoutDataObject"].Contains("Session");
		}

		public virtual async Task<SessionDataObject> GetSessionAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SessionDataObject session;

				
			if (_session_NewObjectId != null)
			{
				session = _serviceProvider.GetRequiredService<SessionDataObject>();
				session.IsNew = true;
				session.InternalObjectId = _session_NewObjectId;
				session = (SessionDataObject)ObjectsDataSet.GetObject(session);
			}
			else
			{
				if (this.SessionId == null)
					return null;
				if (SessionId == null)
					session = null;
				else
				session = _serviceProvider.GetRequiredService<SessionDataObject>().Initialize((System.Guid)this.SessionId);
				session.IsNew = false;
				session = (SessionDataObject)ObjectsDataSet.GetObject(session);
				
				if (allowLazyLoading && session == null && LazyLoadingEnabled && (!__sessionAlreadyLazyLoaded || forceReload))
				{
					session = await LoadSessionAsync(forceReload : forceReload);
				}
			}
				
			return session;
		}

		public virtual Nullable<System.Guid> SessionForeignKey
		{
			get { return SessionId; }
			set 
			{	
				SessionId = value;
			}
			
		}
		

		protected IDataProvider<VehicleDataObject> _vehicleService => _serviceProvider.GetRequiredService<IDataProvider<VehicleDataObject>>();
      public virtual void SetVehicleValue(VehicleDataObject valueToSet)
		{
			SetVehicleValue(valueToSet, true, true);
		}

        public virtual void SetVehicleValue(VehicleDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			VehicleDataObject existing_vehicle = null ;

			if ( !(this.VehicleId == null || ObjectsDataSet == null))
			{
				VehicleDataObject key;

				if (this._vehicle_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<VehicleDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._vehicle_NewObjectId;			
				}

				existing_vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_vehicle ,valueToSet))
            {
                if (valueToSet == null)
                {
					_vehicle_NewObjectId = null;
					_vehicleId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Vehicle", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleLockoutDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_vehicle_NewObjectId != valueToSet.InternalObjectId)
					{
						_vehicle_NewObjectId = valueToSet.InternalObjectId;
						_vehicleId = valueToSet.Id;
						OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_vehicleId != valueToSet.Id)
					{
						_vehicle_NewObjectId = null;

						_vehicleId = valueToSet.Id;
						OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_vehicle_NewObjectId = null;
					_vehicleId = null;
					
				OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_vehicle ,valueToSet))
				OnPropertyChanged("Vehicle", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __vehicleSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Vehicle", which is a VehicleDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a VehicleDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<VehicleDataObject> LoadVehicleAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<VehicleDataObject> LoadVehicleAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleAlreadyLazyLoaded || forceReload)
                {
								
					if (this.VehicleId == null)
					{
						return null;
					}
				
					VehicleDataObject vehicle = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __vehicleAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId);
						vehicle.IsNew = false;
						vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
						if (vehicle != null)
						{
							return vehicle;
						}
					}

					vehicle = await _vehicleService.GetAsync(_serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId), parameters : parameters, skipSecurity: skipSecurity);

					SetVehicleValue(vehicle, false, false);
					__vehicleAlreadyLazyLoaded = true;				
		
					vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId);
					vehicle.IsNew = false;
					vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
                    __vehicleAlreadyLazyLoaded = true;
                }

                return await GetVehicleAsync(false);
            }
            finally
            {
                __vehicleSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual VehicleDataObject Vehicle 
		{
			get
			{			
				return GetVehicleAsync(true).Result;
			}
			set
			{
				SetVehicleValue(value);
			}
		}
		
		public virtual bool ShouldSerializeVehicle()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleLockoutDataObject") && ObjectsDataSet.RelationsToInclude["VehicleLockoutDataObject"].Contains("Vehicle");
		}

		public virtual async Task<VehicleDataObject> GetVehicleAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			VehicleDataObject vehicle;

				
			if (_vehicle_NewObjectId != null)
			{
				vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
				vehicle.IsNew = true;
				vehicle.InternalObjectId = _vehicle_NewObjectId;
				vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
			}
			else
			{
				if (this.VehicleId == null)
					return null;
				if (VehicleId == null)
					vehicle = null;
				else
				vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId);
				vehicle.IsNew = false;
				vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
				
				if (allowLazyLoading && vehicle == null && LazyLoadingEnabled && (!__vehicleAlreadyLazyLoaded || forceReload))
				{
					vehicle = await LoadVehicleAsync(forceReload : forceReload);
				}
			}
				
			return vehicle;
		}

		public virtual Nullable<System.Guid> VehicleForeignKey
		{
			get { return VehicleId; }
			set 
			{	
				VehicleId = value;
			}
			
		}
		

		public override void ClearLazyLoadFlags()
		{
			__allVehicleUnlocksViewItemsAlreadyLazyLoaded = false;
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadDriverAsync()) != null)
				result.Add(Driver);
			if ((await LoadGOUserAsync()) != null)
				result.Add(GOUser);
			if ((await LoadSessionAsync()) != null)
				result.Add(Session);
			if ((await LoadVehicleAsync()) != null)
				result.Add(Vehicle);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadAllVehicleUnlocksViewItemsAsync()) != null)
				result.AddRange(AllVehicleUnlocksViewItems);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				GOUser == other ||
				(other is GOUserDataObject && (GOUserId != default(Nullable<System.Guid>)) && (GOUserId == (other as GOUserDataObject).Id)) || 
				Vehicle == other ||
				(other is VehicleDataObject && (VehicleId != default(Nullable<System.Guid>)) && (VehicleId == (other as VehicleDataObject).Id)) || 
				Session == other ||
				(other is SessionDataObject && (SessionId != default(Nullable<System.Guid>)) && (SessionId == (other as SessionDataObject).Id)) || 
				Driver == other ||
				(other is DriverDataObject && (DriverId != default(Nullable<System.Guid>)) && (DriverId == (other as DriverDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetCommentValue(System.String valueToSet)
		{
			SetCommentValue(valueToSet, true, true);
		}

		public virtual void SetCommentValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_comment != valueToSet)
			{
				_comment = valueToSet;

				OnPropertyChanged("Comment", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Comment property of the VehicleLockout DataObject</summary>
        public virtual System.String Comment 
		{
			get	{ return String.IsNullOrEmpty(_comment) ? null : _comment; }
			
			
			set
			{
				SetCommentValue(value);
			}
		}		
			
			
		public virtual void SetDriverIdValue(Nullable<System.Guid> valueToSet)
		{
			SetDriverIdValue(valueToSet, true, true);
		}

		public virtual void SetDriverIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_driverId != valueToSet)
			{
				_driverId = valueToSet;

				OnPropertyChanged("DriverId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DriverId property of the VehicleLockout DataObject</summary>
        public virtual Nullable<System.Guid> DriverId 
		{
			get	{ return _driverId;}
			
			
			set
			{
				SetDriverIdValue(value);
			}
		}		
			
			
		public virtual void SetGOUserIdValue(Nullable<System.Guid> valueToSet)
		{
			SetGOUserIdValue(valueToSet, true, true);
		}

		public virtual void SetGOUserIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_gOUserId != valueToSet)
			{
				_gOUserId = valueToSet;

				OnPropertyChanged("GOUserId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The GOUserId property of the VehicleLockout DataObject</summary>
        public virtual Nullable<System.Guid> GOUserId 
		{
			get	{ return _gOUserId;}
			
			
			set
			{
				SetGOUserIdValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the VehicleLockout DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetLockoutTimeValue(System.DateTime valueToSet)
		{
			SetLockoutTimeValue(valueToSet, true, true);
		}

		public virtual void SetLockoutTimeValue(System.DateTime valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null || valueToSet == DateTime.MinValue) 
			{
				if (_lockoutTime != DateTime.MinValue.ToUniversalTime())
				{
					_lockoutTime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("LockoutTime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_lockoutTime != DateTime.MaxValue.ToUniversalTime())
				{
					_lockoutTime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("LockoutTime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_lockoutTime != valueToSet ||
                (_lockoutTime != null && ((DateTime)_lockoutTime).Kind == DateTimeKind.Unspecified))
			{
				_lockoutTime = DateTime.SpecifyKind(valueToSet, DateTimeKind.Local);
				OnPropertyChanged("LockoutTime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Lockout Time property of the VehicleLockout DataObject</summary>
        public virtual System.DateTime LockoutTime 
		{
			get	{ return _lockoutTime;}
			
			
			set
			{
				SetLockoutTimeValue(value);
			}
		}		
			
			
		public virtual void SetNoteValue(System.String valueToSet)
		{
			SetNoteValue(valueToSet, true, true);
		}

		public virtual void SetNoteValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_note != valueToSet)
			{
				_note = valueToSet;

				OnPropertyChanged("Note", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Note property of the VehicleLockout DataObject</summary>
        public virtual System.String Note 
		{
			get	{ return String.IsNullOrEmpty(_note) ? null : _note; }
			
			
			set
			{
				SetNoteValue(value);
			}
		}		
			
			
		public virtual void SetRealImpactValue(Nullable<ImpactLockoutConfirmationEnum> valueToSet)
		{
			SetRealImpactValue(valueToSet, true, true);
		}

		public virtual void SetRealImpactValue(Nullable<ImpactLockoutConfirmationEnum> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_realImpact != valueToSet)
			{
				_realImpact = valueToSet;

				OnPropertyChanged("RealImpact", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("RealImpactDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Real Impact property of the VehicleLockout DataObject</summary>
        public virtual Nullable<ImpactLockoutConfirmationEnum> RealImpact 
		{
			get	{ return _realImpact;}
			
			
			set
			{
				SetRealImpactValue(value);
			}
		}		
      public virtual string RealImpactDisplayString
		{
			get
			{
				if (RealImpact == null)
					return "-";

				return RealImpactEnumDisplayNameCollection.Where(v => v.Value == RealImpact).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<ImpactLockoutConfirmationEnum>> RealImpactEnumDisplayNameCollection
	    {
	        get
	        {
                return ImpactLockoutConfirmationEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetReasonValue(LockoutReasonEnum valueToSet)
		{
			SetReasonValue(valueToSet, true, true);
		}

		public virtual void SetReasonValue(LockoutReasonEnum valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_reason != valueToSet)
			{
				_reason = valueToSet;

				OnPropertyChanged("Reason", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("ReasonDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Reason property of the VehicleLockout DataObject</summary>
        public virtual LockoutReasonEnum Reason 
		{
			get	{ return _reason;}
			
			
			set
			{
				SetReasonValue(value);
			}
		}		
      public virtual string ReasonDisplayString
		{
			get
			{
				return ReasonEnumDisplayNameCollection.Where(v => v.Value == Reason).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<LockoutReasonEnum>> ReasonEnumDisplayNameCollection
	    {
	        get
	        {
                return LockoutReasonEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetSessionIdValue(Nullable<System.Guid> valueToSet)
		{
			SetSessionIdValue(valueToSet, true, true);
		}

		public virtual void SetSessionIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_sessionId != valueToSet)
			{
				_sessionId = valueToSet;

				OnPropertyChanged("SessionId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SessionId property of the VehicleLockout DataObject</summary>
        public virtual Nullable<System.Guid> SessionId 
		{
			get	{ return _sessionId;}
			
			
			set
			{
				SetSessionIdValue(value);
			}
		}		
			
			
		public virtual void SetUnlockDateTimeValue(System.DateTime valueToSet)
		{
			SetUnlockDateTimeValue(valueToSet, true, true);
		}

		public virtual void SetUnlockDateTimeValue(System.DateTime valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null || valueToSet == DateTime.MinValue) 
			{
				if (_unlockDateTime != DateTime.MinValue.ToUniversalTime())
				{
					_unlockDateTime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("UnlockDateTime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_unlockDateTime != DateTime.MaxValue.ToUniversalTime())
				{
					_unlockDateTime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("UnlockDateTime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_unlockDateTime != valueToSet ||
                (_unlockDateTime != null && ((DateTime)_unlockDateTime).Kind == DateTimeKind.Unspecified))
			{
				_unlockDateTime = DateTime.SpecifyKind(valueToSet, DateTimeKind.Local);
				OnPropertyChanged("UnlockDateTime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Unlock DateTime property of the VehicleLockout DataObject</summary>
        public virtual System.DateTime UnlockDateTime 
		{
			get	{ return _unlockDateTime;}
			
			
			set
			{
				SetUnlockDateTimeValue(value);
			}
		}		
			
			
		public virtual void SetVehicleIdValue(Nullable<System.Guid> valueToSet)
		{
			SetVehicleIdValue(valueToSet, true, true);
		}

		public virtual void SetVehicleIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleId != valueToSet)
			{
				_vehicleId = valueToSet;

				OnPropertyChanged("VehicleId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The VehicleId property of the VehicleLockout DataObject</summary>
        public virtual Nullable<System.Guid> VehicleId 
		{
			get	{ return _vehicleId;}
			
			
			set
			{
				SetVehicleIdValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
			var _allVehicleUnlocksViewItems = GetAllVehicleUnlocksViewItemsAsync(false).Result;
			if (_allVehicleUnlocksViewItems != null)
            {
                foreach (var item in _allVehicleUnlocksViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("VehicleLockout.", propertyName), callers);                    
                }
            }
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<VehicleLockoutDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is VehicleLockoutDataObject))
				return false;

			var p = (VehicleLockoutDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.Comment == p.Comment;
			fieldsComparison &= this.DriverId == p.DriverId;
			fieldsComparison &= this.GOUserId == p.GOUserId;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.LockoutTime == p.LockoutTime;
			fieldsComparison &= this.Note == p.Note;
			fieldsComparison &= this.RealImpact == p.RealImpact;
			fieldsComparison &= this.Reason == p.Reason;
			fieldsComparison &= this.SessionId == p.SessionId;
			fieldsComparison &= this.UnlockDateTime == p.UnlockDateTime;
			fieldsComparison &= this.VehicleId == p.VehicleId;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// LockoutTime is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._lockoutTime_WithTimezoneOffset != null)
			{
				this.LockoutTime = ((DateTimeOffset)this._lockoutTime_WithTimezoneOffset).DateTime;
			}
			// UnlockDateTime is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._unlockDateTime_WithTimezoneOffset != null)
			{
				this.UnlockDateTime = ((DateTimeOffset)this._unlockDateTime_WithTimezoneOffset).DateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class VehicleLockoutCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public VehicleLockoutCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public VehicleLockoutCollectionContainer()
		{
		}
		
		public VehicleLockoutCollectionContainer Construct(DataObjectCollection<VehicleLockoutDataObject> vehicleLockoutItems)
        {
            if (vehicleLockoutItems == null)
                return this;
				
			this.PrimaryKeys = vehicleLockoutItems.Select(c => c.PrimaryKey).ToList();
            if (vehicleLockoutItems.ObjectsDataSet == null)
            {
                vehicleLockoutItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = vehicleLockoutItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = vehicleLockoutItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<VehicleLockoutDataObject> ExtractVehicleLockoutItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<VehicleLockoutDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<VehicleLockoutDataObject>(typeof(VehicleLockoutDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class VehicleLockoutContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public VehicleLockoutContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual VehicleLockoutContainer Construct(VehicleLockoutDataObject vehicleLockout, bool includeDirtyObjectsOnly = false)
		{
            if (vehicleLockout == null)
                return this;

			this.PrimaryKey = vehicleLockout.PrimaryKey;
			
            if (vehicleLockout.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(vehicleLockout);
            }

			if(vehicleLockout.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity VehicleLockout", "Unable to set a dataset to the entity. Container may not be initialized", "VehicleLockoutDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : Vehicle Lockout");
			}

			if(vehicleLockout.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in VehicleLockoutDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "VehicleLockoutDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in Vehicle LockoutDataObject");
			}
			this.InternalObjectId = (int) vehicleLockout.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? vehicleLockout.ObjectsDataSet.CloneDirtyObjects() : vehicleLockout.ObjectsDataSet;

			return this;
		}
		
		public VehicleLockoutDataObject ExtractVehicleLockout()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<VehicleLockoutDataObject>(typeof(VehicleLockoutDataObject), InternalObjectId);
			
			return result;
        }	
	}

}