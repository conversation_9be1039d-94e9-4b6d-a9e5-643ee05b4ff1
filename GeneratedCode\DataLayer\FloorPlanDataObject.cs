﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'FloorPlan'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class FloorPlanDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("File")]
		protected System.String _file;
		[JsonProperty ("FileFileSize")]
		protected Nullable<System.Int32> _fileFileSize;
		[JsonProperty ("FileInternalName")]
		protected System.String _fileInternalName;
		[JsonProperty ("FloorLevel")]
		protected System.String _floorLevel;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("Length")]
		protected Nullable<System.Decimal> _length;
		[JsonProperty ("Name")]
		protected System.String _name;
		[JsonProperty ("Opacity")]
		protected Nullable<System.Decimal> _opacity;
		[JsonProperty ("Rotation")]
		protected Nullable<System.Decimal> _rotation;
		[JsonProperty ("Scale")]
		protected Nullable<System.Decimal> _scale;
		[JsonProperty ("SiteId")]
		protected System.Guid _siteId;
		[JsonProperty ("Width")]
		protected Nullable<System.Decimal> _width;
		[JsonProperty ("XPosition")]
		protected Nullable<System.Decimal> _xPosition;
		[JsonProperty ("YPosition")]
		protected Nullable<System.Decimal> _yPosition;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)

		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _site_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_site_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }



		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public FloorPlanDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public FloorPlanDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public FloorPlanDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public FloorPlanDataObject Initialize(FloorPlanDataObject template, bool deepCopy)
		{
			this.SetFileValue(template.File, false, false);
			this.SetFileFileSizeValue(template.FileFileSize, false, false);
			this.SetFileInternalNameValue(template.FileInternalName, false, false);
			this.SetFloorLevelValue(template.FloorLevel, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetLengthValue(template.Length, false, false);
			this.SetNameValue(template.Name, false, false);
			this.SetOpacityValue(template.Opacity, false, false);
			this.SetRotationValue(template.Rotation, false, false);
			this.SetScaleValue(template.Scale, false, false);
			this.SetSiteIdValue(template.SiteId, false, false);
			this.SetWidthValue(template.Width, false, false);
			this.SetXPositionValue(template.XPosition, false, false);
			this.SetYPositionValue(template.YPosition, false, false);
 
 
			this._site_NewObjectId = template._site_NewObjectId;
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual FloorPlanDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual FloorPlanDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<FloorPlanDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var floorPlanSource = sourceObject as FloorPlanDataObject;

			if (ReferenceEquals(null, floorPlanSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetFileValue(floorPlanSource.File, false, false);
			this.SetFileFileSizeValue(floorPlanSource.FileFileSize, false, false);
			this.SetFileInternalNameValue(floorPlanSource.FileInternalName, false, false);
			this.SetFloorLevelValue(floorPlanSource.FloorLevel, false, false);
			this.SetIdValue(floorPlanSource.Id, false, false);
			this.SetLengthValue(floorPlanSource.Length, false, false);
			this.SetNameValue(floorPlanSource.Name, false, false);
			this.SetOpacityValue(floorPlanSource.Opacity, false, false);
			this.SetRotationValue(floorPlanSource.Rotation, false, false);
			this.SetScaleValue(floorPlanSource.Scale, false, false);
			this.SetSiteIdValue(floorPlanSource.SiteId, false, false);
			this.SetWidthValue(floorPlanSource.Width, false, false);
			this.SetXPositionValue(floorPlanSource.XPosition, false, false);
			this.SetYPositionValue(floorPlanSource.YPosition, false, false);

			this._site_NewObjectId = (sourceObject as FloorPlanDataObject)._site_NewObjectId;


			if (deepCopy)
			{
				this.ObjectsDataSet = floorPlanSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(floorPlanSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as FloorPlanDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {

			if (this._site_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._site_NewObjectId))
				{
                    this._site_NewObjectId = null;
				}
                else
				{
					this._site_NewObjectId = datasetMergingInternalIdMapping[(int) this._site_NewObjectId];
				}
			}


		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<FloorZonesDataObject> _floorZonesService => _serviceProvider.GetRequiredService<IDataProvider<FloorZonesDataObject>>();

		private readonly SemaphoreSlim __floorZonesItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __floorZonesItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "FloorZonesItems", which is a collection of FloorZonesDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of FloorZonesDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<FloorZonesDataObject>> LoadFloorZonesItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadFloorZonesItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<FloorZonesDataObject>> LoadFloorZonesItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __floorZonesItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__floorZonesItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "FloorPlanId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _floorZonesService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __floorZonesItemsAlreadyLazyLoaded = true;
                }

                return await GetFloorZonesItemsAsync(false);
            }
            finally
            {
                __floorZonesItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<FloorZonesDataObject> FloorZonesItems 
		{
			get
			{			
				return GetFloorZonesItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeFloorZonesItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("FloorPlanDataObject") && ObjectsDataSet.RelationsToInclude["FloorPlanDataObject"].Contains("FloorZonesItems");
		}

		public virtual async Task<DataObjectCollection<FloorZonesDataObject>> GetFloorZonesItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__floorZonesItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadFloorZonesItemsAsync(forceReload : forceReload);
			}
			var floorZonesItems = ObjectsDataSet.GetRelatedObjects<FloorZonesDataObject>(this, "FloorZonesItems");							
			floorZonesItems.CollectionChanged += new NotifyCollectionChangedEventHandler(FloorZonesItems_CollectionChanged);
				
			return floorZonesItems;
		}

        private void FloorZonesItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as FloorZonesDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : FloorZones", "FloorPlanDataObject.FloorZonesItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of FloorPlanDataObject throw an exception while trying to add FloorZonesDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._floorPlan_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.FloorPlanId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.FloorPlanId == default(System.Guid))
							relatedObj.FloorPlanId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as FloorZonesDataObject).FloorPlan = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<SiteDataObject> _siteService => _serviceProvider.GetRequiredService<IDataProvider<SiteDataObject>>();
      public virtual void SetSiteValue(SiteDataObject valueToSet)
		{
			SetSiteValue(valueToSet, true, true);
		}

        public virtual void SetSiteValue(SiteDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			SiteDataObject existing_site = null ;

			if ( !(ObjectsDataSet == null))
			{
				SiteDataObject key;

				if (this._site_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<SiteDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._site_NewObjectId;			
				}

				existing_site = (SiteDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_site ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Site", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "FloorPlanDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_site_NewObjectId != valueToSet.InternalObjectId)
					{
						_site_NewObjectId = valueToSet.InternalObjectId;
						_siteId = valueToSet.Id;
						OnPropertyChanged("SiteId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_siteId != valueToSet.Id)
					{
						_site_NewObjectId = null;

						_siteId = valueToSet.Id;
						OnPropertyChanged("SiteId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_siteId = Guid.Empty;
				OnPropertyChanged("SiteId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_site ,valueToSet))
				OnPropertyChanged("Site", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __siteSemaphore = new SemaphoreSlim(1, 1);
		private bool __siteAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Site", which is a SiteDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SiteDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SiteDataObject> LoadSiteAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSiteAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SiteDataObject> LoadSiteAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __siteSemaphore.WaitAsync();
			
	        try
            {
                if (!__siteAlreadyLazyLoaded || forceReload)
                {
								
					SiteDataObject site = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __siteAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						site = _serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId);
						site.IsNew = false;
						site = (SiteDataObject)ObjectsDataSet.GetObject(site);
						if (site != null)
						{
							return site;
						}
					}

					site = await _siteService.GetAsync(_serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId), parameters : parameters, skipSecurity: skipSecurity);

					SetSiteValue(site, false, false);
					__siteAlreadyLazyLoaded = true;				
		
					site = _serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId);
					site.IsNew = false;
					site = (SiteDataObject)ObjectsDataSet.GetObject(site);
                    __siteAlreadyLazyLoaded = true;
                }

                return await GetSiteAsync(false);
            }
            finally
            {
                __siteSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SiteDataObject Site 
		{
			get
			{			
				return GetSiteAsync(true).Result;
			}
			set
			{
				SetSiteValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSite()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("FloorPlanDataObject") && ObjectsDataSet.RelationsToInclude["FloorPlanDataObject"].Contains("Site");
		}

		public virtual async Task<SiteDataObject> GetSiteAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SiteDataObject site;

				
			if (_site_NewObjectId != null)
			{
				site = _serviceProvider.GetRequiredService<SiteDataObject>();
				site.IsNew = true;
				site.InternalObjectId = _site_NewObjectId;
				site = (SiteDataObject)ObjectsDataSet.GetObject(site);
			}
			else
			{
				site = _serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId);
				site.IsNew = false;
				site = (SiteDataObject)ObjectsDataSet.GetObject(site);
				
				if (allowLazyLoading && site == null && LazyLoadingEnabled && (!__siteAlreadyLazyLoaded || forceReload))
				{
					site = await LoadSiteAsync(forceReload : forceReload);
				}
			}
				
			return site;
		}

		public virtual System.Guid SiteForeignKey
		{
			get { return SiteId; }
			set 
			{	
				SiteId = value;
			}
			
		}
		

		protected IDataProvider<SiteFloorPlanDataObject> _siteFloorPlanService => _serviceProvider.GetRequiredService<IDataProvider<SiteFloorPlanDataObject>>();

		private readonly SemaphoreSlim __siteFloorPlanItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __siteFloorPlanItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SiteFloorPlanItems", which is a collection of SiteFloorPlanDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SiteFloorPlanDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SiteFloorPlanDataObject>> LoadSiteFloorPlanItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSiteFloorPlanItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SiteFloorPlanDataObject>> LoadSiteFloorPlanItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __siteFloorPlanItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__siteFloorPlanItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "FloorPlanId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _siteFloorPlanService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __siteFloorPlanItemsAlreadyLazyLoaded = true;
                }

                return await GetSiteFloorPlanItemsAsync(false);
            }
            finally
            {
                __siteFloorPlanItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SiteFloorPlanDataObject> SiteFloorPlanItems 
		{
			get
			{			
				return GetSiteFloorPlanItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSiteFloorPlanItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("FloorPlanDataObject") && ObjectsDataSet.RelationsToInclude["FloorPlanDataObject"].Contains("SiteFloorPlanItems");
		}

		public virtual async Task<DataObjectCollection<SiteFloorPlanDataObject>> GetSiteFloorPlanItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__siteFloorPlanItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadSiteFloorPlanItemsAsync(forceReload : forceReload);
			}
			var siteFloorPlanItems = ObjectsDataSet.GetRelatedObjects<SiteFloorPlanDataObject>(this, "SiteFloorPlanItems");							
			siteFloorPlanItems.CollectionChanged += new NotifyCollectionChangedEventHandler(SiteFloorPlanItems_CollectionChanged);
				
			return siteFloorPlanItems;
		}

        private void SiteFloorPlanItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SiteFloorPlanDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : SiteFloorPlan", "FloorPlanDataObject.SiteFloorPlanItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of FloorPlanDataObject throw an exception while trying to add SiteFloorPlanDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._floorPlan_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.FloorPlanId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.FloorPlanId == default(System.Guid))
							relatedObj.FloorPlanId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SiteFloorPlanDataObject).FloorPlan = null;
                    // }
                    break;
            }            
        }

		public override void ClearLazyLoadFlags()
		{
			__floorZonesItemsAlreadyLazyLoaded = false;
			__siteFloorPlanItemsAlreadyLazyLoaded = false;
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadSiteAsync()) != null)
				result.Add(Site);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadFloorZonesItemsAsync()) != null)
				result.AddRange(FloorZonesItems);
			if ((await LoadSiteFloorPlanItemsAsync()) != null)
				result.AddRange(SiteFloorPlanItems);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				Site == other ||
				(other is SiteDataObject && (SiteId != default(System.Guid)) && (SiteId == (other as SiteDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetFileValue(System.String valueToSet)
		{
			SetFileValue(valueToSet, true, true);
		}

		public virtual void SetFileValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_file != valueToSet)
			{
				_file = valueToSet;

				OnPropertyChanged("File", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The File property of the FloorPlan DataObject</summary>
        public virtual System.String File 
		{
			get	{ return _file;}
			
			
			set
			{
				SetFileValue(value);
			}
		}		
			
			
		public virtual void SetFileFileSizeValue(Nullable<System.Int32> valueToSet)
		{
			SetFileFileSizeValue(valueToSet, true, true);
		}

		public virtual void SetFileFileSizeValue(Nullable<System.Int32> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_fileFileSize != valueToSet)
			{
				_fileFileSize = valueToSet;

				OnPropertyChanged("FileFileSize", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The File File size (bytes) property of the FloorPlan DataObject</summary>
        public virtual Nullable<System.Int32> FileFileSize 
		{
			get	{ return _fileFileSize;}
			
			
			set
			{
				SetFileFileSizeValue(value);
			}
		}		
			
			
		public virtual void SetFileInternalNameValue(System.String valueToSet)
		{
			SetFileInternalNameValue(valueToSet, true, true);
		}

		public virtual void SetFileInternalNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_fileInternalName != valueToSet)
			{
				_fileInternalName = valueToSet;

				OnPropertyChanged("FileInternalName", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The File Internal Name property of the FloorPlan DataObject</summary>
        public virtual System.String FileInternalName 
		{
			get	{ return String.IsNullOrEmpty(_fileInternalName) ? null : _fileInternalName; }
			
			
			set
			{
				SetFileInternalNameValue(value);
			}
		}		
			
		
		/// <summary> The File Url property of the FloorPlan DataObject</summary>
        public virtual System.String FileUrl 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ((File != null) ? ("api/floorplan/file/" + Id.ToString() + "/File?t=" + "".ToString()) : "");				
			}
			
		}		
			
			
		public virtual void SetFloorLevelValue(System.String valueToSet)
		{
			SetFloorLevelValue(valueToSet, true, true);
		}

		public virtual void SetFloorLevelValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_floorLevel != valueToSet)
			{
				_floorLevel = valueToSet;

				OnPropertyChanged("FloorLevel", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Floor Level property of the FloorPlan DataObject</summary>
        public virtual System.String FloorLevel 
		{
			get	{ return _floorLevel; }
			
			
			set
			{
				SetFloorLevelValue(value);
			}
		}		
			
		
		/// <summary> The FloorPlanNameWithFloorLevel property of the FloorPlan DataObject</summary>
        public virtual System.String FloorPlanNameWithFloorLevel 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ("<div><p>" + Name + "- " + FloorLevel + "</p></div>");				
			}
			
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the FloorPlan DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetLengthValue(Nullable<System.Decimal> valueToSet)
		{
			SetLengthValue(valueToSet, true, true);
		}

		public virtual void SetLengthValue(Nullable<System.Decimal> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_length != (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2)))
			{
				_length = (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2));

				OnPropertyChanged("Length", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Length property of the FloorPlan DataObject</summary>
        public virtual Nullable<System.Decimal> Length 
		{
			get	{ return _length;}
			
			
			set
			{
				SetLengthValue(value);
			}
		}		
			
			
		public virtual void SetNameValue(System.String valueToSet)
		{
			SetNameValue(valueToSet, true, true);
		}

		public virtual void SetNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_name != valueToSet)
			{
				_name = valueToSet;

				OnPropertyChanged("Name", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Name property of the FloorPlan DataObject</summary>
        public virtual System.String Name 
		{
			get	{ return _name; }
			
			
			set
			{
				SetNameValue(value);
			}
		}		
			
			
		public virtual void SetOpacityValue(Nullable<System.Decimal> valueToSet)
		{
			SetOpacityValue(valueToSet, true, true);
		}

		public virtual void SetOpacityValue(Nullable<System.Decimal> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_opacity != (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2)))
			{
				_opacity = (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2));

				OnPropertyChanged("Opacity", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Opacity property of the FloorPlan DataObject</summary>
        public virtual Nullable<System.Decimal> Opacity 
		{
			get	{ return _opacity;}
			
			
			set
			{
				SetOpacityValue(value);
			}
		}		
			
			
		public virtual void SetRotationValue(Nullable<System.Decimal> valueToSet)
		{
			SetRotationValue(valueToSet, true, true);
		}

		public virtual void SetRotationValue(Nullable<System.Decimal> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_rotation != (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2)))
			{
				_rotation = (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2));

				OnPropertyChanged("Rotation", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Rotation property of the FloorPlan DataObject</summary>
        public virtual Nullable<System.Decimal> Rotation 
		{
			get	{ return _rotation;}
			
			
			set
			{
				SetRotationValue(value);
			}
		}		
			
			
		public virtual void SetScaleValue(Nullable<System.Decimal> valueToSet)
		{
			SetScaleValue(valueToSet, true, true);
		}

		public virtual void SetScaleValue(Nullable<System.Decimal> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_scale != (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2)))
			{
				_scale = (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2));

				OnPropertyChanged("Scale", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Scale property of the FloorPlan DataObject</summary>
        public virtual Nullable<System.Decimal> Scale 
		{
			get	{ return _scale;}
			
			
			set
			{
				SetScaleValue(value);
			}
		}		
			
			
		public virtual void SetSiteIdValue(System.Guid valueToSet)
		{
			SetSiteIdValue(valueToSet, true, true);
		}

		public virtual void SetSiteIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_siteId != valueToSet)
			{
				_siteId = valueToSet;

				OnPropertyChanged("SiteId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SiteId property of the FloorPlan DataObject</summary>
        public virtual System.Guid SiteId 
		{
			get	{ return _siteId;}
			
			
			set
			{
				SetSiteIdValue(value);
			}
		}		
			
			
		public virtual void SetWidthValue(Nullable<System.Decimal> valueToSet)
		{
			SetWidthValue(valueToSet, true, true);
		}

		public virtual void SetWidthValue(Nullable<System.Decimal> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_width != (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2)))
			{
				_width = (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2));

				OnPropertyChanged("Width", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Width property of the FloorPlan DataObject</summary>
        public virtual Nullable<System.Decimal> Width 
		{
			get	{ return _width;}
			
			
			set
			{
				SetWidthValue(value);
			}
		}		
			
			
		public virtual void SetXPositionValue(Nullable<System.Decimal> valueToSet)
		{
			SetXPositionValue(valueToSet, true, true);
		}

		public virtual void SetXPositionValue(Nullable<System.Decimal> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_xPosition != (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2)))
			{
				_xPosition = (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2));

				OnPropertyChanged("XPosition", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The XPosition property of the FloorPlan DataObject</summary>
        public virtual Nullable<System.Decimal> XPosition 
		{
			get	{ return _xPosition;}
			
			
			set
			{
				SetXPositionValue(value);
			}
		}		
			
			
		public virtual void SetYPositionValue(Nullable<System.Decimal> valueToSet)
		{
			SetYPositionValue(valueToSet, true, true);
		}

		public virtual void SetYPositionValue(Nullable<System.Decimal> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_yPosition != (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2)))
			{
				_yPosition = (valueToSet == null ? (decimal?) null : Math.Round((decimal)valueToSet, 2));

				OnPropertyChanged("YPosition", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The YPosition property of the FloorPlan DataObject</summary>
        public virtual Nullable<System.Decimal> YPosition 
		{
			get	{ return _yPosition;}
			
			
			set
			{
				SetYPositionValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			if (propertyName == "Name")
			{
				OnPropertyChanged("FloorPlanNameWithFloorLevel", true, dirtyHandlerOn);
			}

			if (propertyName == "FloorLevel")
			{
				OnPropertyChanged("FloorPlanNameWithFloorLevel", true, dirtyHandlerOn);
			}

			
			// Push the notification to related objects
			var _floorZonesItems = GetFloorZonesItemsAsync(false).Result;
			if (_floorZonesItems != null)
            {
                foreach (var item in _floorZonesItems)
                {
                    item.NotifyPropertyChanged(String.Concat("FloorPlan.", propertyName), callers);                    
                }
            }
			var _siteFloorPlanItems = GetSiteFloorPlanItemsAsync(false).Result;
			if (_siteFloorPlanItems != null)
            {
                foreach (var item in _siteFloorPlanItems)
                {
                    item.NotifyPropertyChanged(String.Concat("FloorPlan.", propertyName), callers);                    
                }
            }
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<FloorPlanDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is FloorPlanDataObject))
				return false;

			var p = (FloorPlanDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.File == p.File;
			fieldsComparison &= this.FileFileSize == p.FileFileSize;
			fieldsComparison &= this.FileInternalName == p.FileInternalName;
			fieldsComparison &= this.FloorLevel == p.FloorLevel;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.Length == p.Length;
			fieldsComparison &= this.Name == p.Name;
			fieldsComparison &= this.Opacity == p.Opacity;
			fieldsComparison &= this.Rotation == p.Rotation;
			fieldsComparison &= this.Scale == p.Scale;
			fieldsComparison &= this.SiteId == p.SiteId;
			fieldsComparison &= this.Width == p.Width;
			fieldsComparison &= this.XPosition == p.XPosition;
			fieldsComparison &= this.YPosition == p.YPosition;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class FloorPlanCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public FloorPlanCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public FloorPlanCollectionContainer()
		{
		}
		
		public FloorPlanCollectionContainer Construct(DataObjectCollection<FloorPlanDataObject> floorPlanItems)
        {
            if (floorPlanItems == null)
                return this;
				
			this.PrimaryKeys = floorPlanItems.Select(c => c.PrimaryKey).ToList();
            if (floorPlanItems.ObjectsDataSet == null)
            {
                floorPlanItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = floorPlanItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = floorPlanItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<FloorPlanDataObject> ExtractFloorPlanItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<FloorPlanDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<FloorPlanDataObject>(typeof(FloorPlanDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class FloorPlanContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public FloorPlanContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual FloorPlanContainer Construct(FloorPlanDataObject floorPlan, bool includeDirtyObjectsOnly = false)
		{
            if (floorPlan == null)
                return this;

			this.PrimaryKey = floorPlan.PrimaryKey;
			
            if (floorPlan.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(floorPlan);
            }

			if(floorPlan.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity FloorPlan", "Unable to set a dataset to the entity. Container may not be initialized", "FloorPlanDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : FloorPlan");
			}

			if(floorPlan.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in FloorPlanDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "FloorPlanDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in FloorPlanDataObject");
			}
			this.InternalObjectId = (int) floorPlan.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? floorPlan.ObjectsDataSet.CloneDirtyObjects() : floorPlan.ObjectsDataSet;

			return this;
		}
		
		public FloorPlanDataObject ExtractFloorPlan()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<FloorPlanDataObject>(typeof(FloorPlanDataObject), InternalObjectId);
			
			return result;
        }	
	}

}