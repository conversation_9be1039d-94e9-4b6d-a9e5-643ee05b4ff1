﻿{
  "entityName": "SlamcoreDevice",
  "entityNamePlural": "SlamcoreDevices",   "entityDescription": "This entity represents a SlamcoreDevice",
  "fields": {
    "Customer": {
        "displayName": "Customer", 
        "description": "Customer"
    },
    "CustomerId": {
        "displayName": "CustomerId", 
        "description": "Foreign Key"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "IPAddress": {
        "displayName": "IPAddress", 
        "description": "IPAddress"
    },
    "LastConnectedDateTime": {
        "displayName": "LastConnectedDateTime", 
        "description": "LastConnectedDateTime"
    },
    "Name": {
        "displayName": "Name", 
        "description": "Name"
    },
    "SerialNo": {
        "displayName": "SerialNo", 
        "description": "SerialNo"
    },
    "SlamcoreAPIKey": {
        "displayName": "SlamcoreAPIKey", 
        "description": "SlamcoreAPIKey"
    },
    "SlamcoreAPIKeyId": {
        "displayName": "SlamcoreAPIKeyId", 
        "description": "Foreign Key"
    },
    "SlamcoreAwareAuthenticationDetails": {
        "displayName": "SlamcoreAwareAuthenticationDetails", 
        "description": "SlamcoreAwareAuthenticationDetails"
    },
    "SlamcoreAwareAuthenticationDetailsId": {
        "displayName": "SlamcoreAwareAuthenticationDetailsId", 
        "description": "Foreign Key"
    },
    "SlamcoreDeviceConnectionViewItems": {
        "displayName": "Slamcore Device Connection View Items", 
        "description": "Slamcore Device Connection View"
    },
    "SlamcoreDeviceHistoryItems": {
        "displayName": "SlamcoreDeviceHistory Items", 
        "description": "SlamcoreDeviceHistory"
    },
    "SlamcorePedestrianDetectionItems": {
        "displayName": "Slamcore Pedestrian Detection Items", 
        "description": "Pedestrian Detection History Items"
    },
    "Status": {
        "displayName": "Status", 
        "description": "Status"
    },
    "UpdateRate": {
        "displayName": "UpdateRate", 
        "description": "UpdateRate"
    },
    "UpdateRateDisplay": {
        "displayName": "UpdateRateDisplay", 
        "description": "UpdateRateDisplay"
    },
    "Vehicle": {
        "displayName": "Vehicle", 
        "description": "Vehicle"
    },
    "VehicleId": {
        "displayName": "VehicleId", 
        "description": "Foreign Key"
    },
    "VehicleSlamcoreLocationHistoryItems": {
        "displayName": "VehicleSlamcoreLocationHistory Items", 
        "description": "VehicleSlamcoreLocationHistory"
    }
  }
} 