﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Storage.Common;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using System.IO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Primitives;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.CsvImportExport;
using FleetXQ.BusinessLayer.Components.Server.Import;
using FleetXQ.BusinessLayer.Tasks;
using Parameters = System.Collections.Generic.Dictionary<string, object>;

namespace FleetXQ.BusinessLayer.Components.Server
{
    public partial interface ISlamcoreDeviceExportSection0Component    
	{
		System.Threading.Tasks.Task<bool> ExportAsync(Task<ExportJobStatusDataObject> task, IExportWriter exportWriter, string exportFilePath, string exportFileName, string exportFileInternalName, string filterPredicate, string filterParameters, int startProgress = 0, int endProgress = 100);
	}
    public partial class SlamcoreDeviceExportSection0Component : ImportExportComponent<SlamcoreDeviceDataObject>, ISlamcoreDeviceExportSection0Component
    {

		public SlamcoreDeviceExportSection0Component(IServiceProvider provider, IConfiguration configuration, IApiFilterArgumentBuilder apiFilterArgumentBuilder) : base(provider, configuration)
		{
			_apiFilterArgumentBuilder = apiFilterArgumentBuilder;	
		}
		
		private readonly IApiFilterArgumentBuilder _apiFilterArgumentBuilder;	

		protected IDataFacade _dataFacade => _serviceProvider.GetRequiredService<IDataFacade>();
		protected IDataImporter _dataImporter => _serviceProvider.GetRequiredService<IDataImporter>();

        private static int _linesToSkip = 1;

 
        private static string _dateTimeFormat = "dd/MM/yyyy";
        
        public const string COL_DEVICE = "Device";
        public const string COL_IPADDRESS = "IPAddress";
        public const string COL_SERIALNO = "Serial No";
        public const string COL_LASTCONNECTEDDATE = "Last Connected Date";
        public const string COL_STATUS = "Status";


		public async System.Threading.Tasks.Task<bool> ExportAsync(
			Task<ExportJobStatusDataObject> task, 
			IExportWriter exportWriter, 
			string exportFilePath, 
			string exportFileName,
			string exportFileInternalName,
			string filterPredicate,
			string filterParameters,
			int startProgress = 0, 
			int endProgress = 100)
        {
            EnsureInitialized();
			var exportJob = task.DataObject;
			int count = 0;
            
			string exportLogFileName = $"exportlog-{exportJob.Id}.txt";
            string exportLogFileInternalName = $"exports/{Guid.NewGuid()}.txt";
			string storageContainer = _configuration["StorageContainer"] == StringValues.Empty ? "files" : _configuration["StorageContainer"];
			string exportLogPath = $"{_serviceProvider.GetRequiredService<IServicePath>().ProjectBaseURL().Trim('/')}/{storageContainer}/{exportLogFileInternalName}";

            var outputLog = new StringBuilder();
            var parameters = new Dictionary<string, object>();

			ValueWrapper<string> filterPredicateWrapper = new ValueWrapper<string>(filterPredicate);
            ValueWrapper<string> filterParametersWrapper = new ValueWrapper<string>(filterParameters);

            try
            {
                outputLog.AppendLine($"{DateTime.UtcNow} : Start Exporting data");

				int configuredExportBatchSize;
				double numberOfBatches;

				using (var outputStream = await _dataImporter.GetStorageProvider().GetFileStream(exportFilePath, FileMode.Create))
				{
					using (var writer = new CsvFileWriter(outputStream))
					{
					writer.RowSeparator = ',';

					try
					{
						await FireOnBeforeExportAsync(writer, exportFilePath, filterPredicateWrapper, filterParametersWrapper, outputLog, parameters);

						filterPredicate = filterPredicateWrapper.Value;
						filterParameters = filterParametersWrapper.Value;
					}
					catch(Exception e)
					{
						outputLog.AppendLine($"Error while calling before export : {e.Message}");
					}

					var deserializedApiFilterArguments = _apiFilterArgumentBuilder.DeserializeApiFilterArguments(filterParameters);

					count = await _dataFacade.SlamcoreDeviceDataProvider.CountAsync(null, filterPredicate, deserializedApiFilterArguments, null);

					configuredExportBatchSize = _configuration["ExportBatchSize"] == StringValues.Empty ? 1000 : Convert.ToInt32(_configuration["ExportBatchSize"]);
					numberOfBatches = Math.Ceiling(((double)count) / configuredExportBatchSize);

					var row = new DataRow();
					row.AddColumn(1, "Device");
					row.AddColumn(2, "IPAddress");
					row.AddColumn(3, "Serial No");
					row.AddColumn(4, "Last Connected Date");
					row.AddColumn(5, "Status");
 
                    // First line => headers
					row[1] = "Device";
					row[2] = "IPAddress";
					row[3] = "Serial No";
					row[4] = "Last Connected Date";
					row[5] = "Status";
 
                    writer.WriteRow(row);
					int totalLinesProcessed = 0;

                    for (int i = 0; i < numberOfBatches; i++)
                    {
                        var entities = await _dataFacade.SlamcoreDeviceDataProvider.GetCollectionAsync(
							filterPredicate: filterPredicate, 
							filterArguments: deserializedApiFilterArguments,
							pageNumber: i + 1, 
							pageSize : configuredExportBatchSize, 
							parameters: new Parameters { { "ImportContext", true } }
						);

						int lineCount = 0;

                        // Now contents
                        foreach (var entity in entities)
                        {
							totalLinesProcessed++;
							lineCount++;

							short percentCompleted = (short) (((float)totalLinesProcessed / (float)count) * (endProgress - startProgress) + startProgress);

							if (percentCompleted != exportJob.Progress)
							{
								exportJob.Progress = percentCompleted;
								exportJob = await task.SaveProgressAsync(percentCompleted);
							}

                            row.Clean();
                            var beforeDataRowEvent = await FireOnBeforeExportDataRowAsync(writer, exportFilePath, outputLog, row, entity);
	                        if (beforeDataRowEvent.SkipRow) continue;
	                        if (beforeDataRowEvent.ErrorRaised) continue;

							
							// Row 1 => Device 
							{
								row[1] = (entity.Name.ToString());
							}
							
							// Row 2 => IPAddress 
							{
								row[2] = (entity.IPAddress.ToString());
							}
							
							// Row 3 => Serial No 
							{
								row[3] = (entity.SerialNo.ToString());
							}
							
							// Row 4 => Last Connected Date 
							{
								row[4] = (entity.LastConnectedDateTime == null ? "" : ((System.DateTime)entity.LastConnectedDateTime).ToString(_dateTimeFormat));
							}
							
							// Row 5 => Status 
							{
								row[5] = (entity.Status.ToString());
							}
 
	                        var afterDataRowEvent = await FireOnAfterExportDataRowAsync(writer, exportFilePath, outputLog, row, entity);
	                        if (afterDataRowEvent.SkipRow) continue;
	                        if (afterDataRowEvent.ErrorRaised) continue;
	                        
                            writer.WriteRow(row);
                        }
                    }
					try
					{
						await FireOnAfterExportAsync(writer, exportFilePath, outputLog, parameters);
					}
					catch(Exception e)
					{
						outputLog.AppendLine($"Error while calling after export : {e.Message}");
					}
                }
					exportJob.ExportedFile = exportFileName;
					exportJob.ExportedFileInternalName = exportFileInternalName;
					// not supported by azure storage.. find another way
					//exportJob.ExportedFileFileSize =(int) outputStream.Length;
				}
              outputLog.AppendLine($"{DateTime.UtcNow} : End Exporting Demandes - {count} Demandes exported");
            }
            catch (Exception e)
            {
                outputLog.AppendLine($"Error while exporting demandes : {e.Message} ");
                if (exportJob != null)
                    exportJob.TaskStatus = GOTaskStatusEnum.Failed;

				return false;
            }
            finally
            {
				using (StreamWriter logfile = new StreamWriter(await _dataImporter.GetStorageProvider().GetFileStream(exportLogPath, FileMode.Create)))
                {
                    logfile.Write(outputLog.ToString());
                    logfile.Flush();
					exportJob.ExportLogFile = exportLogFileName;
					exportJob.ExportLogFileInternalName = exportLogFileInternalName;
					// not supported by azure storage.. find another way
                    //exportJob.ExportLogFileFileSize = (int) logfile.BaseStream.Length;
                }
			}
		
			return true;
		}	
	}
 
	public partial class SlamcoreDeviceExportComponent : BaseServerComponent, ISlamcoreDeviceExportComponent 
	{
		public SlamcoreDeviceExportComponent(IServiceProvider provider, IConfiguration configuration, IDataFacade dataFacade) : base(provider, configuration, dataFacade)
		{
		}

		protected IDataImporter _dataImporter => _serviceProvider.GetRequiredService<IDataImporter>();
 
        public System.Threading.Tasks.Task Export_OnInitTaskAsync(ExportJobStatusDataObject taskObject, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
			return System.Threading.Tasks.Task.CompletedTask;
        }
	
		/// Generated asynchronous version of the API
		public async System.Threading.Tasks.Task ExportAsync(Task<ExportJobStatusDataObject> task, System.String filterPredicate, System.String filterParameters, Dictionary<string, object> parameters = null)
		{	
			int startProgress;
			int endProgress;

			var exportJob = task.DataObject;

			string exportFileName = String.Format("SlamcoreDeviceExportFile-{0}.csv", exportJob.Id);
            string exportFileInternalName = $"exports/{Guid.NewGuid()}.csv";
			string storageContainer = _configuration["StorageContainer"] == StringValues.Empty ? "files" : _configuration["StorageContainer"];
            string exportFilePath = $"{_serviceProvider.GetRequiredService<IServicePath>().ProjectBaseURL().Trim('/')}/{storageContainer}/{exportFileInternalName}";

			{
				
				exportFileInternalName = $"exports/{Guid.NewGuid()}.csv";
				storageContainer = _configuration["StorageContainer"] == StringValues.Empty ? "files" : _configuration["StorageContainer"];
				exportFilePath = $"{_serviceProvider.GetRequiredService<IServicePath>().ProjectBaseURL().Trim('/')}/{storageContainer}/{exportFileInternalName}";
				startProgress = 0;
				endProgress = 100;
				var section0ExportComponent = _serviceProvider.GetRequiredService<ISlamcoreDeviceExportSection0Component>();
	            await section0ExportComponent.ExportAsync(task, null, exportFilePath, exportFileName, exportFileInternalName, filterPredicate, filterParameters, startProgress, endProgress);
			
			}

            exportJob = task.DataObject;
			exportJob.End = DateTime.UtcNow;

			// GRD-462 Don't overwrite fail export result with success
			if (exportJob.TaskStatus != GOTaskStatusEnum.Failed)
			{
				exportJob.TaskStatus = GOTaskStatusEnum.Complete;
				exportJob.Progress = 100;
			}

			exportJob = await task.SaveProgressAsync(100);       
		}
	}
}
