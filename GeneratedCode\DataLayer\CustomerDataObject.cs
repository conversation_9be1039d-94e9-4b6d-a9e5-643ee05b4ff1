﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'Customer'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class CustomerDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("Active")]
		protected System.Boolean _active;
		[JsonProperty ("Addess")]
		protected System.String _addess;
		[JsonProperty ("CompanyName")]
		protected System.String _companyName;
		[JsonProperty ("ConnectionString")]
		protected System.String _connectionString;
		[JsonProperty ("ContactNumber")]
		protected System.String _contactNumber;
		[JsonProperty ("ContactPersonInformationId")]
		protected Nullable<System.Guid> _contactPersonInformationId;
		[JsonProperty ("ContractDate")]
		protected Nullable<System.DateTime> _contractDate;
		[JsonProperty("ContractDate_WithTimezoneOffset")]
		protected System.DateTimeOffset? _contractDate_WithTimezoneOffset;
		[JsonProperty ("ContractNumber")]
		protected System.String _contractNumber;
		[JsonProperty ("CountryId")]
		protected System.Guid _countryId;
		[JsonProperty ("CustomerFeatureSubscriptionId")]
		protected Nullable<System.Guid> _customerFeatureSubscriptionId;
		[JsonProperty ("CustomerLogo")]
		protected System.String _customerLogo;
		[JsonProperty ("CustomerLogoFileSize")]
		protected Nullable<System.Int32> _customerLogoFileSize;
		[JsonProperty ("CustomerLogoInternalName")]
		protected System.String _customerLogoInternalName;
		[JsonProperty ("DealerCustomer")]
		protected Nullable<System.Boolean> _dealerCustomer;
		[JsonProperty ("DealerId")]
		protected System.Guid _dealerId;
		[JsonProperty ("DeletedAtUtc")]
		protected Nullable<System.DateTime> _deletedAtUtc;
		[JsonProperty("DeletedAtUtc_WithTimezoneOffset")]
		protected System.DateTimeOffset? _deletedAtUtc_WithTimezoneOffset;
		[JsonProperty ("Description")]
		protected System.String _description;
		[JsonProperty ("Email")]
		protected System.String _email;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("PreferredLocale")]
		protected Nullable<LocaleEnum> _preferredLocale;
		[JsonProperty ("PreferredLocaleString")]
		protected System.String _preferredLocaleString;
		[JsonProperty ("Prefix")]
		protected System.String _prefix;
		[JsonProperty ("SitesCount")]
		protected Nullable<System.Int16> _sitesCount;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _contactPersonInformation_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_contactPersonInformation_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _country_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_country_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }





		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _customerFeatureSubscription_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_customerFeatureSubscription_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }













		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _dealer_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_dealer_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }























		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public CustomerDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
			SetDealerCustomerValue(false, false, false);
			SetCustomerLogoInternalNameValue("no-image.jpg", false, false);
			SetCustomerLogoValue("no-image.jpg", false, false);
			SetActiveValue(true, false, false);
			SetPrefixValue("cii", false, false);
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public CustomerDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public CustomerDataObject Initialize(System.Guid id)
		{
			this._id = id;
			SetDealerCustomerValue(false, false, false);
			SetCustomerLogoInternalNameValue("no-image.jpg", false, false);
			SetCustomerLogoValue("no-image.jpg", false, false);
			SetActiveValue(true, false, false);
			SetPrefixValue("cii", false, false);
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public CustomerDataObject Initialize(CustomerDataObject template, bool deepCopy)
		{
			this.SetContractDateValue(template.ContractDate, false, false);
			this._contractDate_WithTimezoneOffset = template._contractDate_WithTimezoneOffset;
			this.SetDeletedAtUtcValue(template.DeletedAtUtc, false, false);
			this._deletedAtUtc_WithTimezoneOffset = template._deletedAtUtc_WithTimezoneOffset;
			this.SetActiveValue(template.Active, false, false);
			this.SetAddessValue(template.Addess, false, false);
			this.SetCompanyNameValue(template.CompanyName, false, false);
			this.SetConnectionStringValue(template.ConnectionString, false, false);
			this.SetContactNumberValue(template.ContactNumber, false, false);
			this.SetContactPersonInformationIdValue(template.ContactPersonInformationId, false, false);
			this.SetContractNumberValue(template.ContractNumber, false, false);
			this.SetCountryIdValue(template.CountryId, false, false);
			this.SetCustomerFeatureSubscriptionIdValue(template.CustomerFeatureSubscriptionId, false, false);
			this.SetCustomerLogoValue(template.CustomerLogo, false, false);
			this.SetCustomerLogoFileSizeValue(template.CustomerLogoFileSize, false, false);
			this.SetCustomerLogoInternalNameValue(template.CustomerLogoInternalName, false, false);
			this.SetDealerCustomerValue(template.DealerCustomer, false, false);
			this.SetDealerIdValue(template.DealerId, false, false);
			this.SetDescriptionValue(template.Description, false, false);
			this.SetEmailValue(template.Email, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetPreferredLocaleValue(template.PreferredLocale, false, false);
			this.SetPreferredLocaleStringValue(template.PreferredLocaleString, false, false);
			this.SetPrefixValue(template.Prefix, false, false);
			this.SetSitesCountValue(template.SitesCount, false, false);
 
 
 
			this._contactPersonInformation_NewObjectId = template._contactPersonInformation_NewObjectId;
 
			this._country_NewObjectId = template._country_NewObjectId;
 
 
 
 
			this._customerFeatureSubscription_NewObjectId = template._customerFeatureSubscription_NewObjectId;
 
 
 
 
 
 
 
 
 
 
 
 
			this._dealer_NewObjectId = template._dealer_NewObjectId;
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual CustomerDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual CustomerDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var customerSource = sourceObject as CustomerDataObject;

			if (ReferenceEquals(null, customerSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetActiveValue(customerSource.Active, false, false);
			this.SetAddessValue(customerSource.Addess, false, false);
			this.SetCompanyNameValue(customerSource.CompanyName, false, false);
			this.SetConnectionStringValue(customerSource.ConnectionString, false, false);
			this.SetContactNumberValue(customerSource.ContactNumber, false, false);
			this.SetContactPersonInformationIdValue(customerSource.ContactPersonInformationId, false, false);
			this.SetContractDateValue(customerSource.ContractDate, false, false);
			this.SetContractNumberValue(customerSource.ContractNumber, false, false);
			this.SetCountryIdValue(customerSource.CountryId, false, false);
			this.SetCustomerFeatureSubscriptionIdValue(customerSource.CustomerFeatureSubscriptionId, false, false);
			this.SetCustomerLogoValue(customerSource.CustomerLogo, false, false);
			this.SetCustomerLogoFileSizeValue(customerSource.CustomerLogoFileSize, false, false);
			this.SetCustomerLogoInternalNameValue(customerSource.CustomerLogoInternalName, false, false);
			this.SetDealerCustomerValue(customerSource.DealerCustomer, false, false);
			this.SetDealerIdValue(customerSource.DealerId, false, false);
			this.SetDeletedAtUtcValue(customerSource.DeletedAtUtc, false, false);
			this.SetDescriptionValue(customerSource.Description, false, false);
			this.SetEmailValue(customerSource.Email, false, false);
			this.SetIdValue(customerSource.Id, false, false);
			this.SetPreferredLocaleValue(customerSource.PreferredLocale, false, false);
			this.SetPreferredLocaleStringValue(customerSource.PreferredLocaleString, false, false);
			this.SetPrefixValue(customerSource.Prefix, false, false);
			this.SetSitesCountValue(customerSource.SitesCount, false, false);


			this._contactPersonInformation_NewObjectId = (sourceObject as CustomerDataObject)._contactPersonInformation_NewObjectId;

			this._country_NewObjectId = (sourceObject as CustomerDataObject)._country_NewObjectId;




			this._customerFeatureSubscription_NewObjectId = (sourceObject as CustomerDataObject)._customerFeatureSubscription_NewObjectId;












			this._dealer_NewObjectId = (sourceObject as CustomerDataObject)._dealer_NewObjectId;






















			if (deepCopy)
			{
				this.ObjectsDataSet = customerSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(customerSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as CustomerDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {


			if (this._contactPersonInformation_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._contactPersonInformation_NewObjectId))
				{
                    this._contactPersonInformation_NewObjectId = null;
				}
                else
				{
					this._contactPersonInformation_NewObjectId = datasetMergingInternalIdMapping[(int) this._contactPersonInformation_NewObjectId];
				}
			}

			if (this._country_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._country_NewObjectId))
				{
                    this._country_NewObjectId = null;
				}
                else
				{
					this._country_NewObjectId = datasetMergingInternalIdMapping[(int) this._country_NewObjectId];
				}
			}




			if (this._customerFeatureSubscription_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._customerFeatureSubscription_NewObjectId))
				{
                    this._customerFeatureSubscription_NewObjectId = null;
				}
                else
				{
					this._customerFeatureSubscription_NewObjectId = datasetMergingInternalIdMapping[(int) this._customerFeatureSubscription_NewObjectId];
				}
			}












			if (this._dealer_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._dealer_NewObjectId))
				{
                    this._dealer_NewObjectId = null;
				}
                else
				{
					this._dealer_NewObjectId = datasetMergingInternalIdMapping[(int) this._dealer_NewObjectId];
				}
			}






















		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<AccessGroupDataObject> _accessGroupService => _serviceProvider.GetRequiredService<IDataProvider<AccessGroupDataObject>>();

		private readonly SemaphoreSlim __accessGroupItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __accessGroupItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "AccessGroupItems", which is a collection of AccessGroupDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of AccessGroupDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<AccessGroupDataObject>> LoadAccessGroupItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadAccessGroupItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<AccessGroupDataObject>> LoadAccessGroupItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __accessGroupItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__accessGroupItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _accessGroupService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __accessGroupItemsAlreadyLazyLoaded = true;
                }

                return await GetAccessGroupItemsAsync(false);
            }
            finally
            {
                __accessGroupItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<AccessGroupDataObject> AccessGroupItems 
		{
			get
			{			
				return GetAccessGroupItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeAccessGroupItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("AccessGroupItems");
		}

		public virtual async Task<DataObjectCollection<AccessGroupDataObject>> GetAccessGroupItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__accessGroupItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadAccessGroupItemsAsync(forceReload : forceReload);
			}
			var accessGroupItems = ObjectsDataSet.GetRelatedObjects<AccessGroupDataObject>(this, "AccessGroupItems");							
			accessGroupItems.CollectionChanged += new NotifyCollectionChangedEventHandler(AccessGroupItems_CollectionChanged);
				
			return accessGroupItems;
		}

        private void AccessGroupItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as AccessGroupDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : AccessGroup", "CustomerDataObject.AccessGroupItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add AccessGroupDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as AccessGroupDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ChecklistStatusViewDataObject> _checklistStatusViewService => _serviceProvider.GetRequiredService<IDataProvider<ChecklistStatusViewDataObject>>();

		private readonly SemaphoreSlim __checklistStatusViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __checklistStatusViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ChecklistStatusViewItems", which is a collection of ChecklistStatusViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ChecklistStatusViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ChecklistStatusViewDataObject>> LoadChecklistStatusViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadChecklistStatusViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ChecklistStatusViewDataObject>> LoadChecklistStatusViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __checklistStatusViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__checklistStatusViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _checklistStatusViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __checklistStatusViewItemsAlreadyLazyLoaded = true;
                }

                return await GetChecklistStatusViewItemsAsync(false);
            }
            finally
            {
                __checklistStatusViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ChecklistStatusViewDataObject> ChecklistStatusViewItems 
		{
			get
			{			
				return GetChecklistStatusViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeChecklistStatusViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("ChecklistStatusViewItems");
		}

		public virtual async Task<DataObjectCollection<ChecklistStatusViewDataObject>> GetChecklistStatusViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__checklistStatusViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadChecklistStatusViewItemsAsync(forceReload : forceReload);
			}
			var checklistStatusViewItems = ObjectsDataSet.GetRelatedObjects<ChecklistStatusViewDataObject>(this, "ChecklistStatusViewItems");							
			checklistStatusViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ChecklistStatusViewItems_CollectionChanged);
				
			return checklistStatusViewItems;
		}

        private void ChecklistStatusViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ChecklistStatusViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ChecklistStatusView", "CustomerDataObject.ChecklistStatusViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add ChecklistStatusViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ChecklistStatusViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ContactPersonInformationDataObject> _contactPersonInformationService => _serviceProvider.GetRequiredService<IDataProvider<ContactPersonInformationDataObject>>();
      public virtual void SetContactPersonInformationValue(ContactPersonInformationDataObject valueToSet)
		{
			SetContactPersonInformationValue(valueToSet, true, true);
		}

        public virtual void SetContactPersonInformationValue(ContactPersonInformationDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			ContactPersonInformationDataObject existing_contactPersonInformation = null ;

			if ( !(this.ContactPersonInformationId == null || ObjectsDataSet == null))
			{
				ContactPersonInformationDataObject key;

				if (this._contactPersonInformation_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<ContactPersonInformationDataObject>().Initialize((System.Guid)this.ContactPersonInformationId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<ContactPersonInformationDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._contactPersonInformation_NewObjectId;			
				}

				existing_contactPersonInformation = (ContactPersonInformationDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_contactPersonInformation ,valueToSet))
            {
                if (valueToSet == null)
                {
					_contactPersonInformation_NewObjectId = null;
					_contactPersonInformationId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("ContactPersonInformation", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "CustomerDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_contactPersonInformation_NewObjectId != valueToSet.InternalObjectId)
					{
						_contactPersonInformation_NewObjectId = valueToSet.InternalObjectId;
						_contactPersonInformationId = valueToSet.Id;
						OnPropertyChanged("ContactPersonInformationId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_contactPersonInformationId != valueToSet.Id)
					{
						_contactPersonInformation_NewObjectId = null;

						_contactPersonInformationId = valueToSet.Id;
						OnPropertyChanged("ContactPersonInformationId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_contactPersonInformation_NewObjectId = null;
					_contactPersonInformationId = null;
					
				OnPropertyChanged("ContactPersonInformationId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_contactPersonInformation ,valueToSet))
				OnPropertyChanged("ContactPersonInformation", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __contactPersonInformationSemaphore = new SemaphoreSlim(1, 1);
		private bool __contactPersonInformationAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ContactPersonInformation", which is a ContactPersonInformationDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a ContactPersonInformationDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<ContactPersonInformationDataObject> LoadContactPersonInformationAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadContactPersonInformationAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<ContactPersonInformationDataObject> LoadContactPersonInformationAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __contactPersonInformationSemaphore.WaitAsync();
			
	        try
            {
                if (!__contactPersonInformationAlreadyLazyLoaded || forceReload)
                {
								
					if (this.ContactPersonInformationId == null)
					{
						return null;
					}
				
					ContactPersonInformationDataObject contactPersonInformation = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __contactPersonInformationAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						contactPersonInformation = _serviceProvider.GetRequiredService<ContactPersonInformationDataObject>().Initialize((System.Guid)this.ContactPersonInformationId);
						contactPersonInformation.IsNew = false;
						contactPersonInformation = (ContactPersonInformationDataObject)ObjectsDataSet.GetObject(contactPersonInformation);
						if (contactPersonInformation != null)
						{
							return contactPersonInformation;
						}
					}

					contactPersonInformation = await _contactPersonInformationService.GetAsync(_serviceProvider.GetRequiredService<ContactPersonInformationDataObject>().Initialize((System.Guid)this.ContactPersonInformationId), parameters : parameters, skipSecurity: skipSecurity);

					SetContactPersonInformationValue(contactPersonInformation, false, false);
					__contactPersonInformationAlreadyLazyLoaded = true;				
		
					contactPersonInformation = _serviceProvider.GetRequiredService<ContactPersonInformationDataObject>().Initialize((System.Guid)this.ContactPersonInformationId);
					contactPersonInformation.IsNew = false;
					contactPersonInformation = (ContactPersonInformationDataObject)ObjectsDataSet.GetObject(contactPersonInformation);
                    __contactPersonInformationAlreadyLazyLoaded = true;
                }

                return await GetContactPersonInformationAsync(false);
            }
            finally
            {
                __contactPersonInformationSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual ContactPersonInformationDataObject ContactPersonInformation 
		{
			get
			{			
				return GetContactPersonInformationAsync(true).Result;
			}
			set
			{
				SetContactPersonInformationValue(value);
			}
		}
		
		public virtual bool ShouldSerializeContactPersonInformation()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("ContactPersonInformation");
		}

		public virtual async Task<ContactPersonInformationDataObject> GetContactPersonInformationAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			ContactPersonInformationDataObject contactPersonInformation;

				
			if (_contactPersonInformation_NewObjectId != null)
			{
				contactPersonInformation = _serviceProvider.GetRequiredService<ContactPersonInformationDataObject>();
				contactPersonInformation.IsNew = true;
				contactPersonInformation.InternalObjectId = _contactPersonInformation_NewObjectId;
				contactPersonInformation = (ContactPersonInformationDataObject)ObjectsDataSet.GetObject(contactPersonInformation);
			}
			else
			{
				if (this.ContactPersonInformationId == null)
					return null;
				if (ContactPersonInformationId == null)
					contactPersonInformation = null;
				else
				contactPersonInformation = _serviceProvider.GetRequiredService<ContactPersonInformationDataObject>().Initialize((System.Guid)this.ContactPersonInformationId);
				contactPersonInformation.IsNew = false;
				contactPersonInformation = (ContactPersonInformationDataObject)ObjectsDataSet.GetObject(contactPersonInformation);
				
				if (allowLazyLoading && contactPersonInformation == null && LazyLoadingEnabled && (!__contactPersonInformationAlreadyLazyLoaded || forceReload))
				{
					contactPersonInformation = await LoadContactPersonInformationAsync(forceReload : forceReload);
				}
			}
				
			return contactPersonInformation;
		}

		public virtual Nullable<System.Guid> ContactPersonInformationForeignKey
		{
			get { return ContactPersonInformationId; }
			set 
			{	
				ContactPersonInformationId = value;
			}
			
		}
		

		protected IDataProvider<CountryDataObject> _countryService => _serviceProvider.GetRequiredService<IDataProvider<CountryDataObject>>();
      public virtual void SetCountryValue(CountryDataObject valueToSet)
		{
			SetCountryValue(valueToSet, true, true);
		}

        public virtual void SetCountryValue(CountryDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			CountryDataObject existing_country = null ;

			if ( !(ObjectsDataSet == null))
			{
				CountryDataObject key;

				if (this._country_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<CountryDataObject>().Initialize(this.CountryId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<CountryDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._country_NewObjectId;			
				}

				existing_country = (CountryDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_country ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Country", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "CustomerDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_country_NewObjectId != valueToSet.InternalObjectId)
					{
						_country_NewObjectId = valueToSet.InternalObjectId;
						_countryId = valueToSet.Id;
						OnPropertyChanged("CountryId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_countryId != valueToSet.Id)
					{
						_country_NewObjectId = null;

						_countryId = valueToSet.Id;
						OnPropertyChanged("CountryId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_countryId = Guid.Empty;
				OnPropertyChanged("CountryId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_country ,valueToSet))
				OnPropertyChanged("Country", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __countrySemaphore = new SemaphoreSlim(1, 1);
		private bool __countryAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Country", which is a CountryDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a CountryDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<CountryDataObject> LoadCountryAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCountryAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<CountryDataObject> LoadCountryAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __countrySemaphore.WaitAsync();
			
	        try
            {
                if (!__countryAlreadyLazyLoaded || forceReload)
                {
								
					CountryDataObject country = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __countryAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						country = _serviceProvider.GetRequiredService<CountryDataObject>().Initialize(this.CountryId);
						country.IsNew = false;
						country = (CountryDataObject)ObjectsDataSet.GetObject(country);
						if (country != null)
						{
							return country;
						}
					}

					country = await _countryService.GetAsync(_serviceProvider.GetRequiredService<CountryDataObject>().Initialize(this.CountryId), parameters : parameters, skipSecurity: skipSecurity);

					SetCountryValue(country, false, false);
					__countryAlreadyLazyLoaded = true;				
		
					country = _serviceProvider.GetRequiredService<CountryDataObject>().Initialize(this.CountryId);
					country.IsNew = false;
					country = (CountryDataObject)ObjectsDataSet.GetObject(country);
                    __countryAlreadyLazyLoaded = true;
                }

                return await GetCountryAsync(false);
            }
            finally
            {
                __countrySemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual CountryDataObject Country 
		{
			get
			{			
				return GetCountryAsync(true).Result;
			}
			set
			{
				SetCountryValue(value);
			}
		}
		
		public virtual bool ShouldSerializeCountry()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("Country");
		}

		public virtual async Task<CountryDataObject> GetCountryAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			CountryDataObject country;

				
			if (_country_NewObjectId != null)
			{
				country = _serviceProvider.GetRequiredService<CountryDataObject>();
				country.IsNew = true;
				country.InternalObjectId = _country_NewObjectId;
				country = (CountryDataObject)ObjectsDataSet.GetObject(country);
			}
			else
			{
				country = _serviceProvider.GetRequiredService<CountryDataObject>().Initialize(this.CountryId);
				country.IsNew = false;
				country = (CountryDataObject)ObjectsDataSet.GetObject(country);
				
				if (allowLazyLoading && country == null && LazyLoadingEnabled && (!__countryAlreadyLazyLoaded || forceReload))
				{
					country = await LoadCountryAsync(forceReload : forceReload);
				}
			}
				
			return country;
		}

		public virtual System.Guid CountryForeignKey
		{
			get { return CountryId; }
			set 
			{	
				CountryId = value;
			}
			
		}
		

		protected IDataProvider<CurrentDriverStatusChartViewDataObject> _currentDriverStatusChartViewService => _serviceProvider.GetRequiredService<IDataProvider<CurrentDriverStatusChartViewDataObject>>();

		private readonly SemaphoreSlim __currentDriverStatusChartViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __currentDriverStatusChartViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CurrentDriverStatusChartViewItems", which is a collection of CurrentDriverStatusChartViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CurrentDriverStatusChartViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CurrentDriverStatusChartViewDataObject>> LoadCurrentDriverStatusChartViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCurrentDriverStatusChartViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CurrentDriverStatusChartViewDataObject>> LoadCurrentDriverStatusChartViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __currentDriverStatusChartViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__currentDriverStatusChartViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _currentDriverStatusChartViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __currentDriverStatusChartViewItemsAlreadyLazyLoaded = true;
                }

                return await GetCurrentDriverStatusChartViewItemsAsync(false);
            }
            finally
            {
                __currentDriverStatusChartViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CurrentDriverStatusChartViewDataObject> CurrentDriverStatusChartViewItems 
		{
			get
			{			
				return GetCurrentDriverStatusChartViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCurrentDriverStatusChartViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CurrentDriverStatusChartViewItems");
		}

		public virtual async Task<DataObjectCollection<CurrentDriverStatusChartViewDataObject>> GetCurrentDriverStatusChartViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__currentDriverStatusChartViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCurrentDriverStatusChartViewItemsAsync(forceReload : forceReload);
			}
			var currentDriverStatusChartViewItems = ObjectsDataSet.GetRelatedObjects<CurrentDriverStatusChartViewDataObject>(this, "CurrentDriverStatusChartViewItems");							
			currentDriverStatusChartViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CurrentDriverStatusChartViewItems_CollectionChanged);
				
			return currentDriverStatusChartViewItems;
		}

        private void CurrentDriverStatusChartViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CurrentDriverStatusChartViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CurrentDriverStatusChartView", "CustomerDataObject.CurrentDriverStatusChartViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add CurrentDriverStatusChartViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CurrentDriverStatusChartViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CurrentVehicleStatusChartViewDataObject> _currentVehicleStatusChartViewService => _serviceProvider.GetRequiredService<IDataProvider<CurrentVehicleStatusChartViewDataObject>>();

		private readonly SemaphoreSlim __currentVehicleStatusChartViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __currentVehicleStatusChartViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CurrentVehicleStatusChartViewItems", which is a collection of CurrentVehicleStatusChartViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CurrentVehicleStatusChartViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CurrentVehicleStatusChartViewDataObject>> LoadCurrentVehicleStatusChartViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCurrentVehicleStatusChartViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CurrentVehicleStatusChartViewDataObject>> LoadCurrentVehicleStatusChartViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __currentVehicleStatusChartViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__currentVehicleStatusChartViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _currentVehicleStatusChartViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __currentVehicleStatusChartViewItemsAlreadyLazyLoaded = true;
                }

                return await GetCurrentVehicleStatusChartViewItemsAsync(false);
            }
            finally
            {
                __currentVehicleStatusChartViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CurrentVehicleStatusChartViewDataObject> CurrentVehicleStatusChartViewItems 
		{
			get
			{			
				return GetCurrentVehicleStatusChartViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCurrentVehicleStatusChartViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CurrentVehicleStatusChartViewItems");
		}

		public virtual async Task<DataObjectCollection<CurrentVehicleStatusChartViewDataObject>> GetCurrentVehicleStatusChartViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__currentVehicleStatusChartViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCurrentVehicleStatusChartViewItemsAsync(forceReload : forceReload);
			}
			var currentVehicleStatusChartViewItems = ObjectsDataSet.GetRelatedObjects<CurrentVehicleStatusChartViewDataObject>(this, "CurrentVehicleStatusChartViewItems");							
			currentVehicleStatusChartViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CurrentVehicleStatusChartViewItems_CollectionChanged);
				
			return currentVehicleStatusChartViewItems;
		}

        private void CurrentVehicleStatusChartViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CurrentVehicleStatusChartViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CurrentVehicleStatusChartView", "CustomerDataObject.CurrentVehicleStatusChartViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add CurrentVehicleStatusChartViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CurrentVehicleStatusChartViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CustomerAuditDataObject> _customerAuditService => _serviceProvider.GetRequiredService<IDataProvider<CustomerAuditDataObject>>();
      public virtual void SetCustomerAuditValue(CustomerAuditDataObject valueToSet)
		{
			SetCustomerAuditValue(valueToSet, true, true);
		}

        public virtual void SetCustomerAuditValue(CustomerAuditDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<CustomerAuditDataObject>(this, "CustomerAudit");
			var existing_customerAudit = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("CustomerAudit", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._customer_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.Customer = this;
					valueToSet.fkCustomerId = this.Id;
				}			
			}
			else  if (existing_customerAudit != null)
            {
                ObjectsDataSet.RemoveObject(existing_customerAudit);
            }
			if (!ReferenceEquals(existing_customerAudit ,valueToSet))
				OnPropertyChanged("CustomerAudit", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __customerAuditSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerAuditAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerAudit", which is a CustomerAuditDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a CustomerAuditDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<CustomerAuditDataObject> LoadCustomerAuditAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerAuditAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<CustomerAuditDataObject> LoadCustomerAuditAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerAuditSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerAuditAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data CustomerAudit for the current entity. The DataObjects doesn't have an ObjectsDataSet", "CustomerObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var customerAudit = (this.ObjectsDataSet as ObjectsDataSet).CustomerAuditObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).CustomerAuditObjects.Where(item => item.Value.fkCustomerId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(customerAudit, null))
					{
						var filterPredicate = "fkCustomerId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						customerAudit = (await _customerAuditService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetCustomerAuditValue(customerAudit, false, false);
						__customerAuditAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a customerAudit, but relation field not set, encourage it to get set by removing and re-adding the customerAudit 
					if (customerAudit != null && this.CustomerAudit == null)
					{
						this.ObjectsDataSet.RemoveObject(customerAudit);
						this.ObjectsDataSet.AddObject(customerAudit);
					}			
                    __customerAuditAlreadyLazyLoaded = true;
                }

                return await GetCustomerAuditAsync(false);
            }
            finally
            {
                __customerAuditSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual CustomerAuditDataObject CustomerAudit 
		{
			get
			{			
				return GetCustomerAuditAsync(true).Result;
			}
			set
			{
				SetCustomerAuditValue(value);
			}
		}
		
		public virtual bool ShouldSerializeCustomerAudit()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CustomerAudit");
		}

		public virtual async Task<CustomerAuditDataObject> GetCustomerAuditAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			CustomerAuditDataObject customerAudit;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<CustomerAuditDataObject>(this, "CustomerAudit");
               	customerAudit = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && customerAudit == null && LazyLoadingEnabled && (!__customerAuditAlreadyLazyLoaded || forceReload))
				{
					customerAudit = await LoadCustomerAuditAsync(forceReload : forceReload);
				}
			}
				
			return customerAudit;
		}


		protected IDataProvider<CustomerFeatureSubscriptionDataObject> _customerFeatureSubscriptionService => _serviceProvider.GetRequiredService<IDataProvider<CustomerFeatureSubscriptionDataObject>>();
      public virtual void SetCustomerFeatureSubscriptionValue(CustomerFeatureSubscriptionDataObject valueToSet)
		{
			SetCustomerFeatureSubscriptionValue(valueToSet, true, true);
		}

        public virtual void SetCustomerFeatureSubscriptionValue(CustomerFeatureSubscriptionDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			CustomerFeatureSubscriptionDataObject existing_customerFeatureSubscription = null ;

			if ( !(this.CustomerFeatureSubscriptionId == null || ObjectsDataSet == null))
			{
				CustomerFeatureSubscriptionDataObject key;

				if (this._customerFeatureSubscription_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize((System.Guid)this.CustomerFeatureSubscriptionId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._customerFeatureSubscription_NewObjectId;			
				}

				existing_customerFeatureSubscription = (CustomerFeatureSubscriptionDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_customerFeatureSubscription ,valueToSet))
            {
                if (valueToSet == null)
                {
					_customerFeatureSubscription_NewObjectId = null;
					_customerFeatureSubscriptionId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("CustomerFeatureSubscription", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "CustomerDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_customerFeatureSubscription_NewObjectId != valueToSet.InternalObjectId)
					{
						_customerFeatureSubscription_NewObjectId = valueToSet.InternalObjectId;
						_customerFeatureSubscriptionId = valueToSet.Id;
						OnPropertyChanged("CustomerFeatureSubscriptionId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_customerFeatureSubscriptionId != valueToSet.Id)
					{
						_customerFeatureSubscription_NewObjectId = null;

						_customerFeatureSubscriptionId = valueToSet.Id;
						OnPropertyChanged("CustomerFeatureSubscriptionId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_customerFeatureSubscription_NewObjectId = null;
					_customerFeatureSubscriptionId = null;
					
				OnPropertyChanged("CustomerFeatureSubscriptionId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_customerFeatureSubscription ,valueToSet))
				OnPropertyChanged("CustomerFeatureSubscription", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __customerFeatureSubscriptionSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerFeatureSubscriptionAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerFeatureSubscription", which is a CustomerFeatureSubscriptionDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a CustomerFeatureSubscriptionDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<CustomerFeatureSubscriptionDataObject> LoadCustomerFeatureSubscriptionAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerFeatureSubscriptionAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<CustomerFeatureSubscriptionDataObject> LoadCustomerFeatureSubscriptionAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerFeatureSubscriptionSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerFeatureSubscriptionAlreadyLazyLoaded || forceReload)
                {
								
					if (this.CustomerFeatureSubscriptionId == null)
					{
						return null;
					}
				
					CustomerFeatureSubscriptionDataObject customerFeatureSubscription = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __customerFeatureSubscriptionAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						customerFeatureSubscription = _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize((System.Guid)this.CustomerFeatureSubscriptionId);
						customerFeatureSubscription.IsNew = false;
						customerFeatureSubscription = (CustomerFeatureSubscriptionDataObject)ObjectsDataSet.GetObject(customerFeatureSubscription);
						if (customerFeatureSubscription != null)
						{
							return customerFeatureSubscription;
						}
					}

					customerFeatureSubscription = await _customerFeatureSubscriptionService.GetAsync(_serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize((System.Guid)this.CustomerFeatureSubscriptionId), parameters : parameters, skipSecurity: skipSecurity);

					SetCustomerFeatureSubscriptionValue(customerFeatureSubscription, false, false);
					__customerFeatureSubscriptionAlreadyLazyLoaded = true;				
		
					customerFeatureSubscription = _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize((System.Guid)this.CustomerFeatureSubscriptionId);
					customerFeatureSubscription.IsNew = false;
					customerFeatureSubscription = (CustomerFeatureSubscriptionDataObject)ObjectsDataSet.GetObject(customerFeatureSubscription);
                    __customerFeatureSubscriptionAlreadyLazyLoaded = true;
                }

                return await GetCustomerFeatureSubscriptionAsync(false);
            }
            finally
            {
                __customerFeatureSubscriptionSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual CustomerFeatureSubscriptionDataObject CustomerFeatureSubscription 
		{
			get
			{			
				return GetCustomerFeatureSubscriptionAsync(true).Result;
			}
			set
			{
				SetCustomerFeatureSubscriptionValue(value);
			}
		}
		
		public virtual bool ShouldSerializeCustomerFeatureSubscription()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CustomerFeatureSubscription");
		}

		public virtual async Task<CustomerFeatureSubscriptionDataObject> GetCustomerFeatureSubscriptionAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			CustomerFeatureSubscriptionDataObject customerFeatureSubscription;

				
			if (_customerFeatureSubscription_NewObjectId != null)
			{
				customerFeatureSubscription = _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>();
				customerFeatureSubscription.IsNew = true;
				customerFeatureSubscription.InternalObjectId = _customerFeatureSubscription_NewObjectId;
				customerFeatureSubscription = (CustomerFeatureSubscriptionDataObject)ObjectsDataSet.GetObject(customerFeatureSubscription);
			}
			else
			{
				if (this.CustomerFeatureSubscriptionId == null)
					return null;
				if (CustomerFeatureSubscriptionId == null)
					customerFeatureSubscription = null;
				else
				customerFeatureSubscription = _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize((System.Guid)this.CustomerFeatureSubscriptionId);
				customerFeatureSubscription.IsNew = false;
				customerFeatureSubscription = (CustomerFeatureSubscriptionDataObject)ObjectsDataSet.GetObject(customerFeatureSubscription);
				
				if (allowLazyLoading && customerFeatureSubscription == null && LazyLoadingEnabled && (!__customerFeatureSubscriptionAlreadyLazyLoaded || forceReload))
				{
					customerFeatureSubscription = await LoadCustomerFeatureSubscriptionAsync(forceReload : forceReload);
				}
			}
				
			return customerFeatureSubscription;
		}

		public virtual Nullable<System.Guid> CustomerFeatureSubscriptionForeignKey
		{
			get { return CustomerFeatureSubscriptionId; }
			set 
			{	
				CustomerFeatureSubscriptionId = value;
			}
			
		}
		

		protected IDataProvider<CustomerModelDataObject> _customerModelService => _serviceProvider.GetRequiredService<IDataProvider<CustomerModelDataObject>>();

		private readonly SemaphoreSlim __customerModelItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerModelItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerModelItems", which is a collection of CustomerModelDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CustomerModelDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CustomerModelDataObject>> LoadCustomerModelItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerModelItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CustomerModelDataObject>> LoadCustomerModelItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerModelItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerModelItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _customerModelService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __customerModelItemsAlreadyLazyLoaded = true;
                }

                return await GetCustomerModelItemsAsync(false);
            }
            finally
            {
                __customerModelItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CustomerModelDataObject> CustomerModelItems 
		{
			get
			{			
				return GetCustomerModelItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCustomerModelItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CustomerModelItems");
		}

		public virtual async Task<DataObjectCollection<CustomerModelDataObject>> GetCustomerModelItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__customerModelItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCustomerModelItemsAsync(forceReload : forceReload);
			}
			var customerModelItems = ObjectsDataSet.GetRelatedObjects<CustomerModelDataObject>(this, "CustomerModelItems");							
			customerModelItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CustomerModelItems_CollectionChanged);
				
			return customerModelItems;
		}

        private void CustomerModelItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CustomerModelDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CustomerModel", "CustomerDataObject.CustomerModelItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add CustomerModelDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CustomerModelDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CustomerPreOperationalChecklistTemplateDataObject> _customerPreOperationalChecklistTemplateService => _serviceProvider.GetRequiredService<IDataProvider<CustomerPreOperationalChecklistTemplateDataObject>>();

		private readonly SemaphoreSlim __customerPreOperationalChecklistTemplateItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerPreOperationalChecklistTemplateItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerPreOperationalChecklistTemplateItems", which is a collection of CustomerPreOperationalChecklistTemplateDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CustomerPreOperationalChecklistTemplateDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CustomerPreOperationalChecklistTemplateDataObject>> LoadCustomerPreOperationalChecklistTemplateItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerPreOperationalChecklistTemplateItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CustomerPreOperationalChecklistTemplateDataObject>> LoadCustomerPreOperationalChecklistTemplateItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerPreOperationalChecklistTemplateItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerPreOperationalChecklistTemplateItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _customerPreOperationalChecklistTemplateService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __customerPreOperationalChecklistTemplateItemsAlreadyLazyLoaded = true;
                }

                return await GetCustomerPreOperationalChecklistTemplateItemsAsync(false);
            }
            finally
            {
                __customerPreOperationalChecklistTemplateItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CustomerPreOperationalChecklistTemplateDataObject> CustomerPreOperationalChecklistTemplateItems 
		{
			get
			{			
				return GetCustomerPreOperationalChecklistTemplateItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCustomerPreOperationalChecklistTemplateItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CustomerPreOperationalChecklistTemplateItems");
		}

		public virtual async Task<DataObjectCollection<CustomerPreOperationalChecklistTemplateDataObject>> GetCustomerPreOperationalChecklistTemplateItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__customerPreOperationalChecklistTemplateItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCustomerPreOperationalChecklistTemplateItemsAsync(forceReload : forceReload);
			}
			var customerPreOperationalChecklistTemplateItems = ObjectsDataSet.GetRelatedObjects<CustomerPreOperationalChecklistTemplateDataObject>(this, "CustomerPreOperationalChecklistTemplateItems");							
			customerPreOperationalChecklistTemplateItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CustomerPreOperationalChecklistTemplateItems_CollectionChanged);
				
			return customerPreOperationalChecklistTemplateItems;
		}

        private void CustomerPreOperationalChecklistTemplateItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CustomerPreOperationalChecklistTemplateDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CustomerPreOperationalChecklistTemplate", "CustomerDataObject.CustomerPreOperationalChecklistTemplateItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add CustomerPreOperationalChecklistTemplateDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CustomerPreOperationalChecklistTemplateDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CustomerSnapshotDataObject> _customerSnapshotService => _serviceProvider.GetRequiredService<IDataProvider<CustomerSnapshotDataObject>>();

		private readonly SemaphoreSlim __customerSnapshotItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerSnapshotItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerSnapshotItems", which is a collection of CustomerSnapshotDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CustomerSnapshotDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CustomerSnapshotDataObject>> LoadCustomerSnapshotItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerSnapshotItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CustomerSnapshotDataObject>> LoadCustomerSnapshotItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerSnapshotItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerSnapshotItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "fkCustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _customerSnapshotService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __customerSnapshotItemsAlreadyLazyLoaded = true;
                }

                return await GetCustomerSnapshotItemsAsync(false);
            }
            finally
            {
                __customerSnapshotItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CustomerSnapshotDataObject> CustomerSnapshotItems 
		{
			get
			{			
				return GetCustomerSnapshotItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCustomerSnapshotItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CustomerSnapshotItems");
		}

		public virtual async Task<DataObjectCollection<CustomerSnapshotDataObject>> GetCustomerSnapshotItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__customerSnapshotItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCustomerSnapshotItemsAsync(forceReload : forceReload);
			}
			var customerSnapshotItems = ObjectsDataSet.GetRelatedObjects<CustomerSnapshotDataObject>(this, "CustomerSnapshotItems");							
			customerSnapshotItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CustomerSnapshotItems_CollectionChanged);
				
			return customerSnapshotItems;
		}

        private void CustomerSnapshotItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CustomerSnapshotDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CustomerSnapshot", "CustomerDataObject.CustomerSnapshotItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add CustomerSnapshotDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customerBeingChangeTracked_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.fkCustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.fkCustomerId == default(System.Guid))
							relatedObj.fkCustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CustomerSnapshotDataObject).CustomerBeingChangeTracked = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CustomerSSODetailDataObject> _customerSSODetailService => _serviceProvider.GetRequiredService<IDataProvider<CustomerSSODetailDataObject>>();

		private readonly SemaphoreSlim __customerSSODetailItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerSSODetailItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerSSODetailItems", which is a collection of CustomerSSODetailDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CustomerSSODetailDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CustomerSSODetailDataObject>> LoadCustomerSSODetailItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerSSODetailItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CustomerSSODetailDataObject>> LoadCustomerSSODetailItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerSSODetailItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerSSODetailItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _customerSSODetailService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __customerSSODetailItemsAlreadyLazyLoaded = true;
                }

                return await GetCustomerSSODetailItemsAsync(false);
            }
            finally
            {
                __customerSSODetailItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CustomerSSODetailDataObject> CustomerSSODetailItems 
		{
			get
			{			
				return GetCustomerSSODetailItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCustomerSSODetailItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CustomerSSODetailItems");
		}

		public virtual async Task<DataObjectCollection<CustomerSSODetailDataObject>> GetCustomerSSODetailItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__customerSSODetailItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCustomerSSODetailItemsAsync(forceReload : forceReload);
			}
			var customerSSODetailItems = ObjectsDataSet.GetRelatedObjects<CustomerSSODetailDataObject>(this, "CustomerSSODetailItems");							
			customerSSODetailItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CustomerSSODetailItems_CollectionChanged);
				
			return customerSSODetailItems;
		}

        private void CustomerSSODetailItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CustomerSSODetailDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CustomerSSODetail", "CustomerDataObject.CustomerSSODetailItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add CustomerSSODetailDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CustomerSSODetailDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CustomerToModelDataObject> _customerToModelService => _serviceProvider.GetRequiredService<IDataProvider<CustomerToModelDataObject>>();

		private readonly SemaphoreSlim __customerToModelItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerToModelItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerToModelItems", which is a collection of CustomerToModelDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CustomerToModelDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CustomerToModelDataObject>> LoadCustomerToModelItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerToModelItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CustomerToModelDataObject>> LoadCustomerToModelItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerToModelItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerToModelItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _customerToModelService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __customerToModelItemsAlreadyLazyLoaded = true;
                }

                return await GetCustomerToModelItemsAsync(false);
            }
            finally
            {
                __customerToModelItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CustomerToModelDataObject> CustomerToModelItems 
		{
			get
			{			
				return GetCustomerToModelItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCustomerToModelItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CustomerToModelItems");
		}

		public virtual async Task<DataObjectCollection<CustomerToModelDataObject>> GetCustomerToModelItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__customerToModelItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCustomerToModelItemsAsync(forceReload : forceReload);
			}
			var customerToModelItems = ObjectsDataSet.GetRelatedObjects<CustomerToModelDataObject>(this, "CustomerToModelItems");							
			customerToModelItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CustomerToModelItems_CollectionChanged);
				
			return customerToModelItems;
		}

        private void CustomerToModelItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CustomerToModelDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CustomerToModel", "CustomerDataObject.CustomerToModelItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add CustomerToModelDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CustomerToModelDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CustomerToPersonViewDataObject> _customerToPersonViewService => _serviceProvider.GetRequiredService<IDataProvider<CustomerToPersonViewDataObject>>();

		private readonly SemaphoreSlim __customerToPersonViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerToPersonViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CustomerToPersonViewItems", which is a collection of CustomerToPersonViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CustomerToPersonViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CustomerToPersonViewDataObject>> LoadCustomerToPersonViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerToPersonViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CustomerToPersonViewDataObject>> LoadCustomerToPersonViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerToPersonViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerToPersonViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _customerToPersonViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __customerToPersonViewItemsAlreadyLazyLoaded = true;
                }

                return await GetCustomerToPersonViewItemsAsync(false);
            }
            finally
            {
                __customerToPersonViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CustomerToPersonViewDataObject> CustomerToPersonViewItems 
		{
			get
			{			
				return GetCustomerToPersonViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCustomerToPersonViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("CustomerToPersonViewItems");
		}

		public virtual async Task<DataObjectCollection<CustomerToPersonViewDataObject>> GetCustomerToPersonViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__customerToPersonViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCustomerToPersonViewItemsAsync(forceReload : forceReload);
			}
			var customerToPersonViewItems = ObjectsDataSet.GetRelatedObjects<CustomerToPersonViewDataObject>(this, "CustomerToPersonViewItems");							
			customerToPersonViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CustomerToPersonViewItems_CollectionChanged);
				
			return customerToPersonViewItems;
		}

        private void CustomerToPersonViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CustomerToPersonViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CustomerToPersonView", "CustomerDataObject.CustomerToPersonViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add CustomerToPersonViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CustomerToPersonViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DashboardDriverCardViewDataObject> _dashboardDriverCardViewService => _serviceProvider.GetRequiredService<IDataProvider<DashboardDriverCardViewDataObject>>();

		private readonly SemaphoreSlim __dashboardCardViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __dashboardCardViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DashboardCardViewItems", which is a collection of DashboardDriverCardViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DashboardDriverCardViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DashboardDriverCardViewDataObject>> LoadDashboardCardViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDashboardCardViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DashboardDriverCardViewDataObject>> LoadDashboardCardViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __dashboardCardViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__dashboardCardViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _dashboardDriverCardViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __dashboardCardViewItemsAlreadyLazyLoaded = true;
                }

                return await GetDashboardCardViewItemsAsync(false);
            }
            finally
            {
                __dashboardCardViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DashboardDriverCardViewDataObject> DashboardCardViewItems 
		{
			get
			{			
				return GetDashboardCardViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDashboardCardViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("DashboardCardViewItems");
		}

		public virtual async Task<DataObjectCollection<DashboardDriverCardViewDataObject>> GetDashboardCardViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__dashboardCardViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDashboardCardViewItemsAsync(forceReload : forceReload);
			}
			var dashboardCardViewItems = ObjectsDataSet.GetRelatedObjects<DashboardDriverCardViewDataObject>(this, "DashboardCardViewItems");							
			dashboardCardViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DashboardCardViewItems_CollectionChanged);
				
			return dashboardCardViewItems;
		}

        private void DashboardCardViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DashboardDriverCardViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DashboardDriverCardView", "CustomerDataObject.DashboardCardViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add DashboardDriverCardViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DashboardDriverCardViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DashboardDriverCardStoreProcedureDataObject> _dashboardDriverCardStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<DashboardDriverCardStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __dashboardDriverCardStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __dashboardDriverCardStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DashboardDriverCardStoreProcedureItems", which is a collection of DashboardDriverCardStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DashboardDriverCardStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DashboardDriverCardStoreProcedureDataObject>> LoadDashboardDriverCardStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDashboardDriverCardStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DashboardDriverCardStoreProcedureDataObject>> LoadDashboardDriverCardStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __dashboardDriverCardStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__dashboardDriverCardStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _dashboardDriverCardStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __dashboardDriverCardStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetDashboardDriverCardStoreProcedureItemsAsync(false);
            }
            finally
            {
                __dashboardDriverCardStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DashboardDriverCardStoreProcedureDataObject> DashboardDriverCardStoreProcedureItems 
		{
			get
			{			
				return GetDashboardDriverCardStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDashboardDriverCardStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("DashboardDriverCardStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<DashboardDriverCardStoreProcedureDataObject>> GetDashboardDriverCardStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__dashboardDriverCardStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDashboardDriverCardStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var dashboardDriverCardStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<DashboardDriverCardStoreProcedureDataObject>(this, "DashboardDriverCardStoreProcedureItems");							
			dashboardDriverCardStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DashboardDriverCardStoreProcedureItems_CollectionChanged);
				
			return dashboardDriverCardStoreProcedureItems;
		}

        private void DashboardDriverCardStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DashboardDriverCardStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DashboardDriverCardStoreProcedure", "CustomerDataObject.DashboardDriverCardStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add DashboardDriverCardStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DashboardDriverCardStoreProcedureDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DashboardFilterDataObject> _dashboardFilterService => _serviceProvider.GetRequiredService<IDataProvider<DashboardFilterDataObject>>();

		private readonly SemaphoreSlim __dashboardFilterItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __dashboardFilterItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DashboardFilterItems", which is a collection of DashboardFilterDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DashboardFilterDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DashboardFilterDataObject>> LoadDashboardFilterItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDashboardFilterItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DashboardFilterDataObject>> LoadDashboardFilterItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __dashboardFilterItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__dashboardFilterItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _dashboardFilterService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __dashboardFilterItemsAlreadyLazyLoaded = true;
                }

                return await GetDashboardFilterItemsAsync(false);
            }
            finally
            {
                __dashboardFilterItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DashboardFilterDataObject> DashboardFilterItems 
		{
			get
			{			
				return GetDashboardFilterItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDashboardFilterItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("DashboardFilterItems");
		}

		public virtual async Task<DataObjectCollection<DashboardFilterDataObject>> GetDashboardFilterItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__dashboardFilterItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDashboardFilterItemsAsync(forceReload : forceReload);
			}
			var dashboardFilterItems = ObjectsDataSet.GetRelatedObjects<DashboardFilterDataObject>(this, "DashboardFilterItems");							
			dashboardFilterItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DashboardFilterItems_CollectionChanged);
				
			return dashboardFilterItems;
		}

        private void DashboardFilterItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DashboardFilterDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DashboardFilter", "CustomerDataObject.DashboardFilterItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add DashboardFilterDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DashboardFilterDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DashboardVehicleCardStoreProcedureDataObject> _dashboardVehicleCardStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<DashboardVehicleCardStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __dashboardVehicleCardStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __dashboardVehicleCardStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DashboardVehicleCardStoreProcedureItems", which is a collection of DashboardVehicleCardStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DashboardVehicleCardStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DashboardVehicleCardStoreProcedureDataObject>> LoadDashboardVehicleCardStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDashboardVehicleCardStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DashboardVehicleCardStoreProcedureDataObject>> LoadDashboardVehicleCardStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __dashboardVehicleCardStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__dashboardVehicleCardStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _dashboardVehicleCardStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __dashboardVehicleCardStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetDashboardVehicleCardStoreProcedureItemsAsync(false);
            }
            finally
            {
                __dashboardVehicleCardStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DashboardVehicleCardStoreProcedureDataObject> DashboardVehicleCardStoreProcedureItems 
		{
			get
			{			
				return GetDashboardVehicleCardStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDashboardVehicleCardStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("DashboardVehicleCardStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<DashboardVehicleCardStoreProcedureDataObject>> GetDashboardVehicleCardStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__dashboardVehicleCardStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDashboardVehicleCardStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var dashboardVehicleCardStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<DashboardVehicleCardStoreProcedureDataObject>(this, "DashboardVehicleCardStoreProcedureItems");							
			dashboardVehicleCardStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DashboardVehicleCardStoreProcedureItems_CollectionChanged);
				
			return dashboardVehicleCardStoreProcedureItems;
		}

        private void DashboardVehicleCardStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DashboardVehicleCardStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DashboardVehicleCardStoreProcedure", "CustomerDataObject.DashboardVehicleCardStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add DashboardVehicleCardStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DashboardVehicleCardStoreProcedureDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DashboardVehicleCardViewDataObject> _dashboardVehicleCardViewService => _serviceProvider.GetRequiredService<IDataProvider<DashboardVehicleCardViewDataObject>>();

		private readonly SemaphoreSlim __dashboardVehicleCardViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __dashboardVehicleCardViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DashboardVehicleCardViewItems", which is a collection of DashboardVehicleCardViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DashboardVehicleCardViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DashboardVehicleCardViewDataObject>> LoadDashboardVehicleCardViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDashboardVehicleCardViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DashboardVehicleCardViewDataObject>> LoadDashboardVehicleCardViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __dashboardVehicleCardViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__dashboardVehicleCardViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _dashboardVehicleCardViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __dashboardVehicleCardViewItemsAlreadyLazyLoaded = true;
                }

                return await GetDashboardVehicleCardViewItemsAsync(false);
            }
            finally
            {
                __dashboardVehicleCardViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DashboardVehicleCardViewDataObject> DashboardVehicleCardViewItems 
		{
			get
			{			
				return GetDashboardVehicleCardViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDashboardVehicleCardViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("DashboardVehicleCardViewItems");
		}

		public virtual async Task<DataObjectCollection<DashboardVehicleCardViewDataObject>> GetDashboardVehicleCardViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__dashboardVehicleCardViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDashboardVehicleCardViewItemsAsync(forceReload : forceReload);
			}
			var dashboardVehicleCardViewItems = ObjectsDataSet.GetRelatedObjects<DashboardVehicleCardViewDataObject>(this, "DashboardVehicleCardViewItems");							
			dashboardVehicleCardViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DashboardVehicleCardViewItems_CollectionChanged);
				
			return dashboardVehicleCardViewItems;
		}

        private void DashboardVehicleCardViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DashboardVehicleCardViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DashboardVehicleCardView", "CustomerDataObject.DashboardVehicleCardViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add DashboardVehicleCardViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DashboardVehicleCardViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DealerDataObject> _dealerService => _serviceProvider.GetRequiredService<IDataProvider<DealerDataObject>>();
      public virtual void SetDealerValue(DealerDataObject valueToSet)
		{
			SetDealerValue(valueToSet, true, true);
		}

        public virtual void SetDealerValue(DealerDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DealerDataObject existing_dealer = null ;

			if ( !(ObjectsDataSet == null))
			{
				DealerDataObject key;

				if (this._dealer_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DealerDataObject>().Initialize(this.DealerId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DealerDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._dealer_NewObjectId;			
				}

				existing_dealer = (DealerDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_dealer ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Dealer", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "CustomerDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_dealer_NewObjectId != valueToSet.InternalObjectId)
					{
						_dealer_NewObjectId = valueToSet.InternalObjectId;
						_dealerId = valueToSet.Id;
						OnPropertyChanged("DealerId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_dealerId != valueToSet.Id)
					{
						_dealer_NewObjectId = null;

						_dealerId = valueToSet.Id;
						OnPropertyChanged("DealerId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_dealerId = Guid.Empty;
				OnPropertyChanged("DealerId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_dealer ,valueToSet))
				OnPropertyChanged("Dealer", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __dealerSemaphore = new SemaphoreSlim(1, 1);
		private bool __dealerAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Dealer", which is a DealerDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DealerDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DealerDataObject> LoadDealerAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDealerAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DealerDataObject> LoadDealerAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __dealerSemaphore.WaitAsync();
			
	        try
            {
                if (!__dealerAlreadyLazyLoaded || forceReload)
                {
								
					DealerDataObject dealer = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __dealerAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						dealer = _serviceProvider.GetRequiredService<DealerDataObject>().Initialize(this.DealerId);
						dealer.IsNew = false;
						dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
						if (dealer != null)
						{
							return dealer;
						}
					}

					dealer = await _dealerService.GetAsync(_serviceProvider.GetRequiredService<DealerDataObject>().Initialize(this.DealerId), parameters : parameters, skipSecurity: skipSecurity);

					SetDealerValue(dealer, false, false);
					__dealerAlreadyLazyLoaded = true;				
		
					dealer = _serviceProvider.GetRequiredService<DealerDataObject>().Initialize(this.DealerId);
					dealer.IsNew = false;
					dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
                    __dealerAlreadyLazyLoaded = true;
                }

                return await GetDealerAsync(false);
            }
            finally
            {
                __dealerSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DealerDataObject Dealer 
		{
			get
			{			
				return GetDealerAsync(true).Result;
			}
			set
			{
				SetDealerValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDealer()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("Dealer");
		}

		public virtual async Task<DealerDataObject> GetDealerAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DealerDataObject dealer;

				
			if (_dealer_NewObjectId != null)
			{
				dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
				dealer.IsNew = true;
				dealer.InternalObjectId = _dealer_NewObjectId;
				dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
			}
			else
			{
				dealer = _serviceProvider.GetRequiredService<DealerDataObject>().Initialize(this.DealerId);
				dealer.IsNew = false;
				dealer = (DealerDataObject)ObjectsDataSet.GetObject(dealer);
				
				if (allowLazyLoading && dealer == null && LazyLoadingEnabled && (!__dealerAlreadyLazyLoaded || forceReload))
				{
					dealer = await LoadDealerAsync(forceReload : forceReload);
				}
			}
				
			return dealer;
		}

		public virtual System.Guid DealerForeignKey
		{
			get { return DealerId; }
			set 
			{	
				DealerId = value;
			}
			
		}
		

		protected IDataProvider<DepartmentDataObject> _departmentService => _serviceProvider.GetRequiredService<IDataProvider<DepartmentDataObject>>();

		private readonly SemaphoreSlim __departmentItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __departmentItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DepartmentItems", which is a collection of DepartmentDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DepartmentDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DepartmentDataObject>> LoadDepartmentItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDepartmentItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DepartmentDataObject>> LoadDepartmentItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __departmentItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__departmentItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _departmentService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __departmentItemsAlreadyLazyLoaded = true;
                }

                return await GetDepartmentItemsAsync(false);
            }
            finally
            {
                __departmentItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DepartmentDataObject> DepartmentItems 
		{
			get
			{			
				return GetDepartmentItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDepartmentItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("DepartmentItems");
		}

		public virtual async Task<DataObjectCollection<DepartmentDataObject>> GetDepartmentItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__departmentItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDepartmentItemsAsync(forceReload : forceReload);
			}
			var departmentItems = ObjectsDataSet.GetRelatedObjects<DepartmentDataObject>(this, "DepartmentItems");							
			departmentItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DepartmentItems_CollectionChanged);
				
			return departmentItems;
		}

        private void DepartmentItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DepartmentDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : Department", "CustomerDataObject.DepartmentItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add DepartmentDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DepartmentDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DriverDataObject> _driverService => _serviceProvider.GetRequiredService<IDataProvider<DriverDataObject>>();

		private readonly SemaphoreSlim __driverItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __driverItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DriverItems", which is a collection of DriverDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DriverDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DriverDataObject>> LoadDriverItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDriverItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DriverDataObject>> LoadDriverItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __driverItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__driverItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _driverService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __driverItemsAlreadyLazyLoaded = true;
                }

                return await GetDriverItemsAsync(false);
            }
            finally
            {
                __driverItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DriverDataObject> DriverItems 
		{
			get
			{			
				return GetDriverItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDriverItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("DriverItems");
		}

		public virtual async Task<DataObjectCollection<DriverDataObject>> GetDriverItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__driverItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDriverItemsAsync(forceReload : forceReload);
			}
			var driverItems = ObjectsDataSet.GetRelatedObjects<DriverDataObject>(this, "DriverItems");							
			driverItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DriverItems_CollectionChanged);
				
			return driverItems;
		}

        private void DriverItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DriverDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : Driver", "CustomerDataObject.DriverItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add DriverDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DriverDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DriverLicenseExpiryStoreProcedureDataObject> _driverLicenseExpiryStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<DriverLicenseExpiryStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __driverLicenseExpiryStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __driverLicenseExpiryStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DriverLicenseExpiryStoreProcedureItems", which is a collection of DriverLicenseExpiryStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DriverLicenseExpiryStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DriverLicenseExpiryStoreProcedureDataObject>> LoadDriverLicenseExpiryStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDriverLicenseExpiryStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DriverLicenseExpiryStoreProcedureDataObject>> LoadDriverLicenseExpiryStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __driverLicenseExpiryStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__driverLicenseExpiryStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _driverLicenseExpiryStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __driverLicenseExpiryStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetDriverLicenseExpiryStoreProcedureItemsAsync(false);
            }
            finally
            {
                __driverLicenseExpiryStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DriverLicenseExpiryStoreProcedureDataObject> DriverLicenseExpiryStoreProcedureItems 
		{
			get
			{			
				return GetDriverLicenseExpiryStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDriverLicenseExpiryStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("DriverLicenseExpiryStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<DriverLicenseExpiryStoreProcedureDataObject>> GetDriverLicenseExpiryStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__driverLicenseExpiryStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDriverLicenseExpiryStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var driverLicenseExpiryStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<DriverLicenseExpiryStoreProcedureDataObject>(this, "DriverLicenseExpiryStoreProcedureItems");							
			driverLicenseExpiryStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DriverLicenseExpiryStoreProcedureItems_CollectionChanged);
				
			return driverLicenseExpiryStoreProcedureItems;
		}

        private void DriverLicenseExpiryStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DriverLicenseExpiryStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DriverLicenseExpiryStoreProcedure", "CustomerDataObject.DriverLicenseExpiryStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add DriverLicenseExpiryStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DriverLicenseExpiryStoreProcedureDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DriverLicenseExpiryViewDataObject> _driverLicenseExpiryViewService => _serviceProvider.GetRequiredService<IDataProvider<DriverLicenseExpiryViewDataObject>>();

		private readonly SemaphoreSlim __driverLicenseExpiryViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __driverLicenseExpiryViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DriverLicenseExpiryViewItems", which is a collection of DriverLicenseExpiryViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DriverLicenseExpiryViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DriverLicenseExpiryViewDataObject>> LoadDriverLicenseExpiryViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDriverLicenseExpiryViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DriverLicenseExpiryViewDataObject>> LoadDriverLicenseExpiryViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __driverLicenseExpiryViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__driverLicenseExpiryViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _driverLicenseExpiryViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __driverLicenseExpiryViewItemsAlreadyLazyLoaded = true;
                }

                return await GetDriverLicenseExpiryViewItemsAsync(false);
            }
            finally
            {
                __driverLicenseExpiryViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DriverLicenseExpiryViewDataObject> DriverLicenseExpiryViewItems 
		{
			get
			{			
				return GetDriverLicenseExpiryViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDriverLicenseExpiryViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("DriverLicenseExpiryViewItems");
		}

		public virtual async Task<DataObjectCollection<DriverLicenseExpiryViewDataObject>> GetDriverLicenseExpiryViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__driverLicenseExpiryViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDriverLicenseExpiryViewItemsAsync(forceReload : forceReload);
			}
			var driverLicenseExpiryViewItems = ObjectsDataSet.GetRelatedObjects<DriverLicenseExpiryViewDataObject>(this, "DriverLicenseExpiryViewItems");							
			driverLicenseExpiryViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DriverLicenseExpiryViewItems_CollectionChanged);
				
			return driverLicenseExpiryViewItems;
		}

        private void DriverLicenseExpiryViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DriverLicenseExpiryViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DriverLicenseExpiryView", "CustomerDataObject.DriverLicenseExpiryViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add DriverLicenseExpiryViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DriverLicenseExpiryViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<EmailGroupsDataObject> _emailGroupsService => _serviceProvider.GetRequiredService<IDataProvider<EmailGroupsDataObject>>();

		private readonly SemaphoreSlim __emailGroupsItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __emailGroupsItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "EmailGroupsItems", which is a collection of EmailGroupsDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of EmailGroupsDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<EmailGroupsDataObject>> LoadEmailGroupsItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadEmailGroupsItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<EmailGroupsDataObject>> LoadEmailGroupsItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __emailGroupsItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__emailGroupsItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _emailGroupsService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __emailGroupsItemsAlreadyLazyLoaded = true;
                }

                return await GetEmailGroupsItemsAsync(false);
            }
            finally
            {
                __emailGroupsItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<EmailGroupsDataObject> EmailGroupsItems 
		{
			get
			{			
				return GetEmailGroupsItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeEmailGroupsItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("EmailGroupsItems");
		}

		public virtual async Task<DataObjectCollection<EmailGroupsDataObject>> GetEmailGroupsItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__emailGroupsItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadEmailGroupsItemsAsync(forceReload : forceReload);
			}
			var emailGroupsItems = ObjectsDataSet.GetRelatedObjects<EmailGroupsDataObject>(this, "EmailGroupsItems");							
			emailGroupsItems.CollectionChanged += new NotifyCollectionChangedEventHandler(EmailGroupsItems_CollectionChanged);
				
			return emailGroupsItems;
		}

        private void EmailGroupsItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as EmailGroupsDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : EmailGroups", "CustomerDataObject.EmailGroupsItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add EmailGroupsDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as EmailGroupsDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<GoUserToCustomerDataObject> _goUserToCustomerService => _serviceProvider.GetRequiredService<IDataProvider<GoUserToCustomerDataObject>>();

		private readonly SemaphoreSlim __goUserToCustomerItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __goUserToCustomerItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "GoUserToCustomerItems", which is a collection of GoUserToCustomerDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of GoUserToCustomerDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<GoUserToCustomerDataObject>> LoadGoUserToCustomerItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadGoUserToCustomerItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<GoUserToCustomerDataObject>> LoadGoUserToCustomerItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __goUserToCustomerItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__goUserToCustomerItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _goUserToCustomerService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __goUserToCustomerItemsAlreadyLazyLoaded = true;
                }

                return await GetGoUserToCustomerItemsAsync(false);
            }
            finally
            {
                __goUserToCustomerItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<GoUserToCustomerDataObject> GoUserToCustomerItems 
		{
			get
			{			
				return GetGoUserToCustomerItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeGoUserToCustomerItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("GoUserToCustomerItems");
		}

		public virtual async Task<DataObjectCollection<GoUserToCustomerDataObject>> GetGoUserToCustomerItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__goUserToCustomerItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadGoUserToCustomerItemsAsync(forceReload : forceReload);
			}
			var goUserToCustomerItems = ObjectsDataSet.GetRelatedObjects<GoUserToCustomerDataObject>(this, "GoUserToCustomerItems");							
			goUserToCustomerItems.CollectionChanged += new NotifyCollectionChangedEventHandler(GoUserToCustomerItems_CollectionChanged);
				
			return goUserToCustomerItems;
		}

        private void GoUserToCustomerItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as GoUserToCustomerDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : GoUserToCustomer", "CustomerDataObject.GoUserToCustomerItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add GoUserToCustomerDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as GoUserToCustomerDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject> _impactFrequencyPerTimeSlotViewService => _serviceProvider.GetRequiredService<IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject>>();

		private readonly SemaphoreSlim __impactFrequencyPerTimeSlotViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __impactFrequencyPerTimeSlotViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ImpactFrequencyPerTimeSlotViewItems", which is a collection of ImpactFrequencyPerTimeSlotViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ImpactFrequencyPerTimeSlotViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ImpactFrequencyPerTimeSlotViewDataObject>> LoadImpactFrequencyPerTimeSlotViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadImpactFrequencyPerTimeSlotViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ImpactFrequencyPerTimeSlotViewDataObject>> LoadImpactFrequencyPerTimeSlotViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __impactFrequencyPerTimeSlotViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__impactFrequencyPerTimeSlotViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _impactFrequencyPerTimeSlotViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __impactFrequencyPerTimeSlotViewItemsAlreadyLazyLoaded = true;
                }

                return await GetImpactFrequencyPerTimeSlotViewItemsAsync(false);
            }
            finally
            {
                __impactFrequencyPerTimeSlotViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ImpactFrequencyPerTimeSlotViewDataObject> ImpactFrequencyPerTimeSlotViewItems 
		{
			get
			{			
				return GetImpactFrequencyPerTimeSlotViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeImpactFrequencyPerTimeSlotViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("ImpactFrequencyPerTimeSlotViewItems");
		}

		public virtual async Task<DataObjectCollection<ImpactFrequencyPerTimeSlotViewDataObject>> GetImpactFrequencyPerTimeSlotViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__impactFrequencyPerTimeSlotViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadImpactFrequencyPerTimeSlotViewItemsAsync(forceReload : forceReload);
			}
			var impactFrequencyPerTimeSlotViewItems = ObjectsDataSet.GetRelatedObjects<ImpactFrequencyPerTimeSlotViewDataObject>(this, "ImpactFrequencyPerTimeSlotViewItems");							
			impactFrequencyPerTimeSlotViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ImpactFrequencyPerTimeSlotViewItems_CollectionChanged);
				
			return impactFrequencyPerTimeSlotViewItems;
		}

        private void ImpactFrequencyPerTimeSlotViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ImpactFrequencyPerTimeSlotViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ImpactFrequencyPerTimeSlotView", "CustomerDataObject.ImpactFrequencyPerTimeSlotViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add ImpactFrequencyPerTimeSlotViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ImpactFrequencyPerTimeSlotViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ImpactFrequencyPerWeekDayViewDataObject> _impactFrequencyPerWeekDayViewService => _serviceProvider.GetRequiredService<IDataProvider<ImpactFrequencyPerWeekDayViewDataObject>>();

		private readonly SemaphoreSlim __impactFrequencyPerWeekDayViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __impactFrequencyPerWeekDayViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ImpactFrequencyPerWeekDayViewItems", which is a collection of ImpactFrequencyPerWeekDayViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ImpactFrequencyPerWeekDayViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ImpactFrequencyPerWeekDayViewDataObject>> LoadImpactFrequencyPerWeekDayViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadImpactFrequencyPerWeekDayViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ImpactFrequencyPerWeekDayViewDataObject>> LoadImpactFrequencyPerWeekDayViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __impactFrequencyPerWeekDayViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__impactFrequencyPerWeekDayViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _impactFrequencyPerWeekDayViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __impactFrequencyPerWeekDayViewItemsAlreadyLazyLoaded = true;
                }

                return await GetImpactFrequencyPerWeekDayViewItemsAsync(false);
            }
            finally
            {
                __impactFrequencyPerWeekDayViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ImpactFrequencyPerWeekDayViewDataObject> ImpactFrequencyPerWeekDayViewItems 
		{
			get
			{			
				return GetImpactFrequencyPerWeekDayViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeImpactFrequencyPerWeekDayViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("ImpactFrequencyPerWeekDayViewItems");
		}

		public virtual async Task<DataObjectCollection<ImpactFrequencyPerWeekDayViewDataObject>> GetImpactFrequencyPerWeekDayViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__impactFrequencyPerWeekDayViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadImpactFrequencyPerWeekDayViewItemsAsync(forceReload : forceReload);
			}
			var impactFrequencyPerWeekDayViewItems = ObjectsDataSet.GetRelatedObjects<ImpactFrequencyPerWeekDayViewDataObject>(this, "ImpactFrequencyPerWeekDayViewItems");							
			impactFrequencyPerWeekDayViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ImpactFrequencyPerWeekDayViewItems_CollectionChanged);
				
			return impactFrequencyPerWeekDayViewItems;
		}

        private void ImpactFrequencyPerWeekDayViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ImpactFrequencyPerWeekDayViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ImpactFrequencyPerWeekDayView", "CustomerDataObject.ImpactFrequencyPerWeekDayViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add ImpactFrequencyPerWeekDayViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ImpactFrequencyPerWeekDayViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject> _impactFrequencyPerWeekMonthViewService => _serviceProvider.GetRequiredService<IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject>>();

		private readonly SemaphoreSlim __impactFrequencyPerWeekMonthViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __impactFrequencyPerWeekMonthViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ImpactFrequencyPerWeekMonthViewItems", which is a collection of ImpactFrequencyPerWeekMonthViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ImpactFrequencyPerWeekMonthViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ImpactFrequencyPerWeekMonthViewDataObject>> LoadImpactFrequencyPerWeekMonthViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadImpactFrequencyPerWeekMonthViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ImpactFrequencyPerWeekMonthViewDataObject>> LoadImpactFrequencyPerWeekMonthViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __impactFrequencyPerWeekMonthViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__impactFrequencyPerWeekMonthViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _impactFrequencyPerWeekMonthViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __impactFrequencyPerWeekMonthViewItemsAlreadyLazyLoaded = true;
                }

                return await GetImpactFrequencyPerWeekMonthViewItemsAsync(false);
            }
            finally
            {
                __impactFrequencyPerWeekMonthViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ImpactFrequencyPerWeekMonthViewDataObject> ImpactFrequencyPerWeekMonthViewItems 
		{
			get
			{			
				return GetImpactFrequencyPerWeekMonthViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeImpactFrequencyPerWeekMonthViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("ImpactFrequencyPerWeekMonthViewItems");
		}

		public virtual async Task<DataObjectCollection<ImpactFrequencyPerWeekMonthViewDataObject>> GetImpactFrequencyPerWeekMonthViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__impactFrequencyPerWeekMonthViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadImpactFrequencyPerWeekMonthViewItemsAsync(forceReload : forceReload);
			}
			var impactFrequencyPerWeekMonthViewItems = ObjectsDataSet.GetRelatedObjects<ImpactFrequencyPerWeekMonthViewDataObject>(this, "ImpactFrequencyPerWeekMonthViewItems");							
			impactFrequencyPerWeekMonthViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ImpactFrequencyPerWeekMonthViewItems_CollectionChanged);
				
			return impactFrequencyPerWeekMonthViewItems;
		}

        private void ImpactFrequencyPerWeekMonthViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ImpactFrequencyPerWeekMonthViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ImpactFrequencyPerWeekMonthView", "CustomerDataObject.ImpactFrequencyPerWeekMonthViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add ImpactFrequencyPerWeekMonthViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ImpactFrequencyPerWeekMonthViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<IncompletedChecklistViewDataObject> _incompletedChecklistViewService => _serviceProvider.GetRequiredService<IDataProvider<IncompletedChecklistViewDataObject>>();

		private readonly SemaphoreSlim __incompletedChecklistViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __incompletedChecklistViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "IncompletedChecklistViewItems", which is a collection of IncompletedChecklistViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of IncompletedChecklistViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<IncompletedChecklistViewDataObject>> LoadIncompletedChecklistViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadIncompletedChecklistViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<IncompletedChecklistViewDataObject>> LoadIncompletedChecklistViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __incompletedChecklistViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__incompletedChecklistViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _incompletedChecklistViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __incompletedChecklistViewItemsAlreadyLazyLoaded = true;
                }

                return await GetIncompletedChecklistViewItemsAsync(false);
            }
            finally
            {
                __incompletedChecklistViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<IncompletedChecklistViewDataObject> IncompletedChecklistViewItems 
		{
			get
			{			
				return GetIncompletedChecklistViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeIncompletedChecklistViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("IncompletedChecklistViewItems");
		}

		public virtual async Task<DataObjectCollection<IncompletedChecklistViewDataObject>> GetIncompletedChecklistViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__incompletedChecklistViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadIncompletedChecklistViewItemsAsync(forceReload : forceReload);
			}
			var incompletedChecklistViewItems = ObjectsDataSet.GetRelatedObjects<IncompletedChecklistViewDataObject>(this, "IncompletedChecklistViewItems");							
			incompletedChecklistViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(IncompletedChecklistViewItems_CollectionChanged);
				
			return incompletedChecklistViewItems;
		}

        private void IncompletedChecklistViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as IncompletedChecklistViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : IncompletedChecklistView", "CustomerDataObject.IncompletedChecklistViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add IncompletedChecklistViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as IncompletedChecklistViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<LoggedHoursVersusSeatHoursViewDataObject> _loggedHoursVersusSeatHoursViewService => _serviceProvider.GetRequiredService<IDataProvider<LoggedHoursVersusSeatHoursViewDataObject>>();

		private readonly SemaphoreSlim __loggedHoursVersusSeatHoursViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __loggedHoursVersusSeatHoursViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "LoggedHoursVersusSeatHoursViewItems", which is a collection of LoggedHoursVersusSeatHoursViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of LoggedHoursVersusSeatHoursViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<LoggedHoursVersusSeatHoursViewDataObject>> LoadLoggedHoursVersusSeatHoursViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadLoggedHoursVersusSeatHoursViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<LoggedHoursVersusSeatHoursViewDataObject>> LoadLoggedHoursVersusSeatHoursViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __loggedHoursVersusSeatHoursViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__loggedHoursVersusSeatHoursViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _loggedHoursVersusSeatHoursViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __loggedHoursVersusSeatHoursViewItemsAlreadyLazyLoaded = true;
                }

                return await GetLoggedHoursVersusSeatHoursViewItemsAsync(false);
            }
            finally
            {
                __loggedHoursVersusSeatHoursViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<LoggedHoursVersusSeatHoursViewDataObject> LoggedHoursVersusSeatHoursViewItems 
		{
			get
			{			
				return GetLoggedHoursVersusSeatHoursViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeLoggedHoursVersusSeatHoursViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("LoggedHoursVersusSeatHoursViewItems");
		}

		public virtual async Task<DataObjectCollection<LoggedHoursVersusSeatHoursViewDataObject>> GetLoggedHoursVersusSeatHoursViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__loggedHoursVersusSeatHoursViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadLoggedHoursVersusSeatHoursViewItemsAsync(forceReload : forceReload);
			}
			var loggedHoursVersusSeatHoursViewItems = ObjectsDataSet.GetRelatedObjects<LoggedHoursVersusSeatHoursViewDataObject>(this, "LoggedHoursVersusSeatHoursViewItems");							
			loggedHoursVersusSeatHoursViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(LoggedHoursVersusSeatHoursViewItems_CollectionChanged);
				
			return loggedHoursVersusSeatHoursViewItems;
		}

        private void LoggedHoursVersusSeatHoursViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as LoggedHoursVersusSeatHoursViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : LoggedHoursVersusSeatHoursView", "CustomerDataObject.LoggedHoursVersusSeatHoursViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add LoggedHoursVersusSeatHoursViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as LoggedHoursVersusSeatHoursViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<PersonDataObject> _personService => _serviceProvider.GetRequiredService<IDataProvider<PersonDataObject>>();

		private readonly SemaphoreSlim __personItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __personItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "PersonItems", which is a collection of PersonDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of PersonDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<PersonDataObject>> LoadPersonItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPersonItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<PersonDataObject>> LoadPersonItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __personItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__personItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _personService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __personItemsAlreadyLazyLoaded = true;
                }

                return await GetPersonItemsAsync(false);
            }
            finally
            {
                __personItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<PersonDataObject> PersonItems 
		{
			get
			{			
				return GetPersonItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializePersonItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("PersonItems");
		}

		public virtual async Task<DataObjectCollection<PersonDataObject>> GetPersonItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__personItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadPersonItemsAsync(forceReload : forceReload);
			}
			var personItems = ObjectsDataSet.GetRelatedObjects<PersonDataObject>(this, "PersonItems");							
			personItems.CollectionChanged += new NotifyCollectionChangedEventHandler(PersonItems_CollectionChanged);
				
			return personItems;
		}

        private void PersonItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as PersonDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : Person", "CustomerDataObject.PersonItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add PersonDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as PersonDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<SiteDataObject> _siteService => _serviceProvider.GetRequiredService<IDataProvider<SiteDataObject>>();

		private readonly SemaphoreSlim __sitesSemaphore = new SemaphoreSlim(1, 1);
		private bool __sitesAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Sites", which is a collection of SiteDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SiteDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SiteDataObject>> LoadSitesAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSitesAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SiteDataObject>> LoadSitesAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __sitesSemaphore.WaitAsync();
			
	        try
            {
                if (!__sitesAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _siteService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __sitesAlreadyLazyLoaded = true;
                }

                return await GetSitesAsync(false);
            }
            finally
            {
                __sitesSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SiteDataObject> Sites 
		{
			get
			{			
				return GetSitesAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSites()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("Sites");
		}

		public virtual async Task<DataObjectCollection<SiteDataObject>> GetSitesAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__sitesAlreadyLazyLoaded || forceReload) )
			{
				await LoadSitesAsync(forceReload : forceReload);
			}
			var sites = ObjectsDataSet.GetRelatedObjects<SiteDataObject>(this, "Sites");							
			sites.CollectionChanged += new NotifyCollectionChangedEventHandler(Sites_CollectionChanged);
				
			return sites;
		}

        private void Sites_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SiteDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : Site", "CustomerDataObject.Sites_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add SiteDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SiteDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<SlamcoreDeviceDataObject> _slamcoreDeviceService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceDataObject>>();

		private readonly SemaphoreSlim __slamcoreDeviceItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreDeviceItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreDeviceItems", which is a collection of SlamcoreDeviceDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SlamcoreDeviceDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SlamcoreDeviceDataObject>> LoadSlamcoreDeviceItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreDeviceItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SlamcoreDeviceDataObject>> LoadSlamcoreDeviceItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreDeviceItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreDeviceItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _slamcoreDeviceService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __slamcoreDeviceItemsAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreDeviceItemsAsync(false);
            }
            finally
            {
                __slamcoreDeviceItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SlamcoreDeviceDataObject> SlamcoreDeviceItems 
		{
			get
			{			
				return GetSlamcoreDeviceItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreDeviceItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("SlamcoreDeviceItems");
		}

		public virtual async Task<DataObjectCollection<SlamcoreDeviceDataObject>> GetSlamcoreDeviceItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__slamcoreDeviceItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadSlamcoreDeviceItemsAsync(forceReload : forceReload);
			}
			var slamcoreDeviceItems = ObjectsDataSet.GetRelatedObjects<SlamcoreDeviceDataObject>(this, "SlamcoreDeviceItems");							
			slamcoreDeviceItems.CollectionChanged += new NotifyCollectionChangedEventHandler(SlamcoreDeviceItems_CollectionChanged);
				
			return slamcoreDeviceItems;
		}

        private void SlamcoreDeviceItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SlamcoreDeviceDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : SlamcoreDevice", "CustomerDataObject.SlamcoreDeviceItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add SlamcoreDeviceDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SlamcoreDeviceDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<TodaysImpactStoreProcedureDataObject> _todaysImpactStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<TodaysImpactStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __todaysImpactStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __todaysImpactStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "TodaysImpactStoreProcedureItems", which is a collection of TodaysImpactStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of TodaysImpactStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<TodaysImpactStoreProcedureDataObject>> LoadTodaysImpactStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadTodaysImpactStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<TodaysImpactStoreProcedureDataObject>> LoadTodaysImpactStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __todaysImpactStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__todaysImpactStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _todaysImpactStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __todaysImpactStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetTodaysImpactStoreProcedureItemsAsync(false);
            }
            finally
            {
                __todaysImpactStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<TodaysImpactStoreProcedureDataObject> TodaysImpactStoreProcedureItems 
		{
			get
			{			
				return GetTodaysImpactStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeTodaysImpactStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("TodaysImpactStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<TodaysImpactStoreProcedureDataObject>> GetTodaysImpactStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__todaysImpactStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadTodaysImpactStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var todaysImpactStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<TodaysImpactStoreProcedureDataObject>(this, "TodaysImpactStoreProcedureItems");							
			todaysImpactStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(TodaysImpactStoreProcedureItems_CollectionChanged);
				
			return todaysImpactStoreProcedureItems;
		}

        private void TodaysImpactStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as TodaysImpactStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : TodaysImpactStoreProcedure", "CustomerDataObject.TodaysImpactStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add TodaysImpactStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as TodaysImpactStoreProcedureDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<TodaysImpactViewDataObject> _todaysImpactViewService => _serviceProvider.GetRequiredService<IDataProvider<TodaysImpactViewDataObject>>();

		private readonly SemaphoreSlim __todaysImpactViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __todaysImpactViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "TodaysImpactViewItems", which is a collection of TodaysImpactViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of TodaysImpactViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<TodaysImpactViewDataObject>> LoadTodaysImpactViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadTodaysImpactViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<TodaysImpactViewDataObject>> LoadTodaysImpactViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __todaysImpactViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__todaysImpactViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _todaysImpactViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __todaysImpactViewItemsAlreadyLazyLoaded = true;
                }

                return await GetTodaysImpactViewItemsAsync(false);
            }
            finally
            {
                __todaysImpactViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<TodaysImpactViewDataObject> TodaysImpactViewItems 
		{
			get
			{			
				return GetTodaysImpactViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeTodaysImpactViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("TodaysImpactViewItems");
		}

		public virtual async Task<DataObjectCollection<TodaysImpactViewDataObject>> GetTodaysImpactViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__todaysImpactViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadTodaysImpactViewItemsAsync(forceReload : forceReload);
			}
			var todaysImpactViewItems = ObjectsDataSet.GetRelatedObjects<TodaysImpactViewDataObject>(this, "TodaysImpactViewItems");							
			todaysImpactViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(TodaysImpactViewItems_CollectionChanged);
				
			return todaysImpactViewItems;
		}

        private void TodaysImpactViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as TodaysImpactViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : TodaysImpactView", "CustomerDataObject.TodaysImpactViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add TodaysImpactViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as TodaysImpactViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<TodaysPreopCheckStoreProcedureDataObject> _todaysPreopCheckStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<TodaysPreopCheckStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __todaysPreopCheckStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __todaysPreopCheckStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "TodaysPreopCheckStoreProcedureItems", which is a collection of TodaysPreopCheckStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of TodaysPreopCheckStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<TodaysPreopCheckStoreProcedureDataObject>> LoadTodaysPreopCheckStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadTodaysPreopCheckStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<TodaysPreopCheckStoreProcedureDataObject>> LoadTodaysPreopCheckStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __todaysPreopCheckStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__todaysPreopCheckStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _todaysPreopCheckStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __todaysPreopCheckStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetTodaysPreopCheckStoreProcedureItemsAsync(false);
            }
            finally
            {
                __todaysPreopCheckStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<TodaysPreopCheckStoreProcedureDataObject> TodaysPreopCheckStoreProcedureItems 
		{
			get
			{			
				return GetTodaysPreopCheckStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeTodaysPreopCheckStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("TodaysPreopCheckStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<TodaysPreopCheckStoreProcedureDataObject>> GetTodaysPreopCheckStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__todaysPreopCheckStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadTodaysPreopCheckStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var todaysPreopCheckStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<TodaysPreopCheckStoreProcedureDataObject>(this, "TodaysPreopCheckStoreProcedureItems");							
			todaysPreopCheckStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(TodaysPreopCheckStoreProcedureItems_CollectionChanged);
				
			return todaysPreopCheckStoreProcedureItems;
		}

        private void TodaysPreopCheckStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as TodaysPreopCheckStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : TodaysPreopCheckStoreProcedure", "CustomerDataObject.TodaysPreopCheckStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add TodaysPreopCheckStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as TodaysPreopCheckStoreProcedureDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<TodaysPreopCheckViewDataObject> _todaysPreopCheckViewService => _serviceProvider.GetRequiredService<IDataProvider<TodaysPreopCheckViewDataObject>>();

		private readonly SemaphoreSlim __todaysPreopCheckViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __todaysPreopCheckViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "TodaysPreopCheckViewItems", which is a collection of TodaysPreopCheckViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of TodaysPreopCheckViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<TodaysPreopCheckViewDataObject>> LoadTodaysPreopCheckViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadTodaysPreopCheckViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<TodaysPreopCheckViewDataObject>> LoadTodaysPreopCheckViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __todaysPreopCheckViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__todaysPreopCheckViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _todaysPreopCheckViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __todaysPreopCheckViewItemsAlreadyLazyLoaded = true;
                }

                return await GetTodaysPreopCheckViewItemsAsync(false);
            }
            finally
            {
                __todaysPreopCheckViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<TodaysPreopCheckViewDataObject> TodaysPreopCheckViewItems 
		{
			get
			{			
				return GetTodaysPreopCheckViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeTodaysPreopCheckViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("TodaysPreopCheckViewItems");
		}

		public virtual async Task<DataObjectCollection<TodaysPreopCheckViewDataObject>> GetTodaysPreopCheckViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__todaysPreopCheckViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadTodaysPreopCheckViewItemsAsync(forceReload : forceReload);
			}
			var todaysPreopCheckViewItems = ObjectsDataSet.GetRelatedObjects<TodaysPreopCheckViewDataObject>(this, "TodaysPreopCheckViewItems");							
			todaysPreopCheckViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(TodaysPreopCheckViewItems_CollectionChanged);
				
			return todaysPreopCheckViewItems;
		}

        private void TodaysPreopCheckViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as TodaysPreopCheckViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : TodaysPreopCheckView", "CustomerDataObject.TodaysPreopCheckViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add TodaysPreopCheckViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as TodaysPreopCheckViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleDataObject> _vehicleService => _serviceProvider.GetRequiredService<IDataProvider<VehicleDataObject>>();

		private readonly SemaphoreSlim __vehicleItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleItems", which is a collection of VehicleDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleDataObject>> LoadVehicleItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleDataObject>> LoadVehicleItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleItemsAsync(false);
            }
            finally
            {
                __vehicleItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleDataObject> VehicleItems 
		{
			get
			{			
				return GetVehicleItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("VehicleItems");
		}

		public virtual async Task<DataObjectCollection<VehicleDataObject>> GetVehicleItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleItemsAsync(forceReload : forceReload);
			}
			var vehicleItems = ObjectsDataSet.GetRelatedObjects<VehicleDataObject>(this, "VehicleItems");							
			vehicleItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleItems_CollectionChanged);
				
			return vehicleItems;
		}

        private void VehicleItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : Vehicle", "CustomerDataObject.VehicleItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add VehicleDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(System.Guid))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> _vehicleUtilizationLastTwelveHoursStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __vehicleUtilizationLastTwelveHoursStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleUtilizationLastTwelveHoursStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleUtilizationLastTwelveHoursStoreProcedureItems", which is a collection of VehicleUtilizationLastTwelveHoursStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleUtilizationLastTwelveHoursStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>> LoadVehicleUtilizationLastTwelveHoursStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleUtilizationLastTwelveHoursStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>> LoadVehicleUtilizationLastTwelveHoursStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleUtilizationLastTwelveHoursStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleUtilizationLastTwelveHoursStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleUtilizationLastTwelveHoursStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleUtilizationLastTwelveHoursStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleUtilizationLastTwelveHoursStoreProcedureItemsAsync(false);
            }
            finally
            {
                __vehicleUtilizationLastTwelveHoursStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> VehicleUtilizationLastTwelveHoursStoreProcedureItems 
		{
			get
			{			
				return GetVehicleUtilizationLastTwelveHoursStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleUtilizationLastTwelveHoursStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("VehicleUtilizationLastTwelveHoursStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>> GetVehicleUtilizationLastTwelveHoursStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleUtilizationLastTwelveHoursStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleUtilizationLastTwelveHoursStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var vehicleUtilizationLastTwelveHoursStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>(this, "VehicleUtilizationLastTwelveHoursStoreProcedureItems");							
			vehicleUtilizationLastTwelveHoursStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleUtilizationLastTwelveHoursStoreProcedureItems_CollectionChanged);
				
			return vehicleUtilizationLastTwelveHoursStoreProcedureItems;
		}

        private void VehicleUtilizationLastTwelveHoursStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleUtilizationLastTwelveHoursStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleUtilizationLastTwelveHoursStoreProcedure", "CustomerDataObject.VehicleUtilizationLastTwelveHoursStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add VehicleUtilizationLastTwelveHoursStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleUtilizationLastTwelveHoursStoreProcedureDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject> _vehicleUtilizationLastTwelveHoursViewService => _serviceProvider.GetRequiredService<IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject>>();

		private readonly SemaphoreSlim __vehicleUtilizationLastTwelveHoursViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleUtilizationLastTwelveHoursViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleUtilizationLastTwelveHoursViewItems", which is a collection of VehicleUtilizationLastTwelveHoursViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleUtilizationLastTwelveHoursViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleUtilizationLastTwelveHoursViewDataObject>> LoadVehicleUtilizationLastTwelveHoursViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleUtilizationLastTwelveHoursViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleUtilizationLastTwelveHoursViewDataObject>> LoadVehicleUtilizationLastTwelveHoursViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleUtilizationLastTwelveHoursViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleUtilizationLastTwelveHoursViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "CustomerId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleUtilizationLastTwelveHoursViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleUtilizationLastTwelveHoursViewItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleUtilizationLastTwelveHoursViewItemsAsync(false);
            }
            finally
            {
                __vehicleUtilizationLastTwelveHoursViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleUtilizationLastTwelveHoursViewDataObject> VehicleUtilizationLastTwelveHoursViewItems 
		{
			get
			{			
				return GetVehicleUtilizationLastTwelveHoursViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleUtilizationLastTwelveHoursViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerDataObject") && ObjectsDataSet.RelationsToInclude["CustomerDataObject"].Contains("VehicleUtilizationLastTwelveHoursViewItems");
		}

		public virtual async Task<DataObjectCollection<VehicleUtilizationLastTwelveHoursViewDataObject>> GetVehicleUtilizationLastTwelveHoursViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleUtilizationLastTwelveHoursViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleUtilizationLastTwelveHoursViewItemsAsync(forceReload : forceReload);
			}
			var vehicleUtilizationLastTwelveHoursViewItems = ObjectsDataSet.GetRelatedObjects<VehicleUtilizationLastTwelveHoursViewDataObject>(this, "VehicleUtilizationLastTwelveHoursViewItems");							
			vehicleUtilizationLastTwelveHoursViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleUtilizationLastTwelveHoursViewItems_CollectionChanged);
				
			return vehicleUtilizationLastTwelveHoursViewItems;
		}

        private void VehicleUtilizationLastTwelveHoursViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleUtilizationLastTwelveHoursViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleUtilizationLastTwelveHoursView", "CustomerDataObject.VehicleUtilizationLastTwelveHoursViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of CustomerDataObject throw an exception while trying to add VehicleUtilizationLastTwelveHoursViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._customer_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.CustomerId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.CustomerId == default(Nullable<System.Guid>))
							relatedObj.CustomerId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleUtilizationLastTwelveHoursViewDataObject).Customer = null;
                    // }
                    break;
            }            
        }

		public override void ClearLazyLoadFlags()
		{
			__accessGroupItemsAlreadyLazyLoaded = false;
			__checklistStatusViewItemsAlreadyLazyLoaded = false;
			__currentDriverStatusChartViewItemsAlreadyLazyLoaded = false;
			__currentVehicleStatusChartViewItemsAlreadyLazyLoaded = false;
			__customerModelItemsAlreadyLazyLoaded = false;
			__customerPreOperationalChecklistTemplateItemsAlreadyLazyLoaded = false;
			__customerSnapshotItemsAlreadyLazyLoaded = false;
			__customerSSODetailItemsAlreadyLazyLoaded = false;
			__customerToModelItemsAlreadyLazyLoaded = false;
			__customerToPersonViewItemsAlreadyLazyLoaded = false;
			__dashboardCardViewItemsAlreadyLazyLoaded = false;
			__dashboardDriverCardStoreProcedureItemsAlreadyLazyLoaded = false;
			__dashboardFilterItemsAlreadyLazyLoaded = false;
			__dashboardVehicleCardStoreProcedureItemsAlreadyLazyLoaded = false;
			__dashboardVehicleCardViewItemsAlreadyLazyLoaded = false;
			__departmentItemsAlreadyLazyLoaded = false;
			__driverItemsAlreadyLazyLoaded = false;
			__driverLicenseExpiryStoreProcedureItemsAlreadyLazyLoaded = false;
			__driverLicenseExpiryViewItemsAlreadyLazyLoaded = false;
			__emailGroupsItemsAlreadyLazyLoaded = false;
			__goUserToCustomerItemsAlreadyLazyLoaded = false;
			__impactFrequencyPerTimeSlotViewItemsAlreadyLazyLoaded = false;
			__impactFrequencyPerWeekDayViewItemsAlreadyLazyLoaded = false;
			__impactFrequencyPerWeekMonthViewItemsAlreadyLazyLoaded = false;
			__incompletedChecklistViewItemsAlreadyLazyLoaded = false;
			__loggedHoursVersusSeatHoursViewItemsAlreadyLazyLoaded = false;
			__personItemsAlreadyLazyLoaded = false;
			__sitesAlreadyLazyLoaded = false;
			__slamcoreDeviceItemsAlreadyLazyLoaded = false;
			__todaysImpactStoreProcedureItemsAlreadyLazyLoaded = false;
			__todaysImpactViewItemsAlreadyLazyLoaded = false;
			__todaysPreopCheckStoreProcedureItemsAlreadyLazyLoaded = false;
			__todaysPreopCheckViewItemsAlreadyLazyLoaded = false;
			__vehicleItemsAlreadyLazyLoaded = false;
			__vehicleUtilizationLastTwelveHoursStoreProcedureItemsAlreadyLazyLoaded = false;
			__vehicleUtilizationLastTwelveHoursViewItemsAlreadyLazyLoaded = false;
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadContactPersonInformationAsync()) != null)
				result.Add(ContactPersonInformation);
			if ((await LoadCountryAsync()) != null)
				result.Add(Country);
			if ((await LoadCustomerFeatureSubscriptionAsync()) != null)
				result.Add(CustomerFeatureSubscription);
			if ((await LoadDealerAsync()) != null)
				result.Add(Dealer);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadAccessGroupItemsAsync()) != null)
				result.AddRange(AccessGroupItems);
			if ((await LoadChecklistStatusViewItemsAsync()) != null)
				result.AddRange(ChecklistStatusViewItems);
			if ((await LoadCurrentDriverStatusChartViewItemsAsync()) != null)
				result.AddRange(CurrentDriverStatusChartViewItems);
			if ((await LoadCurrentVehicleStatusChartViewItemsAsync()) != null)
				result.AddRange(CurrentVehicleStatusChartViewItems);
			if ((await LoadCustomerAuditAsync()) != null)
				result.Add(CustomerAudit);
			if ((await LoadCustomerModelItemsAsync()) != null)
				result.AddRange(CustomerModelItems);
			if ((await LoadCustomerPreOperationalChecklistTemplateItemsAsync()) != null)
				result.AddRange(CustomerPreOperationalChecklistTemplateItems);
			if ((await LoadCustomerSnapshotItemsAsync()) != null)
				result.AddRange(CustomerSnapshotItems);
			if ((await LoadCustomerSSODetailItemsAsync()) != null)
				result.AddRange(CustomerSSODetailItems);
			if ((await LoadCustomerToModelItemsAsync()) != null)
				result.AddRange(CustomerToModelItems);
			if ((await LoadCustomerToPersonViewItemsAsync()) != null)
				result.AddRange(CustomerToPersonViewItems);
			if ((await LoadDashboardCardViewItemsAsync()) != null)
				result.AddRange(DashboardCardViewItems);
			if ((await LoadDashboardDriverCardStoreProcedureItemsAsync()) != null)
				result.AddRange(DashboardDriverCardStoreProcedureItems);
			if ((await LoadDashboardFilterItemsAsync()) != null)
				result.AddRange(DashboardFilterItems);
			if ((await LoadDashboardVehicleCardStoreProcedureItemsAsync()) != null)
				result.AddRange(DashboardVehicleCardStoreProcedureItems);
			if ((await LoadDashboardVehicleCardViewItemsAsync()) != null)
				result.AddRange(DashboardVehicleCardViewItems);
			if ((await LoadDepartmentItemsAsync()) != null)
				result.AddRange(DepartmentItems);
			if ((await LoadDriverItemsAsync()) != null)
				result.AddRange(DriverItems);
			if ((await LoadDriverLicenseExpiryStoreProcedureItemsAsync()) != null)
				result.AddRange(DriverLicenseExpiryStoreProcedureItems);
			if ((await LoadDriverLicenseExpiryViewItemsAsync()) != null)
				result.AddRange(DriverLicenseExpiryViewItems);
			if ((await LoadEmailGroupsItemsAsync()) != null)
				result.AddRange(EmailGroupsItems);
			if ((await LoadGoUserToCustomerItemsAsync()) != null)
				result.AddRange(GoUserToCustomerItems);
			if ((await LoadImpactFrequencyPerTimeSlotViewItemsAsync()) != null)
				result.AddRange(ImpactFrequencyPerTimeSlotViewItems);
			if ((await LoadImpactFrequencyPerWeekDayViewItemsAsync()) != null)
				result.AddRange(ImpactFrequencyPerWeekDayViewItems);
			if ((await LoadImpactFrequencyPerWeekMonthViewItemsAsync()) != null)
				result.AddRange(ImpactFrequencyPerWeekMonthViewItems);
			if ((await LoadIncompletedChecklistViewItemsAsync()) != null)
				result.AddRange(IncompletedChecklistViewItems);
			if ((await LoadLoggedHoursVersusSeatHoursViewItemsAsync()) != null)
				result.AddRange(LoggedHoursVersusSeatHoursViewItems);
			if ((await LoadPersonItemsAsync()) != null)
				result.AddRange(PersonItems);
			if ((await LoadSitesAsync()) != null)
				result.AddRange(Sites);
			if ((await LoadSlamcoreDeviceItemsAsync()) != null)
				result.AddRange(SlamcoreDeviceItems);
			if ((await LoadTodaysImpactStoreProcedureItemsAsync()) != null)
				result.AddRange(TodaysImpactStoreProcedureItems);
			if ((await LoadTodaysImpactViewItemsAsync()) != null)
				result.AddRange(TodaysImpactViewItems);
			if ((await LoadTodaysPreopCheckStoreProcedureItemsAsync()) != null)
				result.AddRange(TodaysPreopCheckStoreProcedureItems);
			if ((await LoadTodaysPreopCheckViewItemsAsync()) != null)
				result.AddRange(TodaysPreopCheckViewItems);
			if ((await LoadVehicleItemsAsync()) != null)
				result.AddRange(VehicleItems);
			if ((await LoadVehicleUtilizationLastTwelveHoursStoreProcedureItemsAsync()) != null)
				result.AddRange(VehicleUtilizationLastTwelveHoursStoreProcedureItems);
			if ((await LoadVehicleUtilizationLastTwelveHoursViewItemsAsync()) != null)
				result.AddRange(VehicleUtilizationLastTwelveHoursViewItems);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				ContactPersonInformation == other ||
				(other is ContactPersonInformationDataObject && (ContactPersonInformationId != default(Nullable<System.Guid>)) && (ContactPersonInformationId == (other as ContactPersonInformationDataObject).Id)) || 
				Country == other ||
				(other is CountryDataObject && (CountryId != default(System.Guid)) && (CountryId == (other as CountryDataObject).Id)) || 
				CustomerFeatureSubscription == other ||
				(other is CustomerFeatureSubscriptionDataObject && (CustomerFeatureSubscriptionId != default(Nullable<System.Guid>)) && (CustomerFeatureSubscriptionId == (other as CustomerFeatureSubscriptionDataObject).Id)) || 
				Dealer == other ||
				(other is DealerDataObject && (DealerId != default(System.Guid)) && (DealerId == (other as DealerDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetActiveValue(System.Boolean valueToSet)
		{
			SetActiveValue(valueToSet, true, true);
		}

		public virtual void SetActiveValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_active != valueToSet)
			{
				_active = valueToSet;

				OnPropertyChanged("Active", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Active property of the Customer DataObject</summary>
        public virtual System.Boolean Active 
		{
			get	{ return _active;}
			
			
			set
			{
				SetActiveValue(value);
			}
		}		
			
			
		public virtual void SetAddessValue(System.String valueToSet)
		{
			SetAddessValue(valueToSet, true, true);
		}

		public virtual void SetAddessValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_addess != valueToSet)
			{
				_addess = valueToSet;

				OnPropertyChanged("Addess", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Address property of the Customer DataObject</summary>
        public virtual System.String Addess 
		{
			get	{ return String.IsNullOrEmpty(_addess) ? null : _addess; }
			
			
			set
			{
				SetAddessValue(value);
			}
		}		
			
			
		public virtual void SetCompanyNameValue(System.String valueToSet)
		{
			SetCompanyNameValue(valueToSet, true, true);
		}

		public virtual void SetCompanyNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_companyName != valueToSet)
			{
				_companyName = valueToSet;

				OnPropertyChanged("CompanyName", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Customer Name property of the Customer DataObject</summary>
        public virtual System.String CompanyName 
		{
			get	{ return _companyName; }
			
			
			set
			{
				SetCompanyNameValue(value);
			}
		}		
			
			
		public virtual void SetConnectionStringValue(System.String valueToSet)
		{
			SetConnectionStringValue(valueToSet, true, true);
		}

		public virtual void SetConnectionStringValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_connectionString != valueToSet)
			{
				_connectionString = valueToSet;

				OnPropertyChanged("ConnectionString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Connection String property of the Customer DataObject</summary>
        public virtual System.String ConnectionString 
		{
			get	{ return String.IsNullOrEmpty(_connectionString) ? null : _connectionString; }
			
			
			set
			{
				SetConnectionStringValue(value);
			}
		}		
			
			
		public virtual void SetContactNumberValue(System.String valueToSet)
		{
			SetContactNumberValue(valueToSet, true, true);
		}

		public virtual void SetContactNumberValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_contactNumber != valueToSet)
			{
				_contactNumber = valueToSet;

				OnPropertyChanged("ContactNumber", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Mobile Number property of the Customer DataObject</summary>
        public virtual System.String ContactNumber 
		{
			get	{ return String.IsNullOrEmpty(_contactNumber) ? null : _contactNumber; }
			
			
			set
			{
				SetContactNumberValue(value);
			}
		}		
			
			
		public virtual void SetContactPersonInformationIdValue(Nullable<System.Guid> valueToSet)
		{
			SetContactPersonInformationIdValue(valueToSet, true, true);
		}

		public virtual void SetContactPersonInformationIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_contactPersonInformationId != valueToSet)
			{
				_contactPersonInformationId = valueToSet;

				// ContactPersonInformationId is a FK. Setting its value should result in a event
				OnPropertyChanged("ContactPersonInformation", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("ContactPersonInformationId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The ContactPersonInformationId property of the Customer DataObject</summary>
        public virtual Nullable<System.Guid> ContactPersonInformationId 
		{
			get	{ return _contactPersonInformationId;}
			
			
			set
			{
				SetContactPersonInformationIdValue(value);
			}
		}		
			
			
		public virtual void SetContractDateValue(Nullable<System.DateTime> valueToSet)
		{
			SetContractDateValue(valueToSet, true, true);
		}

		public virtual void SetContractDateValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_contractDate != null)
				{
					_contractDate = null;
					OnPropertyChanged("ContractDate", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_contractDate != DateTime.MinValue.ToUniversalTime())
				{
					_contractDate = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("ContractDate", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_contractDate != DateTime.MaxValue.ToUniversalTime())
				{
					_contractDate = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("ContractDate", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_contractDate != valueToSet ||
                (_contractDate != null && ((DateTime)_contractDate).Kind == DateTimeKind.Unspecified))
			{
				_contractDate = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("ContractDate", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Contract Date property of the Customer DataObject</summary>
        public virtual Nullable<System.DateTime> ContractDate 
		{
			get	{ return _contractDate;}
			
			
			set
			{
				SetContractDateValue(value);
			}
		}		
			
			
		public virtual void SetContractNumberValue(System.String valueToSet)
		{
			SetContractNumberValue(valueToSet, true, true);
		}

		public virtual void SetContractNumberValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_contractNumber != valueToSet)
			{
				_contractNumber = valueToSet;

				OnPropertyChanged("ContractNumber", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Contract Number property of the Customer DataObject</summary>
        public virtual System.String ContractNumber 
		{
			get	{ return String.IsNullOrEmpty(_contractNumber) ? null : _contractNumber; }
			
			
			set
			{
				SetContractNumberValue(value);
			}
		}		
			
			
		public virtual void SetCountryIdValue(System.Guid valueToSet)
		{
			SetCountryIdValue(valueToSet, true, true);
		}

		public virtual void SetCountryIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_countryId != valueToSet)
			{
				_countryId = valueToSet;

				OnPropertyChanged("CountryId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The CountryId property of the Customer DataObject</summary>
        public virtual System.Guid CountryId 
		{
			get	{ return _countryId;}
			
			
			set
			{
				SetCountryIdValue(value);
			}
		}		
			
			
		public virtual void SetCustomerFeatureSubscriptionIdValue(Nullable<System.Guid> valueToSet)
		{
			SetCustomerFeatureSubscriptionIdValue(valueToSet, true, true);
		}

		public virtual void SetCustomerFeatureSubscriptionIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_customerFeatureSubscriptionId != valueToSet)
			{
				_customerFeatureSubscriptionId = valueToSet;

				// CustomerFeatureSubscriptionId is a FK. Setting its value should result in a event
				OnPropertyChanged("CustomerFeatureSubscription", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("CustomerFeatureSubscriptionId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The CustomerFeatureSubscriptionId property of the Customer DataObject</summary>
        public virtual Nullable<System.Guid> CustomerFeatureSubscriptionId 
		{
			get	{ return _customerFeatureSubscriptionId;}
			
			
			set
			{
				SetCustomerFeatureSubscriptionIdValue(value);
			}
		}		
			
			
		public virtual void SetCustomerLogoValue(System.String valueToSet)
		{
			SetCustomerLogoValue(valueToSet, true, true);
		}

		public virtual void SetCustomerLogoValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_customerLogo != valueToSet)
			{
				_customerLogo = valueToSet;

				OnPropertyChanged("CustomerLogo", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Customer Logo property of the Customer DataObject</summary>
        public virtual System.String CustomerLogo 
		{
			get	{ return _customerLogo;}
			
			
			set
			{
				SetCustomerLogoValue(value);
			}
		}		
			
			
		public virtual void SetCustomerLogoFileSizeValue(Nullable<System.Int32> valueToSet)
		{
			SetCustomerLogoFileSizeValue(valueToSet, true, true);
		}

		public virtual void SetCustomerLogoFileSizeValue(Nullable<System.Int32> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_customerLogoFileSize != valueToSet)
			{
				_customerLogoFileSize = valueToSet;

				OnPropertyChanged("CustomerLogoFileSize", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Customer Logo File size (bytes) property of the Customer DataObject</summary>
        public virtual Nullable<System.Int32> CustomerLogoFileSize 
		{
			get	{ return _customerLogoFileSize;}
			
			
			set
			{
				SetCustomerLogoFileSizeValue(value);
			}
		}		
			
			
		public virtual void SetCustomerLogoInternalNameValue(System.String valueToSet)
		{
			SetCustomerLogoInternalNameValue(valueToSet, true, true);
		}

		public virtual void SetCustomerLogoInternalNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_customerLogoInternalName != valueToSet)
			{
				_customerLogoInternalName = valueToSet;

				OnPropertyChanged("CustomerLogoInternalName", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Customer Logo Internal Name property of the Customer DataObject</summary>
        public virtual System.String CustomerLogoInternalName 
		{
			get	{ return String.IsNullOrEmpty(_customerLogoInternalName) ? null : _customerLogoInternalName; }
			
			
			set
			{
				SetCustomerLogoInternalNameValue(value);
			}
		}		
			
		
		/// <summary> The Customer Logo Url property of the Customer DataObject</summary>
        public virtual System.String CustomerLogoUrl 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ((CustomerLogo != null) ? ("api/customer/file/" + Id.ToString() + "/CustomerLogo?t=" + "".ToString()) : "");				
			}
			
		}		
			
			
		public virtual void SetDealerCustomerValue(Nullable<System.Boolean> valueToSet)
		{
			SetDealerCustomerValue(valueToSet, true, true);
		}

		public virtual void SetDealerCustomerValue(Nullable<System.Boolean> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_dealerCustomer != valueToSet)
			{
				_dealerCustomer = valueToSet;

				OnPropertyChanged("DealerCustomer", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Dealer Customer? property of the Customer DataObject</summary>
        public virtual Nullable<System.Boolean> DealerCustomer 
		{
			get	{ return _dealerCustomer;}
			
			
			set
			{
				SetDealerCustomerValue(value);
			}
		}		
			
			
		public virtual void SetDealerIdValue(System.Guid valueToSet)
		{
			SetDealerIdValue(valueToSet, true, true);
		}

		public virtual void SetDealerIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_dealerId != valueToSet)
			{
				_dealerId = valueToSet;

				OnPropertyChanged("DealerId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DealerId property of the Customer DataObject</summary>
        public virtual System.Guid DealerId 
		{
			get	{ return _dealerId;}
			
			
			set
			{
				SetDealerIdValue(value);
			}
		}		
			
			
		public virtual void SetDeletedAtUtcValue(Nullable<System.DateTime> valueToSet)
		{
			SetDeletedAtUtcValue(valueToSet, true, true);
		}

		public virtual void SetDeletedAtUtcValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_deletedAtUtc != null)
				{
					_deletedAtUtc = null;
					OnPropertyChanged("DeletedAtUtc", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_deletedAtUtc != DateTime.MinValue.ToUniversalTime())
				{
					_deletedAtUtc = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("DeletedAtUtc", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_deletedAtUtc != DateTime.MaxValue.ToUniversalTime())
				{
					_deletedAtUtc = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("DeletedAtUtc", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_deletedAtUtc != valueToSet ||
                (_deletedAtUtc != null && ((DateTime)_deletedAtUtc).Kind == DateTimeKind.Unspecified))
			{
				_deletedAtUtc = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("DeletedAtUtc", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DeletedAtUtc property of the Customer DataObject</summary>
        public virtual Nullable<System.DateTime> DeletedAtUtc 
		{
			get	{ return _deletedAtUtc;}
			
			
			set
			{
				SetDeletedAtUtcValue(value);
			}
		}		
			
			
		public virtual void SetDescriptionValue(System.String valueToSet)
		{
			SetDescriptionValue(valueToSet, true, true);
		}

		public virtual void SetDescriptionValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_description != valueToSet)
			{
				_description = valueToSet;

				OnPropertyChanged("Description", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Description property of the Customer DataObject</summary>
        public virtual System.String Description 
		{
			get	{ return String.IsNullOrEmpty(_description) ? null : _description; }
			
			
			set
			{
				SetDescriptionValue(value);
			}
		}		
			
			
		public virtual void SetEmailValue(System.String valueToSet)
		{
			SetEmailValue(valueToSet, true, true);
		}

		public virtual void SetEmailValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_email != valueToSet)
			{
				_email = valueToSet;

				OnPropertyChanged("Email", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Email property of the Customer DataObject</summary>
        public virtual System.String Email 
		{
			get	{ return String.IsNullOrEmpty(_email) ? null : _email; }
			
			
			set
			{
				SetEmailValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the Customer DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetPreferredLocaleValue(Nullable<LocaleEnum> valueToSet)
		{
			SetPreferredLocaleValue(valueToSet, true, true);
		}

		public virtual void SetPreferredLocaleValue(Nullable<LocaleEnum> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_preferredLocale != valueToSet)
			{
				_preferredLocale = valueToSet;

				OnPropertyChanged("PreferredLocale", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PreferredLocaleDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The PreferredLocale property of the Customer DataObject</summary>
        public virtual Nullable<LocaleEnum> PreferredLocale 
		{
			get	{ return _preferredLocale;}
			
			
			set
			{
				SetPreferredLocaleValue(value);
			}
		}		
      public virtual string PreferredLocaleDisplayString
		{
			get
			{
				if (PreferredLocale == null)
					return "-";

				return PreferredLocaleEnumDisplayNameCollection.Where(v => v.Value == PreferredLocale).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<LocaleEnum>> PreferredLocaleEnumDisplayNameCollection
	    {
	        get
	        {
                return LocaleEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetPreferredLocaleStringValue(System.String valueToSet)
		{
			SetPreferredLocaleStringValue(valueToSet, true, true);
		}

		public virtual void SetPreferredLocaleStringValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_preferredLocaleString != valueToSet)
			{
				_preferredLocaleString = valueToSet;

				OnPropertyChanged("PreferredLocaleString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The PreferredLocaleString property of the Customer DataObject</summary>
        public virtual System.String PreferredLocaleString 
		{
			get	{ return String.IsNullOrEmpty(_preferredLocaleString) ? null : _preferredLocaleString; }
			
			
			set
			{
				SetPreferredLocaleStringValue(value);
			}
		}		
			
			
		public virtual void SetPrefixValue(System.String valueToSet)
		{
			SetPrefixValue(valueToSet, true, true);
		}

		public virtual void SetPrefixValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_prefix != valueToSet)
			{
				_prefix = valueToSet;

				OnPropertyChanged("Prefix", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Prefix property of the Customer DataObject</summary>
        public virtual System.String Prefix 
		{
			get	{ return String.IsNullOrEmpty(_prefix) ? null : _prefix; }
			
			
			set
			{
				SetPrefixValue(value);
			}
		}		
			
			
		public virtual void SetSitesCountValue(Nullable<System.Int16> valueToSet)
		{
			SetSitesCountValue(valueToSet, true, true);
		}

		public virtual void SetSitesCountValue(Nullable<System.Int16> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_sitesCount != valueToSet)
			{
				_sitesCount = valueToSet;

				OnPropertyChanged("SitesCount", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Sites count property of the Customer DataObject</summary>
        public virtual Nullable<System.Int16> SitesCount 
		{
			get	{ return _sitesCount;}
			
			
			set
			{
				SetSitesCountValue(value);
			}
		}		
			
		
		/// <summary> The SitesCountAndLink property of the Customer DataObject</summary>
        public virtual System.String SitesCountAndLink 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ((SitesCount != null && (SitesCount.Value == 0)) ? ("<a href=\"?#!/Customers/CustomerDetails/" + Id.ToString() + "\">No Site</a>") : ("<a href=\"?#!/Customers/CustomerDetails/" + Id.ToString() + "\">" + (SitesCount != null ? SitesCount.Value.ToString() : "") + " Sites</a>"));				
			}
			
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			if (propertyName == "SitesCount")
			{
				OnPropertyChanged("SitesCountAndLink", true, dirtyHandlerOn);
			}

			if (propertyName == "Id")
			{
				OnPropertyChanged("SitesCountAndLink", true, dirtyHandlerOn);
			}

			
			// Push the notification to related objects
			var _accessGroupItems = GetAccessGroupItemsAsync(false).Result;
			if (_accessGroupItems != null)
            {
                foreach (var item in _accessGroupItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _checklistStatusViewItems = GetChecklistStatusViewItemsAsync(false).Result;
			if (_checklistStatusViewItems != null)
            {
                foreach (var item in _checklistStatusViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var contactPersonInformation = GetContactPersonInformationAsync(false).Result;
			if (contactPersonInformation != null && this.IsDirty)
            {
				contactPersonInformation.NotifyPropertyChanged("Customer." + propertyName, callers);
			}
			var _currentDriverStatusChartViewItems = GetCurrentDriverStatusChartViewItemsAsync(false).Result;
			if (_currentDriverStatusChartViewItems != null)
            {
                foreach (var item in _currentDriverStatusChartViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _currentVehicleStatusChartViewItems = GetCurrentVehicleStatusChartViewItemsAsync(false).Result;
			if (_currentVehicleStatusChartViewItems != null)
            {
                foreach (var item in _currentVehicleStatusChartViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var customerAudit = GetCustomerAuditAsync(false).Result;
			if (customerAudit != null && this.IsDirty)
            {
				customerAudit.NotifyPropertyChanged("Customer." + propertyName, callers);
			}
			var customerFeatureSubscription = GetCustomerFeatureSubscriptionAsync(false).Result;
			if (customerFeatureSubscription != null && this.IsDirty)
            {
				customerFeatureSubscription.NotifyPropertyChanged("Customer." + propertyName, callers);
			}
			var _customerModelItems = GetCustomerModelItemsAsync(false).Result;
			if (_customerModelItems != null)
            {
                foreach (var item in _customerModelItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _customerPreOperationalChecklistTemplateItems = GetCustomerPreOperationalChecklistTemplateItemsAsync(false).Result;
			if (_customerPreOperationalChecklistTemplateItems != null)
            {
                foreach (var item in _customerPreOperationalChecklistTemplateItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _customerSnapshotItems = GetCustomerSnapshotItemsAsync(false).Result;
			if (_customerSnapshotItems != null)
            {
                foreach (var item in _customerSnapshotItems)
                {
                    item.NotifyPropertyChanged(String.Concat("CustomerBeingChangeTracked.", propertyName), callers);                    
                }
            }
			var _customerSSODetailItems = GetCustomerSSODetailItemsAsync(false).Result;
			if (_customerSSODetailItems != null)
            {
                foreach (var item in _customerSSODetailItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _customerToModelItems = GetCustomerToModelItemsAsync(false).Result;
			if (_customerToModelItems != null)
            {
                foreach (var item in _customerToModelItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _customerToPersonViewItems = GetCustomerToPersonViewItemsAsync(false).Result;
			if (_customerToPersonViewItems != null)
            {
                foreach (var item in _customerToPersonViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _dashboardCardViewItems = GetDashboardCardViewItemsAsync(false).Result;
			if (_dashboardCardViewItems != null)
            {
                foreach (var item in _dashboardCardViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _dashboardDriverCardStoreProcedureItems = GetDashboardDriverCardStoreProcedureItemsAsync(false).Result;
			if (_dashboardDriverCardStoreProcedureItems != null)
            {
                foreach (var item in _dashboardDriverCardStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _dashboardFilterItems = GetDashboardFilterItemsAsync(false).Result;
			if (_dashboardFilterItems != null)
            {
                foreach (var item in _dashboardFilterItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _dashboardVehicleCardStoreProcedureItems = GetDashboardVehicleCardStoreProcedureItemsAsync(false).Result;
			if (_dashboardVehicleCardStoreProcedureItems != null)
            {
                foreach (var item in _dashboardVehicleCardStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _dashboardVehicleCardViewItems = GetDashboardVehicleCardViewItemsAsync(false).Result;
			if (_dashboardVehicleCardViewItems != null)
            {
                foreach (var item in _dashboardVehicleCardViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _departmentItems = GetDepartmentItemsAsync(false).Result;
			if (_departmentItems != null)
            {
                foreach (var item in _departmentItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _driverItems = GetDriverItemsAsync(false).Result;
			if (_driverItems != null)
            {
                foreach (var item in _driverItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _driverLicenseExpiryStoreProcedureItems = GetDriverLicenseExpiryStoreProcedureItemsAsync(false).Result;
			if (_driverLicenseExpiryStoreProcedureItems != null)
            {
                foreach (var item in _driverLicenseExpiryStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _driverLicenseExpiryViewItems = GetDriverLicenseExpiryViewItemsAsync(false).Result;
			if (_driverLicenseExpiryViewItems != null)
            {
                foreach (var item in _driverLicenseExpiryViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _emailGroupsItems = GetEmailGroupsItemsAsync(false).Result;
			if (_emailGroupsItems != null)
            {
                foreach (var item in _emailGroupsItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _goUserToCustomerItems = GetGoUserToCustomerItemsAsync(false).Result;
			if (_goUserToCustomerItems != null)
            {
                foreach (var item in _goUserToCustomerItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _impactFrequencyPerTimeSlotViewItems = GetImpactFrequencyPerTimeSlotViewItemsAsync(false).Result;
			if (_impactFrequencyPerTimeSlotViewItems != null)
            {
                foreach (var item in _impactFrequencyPerTimeSlotViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _impactFrequencyPerWeekDayViewItems = GetImpactFrequencyPerWeekDayViewItemsAsync(false).Result;
			if (_impactFrequencyPerWeekDayViewItems != null)
            {
                foreach (var item in _impactFrequencyPerWeekDayViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _impactFrequencyPerWeekMonthViewItems = GetImpactFrequencyPerWeekMonthViewItemsAsync(false).Result;
			if (_impactFrequencyPerWeekMonthViewItems != null)
            {
                foreach (var item in _impactFrequencyPerWeekMonthViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _incompletedChecklistViewItems = GetIncompletedChecklistViewItemsAsync(false).Result;
			if (_incompletedChecklistViewItems != null)
            {
                foreach (var item in _incompletedChecklistViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _loggedHoursVersusSeatHoursViewItems = GetLoggedHoursVersusSeatHoursViewItemsAsync(false).Result;
			if (_loggedHoursVersusSeatHoursViewItems != null)
            {
                foreach (var item in _loggedHoursVersusSeatHoursViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _personItems = GetPersonItemsAsync(false).Result;
			if (_personItems != null)
            {
                foreach (var item in _personItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _sites = GetSitesAsync(false).Result;
			if (_sites != null)
            {
                foreach (var item in _sites)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _slamcoreDeviceItems = GetSlamcoreDeviceItemsAsync(false).Result;
			if (_slamcoreDeviceItems != null)
            {
                foreach (var item in _slamcoreDeviceItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _todaysImpactStoreProcedureItems = GetTodaysImpactStoreProcedureItemsAsync(false).Result;
			if (_todaysImpactStoreProcedureItems != null)
            {
                foreach (var item in _todaysImpactStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _todaysImpactViewItems = GetTodaysImpactViewItemsAsync(false).Result;
			if (_todaysImpactViewItems != null)
            {
                foreach (var item in _todaysImpactViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _todaysPreopCheckStoreProcedureItems = GetTodaysPreopCheckStoreProcedureItemsAsync(false).Result;
			if (_todaysPreopCheckStoreProcedureItems != null)
            {
                foreach (var item in _todaysPreopCheckStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _todaysPreopCheckViewItems = GetTodaysPreopCheckViewItemsAsync(false).Result;
			if (_todaysPreopCheckViewItems != null)
            {
                foreach (var item in _todaysPreopCheckViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _vehicleItems = GetVehicleItemsAsync(false).Result;
			if (_vehicleItems != null)
            {
                foreach (var item in _vehicleItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _vehicleUtilizationLastTwelveHoursStoreProcedureItems = GetVehicleUtilizationLastTwelveHoursStoreProcedureItemsAsync(false).Result;
			if (_vehicleUtilizationLastTwelveHoursStoreProcedureItems != null)
            {
                foreach (var item in _vehicleUtilizationLastTwelveHoursStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
			var _vehicleUtilizationLastTwelveHoursViewItems = GetVehicleUtilizationLastTwelveHoursViewItemsAsync(false).Result;
			if (_vehicleUtilizationLastTwelveHoursViewItems != null)
            {
                foreach (var item in _vehicleUtilizationLastTwelveHoursViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Customer.", propertyName), callers);                    
                }
            }
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<CustomerDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is CustomerDataObject))
				return false;

			var p = (CustomerDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.Active == p.Active;
			fieldsComparison &= this.Addess == p.Addess;
			fieldsComparison &= this.CompanyName == p.CompanyName;
			fieldsComparison &= this.ConnectionString == p.ConnectionString;
			fieldsComparison &= this.ContactNumber == p.ContactNumber;
			fieldsComparison &= this.ContactPersonInformationId == p.ContactPersonInformationId;
			fieldsComparison &= this.ContractDate == p.ContractDate;
			fieldsComparison &= this.ContractNumber == p.ContractNumber;
			fieldsComparison &= this.CountryId == p.CountryId;
			fieldsComparison &= this.CustomerFeatureSubscriptionId == p.CustomerFeatureSubscriptionId;
			fieldsComparison &= this.CustomerLogo == p.CustomerLogo;
			fieldsComparison &= this.CustomerLogoFileSize == p.CustomerLogoFileSize;
			fieldsComparison &= this.CustomerLogoInternalName == p.CustomerLogoInternalName;
			fieldsComparison &= this.DealerCustomer == p.DealerCustomer;
			fieldsComparison &= this.DealerId == p.DealerId;
			fieldsComparison &= this.DeletedAtUtc == p.DeletedAtUtc;
			fieldsComparison &= this.Description == p.Description;
			fieldsComparison &= this.Email == p.Email;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.PreferredLocale == p.PreferredLocale;
			fieldsComparison &= this.PreferredLocaleString == p.PreferredLocaleString;
			fieldsComparison &= this.Prefix == p.Prefix;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// DeletedAtUtc is a local datetime: Convert to UTC for server-side handling and storing
			if (this._deletedAtUtc_WithTimezoneOffset != null)
			{
				this.DeletedAtUtc = ((DateTimeOffset)this._deletedAtUtc_WithTimezoneOffset).UtcDateTime;
			}
			// ContractDate is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._contractDate_WithTimezoneOffset != null)
			{
				this.ContractDate = ((DateTimeOffset)this._contractDate_WithTimezoneOffset).DateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class CustomerCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public CustomerCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public CustomerCollectionContainer()
		{
		}
		
		public CustomerCollectionContainer Construct(DataObjectCollection<CustomerDataObject> customerItems)
        {
            if (customerItems == null)
                return this;
				
			this.PrimaryKeys = customerItems.Select(c => c.PrimaryKey).ToList();
            if (customerItems.ObjectsDataSet == null)
            {
                customerItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = customerItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = customerItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<CustomerDataObject> ExtractCustomerItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<CustomerDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<CustomerDataObject>(typeof(CustomerDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class CustomerContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public CustomerContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual CustomerContainer Construct(CustomerDataObject customer, bool includeDirtyObjectsOnly = false)
		{
            if (customer == null)
                return this;

			this.PrimaryKey = customer.PrimaryKey;
			
            if (customer.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(customer);
            }

			if(customer.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity Customer", "Unable to set a dataset to the entity. Container may not be initialized", "CustomerDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : Customer");
			}

			if(customer.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in CustomerDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "CustomerDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in CustomerDataObject");
			}
			this.InternalObjectId = (int) customer.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? customer.ObjectsDataSet.CloneDirtyObjects() : customer.ObjectsDataSet;

			return this;
		}
		
		public CustomerDataObject ExtractCustomer()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<CustomerDataObject>(typeof(CustomerDataObject), InternalObjectId);
			
			return result;
        }	
	}

}