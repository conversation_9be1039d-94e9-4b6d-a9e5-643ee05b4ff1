%% ================================================================================
%% FleetXQ Tools Directory - Comprehensive Architecture and Workflow Diagram
%% ================================================================================
%% 
%% This diagram visualizes the complete workflow and architecture of the FleetXQ
%% BulkImporter tool, showing:
%% 
%% 1. Application startup and configuration loading
%% 2. Interactive vs non-interactive mode decision paths
%% 3. SQL-based data generation workflow (replaced CSV dependencies)
%% 4. Staging table validation and processing
%% 5. Error handling and logging throughout the process
%% 6. Service layer interactions and dependencies
%% 7. Database operations and stored procedure execution
%% 8. Configuration management and external dependencies
%% 
%% Key Components:
%% - BulkImportService: Main orchestration service
%% - SqlDataGenerationService: SQL-based data generation (no CSV files)
%% - CommandLineService: Argument parsing and validation
%% - InteractiveService: User interaction and prompts
%% - Staging tables: Driver/Vehicle import with session tracking
%% - Stored procedures: Validation and production processing
%% 
%% Migration Note: This architecture eliminates CSV file dependencies by using
%% pure SQL-based data generation directly in staging tables.
%% ================================================================================

flowchart TD
    %% Entry Points
    A[["🚀 Application Start<br/>Program.Main(args[])"]] --> B{{"📋 Parse Command Line<br/>CommandLineService"}}
    
    %% Configuration and Setup
    B --> C[["⚙️ Load Configuration<br/>appsettings.json<br/>Environment Variables"]]
    C --> D[["🔧 Initialize Services<br/>DI Container Setup"]]
    D --> E[["📝 Setup Logging<br/>Serilog + Correlation Context"]]
    E --> F[["🎯 Create Host<br/>BulkImportHostedService"]]
    
    %% Mode Decision
    F --> G{{"`💬 Interactive Mode?
    options.Interactive`"}}
    G -->|Yes| H[["👤 Interactive Prompts<br/>InteractiveService"]]
    G -->|No| I[["⚡ Non-Interactive Mode"]]
    
    %% Interactive Flow
    H --> H1[["❓ Prompt for Drivers Count"]]
    H1 --> H2[["❓ Prompt for Vehicles Count"]]
    H2 --> H3[["❓ Prompt for Batch Size"]]
    H3 --> H4[["❓ Prompt for Data Source"]]
    H4 --> H5[["❓ Prompt for Dry Run"]]
    H5 --> H6{{"`✅ User Confirmation?
    ConfirmOperation()`"}}
    H6 -->|Yes| J[["📊 Execute Import<br/>BulkImportService"]]
    H6 -->|No| Z1[["❌ Operation Cancelled"]]
    
    %% Non-Interactive Flow
    I --> I1[["✅ Validate Options<br/>ValidateImportOptions()"]]
    I1 --> I2[["🔧 Apply Defaults<br/>ApplyDefaultOptions()"]]
    I2 --> J
    
    %% Core Import Workflow
    J --> J1[["🆔 Generate Session ID<br/>Correlation Context"]]
    J1 --> J2[["📊 Create Import Session<br/>SQL: ImportSession Table"]]
    J2 --> K{{"`🎲 Generate Data?
    options.GenerateData`"}}
    
    %% Data Generation Branch
    K -->|Yes| L[["🔄 SQL Data Generation<br/>SqlDataGenerationService"]]
    K -->|No| M[["📁 File Processing<br/>(Not Implemented)"]]
    M --> M1[["⚠️ Fallback to Generation"]]
    M1 --> L
    
    %% SQL Data Generation Workflow
    L --> L1{{"`👥 Generate Drivers?
    driversCount > 0`"}}
    L1 -->|Yes| L2[["🏭 Generate Driver Batches<br/>GenerateDriverDataAsync()"]]
    L1 -->|No| L3{{"`🚗 Generate Vehicles?
    vehiclesCount > 0`"}}
    
    L2 --> L2a[["📊 SQL Batch Generation<br/>Complex SELECT with CTEs"]]
    L2a --> L2b[["💾 Insert into Staging.DriverImport<br/>Batch Size Controlled"]]
    L2b --> L2c[["📈 Update Progress<br/>Logging & Metrics"]]
    L2c --> L3
    
    L3 -->|Yes| L4[["🏭 Generate Vehicle Batches<br/>GenerateVehicleDataAsync()"]]
    L3 -->|No| N[["✅ Data Validation<br/>ValidateStagedDataAsync()"]]
    
    L4 --> L4a[["📊 SQL Batch Generation<br/>Complex SELECT with CTEs"]]
    L4a --> L4b[["💾 Insert into Staging.VehicleImport<br/>Batch Size Controlled"]]
    L4b --> L4c[["📈 Update Progress<br/>Logging & Metrics"]]
    L4c --> N
    
    %% Validation Phase
    N --> N1[["🔍 Execute Validation SP<br/>EXEC Staging.ValidateImportData"]]
    N1 --> N2[["📋 Update ValidationStatus<br/>Valid/Invalid/Pending"]]
    N2 --> N3[["📊 Count Valid/Invalid Rows"]]
    N3 --> N4{{"`⚠️ Validation Issues?
    InvalidRows > 0`"}}
    N4 -->|Yes| N5[["⚠️ Log Warnings<br/>Continue Processing"]]
    N4 -->|No| O{{"`🧪 Dry Run Mode?
    options.DryRun`"}}
    N5 --> O
    
    %% Processing Decision
    O -->|Yes| P1[["🧪 Dry Run Processing<br/>ProcessImportDataDryRun SP"]]
    O -->|No| P2[["🚀 Production Processing<br/>ProcessImportData SP"]]
    
    %% Dry Run Path
    P1 --> P1a[["📊 Count Records to Process<br/>No Actual Changes"]]
    P1a --> P1b[["📈 Return Simulation Results"]]
    P1b --> Q[["🔄 Update Session Status<br/>UpdateImportSessionAsync()"]]
    
    %% Production Path
    P2 --> P2a[["📝 Mark Records as Processed<br/>Update ValidationStatus"]]
    P2a --> P2b[["🕐 Set ProcessedAt Timestamp"]]
    P2b --> P2c[["📊 Return Processing Results<br/>Inserted/Updated/Skipped"]]
    P2c --> Q
    
    %% Completion and Results
    Q --> R[["📈 Calculate Final Metrics<br/>Duration, Throughput, etc."]]
    R --> S[["📋 Generate Summary Report<br/>ImportResult Object"]]
    S --> T[["📺 Display Results<br/>Console Output + Logging"]]
    T --> U[["🏁 Application Exit<br/>Exit Code: 0=Success, 1=Error"]]
    
    %% Error Handling Flow
    J --> EH1{{"`❌ Exception?
    Any Step Fails`"}}
    EH1 -->|Yes| EH2[["🚨 Error Handling<br/>Log Exception"]]
    EH2 --> EH3[["🔄 Update Session Status<br/>Status = 'Failed'"]]
    EH3 --> EH4[["📋 Create Error Result<br/>Success = false"]]
    EH4 --> T
    
    %% Database Layer
    subgraph DB["🗄️ SQL Server Database"]
        DB1[["🏗️ Staging Schema<br/>- ImportSession<br/>- DriverImport<br/>- VehicleImport"]]
        DB2[["📊 Stored Procedures<br/>- ValidateImportData<br/>- ProcessImportData<br/>- ProcessImportDataDryRun"]]
        DB3[["🎯 Production Tables<br/>(FleetXQ Schema)"]]
    end
    
    %% Configuration Layer
    subgraph CONFIG["⚙️ Configuration Layer"]
        CFG1[["📋 BulkImporterOptions<br/>Batch sizes, timeouts, etc."]]
        CFG2[["🎲 DataGenerationOptions<br/>Random seed, memory limits"]]
        CFG3[["🔗 ConnectionStringOptions<br/>Database connections"]]
    end
    
    %% Logging Layer
    subgraph LOG["📝 Logging & Monitoring"]
        LOG1[["🔗 Correlation Context<br/>Session & Operation IDs"]]
        LOG2[["📄 Serilog Logging<br/>Console + File output"]]
        LOG3[["📊 Performance Metrics<br/>Throughput, timing"]]
    end
    
    %% Service Layer
    subgraph SERVICES["🔧 Service Layer"]
        SRV1[["📊 BulkImportService<br/>Main orchestration"]]
        SRV2[["🎲 SqlDataGenerationService<br/>SQL data generation"]]
        SRV3[["📋 CommandLineService<br/>Argument parsing"]]
        SRV4[["👤 InteractiveService<br/>User prompts"]]
        SRV5[["🎯 BulkImportHostedService<br/>Application lifecycle"]]
    end
    
    %% External Dependencies
    subgraph EXT["🔗 External Dependencies"]
        EXT1[["📦 Microsoft.Data.SqlClient<br/>Database connectivity"]]
        EXT2[["📝 Serilog<br/>Structured logging"]]
        EXT3[["🔄 Polly<br/>Retry policies"]]
        EXT4[["🏗️ Microsoft.Extensions.Hosting<br/>Application hosting"]]
    end
    
    %% Connect external systems and dependencies
    L2a -.-> DB1
    L4a -.-> DB1
    N1 -.-> DB2
    P1 -.-> DB2
    P2 -.-> DB2
    P2c -.-> DB3
    
    J1 -.-> LOG1
    R -.-> LOG2
    T -.-> LOG3
    
    D -.-> CONFIG
    I1 -.-> CFG1
    L -.-> CFG2
    J2 -.-> CFG3
    
    F -.-> SRV5
    J -.-> SRV1
    L -.-> SRV2
    B -.-> SRV3
    H -.-> SRV4
    
    %% External Dependencies Connections
    SRV2 -.-> EXT1
    LOG2 -.-> EXT2
    EH2 -.-> EXT3
    SRV5 -.-> EXT4
    
    %% Add documentation
    subgraph DOCS["📚 Documentation & Resources"]
        DOC1[["📖 README.md<br/>User guide & examples"]]
        DOC2[["📋 SQL Scripts<br/>Database setup & procedures"]]
        DOC3[["⚙️ Configuration Examples<br/>appsettings.json templates"]]
        DOC4[["📊 Migration Summary<br/>CSV to SQL transition"]]
    end
    
    %% Style the diagram
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef database fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef error fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class A,F entryPoint
    class B,G,H6,I1,K,L1,L3,N4,O,EH1 decision
    class J,L,N,P1,P2,Q,R,S,T process
    class DB1,DB2,DB3 database
    class EH2,EH3,EH4,Z1 error
    class U success

%% ================================================================================
%% Usage Examples and Command Line Patterns
%% ================================================================================
%% 
%% Interactive Mode (Default):
%% > BulkImporter
%% 
%% Non-Interactive Examples:
%% > BulkImporter --drivers 1000 --vehicles 500 --non-interactive
%% > BulkImporter --generate --dry-run --batch-size 2000
%% > BulkImporter --drivers 10000 --vehicles 5000 --non-interactive
%% 
%% Dry Run Testing:
%% > BulkImporter --generate --dry-run
%% > BulkImporter --drivers 1000 --dry-run --non-interactive
%% 
%% Configuration Override:
%% > FLEETXQ_BULKIMPORTER_BulkImporter__DefaultBatchSize=5000 BulkImporter
%% 
%% Database Dependencies:
%% - SQL Server with FleetXQ database
%% - Staging schema and tables (created by SQL scripts)
%% - Stored procedures for validation and processing
%% - Appropriate database permissions for bulk operations
%% 
%% Key Features:
%% - Pure SQL-based data generation (no CSV files required)
%% - Configurable batch processing for memory efficiency
%% - Comprehensive error handling and retry policies
%% - Session tracking with correlation IDs
%% - Structured logging with Serilog
%% - Dry-run mode for safe testing
%% ================================================================================
