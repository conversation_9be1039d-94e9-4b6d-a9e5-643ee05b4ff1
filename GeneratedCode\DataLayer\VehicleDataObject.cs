﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'Vehicle'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class VehicleDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("CanruleId")]
		protected Nullable<System.Guid> _canruleId;
		[JsonProperty ("ChecklistSettingsId")]
		protected Nullable<System.Guid> _checklistSettingsId;
		[JsonProperty ("CustomerId")]
		protected System.Guid _customerId;
		[JsonProperty ("DehireTime")]
		protected Nullable<System.DateTime> _dehireTime;
		[JsonProperty("DehireTime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _dehireTime_WithTimezoneOffset;
		[JsonProperty ("DeletedAtUtc")]
		protected Nullable<System.DateTime> _deletedAtUtc;
		[JsonProperty("DeletedAtUtc_WithTimezoneOffset")]
		protected System.DateTimeOffset? _deletedAtUtc_WithTimezoneOffset;
		[JsonProperty ("DepartmentChecklistId")]
		protected Nullable<System.Guid> _departmentChecklistId;
		[JsonProperty ("DepartmentId")]
		protected System.Guid _departmentId;
		[JsonProperty ("Description")]
		protected System.String _description;
		[JsonProperty ("DriverId")]
		protected Nullable<System.Guid> _driverId;
		[JsonProperty ("FirmwareId")]
		protected Nullable<System.Guid> _firmwareId;
		[JsonProperty ("GPSDateTimeDisplay")]
		protected System.String _gPSDateTimeDisplay;
		[JsonProperty ("HireNo")]
		protected System.String _hireNo;
		[JsonProperty ("HireTime")]
		protected Nullable<System.DateTime> _hireTime;
		[JsonProperty("HireTime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _hireTime_WithTimezoneOffset;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("IDLETimer")]
		protected Nullable<System.Int32> _iDLETimer;
		[JsonProperty ("ImpactLockout")]
		protected System.Boolean _impactLockout;
		[JsonProperty ("InspectionId")]
		protected Nullable<System.Guid> _inspectionId;
		[JsonProperty ("IsCanbus")]
		protected System.Boolean _isCanbus;
		[JsonProperty ("LastSessionDate")]
		protected Nullable<System.DateTime> _lastSessionDate;
		[JsonProperty("LastSessionDate_WithTimezoneOffset")]
		protected System.DateTimeOffset? _lastSessionDate_WithTimezoneOffset;
		[JsonProperty ("LastSessionDateTzAdjusted")]
		protected Nullable<System.DateTime> _lastSessionDateTzAdjusted;
		[JsonProperty("LastSessionDateTzAdjusted_WithTimezoneOffset")]
		protected System.DateTimeOffset? _lastSessionDateTzAdjusted_WithTimezoneOffset;
		[JsonProperty ("LastSessionDateTzAdjustedDisplay")]
		protected System.String _lastSessionDateTzAdjustedDisplay;
		[JsonProperty ("LastSessionId")]
		protected System.String _lastSessionId;
		[JsonProperty ("ModelId")]
		protected System.Guid _modelId;
		[JsonProperty ("ModuleId1")]
		protected System.Guid _moduleId1;
		[JsonProperty ("ModuleIsConnected")]
		protected System.Boolean _moduleIsConnected;
		[JsonProperty ("ModuleSwapNote")]
		protected System.String _moduleSwapNote;
		[JsonProperty ("OnHire")]
		protected System.Boolean _onHire;
		[JsonProperty ("PersonId")]
		protected Nullable<System.Guid> _personId;
		[JsonProperty ("SerialNo")]
		protected System.String _serialNo;
		[JsonProperty ("ServiceSettingsId")]
		protected Nullable<System.Guid> _serviceSettingsId;
		[JsonProperty ("SiteId")]
		protected System.Guid _siteId;
		[JsonProperty ("TimeoutEnabled")]
		protected System.Boolean _timeoutEnabled;
		[JsonProperty ("VehicleImage")]
		protected System.String _vehicleImage;
		[JsonProperty ("VehicleImageFileSize")]
		protected Nullable<System.Int32> _vehicleImageFileSize;
		[JsonProperty ("VehicleImageInternalName")]
		protected System.String _vehicleImageInternalName;
		[JsonProperty ("VehicleOtherSettingsId")]
		protected Nullable<System.Guid> _vehicleOtherSettingsId;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)



		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _canrule_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_canrule_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }



		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _checklistSettings_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_checklistSettings_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }



		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _customer_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_customer_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _department_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_department_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _departmentChecklist_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_departmentChecklist_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }




		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _driver_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_driver_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _firmware_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_firmware_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }




		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _inspection_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_inspection_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }



		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _model_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_model_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _module_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_module_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }







		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _person_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_person_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }





		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _serviceSettings_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_serviceSettings_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }



		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _site_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_site_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }













		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _vehicleOtherSettings_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_vehicleOtherSettings_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }







		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public VehicleDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
			SetTimeoutEnabledValue(false, false, false);
			SetImpactLockoutValue(false, false, false);
			SetModuleIsConnectedValue(false, false, false);
			SetIsCanbusValue(false, false, false);
			SetOnHireValue(true, false, false);
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public VehicleDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public VehicleDataObject Initialize(System.Guid id)
		{
			this._id = id;
			SetTimeoutEnabledValue(false, false, false);
			SetImpactLockoutValue(false, false, false);
			SetModuleIsConnectedValue(false, false, false);
			SetIsCanbusValue(false, false, false);
			SetOnHireValue(true, false, false);
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public VehicleDataObject Initialize(VehicleDataObject template, bool deepCopy)
		{
			this.SetDehireTimeValue(template.DehireTime, false, false);
			this._dehireTime_WithTimezoneOffset = template._dehireTime_WithTimezoneOffset;
			this.SetDeletedAtUtcValue(template.DeletedAtUtc, false, false);
			this._deletedAtUtc_WithTimezoneOffset = template._deletedAtUtc_WithTimezoneOffset;
			this.SetHireTimeValue(template.HireTime, false, false);
			this._hireTime_WithTimezoneOffset = template._hireTime_WithTimezoneOffset;
			this.SetLastSessionDateValue(template.LastSessionDate, false, false);
			this._lastSessionDate_WithTimezoneOffset = template._lastSessionDate_WithTimezoneOffset;
			this.SetLastSessionDateTzAdjustedValue(template.LastSessionDateTzAdjusted, false, false);
			this._lastSessionDateTzAdjusted_WithTimezoneOffset = template._lastSessionDateTzAdjusted_WithTimezoneOffset;
			this.SetCanruleIdValue(template.CanruleId, false, false);
			this.SetChecklistSettingsIdValue(template.ChecklistSettingsId, false, false);
			this.SetCustomerIdValue(template.CustomerId, false, false);
			this.SetDepartmentChecklistIdValue(template.DepartmentChecklistId, false, false);
			this.SetDepartmentIdValue(template.DepartmentId, false, false);
			this.SetDescriptionValue(template.Description, false, false);
			this.SetDriverIdValue(template.DriverId, false, false);
			this.SetFirmwareIdValue(template.FirmwareId, false, false);
			this.SetGPSDateTimeDisplayValue(template.GPSDateTimeDisplay, false, false);
			this.SetHireNoValue(template.HireNo, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetIDLETimerValue(template.IDLETimer, false, false);
			this.SetImpactLockoutValue(template.ImpactLockout, false, false);
			this.SetInspectionIdValue(template.InspectionId, false, false);
			this.SetIsCanbusValue(template.IsCanbus, false, false);
			this.SetLastSessionDateTzAdjustedDisplayValue(template.LastSessionDateTzAdjustedDisplay, false, false);
			this.SetLastSessionIdValue(template.LastSessionId, false, false);
			this.SetModelIdValue(template.ModelId, false, false);
			this.SetModuleId1Value(template.ModuleId1, false, false);
			this.SetModuleIsConnectedValue(template.ModuleIsConnected, false, false);
			this.SetModuleSwapNoteValue(template.ModuleSwapNote, false, false);
			this.SetOnHireValue(template.OnHire, false, false);
			this.SetPersonIdValue(template.PersonId, false, false);
			this.SetSerialNoValue(template.SerialNo, false, false);
			this.SetServiceSettingsIdValue(template.ServiceSettingsId, false, false);
			this.SetSiteIdValue(template.SiteId, false, false);
			this.SetTimeoutEnabledValue(template.TimeoutEnabled, false, false);
			this.SetVehicleImageValue(template.VehicleImage, false, false);
			this.SetVehicleImageFileSizeValue(template.VehicleImageFileSize, false, false);
			this.SetVehicleImageInternalNameValue(template.VehicleImageInternalName, false, false);
			this.SetVehicleOtherSettingsIdValue(template.VehicleOtherSettingsId, false, false);
 
 
 
 
			this._canrule_NewObjectId = template._canrule_NewObjectId;
 
 
			this._checklistSettings_NewObjectId = template._checklistSettings_NewObjectId;
 
 
			this._customer_NewObjectId = template._customer_NewObjectId;
 
			this._department_NewObjectId = template._department_NewObjectId;
 
			this._departmentChecklist_NewObjectId = template._departmentChecklist_NewObjectId;
 
 
 
			this._driver_NewObjectId = template._driver_NewObjectId;
 
			this._firmware_NewObjectId = template._firmware_NewObjectId;
 
 
 
			this._inspection_NewObjectId = template._inspection_NewObjectId;
 
 
			this._model_NewObjectId = template._model_NewObjectId;
 
			this._module_NewObjectId = template._module_NewObjectId;
 
 
 
 
 
 
			this._person_NewObjectId = template._person_NewObjectId;
 
 
 
 
			this._serviceSettings_NewObjectId = template._serviceSettings_NewObjectId;
 
 
			this._site_NewObjectId = template._site_NewObjectId;
 
 
 
 
 
 
 
 
 
 
 
 
			this._vehicleOtherSettings_NewObjectId = template._vehicleOtherSettings_NewObjectId;
 
 
 
 
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual VehicleDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual VehicleDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var vehicleSource = sourceObject as VehicleDataObject;

			if (ReferenceEquals(null, vehicleSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetCanruleIdValue(vehicleSource.CanruleId, false, false);
			this.SetChecklistSettingsIdValue(vehicleSource.ChecklistSettingsId, false, false);
			this.SetCustomerIdValue(vehicleSource.CustomerId, false, false);
			this.SetDehireTimeValue(vehicleSource.DehireTime, false, false);
			this.SetDeletedAtUtcValue(vehicleSource.DeletedAtUtc, false, false);
			this.SetDepartmentChecklistIdValue(vehicleSource.DepartmentChecklistId, false, false);
			this.SetDepartmentIdValue(vehicleSource.DepartmentId, false, false);
			this.SetDescriptionValue(vehicleSource.Description, false, false);
			this.SetDriverIdValue(vehicleSource.DriverId, false, false);
			this.SetFirmwareIdValue(vehicleSource.FirmwareId, false, false);
			this.SetGPSDateTimeDisplayValue(vehicleSource.GPSDateTimeDisplay, false, false);
			this.SetHireNoValue(vehicleSource.HireNo, false, false);
			this.SetHireTimeValue(vehicleSource.HireTime, false, false);
			this.SetIdValue(vehicleSource.Id, false, false);
			this.SetIDLETimerValue(vehicleSource.IDLETimer, false, false);
			this.SetImpactLockoutValue(vehicleSource.ImpactLockout, false, false);
			this.SetInspectionIdValue(vehicleSource.InspectionId, false, false);
			this.SetIsCanbusValue(vehicleSource.IsCanbus, false, false);
			this.SetLastSessionDateValue(vehicleSource.LastSessionDate, false, false);
			this.SetLastSessionDateTzAdjustedValue(vehicleSource.LastSessionDateTzAdjusted, false, false);
			this.SetLastSessionDateTzAdjustedDisplayValue(vehicleSource.LastSessionDateTzAdjustedDisplay, false, false);
			this.SetLastSessionIdValue(vehicleSource.LastSessionId, false, false);
			this.SetModelIdValue(vehicleSource.ModelId, false, false);
			this.SetModuleId1Value(vehicleSource.ModuleId1, false, false);
			this.SetModuleIsConnectedValue(vehicleSource.ModuleIsConnected, false, false);
			this.SetModuleSwapNoteValue(vehicleSource.ModuleSwapNote, false, false);
			this.SetOnHireValue(vehicleSource.OnHire, false, false);
			this.SetPersonIdValue(vehicleSource.PersonId, false, false);
			this.SetSerialNoValue(vehicleSource.SerialNo, false, false);
			this.SetServiceSettingsIdValue(vehicleSource.ServiceSettingsId, false, false);
			this.SetSiteIdValue(vehicleSource.SiteId, false, false);
			this.SetTimeoutEnabledValue(vehicleSource.TimeoutEnabled, false, false);
			this.SetVehicleImageValue(vehicleSource.VehicleImage, false, false);
			this.SetVehicleImageFileSizeValue(vehicleSource.VehicleImageFileSize, false, false);
			this.SetVehicleImageInternalNameValue(vehicleSource.VehicleImageInternalName, false, false);
			this.SetVehicleOtherSettingsIdValue(vehicleSource.VehicleOtherSettingsId, false, false);



			this._canrule_NewObjectId = (sourceObject as VehicleDataObject)._canrule_NewObjectId;


			this._checklistSettings_NewObjectId = (sourceObject as VehicleDataObject)._checklistSettings_NewObjectId;


			this._customer_NewObjectId = (sourceObject as VehicleDataObject)._customer_NewObjectId;

			this._department_NewObjectId = (sourceObject as VehicleDataObject)._department_NewObjectId;

			this._departmentChecklist_NewObjectId = (sourceObject as VehicleDataObject)._departmentChecklist_NewObjectId;



			this._driver_NewObjectId = (sourceObject as VehicleDataObject)._driver_NewObjectId;

			this._firmware_NewObjectId = (sourceObject as VehicleDataObject)._firmware_NewObjectId;



			this._inspection_NewObjectId = (sourceObject as VehicleDataObject)._inspection_NewObjectId;


			this._model_NewObjectId = (sourceObject as VehicleDataObject)._model_NewObjectId;

			this._module_NewObjectId = (sourceObject as VehicleDataObject)._module_NewObjectId;






			this._person_NewObjectId = (sourceObject as VehicleDataObject)._person_NewObjectId;




			this._serviceSettings_NewObjectId = (sourceObject as VehicleDataObject)._serviceSettings_NewObjectId;


			this._site_NewObjectId = (sourceObject as VehicleDataObject)._site_NewObjectId;












			this._vehicleOtherSettings_NewObjectId = (sourceObject as VehicleDataObject)._vehicleOtherSettings_NewObjectId;






			if (deepCopy)
			{
				this.ObjectsDataSet = vehicleSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(vehicleSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as VehicleDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {



			if (this._canrule_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._canrule_NewObjectId))
				{
                    this._canrule_NewObjectId = null;
				}
                else
				{
					this._canrule_NewObjectId = datasetMergingInternalIdMapping[(int) this._canrule_NewObjectId];
				}
			}


			if (this._checklistSettings_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._checklistSettings_NewObjectId))
				{
                    this._checklistSettings_NewObjectId = null;
				}
                else
				{
					this._checklistSettings_NewObjectId = datasetMergingInternalIdMapping[(int) this._checklistSettings_NewObjectId];
				}
			}


			if (this._customer_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._customer_NewObjectId))
				{
                    this._customer_NewObjectId = null;
				}
                else
				{
					this._customer_NewObjectId = datasetMergingInternalIdMapping[(int) this._customer_NewObjectId];
				}
			}

			if (this._department_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._department_NewObjectId))
				{
                    this._department_NewObjectId = null;
				}
                else
				{
					this._department_NewObjectId = datasetMergingInternalIdMapping[(int) this._department_NewObjectId];
				}
			}

			if (this._departmentChecklist_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._departmentChecklist_NewObjectId))
				{
                    this._departmentChecklist_NewObjectId = null;
				}
                else
				{
					this._departmentChecklist_NewObjectId = datasetMergingInternalIdMapping[(int) this._departmentChecklist_NewObjectId];
				}
			}



			if (this._driver_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._driver_NewObjectId))
				{
                    this._driver_NewObjectId = null;
				}
                else
				{
					this._driver_NewObjectId = datasetMergingInternalIdMapping[(int) this._driver_NewObjectId];
				}
			}

			if (this._firmware_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._firmware_NewObjectId))
				{
                    this._firmware_NewObjectId = null;
				}
                else
				{
					this._firmware_NewObjectId = datasetMergingInternalIdMapping[(int) this._firmware_NewObjectId];
				}
			}



			if (this._inspection_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._inspection_NewObjectId))
				{
                    this._inspection_NewObjectId = null;
				}
                else
				{
					this._inspection_NewObjectId = datasetMergingInternalIdMapping[(int) this._inspection_NewObjectId];
				}
			}


			if (this._model_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._model_NewObjectId))
				{
                    this._model_NewObjectId = null;
				}
                else
				{
					this._model_NewObjectId = datasetMergingInternalIdMapping[(int) this._model_NewObjectId];
				}
			}

			if (this._module_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._module_NewObjectId))
				{
                    this._module_NewObjectId = null;
				}
                else
				{
					this._module_NewObjectId = datasetMergingInternalIdMapping[(int) this._module_NewObjectId];
				}
			}






			if (this._person_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._person_NewObjectId))
				{
                    this._person_NewObjectId = null;
				}
                else
				{
					this._person_NewObjectId = datasetMergingInternalIdMapping[(int) this._person_NewObjectId];
				}
			}




			if (this._serviceSettings_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._serviceSettings_NewObjectId))
				{
                    this._serviceSettings_NewObjectId = null;
				}
                else
				{
					this._serviceSettings_NewObjectId = datasetMergingInternalIdMapping[(int) this._serviceSettings_NewObjectId];
				}
			}


			if (this._site_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._site_NewObjectId))
				{
                    this._site_NewObjectId = null;
				}
                else
				{
					this._site_NewObjectId = datasetMergingInternalIdMapping[(int) this._site_NewObjectId];
				}
			}












			if (this._vehicleOtherSettings_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._vehicleOtherSettings_NewObjectId))
				{
                    this._vehicleOtherSettings_NewObjectId = null;
				}
                else
				{
					this._vehicleOtherSettings_NewObjectId = datasetMergingInternalIdMapping[(int) this._vehicleOtherSettings_NewObjectId];
				}
			}






		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<AllVehicleCalibrationStoreProcedureDataObject> _allVehicleCalibrationStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<AllVehicleCalibrationStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __allVehicleCalibrationStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __allVehicleCalibrationStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "AllVehicleCalibrationStoreProcedureItems", which is a collection of AllVehicleCalibrationStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of AllVehicleCalibrationStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<AllVehicleCalibrationStoreProcedureDataObject>> LoadAllVehicleCalibrationStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadAllVehicleCalibrationStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<AllVehicleCalibrationStoreProcedureDataObject>> LoadAllVehicleCalibrationStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __allVehicleCalibrationStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__allVehicleCalibrationStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _allVehicleCalibrationStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __allVehicleCalibrationStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetAllVehicleCalibrationStoreProcedureItemsAsync(false);
            }
            finally
            {
                __allVehicleCalibrationStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<AllVehicleCalibrationStoreProcedureDataObject> AllVehicleCalibrationStoreProcedureItems 
		{
			get
			{			
				return GetAllVehicleCalibrationStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeAllVehicleCalibrationStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("AllVehicleCalibrationStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<AllVehicleCalibrationStoreProcedureDataObject>> GetAllVehicleCalibrationStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__allVehicleCalibrationStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadAllVehicleCalibrationStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var allVehicleCalibrationStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<AllVehicleCalibrationStoreProcedureDataObject>(this, "AllVehicleCalibrationStoreProcedureItems");							
			allVehicleCalibrationStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(AllVehicleCalibrationStoreProcedureItems_CollectionChanged);
				
			return allVehicleCalibrationStoreProcedureItems;
		}

        private void AllVehicleCalibrationStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as AllVehicleCalibrationStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : AllVehicleCalibrationStoreProcedure", "VehicleDataObject.AllVehicleCalibrationStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add AllVehicleCalibrationStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as AllVehicleCalibrationStoreProcedureDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject> _allVORSessionsPerVehicleStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __allVORSessionsPerVehicleStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __allVORSessionsPerVehicleStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "AllVORSessionsPerVehicleStoreProcedureItems", which is a collection of AllVORSessionsPerVehicleStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of AllVORSessionsPerVehicleStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<AllVORSessionsPerVehicleStoreProcedureDataObject>> LoadAllVORSessionsPerVehicleStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadAllVORSessionsPerVehicleStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<AllVORSessionsPerVehicleStoreProcedureDataObject>> LoadAllVORSessionsPerVehicleStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __allVORSessionsPerVehicleStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__allVORSessionsPerVehicleStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _allVORSessionsPerVehicleStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __allVORSessionsPerVehicleStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetAllVORSessionsPerVehicleStoreProcedureItemsAsync(false);
            }
            finally
            {
                __allVORSessionsPerVehicleStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<AllVORSessionsPerVehicleStoreProcedureDataObject> AllVORSessionsPerVehicleStoreProcedureItems 
		{
			get
			{			
				return GetAllVORSessionsPerVehicleStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeAllVORSessionsPerVehicleStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("AllVORSessionsPerVehicleStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<AllVORSessionsPerVehicleStoreProcedureDataObject>> GetAllVORSessionsPerVehicleStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__allVORSessionsPerVehicleStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadAllVORSessionsPerVehicleStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var allVORSessionsPerVehicleStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<AllVORSessionsPerVehicleStoreProcedureDataObject>(this, "AllVORSessionsPerVehicleStoreProcedureItems");							
			allVORSessionsPerVehicleStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(AllVORSessionsPerVehicleStoreProcedureItems_CollectionChanged);
				
			return allVORSessionsPerVehicleStoreProcedureItems;
		}

        private void AllVORSessionsPerVehicleStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as AllVORSessionsPerVehicleStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : AllVORSessionsPerVehicleStoreProcedure", "VehicleDataObject.AllVORSessionsPerVehicleStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add AllVORSessionsPerVehicleStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as AllVORSessionsPerVehicleStoreProcedureDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<BroadcastMessageHistoryDataObject> _broadcastMessageHistoryService => _serviceProvider.GetRequiredService<IDataProvider<BroadcastMessageHistoryDataObject>>();

		private readonly SemaphoreSlim __broadcastMessageHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __broadcastMessageHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "BroadcastMessageHistoryItems", which is a collection of BroadcastMessageHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of BroadcastMessageHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<BroadcastMessageHistoryDataObject>> LoadBroadcastMessageHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadBroadcastMessageHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<BroadcastMessageHistoryDataObject>> LoadBroadcastMessageHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __broadcastMessageHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__broadcastMessageHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _broadcastMessageHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __broadcastMessageHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetBroadcastMessageHistoryItemsAsync(false);
            }
            finally
            {
                __broadcastMessageHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<BroadcastMessageHistoryDataObject> BroadcastMessageHistoryItems 
		{
			get
			{			
				return GetBroadcastMessageHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeBroadcastMessageHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("BroadcastMessageHistoryItems");
		}

		public virtual async Task<DataObjectCollection<BroadcastMessageHistoryDataObject>> GetBroadcastMessageHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__broadcastMessageHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadBroadcastMessageHistoryItemsAsync(forceReload : forceReload);
			}
			var broadcastMessageHistoryItems = ObjectsDataSet.GetRelatedObjects<BroadcastMessageHistoryDataObject>(this, "BroadcastMessageHistoryItems");							
			broadcastMessageHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(BroadcastMessageHistoryItems_CollectionChanged);
				
			return broadcastMessageHistoryItems;
		}

        private void BroadcastMessageHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as BroadcastMessageHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : BroadcastMessageHistory", "VehicleDataObject.BroadcastMessageHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add BroadcastMessageHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as BroadcastMessageHistoryDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CanruleDataObject> _canruleService => _serviceProvider.GetRequiredService<IDataProvider<CanruleDataObject>>();
      public virtual void SetCanruleValue(CanruleDataObject valueToSet)
		{
			SetCanruleValue(valueToSet, true, true);
		}

        public virtual void SetCanruleValue(CanruleDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			CanruleDataObject existing_canrule = null ;

			if ( !(this.CanruleId == null || ObjectsDataSet == null))
			{
				CanruleDataObject key;

				if (this._canrule_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<CanruleDataObject>().Initialize((System.Guid)this.CanruleId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<CanruleDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._canrule_NewObjectId;			
				}

				existing_canrule = (CanruleDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_canrule ,valueToSet))
            {
                if (valueToSet == null)
                {
					_canrule_NewObjectId = null;
					_canruleId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Canrule", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_canrule_NewObjectId != valueToSet.InternalObjectId)
					{
						_canrule_NewObjectId = valueToSet.InternalObjectId;
						_canruleId = valueToSet.Id;
						OnPropertyChanged("CanruleId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_canruleId != valueToSet.Id)
					{
						_canrule_NewObjectId = null;

						_canruleId = valueToSet.Id;
						OnPropertyChanged("CanruleId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_canrule_NewObjectId = null;
					_canruleId = null;
					
				OnPropertyChanged("CanruleId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_canrule ,valueToSet))
				OnPropertyChanged("Canrule", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __canruleSemaphore = new SemaphoreSlim(1, 1);
		private bool __canruleAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Canrule", which is a CanruleDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a CanruleDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<CanruleDataObject> LoadCanruleAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCanruleAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<CanruleDataObject> LoadCanruleAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __canruleSemaphore.WaitAsync();
			
	        try
            {
                if (!__canruleAlreadyLazyLoaded || forceReload)
                {
								
					if (this.CanruleId == null)
					{
						return null;
					}
				
					CanruleDataObject canrule = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __canruleAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						canrule = _serviceProvider.GetRequiredService<CanruleDataObject>().Initialize((System.Guid)this.CanruleId);
						canrule.IsNew = false;
						canrule = (CanruleDataObject)ObjectsDataSet.GetObject(canrule);
						if (canrule != null)
						{
							return canrule;
						}
					}

					canrule = await _canruleService.GetAsync(_serviceProvider.GetRequiredService<CanruleDataObject>().Initialize((System.Guid)this.CanruleId), parameters : parameters, skipSecurity: skipSecurity);

					SetCanruleValue(canrule, false, false);
					__canruleAlreadyLazyLoaded = true;				
		
					canrule = _serviceProvider.GetRequiredService<CanruleDataObject>().Initialize((System.Guid)this.CanruleId);
					canrule.IsNew = false;
					canrule = (CanruleDataObject)ObjectsDataSet.GetObject(canrule);
                    __canruleAlreadyLazyLoaded = true;
                }

                return await GetCanruleAsync(false);
            }
            finally
            {
                __canruleSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual CanruleDataObject Canrule 
		{
			get
			{			
				return GetCanruleAsync(true).Result;
			}
			set
			{
				SetCanruleValue(value);
			}
		}
		
		public virtual bool ShouldSerializeCanrule()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Canrule");
		}

		public virtual async Task<CanruleDataObject> GetCanruleAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			CanruleDataObject canrule;

				
			if (_canrule_NewObjectId != null)
			{
				canrule = _serviceProvider.GetRequiredService<CanruleDataObject>();
				canrule.IsNew = true;
				canrule.InternalObjectId = _canrule_NewObjectId;
				canrule = (CanruleDataObject)ObjectsDataSet.GetObject(canrule);
			}
			else
			{
				if (this.CanruleId == null)
					return null;
				if (CanruleId == null)
					canrule = null;
				else
				canrule = _serviceProvider.GetRequiredService<CanruleDataObject>().Initialize((System.Guid)this.CanruleId);
				canrule.IsNew = false;
				canrule = (CanruleDataObject)ObjectsDataSet.GetObject(canrule);
				
				if (allowLazyLoading && canrule == null && LazyLoadingEnabled && (!__canruleAlreadyLazyLoaded || forceReload))
				{
					canrule = await LoadCanruleAsync(forceReload : forceReload);
				}
			}
				
			return canrule;
		}

		public virtual Nullable<System.Guid> CanruleForeignKey
		{
			get { return CanruleId; }
			set 
			{	
				CanruleId = value;
			}
			
		}
		

		protected IDataProvider<ChecklistFailurePerVechicleViewDataObject> _checklistFailurePerVechicleViewService => _serviceProvider.GetRequiredService<IDataProvider<ChecklistFailurePerVechicleViewDataObject>>();

		private readonly SemaphoreSlim __checklistFailurePerVechicleViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __checklistFailurePerVechicleViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ChecklistFailurePerVechicleViewItems", which is a collection of ChecklistFailurePerVechicleViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ChecklistFailurePerVechicleViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ChecklistFailurePerVechicleViewDataObject>> LoadChecklistFailurePerVechicleViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadChecklistFailurePerVechicleViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ChecklistFailurePerVechicleViewDataObject>> LoadChecklistFailurePerVechicleViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __checklistFailurePerVechicleViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__checklistFailurePerVechicleViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _checklistFailurePerVechicleViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __checklistFailurePerVechicleViewItemsAlreadyLazyLoaded = true;
                }

                return await GetChecklistFailurePerVechicleViewItemsAsync(false);
            }
            finally
            {
                __checklistFailurePerVechicleViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ChecklistFailurePerVechicleViewDataObject> ChecklistFailurePerVechicleViewItems 
		{
			get
			{			
				return GetChecklistFailurePerVechicleViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeChecklistFailurePerVechicleViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("ChecklistFailurePerVechicleViewItems");
		}

		public virtual async Task<DataObjectCollection<ChecklistFailurePerVechicleViewDataObject>> GetChecklistFailurePerVechicleViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__checklistFailurePerVechicleViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadChecklistFailurePerVechicleViewItemsAsync(forceReload : forceReload);
			}
			var checklistFailurePerVechicleViewItems = ObjectsDataSet.GetRelatedObjects<ChecklistFailurePerVechicleViewDataObject>(this, "ChecklistFailurePerVechicleViewItems");							
			checklistFailurePerVechicleViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ChecklistFailurePerVechicleViewItems_CollectionChanged);
				
			return checklistFailurePerVechicleViewItems;
		}

        private void ChecklistFailurePerVechicleViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ChecklistFailurePerVechicleViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ChecklistFailurePerVechicleView", "VehicleDataObject.ChecklistFailurePerVechicleViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add ChecklistFailurePerVechicleViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ChecklistFailurePerVechicleViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ChecklistSettingsDataObject> _checklistSettingsService => _serviceProvider.GetRequiredService<IDataProvider<ChecklistSettingsDataObject>>();
      public virtual void SetChecklistSettingsValue(ChecklistSettingsDataObject valueToSet)
		{
			SetChecklistSettingsValue(valueToSet, true, true);
		}

        public virtual void SetChecklistSettingsValue(ChecklistSettingsDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			ChecklistSettingsDataObject existing_checklistSettings = null ;

			if ( !(this.ChecklistSettingsId == null || ObjectsDataSet == null))
			{
				ChecklistSettingsDataObject key;

				if (this._checklistSettings_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<ChecklistSettingsDataObject>().Initialize((System.Guid)this.ChecklistSettingsId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<ChecklistSettingsDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._checklistSettings_NewObjectId;			
				}

				existing_checklistSettings = (ChecklistSettingsDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_checklistSettings ,valueToSet))
            {
                if (valueToSet == null)
                {
					_checklistSettings_NewObjectId = null;
					_checklistSettingsId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("ChecklistSettings", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_checklistSettings_NewObjectId != valueToSet.InternalObjectId)
					{
						_checklistSettings_NewObjectId = valueToSet.InternalObjectId;
						_checklistSettingsId = valueToSet.Id;
						OnPropertyChanged("ChecklistSettingsId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_checklistSettingsId != valueToSet.Id)
					{
						_checklistSettings_NewObjectId = null;

						_checklistSettingsId = valueToSet.Id;
						OnPropertyChanged("ChecklistSettingsId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_checklistSettings_NewObjectId = null;
					_checklistSettingsId = null;
					
				OnPropertyChanged("ChecklistSettingsId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_checklistSettings ,valueToSet))
				OnPropertyChanged("ChecklistSettings", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __checklistSettingsSemaphore = new SemaphoreSlim(1, 1);
		private bool __checklistSettingsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ChecklistSettings", which is a ChecklistSettingsDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a ChecklistSettingsDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<ChecklistSettingsDataObject> LoadChecklistSettingsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadChecklistSettingsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<ChecklistSettingsDataObject> LoadChecklistSettingsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __checklistSettingsSemaphore.WaitAsync();
			
	        try
            {
                if (!__checklistSettingsAlreadyLazyLoaded || forceReload)
                {
								
					if (this.ChecklistSettingsId == null)
					{
						return null;
					}
				
					ChecklistSettingsDataObject checklistSettings = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __checklistSettingsAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						checklistSettings = _serviceProvider.GetRequiredService<ChecklistSettingsDataObject>().Initialize((System.Guid)this.ChecklistSettingsId);
						checklistSettings.IsNew = false;
						checklistSettings = (ChecklistSettingsDataObject)ObjectsDataSet.GetObject(checklistSettings);
						if (checklistSettings != null)
						{
							return checklistSettings;
						}
					}

					checklistSettings = await _checklistSettingsService.GetAsync(_serviceProvider.GetRequiredService<ChecklistSettingsDataObject>().Initialize((System.Guid)this.ChecklistSettingsId), parameters : parameters, skipSecurity: skipSecurity);

					SetChecklistSettingsValue(checklistSettings, false, false);
					__checklistSettingsAlreadyLazyLoaded = true;				
		
					checklistSettings = _serviceProvider.GetRequiredService<ChecklistSettingsDataObject>().Initialize((System.Guid)this.ChecklistSettingsId);
					checklistSettings.IsNew = false;
					checklistSettings = (ChecklistSettingsDataObject)ObjectsDataSet.GetObject(checklistSettings);
                    __checklistSettingsAlreadyLazyLoaded = true;
                }

                return await GetChecklistSettingsAsync(false);
            }
            finally
            {
                __checklistSettingsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual ChecklistSettingsDataObject ChecklistSettings 
		{
			get
			{			
				return GetChecklistSettingsAsync(true).Result;
			}
			set
			{
				SetChecklistSettingsValue(value);
			}
		}
		
		public virtual bool ShouldSerializeChecklistSettings()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("ChecklistSettings");
		}

		public virtual async Task<ChecklistSettingsDataObject> GetChecklistSettingsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			ChecklistSettingsDataObject checklistSettings;

				
			if (_checklistSettings_NewObjectId != null)
			{
				checklistSettings = _serviceProvider.GetRequiredService<ChecklistSettingsDataObject>();
				checklistSettings.IsNew = true;
				checklistSettings.InternalObjectId = _checklistSettings_NewObjectId;
				checklistSettings = (ChecklistSettingsDataObject)ObjectsDataSet.GetObject(checklistSettings);
			}
			else
			{
				if (this.ChecklistSettingsId == null)
					return null;
				if (ChecklistSettingsId == null)
					checklistSettings = null;
				else
				checklistSettings = _serviceProvider.GetRequiredService<ChecklistSettingsDataObject>().Initialize((System.Guid)this.ChecklistSettingsId);
				checklistSettings.IsNew = false;
				checklistSettings = (ChecklistSettingsDataObject)ObjectsDataSet.GetObject(checklistSettings);
				
				if (allowLazyLoading && checklistSettings == null && LazyLoadingEnabled && (!__checklistSettingsAlreadyLazyLoaded || forceReload))
				{
					checklistSettings = await LoadChecklistSettingsAsync(forceReload : forceReload);
				}
			}
				
			return checklistSettings;
		}

		public virtual Nullable<System.Guid> ChecklistSettingsForeignKey
		{
			get { return ChecklistSettingsId; }
			set 
			{	
				ChecklistSettingsId = value;
			}
			
		}
		

		protected IDataProvider<CurrentStatusVehicleViewDataObject> _currentStatusVehicleViewService => _serviceProvider.GetRequiredService<IDataProvider<CurrentStatusVehicleViewDataObject>>();

		private readonly SemaphoreSlim __currentStatusVehicleViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __currentStatusVehicleViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "CurrentStatusVehicleViewItems", which is a collection of CurrentStatusVehicleViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of CurrentStatusVehicleViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<CurrentStatusVehicleViewDataObject>> LoadCurrentStatusVehicleViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCurrentStatusVehicleViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<CurrentStatusVehicleViewDataObject>> LoadCurrentStatusVehicleViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __currentStatusVehicleViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__currentStatusVehicleViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _currentStatusVehicleViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __currentStatusVehicleViewItemsAlreadyLazyLoaded = true;
                }

                return await GetCurrentStatusVehicleViewItemsAsync(false);
            }
            finally
            {
                __currentStatusVehicleViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<CurrentStatusVehicleViewDataObject> CurrentStatusVehicleViewItems 
		{
			get
			{			
				return GetCurrentStatusVehicleViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeCurrentStatusVehicleViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("CurrentStatusVehicleViewItems");
		}

		public virtual async Task<DataObjectCollection<CurrentStatusVehicleViewDataObject>> GetCurrentStatusVehicleViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__currentStatusVehicleViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadCurrentStatusVehicleViewItemsAsync(forceReload : forceReload);
			}
			var currentStatusVehicleViewItems = ObjectsDataSet.GetRelatedObjects<CurrentStatusVehicleViewDataObject>(this, "CurrentStatusVehicleViewItems");							
			currentStatusVehicleViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(CurrentStatusVehicleViewItems_CollectionChanged);
				
			return currentStatusVehicleViewItems;
		}

        private void CurrentStatusVehicleViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as CurrentStatusVehicleViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : CurrentStatusVehicleView", "VehicleDataObject.CurrentStatusVehicleViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add CurrentStatusVehicleViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as CurrentStatusVehicleViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<CustomerDataObject> _customerService => _serviceProvider.GetRequiredService<IDataProvider<CustomerDataObject>>();
      public virtual void SetCustomerValue(CustomerDataObject valueToSet)
		{
			SetCustomerValue(valueToSet, true, true);
		}

        public virtual void SetCustomerValue(CustomerDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			CustomerDataObject existing_customer = null ;

			if ( !(ObjectsDataSet == null))
			{
				CustomerDataObject key;

				if (this._customer_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<CustomerDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._customer_NewObjectId;			
				}

				existing_customer = (CustomerDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_customer ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Customer", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_customer_NewObjectId != valueToSet.InternalObjectId)
					{
						_customer_NewObjectId = valueToSet.InternalObjectId;
						_customerId = valueToSet.Id;
						OnPropertyChanged("CustomerId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_customerId != valueToSet.Id)
					{
						_customer_NewObjectId = null;

						_customerId = valueToSet.Id;
						OnPropertyChanged("CustomerId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_customerId = Guid.Empty;
				OnPropertyChanged("CustomerId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_customer ,valueToSet))
				OnPropertyChanged("Customer", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __customerSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Customer", which is a CustomerDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a CustomerDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<CustomerDataObject> LoadCustomerAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<CustomerDataObject> LoadCustomerAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerAlreadyLazyLoaded || forceReload)
                {
								
					CustomerDataObject customer = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __customerAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						customer = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId);
						customer.IsNew = false;
						customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
						if (customer != null)
						{
							return customer;
						}
					}

					customer = await _customerService.GetAsync(_serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId), parameters : parameters, skipSecurity: skipSecurity);

					SetCustomerValue(customer, false, false);
					__customerAlreadyLazyLoaded = true;				
		
					customer = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId);
					customer.IsNew = false;
					customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
                    __customerAlreadyLazyLoaded = true;
                }

                return await GetCustomerAsync(false);
            }
            finally
            {
                __customerSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual CustomerDataObject Customer 
		{
			get
			{			
				return GetCustomerAsync(true).Result;
			}
			set
			{
				SetCustomerValue(value);
			}
		}
		
		public virtual bool ShouldSerializeCustomer()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Customer");
		}

		public virtual async Task<CustomerDataObject> GetCustomerAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			CustomerDataObject customer;

				
			if (_customer_NewObjectId != null)
			{
				customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
				customer.IsNew = true;
				customer.InternalObjectId = _customer_NewObjectId;
				customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
			}
			else
			{
				customer = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId);
				customer.IsNew = false;
				customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
				
				if (allowLazyLoading && customer == null && LazyLoadingEnabled && (!__customerAlreadyLazyLoaded || forceReload))
				{
					customer = await LoadCustomerAsync(forceReload : forceReload);
				}
			}
				
			return customer;
		}

		public virtual System.Guid CustomerForeignKey
		{
			get { return CustomerId; }
			set 
			{	
				CustomerId = value;
			}
			
		}
		

		protected IDataProvider<DepartmentDataObject> _departmentService => _serviceProvider.GetRequiredService<IDataProvider<DepartmentDataObject>>();
      public virtual void SetDepartmentValue(DepartmentDataObject valueToSet)
		{
			SetDepartmentValue(valueToSet, true, true);
		}

        public virtual void SetDepartmentValue(DepartmentDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DepartmentDataObject existing_department = null ;

			if ( !(ObjectsDataSet == null))
			{
				DepartmentDataObject key;

				if (this._department_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize(this.DepartmentId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DepartmentDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._department_NewObjectId;			
				}

				existing_department = (DepartmentDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_department ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Department", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_department_NewObjectId != valueToSet.InternalObjectId)
					{
						_department_NewObjectId = valueToSet.InternalObjectId;
						_departmentId = valueToSet.Id;
						OnPropertyChanged("DepartmentId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_departmentId != valueToSet.Id)
					{
						_department_NewObjectId = null;

						_departmentId = valueToSet.Id;
						OnPropertyChanged("DepartmentId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_departmentId = Guid.Empty;
				OnPropertyChanged("DepartmentId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_department ,valueToSet))
				OnPropertyChanged("Department", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __departmentSemaphore = new SemaphoreSlim(1, 1);
		private bool __departmentAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Department", which is a DepartmentDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DepartmentDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DepartmentDataObject> LoadDepartmentAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDepartmentAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DepartmentDataObject> LoadDepartmentAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __departmentSemaphore.WaitAsync();
			
	        try
            {
                if (!__departmentAlreadyLazyLoaded || forceReload)
                {
								
					DepartmentDataObject department = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __departmentAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						department = _serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize(this.DepartmentId);
						department.IsNew = false;
						department = (DepartmentDataObject)ObjectsDataSet.GetObject(department);
						if (department != null)
						{
							return department;
						}
					}

					department = await _departmentService.GetAsync(_serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize(this.DepartmentId), parameters : parameters, skipSecurity: skipSecurity);

					SetDepartmentValue(department, false, false);
					__departmentAlreadyLazyLoaded = true;				
		
					department = _serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize(this.DepartmentId);
					department.IsNew = false;
					department = (DepartmentDataObject)ObjectsDataSet.GetObject(department);
                    __departmentAlreadyLazyLoaded = true;
                }

                return await GetDepartmentAsync(false);
            }
            finally
            {
                __departmentSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DepartmentDataObject Department 
		{
			get
			{			
				return GetDepartmentAsync(true).Result;
			}
			set
			{
				SetDepartmentValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDepartment()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Department");
		}

		public virtual async Task<DepartmentDataObject> GetDepartmentAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DepartmentDataObject department;

				
			if (_department_NewObjectId != null)
			{
				department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
				department.IsNew = true;
				department.InternalObjectId = _department_NewObjectId;
				department = (DepartmentDataObject)ObjectsDataSet.GetObject(department);
			}
			else
			{
				department = _serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize(this.DepartmentId);
				department.IsNew = false;
				department = (DepartmentDataObject)ObjectsDataSet.GetObject(department);
				
				if (allowLazyLoading && department == null && LazyLoadingEnabled && (!__departmentAlreadyLazyLoaded || forceReload))
				{
					department = await LoadDepartmentAsync(forceReload : forceReload);
				}
			}
				
			return department;
		}

		public virtual System.Guid DepartmentForeignKey
		{
			get { return DepartmentId; }
			set 
			{	
				DepartmentId = value;
			}
			
		}
		

		protected IDataProvider<DepartmentChecklistDataObject> _departmentChecklistService => _serviceProvider.GetRequiredService<IDataProvider<DepartmentChecklistDataObject>>();
      public virtual void SetDepartmentChecklistValue(DepartmentChecklistDataObject valueToSet)
		{
			SetDepartmentChecklistValue(valueToSet, true, true);
		}

        public virtual void SetDepartmentChecklistValue(DepartmentChecklistDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DepartmentChecklistDataObject existing_departmentChecklist = null ;

			if ( !(this.DepartmentChecklistId == null || ObjectsDataSet == null))
			{
				DepartmentChecklistDataObject key;

				if (this._departmentChecklist_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>().Initialize((System.Guid)this.DepartmentChecklistId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._departmentChecklist_NewObjectId;			
				}

				existing_departmentChecklist = (DepartmentChecklistDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_departmentChecklist ,valueToSet))
            {
                if (valueToSet == null)
                {
					_departmentChecklist_NewObjectId = null;
					_departmentChecklistId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("DepartmentChecklist", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_departmentChecklist_NewObjectId != valueToSet.InternalObjectId)
					{
						_departmentChecklist_NewObjectId = valueToSet.InternalObjectId;
						_departmentChecklistId = valueToSet.Id;
						OnPropertyChanged("DepartmentChecklistId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_departmentChecklistId != valueToSet.Id)
					{
						_departmentChecklist_NewObjectId = null;

						_departmentChecklistId = valueToSet.Id;
						OnPropertyChanged("DepartmentChecklistId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_departmentChecklist_NewObjectId = null;
					_departmentChecklistId = null;
					
				OnPropertyChanged("DepartmentChecklistId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_departmentChecklist ,valueToSet))
				OnPropertyChanged("DepartmentChecklist", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __departmentChecklistSemaphore = new SemaphoreSlim(1, 1);
		private bool __departmentChecklistAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DepartmentChecklist", which is a DepartmentChecklistDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DepartmentChecklistDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DepartmentChecklistDataObject> LoadDepartmentChecklistAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDepartmentChecklistAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DepartmentChecklistDataObject> LoadDepartmentChecklistAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __departmentChecklistSemaphore.WaitAsync();
			
	        try
            {
                if (!__departmentChecklistAlreadyLazyLoaded || forceReload)
                {
								
					if (this.DepartmentChecklistId == null)
					{
						return null;
					}
				
					DepartmentChecklistDataObject departmentChecklist = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __departmentChecklistAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>().Initialize((System.Guid)this.DepartmentChecklistId);
						departmentChecklist.IsNew = false;
						departmentChecklist = (DepartmentChecklistDataObject)ObjectsDataSet.GetObject(departmentChecklist);
						if (departmentChecklist != null)
						{
							return departmentChecklist;
						}
					}

					departmentChecklist = await _departmentChecklistService.GetAsync(_serviceProvider.GetRequiredService<DepartmentChecklistDataObject>().Initialize((System.Guid)this.DepartmentChecklistId), parameters : parameters, skipSecurity: skipSecurity);

					SetDepartmentChecklistValue(departmentChecklist, false, false);
					__departmentChecklistAlreadyLazyLoaded = true;				
		
					departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>().Initialize((System.Guid)this.DepartmentChecklistId);
					departmentChecklist.IsNew = false;
					departmentChecklist = (DepartmentChecklistDataObject)ObjectsDataSet.GetObject(departmentChecklist);
                    __departmentChecklistAlreadyLazyLoaded = true;
                }

                return await GetDepartmentChecklistAsync(false);
            }
            finally
            {
                __departmentChecklistSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DepartmentChecklistDataObject DepartmentChecklist 
		{
			get
			{			
				return GetDepartmentChecklistAsync(true).Result;
			}
			set
			{
				SetDepartmentChecklistValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDepartmentChecklist()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("DepartmentChecklist");
		}

		public virtual async Task<DepartmentChecklistDataObject> GetDepartmentChecklistAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DepartmentChecklistDataObject departmentChecklist;

				
			if (_departmentChecklist_NewObjectId != null)
			{
				departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
				departmentChecklist.IsNew = true;
				departmentChecklist.InternalObjectId = _departmentChecklist_NewObjectId;
				departmentChecklist = (DepartmentChecklistDataObject)ObjectsDataSet.GetObject(departmentChecklist);
			}
			else
			{
				if (this.DepartmentChecklistId == null)
					return null;
				if (DepartmentChecklistId == null)
					departmentChecklist = null;
				else
				departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>().Initialize((System.Guid)this.DepartmentChecklistId);
				departmentChecklist.IsNew = false;
				departmentChecklist = (DepartmentChecklistDataObject)ObjectsDataSet.GetObject(departmentChecklist);
				
				if (allowLazyLoading && departmentChecklist == null && LazyLoadingEnabled && (!__departmentChecklistAlreadyLazyLoaded || forceReload))
				{
					departmentChecklist = await LoadDepartmentChecklistAsync(forceReload : forceReload);
				}
			}
				
			return departmentChecklist;
		}

		public virtual Nullable<System.Guid> DepartmentChecklistForeignKey
		{
			get { return DepartmentChecklistId; }
			set 
			{	
				DepartmentChecklistId = value;
			}
			
		}
		

		protected IDataProvider<DetailedSessionViewDataObject> _detailedSessionViewService => _serviceProvider.GetRequiredService<IDataProvider<DetailedSessionViewDataObject>>();

		private readonly SemaphoreSlim __detailedSessionViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __detailedSessionViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DetailedSessionViewItems", which is a collection of DetailedSessionViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DetailedSessionViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DetailedSessionViewDataObject>> LoadDetailedSessionViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDetailedSessionViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DetailedSessionViewDataObject>> LoadDetailedSessionViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __detailedSessionViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__detailedSessionViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _detailedSessionViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __detailedSessionViewItemsAlreadyLazyLoaded = true;
                }

                return await GetDetailedSessionViewItemsAsync(false);
            }
            finally
            {
                __detailedSessionViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DetailedSessionViewDataObject> DetailedSessionViewItems 
		{
			get
			{			
				return GetDetailedSessionViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDetailedSessionViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("DetailedSessionViewItems");
		}

		public virtual async Task<DataObjectCollection<DetailedSessionViewDataObject>> GetDetailedSessionViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__detailedSessionViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDetailedSessionViewItemsAsync(forceReload : forceReload);
			}
			var detailedSessionViewItems = ObjectsDataSet.GetRelatedObjects<DetailedSessionViewDataObject>(this, "DetailedSessionViewItems");							
			detailedSessionViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DetailedSessionViewItems_CollectionChanged);
				
			return detailedSessionViewItems;
		}

        private void DetailedSessionViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DetailedSessionViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DetailedSessionView", "VehicleDataObject.DetailedSessionViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add DetailedSessionViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DetailedSessionViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DetailedVORSessionStoreProcedureDataObject> _detailedVORSessionStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<DetailedVORSessionStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __detailedVORSessionStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DetailedVORSessionStoreProcedureItems", which is a collection of DetailedVORSessionStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DetailedVORSessionStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DetailedVORSessionStoreProcedureDataObject>> LoadDetailedVORSessionStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDetailedVORSessionStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DetailedVORSessionStoreProcedureDataObject>> LoadDetailedVORSessionStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __detailedVORSessionStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _detailedVORSessionStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetDetailedVORSessionStoreProcedureItemsAsync(false);
            }
            finally
            {
                __detailedVORSessionStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DetailedVORSessionStoreProcedureDataObject> DetailedVORSessionStoreProcedureItems 
		{
			get
			{			
				return GetDetailedVORSessionStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDetailedVORSessionStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("DetailedVORSessionStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<DetailedVORSessionStoreProcedureDataObject>> GetDetailedVORSessionStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDetailedVORSessionStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var detailedVORSessionStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<DetailedVORSessionStoreProcedureDataObject>(this, "DetailedVORSessionStoreProcedureItems");							
			detailedVORSessionStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DetailedVORSessionStoreProcedureItems_CollectionChanged);
				
			return detailedVORSessionStoreProcedureItems;
		}

        private void DetailedVORSessionStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DetailedVORSessionStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DetailedVORSessionStoreProcedure", "VehicleDataObject.DetailedVORSessionStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add DetailedVORSessionStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DetailedVORSessionStoreProcedureDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DriverDataObject> _driverService => _serviceProvider.GetRequiredService<IDataProvider<DriverDataObject>>();
      public virtual void SetDriverValue(DriverDataObject valueToSet)
		{
			SetDriverValue(valueToSet, true, true);
		}

        public virtual void SetDriverValue(DriverDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DriverDataObject existing_driver = null ;

			if ( !(this.DriverId == null || ObjectsDataSet == null))
			{
				DriverDataObject key;

				if (this._driver_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DriverDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._driver_NewObjectId;			
				}

				existing_driver = (DriverDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_driver ,valueToSet))
            {
                if (valueToSet == null)
                {
					_driver_NewObjectId = null;
					_driverId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Driver", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_driver_NewObjectId != valueToSet.InternalObjectId)
					{
						_driver_NewObjectId = valueToSet.InternalObjectId;
						_driverId = valueToSet.Id;
						OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_driverId != valueToSet.Id)
					{
						_driver_NewObjectId = null;

						_driverId = valueToSet.Id;
						OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_driver_NewObjectId = null;
					_driverId = null;
					
				OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_driver ,valueToSet))
				OnPropertyChanged("Driver", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __driverSemaphore = new SemaphoreSlim(1, 1);
		private bool __driverAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Driver", which is a DriverDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DriverDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DriverDataObject> LoadDriverAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDriverAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DriverDataObject> LoadDriverAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __driverSemaphore.WaitAsync();
			
	        try
            {
                if (!__driverAlreadyLazyLoaded || forceReload)
                {
								
					if (this.DriverId == null)
					{
						return null;
					}
				
					DriverDataObject driver = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __driverAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId);
						driver.IsNew = false;
						driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
						if (driver != null)
						{
							return driver;
						}
					}

					driver = await _driverService.GetAsync(_serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId), parameters : parameters, skipSecurity: skipSecurity);

					SetDriverValue(driver, false, false);
					__driverAlreadyLazyLoaded = true;				
		
					driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId);
					driver.IsNew = false;
					driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
                    __driverAlreadyLazyLoaded = true;
                }

                return await GetDriverAsync(false);
            }
            finally
            {
                __driverSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DriverDataObject Driver 
		{
			get
			{			
				return GetDriverAsync(true).Result;
			}
			set
			{
				SetDriverValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDriver()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Driver");
		}

		public virtual async Task<DriverDataObject> GetDriverAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DriverDataObject driver;

				
			if (_driver_NewObjectId != null)
			{
				driver = _serviceProvider.GetRequiredService<DriverDataObject>();
				driver.IsNew = true;
				driver.InternalObjectId = _driver_NewObjectId;
				driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
			}
			else
			{
				if (this.DriverId == null)
					return null;
				if (DriverId == null)
					driver = null;
				else
				driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize((System.Guid)this.DriverId);
				driver.IsNew = false;
				driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
				
				if (allowLazyLoading && driver == null && LazyLoadingEnabled && (!__driverAlreadyLazyLoaded || forceReload))
				{
					driver = await LoadDriverAsync(forceReload : forceReload);
				}
			}
				
			return driver;
		}

		public virtual Nullable<System.Guid> DriverForeignKey
		{
			get { return DriverId; }
			set 
			{	
				DriverId = value;
			}
			
		}
		

		protected IDataProvider<FirmwareDataObject> _firmwareService => _serviceProvider.GetRequiredService<IDataProvider<FirmwareDataObject>>();
      public virtual void SetFirmwareValue(FirmwareDataObject valueToSet)
		{
			SetFirmwareValue(valueToSet, true, true);
		}

        public virtual void SetFirmwareValue(FirmwareDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			FirmwareDataObject existing_firmware = null ;

			if ( !(this.FirmwareId == null || ObjectsDataSet == null))
			{
				FirmwareDataObject key;

				if (this._firmware_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<FirmwareDataObject>().Initialize((System.Guid)this.FirmwareId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<FirmwareDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._firmware_NewObjectId;			
				}

				existing_firmware = (FirmwareDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_firmware ,valueToSet))
            {
                if (valueToSet == null)
                {
					_firmware_NewObjectId = null;
					_firmwareId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Firmware", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_firmware_NewObjectId != valueToSet.InternalObjectId)
					{
						_firmware_NewObjectId = valueToSet.InternalObjectId;
						_firmwareId = valueToSet.Id;
						OnPropertyChanged("FirmwareId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_firmwareId != valueToSet.Id)
					{
						_firmware_NewObjectId = null;

						_firmwareId = valueToSet.Id;
						OnPropertyChanged("FirmwareId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_firmware_NewObjectId = null;
					_firmwareId = null;
					
				OnPropertyChanged("FirmwareId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_firmware ,valueToSet))
				OnPropertyChanged("Firmware", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __firmwareSemaphore = new SemaphoreSlim(1, 1);
		private bool __firmwareAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Firmware", which is a FirmwareDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a FirmwareDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<FirmwareDataObject> LoadFirmwareAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadFirmwareAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<FirmwareDataObject> LoadFirmwareAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __firmwareSemaphore.WaitAsync();
			
	        try
            {
                if (!__firmwareAlreadyLazyLoaded || forceReload)
                {
								
					if (this.FirmwareId == null)
					{
						return null;
					}
				
					FirmwareDataObject firmware = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __firmwareAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>().Initialize((System.Guid)this.FirmwareId);
						firmware.IsNew = false;
						firmware = (FirmwareDataObject)ObjectsDataSet.GetObject(firmware);
						if (firmware != null)
						{
							return firmware;
						}
					}

					firmware = await _firmwareService.GetAsync(_serviceProvider.GetRequiredService<FirmwareDataObject>().Initialize((System.Guid)this.FirmwareId), parameters : parameters, skipSecurity: skipSecurity);

					SetFirmwareValue(firmware, false, false);
					__firmwareAlreadyLazyLoaded = true;				
		
					firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>().Initialize((System.Guid)this.FirmwareId);
					firmware.IsNew = false;
					firmware = (FirmwareDataObject)ObjectsDataSet.GetObject(firmware);
                    __firmwareAlreadyLazyLoaded = true;
                }

                return await GetFirmwareAsync(false);
            }
            finally
            {
                __firmwareSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual FirmwareDataObject Firmware 
		{
			get
			{			
				return GetFirmwareAsync(true).Result;
			}
			set
			{
				SetFirmwareValue(value);
			}
		}
		
		public virtual bool ShouldSerializeFirmware()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Firmware");
		}

		public virtual async Task<FirmwareDataObject> GetFirmwareAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			FirmwareDataObject firmware;

				
			if (_firmware_NewObjectId != null)
			{
				firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>();
				firmware.IsNew = true;
				firmware.InternalObjectId = _firmware_NewObjectId;
				firmware = (FirmwareDataObject)ObjectsDataSet.GetObject(firmware);
			}
			else
			{
				if (this.FirmwareId == null)
					return null;
				if (FirmwareId == null)
					firmware = null;
				else
				firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>().Initialize((System.Guid)this.FirmwareId);
				firmware.IsNew = false;
				firmware = (FirmwareDataObject)ObjectsDataSet.GetObject(firmware);
				
				if (allowLazyLoading && firmware == null && LazyLoadingEnabled && (!__firmwareAlreadyLazyLoaded || forceReload))
				{
					firmware = await LoadFirmwareAsync(forceReload : forceReload);
				}
			}
				
			return firmware;
		}

		public virtual Nullable<System.Guid> FirmwareForeignKey
		{
			get { return FirmwareId; }
			set 
			{	
				FirmwareId = value;
			}
			
		}
		

		protected IDataProvider<GeneralProductivityPerVehicleViewDataObject> _generalProductivityPerVehicleViewService => _serviceProvider.GetRequiredService<IDataProvider<GeneralProductivityPerVehicleViewDataObject>>();

		private readonly SemaphoreSlim __generalProductivityPerVehicleViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __generalProductivityPerVehicleViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "GeneralProductivityPerVehicleViewItems", which is a collection of GeneralProductivityPerVehicleViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of GeneralProductivityPerVehicleViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<GeneralProductivityPerVehicleViewDataObject>> LoadGeneralProductivityPerVehicleViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadGeneralProductivityPerVehicleViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<GeneralProductivityPerVehicleViewDataObject>> LoadGeneralProductivityPerVehicleViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __generalProductivityPerVehicleViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__generalProductivityPerVehicleViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _generalProductivityPerVehicleViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __generalProductivityPerVehicleViewItemsAlreadyLazyLoaded = true;
                }

                return await GetGeneralProductivityPerVehicleViewItemsAsync(false);
            }
            finally
            {
                __generalProductivityPerVehicleViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<GeneralProductivityPerVehicleViewDataObject> GeneralProductivityPerVehicleViewItems 
		{
			get
			{			
				return GetGeneralProductivityPerVehicleViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeGeneralProductivityPerVehicleViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("GeneralProductivityPerVehicleViewItems");
		}

		public virtual async Task<DataObjectCollection<GeneralProductivityPerVehicleViewDataObject>> GetGeneralProductivityPerVehicleViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__generalProductivityPerVehicleViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadGeneralProductivityPerVehicleViewItemsAsync(forceReload : forceReload);
			}
			var generalProductivityPerVehicleViewItems = ObjectsDataSet.GetRelatedObjects<GeneralProductivityPerVehicleViewDataObject>(this, "GeneralProductivityPerVehicleViewItems");							
			generalProductivityPerVehicleViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(GeneralProductivityPerVehicleViewItems_CollectionChanged);
				
			return generalProductivityPerVehicleViewItems;
		}

        private void GeneralProductivityPerVehicleViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as GeneralProductivityPerVehicleViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : GeneralProductivityPerVehicleView", "VehicleDataObject.GeneralProductivityPerVehicleViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add GeneralProductivityPerVehicleViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as GeneralProductivityPerVehicleViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ImpactsForVehicleViewDataObject> _impactsForVehicleViewService => _serviceProvider.GetRequiredService<IDataProvider<ImpactsForVehicleViewDataObject>>();

		private readonly SemaphoreSlim __impactsForVehicleViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __impactsForVehicleViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ImpactsForVehicleViewItems", which is a collection of ImpactsForVehicleViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ImpactsForVehicleViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ImpactsForVehicleViewDataObject>> LoadImpactsForVehicleViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadImpactsForVehicleViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ImpactsForVehicleViewDataObject>> LoadImpactsForVehicleViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __impactsForVehicleViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__impactsForVehicleViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _impactsForVehicleViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __impactsForVehicleViewItemsAlreadyLazyLoaded = true;
                }

                return await GetImpactsForVehicleViewItemsAsync(false);
            }
            finally
            {
                __impactsForVehicleViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ImpactsForVehicleViewDataObject> ImpactsForVehicleViewItems 
		{
			get
			{			
				return GetImpactsForVehicleViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeImpactsForVehicleViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("ImpactsForVehicleViewItems");
		}

		public virtual async Task<DataObjectCollection<ImpactsForVehicleViewDataObject>> GetImpactsForVehicleViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__impactsForVehicleViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadImpactsForVehicleViewItemsAsync(forceReload : forceReload);
			}
			var impactsForVehicleViewItems = ObjectsDataSet.GetRelatedObjects<ImpactsForVehicleViewDataObject>(this, "ImpactsForVehicleViewItems");							
			impactsForVehicleViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ImpactsForVehicleViewItems_CollectionChanged);
				
			return impactsForVehicleViewItems;
		}

        private void ImpactsForVehicleViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ImpactsForVehicleViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ImpactsForVehicleView", "VehicleDataObject.ImpactsForVehicleViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add ImpactsForVehicleViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ImpactsForVehicleViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<InspectionDataObject> _inspectionService => _serviceProvider.GetRequiredService<IDataProvider<InspectionDataObject>>();
      public virtual void SetInspectionValue(InspectionDataObject valueToSet)
		{
			SetInspectionValue(valueToSet, true, true);
		}

        public virtual void SetInspectionValue(InspectionDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			InspectionDataObject existing_inspection = null ;

			if ( !(this.InspectionId == null || ObjectsDataSet == null))
			{
				InspectionDataObject key;

				if (this._inspection_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<InspectionDataObject>().Initialize((System.Guid)this.InspectionId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<InspectionDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._inspection_NewObjectId;			
				}

				existing_inspection = (InspectionDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_inspection ,valueToSet))
            {
                if (valueToSet == null)
                {
					_inspection_NewObjectId = null;
					_inspectionId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Inspection", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_inspection_NewObjectId != valueToSet.InternalObjectId)
					{
						_inspection_NewObjectId = valueToSet.InternalObjectId;
						_inspectionId = valueToSet.Id;
						OnPropertyChanged("InspectionId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_inspectionId != valueToSet.Id)
					{
						_inspection_NewObjectId = null;

						_inspectionId = valueToSet.Id;
						OnPropertyChanged("InspectionId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_inspection_NewObjectId = null;
					_inspectionId = null;
					
				OnPropertyChanged("InspectionId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_inspection ,valueToSet))
				OnPropertyChanged("Inspection", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __inspectionSemaphore = new SemaphoreSlim(1, 1);
		private bool __inspectionAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Inspection", which is a InspectionDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a InspectionDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<InspectionDataObject> LoadInspectionAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadInspectionAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<InspectionDataObject> LoadInspectionAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __inspectionSemaphore.WaitAsync();
			
	        try
            {
                if (!__inspectionAlreadyLazyLoaded || forceReload)
                {
								
					if (this.InspectionId == null)
					{
						return null;
					}
				
					InspectionDataObject inspection = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __inspectionAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						inspection = _serviceProvider.GetRequiredService<InspectionDataObject>().Initialize((System.Guid)this.InspectionId);
						inspection.IsNew = false;
						inspection = (InspectionDataObject)ObjectsDataSet.GetObject(inspection);
						if (inspection != null)
						{
							return inspection;
						}
					}

					inspection = await _inspectionService.GetAsync(_serviceProvider.GetRequiredService<InspectionDataObject>().Initialize((System.Guid)this.InspectionId), parameters : parameters, skipSecurity: skipSecurity);

					SetInspectionValue(inspection, false, false);
					__inspectionAlreadyLazyLoaded = true;				
		
					inspection = _serviceProvider.GetRequiredService<InspectionDataObject>().Initialize((System.Guid)this.InspectionId);
					inspection.IsNew = false;
					inspection = (InspectionDataObject)ObjectsDataSet.GetObject(inspection);
                    __inspectionAlreadyLazyLoaded = true;
                }

                return await GetInspectionAsync(false);
            }
            finally
            {
                __inspectionSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual InspectionDataObject Inspection 
		{
			get
			{			
				return GetInspectionAsync(true).Result;
			}
			set
			{
				SetInspectionValue(value);
			}
		}
		
		public virtual bool ShouldSerializeInspection()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Inspection");
		}

		public virtual async Task<InspectionDataObject> GetInspectionAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			InspectionDataObject inspection;

				
			if (_inspection_NewObjectId != null)
			{
				inspection = _serviceProvider.GetRequiredService<InspectionDataObject>();
				inspection.IsNew = true;
				inspection.InternalObjectId = _inspection_NewObjectId;
				inspection = (InspectionDataObject)ObjectsDataSet.GetObject(inspection);
			}
			else
			{
				if (this.InspectionId == null)
					return null;
				if (InspectionId == null)
					inspection = null;
				else
				inspection = _serviceProvider.GetRequiredService<InspectionDataObject>().Initialize((System.Guid)this.InspectionId);
				inspection.IsNew = false;
				inspection = (InspectionDataObject)ObjectsDataSet.GetObject(inspection);
				
				if (allowLazyLoading && inspection == null && LazyLoadingEnabled && (!__inspectionAlreadyLazyLoaded || forceReload))
				{
					inspection = await LoadInspectionAsync(forceReload : forceReload);
				}
			}
				
			return inspection;
		}

		public virtual Nullable<System.Guid> InspectionForeignKey
		{
			get { return InspectionId; }
			set 
			{	
				InspectionId = value;
			}
			
		}
		

		protected IDataProvider<MessageHistoryDataObject> _messageHistoryService => _serviceProvider.GetRequiredService<IDataProvider<MessageHistoryDataObject>>();

		private readonly SemaphoreSlim __messageHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __messageHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "MessageHistoryItems", which is a collection of MessageHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of MessageHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<MessageHistoryDataObject>> LoadMessageHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadMessageHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<MessageHistoryDataObject>> LoadMessageHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __messageHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__messageHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _messageHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __messageHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetMessageHistoryItemsAsync(false);
            }
            finally
            {
                __messageHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<MessageHistoryDataObject> MessageHistoryItems 
		{
			get
			{			
				return GetMessageHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeMessageHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("MessageHistoryItems");
		}

		public virtual async Task<DataObjectCollection<MessageHistoryDataObject>> GetMessageHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__messageHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadMessageHistoryItemsAsync(forceReload : forceReload);
			}
			var messageHistoryItems = ObjectsDataSet.GetRelatedObjects<MessageHistoryDataObject>(this, "MessageHistoryItems");							
			messageHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(MessageHistoryItems_CollectionChanged);
				
			return messageHistoryItems;
		}

        private void MessageHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as MessageHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : MessageHistory", "VehicleDataObject.MessageHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add MessageHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as MessageHistoryDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ModelDataObject> _modelService => _serviceProvider.GetRequiredService<IDataProvider<ModelDataObject>>();
      public virtual void SetModelValue(ModelDataObject valueToSet)
		{
			SetModelValue(valueToSet, true, true);
		}

        public virtual void SetModelValue(ModelDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			ModelDataObject existing_model = null ;

			if ( !(ObjectsDataSet == null))
			{
				ModelDataObject key;

				if (this._model_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<ModelDataObject>().Initialize(this.ModelId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<ModelDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._model_NewObjectId;			
				}

				existing_model = (ModelDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_model ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Model", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_model_NewObjectId != valueToSet.InternalObjectId)
					{
						_model_NewObjectId = valueToSet.InternalObjectId;
						_modelId = valueToSet.Id;
						OnPropertyChanged("ModelId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_modelId != valueToSet.Id)
					{
						_model_NewObjectId = null;

						_modelId = valueToSet.Id;
						OnPropertyChanged("ModelId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_modelId = Guid.Empty;
				OnPropertyChanged("ModelId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_model ,valueToSet))
				OnPropertyChanged("Model", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __modelSemaphore = new SemaphoreSlim(1, 1);
		private bool __modelAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Model", which is a ModelDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a ModelDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<ModelDataObject> LoadModelAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadModelAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<ModelDataObject> LoadModelAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __modelSemaphore.WaitAsync();
			
	        try
            {
                if (!__modelAlreadyLazyLoaded || forceReload)
                {
								
					ModelDataObject model = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __modelAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						model = _serviceProvider.GetRequiredService<ModelDataObject>().Initialize(this.ModelId);
						model.IsNew = false;
						model = (ModelDataObject)ObjectsDataSet.GetObject(model);
						if (model != null)
						{
							return model;
						}
					}

					model = await _modelService.GetAsync(_serviceProvider.GetRequiredService<ModelDataObject>().Initialize(this.ModelId), parameters : parameters, skipSecurity: skipSecurity);

					SetModelValue(model, false, false);
					__modelAlreadyLazyLoaded = true;				
		
					model = _serviceProvider.GetRequiredService<ModelDataObject>().Initialize(this.ModelId);
					model.IsNew = false;
					model = (ModelDataObject)ObjectsDataSet.GetObject(model);
                    __modelAlreadyLazyLoaded = true;
                }

                return await GetModelAsync(false);
            }
            finally
            {
                __modelSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual ModelDataObject Model 
		{
			get
			{			
				return GetModelAsync(true).Result;
			}
			set
			{
				SetModelValue(value);
			}
		}
		
		public virtual bool ShouldSerializeModel()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Model");
		}

		public virtual async Task<ModelDataObject> GetModelAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			ModelDataObject model;

				
			if (_model_NewObjectId != null)
			{
				model = _serviceProvider.GetRequiredService<ModelDataObject>();
				model.IsNew = true;
				model.InternalObjectId = _model_NewObjectId;
				model = (ModelDataObject)ObjectsDataSet.GetObject(model);
			}
			else
			{
				model = _serviceProvider.GetRequiredService<ModelDataObject>().Initialize(this.ModelId);
				model.IsNew = false;
				model = (ModelDataObject)ObjectsDataSet.GetObject(model);
				
				if (allowLazyLoading && model == null && LazyLoadingEnabled && (!__modelAlreadyLazyLoaded || forceReload))
				{
					model = await LoadModelAsync(forceReload : forceReload);
				}
			}
				
			return model;
		}

		public virtual System.Guid ModelForeignKey
		{
			get { return ModelId; }
			set 
			{	
				ModelId = value;
			}
			
		}
		

		protected IDataProvider<ModuleDataObject> _moduleService => _serviceProvider.GetRequiredService<IDataProvider<ModuleDataObject>>();
      public virtual void SetModuleValue(ModuleDataObject valueToSet)
		{
			SetModuleValue(valueToSet, true, true);
		}

        public virtual void SetModuleValue(ModuleDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			ModuleDataObject existing_module = null ;

			if ( !(ObjectsDataSet == null))
			{
				ModuleDataObject key;

				if (this._module_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<ModuleDataObject>().Initialize(this.ModuleId1);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<ModuleDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._module_NewObjectId;			
				}

				existing_module = (ModuleDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_module ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Module", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_module_NewObjectId != valueToSet.InternalObjectId)
					{
						_module_NewObjectId = valueToSet.InternalObjectId;
						_moduleId1 = valueToSet.Id;
						OnPropertyChanged("ModuleId1",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_moduleId1 != valueToSet.Id)
					{
						_module_NewObjectId = null;

						_moduleId1 = valueToSet.Id;
						OnPropertyChanged("ModuleId1",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_moduleId1 = Guid.Empty;
				OnPropertyChanged("ModuleId1",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_module ,valueToSet))
				OnPropertyChanged("Module", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __moduleSemaphore = new SemaphoreSlim(1, 1);
		private bool __moduleAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Module", which is a ModuleDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a ModuleDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<ModuleDataObject> LoadModuleAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadModuleAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<ModuleDataObject> LoadModuleAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __moduleSemaphore.WaitAsync();
			
	        try
            {
                if (!__moduleAlreadyLazyLoaded || forceReload)
                {
								
					ModuleDataObject module = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __moduleAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						module = _serviceProvider.GetRequiredService<ModuleDataObject>().Initialize(this.ModuleId1);
						module.IsNew = false;
						module = (ModuleDataObject)ObjectsDataSet.GetObject(module);
						if (module != null)
						{
							return module;
						}
					}

					module = await _moduleService.GetAsync(_serviceProvider.GetRequiredService<ModuleDataObject>().Initialize(this.ModuleId1), parameters : parameters, skipSecurity: skipSecurity);

					SetModuleValue(module, false, false);
					__moduleAlreadyLazyLoaded = true;				
		
					module = _serviceProvider.GetRequiredService<ModuleDataObject>().Initialize(this.ModuleId1);
					module.IsNew = false;
					module = (ModuleDataObject)ObjectsDataSet.GetObject(module);
                    __moduleAlreadyLazyLoaded = true;
                }

                return await GetModuleAsync(false);
            }
            finally
            {
                __moduleSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual ModuleDataObject Module 
		{
			get
			{			
				return GetModuleAsync(true).Result;
			}
			set
			{
				SetModuleValue(value);
			}
		}
		
		public virtual bool ShouldSerializeModule()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Module");
		}

		public virtual async Task<ModuleDataObject> GetModuleAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			ModuleDataObject module;

				
			if (_module_NewObjectId != null)
			{
				module = _serviceProvider.GetRequiredService<ModuleDataObject>();
				module.IsNew = true;
				module.InternalObjectId = _module_NewObjectId;
				module = (ModuleDataObject)ObjectsDataSet.GetObject(module);
			}
			else
			{
				module = _serviceProvider.GetRequiredService<ModuleDataObject>().Initialize(this.ModuleId1);
				module.IsNew = false;
				module = (ModuleDataObject)ObjectsDataSet.GetObject(module);
				
				if (allowLazyLoading && module == null && LazyLoadingEnabled && (!__moduleAlreadyLazyLoaded || forceReload))
				{
					module = await LoadModuleAsync(forceReload : forceReload);
				}
			}
				
			return module;
		}

		public virtual System.Guid ModuleForeignKey
		{
			get { return ModuleId1; }
			set 
			{	
				ModuleId1 = value;
			}
			
		}
		

		protected IDataProvider<ModuleHistoryDataObject> _moduleHistoryService => _serviceProvider.GetRequiredService<IDataProvider<ModuleHistoryDataObject>>();

		private readonly SemaphoreSlim __moduleHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __moduleHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ModuleHistoryItems", which is a collection of ModuleHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ModuleHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ModuleHistoryDataObject>> LoadModuleHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadModuleHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ModuleHistoryDataObject>> LoadModuleHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __moduleHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__moduleHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _moduleHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __moduleHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetModuleHistoryItemsAsync(false);
            }
            finally
            {
                __moduleHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ModuleHistoryDataObject> ModuleHistoryItems 
		{
			get
			{			
				return GetModuleHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeModuleHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("ModuleHistoryItems");
		}

		public virtual async Task<DataObjectCollection<ModuleHistoryDataObject>> GetModuleHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__moduleHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadModuleHistoryItemsAsync(forceReload : forceReload);
			}
			var moduleHistoryItems = ObjectsDataSet.GetRelatedObjects<ModuleHistoryDataObject>(this, "ModuleHistoryItems");							
			moduleHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(ModuleHistoryItems_CollectionChanged);
				
			return moduleHistoryItems;
		}

        private void ModuleHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ModuleHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ModuleHistory", "VehicleDataObject.ModuleHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add ModuleHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(Nullable<System.Guid>))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ModuleHistoryDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<NetworkSettingsDataObject> _networkSettingsService => _serviceProvider.GetRequiredService<IDataProvider<NetworkSettingsDataObject>>();

		private readonly SemaphoreSlim __networkSettingsItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __networkSettingsItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "NetworkSettingsItems", which is a collection of NetworkSettingsDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of NetworkSettingsDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<NetworkSettingsDataObject>> LoadNetworkSettingsItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadNetworkSettingsItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<NetworkSettingsDataObject>> LoadNetworkSettingsItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __networkSettingsItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__networkSettingsItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _networkSettingsService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __networkSettingsItemsAlreadyLazyLoaded = true;
                }

                return await GetNetworkSettingsItemsAsync(false);
            }
            finally
            {
                __networkSettingsItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<NetworkSettingsDataObject> NetworkSettingsItems 
		{
			get
			{			
				return GetNetworkSettingsItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeNetworkSettingsItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("NetworkSettingsItems");
		}

		public virtual async Task<DataObjectCollection<NetworkSettingsDataObject>> GetNetworkSettingsItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__networkSettingsItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadNetworkSettingsItemsAsync(forceReload : forceReload);
			}
			var networkSettingsItems = ObjectsDataSet.GetRelatedObjects<NetworkSettingsDataObject>(this, "NetworkSettingsItems");							
			networkSettingsItems.CollectionChanged += new NotifyCollectionChangedEventHandler(NetworkSettingsItems_CollectionChanged);
				
			return networkSettingsItems;
		}

        private void NetworkSettingsItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as NetworkSettingsDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : NetworkSettings", "VehicleDataObject.NetworkSettingsItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add NetworkSettingsDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as NetworkSettingsDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<OnDemandSessionDataObject> _onDemandSessionService => _serviceProvider.GetRequiredService<IDataProvider<OnDemandSessionDataObject>>();

		private readonly SemaphoreSlim __onDemandSessionItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __onDemandSessionItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "OnDemandSessionItems", which is a collection of OnDemandSessionDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of OnDemandSessionDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<OnDemandSessionDataObject>> LoadOnDemandSessionItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadOnDemandSessionItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<OnDemandSessionDataObject>> LoadOnDemandSessionItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __onDemandSessionItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__onDemandSessionItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _onDemandSessionService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __onDemandSessionItemsAlreadyLazyLoaded = true;
                }

                return await GetOnDemandSessionItemsAsync(false);
            }
            finally
            {
                __onDemandSessionItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<OnDemandSessionDataObject> OnDemandSessionItems 
		{
			get
			{			
				return GetOnDemandSessionItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeOnDemandSessionItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("OnDemandSessionItems");
		}

		public virtual async Task<DataObjectCollection<OnDemandSessionDataObject>> GetOnDemandSessionItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__onDemandSessionItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadOnDemandSessionItemsAsync(forceReload : forceReload);
			}
			var onDemandSessionItems = ObjectsDataSet.GetRelatedObjects<OnDemandSessionDataObject>(this, "OnDemandSessionItems");							
			onDemandSessionItems.CollectionChanged += new NotifyCollectionChangedEventHandler(OnDemandSessionItems_CollectionChanged);
				
			return onDemandSessionItems;
		}

        private void OnDemandSessionItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as OnDemandSessionDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : OnDemandSession", "VehicleDataObject.OnDemandSessionItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add OnDemandSessionDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as OnDemandSessionDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<OnDemandSettingsDataObject> _onDemandSettingsService => _serviceProvider.GetRequiredService<IDataProvider<OnDemandSettingsDataObject>>();
      public virtual void SetOnDemandSettingsValue(OnDemandSettingsDataObject valueToSet)
		{
			SetOnDemandSettingsValue(valueToSet, true, true);
		}

        public virtual void SetOnDemandSettingsValue(OnDemandSettingsDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<OnDemandSettingsDataObject>(this, "OnDemandSettings");
			var existing_onDemandSettings = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("OnDemandSettings", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._vehicle_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.Vehicle = this;
					valueToSet.VehicleId = this.Id;
				}			
			}
			else  if (existing_onDemandSettings != null)
            {
                ObjectsDataSet.RemoveObject(existing_onDemandSettings);
            }
			if (!ReferenceEquals(existing_onDemandSettings ,valueToSet))
				OnPropertyChanged("OnDemandSettings", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __onDemandSettingsSemaphore = new SemaphoreSlim(1, 1);
		private bool __onDemandSettingsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "OnDemandSettings", which is a OnDemandSettingsDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a OnDemandSettingsDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<OnDemandSettingsDataObject> LoadOnDemandSettingsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadOnDemandSettingsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<OnDemandSettingsDataObject> LoadOnDemandSettingsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __onDemandSettingsSemaphore.WaitAsync();
			
	        try
            {
                if (!__onDemandSettingsAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data OnDemandSettings for the current entity. The DataObjects doesn't have an ObjectsDataSet", "VehicleObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var onDemandSettings = (this.ObjectsDataSet as ObjectsDataSet).OnDemandSettingsObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).OnDemandSettingsObjects.Where(item => item.Value.VehicleId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(onDemandSettings, null))
					{
						var filterPredicate = "VehicleId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						onDemandSettings = (await _onDemandSettingsService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetOnDemandSettingsValue(onDemandSettings, false, false);
						__onDemandSettingsAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a onDemandSettings, but relation field not set, encourage it to get set by removing and re-adding the onDemandSettings 
					if (onDemandSettings != null && this.OnDemandSettings == null)
					{
						this.ObjectsDataSet.RemoveObject(onDemandSettings);
						this.ObjectsDataSet.AddObject(onDemandSettings);
					}			
                    __onDemandSettingsAlreadyLazyLoaded = true;
                }

                return await GetOnDemandSettingsAsync(false);
            }
            finally
            {
                __onDemandSettingsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual OnDemandSettingsDataObject OnDemandSettings 
		{
			get
			{			
				return GetOnDemandSettingsAsync(true).Result;
			}
			set
			{
				SetOnDemandSettingsValue(value);
			}
		}
		
		public virtual bool ShouldSerializeOnDemandSettings()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("OnDemandSettings");
		}

		public virtual async Task<OnDemandSettingsDataObject> GetOnDemandSettingsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			OnDemandSettingsDataObject onDemandSettings;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<OnDemandSettingsDataObject>(this, "OnDemandSettings");
               	onDemandSettings = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && onDemandSettings == null && LazyLoadingEnabled && (!__onDemandSettingsAlreadyLazyLoaded || forceReload))
				{
					onDemandSettings = await LoadOnDemandSettingsAsync(forceReload : forceReload);
				}
			}
				
			return onDemandSettings;
		}


		protected IDataProvider<PedestrianDetectionHistoryDataObject> _pedestrianDetectionHistoryService => _serviceProvider.GetRequiredService<IDataProvider<PedestrianDetectionHistoryDataObject>>();

		private readonly SemaphoreSlim __pedestrianDetectionHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __pedestrianDetectionHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "PedestrianDetectionHistoryItems", which is a collection of PedestrianDetectionHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of PedestrianDetectionHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<PedestrianDetectionHistoryDataObject>> LoadPedestrianDetectionHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPedestrianDetectionHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<PedestrianDetectionHistoryDataObject>> LoadPedestrianDetectionHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __pedestrianDetectionHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__pedestrianDetectionHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _pedestrianDetectionHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __pedestrianDetectionHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetPedestrianDetectionHistoryItemsAsync(false);
            }
            finally
            {
                __pedestrianDetectionHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<PedestrianDetectionHistoryDataObject> PedestrianDetectionHistoryItems 
		{
			get
			{			
				return GetPedestrianDetectionHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializePedestrianDetectionHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("PedestrianDetectionHistoryItems");
		}

		public virtual async Task<DataObjectCollection<PedestrianDetectionHistoryDataObject>> GetPedestrianDetectionHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__pedestrianDetectionHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadPedestrianDetectionHistoryItemsAsync(forceReload : forceReload);
			}
			var pedestrianDetectionHistoryItems = ObjectsDataSet.GetRelatedObjects<PedestrianDetectionHistoryDataObject>(this, "PedestrianDetectionHistoryItems");							
			pedestrianDetectionHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(PedestrianDetectionHistoryItems_CollectionChanged);
				
			return pedestrianDetectionHistoryItems;
		}

        private void PedestrianDetectionHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as PedestrianDetectionHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : PedestrianDetectionHistory", "VehicleDataObject.PedestrianDetectionHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add PedestrianDetectionHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as PedestrianDetectionHistoryDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<PersonDataObject> _personService => _serviceProvider.GetRequiredService<IDataProvider<PersonDataObject>>();
      public virtual void SetPersonValue(PersonDataObject valueToSet)
		{
			SetPersonValue(valueToSet, true, true);
		}

        public virtual void SetPersonValue(PersonDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			PersonDataObject existing_person = null ;

			if ( !(this.PersonId == null || ObjectsDataSet == null))
			{
				PersonDataObject key;

				if (this._person_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<PersonDataObject>().Initialize((System.Guid)this.PersonId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<PersonDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._person_NewObjectId;			
				}

				existing_person = (PersonDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_person ,valueToSet))
            {
                if (valueToSet == null)
                {
					_person_NewObjectId = null;
					_personId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Person", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_person_NewObjectId != valueToSet.InternalObjectId)
					{
						_person_NewObjectId = valueToSet.InternalObjectId;
						_personId = valueToSet.Id;
						OnPropertyChanged("PersonId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_personId != valueToSet.Id)
					{
						_person_NewObjectId = null;

						_personId = valueToSet.Id;
						OnPropertyChanged("PersonId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_person_NewObjectId = null;
					_personId = null;
					
				OnPropertyChanged("PersonId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_person ,valueToSet))
				OnPropertyChanged("Person", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __personSemaphore = new SemaphoreSlim(1, 1);
		private bool __personAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Person", which is a PersonDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a PersonDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<PersonDataObject> LoadPersonAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPersonAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<PersonDataObject> LoadPersonAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __personSemaphore.WaitAsync();
			
	        try
            {
                if (!__personAlreadyLazyLoaded || forceReload)
                {
								
					if (this.PersonId == null)
					{
						return null;
					}
				
					PersonDataObject person = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __personAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						person = _serviceProvider.GetRequiredService<PersonDataObject>().Initialize((System.Guid)this.PersonId);
						person.IsNew = false;
						person = (PersonDataObject)ObjectsDataSet.GetObject(person);
						if (person != null)
						{
							return person;
						}
					}

					person = await _personService.GetAsync(_serviceProvider.GetRequiredService<PersonDataObject>().Initialize((System.Guid)this.PersonId), parameters : parameters, skipSecurity: skipSecurity);

					SetPersonValue(person, false, false);
					__personAlreadyLazyLoaded = true;				
		
					person = _serviceProvider.GetRequiredService<PersonDataObject>().Initialize((System.Guid)this.PersonId);
					person.IsNew = false;
					person = (PersonDataObject)ObjectsDataSet.GetObject(person);
                    __personAlreadyLazyLoaded = true;
                }

                return await GetPersonAsync(false);
            }
            finally
            {
                __personSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual PersonDataObject Person 
		{
			get
			{			
				return GetPersonAsync(true).Result;
			}
			set
			{
				SetPersonValue(value);
			}
		}
		
		public virtual bool ShouldSerializePerson()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Person");
		}

		public virtual async Task<PersonDataObject> GetPersonAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			PersonDataObject person;

				
			if (_person_NewObjectId != null)
			{
				person = _serviceProvider.GetRequiredService<PersonDataObject>();
				person.IsNew = true;
				person.InternalObjectId = _person_NewObjectId;
				person = (PersonDataObject)ObjectsDataSet.GetObject(person);
			}
			else
			{
				if (this.PersonId == null)
					return null;
				if (PersonId == null)
					person = null;
				else
				person = _serviceProvider.GetRequiredService<PersonDataObject>().Initialize((System.Guid)this.PersonId);
				person.IsNew = false;
				person = (PersonDataObject)ObjectsDataSet.GetObject(person);
				
				if (allowLazyLoading && person == null && LazyLoadingEnabled && (!__personAlreadyLazyLoaded || forceReload))
				{
					person = await LoadPersonAsync(forceReload : forceReload);
				}
			}
				
			return person;
		}

		public virtual Nullable<System.Guid> PersonForeignKey
		{
			get { return PersonId; }
			set 
			{	
				PersonId = value;
			}
			
		}
		

		protected IDataProvider<PersonToPerVehicleMasterAccessViewDataObject> _personToPerVehicleMasterAccessViewService => _serviceProvider.GetRequiredService<IDataProvider<PersonToPerVehicleMasterAccessViewDataObject>>();

		private readonly SemaphoreSlim __personToPerVehicleMasterAccessViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __personToPerVehicleMasterAccessViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "PersonToPerVehicleMasterAccessViewItems", which is a collection of PersonToPerVehicleMasterAccessViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of PersonToPerVehicleMasterAccessViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<PersonToPerVehicleMasterAccessViewDataObject>> LoadPersonToPerVehicleMasterAccessViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPersonToPerVehicleMasterAccessViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<PersonToPerVehicleMasterAccessViewDataObject>> LoadPersonToPerVehicleMasterAccessViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __personToPerVehicleMasterAccessViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__personToPerVehicleMasterAccessViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _personToPerVehicleMasterAccessViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __personToPerVehicleMasterAccessViewItemsAlreadyLazyLoaded = true;
                }

                return await GetPersonToPerVehicleMasterAccessViewItemsAsync(false);
            }
            finally
            {
                __personToPerVehicleMasterAccessViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<PersonToPerVehicleMasterAccessViewDataObject> PersonToPerVehicleMasterAccessViewItems 
		{
			get
			{			
				return GetPersonToPerVehicleMasterAccessViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializePersonToPerVehicleMasterAccessViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("PersonToPerVehicleMasterAccessViewItems");
		}

		public virtual async Task<DataObjectCollection<PersonToPerVehicleMasterAccessViewDataObject>> GetPersonToPerVehicleMasterAccessViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__personToPerVehicleMasterAccessViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadPersonToPerVehicleMasterAccessViewItemsAsync(forceReload : forceReload);
			}
			var personToPerVehicleMasterAccessViewItems = ObjectsDataSet.GetRelatedObjects<PersonToPerVehicleMasterAccessViewDataObject>(this, "PersonToPerVehicleMasterAccessViewItems");							
			personToPerVehicleMasterAccessViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(PersonToPerVehicleMasterAccessViewItems_CollectionChanged);
				
			return personToPerVehicleMasterAccessViewItems;
		}

        private void PersonToPerVehicleMasterAccessViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as PersonToPerVehicleMasterAccessViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : PersonToPerVehicleMasterAccessView", "VehicleDataObject.PersonToPerVehicleMasterAccessViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add PersonToPerVehicleMasterAccessViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as PersonToPerVehicleMasterAccessViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<PersonToPerVehicleNormalAccessViewDataObject> _personToPerVehicleNormalAccessViewService => _serviceProvider.GetRequiredService<IDataProvider<PersonToPerVehicleNormalAccessViewDataObject>>();

		private readonly SemaphoreSlim __personToPerVehicleNormalAccessViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __personToPerVehicleNormalAccessViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "PersonToPerVehicleNormalAccessViewItems", which is a collection of PersonToPerVehicleNormalAccessViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of PersonToPerVehicleNormalAccessViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>> LoadPersonToPerVehicleNormalAccessViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPersonToPerVehicleNormalAccessViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>> LoadPersonToPerVehicleNormalAccessViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __personToPerVehicleNormalAccessViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__personToPerVehicleNormalAccessViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _personToPerVehicleNormalAccessViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __personToPerVehicleNormalAccessViewItemsAlreadyLazyLoaded = true;
                }

                return await GetPersonToPerVehicleNormalAccessViewItemsAsync(false);
            }
            finally
            {
                __personToPerVehicleNormalAccessViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> PersonToPerVehicleNormalAccessViewItems 
		{
			get
			{			
				return GetPersonToPerVehicleNormalAccessViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializePersonToPerVehicleNormalAccessViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("PersonToPerVehicleNormalAccessViewItems");
		}

		public virtual async Task<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>> GetPersonToPerVehicleNormalAccessViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__personToPerVehicleNormalAccessViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadPersonToPerVehicleNormalAccessViewItemsAsync(forceReload : forceReload);
			}
			var personToPerVehicleNormalAccessViewItems = ObjectsDataSet.GetRelatedObjects<PersonToPerVehicleNormalAccessViewDataObject>(this, "PersonToPerVehicleNormalAccessViewItems");							
			personToPerVehicleNormalAccessViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(PersonToPerVehicleNormalAccessViewItems_CollectionChanged);
				
			return personToPerVehicleNormalAccessViewItems;
		}

        private void PersonToPerVehicleNormalAccessViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as PersonToPerVehicleNormalAccessViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : PersonToPerVehicleNormalAccessView", "VehicleDataObject.PersonToPerVehicleNormalAccessViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add PersonToPerVehicleNormalAccessViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as PersonToPerVehicleNormalAccessViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<PerVehicleNormalCardAccessDataObject> _perVehicleNormalCardAccessService => _serviceProvider.GetRequiredService<IDataProvider<PerVehicleNormalCardAccessDataObject>>();

		private readonly SemaphoreSlim __perVehicleNormalCardAccessItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __perVehicleNormalCardAccessItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "PerVehicleNormalCardAccessItems", which is a collection of PerVehicleNormalCardAccessDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of PerVehicleNormalCardAccessDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<PerVehicleNormalCardAccessDataObject>> LoadPerVehicleNormalCardAccessItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPerVehicleNormalCardAccessItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<PerVehicleNormalCardAccessDataObject>> LoadPerVehicleNormalCardAccessItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __perVehicleNormalCardAccessItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__perVehicleNormalCardAccessItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _perVehicleNormalCardAccessService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __perVehicleNormalCardAccessItemsAlreadyLazyLoaded = true;
                }

                return await GetPerVehicleNormalCardAccessItemsAsync(false);
            }
            finally
            {
                __perVehicleNormalCardAccessItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<PerVehicleNormalCardAccessDataObject> PerVehicleNormalCardAccessItems 
		{
			get
			{			
				return GetPerVehicleNormalCardAccessItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializePerVehicleNormalCardAccessItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("PerVehicleNormalCardAccessItems");
		}

		public virtual async Task<DataObjectCollection<PerVehicleNormalCardAccessDataObject>> GetPerVehicleNormalCardAccessItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__perVehicleNormalCardAccessItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadPerVehicleNormalCardAccessItemsAsync(forceReload : forceReload);
			}
			var perVehicleNormalCardAccessItems = ObjectsDataSet.GetRelatedObjects<PerVehicleNormalCardAccessDataObject>(this, "PerVehicleNormalCardAccessItems");							
			perVehicleNormalCardAccessItems.CollectionChanged += new NotifyCollectionChangedEventHandler(PerVehicleNormalCardAccessItems_CollectionChanged);
				
			return perVehicleNormalCardAccessItems;
		}

        private void PerVehicleNormalCardAccessItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as PerVehicleNormalCardAccessDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : PerVehicleNormalCardAccess", "VehicleDataObject.PerVehicleNormalCardAccessItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add PerVehicleNormalCardAccessDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as PerVehicleNormalCardAccessDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ServiceSettingsDataObject> _serviceSettingsService => _serviceProvider.GetRequiredService<IDataProvider<ServiceSettingsDataObject>>();
      public virtual void SetServiceSettingsValue(ServiceSettingsDataObject valueToSet)
		{
			SetServiceSettingsValue(valueToSet, true, true);
		}

        public virtual void SetServiceSettingsValue(ServiceSettingsDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			ServiceSettingsDataObject existing_serviceSettings = null ;

			if ( !(this.ServiceSettingsId == null || ObjectsDataSet == null))
			{
				ServiceSettingsDataObject key;

				if (this._serviceSettings_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<ServiceSettingsDataObject>().Initialize((System.Guid)this.ServiceSettingsId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<ServiceSettingsDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._serviceSettings_NewObjectId;			
				}

				existing_serviceSettings = (ServiceSettingsDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_serviceSettings ,valueToSet))
            {
                if (valueToSet == null)
                {
					_serviceSettings_NewObjectId = null;
					_serviceSettingsId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("ServiceSettings", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_serviceSettings_NewObjectId != valueToSet.InternalObjectId)
					{
						_serviceSettings_NewObjectId = valueToSet.InternalObjectId;
						_serviceSettingsId = valueToSet.Id;
						OnPropertyChanged("ServiceSettingsId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_serviceSettingsId != valueToSet.Id)
					{
						_serviceSettings_NewObjectId = null;

						_serviceSettingsId = valueToSet.Id;
						OnPropertyChanged("ServiceSettingsId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_serviceSettings_NewObjectId = null;
					_serviceSettingsId = null;
					
				OnPropertyChanged("ServiceSettingsId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_serviceSettings ,valueToSet))
				OnPropertyChanged("ServiceSettings", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __serviceSettingsSemaphore = new SemaphoreSlim(1, 1);
		private bool __serviceSettingsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ServiceSettings", which is a ServiceSettingsDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a ServiceSettingsDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<ServiceSettingsDataObject> LoadServiceSettingsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadServiceSettingsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<ServiceSettingsDataObject> LoadServiceSettingsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __serviceSettingsSemaphore.WaitAsync();
			
	        try
            {
                if (!__serviceSettingsAlreadyLazyLoaded || forceReload)
                {
								
					if (this.ServiceSettingsId == null)
					{
						return null;
					}
				
					ServiceSettingsDataObject serviceSettings = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __serviceSettingsAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						serviceSettings = _serviceProvider.GetRequiredService<ServiceSettingsDataObject>().Initialize((System.Guid)this.ServiceSettingsId);
						serviceSettings.IsNew = false;
						serviceSettings = (ServiceSettingsDataObject)ObjectsDataSet.GetObject(serviceSettings);
						if (serviceSettings != null)
						{
							return serviceSettings;
						}
					}

					serviceSettings = await _serviceSettingsService.GetAsync(_serviceProvider.GetRequiredService<ServiceSettingsDataObject>().Initialize((System.Guid)this.ServiceSettingsId), parameters : parameters, skipSecurity: skipSecurity);

					SetServiceSettingsValue(serviceSettings, false, false);
					__serviceSettingsAlreadyLazyLoaded = true;				
		
					serviceSettings = _serviceProvider.GetRequiredService<ServiceSettingsDataObject>().Initialize((System.Guid)this.ServiceSettingsId);
					serviceSettings.IsNew = false;
					serviceSettings = (ServiceSettingsDataObject)ObjectsDataSet.GetObject(serviceSettings);
                    __serviceSettingsAlreadyLazyLoaded = true;
                }

                return await GetServiceSettingsAsync(false);
            }
            finally
            {
                __serviceSettingsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual ServiceSettingsDataObject ServiceSettings 
		{
			get
			{			
				return GetServiceSettingsAsync(true).Result;
			}
			set
			{
				SetServiceSettingsValue(value);
			}
		}
		
		public virtual bool ShouldSerializeServiceSettings()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("ServiceSettings");
		}

		public virtual async Task<ServiceSettingsDataObject> GetServiceSettingsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			ServiceSettingsDataObject serviceSettings;

				
			if (_serviceSettings_NewObjectId != null)
			{
				serviceSettings = _serviceProvider.GetRequiredService<ServiceSettingsDataObject>();
				serviceSettings.IsNew = true;
				serviceSettings.InternalObjectId = _serviceSettings_NewObjectId;
				serviceSettings = (ServiceSettingsDataObject)ObjectsDataSet.GetObject(serviceSettings);
			}
			else
			{
				if (this.ServiceSettingsId == null)
					return null;
				if (ServiceSettingsId == null)
					serviceSettings = null;
				else
				serviceSettings = _serviceProvider.GetRequiredService<ServiceSettingsDataObject>().Initialize((System.Guid)this.ServiceSettingsId);
				serviceSettings.IsNew = false;
				serviceSettings = (ServiceSettingsDataObject)ObjectsDataSet.GetObject(serviceSettings);
				
				if (allowLazyLoading && serviceSettings == null && LazyLoadingEnabled && (!__serviceSettingsAlreadyLazyLoaded || forceReload))
				{
					serviceSettings = await LoadServiceSettingsAsync(forceReload : forceReload);
				}
			}
				
			return serviceSettings;
		}

		public virtual Nullable<System.Guid> ServiceSettingsForeignKey
		{
			get { return ServiceSettingsId; }
			set 
			{	
				ServiceSettingsId = value;
			}
			
		}
		

		protected IDataProvider<SessionDataObject> _sessionService => _serviceProvider.GetRequiredService<IDataProvider<SessionDataObject>>();

		private readonly SemaphoreSlim __sessionsSemaphore = new SemaphoreSlim(1, 1);
		private bool __sessionsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Sessions", which is a collection of SessionDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SessionDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SessionDataObject>> LoadSessionsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSessionsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SessionDataObject>> LoadSessionsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __sessionsSemaphore.WaitAsync();
			
	        try
            {
                if (!__sessionsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _sessionService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __sessionsAlreadyLazyLoaded = true;
                }

                return await GetSessionsAsync(false);
            }
            finally
            {
                __sessionsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SessionDataObject> Sessions 
		{
			get
			{			
				return GetSessionsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSessions()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Sessions");
		}

		public virtual async Task<DataObjectCollection<SessionDataObject>> GetSessionsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__sessionsAlreadyLazyLoaded || forceReload) )
			{
				await LoadSessionsAsync(forceReload : forceReload);
			}
			var sessions = ObjectsDataSet.GetRelatedObjects<SessionDataObject>(this, "Sessions");							
			sessions.CollectionChanged += new NotifyCollectionChangedEventHandler(Sessions_CollectionChanged);
				
			return sessions;
		}

        private void Sessions_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SessionDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : Session", "VehicleDataObject.Sessions_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add SessionDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SessionDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<SiteDataObject> _siteService => _serviceProvider.GetRequiredService<IDataProvider<SiteDataObject>>();
      public virtual void SetSiteValue(SiteDataObject valueToSet)
		{
			SetSiteValue(valueToSet, true, true);
		}

        public virtual void SetSiteValue(SiteDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			SiteDataObject existing_site = null ;

			if ( !(ObjectsDataSet == null))
			{
				SiteDataObject key;

				if (this._site_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<SiteDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._site_NewObjectId;			
				}

				existing_site = (SiteDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_site ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Site", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_site_NewObjectId != valueToSet.InternalObjectId)
					{
						_site_NewObjectId = valueToSet.InternalObjectId;
						_siteId = valueToSet.Id;
						OnPropertyChanged("SiteId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_siteId != valueToSet.Id)
					{
						_site_NewObjectId = null;

						_siteId = valueToSet.Id;
						OnPropertyChanged("SiteId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_siteId = Guid.Empty;
				OnPropertyChanged("SiteId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_site ,valueToSet))
				OnPropertyChanged("Site", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __siteSemaphore = new SemaphoreSlim(1, 1);
		private bool __siteAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Site", which is a SiteDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SiteDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SiteDataObject> LoadSiteAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSiteAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SiteDataObject> LoadSiteAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __siteSemaphore.WaitAsync();
			
	        try
            {
                if (!__siteAlreadyLazyLoaded || forceReload)
                {
								
					SiteDataObject site = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __siteAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						site = _serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId);
						site.IsNew = false;
						site = (SiteDataObject)ObjectsDataSet.GetObject(site);
						if (site != null)
						{
							return site;
						}
					}

					site = await _siteService.GetAsync(_serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId), parameters : parameters, skipSecurity: skipSecurity);

					SetSiteValue(site, false, false);
					__siteAlreadyLazyLoaded = true;				
		
					site = _serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId);
					site.IsNew = false;
					site = (SiteDataObject)ObjectsDataSet.GetObject(site);
                    __siteAlreadyLazyLoaded = true;
                }

                return await GetSiteAsync(false);
            }
            finally
            {
                __siteSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SiteDataObject Site 
		{
			get
			{			
				return GetSiteAsync(true).Result;
			}
			set
			{
				SetSiteValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSite()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("Site");
		}

		public virtual async Task<SiteDataObject> GetSiteAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SiteDataObject site;

				
			if (_site_NewObjectId != null)
			{
				site = _serviceProvider.GetRequiredService<SiteDataObject>();
				site.IsNew = true;
				site.InternalObjectId = _site_NewObjectId;
				site = (SiteDataObject)ObjectsDataSet.GetObject(site);
			}
			else
			{
				site = _serviceProvider.GetRequiredService<SiteDataObject>().Initialize(this.SiteId);
				site.IsNew = false;
				site = (SiteDataObject)ObjectsDataSet.GetObject(site);
				
				if (allowLazyLoading && site == null && LazyLoadingEnabled && (!__siteAlreadyLazyLoaded || forceReload))
				{
					site = await LoadSiteAsync(forceReload : forceReload);
				}
			}
				
			return site;
		}

		public virtual System.Guid SiteForeignKey
		{
			get { return SiteId; }
			set 
			{	
				SiteId = value;
			}
			
		}
		

		protected IDataProvider<SlamcoreDeviceHistoryDataObject> _slamcoreDeviceHistoryService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceHistoryDataObject>>();

		private readonly SemaphoreSlim __slamcoreDeviceHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreDeviceHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreDeviceHistoryItems", which is a collection of SlamcoreDeviceHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SlamcoreDeviceHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SlamcoreDeviceHistoryDataObject>> LoadSlamcoreDeviceHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreDeviceHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SlamcoreDeviceHistoryDataObject>> LoadSlamcoreDeviceHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreDeviceHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreDeviceHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _slamcoreDeviceHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __slamcoreDeviceHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreDeviceHistoryItemsAsync(false);
            }
            finally
            {
                __slamcoreDeviceHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SlamcoreDeviceHistoryDataObject> SlamcoreDeviceHistoryItems 
		{
			get
			{			
				return GetSlamcoreDeviceHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreDeviceHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("SlamcoreDeviceHistoryItems");
		}

		public virtual async Task<DataObjectCollection<SlamcoreDeviceHistoryDataObject>> GetSlamcoreDeviceHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__slamcoreDeviceHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadSlamcoreDeviceHistoryItemsAsync(forceReload : forceReload);
			}
			var slamcoreDeviceHistoryItems = ObjectsDataSet.GetRelatedObjects<SlamcoreDeviceHistoryDataObject>(this, "SlamcoreDeviceHistoryItems");							
			slamcoreDeviceHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(SlamcoreDeviceHistoryItems_CollectionChanged);
				
			return slamcoreDeviceHistoryItems;
		}

        private void SlamcoreDeviceHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SlamcoreDeviceHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : SlamcoreDeviceHistory", "VehicleDataObject.SlamcoreDeviceHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add SlamcoreDeviceHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SlamcoreDeviceHistoryDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<UnitUtilisationStoreProcedureDataObject> _unitUtilisationStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<UnitUtilisationStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __unitUnutilisationStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __unitUnutilisationStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "UnitUnutilisationStoreProcedureItems", which is a collection of UnitUtilisationStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of UnitUtilisationStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<UnitUtilisationStoreProcedureDataObject>> LoadUnitUnutilisationStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadUnitUnutilisationStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<UnitUtilisationStoreProcedureDataObject>> LoadUnitUnutilisationStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __unitUnutilisationStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__unitUnutilisationStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _unitUtilisationStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __unitUnutilisationStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetUnitUnutilisationStoreProcedureItemsAsync(false);
            }
            finally
            {
                __unitUnutilisationStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<UnitUtilisationStoreProcedureDataObject> UnitUnutilisationStoreProcedureItems 
		{
			get
			{			
				return GetUnitUnutilisationStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeUnitUnutilisationStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("UnitUnutilisationStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<UnitUtilisationStoreProcedureDataObject>> GetUnitUnutilisationStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__unitUnutilisationStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadUnitUnutilisationStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var unitUnutilisationStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<UnitUtilisationStoreProcedureDataObject>(this, "UnitUnutilisationStoreProcedureItems");							
			unitUnutilisationStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(UnitUnutilisationStoreProcedureItems_CollectionChanged);
				
			return unitUnutilisationStoreProcedureItems;
		}

        private void UnitUnutilisationStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as UnitUtilisationStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : UnitUtilisationStoreProcedure", "VehicleDataObject.UnitUnutilisationStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add UnitUtilisationStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as UnitUtilisationStoreProcedureDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<UnitUnutilisationStoreProcedureDataObject> _unitUnutilisationStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<UnitUnutilisationStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __unitUtilisationStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __unitUtilisationStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "UnitUtilisationStoreProcedureItems", which is a collection of UnitUnutilisationStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of UnitUnutilisationStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<UnitUnutilisationStoreProcedureDataObject>> LoadUnitUtilisationStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadUnitUtilisationStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<UnitUnutilisationStoreProcedureDataObject>> LoadUnitUtilisationStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __unitUtilisationStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__unitUtilisationStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _unitUnutilisationStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __unitUtilisationStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetUnitUtilisationStoreProcedureItemsAsync(false);
            }
            finally
            {
                __unitUtilisationStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<UnitUnutilisationStoreProcedureDataObject> UnitUtilisationStoreProcedureItems 
		{
			get
			{			
				return GetUnitUtilisationStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeUnitUtilisationStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("UnitUtilisationStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<UnitUnutilisationStoreProcedureDataObject>> GetUnitUtilisationStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__unitUtilisationStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadUnitUtilisationStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var unitUtilisationStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<UnitUnutilisationStoreProcedureDataObject>(this, "UnitUtilisationStoreProcedureItems");							
			unitUtilisationStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(UnitUtilisationStoreProcedureItems_CollectionChanged);
				
			return unitUtilisationStoreProcedureItems;
		}

        private void UnitUtilisationStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as UnitUnutilisationStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : UnitUnutilisationStoreProcedure", "VehicleDataObject.UnitUtilisationStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add UnitUnutilisationStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as UnitUnutilisationStoreProcedureDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleAlertSubscriptionDataObject> _vehicleAlertSubscriptionService => _serviceProvider.GetRequiredService<IDataProvider<VehicleAlertSubscriptionDataObject>>();

		private readonly SemaphoreSlim __vehicleAlertSubscriptionItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleAlertSubscriptionItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleAlertSubscriptionItems", which is a collection of VehicleAlertSubscriptionDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleAlertSubscriptionDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleAlertSubscriptionDataObject>> LoadVehicleAlertSubscriptionItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleAlertSubscriptionItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleAlertSubscriptionDataObject>> LoadVehicleAlertSubscriptionItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleAlertSubscriptionItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleAlertSubscriptionItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleAlertSubscriptionService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleAlertSubscriptionItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleAlertSubscriptionItemsAsync(false);
            }
            finally
            {
                __vehicleAlertSubscriptionItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleAlertSubscriptionDataObject> VehicleAlertSubscriptionItems 
		{
			get
			{			
				return GetVehicleAlertSubscriptionItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleAlertSubscriptionItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleAlertSubscriptionItems");
		}

		public virtual async Task<DataObjectCollection<VehicleAlertSubscriptionDataObject>> GetVehicleAlertSubscriptionItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleAlertSubscriptionItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleAlertSubscriptionItemsAsync(forceReload : forceReload);
			}
			var vehicleAlertSubscriptionItems = ObjectsDataSet.GetRelatedObjects<VehicleAlertSubscriptionDataObject>(this, "VehicleAlertSubscriptionItems");							
			vehicleAlertSubscriptionItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleAlertSubscriptionItems_CollectionChanged);
				
			return vehicleAlertSubscriptionItems;
		}

        private void VehicleAlertSubscriptionItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleAlertSubscriptionDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleAlertSubscription", "VehicleDataObject.VehicleAlertSubscriptionItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VehicleAlertSubscriptionDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleAlertSubscriptionDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleBroadcastMessageDataObject> _vehicleBroadcastMessageService => _serviceProvider.GetRequiredService<IDataProvider<VehicleBroadcastMessageDataObject>>();

		private readonly SemaphoreSlim __vehicleBroadcastMessageItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleBroadcastMessageItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleBroadcastMessageItems", which is a collection of VehicleBroadcastMessageDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleBroadcastMessageDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleBroadcastMessageDataObject>> LoadVehicleBroadcastMessageItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleBroadcastMessageItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleBroadcastMessageDataObject>> LoadVehicleBroadcastMessageItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleBroadcastMessageItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleBroadcastMessageItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleBroadcastMessageService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleBroadcastMessageItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleBroadcastMessageItemsAsync(false);
            }
            finally
            {
                __vehicleBroadcastMessageItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleBroadcastMessageDataObject> VehicleBroadcastMessageItems 
		{
			get
			{			
				return GetVehicleBroadcastMessageItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleBroadcastMessageItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleBroadcastMessageItems");
		}

		public virtual async Task<DataObjectCollection<VehicleBroadcastMessageDataObject>> GetVehicleBroadcastMessageItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleBroadcastMessageItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleBroadcastMessageItemsAsync(forceReload : forceReload);
			}
			var vehicleBroadcastMessageItems = ObjectsDataSet.GetRelatedObjects<VehicleBroadcastMessageDataObject>(this, "VehicleBroadcastMessageItems");							
			vehicleBroadcastMessageItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleBroadcastMessageItems_CollectionChanged);
				
			return vehicleBroadcastMessageItems;
		}

        private void VehicleBroadcastMessageItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleBroadcastMessageDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleBroadcastMessage", "VehicleDataObject.VehicleBroadcastMessageItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VehicleBroadcastMessageDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleBroadcastMessageDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<PerVehicleMasterCardAccessDataObject> _perVehicleMasterCardAccessService => _serviceProvider.GetRequiredService<IDataProvider<PerVehicleMasterCardAccessDataObject>>();

		private readonly SemaphoreSlim __vehicleCardAccessesSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleCardAccessesAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleCardAccesses", which is a collection of PerVehicleMasterCardAccessDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of PerVehicleMasterCardAccessDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<PerVehicleMasterCardAccessDataObject>> LoadVehicleCardAccessesAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleCardAccessesAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<PerVehicleMasterCardAccessDataObject>> LoadVehicleCardAccessesAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleCardAccessesSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleCardAccessesAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _perVehicleMasterCardAccessService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleCardAccessesAlreadyLazyLoaded = true;
                }

                return await GetVehicleCardAccessesAsync(false);
            }
            finally
            {
                __vehicleCardAccessesSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<PerVehicleMasterCardAccessDataObject> VehicleCardAccesses 
		{
			get
			{			
				return GetVehicleCardAccessesAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleCardAccesses()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleCardAccesses");
		}

		public virtual async Task<DataObjectCollection<PerVehicleMasterCardAccessDataObject>> GetVehicleCardAccessesAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleCardAccessesAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleCardAccessesAsync(forceReload : forceReload);
			}
			var vehicleCardAccesses = ObjectsDataSet.GetRelatedObjects<PerVehicleMasterCardAccessDataObject>(this, "VehicleCardAccesses");							
			vehicleCardAccesses.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleCardAccesses_CollectionChanged);
				
			return vehicleCardAccesses;
		}

        private void VehicleCardAccesses_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as PerVehicleMasterCardAccessDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : PerVehicleMasterCardAccess", "VehicleDataObject.VehicleCardAccesses_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add PerVehicleMasterCardAccessDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as PerVehicleMasterCardAccessDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleDiagnosticDataObject> _vehicleDiagnosticService => _serviceProvider.GetRequiredService<IDataProvider<VehicleDiagnosticDataObject>>();
      public virtual void SetVehicleDiagnosticValue(VehicleDiagnosticDataObject valueToSet)
		{
			SetVehicleDiagnosticValue(valueToSet, true, true);
		}

        public virtual void SetVehicleDiagnosticValue(VehicleDiagnosticDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<VehicleDiagnosticDataObject>(this, "VehicleDiagnostic");
			var existing_vehicleDiagnostic = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("VehicleDiagnostic", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._vehicle_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.Vehicle = this;
					valueToSet.VehicleId = this.Id;
				}			
			}
			else  if (existing_vehicleDiagnostic != null)
            {
                ObjectsDataSet.RemoveObject(existing_vehicleDiagnostic);
            }
			if (!ReferenceEquals(existing_vehicleDiagnostic ,valueToSet))
				OnPropertyChanged("VehicleDiagnostic", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __vehicleDiagnosticSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleDiagnosticAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleDiagnostic", which is a VehicleDiagnosticDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a VehicleDiagnosticDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<VehicleDiagnosticDataObject> LoadVehicleDiagnosticAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleDiagnosticAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<VehicleDiagnosticDataObject> LoadVehicleDiagnosticAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleDiagnosticSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleDiagnosticAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data VehicleDiagnostic for the current entity. The DataObjects doesn't have an ObjectsDataSet", "VehicleObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var vehicleDiagnostic = (this.ObjectsDataSet as ObjectsDataSet).VehicleDiagnosticObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).VehicleDiagnosticObjects.Where(item => item.Value.VehicleId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(vehicleDiagnostic, null))
					{
						var filterPredicate = "VehicleId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						vehicleDiagnostic = (await _vehicleDiagnosticService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetVehicleDiagnosticValue(vehicleDiagnostic, false, false);
						__vehicleDiagnosticAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a vehicleDiagnostic, but relation field not set, encourage it to get set by removing and re-adding the vehicleDiagnostic 
					if (vehicleDiagnostic != null && this.VehicleDiagnostic == null)
					{
						this.ObjectsDataSet.RemoveObject(vehicleDiagnostic);
						this.ObjectsDataSet.AddObject(vehicleDiagnostic);
					}			
                    __vehicleDiagnosticAlreadyLazyLoaded = true;
                }

                return await GetVehicleDiagnosticAsync(false);
            }
            finally
            {
                __vehicleDiagnosticSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual VehicleDiagnosticDataObject VehicleDiagnostic 
		{
			get
			{			
				return GetVehicleDiagnosticAsync(true).Result;
			}
			set
			{
				SetVehicleDiagnosticValue(value);
			}
		}
		
		public virtual bool ShouldSerializeVehicleDiagnostic()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleDiagnostic");
		}

		public virtual async Task<VehicleDiagnosticDataObject> GetVehicleDiagnosticAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			VehicleDiagnosticDataObject vehicleDiagnostic;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<VehicleDiagnosticDataObject>(this, "VehicleDiagnostic");
               	vehicleDiagnostic = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && vehicleDiagnostic == null && LazyLoadingEnabled && (!__vehicleDiagnosticAlreadyLazyLoaded || forceReload))
				{
					vehicleDiagnostic = await LoadVehicleDiagnosticAsync(forceReload : forceReload);
				}
			}
				
			return vehicleDiagnostic;
		}


		protected IDataProvider<VehicleGPSDataObject> _vehicleGPSService => _serviceProvider.GetRequiredService<IDataProvider<VehicleGPSDataObject>>();

		private readonly SemaphoreSlim __vehicleGPSLocationsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleGPSLocationsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleGPSLocations", which is a collection of VehicleGPSDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleGPSDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleGPSDataObject>> LoadVehicleGPSLocationsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleGPSLocationsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleGPSDataObject>> LoadVehicleGPSLocationsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleGPSLocationsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleGPSLocationsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleGPSService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleGPSLocationsAlreadyLazyLoaded = true;
                }

                return await GetVehicleGPSLocationsAsync(false);
            }
            finally
            {
                __vehicleGPSLocationsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleGPSDataObject> VehicleGPSLocations 
		{
			get
			{			
				return GetVehicleGPSLocationsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleGPSLocations()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleGPSLocations");
		}

		public virtual async Task<DataObjectCollection<VehicleGPSDataObject>> GetVehicleGPSLocationsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleGPSLocationsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleGPSLocationsAsync(forceReload : forceReload);
			}
			var vehicleGPSLocations = ObjectsDataSet.GetRelatedObjects<VehicleGPSDataObject>(this, "VehicleGPSLocations");							
			vehicleGPSLocations.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleGPSLocations_CollectionChanged);
				
			return vehicleGPSLocations;
		}

        private void VehicleGPSLocations_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleGPSDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleGPS", "VehicleDataObject.VehicleGPSLocations_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VehicleGPSDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleGPSDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleHireDehireHistoryDataObject> _vehicleHireDehireHistoryService => _serviceProvider.GetRequiredService<IDataProvider<VehicleHireDehireHistoryDataObject>>();

		private readonly SemaphoreSlim __vehicleHireDehireHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleHireDehireHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleHireDehireHistoryItems", which is a collection of VehicleHireDehireHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleHireDehireHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleHireDehireHistoryDataObject>> LoadVehicleHireDehireHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleHireDehireHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleHireDehireHistoryDataObject>> LoadVehicleHireDehireHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleHireDehireHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleHireDehireHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleHireDehireHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleHireDehireHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleHireDehireHistoryItemsAsync(false);
            }
            finally
            {
                __vehicleHireDehireHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleHireDehireHistoryDataObject> VehicleHireDehireHistoryItems 
		{
			get
			{			
				return GetVehicleHireDehireHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleHireDehireHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleHireDehireHistoryItems");
		}

		public virtual async Task<DataObjectCollection<VehicleHireDehireHistoryDataObject>> GetVehicleHireDehireHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleHireDehireHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleHireDehireHistoryItemsAsync(forceReload : forceReload);
			}
			var vehicleHireDehireHistoryItems = ObjectsDataSet.GetRelatedObjects<VehicleHireDehireHistoryDataObject>(this, "VehicleHireDehireHistoryItems");							
			vehicleHireDehireHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleHireDehireHistoryItems_CollectionChanged);
				
			return vehicleHireDehireHistoryItems;
		}

        private void VehicleHireDehireHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleHireDehireHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleHireDehireHistory", "VehicleDataObject.VehicleHireDehireHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VehicleHireDehireHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleHireDehireHistoryDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleLastGPSLocationViewDataObject> _vehicleLastGPSLocationViewService => _serviceProvider.GetRequiredService<IDataProvider<VehicleLastGPSLocationViewDataObject>>();
      public virtual void SetVehicleLastGPSLocationViewValue(VehicleLastGPSLocationViewDataObject valueToSet)
		{
			SetVehicleLastGPSLocationViewValue(valueToSet, true, true);
		}

        public virtual void SetVehicleLastGPSLocationViewValue(VehicleLastGPSLocationViewDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<VehicleLastGPSLocationViewDataObject>(this, "VehicleLastGPSLocationView");
			var existing_vehicleLastGPSLocationView = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("VehicleLastGPSLocationView", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._vehicle_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.Vehicle = this;
					valueToSet.VehicleId = this.Id;
				}			
			}
			else  if (existing_vehicleLastGPSLocationView != null)
            {
                ObjectsDataSet.RemoveObject(existing_vehicleLastGPSLocationView);
            }
			if (!ReferenceEquals(existing_vehicleLastGPSLocationView ,valueToSet))
				OnPropertyChanged("VehicleLastGPSLocationView", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __vehicleLastGPSLocationViewSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleLastGPSLocationViewAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleLastGPSLocationView", which is a VehicleLastGPSLocationViewDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a VehicleLastGPSLocationViewDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<VehicleLastGPSLocationViewDataObject> LoadVehicleLastGPSLocationViewAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleLastGPSLocationViewAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<VehicleLastGPSLocationViewDataObject> LoadVehicleLastGPSLocationViewAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleLastGPSLocationViewSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleLastGPSLocationViewAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data VehicleLastGPSLocationView for the current entity. The DataObjects doesn't have an ObjectsDataSet", "VehicleObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var vehicleLastGPSLocationView = (this.ObjectsDataSet as ObjectsDataSet).VehicleLastGPSLocationViewObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).VehicleLastGPSLocationViewObjects.Where(item => item.Value.VehicleId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(vehicleLastGPSLocationView, null))
					{
						var filterPredicate = "VehicleId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						vehicleLastGPSLocationView = (await _vehicleLastGPSLocationViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetVehicleLastGPSLocationViewValue(vehicleLastGPSLocationView, false, false);
						__vehicleLastGPSLocationViewAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a vehicleLastGPSLocationView, but relation field not set, encourage it to get set by removing and re-adding the vehicleLastGPSLocationView 
					if (vehicleLastGPSLocationView != null && this.VehicleLastGPSLocationView == null)
					{
						this.ObjectsDataSet.RemoveObject(vehicleLastGPSLocationView);
						this.ObjectsDataSet.AddObject(vehicleLastGPSLocationView);
					}			
                    __vehicleLastGPSLocationViewAlreadyLazyLoaded = true;
                }

                return await GetVehicleLastGPSLocationViewAsync(false);
            }
            finally
            {
                __vehicleLastGPSLocationViewSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual VehicleLastGPSLocationViewDataObject VehicleLastGPSLocationView 
		{
			get
			{			
				return GetVehicleLastGPSLocationViewAsync(true).Result;
			}
			set
			{
				SetVehicleLastGPSLocationViewValue(value);
			}
		}
		
		public virtual bool ShouldSerializeVehicleLastGPSLocationView()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleLastGPSLocationView");
		}

		public virtual async Task<VehicleLastGPSLocationViewDataObject> GetVehicleLastGPSLocationViewAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			VehicleLastGPSLocationViewDataObject vehicleLastGPSLocationView;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<VehicleLastGPSLocationViewDataObject>(this, "VehicleLastGPSLocationView");
               	vehicleLastGPSLocationView = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && vehicleLastGPSLocationView == null && LazyLoadingEnabled && (!__vehicleLastGPSLocationViewAlreadyLazyLoaded || forceReload))
				{
					vehicleLastGPSLocationView = await LoadVehicleLastGPSLocationViewAsync(forceReload : forceReload);
				}
			}
				
			return vehicleLastGPSLocationView;
		}


		protected IDataProvider<VehicleLockoutDataObject> _vehicleLockoutService => _serviceProvider.GetRequiredService<IDataProvider<VehicleLockoutDataObject>>();

		private readonly SemaphoreSlim __vehicleLockoutItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleLockoutItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleLockoutItems", which is a collection of VehicleLockoutDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleLockoutDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> LoadVehicleLockoutItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleLockoutItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> LoadVehicleLockoutItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleLockoutItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleLockoutItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleLockoutService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleLockoutItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleLockoutItemsAsync(false);
            }
            finally
            {
                __vehicleLockoutItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleLockoutDataObject> VehicleLockoutItems 
		{
			get
			{			
				return GetVehicleLockoutItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleLockoutItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleLockoutItems");
		}

		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> GetVehicleLockoutItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleLockoutItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleLockoutItemsAsync(forceReload : forceReload);
			}
			var vehicleLockoutItems = ObjectsDataSet.GetRelatedObjects<VehicleLockoutDataObject>(this, "VehicleLockoutItems");							
			vehicleLockoutItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleLockoutItems_CollectionChanged);
				
			return vehicleLockoutItems;
		}

        private void VehicleLockoutItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleLockoutDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleLockout", "VehicleDataObject.VehicleLockoutItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VehicleLockoutDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(Nullable<System.Guid>))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleLockoutDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleOtherSettingsDataObject> _vehicleOtherSettingsService => _serviceProvider.GetRequiredService<IDataProvider<VehicleOtherSettingsDataObject>>();
      public virtual void SetVehicleOtherSettingsValue(VehicleOtherSettingsDataObject valueToSet)
		{
			SetVehicleOtherSettingsValue(valueToSet, true, true);
		}

        public virtual void SetVehicleOtherSettingsValue(VehicleOtherSettingsDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			VehicleOtherSettingsDataObject existing_vehicleOtherSettings = null ;

			if ( !(this.VehicleOtherSettingsId == null || ObjectsDataSet == null))
			{
				VehicleOtherSettingsDataObject key;

				if (this._vehicleOtherSettings_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>().Initialize((System.Guid)this.VehicleOtherSettingsId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._vehicleOtherSettings_NewObjectId;			
				}

				existing_vehicleOtherSettings = (VehicleOtherSettingsDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_vehicleOtherSettings ,valueToSet))
            {
                if (valueToSet == null)
                {
					_vehicleOtherSettings_NewObjectId = null;
					_vehicleOtherSettingsId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("VehicleOtherSettings", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "VehicleDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_vehicleOtherSettings_NewObjectId != valueToSet.InternalObjectId)
					{
						_vehicleOtherSettings_NewObjectId = valueToSet.InternalObjectId;
						_vehicleOtherSettingsId = valueToSet.Id;
						OnPropertyChanged("VehicleOtherSettingsId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_vehicleOtherSettingsId != valueToSet.Id)
					{
						_vehicleOtherSettings_NewObjectId = null;

						_vehicleOtherSettingsId = valueToSet.Id;
						OnPropertyChanged("VehicleOtherSettingsId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_vehicleOtherSettings_NewObjectId = null;
					_vehicleOtherSettingsId = null;
					
				OnPropertyChanged("VehicleOtherSettingsId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_vehicleOtherSettings ,valueToSet))
				OnPropertyChanged("VehicleOtherSettings", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __vehicleOtherSettingsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleOtherSettingsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleOtherSettings", which is a VehicleOtherSettingsDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a VehicleOtherSettingsDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<VehicleOtherSettingsDataObject> LoadVehicleOtherSettingsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleOtherSettingsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<VehicleOtherSettingsDataObject> LoadVehicleOtherSettingsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleOtherSettingsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleOtherSettingsAlreadyLazyLoaded || forceReload)
                {
								
					if (this.VehicleOtherSettingsId == null)
					{
						return null;
					}
				
					VehicleOtherSettingsDataObject vehicleOtherSettings = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __vehicleOtherSettingsAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						vehicleOtherSettings = _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>().Initialize((System.Guid)this.VehicleOtherSettingsId);
						vehicleOtherSettings.IsNew = false;
						vehicleOtherSettings = (VehicleOtherSettingsDataObject)ObjectsDataSet.GetObject(vehicleOtherSettings);
						if (vehicleOtherSettings != null)
						{
							return vehicleOtherSettings;
						}
					}

					vehicleOtherSettings = await _vehicleOtherSettingsService.GetAsync(_serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>().Initialize((System.Guid)this.VehicleOtherSettingsId), parameters : parameters, skipSecurity: skipSecurity);

					SetVehicleOtherSettingsValue(vehicleOtherSettings, false, false);
					__vehicleOtherSettingsAlreadyLazyLoaded = true;				
		
					vehicleOtherSettings = _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>().Initialize((System.Guid)this.VehicleOtherSettingsId);
					vehicleOtherSettings.IsNew = false;
					vehicleOtherSettings = (VehicleOtherSettingsDataObject)ObjectsDataSet.GetObject(vehicleOtherSettings);
                    __vehicleOtherSettingsAlreadyLazyLoaded = true;
                }

                return await GetVehicleOtherSettingsAsync(false);
            }
            finally
            {
                __vehicleOtherSettingsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual VehicleOtherSettingsDataObject VehicleOtherSettings 
		{
			get
			{			
				return GetVehicleOtherSettingsAsync(true).Result;
			}
			set
			{
				SetVehicleOtherSettingsValue(value);
			}
		}
		
		public virtual bool ShouldSerializeVehicleOtherSettings()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleOtherSettings");
		}

		public virtual async Task<VehicleOtherSettingsDataObject> GetVehicleOtherSettingsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			VehicleOtherSettingsDataObject vehicleOtherSettings;

				
			if (_vehicleOtherSettings_NewObjectId != null)
			{
				vehicleOtherSettings = _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>();
				vehicleOtherSettings.IsNew = true;
				vehicleOtherSettings.InternalObjectId = _vehicleOtherSettings_NewObjectId;
				vehicleOtherSettings = (VehicleOtherSettingsDataObject)ObjectsDataSet.GetObject(vehicleOtherSettings);
			}
			else
			{
				if (this.VehicleOtherSettingsId == null)
					return null;
				if (VehicleOtherSettingsId == null)
					vehicleOtherSettings = null;
				else
				vehicleOtherSettings = _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>().Initialize((System.Guid)this.VehicleOtherSettingsId);
				vehicleOtherSettings.IsNew = false;
				vehicleOtherSettings = (VehicleOtherSettingsDataObject)ObjectsDataSet.GetObject(vehicleOtherSettings);
				
				if (allowLazyLoading && vehicleOtherSettings == null && LazyLoadingEnabled && (!__vehicleOtherSettingsAlreadyLazyLoaded || forceReload))
				{
					vehicleOtherSettings = await LoadVehicleOtherSettingsAsync(forceReload : forceReload);
				}
			}
				
			return vehicleOtherSettings;
		}

		public virtual Nullable<System.Guid> VehicleOtherSettingsForeignKey
		{
			get { return VehicleOtherSettingsId; }
			set 
			{	
				VehicleOtherSettingsId = value;
			}
			
		}
		

		protected IDataProvider<VehicleProficiencyViewDataObject> _vehicleProficiencyViewService => _serviceProvider.GetRequiredService<IDataProvider<VehicleProficiencyViewDataObject>>();

		private readonly SemaphoreSlim __vehicleProficiencyViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleProficiencyViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleProficiencyViewItems", which is a collection of VehicleProficiencyViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleProficiencyViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleProficiencyViewDataObject>> LoadVehicleProficiencyViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleProficiencyViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleProficiencyViewDataObject>> LoadVehicleProficiencyViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleProficiencyViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleProficiencyViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleProficiencyViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleProficiencyViewItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleProficiencyViewItemsAsync(false);
            }
            finally
            {
                __vehicleProficiencyViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleProficiencyViewDataObject> VehicleProficiencyViewItems 
		{
			get
			{			
				return GetVehicleProficiencyViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleProficiencyViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleProficiencyViewItems");
		}

		public virtual async Task<DataObjectCollection<VehicleProficiencyViewDataObject>> GetVehicleProficiencyViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleProficiencyViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleProficiencyViewItemsAsync(forceReload : forceReload);
			}
			var vehicleProficiencyViewItems = ObjectsDataSet.GetRelatedObjects<VehicleProficiencyViewDataObject>(this, "VehicleProficiencyViewItems");							
			vehicleProficiencyViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleProficiencyViewItems_CollectionChanged);
				
			return vehicleProficiencyViewItems;
		}

        private void VehicleProficiencyViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleProficiencyViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleProficiencyView", "VehicleDataObject.VehicleProficiencyViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VehicleProficiencyViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleProficiencyViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleSessionlessImpactDataObject> _vehicleSessionlessImpactService => _serviceProvider.GetRequiredService<IDataProvider<VehicleSessionlessImpactDataObject>>();

		private readonly SemaphoreSlim __vehicleSessionlessImpactItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleSessionlessImpactItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleSessionlessImpactItems", which is a collection of VehicleSessionlessImpactDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleSessionlessImpactDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleSessionlessImpactDataObject>> LoadVehicleSessionlessImpactItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleSessionlessImpactItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleSessionlessImpactDataObject>> LoadVehicleSessionlessImpactItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleSessionlessImpactItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleSessionlessImpactItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleSessionlessImpactService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleSessionlessImpactItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleSessionlessImpactItemsAsync(false);
            }
            finally
            {
                __vehicleSessionlessImpactItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleSessionlessImpactDataObject> VehicleSessionlessImpactItems 
		{
			get
			{			
				return GetVehicleSessionlessImpactItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleSessionlessImpactItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleSessionlessImpactItems");
		}

		public virtual async Task<DataObjectCollection<VehicleSessionlessImpactDataObject>> GetVehicleSessionlessImpactItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleSessionlessImpactItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleSessionlessImpactItemsAsync(forceReload : forceReload);
			}
			var vehicleSessionlessImpactItems = ObjectsDataSet.GetRelatedObjects<VehicleSessionlessImpactDataObject>(this, "VehicleSessionlessImpactItems");							
			vehicleSessionlessImpactItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleSessionlessImpactItems_CollectionChanged);
				
			return vehicleSessionlessImpactItems;
		}

        private void VehicleSessionlessImpactItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleSessionlessImpactDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleSessionlessImpact", "VehicleDataObject.VehicleSessionlessImpactItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VehicleSessionlessImpactDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleSessionlessImpactDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleSupervisorsViewDataObject> _vehicleSupervisorsViewService => _serviceProvider.GetRequiredService<IDataProvider<VehicleSupervisorsViewDataObject>>();

		private readonly SemaphoreSlim __vehicleSupervisorsViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleSupervisorsViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleSupervisorsViewItems", which is a collection of VehicleSupervisorsViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleSupervisorsViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleSupervisorsViewDataObject>> LoadVehicleSupervisorsViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleSupervisorsViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleSupervisorsViewDataObject>> LoadVehicleSupervisorsViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleSupervisorsViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleSupervisorsViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleSupervisorsViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleSupervisorsViewItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleSupervisorsViewItemsAsync(false);
            }
            finally
            {
                __vehicleSupervisorsViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleSupervisorsViewDataObject> VehicleSupervisorsViewItems 
		{
			get
			{			
				return GetVehicleSupervisorsViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleSupervisorsViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleSupervisorsViewItems");
		}

		public virtual async Task<DataObjectCollection<VehicleSupervisorsViewDataObject>> GetVehicleSupervisorsViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleSupervisorsViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleSupervisorsViewItemsAsync(forceReload : forceReload);
			}
			var vehicleSupervisorsViewItems = ObjectsDataSet.GetRelatedObjects<VehicleSupervisorsViewDataObject>(this, "VehicleSupervisorsViewItems");							
			vehicleSupervisorsViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleSupervisorsViewItems_CollectionChanged);
				
			return vehicleSupervisorsViewItems;
		}

        private void VehicleSupervisorsViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleSupervisorsViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleSupervisorsView", "VehicleDataObject.VehicleSupervisorsViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VehicleSupervisorsViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleSupervisorsViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleToPreOpChecklistViewDataObject> _vehicleToPreOpChecklistViewService => _serviceProvider.GetRequiredService<IDataProvider<VehicleToPreOpChecklistViewDataObject>>();

		private readonly SemaphoreSlim __vehicleToPreOpCheckilstItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleToPreOpCheckilstItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleToPreOpCheckilstItems", which is a collection of VehicleToPreOpChecklistViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleToPreOpChecklistViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleToPreOpChecklistViewDataObject>> LoadVehicleToPreOpCheckilstItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleToPreOpCheckilstItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleToPreOpChecklistViewDataObject>> LoadVehicleToPreOpCheckilstItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleToPreOpCheckilstItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleToPreOpCheckilstItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleToPreOpChecklistViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleToPreOpCheckilstItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleToPreOpCheckilstItemsAsync(false);
            }
            finally
            {
                __vehicleToPreOpCheckilstItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleToPreOpChecklistViewDataObject> VehicleToPreOpCheckilstItems 
		{
			get
			{			
				return GetVehicleToPreOpCheckilstItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleToPreOpCheckilstItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VehicleToPreOpCheckilstItems");
		}

		public virtual async Task<DataObjectCollection<VehicleToPreOpChecklistViewDataObject>> GetVehicleToPreOpCheckilstItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleToPreOpCheckilstItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleToPreOpCheckilstItemsAsync(forceReload : forceReload);
			}
			var vehicleToPreOpCheckilstItems = ObjectsDataSet.GetRelatedObjects<VehicleToPreOpChecklistViewDataObject>(this, "VehicleToPreOpCheckilstItems");							
			vehicleToPreOpCheckilstItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleToPreOpCheckilstItems_CollectionChanged);
				
			return vehicleToPreOpCheckilstItems;
		}

        private void VehicleToPreOpCheckilstItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleToPreOpChecklistViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleToPreOpChecklistView", "VehicleDataObject.VehicleToPreOpCheckilstItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VehicleToPreOpChecklistViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleToPreOpChecklistViewDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VORSettingHistoryDataObject> _vORSettingHistoryService => _serviceProvider.GetRequiredService<IDataProvider<VORSettingHistoryDataObject>>();

		private readonly SemaphoreSlim __vORSettingHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vORSettingHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VORSettingHistoryItems", which is a collection of VORSettingHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VORSettingHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VORSettingHistoryDataObject>> LoadVORSettingHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVORSettingHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VORSettingHistoryDataObject>> LoadVORSettingHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vORSettingHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vORSettingHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "VehicleId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vORSettingHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vORSettingHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetVORSettingHistoryItemsAsync(false);
            }
            finally
            {
                __vORSettingHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VORSettingHistoryDataObject> VORSettingHistoryItems 
		{
			get
			{			
				return GetVORSettingHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVORSettingHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("VehicleDataObject") && ObjectsDataSet.RelationsToInclude["VehicleDataObject"].Contains("VORSettingHistoryItems");
		}

		public virtual async Task<DataObjectCollection<VORSettingHistoryDataObject>> GetVORSettingHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vORSettingHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVORSettingHistoryItemsAsync(forceReload : forceReload);
			}
			var vORSettingHistoryItems = ObjectsDataSet.GetRelatedObjects<VORSettingHistoryDataObject>(this, "VORSettingHistoryItems");							
			vORSettingHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VORSettingHistoryItems_CollectionChanged);
				
			return vORSettingHistoryItems;
		}

        private void VORSettingHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VORSettingHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VORSettingHistory", "VehicleDataObject.VORSettingHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of VehicleDataObject throw an exception while trying to add VORSettingHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._vehicle_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.VehicleId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.VehicleId == default(System.Guid))
							relatedObj.VehicleId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VORSettingHistoryDataObject).Vehicle = null;
                    // }
                    break;
            }            
        }

		public override void ClearLazyLoadFlags()
		{
			__allVehicleCalibrationStoreProcedureItemsAlreadyLazyLoaded = false;
			__allVORSessionsPerVehicleStoreProcedureItemsAlreadyLazyLoaded = false;
			__broadcastMessageHistoryItemsAlreadyLazyLoaded = false;
			__checklistFailurePerVechicleViewItemsAlreadyLazyLoaded = false;
			__currentStatusVehicleViewItemsAlreadyLazyLoaded = false;
			__detailedSessionViewItemsAlreadyLazyLoaded = false;
			__detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded = false;
			__generalProductivityPerVehicleViewItemsAlreadyLazyLoaded = false;
			__impactsForVehicleViewItemsAlreadyLazyLoaded = false;
			__messageHistoryItemsAlreadyLazyLoaded = false;
			__moduleHistoryItemsAlreadyLazyLoaded = false;
			__networkSettingsItemsAlreadyLazyLoaded = false;
			__onDemandSessionItemsAlreadyLazyLoaded = false;
			__pedestrianDetectionHistoryItemsAlreadyLazyLoaded = false;
			__personToPerVehicleMasterAccessViewItemsAlreadyLazyLoaded = false;
			__personToPerVehicleNormalAccessViewItemsAlreadyLazyLoaded = false;
			__perVehicleNormalCardAccessItemsAlreadyLazyLoaded = false;
			__sessionsAlreadyLazyLoaded = false;
			__slamcoreDeviceHistoryItemsAlreadyLazyLoaded = false;
			__unitUnutilisationStoreProcedureItemsAlreadyLazyLoaded = false;
			__unitUtilisationStoreProcedureItemsAlreadyLazyLoaded = false;
			__vehicleAlertSubscriptionItemsAlreadyLazyLoaded = false;
			__vehicleBroadcastMessageItemsAlreadyLazyLoaded = false;
			__vehicleCardAccessesAlreadyLazyLoaded = false;
			__vehicleGPSLocationsAlreadyLazyLoaded = false;
			__vehicleHireDehireHistoryItemsAlreadyLazyLoaded = false;
			__vehicleLockoutItemsAlreadyLazyLoaded = false;
			__vehicleProficiencyViewItemsAlreadyLazyLoaded = false;
			__vehicleSessionlessImpactItemsAlreadyLazyLoaded = false;
			__vehicleSupervisorsViewItemsAlreadyLazyLoaded = false;
			__vehicleToPreOpCheckilstItemsAlreadyLazyLoaded = false;
			__vORSettingHistoryItemsAlreadyLazyLoaded = false;
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadCanruleAsync()) != null)
				result.Add(Canrule);
			if ((await LoadChecklistSettingsAsync()) != null)
				result.Add(ChecklistSettings);
			if ((await LoadCustomerAsync()) != null)
				result.Add(Customer);
			if ((await LoadDepartmentAsync()) != null)
				result.Add(Department);
			if ((await LoadDepartmentChecklistAsync()) != null)
				result.Add(DepartmentChecklist);
			if ((await LoadDriverAsync()) != null)
				result.Add(Driver);
			if ((await LoadFirmwareAsync()) != null)
				result.Add(Firmware);
			if ((await LoadInspectionAsync()) != null)
				result.Add(Inspection);
			if ((await LoadModelAsync()) != null)
				result.Add(Model);
			if ((await LoadModuleAsync()) != null)
				result.Add(Module);
			if ((await LoadPersonAsync()) != null)
				result.Add(Person);
			if ((await LoadServiceSettingsAsync()) != null)
				result.Add(ServiceSettings);
			if ((await LoadSiteAsync()) != null)
				result.Add(Site);
			if ((await LoadVehicleOtherSettingsAsync()) != null)
				result.Add(VehicleOtherSettings);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadAllVehicleCalibrationStoreProcedureItemsAsync()) != null)
				result.AddRange(AllVehicleCalibrationStoreProcedureItems);
			if ((await LoadAllVORSessionsPerVehicleStoreProcedureItemsAsync()) != null)
				result.AddRange(AllVORSessionsPerVehicleStoreProcedureItems);
			if ((await LoadBroadcastMessageHistoryItemsAsync()) != null)
				result.AddRange(BroadcastMessageHistoryItems);
			if ((await LoadChecklistFailurePerVechicleViewItemsAsync()) != null)
				result.AddRange(ChecklistFailurePerVechicleViewItems);
			if ((await LoadCurrentStatusVehicleViewItemsAsync()) != null)
				result.AddRange(CurrentStatusVehicleViewItems);
			if ((await LoadDetailedSessionViewItemsAsync()) != null)
				result.AddRange(DetailedSessionViewItems);
			if ((await LoadDetailedVORSessionStoreProcedureItemsAsync()) != null)
				result.AddRange(DetailedVORSessionStoreProcedureItems);
			if ((await LoadGeneralProductivityPerVehicleViewItemsAsync()) != null)
				result.AddRange(GeneralProductivityPerVehicleViewItems);
			if ((await LoadImpactsForVehicleViewItemsAsync()) != null)
				result.AddRange(ImpactsForVehicleViewItems);
			if ((await LoadMessageHistoryItemsAsync()) != null)
				result.AddRange(MessageHistoryItems);
			if ((await LoadModuleHistoryItemsAsync()) != null)
				result.AddRange(ModuleHistoryItems);
			if ((await LoadNetworkSettingsItemsAsync()) != null)
				result.AddRange(NetworkSettingsItems);
			if ((await LoadOnDemandSessionItemsAsync()) != null)
				result.AddRange(OnDemandSessionItems);
			if ((await LoadOnDemandSettingsAsync()) != null)
				result.Add(OnDemandSettings);
			if ((await LoadPedestrianDetectionHistoryItemsAsync()) != null)
				result.AddRange(PedestrianDetectionHistoryItems);
			if ((await LoadPersonToPerVehicleMasterAccessViewItemsAsync()) != null)
				result.AddRange(PersonToPerVehicleMasterAccessViewItems);
			if ((await LoadPersonToPerVehicleNormalAccessViewItemsAsync()) != null)
				result.AddRange(PersonToPerVehicleNormalAccessViewItems);
			if ((await LoadPerVehicleNormalCardAccessItemsAsync()) != null)
				result.AddRange(PerVehicleNormalCardAccessItems);
			if ((await LoadSessionsAsync()) != null)
				result.AddRange(Sessions);
			if ((await LoadSlamcoreDeviceHistoryItemsAsync()) != null)
				result.AddRange(SlamcoreDeviceHistoryItems);
			if ((await LoadUnitUnutilisationStoreProcedureItemsAsync()) != null)
				result.AddRange(UnitUnutilisationStoreProcedureItems);
			if ((await LoadUnitUtilisationStoreProcedureItemsAsync()) != null)
				result.AddRange(UnitUtilisationStoreProcedureItems);
			if ((await LoadVehicleAlertSubscriptionItemsAsync()) != null)
				result.AddRange(VehicleAlertSubscriptionItems);
			if ((await LoadVehicleBroadcastMessageItemsAsync()) != null)
				result.AddRange(VehicleBroadcastMessageItems);
			if ((await LoadVehicleCardAccessesAsync()) != null)
				result.AddRange(VehicleCardAccesses);
			if ((await LoadVehicleDiagnosticAsync()) != null)
				result.Add(VehicleDiagnostic);
			if ((await LoadVehicleGPSLocationsAsync()) != null)
				result.AddRange(VehicleGPSLocations);
			if ((await LoadVehicleHireDehireHistoryItemsAsync()) != null)
				result.AddRange(VehicleHireDehireHistoryItems);
			if ((await LoadVehicleLastGPSLocationViewAsync()) != null)
				result.Add(VehicleLastGPSLocationView);
			if ((await LoadVehicleLockoutItemsAsync()) != null)
				result.AddRange(VehicleLockoutItems);
			if ((await LoadVehicleProficiencyViewItemsAsync()) != null)
				result.AddRange(VehicleProficiencyViewItems);
			if ((await LoadVehicleSessionlessImpactItemsAsync()) != null)
				result.AddRange(VehicleSessionlessImpactItems);
			if ((await LoadVehicleSupervisorsViewItemsAsync()) != null)
				result.AddRange(VehicleSupervisorsViewItems);
			if ((await LoadVehicleToPreOpCheckilstItemsAsync()) != null)
				result.AddRange(VehicleToPreOpCheckilstItems);
			if ((await LoadVORSettingHistoryItemsAsync()) != null)
				result.AddRange(VORSettingHistoryItems);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				Canrule == other ||
				(other is CanruleDataObject && (CanruleId != default(Nullable<System.Guid>)) && (CanruleId == (other as CanruleDataObject).Id)) || 
				Firmware == other ||
				(other is FirmwareDataObject && (FirmwareId != default(Nullable<System.Guid>)) && (FirmwareId == (other as FirmwareDataObject).Id)) || 
				Department == other ||
				(other is DepartmentDataObject && (DepartmentId != default(System.Guid)) && (DepartmentId == (other as DepartmentDataObject).Id)) || 
				Driver == other ||
				(other is DriverDataObject && (DriverId != default(Nullable<System.Guid>)) && (DriverId == (other as DriverDataObject).Id)) || 
				Customer == other ||
				(other is CustomerDataObject && (CustomerId != default(System.Guid)) && (CustomerId == (other as CustomerDataObject).Id)) || 
				ServiceSettings == other ||
				(other is ServiceSettingsDataObject && (ServiceSettingsId != default(Nullable<System.Guid>)) && (ServiceSettingsId == (other as ServiceSettingsDataObject).Id)) || 
				Module == other ||
				(other is ModuleDataObject && (ModuleId1 != default(System.Guid)) && (ModuleId1 == (other as ModuleDataObject).Id)) || 
				VehicleOtherSettings == other ||
				(other is VehicleOtherSettingsDataObject && (VehicleOtherSettingsId != default(Nullable<System.Guid>)) && (VehicleOtherSettingsId == (other as VehicleOtherSettingsDataObject).Id)) || 
				ChecklistSettings == other ||
				(other is ChecklistSettingsDataObject && (ChecklistSettingsId != default(Nullable<System.Guid>)) && (ChecklistSettingsId == (other as ChecklistSettingsDataObject).Id)) || 
				Model == other ||
				(other is ModelDataObject && (ModelId != default(System.Guid)) && (ModelId == (other as ModelDataObject).Id)) || 
				Person == other ||
				(other is PersonDataObject && (PersonId != default(Nullable<System.Guid>)) && (PersonId == (other as PersonDataObject).Id)) || 
				Inspection == other ||
				(other is InspectionDataObject && (InspectionId != default(Nullable<System.Guid>)) && (InspectionId == (other as InspectionDataObject).Id)) || 
				Site == other ||
				(other is SiteDataObject && (SiteId != default(System.Guid)) && (SiteId == (other as SiteDataObject).Id)) || 
				DepartmentChecklist == other ||
				(other is DepartmentChecklistDataObject && (DepartmentChecklistId != default(Nullable<System.Guid>)) && (DepartmentChecklistId == (other as DepartmentChecklistDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
		
		/// <summary> The Actions property of the Vehicle DataObject</summary>
        public virtual System.String Actions 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return "<div class=\"icon-container\">    <a href=\"#\" onclick=\"event.preventDefault(); $(this).closest('tr').click(); ApplicationController.viewModel.pageController().VehilceItemsViewModel.commands.showUnlockVehiclePopupCommand();\">       <svg class=\"bi icon-unlock\" width=\"16\" height=\"16\">          <use xlink:href=\"Styles/Images/bootstrap-icons.svg#unlock\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" />       </svg>    </a>    <span class=\"tooltip-text\">Unlock Vehicle</span>    </div> <div class=\"icon-container\">    <a href=\"#\" onclick=\"event.preventDefault(); $(this).closest('tr').click(); ApplicationController.viewModel.pageController().VehilceItemsViewModel.commands.showRebootVehiclePopupCommand();\">       <svg class=\"bi icon-unlock\" width=\"16\" height=\"16\">          <use xlink:href=\"Styles/Images/bootstrap-icons.svg#bootstrap-reboot\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" />       </svg>    </a>    <span class=\"tooltip-text\">Reboot Vehicle</span>    </div>";				
			}
			
		}		
			
			
		public virtual void SetCanruleIdValue(Nullable<System.Guid> valueToSet)
		{
			SetCanruleIdValue(valueToSet, true, true);
		}

		public virtual void SetCanruleIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_canruleId != valueToSet)
			{
				_canruleId = valueToSet;

				// CanruleId is a FK. Setting its value should result in a event
				OnPropertyChanged("Canrule", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("CanruleId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The CanruleId property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Guid> CanruleId 
		{
			get	{ return _canruleId;}
			
			
			set
			{
				SetCanruleIdValue(value);
			}
		}		
			
			
		public virtual void SetChecklistSettingsIdValue(Nullable<System.Guid> valueToSet)
		{
			SetChecklistSettingsIdValue(valueToSet, true, true);
		}

		public virtual void SetChecklistSettingsIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_checklistSettingsId != valueToSet)
			{
				_checklistSettingsId = valueToSet;

				// ChecklistSettingsId is a FK. Setting its value should result in a event
				OnPropertyChanged("ChecklistSettings", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("ChecklistSettingsId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The ChecklistSettingsId property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Guid> ChecklistSettingsId 
		{
			get	{ return _checklistSettingsId;}
			
			
			set
			{
				SetChecklistSettingsIdValue(value);
			}
		}		
			
			
		public virtual void SetCustomerIdValue(System.Guid valueToSet)
		{
			SetCustomerIdValue(valueToSet, true, true);
		}

		public virtual void SetCustomerIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_customerId != valueToSet)
			{
				_customerId = valueToSet;

				OnPropertyChanged("CustomerId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The CustomerId property of the Vehicle DataObject</summary>
        public virtual System.Guid CustomerId 
		{
			get	{ return _customerId;}
			
			
			set
			{
				SetCustomerIdValue(value);
			}
		}		
			
			
		public virtual void SetDehireTimeValue(Nullable<System.DateTime> valueToSet)
		{
			SetDehireTimeValue(valueToSet, true, true);
		}

		public virtual void SetDehireTimeValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_dehireTime != null)
				{
					_dehireTime = null;
					OnPropertyChanged("DehireTime", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_dehireTime != DateTime.MinValue.ToUniversalTime())
				{
					_dehireTime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("DehireTime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_dehireTime != DateTime.MaxValue.ToUniversalTime())
				{
					_dehireTime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("DehireTime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_dehireTime != valueToSet ||
                (_dehireTime != null && ((DateTime)_dehireTime).Kind == DateTimeKind.Unspecified))
			{
				_dehireTime = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("DehireTime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Dehire Time property of the Vehicle DataObject</summary>
        public virtual Nullable<System.DateTime> DehireTime 
		{
			get	{ return _dehireTime;}
			
			
			set
			{
				SetDehireTimeValue(value);
			}
		}		
			
			
		public virtual void SetDeletedAtUtcValue(Nullable<System.DateTime> valueToSet)
		{
			SetDeletedAtUtcValue(valueToSet, true, true);
		}

		public virtual void SetDeletedAtUtcValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_deletedAtUtc != null)
				{
					_deletedAtUtc = null;
					OnPropertyChanged("DeletedAtUtc", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_deletedAtUtc != DateTime.MinValue.ToUniversalTime())
				{
					_deletedAtUtc = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("DeletedAtUtc", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_deletedAtUtc != DateTime.MaxValue.ToUniversalTime())
				{
					_deletedAtUtc = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("DeletedAtUtc", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_deletedAtUtc != valueToSet ||
                (_deletedAtUtc != null && ((DateTime)_deletedAtUtc).Kind == DateTimeKind.Unspecified))
			{
				_deletedAtUtc = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("DeletedAtUtc", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DeletedAtUtc property of the Vehicle DataObject</summary>
        public virtual Nullable<System.DateTime> DeletedAtUtc 
		{
			get	{ return _deletedAtUtc;}
			
			
			set
			{
				SetDeletedAtUtcValue(value);
			}
		}		
			
		
		/// <summary> The Department and model property of the Vehicle DataObject</summary>
        public virtual System.String DepartmentAndModel 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ("Department : \"" + Department.Name + "\" Model: \"" + Model.Name + "\"");				
			}
			
		}		
			
			
		public virtual void SetDepartmentChecklistIdValue(Nullable<System.Guid> valueToSet)
		{
			SetDepartmentChecklistIdValue(valueToSet, true, true);
		}

		public virtual void SetDepartmentChecklistIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_departmentChecklistId != valueToSet)
			{
				_departmentChecklistId = valueToSet;

				// DepartmentChecklistId is a FK. Setting its value should result in a event
				OnPropertyChanged("DepartmentChecklist", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("DepartmentChecklistId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DepartmentChecklistId property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Guid> DepartmentChecklistId 
		{
			get	{ return _departmentChecklistId;}
			
			
			set
			{
				SetDepartmentChecklistIdValue(value);
			}
		}		
			
			
		public virtual void SetDepartmentIdValue(System.Guid valueToSet)
		{
			SetDepartmentIdValue(valueToSet, true, true);
		}

		public virtual void SetDepartmentIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_departmentId != valueToSet)
			{
				_departmentId = valueToSet;

				OnPropertyChanged("DepartmentId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DepartmentId property of the Vehicle DataObject</summary>
        public virtual System.Guid DepartmentId 
		{
			get	{ return _departmentId;}
			
			
			set
			{
				SetDepartmentIdValue(value);
			}
		}		
			
			
		public virtual void SetDescriptionValue(System.String valueToSet)
		{
			SetDescriptionValue(valueToSet, true, true);
		}

		public virtual void SetDescriptionValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_description != valueToSet)
			{
				_description = valueToSet;

				OnPropertyChanged("Description", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Description property of the Vehicle DataObject</summary>
        public virtual System.String Description 
		{
			get	{ return String.IsNullOrEmpty(_description) ? null : _description; }
			
			
			set
			{
				SetDescriptionValue(value);
			}
		}		
			
			
		public virtual void SetDriverIdValue(Nullable<System.Guid> valueToSet)
		{
			SetDriverIdValue(valueToSet, true, true);
		}

		public virtual void SetDriverIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_driverId != valueToSet)
			{
				_driverId = valueToSet;

				// DriverId is a FK. Setting its value should result in a event
				OnPropertyChanged("Driver", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("DriverId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DriverId property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Guid> DriverId 
		{
			get	{ return _driverId;}
			
			
			set
			{
				SetDriverIdValue(value);
			}
		}		
			
			
		public virtual void SetFirmwareIdValue(Nullable<System.Guid> valueToSet)
		{
			SetFirmwareIdValue(valueToSet, true, true);
		}

		public virtual void SetFirmwareIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_firmwareId != valueToSet)
			{
				_firmwareId = valueToSet;

				OnPropertyChanged("FirmwareId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The FirmwareId property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Guid> FirmwareId 
		{
			get	{ return _firmwareId;}
			
			
			set
			{
				SetFirmwareIdValue(value);
			}
		}		
			
			
		public virtual void SetGPSDateTimeDisplayValue(System.String valueToSet)
		{
			SetGPSDateTimeDisplayValue(valueToSet, true, true);
		}

		public virtual void SetGPSDateTimeDisplayValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_gPSDateTimeDisplay != valueToSet)
			{
				_gPSDateTimeDisplay = valueToSet;

				OnPropertyChanged("GPSDateTimeDisplay", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The GPSDateTimeDisplay property of the Vehicle DataObject</summary>
        public virtual System.String GPSDateTimeDisplay 
		{
			get	{ return String.IsNullOrEmpty(_gPSDateTimeDisplay) ? null : _gPSDateTimeDisplay; }
			
			
			set
			{
				SetGPSDateTimeDisplayValue(value);
			}
		}		
			
			
		public virtual void SetHireNoValue(System.String valueToSet)
		{
			SetHireNoValue(valueToSet, true, true);
		}

		public virtual void SetHireNoValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_hireNo != valueToSet)
			{
				_hireNo = valueToSet;

				OnPropertyChanged("HireNo", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Hire No property of the Vehicle DataObject</summary>
        public virtual System.String HireNo 
		{
			get	{ return _hireNo; }
			
			
			set
			{
				SetHireNoValue(value);
			}
		}		
			
			
		public virtual void SetHireTimeValue(Nullable<System.DateTime> valueToSet)
		{
			SetHireTimeValue(valueToSet, true, true);
		}

		public virtual void SetHireTimeValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_hireTime != null)
				{
					_hireTime = null;
					OnPropertyChanged("HireTime", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_hireTime != DateTime.MinValue.ToUniversalTime())
				{
					_hireTime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("HireTime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_hireTime != DateTime.MaxValue.ToUniversalTime())
				{
					_hireTime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("HireTime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_hireTime != valueToSet ||
                (_hireTime != null && ((DateTime)_hireTime).Kind == DateTimeKind.Unspecified))
			{
				_hireTime = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("HireTime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Hire Time property of the Vehicle DataObject</summary>
        public virtual Nullable<System.DateTime> HireTime 
		{
			get	{ return _hireTime;}
			
			
			set
			{
				SetHireTimeValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the Vehicle DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetIDLETimerValue(Nullable<System.Int32> valueToSet)
		{
			SetIDLETimerValue(valueToSet, true, true);
		}

		public virtual void SetIDLETimerValue(Nullable<System.Int32> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_iDLETimer != valueToSet)
			{
				_iDLETimer = valueToSet;

				OnPropertyChanged("IDLETimer", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The IDLE Timer property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Int32> IDLETimer 
		{
			get	{ return _iDLETimer;}
			
			
			set
			{
				SetIDLETimerValue(value);
			}
		}		
			
			
		public virtual void SetImpactLockoutValue(System.Boolean valueToSet)
		{
			SetImpactLockoutValue(valueToSet, true, true);
		}

		public virtual void SetImpactLockoutValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_impactLockout != valueToSet)
			{
				_impactLockout = valueToSet;

				OnPropertyChanged("ImpactLockout", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Impact Lockout property of the Vehicle DataObject</summary>
        public virtual System.Boolean ImpactLockout 
		{
			get	{ return _impactLockout;}
			
			
			set
			{
				SetImpactLockoutValue(value);
			}
		}		
			
			
		public virtual void SetInspectionIdValue(Nullable<System.Guid> valueToSet)
		{
			SetInspectionIdValue(valueToSet, true, true);
		}

		public virtual void SetInspectionIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_inspectionId != valueToSet)
			{
				_inspectionId = valueToSet;

				// InspectionId is a FK. Setting its value should result in a event
				OnPropertyChanged("Inspection", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("InspectionId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The InspectionId property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Guid> InspectionId 
		{
			get	{ return _inspectionId;}
			
			
			set
			{
				SetInspectionIdValue(value);
			}
		}		
			
			
		public virtual void SetIsCanbusValue(System.Boolean valueToSet)
		{
			SetIsCanbusValue(valueToSet, true, true);
		}

		public virtual void SetIsCanbusValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_isCanbus != valueToSet)
			{
				_isCanbus = valueToSet;

				OnPropertyChanged("IsCanbus", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Is Canbus property of the Vehicle DataObject</summary>
        public virtual System.Boolean IsCanbus 
		{
			get	{ return _isCanbus;}
			
			
			set
			{
				SetIsCanbusValue(value);
			}
		}		
			
			
		public virtual void SetLastSessionDateValue(Nullable<System.DateTime> valueToSet)
		{
			SetLastSessionDateValue(valueToSet, true, true);
		}

		public virtual void SetLastSessionDateValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_lastSessionDate != null)
				{
					_lastSessionDate = null;
					OnPropertyChanged("LastSessionDate", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_lastSessionDate != DateTime.MinValue.ToUniversalTime())
				{
					_lastSessionDate = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("LastSessionDate", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_lastSessionDate != DateTime.MaxValue.ToUniversalTime())
				{
					_lastSessionDate = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("LastSessionDate", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_lastSessionDate != valueToSet ||
                (_lastSessionDate != null && ((DateTime)_lastSessionDate).Kind == DateTimeKind.Unspecified))
			{
				_lastSessionDate = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("LastSessionDate", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Last Session Date property of the Vehicle DataObject</summary>
        public virtual Nullable<System.DateTime> LastSessionDate 
		{
			get	{ return _lastSessionDate;}
			
			
			set
			{
				SetLastSessionDateValue(value);
			}
		}		
			
			
		public virtual void SetLastSessionDateTzAdjustedValue(Nullable<System.DateTime> valueToSet)
		{
			SetLastSessionDateTzAdjustedValue(valueToSet, true, true);
		}

		public virtual void SetLastSessionDateTzAdjustedValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_lastSessionDateTzAdjusted != null)
				{
					_lastSessionDateTzAdjusted = null;
					OnPropertyChanged("LastSessionDateTzAdjusted", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_lastSessionDateTzAdjusted != DateTime.MinValue.ToUniversalTime())
				{
					_lastSessionDateTzAdjusted = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("LastSessionDateTzAdjusted", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_lastSessionDateTzAdjusted != DateTime.MaxValue.ToUniversalTime())
				{
					_lastSessionDateTzAdjusted = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("LastSessionDateTzAdjusted", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_lastSessionDateTzAdjusted != valueToSet ||
                (_lastSessionDateTzAdjusted != null && ((DateTime)_lastSessionDateTzAdjusted).Kind == DateTimeKind.Unspecified))
			{
				_lastSessionDateTzAdjusted = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("LastSessionDateTzAdjusted", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Last Session Date Tz Adjusted property of the Vehicle DataObject</summary>
        public virtual Nullable<System.DateTime> LastSessionDateTzAdjusted 
		{
			get	{ return _lastSessionDateTzAdjusted;}
			
			
			set
			{
				SetLastSessionDateTzAdjustedValue(value);
			}
		}		
			
			
		public virtual void SetLastSessionDateTzAdjustedDisplayValue(System.String valueToSet)
		{
			SetLastSessionDateTzAdjustedDisplayValue(valueToSet, true, true);
		}

		public virtual void SetLastSessionDateTzAdjustedDisplayValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_lastSessionDateTzAdjustedDisplay != valueToSet)
			{
				_lastSessionDateTzAdjustedDisplay = valueToSet;

				OnPropertyChanged("LastSessionDateTzAdjustedDisplay", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The LastSessionDateTzAdjustedDisplay property of the Vehicle DataObject</summary>
        public virtual System.String LastSessionDateTzAdjustedDisplay 
		{
			get	{ return String.IsNullOrEmpty(_lastSessionDateTzAdjustedDisplay) ? null : _lastSessionDateTzAdjustedDisplay; }
			
			
			set
			{
				SetLastSessionDateTzAdjustedDisplayValue(value);
			}
		}		
			
			
		public virtual void SetLastSessionIdValue(System.String valueToSet)
		{
			SetLastSessionIdValue(valueToSet, true, true);
		}

		public virtual void SetLastSessionIdValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_lastSessionId != valueToSet)
			{
				_lastSessionId = valueToSet;

				OnPropertyChanged("LastSessionId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Last Session Id property of the Vehicle DataObject</summary>
        public virtual System.String LastSessionId 
		{
			get	{ return String.IsNullOrEmpty(_lastSessionId) ? null : _lastSessionId; }
			
			
			set
			{
				SetLastSessionIdValue(value);
			}
		}		
			
			
		public virtual void SetModelIdValue(System.Guid valueToSet)
		{
			SetModelIdValue(valueToSet, true, true);
		}

		public virtual void SetModelIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_modelId != valueToSet)
			{
				_modelId = valueToSet;

				OnPropertyChanged("ModelId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The ModelId property of the Vehicle DataObject</summary>
        public virtual System.Guid ModelId 
		{
			get	{ return _modelId;}
			
			
			set
			{
				SetModelIdValue(value);
			}
		}		
			
			
		public virtual void SetModuleId1Value(System.Guid valueToSet)
		{
			SetModuleId1Value(valueToSet, true, true);
		}

		public virtual void SetModuleId1Value(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_moduleId1 != valueToSet)
			{
				_moduleId1 = valueToSet;

				// ModuleId1 is a FK. Setting its value should result in a event
				OnPropertyChanged("Module", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("ModuleId1", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The ModuleId1 property of the Vehicle DataObject</summary>
        public virtual System.Guid ModuleId1 
		{
			get	{ return _moduleId1;}
			
			
			set
			{
				SetModuleId1Value(value);
			}
		}		
			
			
		public virtual void SetModuleIsConnectedValue(System.Boolean valueToSet)
		{
			SetModuleIsConnectedValue(valueToSet, true, true);
		}

		public virtual void SetModuleIsConnectedValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_moduleIsConnected != valueToSet)
			{
				_moduleIsConnected = valueToSet;

				OnPropertyChanged("ModuleIsConnected", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Module Is Connected property of the Vehicle DataObject</summary>
        public virtual System.Boolean ModuleIsConnected 
		{
			get	{ return _moduleIsConnected;}
			
			
			set
			{
				SetModuleIsConnectedValue(value);
			}
		}		
			
			
		public virtual void SetModuleSwapNoteValue(System.String valueToSet)
		{
			SetModuleSwapNoteValue(valueToSet, true, true);
		}

		public virtual void SetModuleSwapNoteValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_moduleSwapNote != valueToSet)
			{
				_moduleSwapNote = valueToSet;

				OnPropertyChanged("ModuleSwapNote", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Module Swap Note property of the Vehicle DataObject</summary>
        public virtual System.String ModuleSwapNote 
		{
			get	{ return String.IsNullOrEmpty(_moduleSwapNote) ? null : _moduleSwapNote; }
			
			
			set
			{
				SetModuleSwapNoteValue(value);
			}
		}		
			
		
		/// <summary> The Next Hire Dehire Action Calculated property of the Vehicle DataObject</summary>
        public virtual System.String NextHireDehireActionCalculated 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ((OnHire == true) ? "Dehire" : "Hire");				
			}
			
		}		
			
			
		public virtual void SetOnHireValue(System.Boolean valueToSet)
		{
			SetOnHireValue(valueToSet, true, true);
		}

		public virtual void SetOnHireValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_onHire != valueToSet)
			{
				_onHire = valueToSet;

				OnPropertyChanged("OnHire", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The On Hire property of the Vehicle DataObject</summary>
        public virtual System.Boolean OnHire 
		{
			get	{ return _onHire;}
			
			
			set
			{
				SetOnHireValue(value);
			}
		}		
			
		
		/// <summary> The On Hire Calculated property of the Vehicle DataObject</summary>
        public virtual System.String OnHireCalculated 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ((OnHire == true) ? "On Hire" : "Dehired");				
			}
			
		}		
			
			
		public virtual void SetPersonIdValue(Nullable<System.Guid> valueToSet)
		{
			SetPersonIdValue(valueToSet, true, true);
		}

		public virtual void SetPersonIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_personId != valueToSet)
			{
				_personId = valueToSet;

				OnPropertyChanged("PersonId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The PersonId property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Guid> PersonId 
		{
			get	{ return _personId;}
			
			
			set
			{
				SetPersonIdValue(value);
			}
		}		
			
			
		public virtual void SetSerialNoValue(System.String valueToSet)
		{
			SetSerialNoValue(valueToSet, true, true);
		}

		public virtual void SetSerialNoValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_serialNo != valueToSet)
			{
				_serialNo = valueToSet;

				OnPropertyChanged("SerialNo", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Serial No property of the Vehicle DataObject</summary>
        public virtual System.String SerialNo 
		{
			get	{ return _serialNo; }
			
			
			set
			{
				SetSerialNoValue(value);
			}
		}		
			
			
		public virtual void SetServiceSettingsIdValue(Nullable<System.Guid> valueToSet)
		{
			SetServiceSettingsIdValue(valueToSet, true, true);
		}

		public virtual void SetServiceSettingsIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_serviceSettingsId != valueToSet)
			{
				_serviceSettingsId = valueToSet;

				// ServiceSettingsId is a FK. Setting its value should result in a event
				OnPropertyChanged("ServiceSettings", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("ServiceSettingsId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The ServiceSettingsId property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Guid> ServiceSettingsId 
		{
			get	{ return _serviceSettingsId;}
			
			
			set
			{
				SetServiceSettingsIdValue(value);
			}
		}		
			
			
		public virtual void SetSiteIdValue(System.Guid valueToSet)
		{
			SetSiteIdValue(valueToSet, true, true);
		}

		public virtual void SetSiteIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_siteId != valueToSet)
			{
				_siteId = valueToSet;

				OnPropertyChanged("SiteId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SiteId property of the Vehicle DataObject</summary>
        public virtual System.Guid SiteId 
		{
			get	{ return _siteId;}
			
			
			set
			{
				SetSiteIdValue(value);
			}
		}		
			
			
		public virtual void SetTimeoutEnabledValue(System.Boolean valueToSet)
		{
			SetTimeoutEnabledValue(valueToSet, true, true);
		}

		public virtual void SetTimeoutEnabledValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_timeoutEnabled != valueToSet)
			{
				_timeoutEnabled = valueToSet;

				OnPropertyChanged("TimeoutEnabled", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Timeout Enabled property of the Vehicle DataObject</summary>
        public virtual System.Boolean TimeoutEnabled 
		{
			get	{ return _timeoutEnabled;}
			
			
			set
			{
				SetTimeoutEnabledValue(value);
			}
		}		
			
		
		/// <summary> The Vehicle Diagnostic Action property of the Vehicle DataObject</summary>
        public virtual System.String VehicleDiagnosticAction 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return "<div class=\"icon-container\">    <a href=\"#\" data-bind=\"visible: ApplicationController.viewModel.pageController().VehilceItemsViewModel.customViewModel.IsVehicleDiagnosticLinkVisible()\"        onclick=\"event.preventDefault(); $(this).closest('tr').click(); ApplicationController.viewModel.pageController().VehilceItemsViewModel.commands.showDiagnosticPopupCommand();\">       <svg class=\"bi icon-wrench\" width=\"16\" height=\"16\">          <use xlink:href=\"Styles/Images/bootstrap-icons.svg#wrench\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" />       </svg>    </a>    <span class=\"tooltip-text\">Show Diagnostic</span>    </div>   ";				
			}
			
		}		
			
			
		public virtual void SetVehicleImageValue(System.String valueToSet)
		{
			SetVehicleImageValue(valueToSet, true, true);
		}

		public virtual void SetVehicleImageValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleImage != valueToSet)
			{
				_vehicleImage = valueToSet;

				OnPropertyChanged("VehicleImage", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Vehicle Image property of the Vehicle DataObject</summary>
        public virtual System.String VehicleImage 
		{
			get	{ return _vehicleImage;}
			
			
			set
			{
				SetVehicleImageValue(value);
			}
		}		
			
			
		public virtual void SetVehicleImageFileSizeValue(Nullable<System.Int32> valueToSet)
		{
			SetVehicleImageFileSizeValue(valueToSet, true, true);
		}

		public virtual void SetVehicleImageFileSizeValue(Nullable<System.Int32> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleImageFileSize != valueToSet)
			{
				_vehicleImageFileSize = valueToSet;

				OnPropertyChanged("VehicleImageFileSize", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Vehicle Image File size (bytes) property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Int32> VehicleImageFileSize 
		{
			get	{ return _vehicleImageFileSize;}
			
			
			set
			{
				SetVehicleImageFileSizeValue(value);
			}
		}		
			
			
		public virtual void SetVehicleImageInternalNameValue(System.String valueToSet)
		{
			SetVehicleImageInternalNameValue(valueToSet, true, true);
		}

		public virtual void SetVehicleImageInternalNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleImageInternalName != valueToSet)
			{
				_vehicleImageInternalName = valueToSet;

				OnPropertyChanged("VehicleImageInternalName", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Vehicle Image Internal Name property of the Vehicle DataObject</summary>
        public virtual System.String VehicleImageInternalName 
		{
			get	{ return String.IsNullOrEmpty(_vehicleImageInternalName) ? null : _vehicleImageInternalName; }
			
			
			set
			{
				SetVehicleImageInternalNameValue(value);
			}
		}		
			
		
		/// <summary> The Vehicle Image Url property of the Vehicle DataObject</summary>
        public virtual System.String VehicleImageUrl 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ((VehicleImage != null) ? ("api/vehicle/file/" + Id.ToString() + "/VehicleImage?t=" + "".ToString()) : "");				
			}
			
		}		
			
		
		/// <summary> The VehicleModelNo property of the Vehicle DataObject</summary>
        public virtual System.String VehicleModel 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ("Vehicle: " + HireNo);				
			}
			
		}		
			
			
		public virtual void SetVehicleOtherSettingsIdValue(Nullable<System.Guid> valueToSet)
		{
			SetVehicleOtherSettingsIdValue(valueToSet, true, true);
		}

		public virtual void SetVehicleOtherSettingsIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleOtherSettingsId != valueToSet)
			{
				_vehicleOtherSettingsId = valueToSet;

				// VehicleOtherSettingsId is a FK. Setting its value should result in a event
				OnPropertyChanged("VehicleOtherSettings", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("VehicleOtherSettingsId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The VehicleOtherSettingsId property of the Vehicle DataObject</summary>
        public virtual Nullable<System.Guid> VehicleOtherSettingsId 
		{
			get	{ return _vehicleOtherSettingsId;}
			
			
			set
			{
				SetVehicleOtherSettingsIdValue(value);
			}
		}		
			
		
		/// <summary> The Vehicle Tags property of the Vehicle DataObject</summary>
        public virtual System.String VehicleTags 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return (((ImpactLockout == true) && (OnHire == true)) ? "<div class=\"vehicle-access-tag-container-custom\"><p class=\"vehicle-lockout-tag-custom\">Lockout</p><p class=\"vehicle-onhire-tag-custom\">On Hire</p></div>" : (((ImpactLockout == true) && (OnHire == false)) ? "<div class=\"vehicle-access-tag-container-custom\"><p class=\"vehicle-lockout-tag-custom\">Lockout</p></div>" : (((ImpactLockout == false) && (OnHire == true)) ? "<div class=\"vehicle-access-tag-container-custom\"><p class=\"vehicle-onhire-tag-custom\">On Hire</p></div>" : "<div class=\"vehicle-access-tag-container-custom\"><p></p></div>")));				
			}
			
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			if (propertyName == "Department.Name")
			{
				OnPropertyChanged("DepartmentAndModel", true, dirtyHandlerOn);
			}

			if (propertyName == "Model.Name")
			{
				OnPropertyChanged("DepartmentAndModel", true, dirtyHandlerOn);
			}

			if (propertyName == "Department")
			{
				OnPropertyChanged("DepartmentAndModel", true, dirtyHandlerOn);
			}

			if (propertyName == "Model")
			{
				OnPropertyChanged("DepartmentAndModel", true, dirtyHandlerOn);
			}

			if (propertyName == "OnHire")
			{
				OnPropertyChanged("NextHireDehireActionCalculated", true, dirtyHandlerOn);
				OnPropertyChanged("OnHireCalculated", true, dirtyHandlerOn);
				OnPropertyChanged("VehicleTags", true, dirtyHandlerOn);
			}

			if (propertyName == "HireNo")
			{
				OnPropertyChanged("VehicleModel", true, dirtyHandlerOn);
			}

			if (propertyName == "ImpactLockout")
			{
				OnPropertyChanged("VehicleTags", true, dirtyHandlerOn);
			}

			
			// Push the notification to related objects
			var _allVehicleCalibrationStoreProcedureItems = GetAllVehicleCalibrationStoreProcedureItemsAsync(false).Result;
			if (_allVehicleCalibrationStoreProcedureItems != null)
            {
                foreach (var item in _allVehicleCalibrationStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _allVORSessionsPerVehicleStoreProcedureItems = GetAllVORSessionsPerVehicleStoreProcedureItemsAsync(false).Result;
			if (_allVORSessionsPerVehicleStoreProcedureItems != null)
            {
                foreach (var item in _allVORSessionsPerVehicleStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _broadcastMessageHistoryItems = GetBroadcastMessageHistoryItemsAsync(false).Result;
			if (_broadcastMessageHistoryItems != null)
            {
                foreach (var item in _broadcastMessageHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _checklistFailurePerVechicleViewItems = GetChecklistFailurePerVechicleViewItemsAsync(false).Result;
			if (_checklistFailurePerVechicleViewItems != null)
            {
                foreach (var item in _checklistFailurePerVechicleViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var checklistSettings = GetChecklistSettingsAsync(false).Result;
			if (checklistSettings != null && this.IsDirty)
            {
				checklistSettings.NotifyPropertyChanged("Vehicle." + propertyName, callers);
			}
			var _currentStatusVehicleViewItems = GetCurrentStatusVehicleViewItemsAsync(false).Result;
			if (_currentStatusVehicleViewItems != null)
            {
                foreach (var item in _currentStatusVehicleViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _detailedSessionViewItems = GetDetailedSessionViewItemsAsync(false).Result;
			if (_detailedSessionViewItems != null)
            {
                foreach (var item in _detailedSessionViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _detailedVORSessionStoreProcedureItems = GetDetailedVORSessionStoreProcedureItemsAsync(false).Result;
			if (_detailedVORSessionStoreProcedureItems != null)
            {
                foreach (var item in _detailedVORSessionStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var driver = GetDriverAsync(false).Result;
			if (driver != null && this.IsDirty)
            {
				driver.NotifyPropertyChanged("." + propertyName, callers);
			}
			var _generalProductivityPerVehicleViewItems = GetGeneralProductivityPerVehicleViewItemsAsync(false).Result;
			if (_generalProductivityPerVehicleViewItems != null)
            {
                foreach (var item in _generalProductivityPerVehicleViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _impactsForVehicleViewItems = GetImpactsForVehicleViewItemsAsync(false).Result;
			if (_impactsForVehicleViewItems != null)
            {
                foreach (var item in _impactsForVehicleViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var inspection = GetInspectionAsync(false).Result;
			if (inspection != null && this.IsDirty)
            {
				inspection.NotifyPropertyChanged("Vehicle." + propertyName, callers);
			}
			var _messageHistoryItems = GetMessageHistoryItemsAsync(false).Result;
			if (_messageHistoryItems != null)
            {
                foreach (var item in _messageHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var module = GetModuleAsync(false).Result;
			if (module != null && this.IsDirty)
            {
				module.NotifyPropertyChanged("Vehicle." + propertyName, callers);
			}
			var _moduleHistoryItems = GetModuleHistoryItemsAsync(false).Result;
			if (_moduleHistoryItems != null)
            {
                foreach (var item in _moduleHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _networkSettingsItems = GetNetworkSettingsItemsAsync(false).Result;
			if (_networkSettingsItems != null)
            {
                foreach (var item in _networkSettingsItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _onDemandSessionItems = GetOnDemandSessionItemsAsync(false).Result;
			if (_onDemandSessionItems != null)
            {
                foreach (var item in _onDemandSessionItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var onDemandSettings = GetOnDemandSettingsAsync(false).Result;
			if (onDemandSettings != null && this.IsDirty)
            {
				onDemandSettings.NotifyPropertyChanged("Vehicle." + propertyName, callers);
			}
			var _pedestrianDetectionHistoryItems = GetPedestrianDetectionHistoryItemsAsync(false).Result;
			if (_pedestrianDetectionHistoryItems != null)
            {
                foreach (var item in _pedestrianDetectionHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _personToPerVehicleMasterAccessViewItems = GetPersonToPerVehicleMasterAccessViewItemsAsync(false).Result;
			if (_personToPerVehicleMasterAccessViewItems != null)
            {
                foreach (var item in _personToPerVehicleMasterAccessViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _personToPerVehicleNormalAccessViewItems = GetPersonToPerVehicleNormalAccessViewItemsAsync(false).Result;
			if (_personToPerVehicleNormalAccessViewItems != null)
            {
                foreach (var item in _personToPerVehicleNormalAccessViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _perVehicleNormalCardAccessItems = GetPerVehicleNormalCardAccessItemsAsync(false).Result;
			if (_perVehicleNormalCardAccessItems != null)
            {
                foreach (var item in _perVehicleNormalCardAccessItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var serviceSettings = GetServiceSettingsAsync(false).Result;
			if (serviceSettings != null && this.IsDirty)
            {
				serviceSettings.NotifyPropertyChanged("Vehicle." + propertyName, callers);
			}
			var _sessions = GetSessionsAsync(false).Result;
			if (_sessions != null)
            {
                foreach (var item in _sessions)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _slamcoreDeviceHistoryItems = GetSlamcoreDeviceHistoryItemsAsync(false).Result;
			if (_slamcoreDeviceHistoryItems != null)
            {
                foreach (var item in _slamcoreDeviceHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _unitUnutilisationStoreProcedureItems = GetUnitUnutilisationStoreProcedureItemsAsync(false).Result;
			if (_unitUnutilisationStoreProcedureItems != null)
            {
                foreach (var item in _unitUnutilisationStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _unitUtilisationStoreProcedureItems = GetUnitUtilisationStoreProcedureItemsAsync(false).Result;
			if (_unitUtilisationStoreProcedureItems != null)
            {
                foreach (var item in _unitUtilisationStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _vehicleAlertSubscriptionItems = GetVehicleAlertSubscriptionItemsAsync(false).Result;
			if (_vehicleAlertSubscriptionItems != null)
            {
                foreach (var item in _vehicleAlertSubscriptionItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _vehicleBroadcastMessageItems = GetVehicleBroadcastMessageItemsAsync(false).Result;
			if (_vehicleBroadcastMessageItems != null)
            {
                foreach (var item in _vehicleBroadcastMessageItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _vehicleCardAccesses = GetVehicleCardAccessesAsync(false).Result;
			if (_vehicleCardAccesses != null)
            {
                foreach (var item in _vehicleCardAccesses)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var vehicleDiagnostic = GetVehicleDiagnosticAsync(false).Result;
			if (vehicleDiagnostic != null && this.IsDirty)
            {
				vehicleDiagnostic.NotifyPropertyChanged("Vehicle." + propertyName, callers);
			}
			var _vehicleGPSLocations = GetVehicleGPSLocationsAsync(false).Result;
			if (_vehicleGPSLocations != null)
            {
                foreach (var item in _vehicleGPSLocations)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _vehicleHireDehireHistoryItems = GetVehicleHireDehireHistoryItemsAsync(false).Result;
			if (_vehicleHireDehireHistoryItems != null)
            {
                foreach (var item in _vehicleHireDehireHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var vehicleLastGPSLocationView = GetVehicleLastGPSLocationViewAsync(false).Result;
			if (vehicleLastGPSLocationView != null && this.IsDirty)
            {
				vehicleLastGPSLocationView.NotifyPropertyChanged("Vehicle." + propertyName, callers);
			}
			var _vehicleLockoutItems = GetVehicleLockoutItemsAsync(false).Result;
			if (_vehicleLockoutItems != null)
            {
                foreach (var item in _vehicleLockoutItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var vehicleOtherSettings = GetVehicleOtherSettingsAsync(false).Result;
			if (vehicleOtherSettings != null && this.IsDirty)
            {
				vehicleOtherSettings.NotifyPropertyChanged("Vehicle." + propertyName, callers);
			}
			var _vehicleProficiencyViewItems = GetVehicleProficiencyViewItemsAsync(false).Result;
			if (_vehicleProficiencyViewItems != null)
            {
                foreach (var item in _vehicleProficiencyViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _vehicleSessionlessImpactItems = GetVehicleSessionlessImpactItemsAsync(false).Result;
			if (_vehicleSessionlessImpactItems != null)
            {
                foreach (var item in _vehicleSessionlessImpactItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _vehicleSupervisorsViewItems = GetVehicleSupervisorsViewItemsAsync(false).Result;
			if (_vehicleSupervisorsViewItems != null)
            {
                foreach (var item in _vehicleSupervisorsViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _vehicleToPreOpCheckilstItems = GetVehicleToPreOpCheckilstItemsAsync(false).Result;
			if (_vehicleToPreOpCheckilstItems != null)
            {
                foreach (var item in _vehicleToPreOpCheckilstItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
			var _vORSettingHistoryItems = GetVORSettingHistoryItemsAsync(false).Result;
			if (_vORSettingHistoryItems != null)
            {
                foreach (var item in _vORSettingHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Vehicle.", propertyName), callers);                    
                }
            }
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<VehicleDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is VehicleDataObject))
				return false;

			var p = (VehicleDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.CanruleId == p.CanruleId;
			fieldsComparison &= this.ChecklistSettingsId == p.ChecklistSettingsId;
			fieldsComparison &= this.CustomerId == p.CustomerId;
			fieldsComparison &= this.DehireTime == p.DehireTime;
			fieldsComparison &= this.DeletedAtUtc == p.DeletedAtUtc;
			fieldsComparison &= this.DepartmentChecklistId == p.DepartmentChecklistId;
			fieldsComparison &= this.DepartmentId == p.DepartmentId;
			fieldsComparison &= this.Description == p.Description;
			fieldsComparison &= this.DriverId == p.DriverId;
			fieldsComparison &= this.FirmwareId == p.FirmwareId;
			fieldsComparison &= this.HireNo == p.HireNo;
			fieldsComparison &= this.HireTime == p.HireTime;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.IDLETimer == p.IDLETimer;
			fieldsComparison &= this.ImpactLockout == p.ImpactLockout;
			fieldsComparison &= this.InspectionId == p.InspectionId;
			fieldsComparison &= this.IsCanbus == p.IsCanbus;
			fieldsComparison &= this.LastSessionDate == p.LastSessionDate;
			fieldsComparison &= this.LastSessionDateTzAdjusted == p.LastSessionDateTzAdjusted;
			fieldsComparison &= this.LastSessionId == p.LastSessionId;
			fieldsComparison &= this.ModelId == p.ModelId;
			fieldsComparison &= this.ModuleId1 == p.ModuleId1;
			fieldsComparison &= this.ModuleIsConnected == p.ModuleIsConnected;
			fieldsComparison &= this.ModuleSwapNote == p.ModuleSwapNote;
			fieldsComparison &= this.OnHire == p.OnHire;
			fieldsComparison &= this.PersonId == p.PersonId;
			fieldsComparison &= this.SerialNo == p.SerialNo;
			fieldsComparison &= this.ServiceSettingsId == p.ServiceSettingsId;
			fieldsComparison &= this.SiteId == p.SiteId;
			fieldsComparison &= this.TimeoutEnabled == p.TimeoutEnabled;
			fieldsComparison &= this.VehicleImage == p.VehicleImage;
			fieldsComparison &= this.VehicleImageFileSize == p.VehicleImageFileSize;
			fieldsComparison &= this.VehicleImageInternalName == p.VehicleImageInternalName;
			fieldsComparison &= this.VehicleOtherSettingsId == p.VehicleOtherSettingsId;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// DeletedAtUtc is a local datetime: Convert to UTC for server-side handling and storing
			if (this._deletedAtUtc_WithTimezoneOffset != null)
			{
				this.DeletedAtUtc = ((DateTimeOffset)this._deletedAtUtc_WithTimezoneOffset).UtcDateTime;
			}
			// LastSessionDateTzAdjusted is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._lastSessionDateTzAdjusted_WithTimezoneOffset != null)
			{
				this.LastSessionDateTzAdjusted = ((DateTimeOffset)this._lastSessionDateTzAdjusted_WithTimezoneOffset).DateTime;
			}
			// DehireTime is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._dehireTime_WithTimezoneOffset != null)
			{
				this.DehireTime = ((DateTimeOffset)this._dehireTime_WithTimezoneOffset).DateTime;
			}
			// HireTime is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._hireTime_WithTimezoneOffset != null)
			{
				this.HireTime = ((DateTimeOffset)this._hireTime_WithTimezoneOffset).DateTime;
			}
			// LastSessionDate is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._lastSessionDate_WithTimezoneOffset != null)
			{
				this.LastSessionDate = ((DateTimeOffset)this._lastSessionDate_WithTimezoneOffset).DateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class VehicleCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public VehicleCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public VehicleCollectionContainer()
		{
		}
		
		public VehicleCollectionContainer Construct(DataObjectCollection<VehicleDataObject> vehicleItems)
        {
            if (vehicleItems == null)
                return this;
				
			this.PrimaryKeys = vehicleItems.Select(c => c.PrimaryKey).ToList();
            if (vehicleItems.ObjectsDataSet == null)
            {
                vehicleItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = vehicleItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = vehicleItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<VehicleDataObject> ExtractVehicleItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<VehicleDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<VehicleDataObject>(typeof(VehicleDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class VehicleContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public VehicleContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual VehicleContainer Construct(VehicleDataObject vehicle, bool includeDirtyObjectsOnly = false)
		{
            if (vehicle == null)
                return this;

			this.PrimaryKey = vehicle.PrimaryKey;
			
            if (vehicle.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(vehicle);
            }

			if(vehicle.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity Vehicle", "Unable to set a dataset to the entity. Container may not be initialized", "VehicleDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : Vehicle");
			}

			if(vehicle.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in VehicleDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "VehicleDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in VehicleDataObject");
			}
			this.InternalObjectId = (int) vehicle.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? vehicle.ObjectsDataSet.CloneDirtyObjects() : vehicle.ObjectsDataSet;

			return this;
		}
		
		public VehicleDataObject ExtractVehicle()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<VehicleDataObject>(typeof(VehicleDataObject), InternalObjectId);
			
			return result;
        }	
	}

}