﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
namespace FleetXQ.Data
{
	/// <summary>
	/// DataObjectType enum
	/// </summary>
	public enum DataObjectType
	{
		AccessGroupDataObject,
		AccessGroupTemplateDataObject,
		AccessGroupToSiteDataObject,
		AlertDataObject,
		AlertHistoryDataObject,
		AlertSubscriptionDataObject,
		AlertSummaryStoreProcedureDataObject,
		AllChecklistResultViewDataObject,
		AllDriverAccessAbuseStoreProcedureDataObject,
		AllEmailSubscriptionStoreProcedureDataObject,
		AllImpactsViewDataObject,
		AllLicenseExpiryViewDataObject,
		AllMessageHistoryStoreProcedureDataObject,
		AllUserSummaryStoreProcedureDataObject,
		AllVehicleCalibrationFilterDataObject,
		AllVehicleCalibrationStoreProcedureDataObject,
		AllVehicleUnlocksViewDataObject,
		AllVORSessionsPerVehicleStoreProcedureDataObject,
		AllVORStatusStoreProcedureDataObject,
		BroadcastMessageDataObject,
		BroadcastMessageHistoryDataObject,
		BroadcastMessageHistoryFilterDataObject,
		CanruleDataObject,
		CanruleDetailsDataObject,
		CardDataObject,
		CardToCardAccessDataObject,
		CategoryTemplateDataObject,
		ChecklistDetailDataObject,
		ChecklistFailurePerVechicleViewDataObject,
		ChecklistFailureViewDataObject,
		ChecklistResultDataObject,
		ChecklistSettingsDataObject,
		ChecklistStatusViewDataObject,
		ContactPersonInformationDataObject,
		CountryDataObject,
		CurrentDriverStatusChartViewDataObject,
		CurrentStatusCombinedViewDataObject,
		CurrentStatusDriverViewDataObject,
		CurrentStatusVehicleViewDataObject,
		CurrentVehicleStatusChartViewDataObject,
		CustomerDataObject,
		CustomerFeatureSubscriptionDataObject,
		CustomerModelDataObject,
		CustomerPreOperationalChecklistTemplateDataObject,
		CustomerSSODetailDataObject,
		CustomerToModelDataObject,
		CustomerToPersonViewDataObject,
		DashboardDriverCardStoreProcedureDataObject,
		DashboardDriverCardViewDataObject,
		DashboardFilterDataObject,
		DashboardFilterMoreFieldsDataObject,
		DashboardVehicleCardStoreProcedureDataObject,
		DashboardVehicleCardViewDataObject,
		DealerDataObject,
		DealerConfigurationDataObject,
		DealerDriverDataObject,
		DealerFeatureSubscriptionDataObject,
		DepartmentDataObject,
		DepartmentChecklistDataObject,
		DepartmentHourSettingsDataObject,
		DepartmentVehicleMasterCardAccessDataObject,
		DepartmentVehicleNormalCardAccessDataObject,
		DetailedSessionViewDataObject,
		DetailedVORSessionStoreProcedureDataObject,
		DriverDataObject,
		DriverAccessAbuseFilterDataObject,
		DriverLicenseExpiryStoreProcedureDataObject,
		DriverLicenseExpiryViewDataObject,
		DriverProficiencyViewDataObject,
		EmailDataObject,
		EmailGroupsDataObject,
		EmailGroupsToPersonDataObject,
		EmailSubscriptionReportFilterDataObject,
		FeatureSubscriptionsFilterDataObject,
		FeatureSubscriptionTemplateDataObject,
		FirmwareDataObject,
		FloorPlanDataObject,
		FloorZonesDataObject,
		GeneralProductivityPerDriverViewLatestDataObject,
		GeneralProductivityPerVehicleViewDataObject,
		GeneralProductivityReportFilterDataObject,
		GeneralProductivityViewDataObject,
		CustomerAuditDataObject,
		CustomerSnapshotDataObject,
		GOChangeDeltaDataObject,
		RevisionDataObject,
		SnapshotDataObject,
		TagDataObject,
		GO2FAConfigurationDataObject,
		GOGroupDataObject,
		GOGroupRoleDataObject,
		GOLoginHistoryDataObject,
		GORoleDataObject,
		GOUserDataObject,
		GOUser2FADataObject,
		GOUserGroupDataObject,
		GOUserRoleDataObject,
		GOSecurityTokensDataObject,
		GOTaskDataObject,
		GOUserDepartmentDataObject,
		GoUserToCustomerDataObject,
		GPSHistoryDataObject,
		HelpDataObject,
		HireDeHireReportFilterDataObject,
		ImpactDataObject,
		ImpactFrequencyPerTimeSlotViewDataObject,
		ImpactFrequencyPerWeekDayViewDataObject,
		ImpactFrequencyPerWeekMonthViewDataObject,
		ImpactReportFilterDataObject,
		ImpactsForVehicleViewDataObject,
		ExportJobStatusDataObject,
		ImportJobBatchDataObject,
		ImportJobLogDataObject,
		ImportJobStatusDataObject,
		IncompletedChecklistViewDataObject,
		InspectionDataObject,
		IOFIELDDataObject,
		IoTDeviceMessageCacheDataObject,
		LicenceDetailDataObject,
		LicenseByModelDataObject,
		LicenseExpiryReportFilterDataObject,
		LoggedHoursVersusSeatHoursViewDataObject,
		MachineUnlockReportFilterDataObject,
		MainDashboardFilterDataObject,
		MessageHistoryDataObject,
		ModelDataObject,
		ModelVehicleMasterCardAccessDataObject,
		ModelVehicleNormalCardAccessDataObject,
		ModuleDataObject,
		ModuleHistoryDataObject,
		NetworkSettingsDataObject,
		OnDemandAuthorisationFilterDataObject,
		OnDemandAuthorisationStoreProcedureDataObject,
		OnDemandSessionDataObject,
		OnDemandSettingsDataObject,
		PedestrianDetectionHistoryDataObject,
		PedestrianDetectionHistoryFilterDataObject,
		PermissionDataObject,
		PersonDataObject,
		PersonAllocationDataObject,
		PersonChecklistLanguageSettingsDataObject,
		PersonToDepartmentVehicleMasterAccessViewDataObject,
		PersonToDepartmentVehicleNormalAccessViewDataObject,
		PersonToModelVehicleMasterAccessViewDataObject,
		PersonToModelVehicleNormalAccessViewDataObject,
		PersonToPerVehicleMasterAccessViewDataObject,
		PersonToPerVehicleNormalAccessViewDataObject,
		PersonToSiteVehicleMasterAccessViewDataObject,
		PersonToSiteVehicleNormalAccessViewDataObject,
		PerVehicleMasterCardAccessDataObject,
		PerVehicleNormalCardAccessDataObject,
		PreOperationalChecklistDataObject,
		PreOpReportFilterDataObject,
		ProficiencyCombinedViewDataObject,
		ProficiencyReportFilterDataObject,
		PSTATDetailsDataObject,
		RegionDataObject,
		ReportSubscriptionDataObject,
		ReportTypeDataObject,
		ServiceSettingsDataObject,
		SessionDataObject,
		SessionDetailsDataObject,
		SiteDataObject,
		SiteFloorPlanDataObject,
		SiteVehicleMasterCardAccessDataObject,
		SiteVehicleNormalCardAccessDataObject,
		SlamcoreAPIKeyDataObject,
		SlamcoreAwareAuthenticationDetailsDataObject,
		SlamcoreDeviceDataObject,
		SlamcoreDeviceConnectionViewDataObject,
		SlamcoreDeviceFilterDataObject,
		SlamcoreDeviceHistoryDataObject,
		SlamcorePedestrianDetectionDataObject,
		SynchronizationStatusReportFilterDataObject,
		TimezoneDataObject,
		TodaysImpactStoreProcedureDataObject,
		TodaysImpactViewDataObject,
		TodaysPreopCheckStoreProcedureDataObject,
		TodaysPreopCheckViewDataObject,
		UnitSummaryReportDataObject,
		UnitSummaryStoreProcedureDataObject,
		UnitUnutilisationStoreProcedureDataObject,
		UnitUtilisationCombinedViewDataObject,
		UnitUtilisationStoreProcedureDataObject,
		UpdateFirmwareRequestDataObject,
		UploadLogoRequestDataObject,
		VehicleDataObject,
		VehicleAlertSubscriptionDataObject,
		VehicleBroadcastMessageDataObject,
		VehicleDiagnosticDataObject,
		VehicleGPSDataObject,
		VehicleHireDehireHistoryDataObject,
		VehicleHireDehireSynchronizationOptionsDataObject,
		VehicleLastGPSLocationViewDataObject,
		VehicleLockoutDataObject,
		VehicleOtherSettingsDataObject,
		VehicleProficiencyViewDataObject,
		VehicleSessionlessImpactDataObject,
		VehicleSlamcoreLocationHistoryDataObject,
		VehiclesPerModelReportDataObject,
		VehicleSupervisorsViewDataObject,
		VehicleToPreOpChecklistViewDataObject,
		VehicleUtilizationLastTwelveHoursStoreProcedureDataObject,
		VehicleUtilizationLastTwelveHoursViewDataObject,
		VORReportCombinedViewDataObject,
		VORReportFilterDataObject,
		VORSettingHistoryDataObject,
		WebsiteRoleDataObject,
		WebsiteUserDataObject,
		ZoneCoordinatesDataObject,
	}
}