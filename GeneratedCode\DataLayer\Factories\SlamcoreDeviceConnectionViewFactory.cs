﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;

 

namespace FleetXQ.Data.DataObjects.Factories
{
    public class SlamcoreDeviceConnectionViewFactory : IDataObjectFactory<SlamcoreDeviceConnectionViewDataObject> 
    {
		protected IServiceProvider _serviceProvider;
		
		public SlamcoreDeviceConnectionViewFactory(IServiceProvider provider)
		{
      		_serviceProvider = provider;
		}

        public object CreateDataSetContainer(SlamcoreDeviceConnectionViewDataObject entity)
        {
            var result = _serviceProvider.GetRequiredService<SlamcoreDeviceConnectionViewContainer>();
            result.Construct(entity, false);
            return result;
        }

        public object CreateDataSetContainer(DataObjectCollection<SlamcoreDeviceConnectionViewDataObject> entityCollection)
        {
            var result = _serviceProvider.GetRequiredService<SlamcoreDeviceConnectionViewCollectionContainer>();
            result.Construct(entityCollection);
            return result;
        }

        public SlamcoreDeviceConnectionViewDataObject CreateDataObject()
        {
            return _serviceProvider.GetRequiredService<SlamcoreDeviceConnectionViewDataObject>();
        }
    
        public SlamcoreDeviceConnectionViewDataObject CreateDataObject(IEnumerable<string> pks)
        {
            var pksAsArray = pks.ToArray();

            if (pksAsArray.Length != 1)
                throw new ApplicationException("CreateObject - SlamcoreDeviceConnectionView - Wrong number of PKs");

			System.Guid id;

			try 
			{
				id = Guid.Parse(pksAsArray[0]);           
			}
			catch(Exception)
			{
                throw new ApplicationException("Wrong pk type for SlamcoreDeviceConnectionView.Id - should be System.Guid");
			}

                         
            var result = _serviceProvider.GetRequiredService<SlamcoreDeviceConnectionViewDataObject>();
          result.Id = id;
            return result;
        }

        public SlamcoreDeviceConnectionViewDataObject DeserializeFromContainer(string jsonstring, JsonSerializerSettings settings)
        {
            if (string.IsNullOrEmpty(jsonstring) || jsonstring == "null")
            {
                return null;
            }

            var container = _serviceProvider.GetRequiredService<SlamcoreDeviceConnectionViewContainer>(); 
            JsonConvert.PopulateObject(jsonstring, container, settings);

			if(container == null)
			 return null;

			var result = container.ExtractSlamcoreDeviceConnectionView();

			// result can legitimately be null, because e.g. the dataset may contain an instance related to the main/primary entity, but not the main/primary entity itself
			// In this case we create an 'empty' instance of the main/primary entity and attach + reconstruct the dataset
            if (result == null)
            {
                result = _serviceProvider.GetRequiredService<SlamcoreDeviceConnectionViewDataObject>();
			    result.Id = container.PrimaryKey;
              result.IsNew = false;
                result.IsDirty = false;
                result.ObjectsDataSet = container.ObjectsDataSet;

				// Sync the dataset 
				result.ObjectsDataSet.EnsureInitialized();
				result.ObjectsDataSet.ReconstructIndexes();
            }

			result.OnDeserialized();

			return result;
		}

		public SlamcoreDeviceConnectionViewDataObject DeserializeObject(string jsonstring, JsonSerializerSettings settings)
        {	
			var result = JsonConvert.DeserializeObject<SlamcoreDeviceConnectionViewDataObject>(jsonstring, settings);

			if(result == null)
			 return null;

			result.OnDeserialized();
			return result;
		}
	}
}