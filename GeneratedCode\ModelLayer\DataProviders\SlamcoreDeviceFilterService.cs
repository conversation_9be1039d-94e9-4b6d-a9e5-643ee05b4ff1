﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Text;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;	
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Serialization;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.ClientLayer;
using GenerativeObjects.Practices.Settings;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DeleteHandlers;
using Newtonsoft.Json;
using System.IO;
using System.Collections.Generic;
using System.Net;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Parameters = System.Collections.Generic.Dictionary<string, object>;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;


namespace FleetXQ.Client.Model
{
	public partial class SlamcoreDeviceFilterService : BaseService, IDataProvider<SlamcoreDeviceFilterDataObject>
    {	
        private readonly IApiFilterArgumentBuilder _apiFilterArgumentBuilder;
		private readonly IUserIdentity _userIdentity;

		public SlamcoreDeviceFilterService(IServiceProvider provider, IConfiguration configuration, IThreadContext threadContext, IUserIdentity userIdentity, IApiFilterArgumentBuilder apiFilterArgumentBuilder) : base(provider, configuration, threadContext)
		{
  			_apiFilterArgumentBuilder =	apiFilterArgumentBuilder;
			_userIdentity = userIdentity;
            _endpointName = "slamcoredevicefilter";
		}
		
		public virtual async Task<SlamcoreDeviceFilterDataObject> SaveAsync(
			SlamcoreDeviceFilterDataObject theDataObjectToSave, 
			bool includeDirtyObjectsOnly,
			LambdaExpression securityFilterExpression = null, 
			List<string> includes = null, 
			IObjectsDataSet context = null, 
			Parameters parameters = null, 
			bool skipSecurity = false /* skipSecurity has no effect here */)
		{
			var container = _serviceProvider.GetRequiredService<SlamcoreDeviceFilterContainer>();
			container.Construct(theDataObjectToSave, includeDirtyObjectsOnly);

			return await SaveAsync(container, securityFilterExpression, includes, parameters: parameters);
		}

		public virtual async Task<SlamcoreDeviceFilterDataObject> SaveAsync(
			SlamcoreDeviceFilterDataObject theDataObjectToSave, 
			LambdaExpression securityFilterExpression = null, 
			List<string> includes = null, 
			IObjectsDataSet context = null, 
			Parameters parameters = null, 
			bool skipSecurity = false /* skipSecurity has no effect here */)
		{
			bool includeDirtyObjectsOnly = GetIncludeDirtyObjectsOnlyOption(parameters);
			var container = _serviceProvider.GetRequiredService<SlamcoreDeviceFilterContainer>();
			container.Construct(theDataObjectToSave, includeDirtyObjectsOnly);
			return await SaveAsync(container, securityFilterExpression, includes, parameters: parameters);
		}

		public virtual async Task<SlamcoreDeviceFilterDataObject> SaveAsync(
			SlamcoreDeviceFilterContainer theDataObjectToSave,
			LambdaExpression securityFilterExpression = null, 
			List<string> includes = null,
			bool skipSecurity = false,
			Parameters parameters = null) 
		{
            var queryStringParams = new List<string>();

			if (!String.IsNullOrEmpty(_threadContext.DbKey))
			{
				queryStringParams.Add($"dbKey={_threadContext.DbKey}");
			}

            // get user token for currently authenticated user if any    
            var userToken = _threadContext.UserToken;

            if (userToken == null)
            {
                userToken = _userIdentity.UserToken;
            }

            if (!String.IsNullOrEmpty(userToken))
            {
                queryStringParams.Add($"_user_token={userToken}");
            }
			
			var applicationToken = _threadContext.ApplicationToken;

            if (applicationToken == null)
            {
                applicationToken = _userIdentity.ApplicationToken;
            }

            if (!String.IsNullOrEmpty(applicationToken))
            {
                queryStringParams.Add($"_application_token={applicationToken}");
            }

			var queryUrl = _serviceUrl;
            
			if (queryStringParams.Any())
            {
                queryUrl += "?" + String.Join("&", queryStringParams);
            }

            var formData = new Dictionary<string, string>();
			string entityAsJson = JsonConvert.SerializeObject(theDataObjectToSave, _jsonSerializerSettings);

			formData.Add("entity", entityAsJson);			

            if (includes != null && includes.Any())
            {
				formData.Add("include", String.Join(",", includes));
			}

			var formContent = new FormUrlEncodedContent(formData);

			// Create a handler that will store cookies
            var handler = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

			using (var httpClient = new HttpClient(handler))
			{
                // Add the CSRF token to the headers
                httpClient.DefaultRequestHeaders.Add("X-CSRF-TOKEN", await GetCsrfTokenAsync(httpClient));

				// Make the POST request to the API endpoint
				var response = await httpClient.PostAsync(queryUrl, formContent);

				// Check if the request was successful (status code 200)
				if (response.IsSuccessStatusCode)
				{
					// Read the response content as a string
					string responseContent = await response.Content.ReadAsStringAsync();

                    var container = _serviceProvider.GetRequiredService<SlamcoreDeviceFilterContainer>();
                    JsonConvert.PopulateObject(responseContent, container, _jsonSerializerSettings);

					if (container == null)
					{
						if (theDataObjectToSave.ExtractSlamcoreDeviceFilter().IsMarkedForDeletion)
							return null;
						else
							throw new GOServerException("No SlamcoreDeviceFilter in the response to SDK DataProvider Save(). Not expecting a null response here!");
					}
					else
					{
						container.ObjectsDataSet.EnsureInitialized();
						container.ObjectsDataSet.ReconstructIndexes();

						return container.ExtractSlamcoreDeviceFilter();
					}
				}
				else
				{
					// Read the error response content for more details
					string errorContent = await response.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response.StatusCode}. Error details: {errorContent}");
				}
			}
		}

		public virtual async Task<SlamcoreDeviceFilterDataObject> GetAsync(
			SlamcoreDeviceFilterDataObject theDataObjectToGet,
			LambdaExpression securityFilterExpression = null, 
			List<string> includes = null, 
			IObjectsDataSet context = null, 
			Parameters parameters = null, 
			bool skipSecurity = false /* skipSecurity has no effect here */)
		{
			var queryStringParams = new List<string>();

			if (includes != null && includes.Any())
			{
				queryStringParams.Add("include=" + WebUtility.UrlEncode(String.Join(",", includes)));
			}
						
			var pksUrl = "";
			pksUrl += Uri.EscapeDataString(theDataObjectToGet.Id.ToString()) + "/";
			pksUrl.TrimEnd('/');

	        // get user token for currently authenticated user if any    
			var userToken = _threadContext.UserToken;

			if (userToken == null)
			{
				userToken = _userIdentity.UserToken;
			}

            if (!String.IsNullOrEmpty(userToken))
            {
				queryStringParams.Add($"_user_token={userToken}");
            }

			var applicationToken = _threadContext.ApplicationToken;

            if (applicationToken == null)
            {
                applicationToken = _userIdentity.ApplicationToken;
            }
			
			if (!String.IsNullOrEmpty(applicationToken))
            {
                queryStringParams.Add($"_application_token={applicationToken}");
            }


			if (!String.IsNullOrEmpty(_threadContext.DbKey))
			{
				queryStringParams.Add($"dbKey={_threadContext.DbKey}");
			}

			var queryUrl = String.Concat(_serviceUrl, "byid/", pksUrl, queryStringParams.Any() ? "?" + String.Join("&",queryStringParams) : "");

			using (var httpClient = new HttpClient())
			{
                // Make the GET request to the API endpoint
                var response = await httpClient.GetAsync(queryUrl);

				// Check if the request was successful (status code 200)
				if (response.IsSuccessStatusCode)
				{
					// Read the response content as a string
					string responseContent = await response.Content.ReadAsStringAsync();

					var container = _serviceProvider.GetRequiredService<SlamcoreDeviceFilterContainer>();
					JsonConvert.PopulateObject(responseContent, container, _jsonSerializerSettings);

					container.ObjectsDataSet.EnsureInitialized();
					container.ObjectsDataSet.ReconstructIndexes();

                    return container.ExtractSlamcoreDeviceFilter();
                }
				else
				{
                    // Read the error response content for more details
					string errorContent = await response.Content.ReadAsStringAsync();
					throw new GOServerException($"API request failed with status code: {response.StatusCode}. Error details: {errorContent}");
                }
            }
		}

		public virtual async Task<DataObjectCollection<SlamcoreDeviceFilterDataObject>> GetCollectionAsync(
			LambdaExpression securityFilterExpression = null, 
			string filterPredicate = null, 
			object[] filterArguments = null, 
			string orderByPredicate = null, 
			int pageNumber = 0, 
			int pageSize = 0, 
			List<string> includes = null, 
			IObjectsDataSet context = null, 
			Parameters parameters = null, 
			bool skipSecurity = false /* skipSecurity has no effect here */)
	    {
			var queryStringParams = new List<string>();

			var pFilter = filterPredicate;

            if (filterArguments != null)
            {
                var apiFilterArguments = _apiFilterArgumentBuilder.ConvertObjectArrayToApiFilterArguments(filterArguments);

                var serializedApiFilterArguments = JsonConvert.SerializeObject(apiFilterArguments);
                queryStringParams.Add("filterParameters=" + WebUtility.UrlEncode(serializedApiFilterArguments));
            }

			if (!String.IsNullOrEmpty(filterPredicate))
			{
				queryStringParams.Add("filter=" + WebUtility.UrlEncode(filterPredicate));
			}

			if (includes != null && includes.Any())
			{
				queryStringParams.Add("include=" + WebUtility.UrlEncode(String.Join(",", includes)));
			}
			
			if (!String.IsNullOrEmpty(orderByPredicate))
			{
				string[] toks = orderByPredicate.Split(new char[] { ' ' });

				if (toks.Length > 2)
					throw new GOServerException("OrderBy predicate, expected format is '{sortColumnName} {sortOrder}'");

				queryStringParams.Add($"sortColumn={toks[0]}");

				if (toks.Length > 1)
				{
					queryStringParams.Add($"sortOrder={toks[1]}");
				}
			}

			queryStringParams.Add("pageNumber=" + pageNumber);

			if (pageSize > 0)
			{
				queryStringParams.Add("pageSize=" + pageSize);
			}

	        // get user token for currently authenticated user if any    
			var userToken = _threadContext.UserToken;

			if (userToken == null)
			{
				userToken = _userIdentity.UserToken;
			}

            if (!String.IsNullOrEmpty(userToken))
            {
				queryStringParams.Add($"_user_token={userToken}");
			}

			var applicationToken = _threadContext.ApplicationToken;

            if (applicationToken == null)
            {
                applicationToken = _userIdentity.ApplicationToken;
            }

            if (!String.IsNullOrEmpty(applicationToken))
            {
                queryStringParams.Add($"_application_token={applicationToken}");
            }

			if (!String.IsNullOrEmpty(_threadContext.DbKey))
			{
				queryStringParams.Add($"dbKey={_threadContext.DbKey}");
			}

			var queryUrl = String.Concat(_serviceUrl, "list", queryStringParams.Any() ? "?" + String.Join("&", queryStringParams) : "");

            using (var httpClient = new HttpClient())
            {
                // Make the GET request to the API endpoint
                var response = await httpClient.GetAsync(queryUrl);

                // Check if the request was successful (status code 200)
                if (response.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent = await response.Content.ReadAsStringAsync();

                    var container = _serviceProvider.GetRequiredService<SlamcoreDeviceFilterCollectionContainer>();
                    JsonConvert.PopulateObject(responseContent, container, _jsonSerializerSettings);
                    container.ObjectsDataSet.EnsureInitialized();
                    container.ObjectsDataSet.ReconstructIndexes();

                    return container.ExtractSlamcoreDeviceFilterItems();
                }
                else
                {
                    // Read the error response content for more details
					string errorContent = await response.Content.ReadAsStringAsync();
					throw new GOServerException($"API request failed with status code: {response.StatusCode}. Error details: {errorContent}");
                }
            }
	    }

		public virtual async Task DeleteAsync(
			SlamcoreDeviceFilterDataObject theDataObjectToDelete, 
			LambdaExpression securityFilterExpression = null, 
			IObjectsDataSet context = null, 
			Parameters parameters = null, 
			bool skipSecurity = false /* skipSecurity has no effect here */)
		{

			var queryStringParams = new List<string>();

			// Honour dry-run 
			bool isDryRun = parameters != null && parameters.ContainsKey(ParameterKeys.DryDelete) && (bool)parameters[ParameterKeys.DryDelete] == true;

			if (isDryRun)
			{
				queryStringParams.Add("dry=true");
			}

			if (!String.IsNullOrEmpty(_threadContext.DbKey))
			{
				queryStringParams.Add($"dbKey={_threadContext.DbKey}");
			}

			// get user token for currently authenticated user if any    
			var userToken = _threadContext.UserToken;

			if (userToken == null)
			{
				userToken = _userIdentity.UserToken;
			}

			if (!String.IsNullOrEmpty(userToken))
			{
				queryStringParams.Add($"_user_token={userToken}");
			}

			var applicationToken = _threadContext.ApplicationToken;

            if (applicationToken == null)
            {
                applicationToken = _userIdentity.ApplicationToken;
            }

            if (!String.IsNullOrEmpty(applicationToken))
            {
                queryStringParams.Add($"_application_token={applicationToken}");
            }

            var uri = new Uri(String.Concat(_serviceUrl, theDataObjectToDelete.Id, queryStringParams.Any() ? "?" + String.Join("&", queryStringParams) : "?"));

			var queryUrl = String.Concat(_serviceUrl, theDataObjectToDelete.Id, queryStringParams.Any() ? "?" + String.Join("&", queryStringParams) : "?");

            // Create a handler that will store cookies
            var handler = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };
			
			using (var httpClient = new HttpClient(handler))
            {
                // Add the CSRF token to the headers
                httpClient.DefaultRequestHeaders.Add("X-CSRF-TOKEN", await GetCsrfTokenAsync(httpClient));

                // Make the DELETE request to the API endpoint
                var response = await httpClient.DeleteAsync(queryUrl);

                // Check if the request was successful (status code 200)
                if (response.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent = await response.Content.ReadAsStringAsync();

					// if this is a dry-run request, read the response and return to caller via the parameters
					if (isDryRun)
					{
						parameters[ParameterKeys.DeleteStackJsonEncoded] = responseContent;
					}
                }
                else
                {
                    // Read the error response content for more details
					string errorContent = await response.Content.ReadAsStringAsync();
					throw new GOServerException($"API request failed with status code: {response.StatusCode}. Error details: {errorContent}");
                }
            }
		}

        public virtual async Task<int> CountAsync(
			LambdaExpression securityFilterExpression = null, 
			string filterPredicate = null, 
			object[] filterArguments = null, 
			IObjectsDataSet context = null, 
			Parameters parameters = null, 
			bool skipSecurity = false /* skipSecurity has no effect here */)
        {
            var queryStringParams = new List<string>();

            var pFilter = filterPredicate;

            if (filterArguments != null)
            {
                var apiFilterArguments = _apiFilterArgumentBuilder.ConvertObjectArrayToApiFilterArguments(filterArguments);

                var serializedApiFilterArguments = JsonConvert.SerializeObject(apiFilterArguments);
                queryStringParams.Add("filterParameters=" + WebUtility.UrlEncode(serializedApiFilterArguments));
            }

            if (!String.IsNullOrEmpty(filterPredicate))
            {
                queryStringParams.Add("filter=" + WebUtility.UrlEncode(filterPredicate));
            }

            // get user token for currently authenticated user if any    
            var userToken = _threadContext.UserToken;

            if (userToken == null)
            {
                userToken = _userIdentity.UserToken;
            }

            if (!String.IsNullOrEmpty(userToken))
            {
                queryStringParams.Add($"_user_token={userToken}");
            }

			var applicationToken = _threadContext.ApplicationToken;

            if (applicationToken == null)
            {
                applicationToken = _userIdentity.ApplicationToken;
            }

            if (!String.IsNullOrEmpty(applicationToken))
            {
                queryStringParams.Add($"_application_token={applicationToken}");
            }

            if (!String.IsNullOrEmpty(_threadContext.DbKey))
            {
                queryStringParams.Add($"dbKey={_threadContext.DbKey}");
            }

            var queryUrl = String.Concat(_serviceUrl, "count", queryStringParams.Any() ? "?" + String.Join("&", queryStringParams) : "");

            using (var httpClient = new HttpClient())
            {
                // Make the GET request to the API endpoint
                var response = await httpClient.GetAsync(queryUrl);

                // Check if the request was successful (status code 200)
                if (response.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent = await response.Content.ReadAsStringAsync();

					return Convert.ToInt32(responseContent);
                }
                else
                {
                    // Read the error response content for more details
					string errorContent = await response.Content.ReadAsStringAsync();
					throw new GOServerException($"API request failed with status code: {response.StatusCode}. Error details: {errorContent}");
                }
            }
		}

		public Task<SlamcoreDeviceFilterDataObject> SaveOutOfTransactionAsync(SlamcoreDeviceFilterDataObject entity, Parameters parameters = null, bool skipSecurity = false)
        {
            throw new NotImplementedException("Not relevant in the context of API calls");
        }
    }
}
