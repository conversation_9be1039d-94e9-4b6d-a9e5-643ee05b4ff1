# Staging Table Alternatives - Clean Database Schema Approach

## Current Problem
The BulkImporter currently creates permanent staging tables (`[Staging].[ImportSession]`, `[Staging].[DriverImport]`, `[Staging].[VehicleImport]`) that persist in the database schema indefinitely.

## Recommended Solutions

### Option 1: Session-Scoped Temporary Tables (Recommended)
**Approach**: Create temporary tables that are automatically dropped when the session ends.

```sql
-- Create session-specific temporary tables
CREATE TABLE #DriverImport_{SessionId} (
    [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
    [RowNumber] INT NOT NULL,
    [ValidationStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    [ValidationErrors] NVARCHAR(MAX) NULL,
    -- All existing fields from DriverImport
    [ExternalDriverId] NVARCHAR(50) NULL,
    [PersonFirstName] NVARCHAR(50) NOT NULL,
    [PersonLastName] NVARCHAR(50) NOT NULL,
    -- ... (all other fields)
)

CREATE TABLE #VehicleImport_{SessionId} (
    [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
    [RowNumber] INT NOT NULL,
    [ValidationStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    [ValidationErrors] NVARCHAR(MAX) NULL,
    -- All existing fields from VehicleImport
    [ExternalVehicleId] NVARCHAR(50) NULL,
    [HireNo] NVARCHAR(50) NOT NULL,
    [SerialNo] NVARCHAR(50) NOT NULL,
    -- ... (all other fields)
)
```

**Benefits**:
- Tables are automatically dropped when connection closes
- No permanent schema pollution
- Session isolation prevents conflicts
- SQL Server handles cleanup automatically

### Option 2: Table Variables for Small Datasets
**Approach**: Use table variables in stored procedures for data processing.

```sql
DECLARE @DriverData TABLE (
    [RowNumber] INT IDENTITY(1,1),
    [ValidationStatus] NVARCHAR(20) DEFAULT 'Pending',
    [PersonFirstName] NVARCHAR(50),
    [PersonLastName] NVARCHAR(50),
    -- ... other fields
)

DECLARE @VehicleData TABLE (
    [RowNumber] INT IDENTITY(1,1),
    [ValidationStatus] NVARCHAR(20) DEFAULT 'Pending',
    [HireNo] NVARCHAR(50),
    [SerialNo] NVARCHAR(50),
    -- ... other fields
)
```

**Benefits**:
- Memory-based, no disk I/O for small datasets
- Automatically cleaned up
- No schema objects created

**Limitations**:
- Memory constraints for large datasets (>1000 rows)
- Cannot be shared across connections

### Option 3: Direct Processing with CTE/Bulk Operations
**Approach**: Generate and process data in a single operation without staging.

```sql
-- Direct insert from generated data to production tables
WITH GeneratedDrivers AS (
    -- Complex CTE for data generation
    SELECT 
        'EXT_DRV_' + RIGHT('000000' + CAST(ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS VARCHAR), 6) AS ExternalDriverId,
        -- ... data generation logic
    FROM master.dbo.spt_values s1
    CROSS JOIN master.dbo.spt_values s2
    WHERE ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) <= @DriverCount
),
ValidatedDrivers AS (
    -- Validation logic in CTE
    SELECT *,
        CASE 
            WHEN PersonFirstName IS NULL OR PersonLastName IS NULL THEN 'Invalid'
            ELSE 'Valid'
        END AS ValidationStatus
    FROM GeneratedDrivers
)
-- Direct insert to production tables
INSERT INTO Person (FirstName, LastName, Email, ...)
SELECT PersonFirstName, PersonLastName, PersonEmail, ...
FROM ValidatedDrivers
WHERE ValidationStatus = 'Valid'
```

**Benefits**:
- No temporary objects at all
- Single transaction, atomic operation
- Highest performance for batch operations

**Limitations**:
- Less flexibility for complex validation
- Harder to debug individual step failures

### Option 4: Hybrid Approach with Minimal Session Tracking
**Approach**: Keep only essential session tracking, use temporary tables for data.

```sql
-- Minimal session tracking (optional, could be in-memory only)
DECLARE @SessionInfo TABLE (
    SessionId UNIQUEIDENTIFIER,
    StartTime DATETIME2,
    Status NVARCHAR(20),
    TotalRows INT,
    ProcessedRows INT
)

-- Temporary staging tables per session
CREATE TABLE #DriverImport (...)
CREATE TABLE #VehicleImport (...)
```

## Implementation Strategy

### Phase 1: Refactor SqlDataGenerationService
1. Modify `CreateImportSessionAsync` to use in-memory session tracking
2. Update data generation methods to create temporary tables
3. Ensure cleanup happens automatically on connection close

### Phase 2: Update Stored Procedures
1. Modify validation procedures to work with temporary tables
2. Update processing procedures for direct production table operations
3. Remove references to permanent staging tables

### Phase 3: Clean Database Schema
1. Create migration script to drop existing staging schema
2. Update SQL setup scripts to not create permanent objects
3. Test thoroughly with various data volumes

## Code Changes Required

### SqlDataGenerationService Updates
```csharp
public async Task<Guid> CreateImportSessionAsync(string sessionName, CancellationToken cancellationToken = default)
{
    var sessionId = Guid.NewGuid();
    
    // Option 1: Store session info in memory only
    _currentSession = new ImportSession 
    {
        Id = sessionId,
        SessionName = sessionName,
        StartTime = DateTime.UtcNow,
        Status = "Running"
    };
    
    // Create session-specific temporary tables
    await CreateTemporaryTablesAsync(sessionId, cancellationToken);
    
    return sessionId;
}

private async Task CreateTemporaryTablesAsync(Guid sessionId, CancellationToken cancellationToken)
{
    using var connection = new SqlConnection(_connectionOptions.FleetXQConnection);
    await connection.OpenAsync(cancellationToken);
    
    // Create temporary tables with session-specific names
    var createDriverTable = $@"
        CREATE TABLE #DriverImport_{sessionId:N} (
            [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
            [RowNumber] INT NOT NULL,
            [ValidationStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
            -- ... all other fields
        )";
    
    var createVehicleTable = $@"
        CREATE TABLE #VehicleImport_{sessionId:N} (
            [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
            [RowNumber] INT NOT NULL,
            [ValidationStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending',
            -- ... all other fields
        )";
    
    await connection.ExecuteNonQueryAsync(createDriverTable);
    await connection.ExecuteNonQueryAsync(createVehicleTable);
}
```

### Configuration Updates
```json
{
  "BulkImporter": {
    "UseTemporaryTables": true,
    "SessionTrackingMode": "Memory", // "Memory", "TempTable", "None"
    "MaxMemoryDatasetSize": 10000,
    // Remove staging-related options
    // "CleanupStagingData": false,
    // "StagingDataRetentionDays": 7
  }
}
```

## Migration Script
```sql
-- Drop existing staging schema and all objects
IF EXISTS (SELECT * FROM sys.schemas WHERE name = 'Staging')
BEGIN
    -- Drop all tables in staging schema
    DECLARE @sql NVARCHAR(MAX) = ''
    SELECT @sql = @sql + 'DROP TABLE [Staging].[' + TABLE_NAME + '];' + CHAR(13)
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = 'Staging'
    
    EXEC sp_executesql @sql
    
    -- Drop staging schema
    DROP SCHEMA [Staging]
    
    PRINT 'Removed Staging schema and all related objects'
END
```

## Recommended Implementation: Option 1 (Session-Scoped Temporary Tables)

This approach provides the best balance of:
- **Clean Schema**: No permanent objects
- **Performance**: Good performance with automatic cleanup
- **Flexibility**: Maintains current validation and processing patterns
- **Debugging**: Still allows inspection of intermediate data during processing
- **Session Isolation**: Multiple concurrent imports won't interfere

The temporary tables will be automatically dropped when the database connection closes, ensuring the database schema remains clean while maintaining all current functionality.
