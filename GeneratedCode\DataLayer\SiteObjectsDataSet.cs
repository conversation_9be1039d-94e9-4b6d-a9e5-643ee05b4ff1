﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class SiteObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _siteObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all Site objects for current dataset
		private ConcurrentDictionary< int, SiteDataObject> _siteObjects = new ConcurrentDictionary< int, SiteDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<SiteDataObject> _mergedDataObjects;

		private ConcurrentQueue<SiteDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<SiteDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> SiteObjectInternalIds
		{ 
			get { return _siteObjectInternalIds; }
			set { _siteObjectInternalIds = value; }
		}
		
		// Collection holding all Site objects for current dataset
		[JsonProperty("SiteObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, SiteDataObject> SiteObjects
		{ 
			get { return _siteObjects; }
			set { _siteObjects = value; }
		}
		
		
 
		
 
		
 
		
 
		
 
		// Index to quickly find all Site with a given customer foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Customer_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		// Index to quickly find all Site with a given timezone foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Timezone_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public SiteObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public SiteObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<SiteObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SiteObjects)
			{
                var cloneObject = (SiteDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SiteObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SiteObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SiteObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			foreach(var fkKeyValue in this.Customer_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Customer_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Customer_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Timezone_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Timezone_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Timezone_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<SiteObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SiteObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (SiteDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.SiteObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SiteObjectInternalIds
				.Where(o => this.SiteObjects[o.Value].IsDirty || this.SiteObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SiteObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var site in SiteObjects.Values)
			{
				yield return site; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the SiteObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "SiteObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SiteObjects.TryAdd(newInternalId, (SiteDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (SiteObjectInternalIds.ContainsKey(((SiteDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = SiteObjectInternalIds.TryRemove(((SiteDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SiteObjectInternalIds.TryAdd(((SiteDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as SiteDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "SiteDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

	 
	 
	 
	 
	 
			// Update the Customer FK Index 
			if (!Customer_FKIndex.ContainsKey((objectToAdd as SiteDataObject).CustomerId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Customer_FKIndex.TryAdd((objectToAdd as SiteDataObject).CustomerId, new List<int>());
				}
			}
				
			if (!Customer_FKIndex[(objectToAdd as SiteDataObject).CustomerId].Contains(newInternalId))
				Customer_FKIndex[(objectToAdd as SiteDataObject).CustomerId].Add(newInternalId);

            CustomerDataObject relatedCustomer;
            if ((objectToAdd as SiteDataObject)._customer_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as SiteDataObject)._customer_NewObjectId;

	            relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToAdd as SiteDataObject).CustomerId) { IsNew = false });
            }

			if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
                relatedCustomer.NotifyPropertyChanged("Sites", new SeenObjectCollection());
			
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
			// Update the Timezone FK Index 
			if (!Timezone_FKIndex.ContainsKey((objectToAdd as SiteDataObject).TimezoneId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Timezone_FKIndex.TryAdd((objectToAdd as SiteDataObject).TimezoneId, new List<int>());
				}
			}
				
			if (!Timezone_FKIndex[(objectToAdd as SiteDataObject).TimezoneId].Contains(newInternalId))
				Timezone_FKIndex[(objectToAdd as SiteDataObject).TimezoneId].Add(newInternalId);

            TimezoneDataObject relatedTimezone;
            if ((objectToAdd as SiteDataObject)._timezone_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<TimezoneDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as SiteDataObject)._timezone_NewObjectId;

	            relatedTimezone = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedTimezone = _rootObjectDataSet.GetObject(new TimezoneDataObject((objectToAdd as SiteDataObject).TimezoneId) { IsNew = false });
            }

			if (relatedTimezone != null && this.RootObjectDataSet.NotifyChanges)
                relatedTimezone.NotifyPropertyChanged("Sites", new SeenObjectCollection());
			
	 
	 
	 
	 
	 
	 
	 
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (SiteObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as SiteDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "SiteObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = SiteObjectInternalIds.ContainsKey((objectToRemove as SiteDataObject).PrimaryKey) ? (int?) SiteObjectInternalIds[(objectToRemove as SiteDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				SiteDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SiteObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = SiteObjectInternalIds.TryRemove((objectToRemove as SiteDataObject).PrimaryKey, out idvalue);
					}
				}
				
		 
		 
		 
		 
		 
			// Delete the Customer FK Index 
				if (Customer_FKIndex.ContainsKey((objectToRemove as SiteDataObject).CustomerId) && Customer_FKIndex[(objectToRemove as SiteDataObject).CustomerId].Contains((int)objectToRemoveInternalId))
				{
					Customer_FKIndex[(objectToRemove as SiteDataObject).CustomerId].Remove((int)objectToRemoveInternalId);

					if (!Customer_FKIndex[(objectToRemove as SiteDataObject).CustomerId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Customer_FKIndex.TryRemove((objectToRemove as SiteDataObject).CustomerId, out outvalue);
						}
					}
				}
				
				CustomerDataObject relatedCustomer;
	            if ((objectToRemove as SiteDataObject)._customer_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as SiteDataObject)._customer_NewObjectId;

					relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToRemove as SiteDataObject).CustomerId) { IsNew = false });
	            }

	            if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
	                relatedCustomer.NotifyPropertyChanged("Sites", new SeenObjectCollection());
				
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			// Delete the Timezone FK Index 
				if (Timezone_FKIndex.ContainsKey((objectToRemove as SiteDataObject).TimezoneId) && Timezone_FKIndex[(objectToRemove as SiteDataObject).TimezoneId].Contains((int)objectToRemoveInternalId))
				{
					Timezone_FKIndex[(objectToRemove as SiteDataObject).TimezoneId].Remove((int)objectToRemoveInternalId);

					if (!Timezone_FKIndex[(objectToRemove as SiteDataObject).TimezoneId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Timezone_FKIndex.TryRemove((objectToRemove as SiteDataObject).TimezoneId, out outvalue);
						}
					}
				}
				
				TimezoneDataObject relatedTimezone;
	            if ((objectToRemove as SiteDataObject)._timezone_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<TimezoneDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as SiteDataObject)._timezone_NewObjectId;

					relatedTimezone = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedTimezone = _rootObjectDataSet.GetObject(new TimezoneDataObject((objectToRemove as SiteDataObject).TimezoneId) { IsNew = false });
	            }

	            if (relatedTimezone != null && this.RootObjectDataSet.NotifyChanges)
	                relatedTimezone.NotifyPropertyChanged("Sites", new SeenObjectCollection());
				
		 
		 
		 
		 
		 
		 
		 
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return SiteObjects.ContainsKey(internalObjectId) ? SiteObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as SiteDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "SiteObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = SiteObjectInternalIds.ContainsKey((objectToGet as SiteDataObject).PrimaryKey) ? (int?) SiteObjectInternalIds[(objectToGet as SiteDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return SiteObjects.ContainsKey((int)objectToGetInternalId) ? SiteObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return SiteObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return SiteObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		 
		 
		 
		 
		
		public IEnumerable<SiteDataObject> GetSitesForCustomer(CustomerDataObject customerInstance) 
		{
			if (customerInstance.IsNew)
            {
			
              return SiteObjects.Where(o => o.Value._customer_NewObjectId != null && o.Value._customer_NewObjectId == customerInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Customer_FKIndex.ContainsKey(customerInstance.Id))
			{
				return Customer_FKIndex[customerInstance.Id].Where(e => SiteObjects.ContainsKey(e)).Select(e => SiteObjects[e]);
			}
			
			return new DataObjectCollection<SiteDataObject>();
		}
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		
		public IEnumerable<SiteDataObject> GetSitesForTimezone(TimezoneDataObject timezoneInstance) 
		{
			if (timezoneInstance.IsNew)
            {
			
              return SiteObjects.Where(o => o.Value._timezone_NewObjectId != null && o.Value._timezone_NewObjectId == timezoneInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Timezone_FKIndex.ContainsKey(timezoneInstance.Id))
			{
				return Timezone_FKIndex[timezoneInstance.Id].Where(e => SiteObjects.ContainsKey(e)).Select(e => SiteObjects[e]);
			}
			
			return new DataObjectCollection<SiteDataObject>();
		}
		 
		 
		 
		 
		 
		 
		 
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
			if (relationName == "AccessGroupToSiteItems")
            {
				IEnumerable< AccessGroupToSiteDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.AccessGroupToSiteObjectsDataSet.GetAccessGroupToSiteItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ChecklistStatusViewItems")
            {
				IEnumerable< ChecklistStatusViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ChecklistStatusViewObjectsDataSet.GetChecklistStatusViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CurrentDriverStatusChartViewItems")
            {
				IEnumerable< CurrentDriverStatusChartViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CurrentDriverStatusChartViewObjectsDataSet.GetCurrentDriverStatusChartViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CurrentVehicleStatusChartViewItems")
            {
				IEnumerable< CurrentVehicleStatusChartViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CurrentVehicleStatusChartViewObjectsDataSet.GetCurrentVehicleStatusChartViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "DashboardCardViewItems")
            {
				IEnumerable< DashboardDriverCardViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardDriverCardViewObjectsDataSet.GetDashboardCardViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardDriverCardStoreProcedureItems")
            {
				IEnumerable< DashboardDriverCardStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardDriverCardStoreProcedureObjectsDataSet.GetDashboardDriverCardStoreProcedureItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardFilterItems")
            {
				IEnumerable< DashboardFilterDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject);
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.DriverAccessAbuseFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.PedestrianDetectionHistoryFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.MainDashboardFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.GeneralProductivityReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.EmailSubscriptionReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.VORReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.OnDemandAuthorisationFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.DashboardFilterMoreFieldsObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.HireDeHireReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.SlamcoreDeviceFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.ImpactReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.LicenseExpiryReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.BroadcastMessageHistoryFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.SynchronizationStatusReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.ProficiencyReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.AllVehicleCalibrationFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.PreOpReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.MachineUnlockReportFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				relatedObjects = relatedObjects.Concat(_rootObjectDataSet.FeatureSubscriptionsFilterObjectsDataSet.GetDashboardFilterItemsForSite(rootObject as SiteDataObject).Cast<DashboardFilterDataObject>());
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardVehicleCardStoreProcedureItems")
            {
				IEnumerable< DashboardVehicleCardStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardVehicleCardStoreProcedureObjectsDataSet.GetDashboardVehicleCardStoreProcedureItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DashboardVehicleCardViewItems")
            {
				IEnumerable< DashboardVehicleCardViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DashboardVehicleCardViewObjectsDataSet.GetDashboardVehicleCardViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DepartmentItems")
            {
				IEnumerable< DepartmentDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DepartmentObjectsDataSet.GetDepartmentItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DriverItems")
            {
				IEnumerable< DriverDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DriverObjectsDataSet.GetDriverItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DriverLicenseExpiryStoreProcedureItems")
            {
				IEnumerable< DriverLicenseExpiryStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DriverLicenseExpiryStoreProcedureObjectsDataSet.GetDriverLicenseExpiryStoreProcedureItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "DriverLicenseExpiryViewItems")
            {
				IEnumerable< DriverLicenseExpiryViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DriverLicenseExpiryViewObjectsDataSet.GetDriverLicenseExpiryViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "FloorPlanItems")
            {
				IEnumerable< FloorPlanDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.FloorPlanObjectsDataSet.GetFloorPlanItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactFrequencyPerTimeSlotViewItems")
            {
				IEnumerable< ImpactFrequencyPerTimeSlotViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactFrequencyPerTimeSlotViewObjectsDataSet.GetImpactFrequencyPerTimeSlotViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactFrequencyPerWeekDayViewItems")
            {
				IEnumerable< ImpactFrequencyPerWeekDayViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactFrequencyPerWeekDayViewObjectsDataSet.GetImpactFrequencyPerWeekDayViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ImpactFrequencyPerWeekMonthViewItems")
            {
				IEnumerable< ImpactFrequencyPerWeekMonthViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ImpactFrequencyPerWeekMonthViewObjectsDataSet.GetImpactFrequencyPerWeekMonthViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "IncompletedChecklistViewItems")
            {
				IEnumerable< IncompletedChecklistViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.IncompletedChecklistViewObjectsDataSet.GetIncompletedChecklistViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "LoggedHoursVersusSeatHoursViewItems")
            {
				IEnumerable< LoggedHoursVersusSeatHoursViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.LoggedHoursVersusSeatHoursViewObjectsDataSet.GetLoggedHoursVersusSeatHoursViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonAllocationItems")
            {
				IEnumerable< PersonAllocationDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonAllocationObjectsDataSet.GetPersonAllocationItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonItems")
            {
				IEnumerable< PersonDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonObjectsDataSet.GetPersonItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonToSiteVehicleMasterAccessViewItems")
            {
				IEnumerable< PersonToSiteVehicleMasterAccessViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonToSiteVehicleMasterAccessViewObjectsDataSet.GetPersonToSiteVehicleMasterAccessViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "PersonToSiteVehicleNormalAccessViewItems")
            {
				IEnumerable< PersonToSiteVehicleNormalAccessViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonToSiteVehicleNormalAccessViewObjectsDataSet.GetPersonToSiteVehicleNormalAccessViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "SiteFloorPlanItems")
            {
				IEnumerable< SiteFloorPlanDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SiteFloorPlanObjectsDataSet.GetSiteFloorPlanItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "SiteVehicleMasterCardAccessItems")
            {
				IEnumerable< SiteVehicleMasterCardAccessDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SiteVehicleMasterCardAccessObjectsDataSet.GetSiteVehicleMasterCardAccessItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "SiteVehicleNormalCardAccessItems")
            {
				IEnumerable< SiteVehicleNormalCardAccessDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SiteVehicleNormalCardAccessObjectsDataSet.GetSiteVehicleNormalCardAccessItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "TodaysImpactStoreProcedureItems")
            {
				IEnumerable< TodaysImpactStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysImpactStoreProcedureObjectsDataSet.GetTodaysImpactStoreProcedureItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysImpactViewItems")
            {
				IEnumerable< TodaysImpactViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysImpactViewObjectsDataSet.GetTodaysImpactViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysPreopCheckStoreProcedureItems")
            {
				IEnumerable< TodaysPreopCheckStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysPreopCheckStoreProcedureObjectsDataSet.GetTodaysPreopCheckStoreProcedureItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "TodaysPreopCheckViewItems")
            {
				IEnumerable< TodaysPreopCheckViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TodaysPreopCheckViewObjectsDataSet.GetTodaysPreopCheckViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleItems")
            {
				IEnumerable< VehicleDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleObjectsDataSet.GetVehicleItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleUtilizationLastTwelveHoursStoreProcedureItems")
            {
				IEnumerable< VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleUtilizationLastTwelveHoursStoreProcedureObjectsDataSet.GetVehicleUtilizationLastTwelveHoursStoreProcedureItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleUtilizationLastTwelveHoursViewItems")
            {
				IEnumerable< VehicleUtilizationLastTwelveHoursViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleUtilizationLastTwelveHoursViewObjectsDataSet.GetVehicleUtilizationLastTwelveHoursViewItemsForSite(rootObject as SiteDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var SiteDataSet = dataSetToMerge as SiteObjectsDataSet;
				if(SiteDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in SiteDataSet.SiteObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "SiteObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as SiteDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
		 
		 
		 
		 
		 
			// Reconstruct the Customer FK Index 
			Customer_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in SiteObjects.Values)
			{
				if (item.CustomerId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.CustomerId;	

				if (!Customer_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Customer_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SiteObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Customer_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			// Reconstruct the Timezone FK Index 
			Timezone_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in SiteObjects.Values)
			{
				if (item.TimezoneId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.TimezoneId;	

				if (!Timezone_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Timezone_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SiteObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Timezone_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		 
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (SiteObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}