﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMSlamcorePedestrianDetection" 
		table="[SlamcorePedestrianDetection]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="XPosition" >
            <column name="`XPosition`" sql-type="decimal (20, 17)" not-null="true" precision="20" scale="17"/>
        </property> 
		<property name="YPosition" >
            <column name="`YPosition`" sql-type="decimal (20, 17)" not-null="true" precision="20" scale="17"/>
        </property> 

		
		<!-- many-to-one SlamcoreDevice -->
		<property name="SlamcoreDeviceId" type="System.Guid" not-null="true" formula = "[SlamcoreDeviceId]"></property>  
		<many-to-one name="SlamcoreDevice"  > 
			<column name="`SlamcoreDeviceId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
 




    </class> 

</hibernate-mapping>