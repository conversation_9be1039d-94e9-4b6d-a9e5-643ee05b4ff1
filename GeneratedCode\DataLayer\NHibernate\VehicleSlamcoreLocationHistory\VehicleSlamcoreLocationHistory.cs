﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge ORMEntity -> DataObject
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	[Serializable]
	public class ORMVehicleSlamcoreLocationHistory : IORMEntity 
	{
		public virtual System.Guid Id { get; set; }
 
		///
		/// All Persistent non-key fields
		///
		public virtual Nullable<System.Decimal> Speed { get; set; }
		public virtual Nullable<System.Decimal> Bearing { get; set; }
		public virtual System.Decimal YPosition { get; set; }
		public virtual System.Decimal WOrientation { get; set; }
		public virtual System.Decimal XOrientation { get; set; }
		public virtual Nullable<System.Int32> TrailSequence { get; set; }
		public virtual System.Decimal XPosition { get; set; }
		public virtual System.Decimal ZPosition { get; set; }
		public virtual System.Decimal YOrientation { get; set; }
		public virtual System.DateTime AcquisitionDateTime { get; set; }
		public virtual System.Int32 Status { get; set; }
		public virtual Nullable<System.Int32> EventType { get; set; }
		public virtual System.Int32 ReferenceFrameCategory { get; set; }
 
		///
		/// PK-Side one-to-one relations
		///
 
		///
		/// All FK-Side Relations
		///
		public virtual ORMSlamcoreDevice SlamcoreDevice { get; set; }
		public virtual System.Guid SlamcoreDeviceId { get; set; }

	
 
		///
		/// PK-Side one-to-many relations
		///
 
		///
		/// Bridge to DataObject
		///
		public virtual IDataObject ToDataObject(IObjectsDataSet dataset, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
            var session = nHibernateSessionController.GetCurrentSession(threadContext);
            session.Evict(this);

			var x = serviceProvider.GetRequiredService<VehicleSlamcoreLocationHistoryDataObject>();

 
			SetProperties(x);

			x.IsDirty = x.IsNew = x.IsMarkedForDeletion = false;

			x.ObjectsDataSet = dataset;
			x.ObjectsDataSet.AddObjectIfDoesNotExist(x);

			// Deep-map prefetch relations
			if (PrefetchAssociations.HasPrefetchForEntity("VehicleSlamcoreLocationHistory", dataProviderTransaction?.Parameters))
			{
				SetRelations(x, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction);
			}

			return x;
		}

		protected void SetProperties(VehicleSlamcoreLocationHistoryDataObject x)
		{
			x.SetIdValue(Id, false, false);
			x.SetSpeedValue(Speed, false, false);
			x.SetBearingValue(Bearing, false, false);
			x.SetYPositionValue(YPosition, false, false);
			x.SetWOrientationValue(WOrientation, false, false);
			x.SetXOrientationValue(XOrientation, false, false);
			x.SetTrailSequenceValue(TrailSequence, false, false);
			x.SetXPositionValue(XPosition, false, false);
			x.SetZPositionValue(ZPosition, false, false);
			x.SetYOrientationValue(YOrientation, false, false);
			x.SetAcquisitionDateTimeValue(AcquisitionDateTime, false, false);
			x.SetStatusValue((SlamcoreTrackingStatusTypesEnum)Status, false, false);
			x.SetEventTypeValue((Nullable<SlamcoreEventTypesEnum>)EventType, false, false);
			x.SetReferenceFrameCategoryValue((SlamcoreReferenceFramesEnum)ReferenceFrameCategory, false, false);
			x.SetSlamcoreDeviceIdValue(this.SlamcoreDeviceId, false, false);
		}

		protected void SetRelations(VehicleSlamcoreLocationHistoryDataObject x, IServiceProvider serviceProvider, IThreadContext threadContext, INHibernateSessionController nHibernateSessionController, IDataProviderTransaction dataProviderTransaction)
		{
			var prefetches = PrefetchAssociations.Get("VehicleSlamcoreLocationHistory", dataProviderTransaction?.Parameters);

			if (prefetches.Contains("SlamcoreDevice") && this.SlamcoreDevice != null)
			{
				var slamcoreDevice = x.ObjectsDataSet.GetObject(new SlamcoreDeviceDataObject((System.Guid)this.SlamcoreDevice.Id) { IsNew = false });

				if (slamcoreDevice == null)
					slamcoreDevice = this.SlamcoreDevice.ToDataObject(x.ObjectsDataSet, serviceProvider, threadContext, nHibernateSessionController, dataProviderTransaction) as SlamcoreDeviceDataObject;

				x.SetSlamcoreDeviceValue(slamcoreDevice);
			}

		}
		
		// For database filtering, nothing IsNew or IsDirty, but these stubs needed for ORM security predicates to compile
		public virtual bool IsNew => false;
		public virtual bool IsDirty => false;

	}
}