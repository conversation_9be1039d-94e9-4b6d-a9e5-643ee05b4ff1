﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'SlamcoreDevice'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class SlamcoreDeviceDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("CustomerId")]
		protected System.Guid _customerId;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("IPAddress")]
		protected System.String _iPAddress;
		[JsonProperty ("LastConnectedDateTime")]
		protected Nullable<System.DateTime> _lastConnectedDateTime;
		[JsonProperty("LastConnectedDateTime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _lastConnectedDateTime_WithTimezoneOffset;
		[JsonProperty ("Name")]
		protected System.String _name;
		[JsonProperty ("SerialNo")]
		protected System.String _serialNo;
		[JsonProperty ("SlamcoreAPIKeyId")]
		protected Nullable<System.Guid> _slamcoreAPIKeyId;
		[JsonProperty ("SlamcoreAwareAuthenticationDetailsId")]
		protected Nullable<System.Guid> _slamcoreAwareAuthenticationDetailsId;
		[JsonProperty ("Status")]
		protected SlamcoreStatusEnum _status;
		[JsonProperty ("UpdateRate")]
		protected SlamcoreUpdateRateEnum _updateRate;
		[JsonProperty ("VehicleId")]
		protected Nullable<System.Guid> _vehicleId;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)
		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _customer_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_customer_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _slamcoreAPIKey_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_slamcoreAPIKey_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }


		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _slamcoreAwareAuthenticationDetails_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_slamcoreAwareAuthenticationDetails_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }





		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _vehicle_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_vehicle_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }



		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public SlamcoreDeviceDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public SlamcoreDeviceDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public SlamcoreDeviceDataObject Initialize(System.Guid id)
		{
			this._id = id;
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public SlamcoreDeviceDataObject Initialize(SlamcoreDeviceDataObject template, bool deepCopy)
		{
			this.SetLastConnectedDateTimeValue(template.LastConnectedDateTime, false, false);
			this._lastConnectedDateTime_WithTimezoneOffset = template._lastConnectedDateTime_WithTimezoneOffset;
			this.SetCustomerIdValue(template.CustomerId, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetIPAddressValue(template.IPAddress, false, false);
			this.SetNameValue(template.Name, false, false);
			this.SetSerialNoValue(template.SerialNo, false, false);
			this.SetSlamcoreAPIKeyIdValue(template.SlamcoreAPIKeyId, false, false);
			this.SetSlamcoreAwareAuthenticationDetailsIdValue(template.SlamcoreAwareAuthenticationDetailsId, false, false);
			this.SetStatusValue(template.Status, false, false);
			this.SetUpdateRateValue(template.UpdateRate, false, false);
			this.SetVehicleIdValue(template.VehicleId, false, false);
 
			this._customer_NewObjectId = template._customer_NewObjectId;
 
			this._slamcoreAPIKey_NewObjectId = template._slamcoreAPIKey_NewObjectId;
 
			this._slamcoreAwareAuthenticationDetails_NewObjectId = template._slamcoreAwareAuthenticationDetails_NewObjectId;
 
 
 
 
			this._vehicle_NewObjectId = template._vehicle_NewObjectId;
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual SlamcoreDeviceDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual SlamcoreDeviceDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var slamcoreDeviceSource = sourceObject as SlamcoreDeviceDataObject;

			if (ReferenceEquals(null, slamcoreDeviceSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetCustomerIdValue(slamcoreDeviceSource.CustomerId, false, false);
			this.SetIdValue(slamcoreDeviceSource.Id, false, false);
			this.SetIPAddressValue(slamcoreDeviceSource.IPAddress, false, false);
			this.SetLastConnectedDateTimeValue(slamcoreDeviceSource.LastConnectedDateTime, false, false);
			this.SetNameValue(slamcoreDeviceSource.Name, false, false);
			this.SetSerialNoValue(slamcoreDeviceSource.SerialNo, false, false);
			this.SetSlamcoreAPIKeyIdValue(slamcoreDeviceSource.SlamcoreAPIKeyId, false, false);
			this.SetSlamcoreAwareAuthenticationDetailsIdValue(slamcoreDeviceSource.SlamcoreAwareAuthenticationDetailsId, false, false);
			this.SetStatusValue(slamcoreDeviceSource.Status, false, false);
			this.SetUpdateRateValue(slamcoreDeviceSource.UpdateRate, false, false);
			this.SetVehicleIdValue(slamcoreDeviceSource.VehicleId, false, false);
			this._customer_NewObjectId = (sourceObject as SlamcoreDeviceDataObject)._customer_NewObjectId;

			this._slamcoreAPIKey_NewObjectId = (sourceObject as SlamcoreDeviceDataObject)._slamcoreAPIKey_NewObjectId;

			this._slamcoreAwareAuthenticationDetails_NewObjectId = (sourceObject as SlamcoreDeviceDataObject)._slamcoreAwareAuthenticationDetails_NewObjectId;




			this._vehicle_NewObjectId = (sourceObject as SlamcoreDeviceDataObject)._vehicle_NewObjectId;


			if (deepCopy)
			{
				this.ObjectsDataSet = slamcoreDeviceSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(slamcoreDeviceSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as SlamcoreDeviceDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {
			if (this._customer_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._customer_NewObjectId))
				{
                    this._customer_NewObjectId = null;
				}
                else
				{
					this._customer_NewObjectId = datasetMergingInternalIdMapping[(int) this._customer_NewObjectId];
				}
			}

			if (this._slamcoreAPIKey_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._slamcoreAPIKey_NewObjectId))
				{
                    this._slamcoreAPIKey_NewObjectId = null;
				}
                else
				{
					this._slamcoreAPIKey_NewObjectId = datasetMergingInternalIdMapping[(int) this._slamcoreAPIKey_NewObjectId];
				}
			}

			if (this._slamcoreAwareAuthenticationDetails_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._slamcoreAwareAuthenticationDetails_NewObjectId))
				{
                    this._slamcoreAwareAuthenticationDetails_NewObjectId = null;
				}
                else
				{
					this._slamcoreAwareAuthenticationDetails_NewObjectId = datasetMergingInternalIdMapping[(int) this._slamcoreAwareAuthenticationDetails_NewObjectId];
				}
			}




			if (this._vehicle_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._vehicle_NewObjectId))
				{
                    this._vehicle_NewObjectId = null;
				}
                else
				{
					this._vehicle_NewObjectId = datasetMergingInternalIdMapping[(int) this._vehicle_NewObjectId];
				}
			}


		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<CustomerDataObject> _customerService => _serviceProvider.GetRequiredService<IDataProvider<CustomerDataObject>>();
      public virtual void SetCustomerValue(CustomerDataObject valueToSet)
		{
			SetCustomerValue(valueToSet, true, true);
		}

        public virtual void SetCustomerValue(CustomerDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			CustomerDataObject existing_customer = null ;

			if ( !(ObjectsDataSet == null))
			{
				CustomerDataObject key;

				if (this._customer_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<CustomerDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._customer_NewObjectId;			
				}

				existing_customer = (CustomerDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_customer ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Customer", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "SlamcoreDeviceDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_customer_NewObjectId != valueToSet.InternalObjectId)
					{
						_customer_NewObjectId = valueToSet.InternalObjectId;
						_customerId = valueToSet.Id;
						OnPropertyChanged("CustomerId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_customerId != valueToSet.Id)
					{
						_customer_NewObjectId = null;

						_customerId = valueToSet.Id;
						OnPropertyChanged("CustomerId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_customerId = Guid.Empty;
				OnPropertyChanged("CustomerId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_customer ,valueToSet))
				OnPropertyChanged("Customer", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __customerSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Customer", which is a CustomerDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a CustomerDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<CustomerDataObject> LoadCustomerAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<CustomerDataObject> LoadCustomerAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerAlreadyLazyLoaded || forceReload)
                {
								
					CustomerDataObject customer = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __customerAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						customer = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId);
						customer.IsNew = false;
						customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
						if (customer != null)
						{
							return customer;
						}
					}

					customer = await _customerService.GetAsync(_serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId), parameters : parameters, skipSecurity: skipSecurity);

					SetCustomerValue(customer, false, false);
					__customerAlreadyLazyLoaded = true;				
		
					customer = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId);
					customer.IsNew = false;
					customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
                    __customerAlreadyLazyLoaded = true;
                }

                return await GetCustomerAsync(false);
            }
            finally
            {
                __customerSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual CustomerDataObject Customer 
		{
			get
			{			
				return GetCustomerAsync(true).Result;
			}
			set
			{
				SetCustomerValue(value);
			}
		}
		
		public virtual bool ShouldSerializeCustomer()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreDeviceDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreDeviceDataObject"].Contains("Customer");
		}

		public virtual async Task<CustomerDataObject> GetCustomerAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			CustomerDataObject customer;

				
			if (_customer_NewObjectId != null)
			{
				customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
				customer.IsNew = true;
				customer.InternalObjectId = _customer_NewObjectId;
				customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
			}
			else
			{
				customer = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(this.CustomerId);
				customer.IsNew = false;
				customer = (CustomerDataObject)ObjectsDataSet.GetObject(customer);
				
				if (allowLazyLoading && customer == null && LazyLoadingEnabled && (!__customerAlreadyLazyLoaded || forceReload))
				{
					customer = await LoadCustomerAsync(forceReload : forceReload);
				}
			}
				
			return customer;
		}

		public virtual System.Guid CustomerForeignKey
		{
			get { return CustomerId; }
			set 
			{	
				CustomerId = value;
			}
			
		}
		

		protected IDataProvider<SlamcoreAPIKeyDataObject> _slamcoreAPIKeyService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreAPIKeyDataObject>>();
      public virtual void SetSlamcoreAPIKeyValue(SlamcoreAPIKeyDataObject valueToSet)
		{
			SetSlamcoreAPIKeyValue(valueToSet, true, true);
		}

        public virtual void SetSlamcoreAPIKeyValue(SlamcoreAPIKeyDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			SlamcoreAPIKeyDataObject existing_slamcoreAPIKey = null ;

			if ( !(this.SlamcoreAPIKeyId == null || ObjectsDataSet == null))
			{
				SlamcoreAPIKeyDataObject key;

				if (this._slamcoreAPIKey_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>().Initialize((System.Guid)this.SlamcoreAPIKeyId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._slamcoreAPIKey_NewObjectId;			
				}

				existing_slamcoreAPIKey = (SlamcoreAPIKeyDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_slamcoreAPIKey ,valueToSet))
            {
                if (valueToSet == null)
                {
					_slamcoreAPIKey_NewObjectId = null;
					_slamcoreAPIKeyId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("SlamcoreAPIKey", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "SlamcoreDeviceDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_slamcoreAPIKey_NewObjectId != valueToSet.InternalObjectId)
					{
						_slamcoreAPIKey_NewObjectId = valueToSet.InternalObjectId;
						_slamcoreAPIKeyId = valueToSet.Id;
						OnPropertyChanged("SlamcoreAPIKeyId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_slamcoreAPIKeyId != valueToSet.Id)
					{
						_slamcoreAPIKey_NewObjectId = null;

						_slamcoreAPIKeyId = valueToSet.Id;
						OnPropertyChanged("SlamcoreAPIKeyId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_slamcoreAPIKey_NewObjectId = null;
					_slamcoreAPIKeyId = null;
					
				OnPropertyChanged("SlamcoreAPIKeyId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_slamcoreAPIKey ,valueToSet))
				OnPropertyChanged("SlamcoreAPIKey", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __slamcoreAPIKeySemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreAPIKeyAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreAPIKey", which is a SlamcoreAPIKeyDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SlamcoreAPIKeyDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SlamcoreAPIKeyDataObject> LoadSlamcoreAPIKeyAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreAPIKeyAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SlamcoreAPIKeyDataObject> LoadSlamcoreAPIKeyAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreAPIKeySemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreAPIKeyAlreadyLazyLoaded || forceReload)
                {
								
					if (this.SlamcoreAPIKeyId == null)
					{
						return null;
					}
				
					SlamcoreAPIKeyDataObject slamcoreAPIKey = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __slamcoreAPIKeyAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						slamcoreAPIKey = _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>().Initialize((System.Guid)this.SlamcoreAPIKeyId);
						slamcoreAPIKey.IsNew = false;
						slamcoreAPIKey = (SlamcoreAPIKeyDataObject)ObjectsDataSet.GetObject(slamcoreAPIKey);
						if (slamcoreAPIKey != null)
						{
							return slamcoreAPIKey;
						}
					}

					slamcoreAPIKey = await _slamcoreAPIKeyService.GetAsync(_serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>().Initialize((System.Guid)this.SlamcoreAPIKeyId), parameters : parameters, skipSecurity: skipSecurity);

					SetSlamcoreAPIKeyValue(slamcoreAPIKey, false, false);
					__slamcoreAPIKeyAlreadyLazyLoaded = true;				
		
					slamcoreAPIKey = _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>().Initialize((System.Guid)this.SlamcoreAPIKeyId);
					slamcoreAPIKey.IsNew = false;
					slamcoreAPIKey = (SlamcoreAPIKeyDataObject)ObjectsDataSet.GetObject(slamcoreAPIKey);
                    __slamcoreAPIKeyAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreAPIKeyAsync(false);
            }
            finally
            {
                __slamcoreAPIKeySemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SlamcoreAPIKeyDataObject SlamcoreAPIKey 
		{
			get
			{			
				return GetSlamcoreAPIKeyAsync(true).Result;
			}
			set
			{
				SetSlamcoreAPIKeyValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreAPIKey()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreDeviceDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreDeviceDataObject"].Contains("SlamcoreAPIKey");
		}

		public virtual async Task<SlamcoreAPIKeyDataObject> GetSlamcoreAPIKeyAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SlamcoreAPIKeyDataObject slamcoreAPIKey;

				
			if (_slamcoreAPIKey_NewObjectId != null)
			{
				slamcoreAPIKey = _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>();
				slamcoreAPIKey.IsNew = true;
				slamcoreAPIKey.InternalObjectId = _slamcoreAPIKey_NewObjectId;
				slamcoreAPIKey = (SlamcoreAPIKeyDataObject)ObjectsDataSet.GetObject(slamcoreAPIKey);
			}
			else
			{
				if (this.SlamcoreAPIKeyId == null)
					return null;
				if (SlamcoreAPIKeyId == null)
					slamcoreAPIKey = null;
				else
				slamcoreAPIKey = _serviceProvider.GetRequiredService<SlamcoreAPIKeyDataObject>().Initialize((System.Guid)this.SlamcoreAPIKeyId);
				slamcoreAPIKey.IsNew = false;
				slamcoreAPIKey = (SlamcoreAPIKeyDataObject)ObjectsDataSet.GetObject(slamcoreAPIKey);
				
				if (allowLazyLoading && slamcoreAPIKey == null && LazyLoadingEnabled && (!__slamcoreAPIKeyAlreadyLazyLoaded || forceReload))
				{
					slamcoreAPIKey = await LoadSlamcoreAPIKeyAsync(forceReload : forceReload);
				}
			}
				
			return slamcoreAPIKey;
		}

		public virtual Nullable<System.Guid> SlamcoreAPIKeyForeignKey
		{
			get { return SlamcoreAPIKeyId; }
			set 
			{	
				SlamcoreAPIKeyId = value;
			}
			
		}
		

		protected IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject> _slamcoreAwareAuthenticationDetailsService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject>>();
      public virtual void SetSlamcoreAwareAuthenticationDetailsValue(SlamcoreAwareAuthenticationDetailsDataObject valueToSet)
		{
			SetSlamcoreAwareAuthenticationDetailsValue(valueToSet, true, true);
		}

        public virtual void SetSlamcoreAwareAuthenticationDetailsValue(SlamcoreAwareAuthenticationDetailsDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			SlamcoreAwareAuthenticationDetailsDataObject existing_slamcoreAwareAuthenticationDetails = null ;

			if ( !(this.SlamcoreAwareAuthenticationDetailsId == null || ObjectsDataSet == null))
			{
				SlamcoreAwareAuthenticationDetailsDataObject key;

				if (this._slamcoreAwareAuthenticationDetails_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsDataObject>().Initialize((System.Guid)this.SlamcoreAwareAuthenticationDetailsId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._slamcoreAwareAuthenticationDetails_NewObjectId;			
				}

				existing_slamcoreAwareAuthenticationDetails = (SlamcoreAwareAuthenticationDetailsDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_slamcoreAwareAuthenticationDetails ,valueToSet))
            {
                if (valueToSet == null)
                {
					_slamcoreAwareAuthenticationDetails_NewObjectId = null;
					_slamcoreAwareAuthenticationDetailsId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("SlamcoreAwareAuthenticationDetails", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "SlamcoreDeviceDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_slamcoreAwareAuthenticationDetails_NewObjectId != valueToSet.InternalObjectId)
					{
						_slamcoreAwareAuthenticationDetails_NewObjectId = valueToSet.InternalObjectId;
						_slamcoreAwareAuthenticationDetailsId = valueToSet.Id;
						OnPropertyChanged("SlamcoreAwareAuthenticationDetailsId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_slamcoreAwareAuthenticationDetailsId != valueToSet.Id)
					{
						_slamcoreAwareAuthenticationDetails_NewObjectId = null;

						_slamcoreAwareAuthenticationDetailsId = valueToSet.Id;
						OnPropertyChanged("SlamcoreAwareAuthenticationDetailsId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_slamcoreAwareAuthenticationDetails_NewObjectId = null;
					_slamcoreAwareAuthenticationDetailsId = null;
					
				OnPropertyChanged("SlamcoreAwareAuthenticationDetailsId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_slamcoreAwareAuthenticationDetails ,valueToSet))
				OnPropertyChanged("SlamcoreAwareAuthenticationDetails", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __slamcoreAwareAuthenticationDetailsSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreAwareAuthenticationDetailsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreAwareAuthenticationDetails", which is a SlamcoreAwareAuthenticationDetailsDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a SlamcoreAwareAuthenticationDetailsDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<SlamcoreAwareAuthenticationDetailsDataObject> LoadSlamcoreAwareAuthenticationDetailsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreAwareAuthenticationDetailsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<SlamcoreAwareAuthenticationDetailsDataObject> LoadSlamcoreAwareAuthenticationDetailsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreAwareAuthenticationDetailsSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreAwareAuthenticationDetailsAlreadyLazyLoaded || forceReload)
                {
								
					if (this.SlamcoreAwareAuthenticationDetailsId == null)
					{
						return null;
					}
				
					SlamcoreAwareAuthenticationDetailsDataObject slamcoreAwareAuthenticationDetails = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __slamcoreAwareAuthenticationDetailsAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						slamcoreAwareAuthenticationDetails = _serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsDataObject>().Initialize((System.Guid)this.SlamcoreAwareAuthenticationDetailsId);
						slamcoreAwareAuthenticationDetails.IsNew = false;
						slamcoreAwareAuthenticationDetails = (SlamcoreAwareAuthenticationDetailsDataObject)ObjectsDataSet.GetObject(slamcoreAwareAuthenticationDetails);
						if (slamcoreAwareAuthenticationDetails != null)
						{
							return slamcoreAwareAuthenticationDetails;
						}
					}

					slamcoreAwareAuthenticationDetails = await _slamcoreAwareAuthenticationDetailsService.GetAsync(_serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsDataObject>().Initialize((System.Guid)this.SlamcoreAwareAuthenticationDetailsId), parameters : parameters, skipSecurity: skipSecurity);

					SetSlamcoreAwareAuthenticationDetailsValue(slamcoreAwareAuthenticationDetails, false, false);
					__slamcoreAwareAuthenticationDetailsAlreadyLazyLoaded = true;				
		
					slamcoreAwareAuthenticationDetails = _serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsDataObject>().Initialize((System.Guid)this.SlamcoreAwareAuthenticationDetailsId);
					slamcoreAwareAuthenticationDetails.IsNew = false;
					slamcoreAwareAuthenticationDetails = (SlamcoreAwareAuthenticationDetailsDataObject)ObjectsDataSet.GetObject(slamcoreAwareAuthenticationDetails);
                    __slamcoreAwareAuthenticationDetailsAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreAwareAuthenticationDetailsAsync(false);
            }
            finally
            {
                __slamcoreAwareAuthenticationDetailsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual SlamcoreAwareAuthenticationDetailsDataObject SlamcoreAwareAuthenticationDetails 
		{
			get
			{			
				return GetSlamcoreAwareAuthenticationDetailsAsync(true).Result;
			}
			set
			{
				SetSlamcoreAwareAuthenticationDetailsValue(value);
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreAwareAuthenticationDetails()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreDeviceDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreDeviceDataObject"].Contains("SlamcoreAwareAuthenticationDetails");
		}

		public virtual async Task<SlamcoreAwareAuthenticationDetailsDataObject> GetSlamcoreAwareAuthenticationDetailsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			SlamcoreAwareAuthenticationDetailsDataObject slamcoreAwareAuthenticationDetails;

				
			if (_slamcoreAwareAuthenticationDetails_NewObjectId != null)
			{
				slamcoreAwareAuthenticationDetails = _serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsDataObject>();
				slamcoreAwareAuthenticationDetails.IsNew = true;
				slamcoreAwareAuthenticationDetails.InternalObjectId = _slamcoreAwareAuthenticationDetails_NewObjectId;
				slamcoreAwareAuthenticationDetails = (SlamcoreAwareAuthenticationDetailsDataObject)ObjectsDataSet.GetObject(slamcoreAwareAuthenticationDetails);
			}
			else
			{
				if (this.SlamcoreAwareAuthenticationDetailsId == null)
					return null;
				if (SlamcoreAwareAuthenticationDetailsId == null)
					slamcoreAwareAuthenticationDetails = null;
				else
				slamcoreAwareAuthenticationDetails = _serviceProvider.GetRequiredService<SlamcoreAwareAuthenticationDetailsDataObject>().Initialize((System.Guid)this.SlamcoreAwareAuthenticationDetailsId);
				slamcoreAwareAuthenticationDetails.IsNew = false;
				slamcoreAwareAuthenticationDetails = (SlamcoreAwareAuthenticationDetailsDataObject)ObjectsDataSet.GetObject(slamcoreAwareAuthenticationDetails);
				
				if (allowLazyLoading && slamcoreAwareAuthenticationDetails == null && LazyLoadingEnabled && (!__slamcoreAwareAuthenticationDetailsAlreadyLazyLoaded || forceReload))
				{
					slamcoreAwareAuthenticationDetails = await LoadSlamcoreAwareAuthenticationDetailsAsync(forceReload : forceReload);
				}
			}
				
			return slamcoreAwareAuthenticationDetails;
		}

		public virtual Nullable<System.Guid> SlamcoreAwareAuthenticationDetailsForeignKey
		{
			get { return SlamcoreAwareAuthenticationDetailsId; }
			set 
			{	
				SlamcoreAwareAuthenticationDetailsId = value;
			}
			
		}
		

		protected IDataProvider<SlamcoreDeviceConnectionViewDataObject> _slamcoreDeviceConnectionViewService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceConnectionViewDataObject>>();

		private readonly SemaphoreSlim __slamcoreDeviceConnectionViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreDeviceConnectionViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreDeviceConnectionViewItems", which is a collection of SlamcoreDeviceConnectionViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SlamcoreDeviceConnectionViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SlamcoreDeviceConnectionViewDataObject>> LoadSlamcoreDeviceConnectionViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreDeviceConnectionViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SlamcoreDeviceConnectionViewDataObject>> LoadSlamcoreDeviceConnectionViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreDeviceConnectionViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreDeviceConnectionViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SlamcoreDeviceId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _slamcoreDeviceConnectionViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __slamcoreDeviceConnectionViewItemsAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreDeviceConnectionViewItemsAsync(false);
            }
            finally
            {
                __slamcoreDeviceConnectionViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SlamcoreDeviceConnectionViewDataObject> SlamcoreDeviceConnectionViewItems 
		{
			get
			{			
				return GetSlamcoreDeviceConnectionViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreDeviceConnectionViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreDeviceDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreDeviceDataObject"].Contains("SlamcoreDeviceConnectionViewItems");
		}

		public virtual async Task<DataObjectCollection<SlamcoreDeviceConnectionViewDataObject>> GetSlamcoreDeviceConnectionViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__slamcoreDeviceConnectionViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadSlamcoreDeviceConnectionViewItemsAsync(forceReload : forceReload);
			}
			var slamcoreDeviceConnectionViewItems = ObjectsDataSet.GetRelatedObjects<SlamcoreDeviceConnectionViewDataObject>(this, "SlamcoreDeviceConnectionViewItems");							
			slamcoreDeviceConnectionViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(SlamcoreDeviceConnectionViewItems_CollectionChanged);
				
			return slamcoreDeviceConnectionViewItems;
		}

        private void SlamcoreDeviceConnectionViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SlamcoreDeviceConnectionViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : SlamcoreDeviceConnectionView", "SlamcoreDeviceDataObject.SlamcoreDeviceConnectionViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SlamcoreDeviceDataObject throw an exception while trying to add SlamcoreDeviceConnectionViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._slamcoreDevice_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SlamcoreDeviceId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SlamcoreDeviceId == default(Nullable<System.Guid>))
							relatedObj.SlamcoreDeviceId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SlamcoreDeviceConnectionViewDataObject).SlamcoreDevice = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<SlamcoreDeviceHistoryDataObject> _slamcoreDeviceHistoryService => _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceHistoryDataObject>>();

		private readonly SemaphoreSlim __slamcoreDeviceHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcoreDeviceHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcoreDeviceHistoryItems", which is a collection of SlamcoreDeviceHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SlamcoreDeviceHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SlamcoreDeviceHistoryDataObject>> LoadSlamcoreDeviceHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcoreDeviceHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SlamcoreDeviceHistoryDataObject>> LoadSlamcoreDeviceHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcoreDeviceHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcoreDeviceHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SlamcoreDeviceId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _slamcoreDeviceHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __slamcoreDeviceHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetSlamcoreDeviceHistoryItemsAsync(false);
            }
            finally
            {
                __slamcoreDeviceHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SlamcoreDeviceHistoryDataObject> SlamcoreDeviceHistoryItems 
		{
			get
			{			
				return GetSlamcoreDeviceHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSlamcoreDeviceHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreDeviceDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreDeviceDataObject"].Contains("SlamcoreDeviceHistoryItems");
		}

		public virtual async Task<DataObjectCollection<SlamcoreDeviceHistoryDataObject>> GetSlamcoreDeviceHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__slamcoreDeviceHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadSlamcoreDeviceHistoryItemsAsync(forceReload : forceReload);
			}
			var slamcoreDeviceHistoryItems = ObjectsDataSet.GetRelatedObjects<SlamcoreDeviceHistoryDataObject>(this, "SlamcoreDeviceHistoryItems");							
			slamcoreDeviceHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(SlamcoreDeviceHistoryItems_CollectionChanged);
				
			return slamcoreDeviceHistoryItems;
		}

        private void SlamcoreDeviceHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SlamcoreDeviceHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : SlamcoreDeviceHistory", "SlamcoreDeviceDataObject.SlamcoreDeviceHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SlamcoreDeviceDataObject throw an exception while trying to add SlamcoreDeviceHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._slamcoreDevice_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SlamcoreDeviceId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SlamcoreDeviceId == default(System.Guid))
							relatedObj.SlamcoreDeviceId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SlamcoreDeviceHistoryDataObject).SlamcoreDevice = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<SlamcorePedestrianDetectionDataObject> _slamcorePedestrianDetectionService => _serviceProvider.GetRequiredService<IDataProvider<SlamcorePedestrianDetectionDataObject>>();

		private readonly SemaphoreSlim __slamcorePedestrianDetectionItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __slamcorePedestrianDetectionItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SlamcorePedestrianDetectionItems", which is a collection of SlamcorePedestrianDetectionDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SlamcorePedestrianDetectionDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SlamcorePedestrianDetectionDataObject>> LoadSlamcorePedestrianDetectionItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSlamcorePedestrianDetectionItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SlamcorePedestrianDetectionDataObject>> LoadSlamcorePedestrianDetectionItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __slamcorePedestrianDetectionItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__slamcorePedestrianDetectionItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SlamcoreDeviceId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _slamcorePedestrianDetectionService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __slamcorePedestrianDetectionItemsAlreadyLazyLoaded = true;
                }

                return await GetSlamcorePedestrianDetectionItemsAsync(false);
            }
            finally
            {
                __slamcorePedestrianDetectionItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SlamcorePedestrianDetectionDataObject> SlamcorePedestrianDetectionItems 
		{
			get
			{			
				return GetSlamcorePedestrianDetectionItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSlamcorePedestrianDetectionItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreDeviceDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreDeviceDataObject"].Contains("SlamcorePedestrianDetectionItems");
		}

		public virtual async Task<DataObjectCollection<SlamcorePedestrianDetectionDataObject>> GetSlamcorePedestrianDetectionItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__slamcorePedestrianDetectionItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadSlamcorePedestrianDetectionItemsAsync(forceReload : forceReload);
			}
			var slamcorePedestrianDetectionItems = ObjectsDataSet.GetRelatedObjects<SlamcorePedestrianDetectionDataObject>(this, "SlamcorePedestrianDetectionItems");							
			slamcorePedestrianDetectionItems.CollectionChanged += new NotifyCollectionChangedEventHandler(SlamcorePedestrianDetectionItems_CollectionChanged);
				
			return slamcorePedestrianDetectionItems;
		}

        private void SlamcorePedestrianDetectionItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SlamcorePedestrianDetectionDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : SlamcorePedestrianDetection", "SlamcoreDeviceDataObject.SlamcorePedestrianDetectionItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SlamcoreDeviceDataObject throw an exception while trying to add SlamcorePedestrianDetectionDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._slamcoreDevice_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SlamcoreDeviceId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SlamcoreDeviceId == default(System.Guid))
							relatedObj.SlamcoreDeviceId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SlamcorePedestrianDetectionDataObject).SlamcoreDevice = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleDataObject> _vehicleService => _serviceProvider.GetRequiredService<IDataProvider<VehicleDataObject>>();
      public virtual void SetVehicleValue(VehicleDataObject valueToSet)
		{
			SetVehicleValue(valueToSet, true, true);
		}

        public virtual void SetVehicleValue(VehicleDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			VehicleDataObject existing_vehicle = null ;

			if ( !(this.VehicleId == null || ObjectsDataSet == null))
			{
				VehicleDataObject key;

				if (this._vehicle_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<VehicleDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._vehicle_NewObjectId;			
				}

				existing_vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_vehicle ,valueToSet))
            {
                if (valueToSet == null)
                {
					_vehicle_NewObjectId = null;
					_vehicleId = null;
				}
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Vehicle", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "SlamcoreDeviceDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_vehicle_NewObjectId != valueToSet.InternalObjectId)
					{
						_vehicle_NewObjectId = valueToSet.InternalObjectId;
						_vehicleId = valueToSet.Id;
						OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_vehicleId != valueToSet.Id)
					{
						_vehicle_NewObjectId = null;

						_vehicleId = valueToSet.Id;
						OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
					_vehicle_NewObjectId = null;
					_vehicleId = null;
					
				OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_vehicle ,valueToSet))
				OnPropertyChanged("Vehicle", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __vehicleSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Vehicle", which is a VehicleDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a VehicleDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<VehicleDataObject> LoadVehicleAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<VehicleDataObject> LoadVehicleAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleAlreadyLazyLoaded || forceReload)
                {
								
					if (this.VehicleId == null)
					{
						return null;
					}
				
					VehicleDataObject vehicle = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __vehicleAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId);
						vehicle.IsNew = false;
						vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
						if (vehicle != null)
						{
							return vehicle;
						}
					}

					vehicle = await _vehicleService.GetAsync(_serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId), parameters : parameters, skipSecurity: skipSecurity);

					SetVehicleValue(vehicle, false, false);
					__vehicleAlreadyLazyLoaded = true;				
		
					vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId);
					vehicle.IsNew = false;
					vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
                    __vehicleAlreadyLazyLoaded = true;
                }

                return await GetVehicleAsync(false);
            }
            finally
            {
                __vehicleSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual VehicleDataObject Vehicle 
		{
			get
			{			
				return GetVehicleAsync(true).Result;
			}
			set
			{
				SetVehicleValue(value);
			}
		}
		
		public virtual bool ShouldSerializeVehicle()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreDeviceDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreDeviceDataObject"].Contains("Vehicle");
		}

		public virtual async Task<VehicleDataObject> GetVehicleAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			VehicleDataObject vehicle;

				
			if (_vehicle_NewObjectId != null)
			{
				vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
				vehicle.IsNew = true;
				vehicle.InternalObjectId = _vehicle_NewObjectId;
				vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
			}
			else
			{
				if (this.VehicleId == null)
					return null;
				if (VehicleId == null)
					vehicle = null;
				else
				vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize((System.Guid)this.VehicleId);
				vehicle.IsNew = false;
				vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
				
				if (allowLazyLoading && vehicle == null && LazyLoadingEnabled && (!__vehicleAlreadyLazyLoaded || forceReload))
				{
					vehicle = await LoadVehicleAsync(forceReload : forceReload);
				}
			}
				
			return vehicle;
		}

		public virtual Nullable<System.Guid> VehicleForeignKey
		{
			get { return VehicleId; }
			set 
			{	
				VehicleId = value;
			}
			
		}
		

		protected IDataProvider<VehicleSlamcoreLocationHistoryDataObject> _vehicleSlamcoreLocationHistoryService => _serviceProvider.GetRequiredService<IDataProvider<VehicleSlamcoreLocationHistoryDataObject>>();

		private readonly SemaphoreSlim __vehicleSlamcoreLocationHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleSlamcoreLocationHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleSlamcoreLocationHistoryItems", which is a collection of VehicleSlamcoreLocationHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleSlamcoreLocationHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleSlamcoreLocationHistoryDataObject>> LoadVehicleSlamcoreLocationHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleSlamcoreLocationHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleSlamcoreLocationHistoryDataObject>> LoadVehicleSlamcoreLocationHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleSlamcoreLocationHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleSlamcoreLocationHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SlamcoreDeviceId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleSlamcoreLocationHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleSlamcoreLocationHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleSlamcoreLocationHistoryItemsAsync(false);
            }
            finally
            {
                __vehicleSlamcoreLocationHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleSlamcoreLocationHistoryDataObject> VehicleSlamcoreLocationHistoryItems 
		{
			get
			{			
				return GetVehicleSlamcoreLocationHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleSlamcoreLocationHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SlamcoreDeviceDataObject") && ObjectsDataSet.RelationsToInclude["SlamcoreDeviceDataObject"].Contains("VehicleSlamcoreLocationHistoryItems");
		}

		public virtual async Task<DataObjectCollection<VehicleSlamcoreLocationHistoryDataObject>> GetVehicleSlamcoreLocationHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleSlamcoreLocationHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleSlamcoreLocationHistoryItemsAsync(forceReload : forceReload);
			}
			var vehicleSlamcoreLocationHistoryItems = ObjectsDataSet.GetRelatedObjects<VehicleSlamcoreLocationHistoryDataObject>(this, "VehicleSlamcoreLocationHistoryItems");							
			vehicleSlamcoreLocationHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleSlamcoreLocationHistoryItems_CollectionChanged);
				
			return vehicleSlamcoreLocationHistoryItems;
		}

        private void VehicleSlamcoreLocationHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleSlamcoreLocationHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleSlamcoreLocationHistory", "SlamcoreDeviceDataObject.VehicleSlamcoreLocationHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SlamcoreDeviceDataObject throw an exception while trying to add VehicleSlamcoreLocationHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._slamcoreDevice_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SlamcoreDeviceId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SlamcoreDeviceId == default(System.Guid))
							relatedObj.SlamcoreDeviceId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleSlamcoreLocationHistoryDataObject).SlamcoreDevice = null;
                    // }
                    break;
            }            
        }

		public override void ClearLazyLoadFlags()
		{
			__slamcoreDeviceConnectionViewItemsAlreadyLazyLoaded = false;
			__slamcoreDeviceHistoryItemsAlreadyLazyLoaded = false;
			__slamcorePedestrianDetectionItemsAlreadyLazyLoaded = false;
			__vehicleSlamcoreLocationHistoryItemsAlreadyLazyLoaded = false;
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadCustomerAsync()) != null)
				result.Add(Customer);
			if ((await LoadSlamcoreAPIKeyAsync()) != null)
				result.Add(SlamcoreAPIKey);
			if ((await LoadSlamcoreAwareAuthenticationDetailsAsync()) != null)
				result.Add(SlamcoreAwareAuthenticationDetails);
			if ((await LoadVehicleAsync()) != null)
				result.Add(Vehicle);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadSlamcoreDeviceConnectionViewItemsAsync()) != null)
				result.AddRange(SlamcoreDeviceConnectionViewItems);
			if ((await LoadSlamcoreDeviceHistoryItemsAsync()) != null)
				result.AddRange(SlamcoreDeviceHistoryItems);
			if ((await LoadSlamcorePedestrianDetectionItemsAsync()) != null)
				result.AddRange(SlamcorePedestrianDetectionItems);
			if ((await LoadVehicleSlamcoreLocationHistoryItemsAsync()) != null)
				result.AddRange(VehicleSlamcoreLocationHistoryItems);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				SlamcoreAwareAuthenticationDetails == other ||
				(other is SlamcoreAwareAuthenticationDetailsDataObject && (SlamcoreAwareAuthenticationDetailsId != default(Nullable<System.Guid>)) && (SlamcoreAwareAuthenticationDetailsId == (other as SlamcoreAwareAuthenticationDetailsDataObject).Id)) || 
				Vehicle == other ||
				(other is VehicleDataObject && (VehicleId != default(Nullable<System.Guid>)) && (VehicleId == (other as VehicleDataObject).Id)) || 
				SlamcoreAPIKey == other ||
				(other is SlamcoreAPIKeyDataObject && (SlamcoreAPIKeyId != default(Nullable<System.Guid>)) && (SlamcoreAPIKeyId == (other as SlamcoreAPIKeyDataObject).Id)) || 
				Customer == other ||
				(other is CustomerDataObject && (CustomerId != default(System.Guid)) && (CustomerId == (other as CustomerDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetCustomerIdValue(System.Guid valueToSet)
		{
			SetCustomerIdValue(valueToSet, true, true);
		}

		public virtual void SetCustomerIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_customerId != valueToSet)
			{
				_customerId = valueToSet;

				// CustomerId is a FK. Setting its value should result in a event
				OnPropertyChanged("Customer", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("CustomerId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The CustomerId property of the SlamcoreDevice DataObject</summary>
        public virtual System.Guid CustomerId 
		{
			get	{ return _customerId;}
			
			
			set
			{
				SetCustomerIdValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the SlamcoreDevice DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetIPAddressValue(System.String valueToSet)
		{
			SetIPAddressValue(valueToSet, true, true);
		}

		public virtual void SetIPAddressValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_iPAddress != valueToSet)
			{
				_iPAddress = valueToSet;

				OnPropertyChanged("IPAddress", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The IPAddress property of the SlamcoreDevice DataObject</summary>
        public virtual System.String IPAddress 
		{
			get	{ return _iPAddress; }
			
			
			set
			{
				SetIPAddressValue(value);
			}
		}		
			
			
		public virtual void SetLastConnectedDateTimeValue(Nullable<System.DateTime> valueToSet)
		{
			SetLastConnectedDateTimeValue(valueToSet, true, true);
		}

		public virtual void SetLastConnectedDateTimeValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_lastConnectedDateTime != null)
				{
					_lastConnectedDateTime = null;
					OnPropertyChanged("LastConnectedDateTime", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_lastConnectedDateTime != DateTime.MinValue.ToUniversalTime())
				{
					_lastConnectedDateTime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("LastConnectedDateTime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_lastConnectedDateTime != DateTime.MaxValue.ToUniversalTime())
				{
					_lastConnectedDateTime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("LastConnectedDateTime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_lastConnectedDateTime != valueToSet ||
                (_lastConnectedDateTime != null && ((DateTime)_lastConnectedDateTime).Kind == DateTimeKind.Unspecified))
			{
				_lastConnectedDateTime = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("LastConnectedDateTime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The LastConnectedDateTime property of the SlamcoreDevice DataObject</summary>
        public virtual Nullable<System.DateTime> LastConnectedDateTime 
		{
			get	{ return _lastConnectedDateTime;}
			
			
			set
			{
				SetLastConnectedDateTimeValue(value);
			}
		}		
			
			
		public virtual void SetNameValue(System.String valueToSet)
		{
			SetNameValue(valueToSet, true, true);
		}

		public virtual void SetNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_name != valueToSet)
			{
				_name = valueToSet;

				OnPropertyChanged("Name", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Name property of the SlamcoreDevice DataObject</summary>
        public virtual System.String Name 
		{
			get	{ return _name; }
			
			
			set
			{
				SetNameValue(value);
			}
		}		
			
			
		public virtual void SetSerialNoValue(System.String valueToSet)
		{
			SetSerialNoValue(valueToSet, true, true);
		}

		public virtual void SetSerialNoValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_serialNo != valueToSet)
			{
				_serialNo = valueToSet;

				OnPropertyChanged("SerialNo", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SerialNo property of the SlamcoreDevice DataObject</summary>
        public virtual System.String SerialNo 
		{
			get	{ return _serialNo; }
			
			
			set
			{
				SetSerialNoValue(value);
			}
		}		
			
			
		public virtual void SetSlamcoreAPIKeyIdValue(Nullable<System.Guid> valueToSet)
		{
			SetSlamcoreAPIKeyIdValue(valueToSet, true, true);
		}

		public virtual void SetSlamcoreAPIKeyIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_slamcoreAPIKeyId != valueToSet)
			{
				_slamcoreAPIKeyId = valueToSet;

				// SlamcoreAPIKeyId is a FK. Setting its value should result in a event
				OnPropertyChanged("SlamcoreAPIKey", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("SlamcoreAPIKeyId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SlamcoreAPIKeyId property of the SlamcoreDevice DataObject</summary>
        public virtual Nullable<System.Guid> SlamcoreAPIKeyId 
		{
			get	{ return _slamcoreAPIKeyId;}
			
			
			set
			{
				SetSlamcoreAPIKeyIdValue(value);
			}
		}		
			
			
		public virtual void SetSlamcoreAwareAuthenticationDetailsIdValue(Nullable<System.Guid> valueToSet)
		{
			SetSlamcoreAwareAuthenticationDetailsIdValue(valueToSet, true, true);
		}

		public virtual void SetSlamcoreAwareAuthenticationDetailsIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_slamcoreAwareAuthenticationDetailsId != valueToSet)
			{
				_slamcoreAwareAuthenticationDetailsId = valueToSet;

				// SlamcoreAwareAuthenticationDetailsId is a FK. Setting its value should result in a event
				OnPropertyChanged("SlamcoreAwareAuthenticationDetails", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("SlamcoreAwareAuthenticationDetailsId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The SlamcoreAwareAuthenticationDetailsId property of the SlamcoreDevice DataObject</summary>
        public virtual Nullable<System.Guid> SlamcoreAwareAuthenticationDetailsId 
		{
			get	{ return _slamcoreAwareAuthenticationDetailsId;}
			
			
			set
			{
				SetSlamcoreAwareAuthenticationDetailsIdValue(value);
			}
		}		
			
			
		public virtual void SetStatusValue(SlamcoreStatusEnum valueToSet)
		{
			SetStatusValue(valueToSet, true, true);
		}

		public virtual void SetStatusValue(SlamcoreStatusEnum valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_status != valueToSet)
			{
				_status = valueToSet;

				OnPropertyChanged("Status", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("StatusDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Status property of the SlamcoreDevice DataObject</summary>
        public virtual SlamcoreStatusEnum Status 
		{
			get	{ return _status;}
			
			
			set
			{
				SetStatusValue(value);
			}
		}		
      public virtual string StatusDisplayString
		{
			get
			{
				return StatusEnumDisplayNameCollection.Where(v => v.Value == Status).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<SlamcoreStatusEnum>> StatusEnumDisplayNameCollection
	    {
	        get
	        {
                return SlamcoreStatusEnumDisplayNames.Items;
	        }
	    }
		
			
			
		public virtual void SetUpdateRateValue(SlamcoreUpdateRateEnum valueToSet)
		{
			SetUpdateRateValue(valueToSet, true, true);
		}

		public virtual void SetUpdateRateValue(SlamcoreUpdateRateEnum valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_updateRate != valueToSet)
			{
				_updateRate = valueToSet;

				OnPropertyChanged("UpdateRate", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("UpdateRateDisplayString", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The UpdateRate property of the SlamcoreDevice DataObject</summary>
        public virtual SlamcoreUpdateRateEnum UpdateRate 
		{
			get	{ return _updateRate;}
			
			
			set
			{
				SetUpdateRateValue(value);
			}
		}		
      public virtual string UpdateRateDisplayString
		{
			get
			{
				return UpdateRateEnumDisplayNameCollection.Where(v => v.Value == UpdateRate).Single().DisplayString;            
			}
		}

	    public virtual List<EnumDisplayString<SlamcoreUpdateRateEnum>> UpdateRateEnumDisplayNameCollection
	    {
	        get
	        {
                return SlamcoreUpdateRateEnumDisplayNames.Items;
	        }
	    }
		
			
		
		/// <summary> The UpdateRateDisplay property of the SlamcoreDevice DataObject</summary>
        public virtual System.String UpdateRateDisplay 
		{
			get	
			{ 
				if (!AreCalculationsEnabled)
					return default(System.String);

				return ((UpdateRate == FleetXQ.Data.DataObjects.SlamcoreUpdateRateEnum.OneHert) ? "1Hz (Recommended)" : "");				
			}
			
		}		
			
			
		public virtual void SetVehicleIdValue(Nullable<System.Guid> valueToSet)
		{
			SetVehicleIdValue(valueToSet, true, true);
		}

		public virtual void SetVehicleIdValue(Nullable<System.Guid> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleId != valueToSet)
			{
				_vehicleId = valueToSet;

				// VehicleId is a FK. Setting its value should result in a event
				OnPropertyChanged("Vehicle", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("VehicleId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The VehicleId property of the SlamcoreDevice DataObject</summary>
        public virtual Nullable<System.Guid> VehicleId 
		{
			get	{ return _vehicleId;}
			
			
			set
			{
				SetVehicleIdValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			if (propertyName == "UpdateRate")
			{
				OnPropertyChanged("UpdateRateDisplay", true, dirtyHandlerOn);
			}

			
			// Push the notification to related objects
			var slamcoreAPIKey = GetSlamcoreAPIKeyAsync(false).Result;
			if (slamcoreAPIKey != null && this.IsDirty)
            {
				slamcoreAPIKey.NotifyPropertyChanged("SlamcoreDevice." + propertyName, callers);
			}
			var slamcoreAwareAuthenticationDetails = GetSlamcoreAwareAuthenticationDetailsAsync(false).Result;
			if (slamcoreAwareAuthenticationDetails != null && this.IsDirty)
            {
				slamcoreAwareAuthenticationDetails.NotifyPropertyChanged("SlamcoreDevice." + propertyName, callers);
			}
			var _slamcoreDeviceConnectionViewItems = GetSlamcoreDeviceConnectionViewItemsAsync(false).Result;
			if (_slamcoreDeviceConnectionViewItems != null)
            {
                foreach (var item in _slamcoreDeviceConnectionViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("SlamcoreDevice.", propertyName), callers);                    
                }
            }
			var _slamcoreDeviceHistoryItems = GetSlamcoreDeviceHistoryItemsAsync(false).Result;
			if (_slamcoreDeviceHistoryItems != null)
            {
                foreach (var item in _slamcoreDeviceHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("SlamcoreDevice.", propertyName), callers);                    
                }
            }
			var _slamcorePedestrianDetectionItems = GetSlamcorePedestrianDetectionItemsAsync(false).Result;
			if (_slamcorePedestrianDetectionItems != null)
            {
                foreach (var item in _slamcorePedestrianDetectionItems)
                {
                    item.NotifyPropertyChanged(String.Concat("SlamcoreDevice.", propertyName), callers);                    
                }
            }
			var vehicle = GetVehicleAsync(false).Result;
			if (vehicle != null && this.IsDirty)
            {
				vehicle.NotifyPropertyChanged("." + propertyName, callers);
			}
			var _vehicleSlamcoreLocationHistoryItems = GetVehicleSlamcoreLocationHistoryItemsAsync(false).Result;
			if (_vehicleSlamcoreLocationHistoryItems != null)
            {
                foreach (var item in _vehicleSlamcoreLocationHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("SlamcoreDevice.", propertyName), callers);                    
                }
            }
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<SlamcoreDeviceDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is SlamcoreDeviceDataObject))
				return false;

			var p = (SlamcoreDeviceDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.CustomerId == p.CustomerId;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.IPAddress == p.IPAddress;
			fieldsComparison &= this.LastConnectedDateTime == p.LastConnectedDateTime;
			fieldsComparison &= this.Name == p.Name;
			fieldsComparison &= this.SerialNo == p.SerialNo;
			fieldsComparison &= this.SlamcoreAPIKeyId == p.SlamcoreAPIKeyId;
			fieldsComparison &= this.SlamcoreAwareAuthenticationDetailsId == p.SlamcoreAwareAuthenticationDetailsId;
			fieldsComparison &= this.Status == p.Status;
			fieldsComparison &= this.UpdateRate == p.UpdateRate;
			fieldsComparison &= this.VehicleId == p.VehicleId;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// LastConnectedDateTime is a local datetime: Convert to UTC for server-side handling and storing
			if (this._lastConnectedDateTime_WithTimezoneOffset != null)
			{
				this.LastConnectedDateTime = ((DateTimeOffset)this._lastConnectedDateTime_WithTimezoneOffset).UtcDateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreDeviceCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public SlamcoreDeviceCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public SlamcoreDeviceCollectionContainer()
		{
		}
		
		public SlamcoreDeviceCollectionContainer Construct(DataObjectCollection<SlamcoreDeviceDataObject> slamcoreDeviceItems)
        {
            if (slamcoreDeviceItems == null)
                return this;
				
			this.PrimaryKeys = slamcoreDeviceItems.Select(c => c.PrimaryKey).ToList();
            if (slamcoreDeviceItems.ObjectsDataSet == null)
            {
                slamcoreDeviceItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = slamcoreDeviceItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = slamcoreDeviceItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<SlamcoreDeviceDataObject> ExtractSlamcoreDeviceItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<SlamcoreDeviceDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<SlamcoreDeviceDataObject>(typeof(SlamcoreDeviceDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreDeviceContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public SlamcoreDeviceContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual SlamcoreDeviceContainer Construct(SlamcoreDeviceDataObject slamcoreDevice, bool includeDirtyObjectsOnly = false)
		{
            if (slamcoreDevice == null)
                return this;

			this.PrimaryKey = slamcoreDevice.PrimaryKey;
			
            if (slamcoreDevice.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(slamcoreDevice);
            }

			if(slamcoreDevice.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity SlamcoreDevice", "Unable to set a dataset to the entity. Container may not be initialized", "SlamcoreDeviceDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : SlamcoreDevice");
			}

			if(slamcoreDevice.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in SlamcoreDeviceDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "SlamcoreDeviceDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in SlamcoreDeviceDataObject");
			}
			this.InternalObjectId = (int) slamcoreDevice.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? slamcoreDevice.ObjectsDataSet.CloneDirtyObjects() : slamcoreDevice.ObjectsDataSet;

			return this;
		}
		
		public SlamcoreDeviceDataObject ExtractSlamcoreDevice()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<SlamcoreDeviceDataObject>(typeof(SlamcoreDeviceDataObject), InternalObjectId);
			
			return result;
        }	
	}

}