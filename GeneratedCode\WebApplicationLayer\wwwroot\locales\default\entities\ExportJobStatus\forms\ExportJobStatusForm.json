﻿{
  "title": "Export Job Details",
  "description": "Automatically generated entity for Import Export feature. This entity represents an Export job status.",
  "formFields": {
    "Name": {
        "label": "Export Name",
        "description": "Export Name"
    },
    "Start": {
        "label": "Date Start",
        "description": "Date Start"
    },
    "End": {
        "label": "Date End",
        "description": "Date End"
    },
    "TaskStatus": {
        "label": "Status",
        "description": "Status"
    },
    "Progress": {
        "label": "Progress",
        "description": "Progress"
    },
    "ExportedFile": {
        "label": "Exported File",
        "description": "Exported File"
    },
    "ExportedFileFileSize": {
        "label": "Exported File Size (kb)",
        "description": "Exported File Size (kb)"
    },
    "ExportLogFile": {
        "label": "Export Logs",
        "description": "Export Logs"
    }
  },
  "messages": {
    "noDataMessage": "No Export Job Status data available"
  },
  "formButtons": {
    "DriverCurrentStatusReportExport": "DriverCurrentStatusReportExport",
    "LicenseExpiryReportExport": "LicenseExpiryReportExport",
    "VehicleExport": "VehicleExport",
    "UnitUtilisationExport": "UnitUtilisationExport",
    "MachineUnlockReportExport": "MachineUnlockReportExport",
    "DriverAccessAbuseReportExport": "DriverAccessAbuseReportExport",
    "VehicleCurrentStatusReportExport": "VehicleCurrentStatusReportExport",
    "SlamcorePedestrianDetection": "SlamcorePedestrianDetection",
    "SlamcoreAlertHistory": "SlamcoreAlertHistory",
    "GeneralProductivityReportDriverExport": "GeneralProductivityReportDriverExport",
    "PersonToDepartmentVehicleNormalAccessViewExport": "PersonToDepartmentVehicleNormalAccessViewExport",
    "VehicleCalibrationReportExport": "VehicleCalibrationReportExport",
    "BroadcastMessageHistoryExport": "BroadcastMessageHistoryExport",
    "SlamcoreVehicleTelemetryExport": "SlamcoreVehicleTelemetryExport",
    "ImpactReportExport": "ImpactReportExport",
    "PreOpChecklistReportExport": "PreOpChecklistReportExport",
    "ServiceCheckReportExport": "ServiceCheckReportExport",
    "SlamcorePathHistoryExport": "SlamcorePathHistoryExport",
    "ProficiencyReportExport": "ProficiencyReportExport",
    "VORReportExport": "VORReportExport",
    "SynchronizationStatusReportExport": "SynchronizationStatusReportExport",
    "PersonExport": "PersonExport",
    "SlamcorePathHistoryExport1": "SlamcorePathHistoryExport",
    "PedestrianDetectionReportExport": "PedestrianDetectionReportExport",
    "SlamcoreDeviceExport": "SlamcoreDeviceExport",
    "PreopExport": "PreopExport"
  }
} 