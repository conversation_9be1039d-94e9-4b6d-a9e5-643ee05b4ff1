﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMSlamcoreDeviceConnectionView" 
		table="[SlamcoreDeviceConnectionView]" 
		schema="[dbo]"
		mutable="false"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="Offline" >
            <column name="`Offline`" sql-type="smallint" not-null="false" />
        </property> 
		<property name="OnlineDevices" >
            <column name="`OnlineDevices`" sql-type="smallint" not-null="false" />
        </property> 
		<property name="TotalDevices" >
            <column name="`TotalDevices`" sql-type="smallint" not-null="false" />
        </property> 

		
		<!-- many-to-one SlamcoreDevice -->
		<property name="SlamcoreDeviceId" type="System.Guid" not-null="false" formula = "[SlamcoreDeviceId]"></property>  
		<many-to-one name="SlamcoreDevice"  > 
			<column name="`SlamcoreDeviceId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
 




    </class> 

</hibernate-mapping>