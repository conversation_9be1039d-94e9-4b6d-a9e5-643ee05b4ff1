﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;

using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DeleteHandlers
{
	public partial class CustomerDeleteHandler : DeleteHandlerBase<CustomerDataObject>
	{
		
        public CustomerDeleteHandler(IServiceProvider serviceProvider, IDataProvider<CustomerDataObject> tentityDataProvider, ITransactionProvider transactionProvider, IEntityDataProvider entityDataProvider) : base(serviceProvider, tentityDataProvider, transactionProvider, entityDataProvider)
        {
        }

		public override async Task RippleDeleteAsync(CustomerDataObject instance, Parameters parameters, DataProviderDeleteSettings settings)
		{
			// Set resync flag initially so that if any processing is done, it's on the latest copy of the data
			NeedResync = true;

			// Customer.AccessGroupItems (Protected) (i.e. Unable to delete Customer instances because AccessGroupItems.Customer is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadAccessGroupItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.AccessGroupItems);
			}
			// Customer.ContactPersonInformation (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadContactPersonInformationAsync(parameters, skipSecurity: true);
				// We are the FK side entity and the PK side is optional, so no action required here (because when we're deleted, any reference(s) to ContactPersonInformation(s) is (are) deleted with us)	
			}
			// Customer.CustomerFeatureSubscription (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadCustomerFeatureSubscriptionAsync(parameters, skipSecurity: true);
				// We are the FK side entity and the PK side is optional, so no action required here (because when we're deleted, any reference(s) to CustomerFeatureSubscription(s) is (are) deleted with us)	
			}
			// Customer.CustomerModelItems (Protected) (i.e. Unable to delete Customer instances because CustomerModelItems.Customer is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadCustomerModelItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.CustomerModelItems);
			}
			// Customer.CustomerPreOperationalChecklistTemplateItems (Protected) (i.e. Unable to delete Customer instances because CustomerPreOperationalChecklistTemplateItems.Customer is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadCustomerPreOperationalChecklistTemplateItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.CustomerPreOperationalChecklistTemplateItems);
			}
			// Customer.CustomerSSODetailItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadCustomerSSODetailItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.CustomerSSODetailItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// Customer.DepartmentItems (Protected) (i.e. Unable to delete Customer instances because DepartmentItems.Customer is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadDepartmentItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.DepartmentItems);
			}
			// Customer.DriverItems (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadDriverItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.DriverItems)			
				{					
					if (item.Customer != null)
					{	
						item.Customer = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// Customer.EmailGroupsItems (Protected) (i.e. Unable to delete Customer instances because EmailGroupsItems.Customer is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadEmailGroupsItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.EmailGroupsItems);
			}
			// Customer.GoUserToCustomerItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadGoUserToCustomerItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.GoUserToCustomerItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// Customer.PersonItems (Protected) (i.e. Unable to delete Customer instances because PersonItems.Customer is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadPersonItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.PersonItems);
			}
			// Unidrectional backwards reference from ReportSubscription.Customer (BackReference)
			{
				instance = await ResyncAsync(instance);				
				var db = _serviceProvider.GetRequiredService<IDataProvider<ReportSubscriptionDataObject>>();
				var links = await db.GetCollectionAsync(null, "CustomerId == @0", new object[] { instance.Id }, parameters: parameters, skipSecurity: true);
				if (links.Any())
				{
					// instance is optional from point of view of the linked ReportSubscriptions => we're allowed to clear down the references to allow the delete to proceed
					foreach (var item in links)
					{
						item.CustomerId = null;
						await SaveAsync(item);
					}
				}
			}
			// Customer.Sites (Protected) (i.e. Unable to delete Customer instances because Sites.Customer is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSitesAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.Sites);
			}
			// Customer.SlamcoreDeviceItems (Protected) (i.e. Unable to delete Customer instances because SlamcoreDeviceItems.Customer is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSlamcoreDeviceItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.SlamcoreDeviceItems);
			}
			// Customer.VehicleItems (Protected) (i.e. Unable to delete Customer instances because VehicleItems.Customer is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.VehicleItems);
			}
		}
	}
}