﻿{
  "entityName": "Slamcore Pedestrian Detection",
  "entityNamePlural": "Slamcore Pedestrian Detections",   "entityDescription": "This entity represents a Slamcore Pedestrian Detection",
  "fields": {
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "SlamcoreDevice": {
        "displayName": "SlamcoreDevice", 
        "description": "SlamcoreDevice"
    },
    "SlamcoreDeviceId": {
        "displayName": "SlamcoreDeviceId", 
        "description": "Foreign Key"
    },
    "XPosition": {
        "displayName": "XPosition", 
        "description": "XPosition", 
        "validationRules": {
            "1190de36-2667-4a06-bcc9-faae0b6d695f" : {
                    "errorMessage": "Value must be a number for the field XPosition"
            }
        }
    },
    "YPosition": {
        "displayName": "YPosition", 
        "description": "YPosition", 
        "validationRules": {
            "2b7065a8-1a9a-4342-9032-4b2282216fd2" : {
                    "errorMessage": "Value must be a number for the field YPosition"
            }
        }
    }
  }
} 