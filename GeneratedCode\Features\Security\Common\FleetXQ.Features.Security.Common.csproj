﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>1.0.0.6765</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Security Feature Common</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj" />
    <ProjectReference Include="..\..\..\DataLayer\FleetXQ.Data.DataObjects.csproj" />
  </ItemGroup>
</Project>