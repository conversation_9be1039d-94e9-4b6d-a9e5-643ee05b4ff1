﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class GOUser2FAObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _gOUser2FAObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all GOUser2FA objects for current dataset
		private ConcurrentDictionary< int, GOUser2FADataObject> _gOUser2FAObjects = new ConcurrentDictionary< int, GOUser2FADataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<GOUser2FADataObject> _mergedDataObjects;

		private ConcurrentQueue<GOUser2FADataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<GOUser2FADataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> GOUser2FAObjectInternalIds
		{ 
			get { return _gOUser2FAObjectInternalIds; }
			set { _gOUser2FAObjectInternalIds = value; }
		}
		
		// Collection holding all GOUser2FA objects for current dataset
		[JsonProperty("GOUser2FAObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, GOUser2FADataObject> GOUser2FAObjects
		{ 
			get { return _gOUser2FAObjects; }
			set { _gOUser2FAObjects = value; }
		}
		
		
 
		
 
		
 
		
 
		// Index to quickly find all GOUser2FA with a given dealer foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Dealer_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
 
		// Index to quickly find all GOUser2FA with a given gORole foreign key
		public ConcurrentDictionary<System.String, List<int>> GORole_FKIndex = new ConcurrentDictionary<System.String, List<int>>();
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public GOUser2FAObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public GOUser2FAObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<GOUser2FAObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.GOUser2FAObjects)
			{
                var cloneObject = (GOUser2FADataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.GOUser2FAObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.GOUser2FAObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.GOUser2FAObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<GOUser2FAObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.GOUser2FAObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (GOUser2FADataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.GOUser2FAObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.GOUser2FAObjectInternalIds
				.Where(o => this.GOUser2FAObjects[o.Value].IsDirty || this.GOUser2FAObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.GOUser2FAObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var gOUser2FA in GOUser2FAObjects.Values)
			{
				yield return gOUser2FA; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the GOUser2FAObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "GOUser2FAObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = GOUser2FAObjects.TryAdd(newInternalId, (GOUser2FADataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (GOUser2FAObjectInternalIds.ContainsKey(((GOUser2FADataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = GOUser2FAObjectInternalIds.TryRemove(((GOUser2FADataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = GOUser2FAObjectInternalIds.TryAdd(((GOUser2FADataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as GOUser2FADataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "GOUser2FADataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

	 
	 
	 
	 
			// Update the Dealer FK Index 
			if ((objectToAdd as GOUser2FADataObject).DealerId != null)
			{
				if (!Dealer_FKIndex.ContainsKey((System.Guid)(objectToAdd as GOUser2FADataObject).DealerId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Dealer_FKIndex.TryAdd((System.Guid)(objectToAdd as GOUser2FADataObject).DealerId, new List<int>());
					}
				}
				
				if (!Dealer_FKIndex[(System.Guid)(objectToAdd as GOUser2FADataObject).DealerId].Contains(newInternalId))	
					Dealer_FKIndex[(System.Guid)(objectToAdd as GOUser2FADataObject).DealerId].Add(newInternalId);

	            DealerDataObject relatedDealer;
	            if ((objectToAdd as GOUser2FADataObject)._dealer_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<DealerDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as GOUser2FADataObject)._dealer_NewObjectId;

	                relatedDealer = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedDealer = _rootObjectDataSet.GetObject(new DealerDataObject((System.Guid)(objectToAdd as GOUser2FADataObject).DealerId) { IsNew = false });
	            }

	            if (relatedDealer != null && this.RootObjectDataSet.NotifyChanges)
	                relatedDealer.NotifyPropertyChanged("GOUserItems", new SeenObjectCollection());
			}
			
	 
	 
	 
			// Update the GORole FK Index 
			if ((objectToAdd as GOUser2FADataObject).GORoleName != null)
			{
				if (!GORole_FKIndex.ContainsKey((System.String)(objectToAdd as GOUser2FADataObject).GORoleName))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = GORole_FKIndex.TryAdd((System.String)(objectToAdd as GOUser2FADataObject).GORoleName, new List<int>());
					}
				}
				
				if (!GORole_FKIndex[(System.String)(objectToAdd as GOUser2FADataObject).GORoleName].Contains(newInternalId))	
					GORole_FKIndex[(System.String)(objectToAdd as GOUser2FADataObject).GORoleName].Add(newInternalId);

	            GORoleDataObject relatedGORole;
	            if ((objectToAdd as GOUser2FADataObject)._gORole_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<GORoleDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as GOUser2FADataObject)._gORole_NewObjectId;

	                relatedGORole = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedGORole = _rootObjectDataSet.GetObject(new GORoleDataObject((System.String)(objectToAdd as GOUser2FADataObject).GORoleName) { IsNew = false });
	            }

	            if (relatedGORole != null && this.RootObjectDataSet.NotifyChanges)
	                relatedGORole.NotifyPropertyChanged("GOUserItems", new SeenObjectCollection());
			}
			
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (GOUser2FAObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as GOUser2FADataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "GOUser2FAObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = GOUser2FAObjectInternalIds.ContainsKey((objectToRemove as GOUser2FADataObject).PrimaryKey) ? (int?) GOUser2FAObjectInternalIds[(objectToRemove as GOUser2FADataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				GOUser2FADataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = GOUser2FAObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = GOUser2FAObjectInternalIds.TryRemove((objectToRemove as GOUser2FADataObject).PrimaryKey, out idvalue);
					}
				}
				
		 
		 
		 
		 
			// Delete the Dealer FK Index 
				if ((objectToRemove as GOUser2FADataObject).DealerId != null)
				{
					if (Dealer_FKIndex.ContainsKey((System.Guid)(objectToRemove as GOUser2FADataObject).DealerId) && Dealer_FKIndex[(System.Guid)(objectToRemove as GOUser2FADataObject).DealerId].Contains((int)objectToRemoveInternalId))
					{
						Dealer_FKIndex[(System.Guid)(objectToRemove as GOUser2FADataObject).DealerId].Remove((int)objectToRemoveInternalId);

						if (!Dealer_FKIndex[(System.Guid)(objectToRemove as GOUser2FADataObject).DealerId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Dealer_FKIndex.TryRemove((System.Guid)(objectToRemove as GOUser2FADataObject).DealerId, out outvalue);
							}
						}
					}

					DealerDataObject relatedDealer;
		            if ((objectToRemove as GOUser2FADataObject)._dealer_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<DealerDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as GOUser2FADataObject)._dealer_NewObjectId;

						relatedDealer = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedDealer = _rootObjectDataSet.GetObject(new DealerDataObject((System.Guid)(objectToRemove as GOUser2FADataObject).DealerId) { IsNew = false });
		            }

		            if (relatedDealer != null && this.RootObjectDataSet.NotifyChanges)
		                relatedDealer.NotifyPropertyChanged("GOUserItems", new SeenObjectCollection());
					
				}			
		 
		 
		 
			// Delete the GORole FK Index 
				if ((objectToRemove as GOUser2FADataObject).GORoleName != null)
				{
					if (GORole_FKIndex.ContainsKey((System.String)(objectToRemove as GOUser2FADataObject).GORoleName) && GORole_FKIndex[(System.String)(objectToRemove as GOUser2FADataObject).GORoleName].Contains((int)objectToRemoveInternalId))
					{
						GORole_FKIndex[(System.String)(objectToRemove as GOUser2FADataObject).GORoleName].Remove((int)objectToRemoveInternalId);

						if (!GORole_FKIndex[(System.String)(objectToRemove as GOUser2FADataObject).GORoleName].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = GORole_FKIndex.TryRemove((System.String)(objectToRemove as GOUser2FADataObject).GORoleName, out outvalue);
							}
						}
					}

					GORoleDataObject relatedGORole;
		            if ((objectToRemove as GOUser2FADataObject)._gORole_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<GORoleDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as GOUser2FADataObject)._gORole_NewObjectId;

						relatedGORole = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedGORole = _rootObjectDataSet.GetObject(new GORoleDataObject((System.String)(objectToRemove as GOUser2FADataObject).GORoleName) { IsNew = false });
		            }

		            if (relatedGORole != null && this.RootObjectDataSet.NotifyChanges)
		                relatedGORole.NotifyPropertyChanged("GOUserItems", new SeenObjectCollection());
					
				}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return GOUser2FAObjects.ContainsKey(internalObjectId) ? GOUser2FAObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as GOUserDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "GOUser2FAObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = GOUser2FAObjectInternalIds.ContainsKey((objectToGet as GOUserDataObject).PrimaryKey) ? (int?) GOUser2FAObjectInternalIds[(objectToGet as GOUserDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return GOUser2FAObjects.ContainsKey((int)objectToGetInternalId) ? GOUser2FAObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return GOUser2FAObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return GOUser2FAObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		 
		 
		 
		 
		
		public IEnumerable<GOUser2FADataObject> GetGOUserItemsForDealer(DealerDataObject dealerInstance) 
		{
			if (dealerInstance.IsNew)
            {
			
              return GOUser2FAObjects.Where(o => o.Value._dealer_NewObjectId != null && o.Value._dealer_NewObjectId == dealerInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Dealer_FKIndex.ContainsKey(dealerInstance.Id))
			{
				return Dealer_FKIndex[dealerInstance.Id].Where(e => GOUser2FAObjects.ContainsKey(e)).Select(e => GOUser2FAObjects[e]);
			}
			
			return new DataObjectCollection<GOUser2FADataObject>();
		}
		 
		 
		 
		
		public IEnumerable<GOUser2FADataObject> GetGOUserItemsForGORole(GORoleDataObject gORoleInstance) 
		{
			if (gORoleInstance.IsNew)
            {
			
              return GOUser2FAObjects.Where(o => o.Value._gORole_NewObjectId != null && o.Value._gORole_NewObjectId == gORoleInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (GORole_FKIndex.ContainsKey(gORoleInstance.Name))
			{
				return GORole_FKIndex[gORoleInstance.Name].Where(e => GOUser2FAObjects.ContainsKey(e)).Select(e => GOUser2FAObjects[e]);
			}
			
			return new DataObjectCollection<GOUser2FADataObject>();
		}
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
			if (relationName == "AlertSubscriptionItems")
            {
				IEnumerable< AlertSubscriptionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.AlertSubscriptionObjectsDataSet.GetAlertSubscriptionItemsForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerAuditItemsCreated")
            {
				IEnumerable< CustomerAuditDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerAuditObjectsDataSet.GetCustomerAuditItemsCreatedForGOUserWhoCreatedThisCustomer(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerAuditItemsDeleted")
            {
				IEnumerable< CustomerAuditDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerAuditObjectsDataSet.GetCustomerAuditItemsDeletedForGOUserWhoDeletedThisCustomer(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "CustomerAuditItemsModified")
            {
				IEnumerable< CustomerAuditDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.CustomerAuditObjectsDataSet.GetCustomerAuditItemsModifiedForGOUserWhoModifiedThisCustomer(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "DealerDriver")
            {
				IEnumerable< DealerDriverDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.DealerDriverObjectsDataSet.GetDealerDriverForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ExportJobStatusItems")
            {
				IEnumerable< ExportJobStatusDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ExportJobStatusObjectsDataSet.GetExportJobStatusItemsForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "GOUserDepartmentItems")
            {
				IEnumerable< GOUserDepartmentDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GOUserDepartmentObjectsDataSet.GetGOUserDepartmentItemsForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "GoUserToCustomerItems")
            {
				IEnumerable< GoUserToCustomerDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GoUserToCustomerObjectsDataSet.GetGoUserToCustomerItemsForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "MessageHistoryItems")
            {
				IEnumerable< MessageHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.MessageHistoryObjectsDataSet.GetMessageHistoryItemsForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "Person")
            {
				IEnumerable< PersonDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.PersonObjectsDataSet.GetPersonForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "ReportSubscriptionItems")
            {
				IEnumerable< ReportSubscriptionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.ReportSubscriptionObjectsDataSet.GetReportSubscriptionItemsForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "RevisionItems")
            {
				IEnumerable< RevisionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.RevisionObjectsDataSet.GetRevisionItemsForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "Tag")
            {
				IEnumerable< TagDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.TagObjectsDataSet.GetTagForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "UserGroupItems")
            {
				IEnumerable< GOUserGroupDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GOUserGroupObjectsDataSet.GetUserGroupItemsForUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "UserRoleItems")
            {
				IEnumerable< GOUserRoleDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.GOUserRoleObjectsDataSet.GetUserRoleItemsForUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "VehicleLockoutItems")
            {
				IEnumerable< VehicleLockoutDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleLockoutObjectsDataSet.GetVehicleLockoutItemsForGOUser(rootObject as GOUser2FADataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var GOUser2FADataSet = dataSetToMerge as GOUser2FAObjectsDataSet;
				if(GOUser2FADataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in GOUser2FADataSet.GOUser2FAObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "GOUser2FAObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as GOUser2FADataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
		 
		 
		 
		 
			// Reconstruct the Dealer FK Index 
			Dealer_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in GOUser2FAObjects.Values)
			{
				if (item.DealerId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.DealerId;	

				if (!Dealer_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Dealer_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "GOUser2FAObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Dealer_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
			// Reconstruct the GORole FK Index 
			GORole_FKIndex = new ConcurrentDictionary< System.String, List<int>>();
				
			foreach (var item in GOUser2FAObjects.Values)
			{
				if (item.GORoleName == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.GORoleName;	

				if (!GORole_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = GORole_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "GOUser2FAObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				GORole_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (GOUser2FAObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}