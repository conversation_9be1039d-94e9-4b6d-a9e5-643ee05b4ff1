﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>1.0.0.6765</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Security Feature Data Layer</Description>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj" />
    <ProjectReference Include="..\..\Security\Common\FleetXQ.Features.Security.Common.csproj" />
    <ProjectReference Include="..\..\..\DataLayerDataProviders\FleetXQ.Data.DataProviders.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
  </ItemGroup>
</Project>