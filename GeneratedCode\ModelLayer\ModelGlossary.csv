﻿English Term,French Translation
\"Access group\",
\"Access Group Template\",
\"Access group to site\",
\"Alert\",
\"Alert Subscription\",
\"Alert Summary Store Procedure\",
\"AlertHistory\",
\"All Checklist Result View\",
\"All Driver Access Abuse Store Procedure\",
\"All Email Subscription Store Procedure\",
\"All Impacts View\",
\"All license expiry view\",
\"All Message History Store Procedure\",
\"All User Summary Store Procedure\",
\"All Vehicle Calibration Filter\",
\"All Vehicle Calibration Store Procedure\",
\"All Vehicle Unlocks View\",
\"All VOR Sessions Per Vehicle Store Procedure\",
\"All VOR Status Store Procedure\",
\"Automatically generated entity for Import Export feature. This entity represents an Export job status.\",
\"Automatically generated entity for Import Export feature. This entity represents an Import job batch.\",
\"Automatically generated entity for Import Export feature. This entity represents an Import job log line describing errors, etc.\",
\"Automatically generated entity for Import Export feature. This entity represents an Import job status (succeeded, failed, in progress...)\",
\"Broadcast Message History Filter\",
\"BroadcastMessage\",
\"BroadcastMessageHistory\",
\"Cache for the most recent messages from the vehicle\",
\"Canrule\",
\"Canrule Details\",
\"Card\",
\"Card Access Level\",
\"Card to Card Access\",
\"Category Template\",
\"Checklist Answer Detail\",
\"Checklist failure per vechicle view\",
\"Checklist failure view\",
\"Checklist result\",
\"Checklist Settings\",
\"Checklist Status View\",
\"Contact Person Information\",
\"Country\",
\"Current Driver Status Chart View\",
\"Current Status Combined View\",
\"Current Status Driver View\",
\"Current Status Vehicle View\",
\"Current Vehicle Status Chart View\",
\"Customer\",
\"Customer Pre-Operational Checklist Template\",
\"Customer SSO Detail\",
\"Customer To Model View\",
\"Customer To Person View\",
\"CustomerAudit\",
\"CustomerFeatureSubscription\",
\"CustomerModel\",
\"CustomerSnapshot\",
\"Dashboard Driver Card Store Procedure\",
\"Dashboard Driver Card View\",
\"Dashboard filter\",
\"Dashboard Filter More Fields\",
\"Dashboard Vehicle Card Store Procedure\",
\"Dashboard Vehicle Card View\",
\"Dealer\",
\"Dealer Driver\",
\"DealerConfiguration\",
\"DealerFeatureSubscription\",
\"Department\",
\"Department Checklist\",
\"Department Hour Settings\",
\"Department Vehicle Master Card Access\",
\"Department Vehicle Normal Card Access\",
\"Detailed Session View\",
\"Detailed VOR Session Store Procedure\",
\"Driver\",
\"Driver Access Abuse Filter\",
\"Driver License Expiry Store Procedure\",
\"Driver License Expiry View\",
\"Driver Proficiency View\",
\"Email\",
\"Email Groups\",
\"Email Subscription Report Filter\",
\"EmailGroups to Person\",
\"Feature Subscriptions Filter\",
\"FeatureSubscriptionTemplate\",
\"Firmware\",
\"Floor Zones\",
\"FloorPlan\",
\"General License\",
\"General Productivity Per Driver View\",
\"General Productivity Per Vehicle View\",
\"General productivity report filter\",
\"General Productivity View\",
\"Generative Objects Group\",
\"Generative Objects Group Role\",
\"Generative Objects Membership User\",
\"Generative Objects Role\",
\"Generative Objects Security Log-in History\",
\"Generative Objects User 2FA\",
\"Generative Objects User Group\",
\"Generative Objects User Role\",
\"GO 2FA Configuration\",
\"GO Export Job Status\",
\"GO Import Job Batch\",
\"GO Import Job Log\",
\"GO Import Job Status\",
\"GO Security Tokens\",
\"GO User 2FA\",
\"Go User To Customer\",
\"GOChangeDelta\",
\"GOGroup\",
\"GOGroupRole\",
\"GOLoginHistory\",
\"GORole\",
\"GOTask\",
\"GOTask (or descendant) may be set as component operation return type to make the operation asynchronous\",
\"GOUser\",
\"GOUserDepartment\",
\"GOUserGroup\",
\"GOUserRole\",
\"GPS History\",
\"Help\",
\"Help Entity for displaying helpful information to customers\",
\"Hire DeHire Report Filter\",
\"Impact\",
\"Impact frequency per time slot view\",
\"Impact frequency per week day view\",
\"Impact frequency per week month view\",
\"Impact Report Filter\",
\"Impacts for vehicle view\",
\"Incompleted checklist view\",
\"Inspection\",
\"IO_FIELD\",
\"IoTDeviceMessageCache\",
\"License By Model\",
\"License Expiry Report Filter\",
\"Logged hours versus seat hours view\",
\"Machine Unlock Report Filter\",
\"Main Dashboard Filter\",
\"Message History\",
\"Model\",
\"Model Template\",
\"Model Vehicle Master Card  Access\",
\"Model Vehicle Normal Card  Access\",
\"Module\",
\"Module History\",
\"Network Settings\",
\"On Demand Authorisation Filter\",
\"On Demand Authorisation Store Procedure\",
\"On Demand Session\",
\"On Demand Settings\",
\"Pedestrian Detection History\",
\"Pedestrian Detection History Filter\",
\"Per Model Vehicle Normal Card Access for Driver Card\",
\"Per Vehicle Master Card Access\",
\"Per Vehicle Normal Card Access\",
\"Per Vehicle Normal Card Access for Driver Card\",
\"Person\",
\"Person Allocation\",
\"Person to department vehicle master access view\",
\"Person to department vehicle normal access view\",
\"Person to model vehicle master access view\",
\"Person to model vehicle normal access view\",
\"Person to per vehicle master access view\",
\"Person to per vehicle normal access view\",
\"Person to site vehicle master access view\",
\"Person to site vehicle normal access view\",
\"PersonChecklistLanguageSettings\",
\"Pre Op Report Filter\",
\"Pre-Operational Checklist\",
\"Proficiency Combined View\",
\"Proficiency Report Filter\",
\"PSTAT Details\",
\"Region\",
\"Report Subscription\",
\"Report Type\",
\"Revision\",
\"Service Settings\",
\"Session\",
\"Session Details\",
\"Site\",
\"Site Vehicle Master Card Access\",
\"Site Vehicle Normal Card Access\",
\"Site Vehicle Normal Card Access for Driver Card\",
\"SiteFloorPlan\",
\"Slamcore Device Connection View\",
\"Slamcore Device Filter\",
\"Slamcore Pedestrian Detection\",
\"SlamcoreAPIKey\",
\"SlamcoreAwareAuthenticationDetails\",
\"SlamcoreDevice\",
\"SlamcoreDeviceHistory\",
\"Snapshot\",
\"Synchronization Status Report Filter\",
\"Tag\",
\"This Stored Procedure contains the necessary information for displaying the driver card in dashboard page\",
\"This View contains the necessary information for displaying the driver card in dashboard page\",
\"This View contains the necessary information for displaying the vehicle card in dashboard page\",
\"Timezone\",
\"Todays Impact Store Procedure\",
\"Todays Impact View\",
\"Todays Preop Check Store Procedure\",
\"Todays Preop Check View\",
\"Unit Summary Report\",
\"Unit Summary Store Procedure\",
\"Unit Unutilisation Store Procedure\",
\"Unit Utilisation Combined View\",
\"Unit Utilisation Store Procedure\",
\"UpdateFirmwareRequest\",
\"UploadLogoRequest\",
\"Vehicle\",
\"Vehicle Alert Subscription\",
\"Vehicle Diagnostic\",
\"Vehicle GPS\",
\"Vehicle Hire Dehire History\",
\"Vehicle Hire Dehire Synchronization Options\",
\"Vehicle Last GPS Location View\",
\"Vehicle Lockout\",
\"Vehicle Proficiency View\",
\"Vehicle Sessionless Impact\",
\"Vehicle Supervisors View\",
\"Vehicle To Pre Op Checklist View\",
\"Vehicle Utilization Last Twelve Hours Store Procedure\",
\"Vehicle Utilization Last Twelve Hours View\",
\"VehicleBroadcastMessage\",
\"VehicleDiagnostic\",
\"VehicleOtherSettings\",
\"Vehicles per model report\",
\"VehicleSlamcoreLocationHistory\",
\"VOR Report Combined View\",
\"VOR Report Filter\",
\"VOR Setting History\",
\"Website Role\",
\"Website User\",
\"Zone Coordinates\",
 