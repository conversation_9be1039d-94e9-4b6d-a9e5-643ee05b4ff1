﻿{
  "title": "Vehicle Telemetry",
  "description": " ",
  "columns": {
    "NameGridColumn": "Name",
    "IPAddressGridColumn": "IPAddress",
    "SerialNoGridColumn": "SerialNo",
    "UpdateRateGridColumn": "UpdateRate",
    "LastConnectedDateTimeGridColumn": "LastConnectedDateTime",
    "SpeedGridColumn": "Speed",
    "BearingGridColumn": "Bearing"
  },
  "messages": {
    "noDataMessage": "No VehicleSlamcoreLocationHistory data",
    "elementsCountLabel": "Items"
  },
  "commands": {
    "Export": "Export"
  }
} 