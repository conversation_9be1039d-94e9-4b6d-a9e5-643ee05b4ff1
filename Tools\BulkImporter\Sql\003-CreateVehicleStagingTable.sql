-- =============================================
-- FleetXQ Bulk Importer - Vehicle Staging Table
-- Creates staging table for vehicle import data
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE schema_id = SCHEMA_ID('Staging') AND name = 'VehicleImport')
BEGIN
    CREATE TABLE [Staging].[VehicleImport] (
        [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
        [ImportSessionId] UNIQUEIDENTIFIER NOT NULL,
        [RowNumber] INT NOT NULL,
        [ValidationStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending', -- Pending, Valid, Invalid, Processed
        [ValidationErrors] NVARCHAR(MAX) NULL,
        
        -- Raw Input Data (from SQL generation)
        [ExternalVehicleId] NVARCHAR(50) NULL,
        [HireNo] NVARCHAR(50) NOT NULL,
        [SerialNo] NVARCHAR(50) NOT NULL,
        [Description] NVARCHAR(500) NULL,
        [OnHire] BIT NULL,
        [ImpactLockout] BIT NULL,
        [IsCanbus] BIT NULL,
        [TimeoutEnabled] BIT NULL,
        [ModuleIsConnected] BIT NULL,
        [IDLETimer] INT NULL,
        [CustomerName] NVARCHAR(100) NOT NULL,
        [SiteName] NVARCHAR(100) NOT NULL,
        [DepartmentName] NVARCHAR(100) NOT NULL,
        [ModelName] NVARCHAR(100) NOT NULL,
        [ManufacturerName] NVARCHAR(100) NULL,
        [ModuleSerialNumber] NVARCHAR(50) NOT NULL,
        [AssignedDriverEmail] NVARCHAR(50) NULL,
        [AssignedPersonEmail] NVARCHAR(50) NULL,
        
        -- Resolved Foreign Keys (populated during validation)
        [CustomerId] UNIQUEIDENTIFIER NULL,
        [SiteId] UNIQUEIDENTIFIER NULL,
        [DepartmentId] UNIQUEIDENTIFIER NULL,
        [ModelId] UNIQUEIDENTIFIER NULL,
        [ModuleId] UNIQUEIDENTIFIER NULL,
        [AssignedDriverId] UNIQUEIDENTIFIER NULL,
        [AssignedPersonId] UNIQUEIDENTIFIER NULL,
        [ExistingVehicleId] UNIQUEIDENTIFIER NULL,
        
        -- Processing Metadata
        [ProcessingAction] NVARCHAR(20) NULL, -- Insert, Update, Skip
        [ProcessedAt] DATETIME2 NULL,
        [ProcessingErrors] NVARCHAR(MAX) NULL,
        
        -- Foreign key to import session
        CONSTRAINT FK_VehicleImport_ImportSession 
            FOREIGN KEY ([ImportSessionId]) REFERENCES [Staging].[ImportSession]([Id]),
            
        -- Indexes for performance
        INDEX IX_VehicleImport_Session_Status ([ImportSessionId], [ValidationStatus]),
        INDEX IX_VehicleImport_HireNo ([HireNo]),
        INDEX IX_VehicleImport_SerialNo ([SerialNo]),
        INDEX IX_VehicleImport_ExternalId ([ExternalVehicleId]) WHERE [ExternalVehicleId] IS NOT NULL,
        INDEX IX_VehicleImport_ModuleSerial ([ModuleSerialNumber])
    )
    
    PRINT 'Created [Staging].[VehicleImport] table'
END
ELSE
BEGIN
    PRINT '[Staging].[VehicleImport] table already exists'
END
GO