﻿{
  "language": {
    "english": "English",
    "french": "Fran�ais"
  },
  "buttons": {
    "ok": "OK",
    "cancel": "Cancel",
    "save": "Save",
    "delete": "Delete",
    "edit": "Edit",
    "create": "Create",
    "update": "Update",
    "close": "Close",
    "back": "Back",
    "search": "Search",
    "clear": "Clear"
  },
  "lookups": {
    "addItem": "Click to add...",
    "noMatchLabel": "No match found",
    "noAvailableDataLabel": "No available data"
  },
  "navigation": {
    "VehicleCalibrationReport3": "Vehicle Calibration Report",
    "UserEmailAlertSummary2": "User Email Alert Summary",
    "UserDetail": "User Detail",
    "Reports": "Reports",
    "Dashboard": "Dashboard",
    "CountryItems": "Country",
    "PreOpCheckReport": "Pre-Op Check Report",
    "DriverAccessAbuseReport": "Driver Access Abuse Report",
    "VORReport": "VOR Report",
    "CurrentStatusReport": "Current Status Report",
    "FloorPlanManagement": "Floor Plan Management",
    "FloorPlan": "Floor Plan",
    "Import": "Import",
    "Vehicle": "Vehicle Detail",
    "CountryDetails": "Country Details",
    "NewUser": "New User",
    "Help": "Help",
    "RegionItems": "Region ",
    "ServiceCheckReport": "Service Check Report",
    "Vehicles": "Vehicles",
    "GO2FAConfiguration": "2FA Configuration",
    "UserManagement": "Users",
    "Slamcore": "Slamcore",
    "RegionDetails": "Region Details",
    "ChecklistResultAnswers": "Checklist Result Answers",
    "AlertReport": "Alert Report",
    "DealerDetails": "Dealer Details",
    "DataExportTool": "Data Export Tool",
    "PedestrianDetectionReport": "Pedestrian Detection Report",
    "Firmware": "Firmware",
    "VehicleHireDehireReport": "Vehicle Hire Dehire Report",
    "ReportScheduler": "Report Scheduler",
    "Models": "Model Template",
    "FeatureSubscriptions": "Feature Subscriptions",
    "MyAccount": "My Account",
    "MachineUnlockReport": "Machine Unlock Report",
    "CustomerSSO": "Customer SSO",
    "PathAnalysisView": "Path Analysis View",
    "ConnectionStatusDashboard": "Connection Status Dashboard",
    "ProficiencyReport": "Proficiency Report",
    "Devices": "Devices",
    "AdminSettings": "Administration",
    "TimezoneItems": "Timezone",
    "AccessGroupTemplates": "Access Group Template",
    "MoreReports": "More Reports",
    "SynchronizationStatusReport": "Synchronization Status Report",
    "BroadcastMessageReport": "Broadcast Message Report",
    "Customers": "Customers",
    "Export": "Export",
    "UserSummaryReport2": "User Summary Report",
    "OnDemandAuthorisationReport": "On Demand Authorisation Report",
    "VehicleCalibrationReport": "Vehicle Calibration Report",
    "DealerReports2": "Dealer Reports",
    "LiveMap": "Live Map",
    "DealerItems": "Dealer",
    "ImpactReport": "Impact Report",
    "SuperAdmin": "Website Users",
    "LicenseExpiryReport": "License Expiry Report",
    "VehiclesGPSReport": "Vehicles GPS Report",
    "GeneralProductivityReport": "General Productivity Report",
    "EmailSubscriptionReport2": "Email Subscription Report",
    "CustomerDetails": "Customer Details",
    "TimezoneDetails": "Timezone Details",
    "BackNavigationBadge": "Back"
  },
  "lists": {
    "addItem": "Add new {{itemName}}",
    "removeItem": "Remove {{itemName}}"
  },
  "messages": {
    "unsavedChanges": "You have unsaved changes. Are you sure you want to leave this page?",
    "yes": "Yes",
    "no": "No",
    "loading": "Loading...",
    "pageLoading": "Page is loading, please wait...",
    "genericPageError": "An error occured while loading this page. Please contact an administrator.",
    "status": "Status",
    "error": "Error",
    "showDetails": "Show Details",
    "imageCroppingNotSaved": "You haven't validated cropping for the image.<br>Continuing will automatically save the current cropping area.",
    "imageCroppingNotSavedConfirmTitle": "Cropping in progress!",
    "uploadImageTooLarge": "{{imageName}} must be no larger than {{maxImageWidth}} x {{maxImageHeight}} (selected image was {{imageWidth}} x {{imageHeight}})",
    "uploadAllowedFileExtensions": "Allowed file types: {{allowedFileExtensions}}",
    "uploadSuccess": "File {{filename}} uploaded with success",
    "uploadFail": "Uploading file {{filename}} failed",
    "uploadingMessage": "Uploading {{filename}}",
    "uploadMaxSizeReached": "Unable to upload the file. File size should be less than {{size}} kb"
  },
  "dataSource": {
    "deleteError": "Error while deleting {{entity}} data",
    "saveError": "Error while saving {{entity}} data",
    "getError": "Error while loading {{entity}} data",
    "getCollectionError": "Error while loading the {{entity}} collection",
    "countError": "Error while counting {{entity}} data",
    "componentError": "Error while calling {{operation}}",
    "confirmDeleteMessage": "Are you sure you want to delete this {{entity}}?",
    "confirmDeletePopupTitle": "Confirm Deletion"
  },
  "validation": {
    "validationRuleRequiredMessage": "{{fieldName}} is a mandatory field",
    "validationRuleLikeMessage": "{{fieldName}} has to be like '{{pattern}}'",
    "validationRuleRegExMessage": "{{fieldName}} has to match the regular expression {{pattern}}",
    "validationRuleRangeMessage": "{{fieldName}} has to be in the range [{{min}}, {{max}}]",
    "validationRuleMaxMessage": "{{fieldName}} has to be less than or equal to {{max}}",
    "validationRuleMinMessage": "{{fieldName}} has to be greater than or equal to {{min}}",
    "validationRuleMaxLengthMessage": "{{fieldName}}'s length has to be less than {{length}}",
    "validationRuleCustomMessage": "{{entityName}} does not respect the given rule: {{rule}}"
  },
  "login": {
    "loginPageTitle": "Login",
    "loginPageHeading": "Login",
    "holdingPageTitle": "Thank you",
    "holdingPageHeading": "Thank you",  
    "lostPasswordPageTitle": "Lost Password",
    "lostPasswordPageHeading": "Lost Password",
    "validateEmailChangePageTitle": "Validate Email Change",
    "validateEmailChangePageHeading": "Validate Email Change",
    "registerPageTitle": "Register",
    "registerPageHeading": "Register",
    "validateRegistrationPageTitle": "Validate Registration",
    "validateRegistrationPageHeading": "Validate Registration",
    "changePasswordPageTitle": "Change Password",
    "changePasswordPageHeading": "Change Password",
    "validate2FAPageTitle": "Validate 2FA",
    "validate2FAPageHeading": "Validate 2FA",
    "resetPasswordPageTitle": "Reset Password",
    "resetPasswordPageHeading": "Reset Password",
    "username": "Username",
    "password": "Password",
    "currentPassword": "Current Password",
    "newPassword": "New Password",
    "confirmPassword": "Confirm Password",
    "passwordMandatory": "Password is required",
    "changePassword": "Change Password",
    "resetPassword": "Reset Password",
    "login": "Login",
    "firstName": "First Name",
    "lastName": "Last Name",
    "changeUsername": "Change Username",
    "register": "Register",
    "registering": "Registering...",
    "lostPassword": "Forgot Password",
    "emailAddress": "Email Address",
    "pleaseValidateEmail": "Please validate your email address",
    "otpCode": "OTP Code",
    "validate": "Validate",
    "validating": "Validating...",
    "useRecoveryCode": "Use recovery code",
    "errorMessage": "Invalid username or password.",
    "continue": "Continue",
    "connection": "Connection",
    "connecting": "Connecting...",
    "connectingOAuth": "Connecting to OAuth provider...",
    "nullSecurityToken": "Security token is invalid or expired",
    "resettingPassword": "Resetting password...",
    "warningNoActivity": "Due to inactivity, your session will be disconnected soon. Click to keep your session open.",
    "incorrectEmail": "The provided Email Address is invalid.",
    "passwordHasExpired": "Your password has expired. Please create a new password.",
    "authorization": "Authorization",
    "authorizationError": "Authorization check: An error occurred.",
    "permissionDenied": "Permission Denied.",
    "invalidSecurityToken": "Please re-enter your credentials to continue.",
    "expiredSecurityToken": "You were logged out due to inactivity. Please re-enter your credentials to continue.",
    "noActivityLogOut": "You were logged out due to inactivity. Please re-enter your credentials to continue.",
    "unauthorized": "Error: The request was cancelled because you are not sufficiently authorised.",
    "unknownUsernameOrPassword": "Unknown username or password.",
    "changingPassword": "Changing Password",
    "changePasswordMismatchNewConfirm": "The confirming password did not match",
    "changePasswordError": "Unable to change your password.",
    "invalidCurrentPassword": "Invalid Current Password.",
    "sendVerificationCode": "Sending Verification Code...",
    "sendingVerificationCode": "Sending Verification Code...",
    "emailNotVerified": "Your email address has not been verified.",
    "emailNotVerifiedAndResent": "Your email address has not been verified. A new verification email has been sent.",
    "emailNotVerifiedOnApprove": "Your email address must be verified before approval.",
    "adminApprovalPending": "Your account is pending administrator approval.",
    "passwordMustBeDifferent": "New password must be different from the current password.",
    "forbiddenToChangeEmail": "You are not allowed to change your email address.",
    "userAlreadyRegistered": "A user with this email address is already registered.",
    "registerTokenExpired": "Registration token has expired.",
    "noEmailChangeInProgress": "No email change is currently in progress.",
    "emailChangeTokenExpired": "Email change token has expired.",
    "resetPasswordTokenExpired": "Password reset token has expired.",
    "emailVerificationFailed": "Email verification failed.",
    "passwordPolicyNotSatisfied": "Password does not meet the required policy: {0}",
    "approveUserFailed": "Failed to approve user.",
    "approveUserEmailSendFailed": "Failed to send approval email to user.",
    "emailChangeSendEmailFailed": "Failed to send email change verification email.",
    "registrationSendEmailFailed": "Failed to send registration verification email.",
    "passwordRecoverySendEmailFailed": "Failed to send password recovery email.",
    "emailTemplateNotFound": "Email template not found.",
    "emailTemplateContentPlaceholderNotFound": "Email template content placeholder not found.",
    "administratorNotFound": "Administrator not found.",
    "webContextRequired": "Web context is required for this operation.",
    "membershipConfigurationError": "Membership configuration error.",
    "sendEmailFailed": "Failed to send email.",
    "userAccountDisabled": "Your account has been disabled."
  },
  "footer": {
    "builtWith": "Built With Generative Objects!"
  }
}
