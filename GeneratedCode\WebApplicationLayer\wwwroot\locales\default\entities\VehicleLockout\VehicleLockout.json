﻿{
  "entityName": "Vehicle Lockout",
  "entityNamePlural": "Vehicle Lockouts",   "entityDescription": "This entity represents a Vehicle Lockout",
  "fields": {
    "AllVehicleUnlocksViewItems": {
        "displayName": "All Vehicle Unlocks View Items", 
        "description": "All Vehicle Unlocks View Items"
    },
    "Comment": {
        "displayName": "Comment", 
        "description": "Comment"
    },
    "Driver": {
        "displayName": "Driver", 
        "description": "Driver"
    },
    "DriverId": {
        "displayName": "DriverId", 
        "description": "Foreign Key"
    },
    "GOUser": {
        "displayName": "GOUser", 
        "description": "GOUser"
    },
    "GOUserId": {
        "displayName": "GOUserId", 
        "description": "Foreign Key"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "LockoutTime": {
        "displayName": "Lockout Time", 
        "description": "Lockout Time"
    },
    "Note": {
        "displayName": "Note", 
        "description": "Note"
    },
    "RealImpact": {
        "displayName": "Real Impact", 
        "description": "Real Impact"
    },
    "Reason": {
        "displayName": "Reason", 
        "description": "Reason"
    },
    "Session": {
        "displayName": "Session", 
        "description": "Session"
    },
    "SessionId": {
        "displayName": "SessionId", 
        "description": "Foreign Key"
    },
    "UnlockDateTime": {
        "displayName": "Unlock DateTime", 
        "description": "Unlock DateTime"
    },
    "Vehicle": {
        "displayName": "Vehicle", 
        "description": "Vehicle"
    },
    "VehicleId": {
        "displayName": "VehicleId", 
        "description": "Foreign Key"
    }
  }
} 