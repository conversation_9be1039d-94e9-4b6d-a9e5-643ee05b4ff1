﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

(function () {
	// 
	FleetXQ.Web.Controllers.ConnectionStatusDashboardPageController = function(applicationController) {
		var self = this;
		this.subscriptions = [];
		// store all subscriptions in this array, to unsubscribe on release.
		
		this._objectType = "ConnectionStatusDashboardPage";

		this.applicationController = applicationController;
		this.ObjectsDataSet = applicationController.ObjectsDataSet;
		this.contextId = [this.applicationController.getNextContextId()];
		this.customController = undefined;

		// Integrate custom code if any
		if (FleetXQ.Web.Controllers.ConnectionStatusDashboardPageControllerCustom !== undefined) {
		    this.customController = new FleetXQ.Web.Controllers.ConnectionStatusDashboardPageControllerCustom(self);
		};

		// Initialize View Models and Data Stores
		
			this.SlamcoreDeviceFilterFormViewModel = new FleetXQ.Web.ViewModels.SlamcoreDeviceFilterFormViewModel(this, $("#SlamcoreDeviceFilterFormControl"), null, null, this.contextId);		
		this.SlamcoreDeviceFilterFormViewModel.StatusData.ShowTitle(false);		
		
			this.SlamcoreDeviceGrid1ViewModel = new FleetXQ.Web.ViewModels.SlamcoreDeviceGrid1ViewModel(this, $("#SlamcoreDeviceGrid1"), null, null, this.contextId);		
		this.SlamcoreDeviceGrid1ViewModel.StatusData.ShowTitle(true);		
		this.SlamcoreDeviceGrid1ViewModel.include = "auto-include-id-5e3a13fc-f85e-417e-858e-297ad5c3e4e5-10184ded-1f5d-44d6-8b31-b13e0ca87f1c";	
		
			this.SlamcoreDeviceConnectionViewFormViewModel = new FleetXQ.Web.ViewModels.SlamcoreDeviceConnectionViewFormViewModel(this, $("#SlamcoreDeviceConnectionViewFormControl"), null, null, this.contextId);		
		this.SlamcoreDeviceConnectionViewFormViewModel.StatusData.ShowTitle(false);		
		this.customPageTitle = ko.observable(null);

		this.iconToUse = ko.observable(null);

		this.pageTitle = ko.pureComputed (function() {
			if (self.customPageTitle()) {
				return self.customPageTitle();
			}
			return "Connection Status Dashboard";
		});

		this.pageTitleParams = ko.observable({});
		
		this.showGoBackBadge = ko.observable(false);
		this.goBackBadgeLabel = ko.observable("pages/ConnectionStatusDashboardPage:navigation.BackNavigationBadge");
		this.showItemCountBadge = ko.observable(false);
		this.isLoading = ko.observable(false);
		this.itemCount = ko.pureComputed(function() {
			return null;
		}); 
		this.goBack = function () {
			if(self.IsInEditMode && self.IsInEditMode()) {
				if(!confirm(FleetXQ.Web.Messages.i18n.t('messages.unsavedChanges')))
					return;
				
				// Cancel edit mode on current page controller
				if (self.CancelEdit) {
					self.CancelEdit(false);
				}					
			}

};

		this.subscriptions.push(this.SlamcoreDeviceFilterFormViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnSlamcoreDeviceFilterFormViewModelIsBusyChanged(newValue); }));
		this.subscriptions.push(this.SlamcoreDeviceGrid1ViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnSlamcoreDeviceGrid1ViewModelIsBusyChanged(newValue); }));
		this.subscriptions.push(this.SlamcoreDeviceConnectionViewFormViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnSlamcoreDeviceConnectionViewFormViewModelIsBusyChanged(newValue); }));
		
		this.IsInEditMode = function() {

			return  (self.SlamcoreDeviceFilterFormViewModel.StatusData.DisplayMode && self.SlamcoreDeviceFilterFormViewModel.StatusData.DisplayMode() == 'edit') ||  (self.SlamcoreDeviceGrid1ViewModel.StatusData.DisplayMode && self.SlamcoreDeviceGrid1ViewModel.StatusData.DisplayMode() == 'edit') ||  (self.SlamcoreDeviceConnectionViewFormViewModel.StatusData.DisplayMode && self.SlamcoreDeviceConnectionViewFormViewModel.StatusData.DisplayMode() == 'edit');
		};

		this.CancelEdit = function(isCommandCall) {
			if (self.SlamcoreDeviceFilterFormViewModel.CancelEdit) {
				self.SlamcoreDeviceFilterFormViewModel.CancelEdit(isCommandCall);
			}
			if (self.SlamcoreDeviceGrid1ViewModel.CancelEdit) {
				self.SlamcoreDeviceGrid1ViewModel.CancelEdit(isCommandCall);
			}
			if (self.SlamcoreDeviceConnectionViewFormViewModel.CancelEdit) {
				self.SlamcoreDeviceConnectionViewFormViewModel.CancelEdit(isCommandCall);
			}
		};

		// Events Handlers
		this.OnSlamcoreDeviceFilterFormViewModelIsBusyChanged = function (newValue) {
		};

		this.OnSlamcoreDeviceGrid1ViewModelIsBusyChanged = function (newValue) {
		};

		this.OnSlamcoreDeviceConnectionViewFormViewModelIsBusyChanged = function (newValue) {
		};

		this.initialize = function() {
			if (FleetXQ.Web.Controllers.Custom && FleetXQ.Web.Controllers.Custom.getPageTitle) {
				self.customPageTitle(FleetXQ.Web.Controllers.Custom.getPageTitle(self));
			}

	
			// Call custom initialize if defined
			if (self.customController !== undefined && self.customController.initialize !== undefined) {
			    self.customController.initialize();
			}
		// Initial data load for all source elements (no dependencies)
			if (!GO.Filter.hasUrlFilter(self.SlamcoreDeviceGrid1ViewModel.FILTER_NAME, self.SlamcoreDeviceGrid1ViewModel)) {
				self.SlamcoreDeviceGrid1ViewModel.LoadSlamcoreDeviceObjectCollection();
			}
		};
		this.initialize();



		this.release = function() {
			// unsubscribe
			for(var i = 0;i <  self.subscriptions.length;i++) {
				self.subscriptions[i].dispose();
			}
			self.subscriptions = [];
			self.ObjectsDataSet.cleanContext(self.contextId);
			
			self.SlamcoreDeviceFilterFormViewModel.release();
			self.SlamcoreDeviceFilterFormViewModel = null;
			self.SlamcoreDeviceGrid1ViewModel.release();
			self.SlamcoreDeviceGrid1ViewModel = null;
			self.SlamcoreDeviceConnectionViewFormViewModel.release();
			self.SlamcoreDeviceConnectionViewFormViewModel = null;
		};


		return self;
	};		
	
	if (window.ApplicationSourceHandler)
		window.ApplicationSourceHandler.onSourceLoaded("/Controllers/ConnectionStatusDashboardPageController.js");
} ());
