﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;


namespace FleetXQ.BusinessLayer.ORMSupportClasses
{
    public class DataProviderProxy : IDataProviderProxy
    {
        protected IServiceProvider _serviceProvider;

        public DataProviderProxy(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

		#region private fields
        // maps entity types to dataprovider types
        private Dictionary<Type, Type> _dataProviders = new Dictionary<Type, Type> 
        {
			{ typeof(AccessGroupDataObject), typeof(IDataProvider<AccessGroupDataObject>) },
			{ typeof(AccessGroupTemplateDataObject), typeof(IDataProvider<AccessGroupTemplateDataObject>) },
			{ typeof(AccessGroupToSiteDataObject), typeof(IDataProvider<AccessGroupToSiteDataObject>) },
			{ typeof(AlertDataObject), typeof(IDataProvider<AlertDataObject>) },
			{ typeof(AlertHistoryDataObject), typeof(IDataProvider<AlertHistoryDataObject>) },
			{ typeof(AlertSubscriptionDataObject), typeof(IDataProvider<AlertSubscriptionDataObject>) },
			{ typeof(AlertSummaryStoreProcedureDataObject), typeof(IDataProvider<AlertSummaryStoreProcedureDataObject>) },
			{ typeof(AllChecklistResultViewDataObject), typeof(IDataProvider<AllChecklistResultViewDataObject>) },
			{ typeof(AllDriverAccessAbuseStoreProcedureDataObject), typeof(IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject>) },
			{ typeof(AllEmailSubscriptionStoreProcedureDataObject), typeof(IDataProvider<AllEmailSubscriptionStoreProcedureDataObject>) },
			{ typeof(AllImpactsViewDataObject), typeof(IDataProvider<AllImpactsViewDataObject>) },
			{ typeof(AllLicenseExpiryViewDataObject), typeof(IDataProvider<AllLicenseExpiryViewDataObject>) },
			{ typeof(AllMessageHistoryStoreProcedureDataObject), typeof(IDataProvider<AllMessageHistoryStoreProcedureDataObject>) },
			{ typeof(AllUserSummaryStoreProcedureDataObject), typeof(IDataProvider<AllUserSummaryStoreProcedureDataObject>) },
			{ typeof(AllVehicleCalibrationFilterDataObject), typeof(IDataProvider<AllVehicleCalibrationFilterDataObject>) },
			{ typeof(AllVehicleCalibrationStoreProcedureDataObject), typeof(IDataProvider<AllVehicleCalibrationStoreProcedureDataObject>) },
			{ typeof(AllVehicleUnlocksViewDataObject), typeof(IDataProvider<AllVehicleUnlocksViewDataObject>) },
			{ typeof(AllVORSessionsPerVehicleStoreProcedureDataObject), typeof(IDataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>) },
			{ typeof(AllVORStatusStoreProcedureDataObject), typeof(IDataProvider<AllVORStatusStoreProcedureDataObject>) },
			{ typeof(BroadcastMessageDataObject), typeof(IDataProvider<BroadcastMessageDataObject>) },
			{ typeof(BroadcastMessageHistoryDataObject), typeof(IDataProvider<BroadcastMessageHistoryDataObject>) },
			{ typeof(BroadcastMessageHistoryFilterDataObject), typeof(IDataProvider<BroadcastMessageHistoryFilterDataObject>) },
			{ typeof(CanruleDataObject), typeof(IDataProvider<CanruleDataObject>) },
			{ typeof(CanruleDetailsDataObject), typeof(IDataProvider<CanruleDetailsDataObject>) },
			{ typeof(CardDataObject), typeof(IDataProvider<CardDataObject>) },
			{ typeof(CardToCardAccessDataObject), typeof(IDataProvider<CardToCardAccessDataObject>) },
			{ typeof(CategoryTemplateDataObject), typeof(IDataProvider<CategoryTemplateDataObject>) },
			{ typeof(ChecklistDetailDataObject), typeof(IDataProvider<ChecklistDetailDataObject>) },
			{ typeof(ChecklistFailurePerVechicleViewDataObject), typeof(IDataProvider<ChecklistFailurePerVechicleViewDataObject>) },
			{ typeof(ChecklistFailureViewDataObject), typeof(IDataProvider<ChecklistFailureViewDataObject>) },
			{ typeof(ChecklistResultDataObject), typeof(IDataProvider<ChecklistResultDataObject>) },
			{ typeof(ChecklistSettingsDataObject), typeof(IDataProvider<ChecklistSettingsDataObject>) },
			{ typeof(ChecklistStatusViewDataObject), typeof(IDataProvider<ChecklistStatusViewDataObject>) },
			{ typeof(ContactPersonInformationDataObject), typeof(IDataProvider<ContactPersonInformationDataObject>) },
			{ typeof(CountryDataObject), typeof(IDataProvider<CountryDataObject>) },
			{ typeof(CurrentDriverStatusChartViewDataObject), typeof(IDataProvider<CurrentDriverStatusChartViewDataObject>) },
			{ typeof(CurrentStatusCombinedViewDataObject), typeof(IDataProvider<CurrentStatusCombinedViewDataObject>) },
			{ typeof(CurrentStatusDriverViewDataObject), typeof(IDataProvider<CurrentStatusDriverViewDataObject>) },
			{ typeof(CurrentStatusVehicleViewDataObject), typeof(IDataProvider<CurrentStatusVehicleViewDataObject>) },
			{ typeof(CurrentVehicleStatusChartViewDataObject), typeof(IDataProvider<CurrentVehicleStatusChartViewDataObject>) },
			{ typeof(CustomerDataObject), typeof(IDataProvider<CustomerDataObject>) },
			{ typeof(CustomerAuditDataObject), typeof(IDataProvider<CustomerAuditDataObject>) },
			{ typeof(CustomerFeatureSubscriptionDataObject), typeof(IDataProvider<CustomerFeatureSubscriptionDataObject>) },
			{ typeof(CustomerModelDataObject), typeof(IDataProvider<CustomerModelDataObject>) },
			{ typeof(CustomerPreOperationalChecklistTemplateDataObject), typeof(IDataProvider<CustomerPreOperationalChecklistTemplateDataObject>) },
			{ typeof(CustomerSnapshotDataObject), typeof(IDataProvider<CustomerSnapshotDataObject>) },
			{ typeof(CustomerSSODetailDataObject), typeof(IDataProvider<CustomerSSODetailDataObject>) },
			{ typeof(CustomerToModelDataObject), typeof(IDataProvider<CustomerToModelDataObject>) },
			{ typeof(CustomerToPersonViewDataObject), typeof(IDataProvider<CustomerToPersonViewDataObject>) },
			{ typeof(DashboardDriverCardStoreProcedureDataObject), typeof(IDataProvider<DashboardDriverCardStoreProcedureDataObject>) },
			{ typeof(DashboardDriverCardViewDataObject), typeof(IDataProvider<DashboardDriverCardViewDataObject>) },
			{ typeof(DashboardFilterDataObject), typeof(IDataProvider<DashboardFilterDataObject>) },
			{ typeof(DashboardFilterMoreFieldsDataObject), typeof(IDataProvider<DashboardFilterMoreFieldsDataObject>) },
			{ typeof(DashboardVehicleCardStoreProcedureDataObject), typeof(IDataProvider<DashboardVehicleCardStoreProcedureDataObject>) },
			{ typeof(DashboardVehicleCardViewDataObject), typeof(IDataProvider<DashboardVehicleCardViewDataObject>) },
			{ typeof(DealerDataObject), typeof(IDataProvider<DealerDataObject>) },
			{ typeof(DealerConfigurationDataObject), typeof(IDataProvider<DealerConfigurationDataObject>) },
			{ typeof(DealerDriverDataObject), typeof(IDataProvider<DealerDriverDataObject>) },
			{ typeof(DealerFeatureSubscriptionDataObject), typeof(IDataProvider<DealerFeatureSubscriptionDataObject>) },
			{ typeof(DepartmentDataObject), typeof(IDataProvider<DepartmentDataObject>) },
			{ typeof(DepartmentChecklistDataObject), typeof(IDataProvider<DepartmentChecklistDataObject>) },
			{ typeof(DepartmentHourSettingsDataObject), typeof(IDataProvider<DepartmentHourSettingsDataObject>) },
			{ typeof(DepartmentVehicleMasterCardAccessDataObject), typeof(IDataProvider<DepartmentVehicleMasterCardAccessDataObject>) },
			{ typeof(DepartmentVehicleNormalCardAccessDataObject), typeof(IDataProvider<DepartmentVehicleNormalCardAccessDataObject>) },
			{ typeof(DetailedSessionViewDataObject), typeof(IDataProvider<DetailedSessionViewDataObject>) },
			{ typeof(DetailedVORSessionStoreProcedureDataObject), typeof(IDataProvider<DetailedVORSessionStoreProcedureDataObject>) },
			{ typeof(DriverDataObject), typeof(IDataProvider<DriverDataObject>) },
			{ typeof(DriverAccessAbuseFilterDataObject), typeof(IDataProvider<DriverAccessAbuseFilterDataObject>) },
			{ typeof(DriverLicenseExpiryStoreProcedureDataObject), typeof(IDataProvider<DriverLicenseExpiryStoreProcedureDataObject>) },
			{ typeof(DriverLicenseExpiryViewDataObject), typeof(IDataProvider<DriverLicenseExpiryViewDataObject>) },
			{ typeof(DriverProficiencyViewDataObject), typeof(IDataProvider<DriverProficiencyViewDataObject>) },
			{ typeof(EmailDataObject), typeof(IDataProvider<EmailDataObject>) },
			{ typeof(EmailGroupsDataObject), typeof(IDataProvider<EmailGroupsDataObject>) },
			{ typeof(EmailGroupsToPersonDataObject), typeof(IDataProvider<EmailGroupsToPersonDataObject>) },
			{ typeof(EmailSubscriptionReportFilterDataObject), typeof(IDataProvider<EmailSubscriptionReportFilterDataObject>) },
			{ typeof(ExportJobStatusDataObject), typeof(IDataProvider<ExportJobStatusDataObject>) },
			{ typeof(FeatureSubscriptionsFilterDataObject), typeof(IDataProvider<FeatureSubscriptionsFilterDataObject>) },
			{ typeof(FeatureSubscriptionTemplateDataObject), typeof(IDataProvider<FeatureSubscriptionTemplateDataObject>) },
			{ typeof(FirmwareDataObject), typeof(IDataProvider<FirmwareDataObject>) },
			{ typeof(FloorPlanDataObject), typeof(IDataProvider<FloorPlanDataObject>) },
			{ typeof(FloorZonesDataObject), typeof(IDataProvider<FloorZonesDataObject>) },
			{ typeof(GeneralProductivityPerDriverViewLatestDataObject), typeof(IDataProvider<GeneralProductivityPerDriverViewLatestDataObject>) },
			{ typeof(GeneralProductivityPerVehicleViewDataObject), typeof(IDataProvider<GeneralProductivityPerVehicleViewDataObject>) },
			{ typeof(GeneralProductivityReportFilterDataObject), typeof(IDataProvider<GeneralProductivityReportFilterDataObject>) },
			{ typeof(GeneralProductivityViewDataObject), typeof(IDataProvider<GeneralProductivityViewDataObject>) },
			{ typeof(GO2FAConfigurationDataObject), typeof(IDataProvider<GO2FAConfigurationDataObject>) },
			{ typeof(GOChangeDeltaDataObject), typeof(IDataProvider<GOChangeDeltaDataObject>) },
			{ typeof(GOGroupDataObject), typeof(IDataProvider<GOGroupDataObject>) },
			{ typeof(GOGroupRoleDataObject), typeof(IDataProvider<GOGroupRoleDataObject>) },
			{ typeof(GOLoginHistoryDataObject), typeof(IDataProvider<GOLoginHistoryDataObject>) },
			{ typeof(GORoleDataObject), typeof(IDataProvider<GORoleDataObject>) },
			{ typeof(GOSecurityTokensDataObject), typeof(IDataProvider<GOSecurityTokensDataObject>) },
			{ typeof(GOTaskDataObject), typeof(IDataProvider<GOTaskDataObject>) },
			{ typeof(GOUserDataObject), typeof(IDataProvider<GOUserDataObject>) },
			{ typeof(GOUser2FADataObject), typeof(IDataProvider<GOUser2FADataObject>) },
			{ typeof(GOUserDepartmentDataObject), typeof(IDataProvider<GOUserDepartmentDataObject>) },
			{ typeof(GOUserGroupDataObject), typeof(IDataProvider<GOUserGroupDataObject>) },
			{ typeof(GOUserRoleDataObject), typeof(IDataProvider<GOUserRoleDataObject>) },
			{ typeof(GoUserToCustomerDataObject), typeof(IDataProvider<GoUserToCustomerDataObject>) },
			{ typeof(GPSHistoryDataObject), typeof(IDataProvider<GPSHistoryDataObject>) },
			{ typeof(HelpDataObject), typeof(IDataProvider<HelpDataObject>) },
			{ typeof(HireDeHireReportFilterDataObject), typeof(IDataProvider<HireDeHireReportFilterDataObject>) },
			{ typeof(ImpactDataObject), typeof(IDataProvider<ImpactDataObject>) },
			{ typeof(ImpactFrequencyPerTimeSlotViewDataObject), typeof(IDataProvider<ImpactFrequencyPerTimeSlotViewDataObject>) },
			{ typeof(ImpactFrequencyPerWeekDayViewDataObject), typeof(IDataProvider<ImpactFrequencyPerWeekDayViewDataObject>) },
			{ typeof(ImpactFrequencyPerWeekMonthViewDataObject), typeof(IDataProvider<ImpactFrequencyPerWeekMonthViewDataObject>) },
			{ typeof(ImpactReportFilterDataObject), typeof(IDataProvider<ImpactReportFilterDataObject>) },
			{ typeof(ImpactsForVehicleViewDataObject), typeof(IDataProvider<ImpactsForVehicleViewDataObject>) },
			{ typeof(ImportJobBatchDataObject), typeof(IDataProvider<ImportJobBatchDataObject>) },
			{ typeof(ImportJobLogDataObject), typeof(IDataProvider<ImportJobLogDataObject>) },
			{ typeof(ImportJobStatusDataObject), typeof(IDataProvider<ImportJobStatusDataObject>) },
			{ typeof(IncompletedChecklistViewDataObject), typeof(IDataProvider<IncompletedChecklistViewDataObject>) },
			{ typeof(InspectionDataObject), typeof(IDataProvider<InspectionDataObject>) },
			{ typeof(IOFIELDDataObject), typeof(IDataProvider<IOFIELDDataObject>) },
			{ typeof(IoTDeviceMessageCacheDataObject), typeof(IDataProvider<IoTDeviceMessageCacheDataObject>) },
			{ typeof(LicenceDetailDataObject), typeof(IDataProvider<LicenceDetailDataObject>) },
			{ typeof(LicenseByModelDataObject), typeof(IDataProvider<LicenseByModelDataObject>) },
			{ typeof(LicenseExpiryReportFilterDataObject), typeof(IDataProvider<LicenseExpiryReportFilterDataObject>) },
			{ typeof(LoggedHoursVersusSeatHoursViewDataObject), typeof(IDataProvider<LoggedHoursVersusSeatHoursViewDataObject>) },
			{ typeof(MachineUnlockReportFilterDataObject), typeof(IDataProvider<MachineUnlockReportFilterDataObject>) },
			{ typeof(MainDashboardFilterDataObject), typeof(IDataProvider<MainDashboardFilterDataObject>) },
			{ typeof(MessageHistoryDataObject), typeof(IDataProvider<MessageHistoryDataObject>) },
			{ typeof(ModelDataObject), typeof(IDataProvider<ModelDataObject>) },
			{ typeof(ModelVehicleMasterCardAccessDataObject), typeof(IDataProvider<ModelVehicleMasterCardAccessDataObject>) },
			{ typeof(ModelVehicleNormalCardAccessDataObject), typeof(IDataProvider<ModelVehicleNormalCardAccessDataObject>) },
			{ typeof(ModuleDataObject), typeof(IDataProvider<ModuleDataObject>) },
			{ typeof(ModuleHistoryDataObject), typeof(IDataProvider<ModuleHistoryDataObject>) },
			{ typeof(NetworkSettingsDataObject), typeof(IDataProvider<NetworkSettingsDataObject>) },
			{ typeof(OnDemandAuthorisationFilterDataObject), typeof(IDataProvider<OnDemandAuthorisationFilterDataObject>) },
			{ typeof(OnDemandAuthorisationStoreProcedureDataObject), typeof(IDataProvider<OnDemandAuthorisationStoreProcedureDataObject>) },
			{ typeof(OnDemandSessionDataObject), typeof(IDataProvider<OnDemandSessionDataObject>) },
			{ typeof(OnDemandSettingsDataObject), typeof(IDataProvider<OnDemandSettingsDataObject>) },
			{ typeof(PedestrianDetectionHistoryDataObject), typeof(IDataProvider<PedestrianDetectionHistoryDataObject>) },
			{ typeof(PedestrianDetectionHistoryFilterDataObject), typeof(IDataProvider<PedestrianDetectionHistoryFilterDataObject>) },
			{ typeof(PermissionDataObject), typeof(IDataProvider<PermissionDataObject>) },
			{ typeof(PersonDataObject), typeof(IDataProvider<PersonDataObject>) },
			{ typeof(PersonAllocationDataObject), typeof(IDataProvider<PersonAllocationDataObject>) },
			{ typeof(PersonChecklistLanguageSettingsDataObject), typeof(IDataProvider<PersonChecklistLanguageSettingsDataObject>) },
			{ typeof(PersonToDepartmentVehicleMasterAccessViewDataObject), typeof(IDataProvider<PersonToDepartmentVehicleMasterAccessViewDataObject>) },
			{ typeof(PersonToDepartmentVehicleNormalAccessViewDataObject), typeof(IDataProvider<PersonToDepartmentVehicleNormalAccessViewDataObject>) },
			{ typeof(PersonToModelVehicleMasterAccessViewDataObject), typeof(IDataProvider<PersonToModelVehicleMasterAccessViewDataObject>) },
			{ typeof(PersonToModelVehicleNormalAccessViewDataObject), typeof(IDataProvider<PersonToModelVehicleNormalAccessViewDataObject>) },
			{ typeof(PersonToPerVehicleMasterAccessViewDataObject), typeof(IDataProvider<PersonToPerVehicleMasterAccessViewDataObject>) },
			{ typeof(PersonToPerVehicleNormalAccessViewDataObject), typeof(IDataProvider<PersonToPerVehicleNormalAccessViewDataObject>) },
			{ typeof(PersonToSiteVehicleMasterAccessViewDataObject), typeof(IDataProvider<PersonToSiteVehicleMasterAccessViewDataObject>) },
			{ typeof(PersonToSiteVehicleNormalAccessViewDataObject), typeof(IDataProvider<PersonToSiteVehicleNormalAccessViewDataObject>) },
			{ typeof(PerVehicleMasterCardAccessDataObject), typeof(IDataProvider<PerVehicleMasterCardAccessDataObject>) },
			{ typeof(PerVehicleNormalCardAccessDataObject), typeof(IDataProvider<PerVehicleNormalCardAccessDataObject>) },
			{ typeof(PreOperationalChecklistDataObject), typeof(IDataProvider<PreOperationalChecklistDataObject>) },
			{ typeof(PreOpReportFilterDataObject), typeof(IDataProvider<PreOpReportFilterDataObject>) },
			{ typeof(ProficiencyCombinedViewDataObject), typeof(IDataProvider<ProficiencyCombinedViewDataObject>) },
			{ typeof(ProficiencyReportFilterDataObject), typeof(IDataProvider<ProficiencyReportFilterDataObject>) },
			{ typeof(PSTATDetailsDataObject), typeof(IDataProvider<PSTATDetailsDataObject>) },
			{ typeof(RegionDataObject), typeof(IDataProvider<RegionDataObject>) },
			{ typeof(ReportSubscriptionDataObject), typeof(IDataProvider<ReportSubscriptionDataObject>) },
			{ typeof(ReportTypeDataObject), typeof(IDataProvider<ReportTypeDataObject>) },
			{ typeof(RevisionDataObject), typeof(IDataProvider<RevisionDataObject>) },
			{ typeof(ServiceSettingsDataObject), typeof(IDataProvider<ServiceSettingsDataObject>) },
			{ typeof(SessionDataObject), typeof(IDataProvider<SessionDataObject>) },
			{ typeof(SessionDetailsDataObject), typeof(IDataProvider<SessionDetailsDataObject>) },
			{ typeof(SiteDataObject), typeof(IDataProvider<SiteDataObject>) },
			{ typeof(SiteFloorPlanDataObject), typeof(IDataProvider<SiteFloorPlanDataObject>) },
			{ typeof(SiteVehicleMasterCardAccessDataObject), typeof(IDataProvider<SiteVehicleMasterCardAccessDataObject>) },
			{ typeof(SiteVehicleNormalCardAccessDataObject), typeof(IDataProvider<SiteVehicleNormalCardAccessDataObject>) },
			{ typeof(SlamcoreAPIKeyDataObject), typeof(IDataProvider<SlamcoreAPIKeyDataObject>) },
			{ typeof(SlamcoreAwareAuthenticationDetailsDataObject), typeof(IDataProvider<SlamcoreAwareAuthenticationDetailsDataObject>) },
			{ typeof(SlamcoreDeviceDataObject), typeof(IDataProvider<SlamcoreDeviceDataObject>) },
			{ typeof(SlamcoreDeviceConnectionViewDataObject), typeof(IDataProvider<SlamcoreDeviceConnectionViewDataObject>) },
			{ typeof(SlamcoreDeviceFilterDataObject), typeof(IDataProvider<SlamcoreDeviceFilterDataObject>) },
			{ typeof(SlamcoreDeviceHistoryDataObject), typeof(IDataProvider<SlamcoreDeviceHistoryDataObject>) },
			{ typeof(SlamcorePedestrianDetectionDataObject), typeof(IDataProvider<SlamcorePedestrianDetectionDataObject>) },
			{ typeof(SnapshotDataObject), typeof(IDataProvider<SnapshotDataObject>) },
			{ typeof(SynchronizationStatusReportFilterDataObject), typeof(IDataProvider<SynchronizationStatusReportFilterDataObject>) },
			{ typeof(TagDataObject), typeof(IDataProvider<TagDataObject>) },
			{ typeof(TimezoneDataObject), typeof(IDataProvider<TimezoneDataObject>) },
			{ typeof(TodaysImpactStoreProcedureDataObject), typeof(IDataProvider<TodaysImpactStoreProcedureDataObject>) },
			{ typeof(TodaysImpactViewDataObject), typeof(IDataProvider<TodaysImpactViewDataObject>) },
			{ typeof(TodaysPreopCheckStoreProcedureDataObject), typeof(IDataProvider<TodaysPreopCheckStoreProcedureDataObject>) },
			{ typeof(TodaysPreopCheckViewDataObject), typeof(IDataProvider<TodaysPreopCheckViewDataObject>) },
			{ typeof(UnitSummaryReportDataObject), typeof(IDataProvider<UnitSummaryReportDataObject>) },
			{ typeof(UnitSummaryStoreProcedureDataObject), typeof(IDataProvider<UnitSummaryStoreProcedureDataObject>) },
			{ typeof(UnitUnutilisationStoreProcedureDataObject), typeof(IDataProvider<UnitUnutilisationStoreProcedureDataObject>) },
			{ typeof(UnitUtilisationCombinedViewDataObject), typeof(IDataProvider<UnitUtilisationCombinedViewDataObject>) },
			{ typeof(UnitUtilisationStoreProcedureDataObject), typeof(IDataProvider<UnitUtilisationStoreProcedureDataObject>) },
			{ typeof(UpdateFirmwareRequestDataObject), typeof(IDataProvider<UpdateFirmwareRequestDataObject>) },
			{ typeof(UploadLogoRequestDataObject), typeof(IDataProvider<UploadLogoRequestDataObject>) },
			{ typeof(VehicleDataObject), typeof(IDataProvider<VehicleDataObject>) },
			{ typeof(VehicleAlertSubscriptionDataObject), typeof(IDataProvider<VehicleAlertSubscriptionDataObject>) },
			{ typeof(VehicleBroadcastMessageDataObject), typeof(IDataProvider<VehicleBroadcastMessageDataObject>) },
			{ typeof(VehicleDiagnosticDataObject), typeof(IDataProvider<VehicleDiagnosticDataObject>) },
			{ typeof(VehicleGPSDataObject), typeof(IDataProvider<VehicleGPSDataObject>) },
			{ typeof(VehicleHireDehireHistoryDataObject), typeof(IDataProvider<VehicleHireDehireHistoryDataObject>) },
			{ typeof(VehicleHireDehireSynchronizationOptionsDataObject), typeof(IDataProvider<VehicleHireDehireSynchronizationOptionsDataObject>) },
			{ typeof(VehicleLastGPSLocationViewDataObject), typeof(IDataProvider<VehicleLastGPSLocationViewDataObject>) },
			{ typeof(VehicleLockoutDataObject), typeof(IDataProvider<VehicleLockoutDataObject>) },
			{ typeof(VehicleOtherSettingsDataObject), typeof(IDataProvider<VehicleOtherSettingsDataObject>) },
			{ typeof(VehicleProficiencyViewDataObject), typeof(IDataProvider<VehicleProficiencyViewDataObject>) },
			{ typeof(VehicleSessionlessImpactDataObject), typeof(IDataProvider<VehicleSessionlessImpactDataObject>) },
			{ typeof(VehicleSlamcoreLocationHistoryDataObject), typeof(IDataProvider<VehicleSlamcoreLocationHistoryDataObject>) },
			{ typeof(VehiclesPerModelReportDataObject), typeof(IDataProvider<VehiclesPerModelReportDataObject>) },
			{ typeof(VehicleSupervisorsViewDataObject), typeof(IDataProvider<VehicleSupervisorsViewDataObject>) },
			{ typeof(VehicleToPreOpChecklistViewDataObject), typeof(IDataProvider<VehicleToPreOpChecklistViewDataObject>) },
			{ typeof(VehicleUtilizationLastTwelveHoursStoreProcedureDataObject), typeof(IDataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>) },
			{ typeof(VehicleUtilizationLastTwelveHoursViewDataObject), typeof(IDataProvider<VehicleUtilizationLastTwelveHoursViewDataObject>) },
			{ typeof(VORReportCombinedViewDataObject), typeof(IDataProvider<VORReportCombinedViewDataObject>) },
			{ typeof(VORReportFilterDataObject), typeof(IDataProvider<VORReportFilterDataObject>) },
			{ typeof(VORSettingHistoryDataObject), typeof(IDataProvider<VORSettingHistoryDataObject>) },
			{ typeof(WebsiteRoleDataObject), typeof(IDataProvider<WebsiteRoleDataObject>) },
			{ typeof(WebsiteUserDataObject), typeof(IDataProvider<WebsiteUserDataObject>) },
			{ typeof(ZoneCoordinatesDataObject), typeof(IDataProvider<ZoneCoordinatesDataObject>) },
		};
		#endregion

        public int Count(Type entityType, string filterPredicate, object[] filterArguments, GenerativeObjects.Practices.ORMSupportClasses.IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            var dataProvider = _serviceProvider.GetService(_dataProviders[entityType]) as dynamic;
            return dataProvider.Count(filterPredicate, filterArguments, context, parameters, skipSecurity);
        }

        public void Delete(IDataObject entity, IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            var dataProvider = _serviceProvider.GetService(_dataProviders[entity.GetType()]) as dynamic;
            dataProvider.Delete(entity, context, parameters, skipSecurity);
        }

        public IDataObject Get(IDataObject entity, List<string> includes = null, IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            var dataProvider = _serviceProvider.GetService(_dataProviders[entity.GetType()]) as dynamic;
            return dataProvider.Get(entity, includes, context, parameters, skipSecurity);
        }

        public DataObjectCollection<IDataObject> GetCollection(Type entityType, string filterPredicate, object[] filterArguments = null, string orderByPredicate = null, int pageNumber = 0, int pageSize = 0, List<string> includes = null, IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            var dataProvider = _serviceProvider.GetService(_dataProviders[entityType]) as dynamic;
            return dataProvider.GetCollection(filterPredicate, filterArguments, orderByPredicate, pageNumber, pageSize, includes, context, parameters, skipSecurity);
        }

        public IDataObject Save(IDataObject entity, List<string> includes = null, IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            var dataProvider = _serviceProvider.GetService(_dataProviders[entity.GetType()]) as dynamic;
            return dataProvider.Save(entity, includes, context, parameters, skipSecurity);
        }
	}
}
