using FleetXQ.Tools.BulkImporter.Configuration;

namespace FleetXQ.Tools.BulkImporter.Services;

/// <summary>
/// Enhanced SQL data generation service using temporary tables to avoid permanent schema objects
/// </summary>
public interface ISqlDataGenerationServiceTemp
{
    /// <summary>
    /// Creates a new import session with temporary tables (no permanent schema objects)
    /// </summary>
    /// <param name="sessionName">Name for the import session</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Session identifier and connection info</returns>
    Task<ImportSessionInfo> CreateImportSessionAsync(string sessionName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates synthetic driver data directly in session-specific temporary tables
    /// </summary>
    /// <param name="sessionInfo">Session information</param>
    /// <param name="count">Number of drivers to generate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generation result summary</returns>
    Task<DataGenerationResult> GenerateDriverDataAsync(ImportSessionInfo sessionInfo, int count, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates synthetic vehicle data directly in session-specific temporary tables
    /// </summary>
    /// <param name="sessionInfo">Session information</param>
    /// <param name="count">Number of vehicles to generate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generation result summary</returns>
    Task<DataGenerationResult> GenerateVehicleDataAsync(ImportSessionInfo sessionInfo, int count, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates staged data in temporary tables using dynamic SQL
    /// </summary>
    /// <param name="sessionInfo">Session information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result summary</returns>
    Task<ValidationResult> ValidateStagedDataAsync(ImportSessionInfo sessionInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes validated temporary table data directly into production tables
    /// </summary>
    /// <param name="sessionInfo">Session information</param>
    /// <param name="dryRun">Whether to perform a dry run without actual changes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result summary</returns>
    Task<ProcessingResult> ProcessStagedDataAsync(ImportSessionInfo sessionInfo, bool dryRun = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Completes the import session and ensures cleanup of temporary objects
    /// </summary>
    /// <param name="sessionInfo">Session information</param>
    /// <param name="finalStatus">Final session status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task CompleteImportSessionAsync(ImportSessionInfo sessionInfo, string finalStatus, CancellationToken cancellationToken = default);
}

/// <summary>
/// Import session information for temporary table-based processing
/// </summary>
public class ImportSessionInfo
{
    /// <summary>
    /// Unique session identifier
    /// </summary>
    public Guid SessionId { get; set; }

    /// <summary>
    /// Session name/description
    /// </summary>
    public string SessionName { get; set; } = string.Empty;

    /// <summary>
    /// Session start time
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Current session status
    /// </summary>
    public string Status { get; set; } = "Running";

    /// <summary>
    /// Temporary driver table name
    /// </summary>
    public string DriverTableName { get; set; } = string.Empty;

    /// <summary>
    /// Temporary vehicle table name
    /// </summary>
    public string VehicleTableName { get; set; } = string.Empty;

    /// <summary>
    /// Connection string for this session (if using dedicated connections)
    /// </summary>
    public string? ConnectionString { get; set; }

    /// <summary>
    /// Total rows to be processed
    /// </summary>
    public int TotalRows { get; set; }

    /// <summary>
    /// Successfully processed rows
    /// </summary>
    public int ProcessedRows { get; set; }

    /// <summary>
    /// Failed rows
    /// </summary>
    public int FailedRows { get; set; }

    /// <summary>
    /// Session-specific configuration snapshot
    /// </summary>
    public Dictionary<string, object> ConfigurationSnapshot { get; set; } = new();
}

/// <summary>
/// Enhanced data generation result with session information
/// </summary>
public class DataGenerationResultTemp : DataGenerationResult
{
    /// <summary>
    /// Temporary table name where data was generated
    /// </summary>
    public string TemporaryTableName { get; set; } = string.Empty;

    /// <summary>
    /// Memory usage during generation (MB)
    /// </summary>
    public double MemoryUsageMB { get; set; }

    /// <summary>
    /// Whether the data is stored in memory (table variable) or disk (temp table)
    /// </summary>
    public bool IsMemoryBased { get; set; }
}

/// <summary>
/// Configuration options for temporary table-based processing
/// </summary>
public class TemporaryTableOptions
{
    public const string SectionName = "TemporaryTables";

    /// <summary>
    /// Use temporary tables instead of permanent staging tables
    /// </summary>
    public bool UseTemporaryTables { get; set; } = true;

    /// <summary>
    /// Session tracking mode: Memory, TempTable, None
    /// </summary>
    public string SessionTrackingMode { get; set; } = "Memory";

    /// <summary>
    /// Maximum dataset size to keep in memory (table variables)
    /// </summary>
    public int MaxMemoryDatasetSize { get; set; } = 10000;

    /// <summary>
    /// Use global temporary tables (##) for debugging purposes
    /// </summary>
    public bool UseGlobalTempTablesForDebugging { get; set; } = false;

    /// <summary>
    /// Automatically drop temporary tables on completion (vs letting connection close handle it)
    /// </summary>
    public bool ExplicitCleanup { get; set; } = false;

    /// <summary>
    /// Connection pool size for temporary table sessions
    /// </summary>
    public int ConnectionPoolSize { get; set; } = 5;
}
