﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcorePedestrianDetectionObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _slamcorePedestrianDetectionObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all SlamcorePedestrianDetection objects for current dataset
		private ConcurrentDictionary< int, SlamcorePedestrianDetectionDataObject> _slamcorePedestrianDetectionObjects = new ConcurrentDictionary< int, SlamcorePedestrianDetectionDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<SlamcorePedestrianDetectionDataObject> _mergedDataObjects;

		private ConcurrentQueue<SlamcorePedestrianDetectionDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<SlamcorePedestrianDetectionDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> SlamcorePedestrianDetectionObjectInternalIds
		{ 
			get { return _slamcorePedestrianDetectionObjectInternalIds; }
			set { _slamcorePedestrianDetectionObjectInternalIds = value; }
		}
		
		// Collection holding all SlamcorePedestrianDetection objects for current dataset
		[JsonProperty("SlamcorePedestrianDetectionObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, SlamcorePedestrianDetectionDataObject> SlamcorePedestrianDetectionObjects
		{ 
			get { return _slamcorePedestrianDetectionObjects; }
			set { _slamcorePedestrianDetectionObjects = value; }
		}
		
		// Index to quickly find all SlamcorePedestrianDetection with a given slamcoreDevice foreign key
		public ConcurrentDictionary<System.Guid, List<int>> SlamcoreDevice_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public SlamcorePedestrianDetectionObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public SlamcorePedestrianDetectionObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<SlamcorePedestrianDetectionObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SlamcorePedestrianDetectionObjects)
			{
                var cloneObject = (SlamcorePedestrianDetectionDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcorePedestrianDetectionObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SlamcorePedestrianDetectionObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcorePedestrianDetectionObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			foreach(var fkKeyValue in this.SlamcoreDevice_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.SlamcoreDevice_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.SlamcoreDevice_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<SlamcorePedestrianDetectionObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SlamcorePedestrianDetectionObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (SlamcorePedestrianDetectionDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.SlamcorePedestrianDetectionObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SlamcorePedestrianDetectionObjectInternalIds
				.Where(o => this.SlamcorePedestrianDetectionObjects[o.Value].IsDirty || this.SlamcorePedestrianDetectionObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcorePedestrianDetectionObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var slamcorePedestrianDetection in SlamcorePedestrianDetectionObjects.Values)
			{
				yield return slamcorePedestrianDetection; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the SlamcorePedestrianDetectionObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "SlamcorePedestrianDetectionObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcorePedestrianDetectionObjects.TryAdd(newInternalId, (SlamcorePedestrianDetectionDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (SlamcorePedestrianDetectionObjectInternalIds.ContainsKey(((SlamcorePedestrianDetectionDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = SlamcorePedestrianDetectionObjectInternalIds.TryRemove(((SlamcorePedestrianDetectionDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcorePedestrianDetectionObjectInternalIds.TryAdd(((SlamcorePedestrianDetectionDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as SlamcorePedestrianDetectionDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "SlamcorePedestrianDetectionDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

			// Update the SlamcoreDevice FK Index 
			if (!SlamcoreDevice_FKIndex.ContainsKey((objectToAdd as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = SlamcoreDevice_FKIndex.TryAdd((objectToAdd as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId, new List<int>());
				}
			}
				
			if (!SlamcoreDevice_FKIndex[(objectToAdd as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId].Contains(newInternalId))
				SlamcoreDevice_FKIndex[(objectToAdd as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId].Add(newInternalId);

            SlamcoreDeviceDataObject relatedSlamcoreDevice;
            if ((objectToAdd as SlamcorePedestrianDetectionDataObject)._slamcoreDevice_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<SlamcoreDeviceDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as SlamcorePedestrianDetectionDataObject)._slamcoreDevice_NewObjectId;

	            relatedSlamcoreDevice = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedSlamcoreDevice = _rootObjectDataSet.GetObject(new SlamcoreDeviceDataObject((objectToAdd as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId) { IsNew = false });
            }

			if (relatedSlamcoreDevice != null && this.RootObjectDataSet.NotifyChanges)
                relatedSlamcoreDevice.NotifyPropertyChanged("SlamcorePedestrianDetectionItems", new SeenObjectCollection());
			
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (SlamcorePedestrianDetectionObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as SlamcorePedestrianDetectionDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "SlamcorePedestrianDetectionObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = SlamcorePedestrianDetectionObjectInternalIds.ContainsKey((objectToRemove as SlamcorePedestrianDetectionDataObject).PrimaryKey) ? (int?) SlamcorePedestrianDetectionObjectInternalIds[(objectToRemove as SlamcorePedestrianDetectionDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				SlamcorePedestrianDetectionDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcorePedestrianDetectionObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = SlamcorePedestrianDetectionObjectInternalIds.TryRemove((objectToRemove as SlamcorePedestrianDetectionDataObject).PrimaryKey, out idvalue);
					}
				}
				
			// Delete the SlamcoreDevice FK Index 
				if (SlamcoreDevice_FKIndex.ContainsKey((objectToRemove as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId) && SlamcoreDevice_FKIndex[(objectToRemove as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId].Contains((int)objectToRemoveInternalId))
				{
					SlamcoreDevice_FKIndex[(objectToRemove as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId].Remove((int)objectToRemoveInternalId);

					if (!SlamcoreDevice_FKIndex[(objectToRemove as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = SlamcoreDevice_FKIndex.TryRemove((objectToRemove as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId, out outvalue);
						}
					}
				}
				
				SlamcoreDeviceDataObject relatedSlamcoreDevice;
	            if ((objectToRemove as SlamcorePedestrianDetectionDataObject)._slamcoreDevice_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<SlamcoreDeviceDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as SlamcorePedestrianDetectionDataObject)._slamcoreDevice_NewObjectId;

					relatedSlamcoreDevice = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedSlamcoreDevice = _rootObjectDataSet.GetObject(new SlamcoreDeviceDataObject((objectToRemove as SlamcorePedestrianDetectionDataObject).SlamcoreDeviceId) { IsNew = false });
	            }

	            if (relatedSlamcoreDevice != null && this.RootObjectDataSet.NotifyChanges)
	                relatedSlamcoreDevice.NotifyPropertyChanged("SlamcorePedestrianDetectionItems", new SeenObjectCollection());
				
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return SlamcorePedestrianDetectionObjects.ContainsKey(internalObjectId) ? SlamcorePedestrianDetectionObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as SlamcorePedestrianDetectionDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "SlamcorePedestrianDetectionObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = SlamcorePedestrianDetectionObjectInternalIds.ContainsKey((objectToGet as SlamcorePedestrianDetectionDataObject).PrimaryKey) ? (int?) SlamcorePedestrianDetectionObjectInternalIds[(objectToGet as SlamcorePedestrianDetectionDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return SlamcorePedestrianDetectionObjects.ContainsKey((int)objectToGetInternalId) ? SlamcorePedestrianDetectionObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return SlamcorePedestrianDetectionObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return SlamcorePedestrianDetectionObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		
		public IEnumerable<SlamcorePedestrianDetectionDataObject> GetSlamcorePedestrianDetectionItemsForSlamcoreDevice(SlamcoreDeviceDataObject slamcoreDeviceInstance) 
		{
			if (slamcoreDeviceInstance.IsNew)
            {
			
              return SlamcorePedestrianDetectionObjects.Where(o => o.Value._slamcoreDevice_NewObjectId != null && o.Value._slamcoreDevice_NewObjectId == slamcoreDeviceInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (SlamcoreDevice_FKIndex.ContainsKey(slamcoreDeviceInstance.Id))
			{
				return SlamcoreDevice_FKIndex[slamcoreDeviceInstance.Id].Where(e => SlamcorePedestrianDetectionObjects.ContainsKey(e)).Select(e => SlamcorePedestrianDetectionObjects[e]);
			}
			
			return new DataObjectCollection<SlamcorePedestrianDetectionDataObject>();
		}
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var SlamcorePedestrianDetectionDataSet = dataSetToMerge as SlamcorePedestrianDetectionObjectsDataSet;
				if(SlamcorePedestrianDetectionDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in SlamcorePedestrianDetectionDataSet.SlamcorePedestrianDetectionObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "SlamcorePedestrianDetectionObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as SlamcorePedestrianDetectionDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
			// Reconstruct the SlamcoreDevice FK Index 
			SlamcoreDevice_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in SlamcorePedestrianDetectionObjects.Values)
			{
				if (item.SlamcoreDeviceId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.SlamcoreDeviceId;	

				if (!SlamcoreDevice_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = SlamcoreDevice_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SlamcorePedestrianDetectionObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				SlamcoreDevice_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (SlamcorePedestrianDetectionObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}