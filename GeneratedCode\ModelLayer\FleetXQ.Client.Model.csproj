﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <OutputType>Library</OutputType>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
    <PropertyGroup>
    <VersionPrefix>1.0.0.6765</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ SDK</Description>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\CustomCode\ModelLayer\FleetXQ.Client.Model.Custom.csproj" />
    <ProjectReference Include="..\Features\Security\Common\FleetXQ.Features.Security.Common.csproj" />
    <ProjectReference Include="..\BusinessLayer\FleetXQ.BusinessLayer.csproj" />
    <ProjectReference Include="..\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.csproj" />
    <ProjectReference Include="..\..\CustomCode\BusinessLayerClientComponents\FleetXQ.BusinessLayer.Components.Client.Custom.csproj" />
    <ProjectReference Include="..\DataLayerDataProviders\FleetXQ.Data.DataProviders.csproj" />
    <ProjectReference Include="..\DataLayerDeleteHandlers\FleetXQ.Data.DeleteHandlers.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
    <PackageReference Include="GenerativeObjects.Services" Version="2.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
</Project>