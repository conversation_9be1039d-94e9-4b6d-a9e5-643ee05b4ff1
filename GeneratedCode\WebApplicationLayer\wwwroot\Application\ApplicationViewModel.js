﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
(function (global) {
    FleetXQ.Web.Application.ViewModel = function (appcontroller) {
        var self = this;

        this.controller = appcontroller;
        this.contextId = [this.controller.getNextContextId()];

        this.pageController = ko.observable(null);
		this.bottomHeaderVisible = ko.observable(false);

		// Connect to custom code if any
		if (FleetXQ.Web.Application.ViewModelCustom !== undefined)
			this.viewModelCustom = new FleetXQ.Web.Application.ViewModelCustom(self);
 
		this.iconToUse = ko.computed(function () {
			return self.pageController() == null ? "" : self.pageController().iconToUse();
        });

		this.iconToUseVisible = ko.computed(function () {
			return self.iconToUse() != null && self.iconToUse() != '';
        });

		this.pageTitle = ko.computed(function () {
			return self.pageController() == null ? "" : self.pageController().pageTitle();
        });

		this.pageTitleParams = ko.computed(function () {
			return self.pageController() == null ? "" : self.pageController().pageTitleParams();
		});

		this.showGoBackBadge = ko.computed(function () {
			return self.pageController() == null ? "" : self.pageController().showGoBackBadge();
        });
        
        this.goBackBadgeLabel = ko.computed(function () {
			return self.pageController() == null ? "" : self.pageController().goBackBadgeLabel();
        });

        this.showItemCountBadge = ko.computed(function () {
			return self.pageController() == null ? "" : self.pageController().showItemCountBadge();
        });
		

        this.itemCount = ko.computed(function () {
			return self.pageController() == null ? "" : self.pageController().itemCount();
        });

        this.isLoading = ko.computed(function () {
			return self.pageController() == null ? false : self.pageController().isLoading();
        });

        this.goBack = function () {
			if (self.pageController() != null) {
            self.pageController().goBack();
            }
        };

        // class and function to toggle sidebar in mini mode
		this.sidebarMini = ko.observable(false);
		this.sidebarToggleMini = function () {
			self.sidebarMini(!self.sidebarMini());
		};

		this.navigation = {
			breadCrumbs : ko.observableArray(),			
			isVehicleCalibrationReport3Visible : ko.observable(true),
			isVehicleCalibrationReport3Enabled : ko.observable(true),
            isVehicleCalibrationReport3Active : ko.observable(false), 
			isUserEmailAlertSummary2Visible : ko.observable(true),
			isUserEmailAlertSummary2Enabled : ko.observable(true),
            isUserEmailAlertSummary2Active : ko.observable(false), 
			isUserDetailVisible : ko.observable(true),
			isUserDetailEnabled : ko.observable(true),
            isUserDetailActive : ko.observable(false), 
			isReportsVisible : ko.observable(true),
			isReportsEnabled : ko.observable(true),
            isReportsActive : ko.observable(false), 
			isDashboardVisible : ko.observable(true),
			isDashboardEnabled : ko.observable(true),
            isDashboardActive : ko.observable(false), 
			isCountryItemsVisible : ko.observable(true),
			isCountryItemsEnabled : ko.observable(true),
            isCountryItemsActive : ko.observable(false), 
			isPreOpCheckReportVisible : ko.observable(true),
			isPreOpCheckReportEnabled : ko.observable(true),
            isPreOpCheckReportActive : ko.observable(false), 
			isDriverAccessAbuseReportVisible : ko.observable(true),
			isDriverAccessAbuseReportEnabled : ko.observable(true),
            isDriverAccessAbuseReportActive : ko.observable(false), 
			isVORReportVisible : ko.observable(true),
			isVORReportEnabled : ko.observable(true),
            isVORReportActive : ko.observable(false), 
			isCurrentStatusReportVisible : ko.observable(true),
			isCurrentStatusReportEnabled : ko.observable(true),
            isCurrentStatusReportActive : ko.observable(false), 
			isFloorPlanManagementVisible : ko.observable(true),
			isFloorPlanManagementEnabled : ko.observable(true),
            isFloorPlanManagementActive : ko.observable(false), 
			isFloorPlanVisible : ko.observable(true),
			isFloorPlanEnabled : ko.observable(true),
            isFloorPlanActive : ko.observable(false), 
			isImportVisible : ko.observable(true),
			isImportEnabled : ko.observable(true),
            isImportActive : ko.observable(false), 
			isVehicleVisible : ko.observable(true),
			isVehicleEnabled : ko.observable(true),
            isVehicleActive : ko.observable(false), 
			isCountryDetailsVisible : ko.observable(true),
			isCountryDetailsEnabled : ko.observable(true),
            isCountryDetailsActive : ko.observable(false), 
			isNewUserVisible : ko.observable(true),
			isNewUserEnabled : ko.observable(true),
            isNewUserActive : ko.observable(false), 
			isHelpVisible : ko.observable(true),
			isHelpEnabled : ko.observable(true),
            isHelpActive : ko.observable(false), 
			isRegionItemsVisible : ko.observable(true),
			isRegionItemsEnabled : ko.observable(true),
            isRegionItemsActive : ko.observable(false), 
			isServiceCheckReportVisible : ko.observable(true),
			isServiceCheckReportEnabled : ko.observable(true),
            isServiceCheckReportActive : ko.observable(false), 
			isVehiclesVisible : ko.observable(true),
			isVehiclesEnabled : ko.observable(true),
            isVehiclesActive : ko.observable(false), 
			isGO2FAConfigurationVisible : ko.observable(true),
			isGO2FAConfigurationEnabled : ko.observable(true),
            isGO2FAConfigurationActive : ko.observable(false), 
			isUserManagementVisible : ko.observable(true),
			isUserManagementEnabled : ko.observable(true),
            isUserManagementActive : ko.observable(false), 
			isSlamcoreVisible : ko.observable(true),
			isSlamcoreEnabled : ko.observable(true),
            isSlamcoreActive : ko.observable(false), 
			isRegionDetailsVisible : ko.observable(true),
			isRegionDetailsEnabled : ko.observable(true),
            isRegionDetailsActive : ko.observable(false), 
			isChecklistResultAnswersVisible : ko.observable(true),
			isChecklistResultAnswersEnabled : ko.observable(true),
            isChecklistResultAnswersActive : ko.observable(false), 
			isAlertReportVisible : ko.observable(true),
			isAlertReportEnabled : ko.observable(true),
            isAlertReportActive : ko.observable(false), 
			isDealerDetailsVisible : ko.observable(true),
			isDealerDetailsEnabled : ko.observable(true),
            isDealerDetailsActive : ko.observable(false), 
			isDataExportToolVisible : ko.observable(true),
			isDataExportToolEnabled : ko.observable(true),
            isDataExportToolActive : ko.observable(false), 
			isPedestrianDetectionReportVisible : ko.observable(true),
			isPedestrianDetectionReportEnabled : ko.observable(true),
            isPedestrianDetectionReportActive : ko.observable(false), 
			isFirmwareVisible : ko.observable(true),
			isFirmwareEnabled : ko.observable(true),
            isFirmwareActive : ko.observable(false), 
			isVehicleHireDehireReportVisible : ko.observable(true),
			isVehicleHireDehireReportEnabled : ko.observable(true),
            isVehicleHireDehireReportActive : ko.observable(false), 
			isReportSchedulerVisible : ko.observable(true),
			isReportSchedulerEnabled : ko.observable(true),
            isReportSchedulerActive : ko.observable(false), 
			isModelsVisible : ko.observable(true),
			isModelsEnabled : ko.observable(true),
            isModelsActive : ko.observable(false), 
			isFeatureSubscriptionsVisible : ko.observable(true),
			isFeatureSubscriptionsEnabled : ko.observable(true),
            isFeatureSubscriptionsActive : ko.observable(false), 
			isMyAccountVisible : ko.observable(true),
			isMyAccountEnabled : ko.observable(true),
            isMyAccountActive : ko.observable(false), 
			isMachineUnlockReportVisible : ko.observable(true),
			isMachineUnlockReportEnabled : ko.observable(true),
            isMachineUnlockReportActive : ko.observable(false), 
			isCustomerSSOVisible : ko.observable(true),
			isCustomerSSOEnabled : ko.observable(true),
            isCustomerSSOActive : ko.observable(false), 
			isPathAnalysisViewVisible : ko.observable(true),
			isPathAnalysisViewEnabled : ko.observable(true),
            isPathAnalysisViewActive : ko.observable(false), 
			isConnectionStatusDashboardVisible : ko.observable(true),
			isConnectionStatusDashboardEnabled : ko.observable(true),
            isConnectionStatusDashboardActive : ko.observable(false), 
			isProficiencyReportVisible : ko.observable(true),
			isProficiencyReportEnabled : ko.observable(true),
            isProficiencyReportActive : ko.observable(false), 
			isDevicesVisible : ko.observable(true),
			isDevicesEnabled : ko.observable(true),
            isDevicesActive : ko.observable(false), 
			isAdminSettingsVisible : ko.observable(true),
			isAdminSettingsEnabled : ko.observable(true),
            isAdminSettingsActive : ko.observable(false), 
			isTimezoneItemsVisible : ko.observable(true),
			isTimezoneItemsEnabled : ko.observable(true),
            isTimezoneItemsActive : ko.observable(false), 
			isAccessGroupTemplatesVisible : ko.observable(true),
			isAccessGroupTemplatesEnabled : ko.observable(true),
            isAccessGroupTemplatesActive : ko.observable(false), 
			isMoreReportsVisible : ko.observable(true),
			isMoreReportsEnabled : ko.observable(true),
            isMoreReportsActive : ko.observable(false), 
			isSynchronizationStatusReportVisible : ko.observable(true),
			isSynchronizationStatusReportEnabled : ko.observable(true),
            isSynchronizationStatusReportActive : ko.observable(false), 
			isBroadcastMessageReportVisible : ko.observable(true),
			isBroadcastMessageReportEnabled : ko.observable(true),
            isBroadcastMessageReportActive : ko.observable(false), 
			isCustomersVisible : ko.observable(true),
			isCustomersEnabled : ko.observable(true),
            isCustomersActive : ko.observable(false), 
			isExportVisible : ko.observable(true),
			isExportEnabled : ko.observable(true),
            isExportActive : ko.observable(false), 
			isUserSummaryReport2Visible : ko.observable(true),
			isUserSummaryReport2Enabled : ko.observable(true),
            isUserSummaryReport2Active : ko.observable(false), 
			isOnDemandAuthorisationReportVisible : ko.observable(true),
			isOnDemandAuthorisationReportEnabled : ko.observable(true),
            isOnDemandAuthorisationReportActive : ko.observable(false), 
			isVehicleCalibrationReportVisible : ko.observable(true),
			isVehicleCalibrationReportEnabled : ko.observable(true),
            isVehicleCalibrationReportActive : ko.observable(false), 
			isDealerReports2Visible : ko.observable(true),
			isDealerReports2Enabled : ko.observable(true),
            isDealerReports2Active : ko.observable(false), 
			isLiveMapVisible : ko.observable(true),
			isLiveMapEnabled : ko.observable(true),
            isLiveMapActive : ko.observable(false), 
			isDealerItemsVisible : ko.observable(true),
			isDealerItemsEnabled : ko.observable(true),
            isDealerItemsActive : ko.observable(false), 
			isImpactReportVisible : ko.observable(true),
			isImpactReportEnabled : ko.observable(true),
            isImpactReportActive : ko.observable(false), 
			isSuperAdminVisible : ko.observable(true),
			isSuperAdminEnabled : ko.observable(true),
            isSuperAdminActive : ko.observable(false), 
			isLicenseExpiryReportVisible : ko.observable(true),
			isLicenseExpiryReportEnabled : ko.observable(true),
            isLicenseExpiryReportActive : ko.observable(false), 
			isVehiclesGPSReportVisible : ko.observable(true),
			isVehiclesGPSReportEnabled : ko.observable(true),
            isVehiclesGPSReportActive : ko.observable(false), 
			isGeneralProductivityReportVisible : ko.observable(true),
			isGeneralProductivityReportEnabled : ko.observable(true),
            isGeneralProductivityReportActive : ko.observable(false), 
			isEmailSubscriptionReport2Visible : ko.observable(true),
			isEmailSubscriptionReport2Enabled : ko.observable(true),
            isEmailSubscriptionReport2Active : ko.observable(false), 
			isCustomerDetailsVisible : ko.observable(true),
			isCustomerDetailsEnabled : ko.observable(true),
            isCustomerDetailsActive : ko.observable(false), 
			isTimezoneDetailsVisible : ko.observable(true),
			isTimezoneDetailsEnabled : ko.observable(true),
            isTimezoneDetailsActive : ko.observable(false) 
		};

		this.navigation.breadCrumbsWithoutLeaf = ko.pureComputed(function () {
			if (self.navigation.breadCrumbs().length > 1) {
				return self.navigation.breadCrumbs().slice(0, self.navigation.breadCrumbs().length - 1);
			}
			else {
				return "";
			}
		});

        this.getFirstNonVirtualParentNode = function(node) {
          if (node == "UserDetail") {
                return self.getFirstNonVirtualParentNode("UserManagement");
            }
          if (node == "CountryDetails") {
                return self.getFirstNonVirtualParentNode("CountryItems");
            }
          if (node == "RegionDetails") {
                return self.getFirstNonVirtualParentNode("RegionItems");
            }
          if (node == "ChecklistResultAnswers") {
                return self.getFirstNonVirtualParentNode("PreOpCheckReport");
            }
          if (node == "DealerDetails") {
                return self.getFirstNonVirtualParentNode("DealerItems");
            }
          if (node == "CustomerDetails") {
                return self.getFirstNonVirtualParentNode("Customers");
            }
          if (node == "TimezoneDetails") {
                return self.getFirstNonVirtualParentNode("TimezoneItems");
            }
          
            return node;
        };

        this.setActiveNode = function(node) {
          self.navigation.isVehicleCalibrationReport3Active(false);            
          self.navigation.isUserEmailAlertSummary2Active(false);            
          self.navigation.isUserDetailActive(false);            
          self.navigation.isReportsActive(false);            
          self.navigation.isDashboardActive(false);            
          self.navigation.isCountryItemsActive(false);            
          self.navigation.isPreOpCheckReportActive(false);            
          self.navigation.isDriverAccessAbuseReportActive(false);            
          self.navigation.isVORReportActive(false);            
          self.navigation.isCurrentStatusReportActive(false);            
          self.navigation.isFloorPlanManagementActive(false);            
          self.navigation.isFloorPlanActive(false);            
          self.navigation.isImportActive(false);            
          self.navigation.isVehicleActive(false);            
          self.navigation.isCountryDetailsActive(false);            
          self.navigation.isNewUserActive(false);            
          self.navigation.isHelpActive(false);            
          self.navigation.isRegionItemsActive(false);            
          self.navigation.isServiceCheckReportActive(false);            
          self.navigation.isVehiclesActive(false);            
          self.navigation.isGO2FAConfigurationActive(false);            
          self.navigation.isUserManagementActive(false);            
          self.navigation.isSlamcoreActive(false);            
          self.navigation.isRegionDetailsActive(false);            
          self.navigation.isChecklistResultAnswersActive(false);            
          self.navigation.isAlertReportActive(false);            
          self.navigation.isDealerDetailsActive(false);            
          self.navigation.isDataExportToolActive(false);            
          self.navigation.isPedestrianDetectionReportActive(false);            
          self.navigation.isFirmwareActive(false);            
          self.navigation.isVehicleHireDehireReportActive(false);            
          self.navigation.isReportSchedulerActive(false);            
          self.navigation.isModelsActive(false);            
          self.navigation.isFeatureSubscriptionsActive(false);            
          self.navigation.isMyAccountActive(false);            
          self.navigation.isMachineUnlockReportActive(false);            
          self.navigation.isCustomerSSOActive(false);            
          self.navigation.isPathAnalysisViewActive(false);            
          self.navigation.isConnectionStatusDashboardActive(false);            
          self.navigation.isProficiencyReportActive(false);            
          self.navigation.isDevicesActive(false);            
          self.navigation.isAdminSettingsActive(false);            
          self.navigation.isTimezoneItemsActive(false);            
          self.navigation.isAccessGroupTemplatesActive(false);            
          self.navigation.isMoreReportsActive(false);            
          self.navigation.isSynchronizationStatusReportActive(false);            
          self.navigation.isBroadcastMessageReportActive(false);            
          self.navigation.isCustomersActive(false);            
          self.navigation.isExportActive(false);            
          self.navigation.isUserSummaryReport2Active(false);            
          self.navigation.isOnDemandAuthorisationReportActive(false);            
          self.navigation.isVehicleCalibrationReportActive(false);            
          self.navigation.isDealerReports2Active(false);            
          self.navigation.isLiveMapActive(false);            
          self.navigation.isDealerItemsActive(false);            
          self.navigation.isImpactReportActive(false);            
          self.navigation.isSuperAdminActive(false);            
          self.navigation.isLicenseExpiryReportActive(false);            
          self.navigation.isVehiclesGPSReportActive(false);            
          self.navigation.isGeneralProductivityReportActive(false);            
          self.navigation.isEmailSubscriptionReport2Active(false);            
          self.navigation.isCustomerDetailsActive(false);            
          self.navigation.isTimezoneDetailsActive(false);            
      
            var firstNonVirtualParentNode = self.getFirstNonVirtualParentNode(node);
            self.navigation["is" + firstNonVirtualParentNode + "Active"](true);
            self.openParentNavigationNodes(firstNonVirtualParentNode);
        };

        this.openParentNavigationNodes = function(node) {
          if (node == "VehicleCalibrationReport3") {
                $("#DealerReports2-collapse").collapse("show");
                self.navigation.isDealerReports2Active(true);
              self.openParentNavigationNodes("DealerReports2");
          }
    
          if (node == "UserEmailAlertSummary2") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "UserDetail") {
                $("#UserManagement-collapse").collapse("show");
                self.navigation.isUserManagementActive(true);
          }
    
          if (node == "CountryItems") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "PreOpCheckReport") {
                $("#Reports-collapse").collapse("show");
                self.navigation.isReportsActive(true);
          }
    
          if (node == "DriverAccessAbuseReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "VORReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "CurrentStatusReport") {
                $("#Reports-collapse").collapse("show");
                self.navigation.isReportsActive(true);
          }
    
          if (node == "FloorPlanManagement") {
                $("#Slamcore-collapse").collapse("show");
                self.navigation.isSlamcoreActive(true);
          }
    
          if (node == "Import") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "CountryDetails") {
                $("#CountryItems-collapse").collapse("show");
                self.navigation.isCountryItemsActive(true);
              self.openParentNavigationNodes("CountryItems");
          }
    
          if (node == "RegionItems") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "ServiceCheckReport") {
                $("#Reports-collapse").collapse("show");
                self.navigation.isReportsActive(true);
          }
    
          if (node == "GO2FAConfiguration") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "RegionDetails") {
                $("#RegionItems-collapse").collapse("show");
                self.navigation.isRegionItemsActive(true);
              self.openParentNavigationNodes("RegionItems");
          }
    
          if (node == "ChecklistResultAnswers") {
                $("#PreOpCheckReport-collapse").collapse("show");
                self.navigation.isPreOpCheckReportActive(true);
              self.openParentNavigationNodes("PreOpCheckReport");
          }
    
          if (node == "AlertReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "DealerDetails") {
                $("#DealerItems-collapse").collapse("show");
                self.navigation.isDealerItemsActive(true);
              self.openParentNavigationNodes("DealerItems");
          }
    
          if (node == "DataExportTool") {
                $("#Slamcore-collapse").collapse("show");
                self.navigation.isSlamcoreActive(true);
          }
    
          if (node == "PedestrianDetectionReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "Firmware") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "VehicleHireDehireReport") {
                $("#DealerReports2-collapse").collapse("show");
                self.navigation.isDealerReports2Active(true);
              self.openParentNavigationNodes("DealerReports2");
          }
    
          if (node == "ReportScheduler") {
                $("#Slamcore-collapse").collapse("show");
                self.navigation.isSlamcoreActive(true);
          }
    
          if (node == "Models") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "FeatureSubscriptions") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "MachineUnlockReport") {
                $("#Reports-collapse").collapse("show");
                self.navigation.isReportsActive(true);
          }
    
          if (node == "CustomerSSO") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "PathAnalysisView") {
                $("#Slamcore-collapse").collapse("show");
                self.navigation.isSlamcoreActive(true);
          }
    
          if (node == "ConnectionStatusDashboard") {
                $("#Slamcore-collapse").collapse("show");
                self.navigation.isSlamcoreActive(true);
          }
    
          if (node == "ProficiencyReport") {
                $("#Reports-collapse").collapse("show");
                self.navigation.isReportsActive(true);
          }
    
          if (node == "Devices") {
                $("#Slamcore-collapse").collapse("show");
                self.navigation.isSlamcoreActive(true);
          }
    
          if (node == "TimezoneItems") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "AccessGroupTemplates") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "MoreReports") {
                $("#Reports-collapse").collapse("show");
                self.navigation.isReportsActive(true);
          }
    
          if (node == "SynchronizationStatusReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "BroadcastMessageReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "Export") {
                $("#Reports-collapse").collapse("show");
                self.navigation.isReportsActive(true);
          }
    
          if (node == "UserSummaryReport2") {
                $("#DealerReports2-collapse").collapse("show");
                self.navigation.isDealerReports2Active(true);
              self.openParentNavigationNodes("DealerReports2");
          }
    
          if (node == "OnDemandAuthorisationReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "VehicleCalibrationReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "DealerReports2") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "LiveMap") {
                $("#Slamcore-collapse").collapse("show");
                self.navigation.isSlamcoreActive(true);
          }
    
          if (node == "DealerItems") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "ImpactReport") {
                $("#Reports-collapse").collapse("show");
                self.navigation.isReportsActive(true);
          }
    
          if (node == "SuperAdmin") {
                $("#AdminSettings-collapse").collapse("show");
                self.navigation.isAdminSettingsActive(true);
          }
    
          if (node == "LicenseExpiryReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "VehiclesGPSReport") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "GeneralProductivityReport") {
                $("#Reports-collapse").collapse("show");
                self.navigation.isReportsActive(true);
          }
    
          if (node == "EmailSubscriptionReport2") {
                $("#MoreReports-collapse").collapse("show");
                self.navigation.isMoreReportsActive(true);
              self.openParentNavigationNodes("MoreReports");
          }
    
          if (node == "CustomerDetails") {
                $("#Customers-collapse").collapse("show");
                self.navigation.isCustomersActive(true);
          }
    
          if (node == "TimezoneDetails") {
                $("#TimezoneItems-collapse").collapse("show");
                self.navigation.isTimezoneItemsActive(true);
              self.openParentNavigationNodes("TimezoneItems");
          }
    
        };

		this.navigation.updateMenu = function () {
			self.updateDefaultNavigationNodeVisibility();

			self.navigation.isVehicleCalibrationReport3Visible(self.getNodeVisibility('VehicleCalibrationReport3'));
			self.navigation.isUserEmailAlertSummary2Visible(self.getNodeVisibility('UserEmailAlertSummary2'));
			self.navigation.isUserDetailVisible(self.getNodeVisibility('UserDetail'));
			self.navigation.isReportsVisible(self.getNodeVisibility('Reports'));
			self.navigation.isDashboardVisible(self.getNodeVisibility('Dashboard'));
			self.navigation.isCountryItemsVisible(self.getNodeVisibility('CountryItems'));
			self.navigation.isPreOpCheckReportVisible(self.getNodeVisibility('PreOpCheckReport'));
			self.navigation.isDriverAccessAbuseReportVisible(self.getNodeVisibility('DriverAccessAbuseReport'));
			self.navigation.isVORReportVisible(self.getNodeVisibility('VORReport'));
			self.navigation.isCurrentStatusReportVisible(self.getNodeVisibility('CurrentStatusReport'));
			self.navigation.isFloorPlanManagementVisible(self.getNodeVisibility('FloorPlanManagement'));
			self.navigation.isFloorPlanVisible(self.getNodeVisibility('FloorPlan'));
			self.navigation.isImportVisible(self.getNodeVisibility('Import'));
			self.navigation.isVehicleVisible(self.getNodeVisibility('Vehicle'));
			self.navigation.isCountryDetailsVisible(self.getNodeVisibility('CountryDetails'));
			self.navigation.isNewUserVisible(self.getNodeVisibility('NewUser'));
			self.navigation.isHelpVisible(self.getNodeVisibility('Help'));
			self.navigation.isRegionItemsVisible(self.getNodeVisibility('RegionItems'));
			self.navigation.isServiceCheckReportVisible(self.getNodeVisibility('ServiceCheckReport'));
			self.navigation.isVehiclesVisible(self.getNodeVisibility('Vehicles'));
			self.navigation.isGO2FAConfigurationVisible(self.getNodeVisibility('GO2FAConfiguration'));
			self.navigation.isUserManagementVisible(self.getNodeVisibility('UserManagement'));
			self.navigation.isSlamcoreVisible(self.getNodeVisibility('Slamcore'));
			self.navigation.isRegionDetailsVisible(self.getNodeVisibility('RegionDetails'));
			self.navigation.isChecklistResultAnswersVisible(self.getNodeVisibility('ChecklistResultAnswers'));
			self.navigation.isAlertReportVisible(self.getNodeVisibility('AlertReport'));
			self.navigation.isDealerDetailsVisible(self.getNodeVisibility('DealerDetails'));
			self.navigation.isDataExportToolVisible(self.getNodeVisibility('DataExportTool'));
			self.navigation.isPedestrianDetectionReportVisible(self.getNodeVisibility('PedestrianDetectionReport'));
			self.navigation.isFirmwareVisible(self.getNodeVisibility('Firmware'));
			self.navigation.isVehicleHireDehireReportVisible(self.getNodeVisibility('VehicleHireDehireReport'));
			self.navigation.isReportSchedulerVisible(self.getNodeVisibility('ReportScheduler'));
			self.navigation.isModelsVisible(self.getNodeVisibility('Models'));
			self.navigation.isFeatureSubscriptionsVisible(self.getNodeVisibility('FeatureSubscriptions'));
			self.navigation.isMyAccountVisible(self.getNodeVisibility('MyAccount'));
			self.navigation.isMachineUnlockReportVisible(self.getNodeVisibility('MachineUnlockReport'));
			self.navigation.isCustomerSSOVisible(self.getNodeVisibility('CustomerSSO'));
			self.navigation.isPathAnalysisViewVisible(self.getNodeVisibility('PathAnalysisView'));
			self.navigation.isConnectionStatusDashboardVisible(self.getNodeVisibility('ConnectionStatusDashboard'));
			self.navigation.isProficiencyReportVisible(self.getNodeVisibility('ProficiencyReport'));
			self.navigation.isDevicesVisible(self.getNodeVisibility('Devices'));
			self.navigation.isAdminSettingsVisible(self.getNodeVisibility('AdminSettings'));
			self.navigation.isTimezoneItemsVisible(self.getNodeVisibility('TimezoneItems'));
			self.navigation.isAccessGroupTemplatesVisible(self.getNodeVisibility('AccessGroupTemplates'));
			self.navigation.isMoreReportsVisible(self.getNodeVisibility('MoreReports'));
			self.navigation.isSynchronizationStatusReportVisible(self.getNodeVisibility('SynchronizationStatusReport'));
			self.navigation.isBroadcastMessageReportVisible(self.getNodeVisibility('BroadcastMessageReport'));
			self.navigation.isCustomersVisible(self.getNodeVisibility('Customers'));
			self.navigation.isExportVisible(self.getNodeVisibility('Export'));
			self.navigation.isUserSummaryReport2Visible(self.getNodeVisibility('UserSummaryReport2'));
			self.navigation.isOnDemandAuthorisationReportVisible(self.getNodeVisibility('OnDemandAuthorisationReport'));
			self.navigation.isVehicleCalibrationReportVisible(self.getNodeVisibility('VehicleCalibrationReport'));
			self.navigation.isDealerReports2Visible(self.getNodeVisibility('DealerReports2'));
			self.navigation.isLiveMapVisible(self.getNodeVisibility('LiveMap'));
			self.navigation.isDealerItemsVisible(self.getNodeVisibility('DealerItems'));
			self.navigation.isImpactReportVisible(self.getNodeVisibility('ImpactReport'));
			self.navigation.isSuperAdminVisible(self.getNodeVisibility('SuperAdmin'));
			self.navigation.isLicenseExpiryReportVisible(self.getNodeVisibility('LicenseExpiryReport'));
			self.navigation.isVehiclesGPSReportVisible(self.getNodeVisibility('VehiclesGPSReport'));
			self.navigation.isGeneralProductivityReportVisible(self.getNodeVisibility('GeneralProductivityReport'));
			self.navigation.isEmailSubscriptionReport2Visible(self.getNodeVisibility('EmailSubscriptionReport2'));
			self.navigation.isCustomerDetailsVisible(self.getNodeVisibility('CustomerDetails'));
			self.navigation.isTimezoneDetailsVisible(self.getNodeVisibility('TimezoneDetails'));
			
			// Custom updates (to override generated behavior in case of complex rules)
			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehicleCalibrationReport3Enabled !== undefined) 
				self.viewModelCustom.updateIsVehicleCalibrationReport3Enabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehicleCalibrationReport3Visible !== undefined) 
				self.viewModelCustom.updateIsVehicleCalibrationReport3Visible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsUserEmailAlertSummary2Enabled !== undefined) 
				self.viewModelCustom.updateIsUserEmailAlertSummary2Enabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsUserEmailAlertSummary2Visible !== undefined) 
				self.viewModelCustom.updateIsUserEmailAlertSummary2Visible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsUserDetailEnabled !== undefined) 
				self.viewModelCustom.updateIsUserDetailEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsUserDetailVisible !== undefined) 
				self.viewModelCustom.updateIsUserDetailVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsReportsEnabled !== undefined) 
				self.viewModelCustom.updateIsReportsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsReportsVisible !== undefined) 
				self.viewModelCustom.updateIsReportsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDashboardEnabled !== undefined) 
				self.viewModelCustom.updateIsDashboardEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDashboardVisible !== undefined) 
				self.viewModelCustom.updateIsDashboardVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCountryItemsEnabled !== undefined) 
				self.viewModelCustom.updateIsCountryItemsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCountryItemsVisible !== undefined) 
				self.viewModelCustom.updateIsCountryItemsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsPreOpCheckReportEnabled !== undefined) 
				self.viewModelCustom.updateIsPreOpCheckReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsPreOpCheckReportVisible !== undefined) 
				self.viewModelCustom.updateIsPreOpCheckReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDriverAccessAbuseReportEnabled !== undefined) 
				self.viewModelCustom.updateIsDriverAccessAbuseReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDriverAccessAbuseReportVisible !== undefined) 
				self.viewModelCustom.updateIsDriverAccessAbuseReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVORReportEnabled !== undefined) 
				self.viewModelCustom.updateIsVORReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVORReportVisible !== undefined) 
				self.viewModelCustom.updateIsVORReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCurrentStatusReportEnabled !== undefined) 
				self.viewModelCustom.updateIsCurrentStatusReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCurrentStatusReportVisible !== undefined) 
				self.viewModelCustom.updateIsCurrentStatusReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsFloorPlanManagementEnabled !== undefined) 
				self.viewModelCustom.updateIsFloorPlanManagementEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsFloorPlanManagementVisible !== undefined) 
				self.viewModelCustom.updateIsFloorPlanManagementVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsFloorPlanEnabled !== undefined) 
				self.viewModelCustom.updateIsFloorPlanEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsFloorPlanVisible !== undefined) 
				self.viewModelCustom.updateIsFloorPlanVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsImportEnabled !== undefined) 
				self.viewModelCustom.updateIsImportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsImportVisible !== undefined) 
				self.viewModelCustom.updateIsImportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehicleEnabled !== undefined) 
				self.viewModelCustom.updateIsVehicleEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehicleVisible !== undefined) 
				self.viewModelCustom.updateIsVehicleVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCountryDetailsEnabled !== undefined) 
				self.viewModelCustom.updateIsCountryDetailsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCountryDetailsVisible !== undefined) 
				self.viewModelCustom.updateIsCountryDetailsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsNewUserEnabled !== undefined) 
				self.viewModelCustom.updateIsNewUserEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsNewUserVisible !== undefined) 
				self.viewModelCustom.updateIsNewUserVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsHelpEnabled !== undefined) 
				self.viewModelCustom.updateIsHelpEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsHelpVisible !== undefined) 
				self.viewModelCustom.updateIsHelpVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsRegionItemsEnabled !== undefined) 
				self.viewModelCustom.updateIsRegionItemsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsRegionItemsVisible !== undefined) 
				self.viewModelCustom.updateIsRegionItemsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsServiceCheckReportEnabled !== undefined) 
				self.viewModelCustom.updateIsServiceCheckReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsServiceCheckReportVisible !== undefined) 
				self.viewModelCustom.updateIsServiceCheckReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehiclesEnabled !== undefined) 
				self.viewModelCustom.updateIsVehiclesEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehiclesVisible !== undefined) 
				self.viewModelCustom.updateIsVehiclesVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsGO2FAConfigurationEnabled !== undefined) 
				self.viewModelCustom.updateIsGO2FAConfigurationEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsGO2FAConfigurationVisible !== undefined) 
				self.viewModelCustom.updateIsGO2FAConfigurationVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsUserManagementEnabled !== undefined) 
				self.viewModelCustom.updateIsUserManagementEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsUserManagementVisible !== undefined) 
				self.viewModelCustom.updateIsUserManagementVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsSlamcoreEnabled !== undefined) 
				self.viewModelCustom.updateIsSlamcoreEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsSlamcoreVisible !== undefined) 
				self.viewModelCustom.updateIsSlamcoreVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsRegionDetailsEnabled !== undefined) 
				self.viewModelCustom.updateIsRegionDetailsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsRegionDetailsVisible !== undefined) 
				self.viewModelCustom.updateIsRegionDetailsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsChecklistResultAnswersEnabled !== undefined) 
				self.viewModelCustom.updateIsChecklistResultAnswersEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsChecklistResultAnswersVisible !== undefined) 
				self.viewModelCustom.updateIsChecklistResultAnswersVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsAlertReportEnabled !== undefined) 
				self.viewModelCustom.updateIsAlertReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsAlertReportVisible !== undefined) 
				self.viewModelCustom.updateIsAlertReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDealerDetailsEnabled !== undefined) 
				self.viewModelCustom.updateIsDealerDetailsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDealerDetailsVisible !== undefined) 
				self.viewModelCustom.updateIsDealerDetailsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDataExportToolEnabled !== undefined) 
				self.viewModelCustom.updateIsDataExportToolEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDataExportToolVisible !== undefined) 
				self.viewModelCustom.updateIsDataExportToolVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsPedestrianDetectionReportEnabled !== undefined) 
				self.viewModelCustom.updateIsPedestrianDetectionReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsPedestrianDetectionReportVisible !== undefined) 
				self.viewModelCustom.updateIsPedestrianDetectionReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsFirmwareEnabled !== undefined) 
				self.viewModelCustom.updateIsFirmwareEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsFirmwareVisible !== undefined) 
				self.viewModelCustom.updateIsFirmwareVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehicleHireDehireReportEnabled !== undefined) 
				self.viewModelCustom.updateIsVehicleHireDehireReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehicleHireDehireReportVisible !== undefined) 
				self.viewModelCustom.updateIsVehicleHireDehireReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsReportSchedulerEnabled !== undefined) 
				self.viewModelCustom.updateIsReportSchedulerEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsReportSchedulerVisible !== undefined) 
				self.viewModelCustom.updateIsReportSchedulerVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsModelsEnabled !== undefined) 
				self.viewModelCustom.updateIsModelsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsModelsVisible !== undefined) 
				self.viewModelCustom.updateIsModelsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsFeatureSubscriptionsEnabled !== undefined) 
				self.viewModelCustom.updateIsFeatureSubscriptionsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsFeatureSubscriptionsVisible !== undefined) 
				self.viewModelCustom.updateIsFeatureSubscriptionsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsMyAccountEnabled !== undefined) 
				self.viewModelCustom.updateIsMyAccountEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsMyAccountVisible !== undefined) 
				self.viewModelCustom.updateIsMyAccountVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsMachineUnlockReportEnabled !== undefined) 
				self.viewModelCustom.updateIsMachineUnlockReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsMachineUnlockReportVisible !== undefined) 
				self.viewModelCustom.updateIsMachineUnlockReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCustomerSSOEnabled !== undefined) 
				self.viewModelCustom.updateIsCustomerSSOEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCustomerSSOVisible !== undefined) 
				self.viewModelCustom.updateIsCustomerSSOVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsPathAnalysisViewEnabled !== undefined) 
				self.viewModelCustom.updateIsPathAnalysisViewEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsPathAnalysisViewVisible !== undefined) 
				self.viewModelCustom.updateIsPathAnalysisViewVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsConnectionStatusDashboardEnabled !== undefined) 
				self.viewModelCustom.updateIsConnectionStatusDashboardEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsConnectionStatusDashboardVisible !== undefined) 
				self.viewModelCustom.updateIsConnectionStatusDashboardVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsProficiencyReportEnabled !== undefined) 
				self.viewModelCustom.updateIsProficiencyReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsProficiencyReportVisible !== undefined) 
				self.viewModelCustom.updateIsProficiencyReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDevicesEnabled !== undefined) 
				self.viewModelCustom.updateIsDevicesEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDevicesVisible !== undefined) 
				self.viewModelCustom.updateIsDevicesVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsAdminSettingsEnabled !== undefined) 
				self.viewModelCustom.updateIsAdminSettingsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsAdminSettingsVisible !== undefined) 
				self.viewModelCustom.updateIsAdminSettingsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsTimezoneItemsEnabled !== undefined) 
				self.viewModelCustom.updateIsTimezoneItemsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsTimezoneItemsVisible !== undefined) 
				self.viewModelCustom.updateIsTimezoneItemsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsAccessGroupTemplatesEnabled !== undefined) 
				self.viewModelCustom.updateIsAccessGroupTemplatesEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsAccessGroupTemplatesVisible !== undefined) 
				self.viewModelCustom.updateIsAccessGroupTemplatesVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsMoreReportsEnabled !== undefined) 
				self.viewModelCustom.updateIsMoreReportsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsMoreReportsVisible !== undefined) 
				self.viewModelCustom.updateIsMoreReportsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsSynchronizationStatusReportEnabled !== undefined) 
				self.viewModelCustom.updateIsSynchronizationStatusReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsSynchronizationStatusReportVisible !== undefined) 
				self.viewModelCustom.updateIsSynchronizationStatusReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsBroadcastMessageReportEnabled !== undefined) 
				self.viewModelCustom.updateIsBroadcastMessageReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsBroadcastMessageReportVisible !== undefined) 
				self.viewModelCustom.updateIsBroadcastMessageReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCustomersEnabled !== undefined) 
				self.viewModelCustom.updateIsCustomersEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCustomersVisible !== undefined) 
				self.viewModelCustom.updateIsCustomersVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsExportEnabled !== undefined) 
				self.viewModelCustom.updateIsExportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsExportVisible !== undefined) 
				self.viewModelCustom.updateIsExportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsUserSummaryReport2Enabled !== undefined) 
				self.viewModelCustom.updateIsUserSummaryReport2Enabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsUserSummaryReport2Visible !== undefined) 
				self.viewModelCustom.updateIsUserSummaryReport2Visible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsOnDemandAuthorisationReportEnabled !== undefined) 
				self.viewModelCustom.updateIsOnDemandAuthorisationReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsOnDemandAuthorisationReportVisible !== undefined) 
				self.viewModelCustom.updateIsOnDemandAuthorisationReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehicleCalibrationReportEnabled !== undefined) 
				self.viewModelCustom.updateIsVehicleCalibrationReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehicleCalibrationReportVisible !== undefined) 
				self.viewModelCustom.updateIsVehicleCalibrationReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDealerReports2Enabled !== undefined) 
				self.viewModelCustom.updateIsDealerReports2Enabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDealerReports2Visible !== undefined) 
				self.viewModelCustom.updateIsDealerReports2Visible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsLiveMapEnabled !== undefined) 
				self.viewModelCustom.updateIsLiveMapEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsLiveMapVisible !== undefined) 
				self.viewModelCustom.updateIsLiveMapVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDealerItemsEnabled !== undefined) 
				self.viewModelCustom.updateIsDealerItemsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsDealerItemsVisible !== undefined) 
				self.viewModelCustom.updateIsDealerItemsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsImpactReportEnabled !== undefined) 
				self.viewModelCustom.updateIsImpactReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsImpactReportVisible !== undefined) 
				self.viewModelCustom.updateIsImpactReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsSuperAdminEnabled !== undefined) 
				self.viewModelCustom.updateIsSuperAdminEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsSuperAdminVisible !== undefined) 
				self.viewModelCustom.updateIsSuperAdminVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsLicenseExpiryReportEnabled !== undefined) 
				self.viewModelCustom.updateIsLicenseExpiryReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsLicenseExpiryReportVisible !== undefined) 
				self.viewModelCustom.updateIsLicenseExpiryReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehiclesGPSReportEnabled !== undefined) 
				self.viewModelCustom.updateIsVehiclesGPSReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsVehiclesGPSReportVisible !== undefined) 
				self.viewModelCustom.updateIsVehiclesGPSReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsGeneralProductivityReportEnabled !== undefined) 
				self.viewModelCustom.updateIsGeneralProductivityReportEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsGeneralProductivityReportVisible !== undefined) 
				self.viewModelCustom.updateIsGeneralProductivityReportVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsEmailSubscriptionReport2Enabled !== undefined) 
				self.viewModelCustom.updateIsEmailSubscriptionReport2Enabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsEmailSubscriptionReport2Visible !== undefined) 
				self.viewModelCustom.updateIsEmailSubscriptionReport2Visible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCustomerDetailsEnabled !== undefined) 
				self.viewModelCustom.updateIsCustomerDetailsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsCustomerDetailsVisible !== undefined) 
				self.viewModelCustom.updateIsCustomerDetailsVisible();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsTimezoneDetailsEnabled !== undefined) 
				self.viewModelCustom.updateIsTimezoneDetailsEnabled();

			if (self.viewModelCustom !== undefined && self.viewModelCustom.updateIsTimezoneDetailsVisible !== undefined) 
				self.viewModelCustom.updateIsTimezoneDetailsVisible();

		};

        this.commands = {        
            navigateTo: function (viewName) {
                GO.removeAllFiltersFromQueryStringFromUrl();
                self.setActiveNode(viewName);
                self.controller.navigateTo(viewName);
            },
            navigateToUrl: function (breadcrumb) {
                if (breadcrumb.isactive) {
					window.location.hash = breadcrumb.hash;
                    GO.removeAllFiltersFromQueryStringFromUrl(); 
                }
            }
		};
        this.security = {
            isLoggedIn: ko.observable(false),
			jwtToken: ko.observable(null),
			currentUserClaims: ko.observable(null),
            logOut: function () {
                self.logOut();
            }
        };

		this.security.currentUserName = ko.computed( function() {
			return self.security.currentUserClaims() == null ? null : self.security.currentUserClaims().unique_name;
		});
        
        this.onLogOut = function (data, reason) {		
			self.security.jwtToken(null);
			self.security.currentUserClaims(null);
			self.security.isLoggedIn(false);

			setTimeout(function () {
				self.controller.redirectToLoginPage(reason, true);
			}, 1000);  // give time to refresh the cookies		
        };

        this.logOut = function (reason) {
			if (FleetXQ.Web.Application.AuthenticationVirtualPath) {
				// Build the logout URL for the authentication server
				var origin = FleetXQ.Web.Application.BaseURL.match(/^https?:\/\/[^\/]+/)[0];
				var baseUrl = origin + FleetXQ.Web.Application.AuthenticationVirtualPath;

				// First get CSRF token from the target application
				fetch(baseUrl + '/dataset/api/goservices/csrf-token', {
					method: 'GET',
					credentials: 'include'
				}).then(function(response) {
					if (!response.ok) {
						alert('Failed to get CSRF token');
						return;
					}
					return response.json();
				}).then(function(data) {
					if (!data || !data.csrfToken) {
						alert('Invalid CSRF token response');
						return;
					}

					// Now make the logout request with the obtained CSRF token
					var logoutUrl = baseUrl + "/api/gosecurityprovider/logout";
					var formData = new FormData();
					formData.append('useCookies', 'true');
					return fetch(logoutUrl, {
						method: 'POST',
						credentials: 'include',
						headers: {
							'X-CSRF-TOKEN': data.csrfToken
						},
						body: formData
					});
				}).then(function(response) {
					if (!response) return;
					if (!response.ok) {
						alert('Logout failed');
						return;
					}
					// After logout, redirect to login page or home
					setTimeout(function () {
						self.controller.redirectToLoginPage(reason, true);
					}, 1000);
				}).catch(function (error) {
					GO.log('Logout error:', error);
					alert('Logout failed');
				});
			} else {
				// Fallback: use the default logout logic
				var configuration = {
					contextId: self.contextId,
					useCookies: true,
					successHandler: function (data) { self.onLogOut(data, reason); },
					errorHandler: function (data) { alert('Logout failed'); }
				};
				self.controller.getProxyForComponent("GOSecurityProvider").LogOut(configuration);
			}
        };

		/// Node Visibility
        this.defaultNodeVisibility = false;

		/// Summary of the permissioning logic:
		/// User is in at least one authorizing role for the resource => authorised
		/// All User roles are denied access to the resource => denied
		/// Else the default access setting for the resource is used. (so e.g. if no rules for this role, or if not all roles explicitly denied, then the default is used)
		this.getNodeVisibility = function (nodeName) {
            var currentClaims = self.security.currentUserClaims() == null ? null : self.security.currentUserClaims();
            if (currentClaims !== null) {
                var currentRoles = currentClaims.role.split(',');

				switch (nodeName)
				{
					case 'CountryItems':
					{
						// Roles "DealerAdmin" are denied access to CountryItems 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'Import':
					{
						// Roles "DealerAdmin" are denied access to Import 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'RegionItems':
					{
						// Roles "DealerAdmin" are denied access to RegionItems 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'GO2FAConfiguration':
					{
						// Roles "DealerAdmin" are denied access to GO2FAConfiguration 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'Firmware':
					{
						// Roles "DealerAdmin" are denied access to Firmware 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'Models':
					{
						// Roles "DealerAdmin" are denied access to Models 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'FeatureSubscriptions':
					{
						// Roles "DealerAdmin" are denied access to FeatureSubscriptions 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'CustomerSSO':
					{
						// Roles "DealerAdmin" are denied access to CustomerSSO 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'AdminSettings':
					{
						// Roles "Customer" are denied access to AdminSettings 
						var deniedRoles = [ "Customer" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'TimezoneItems':
					{
						// Roles "DealerAdmin" are denied access to TimezoneItems 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					case 'AccessGroupTemplates':
					{
						// Roles "DealerAdmin" are denied access to AccessGroupTemplates 
						var deniedRoles = [ "DealerAdmin" ];
						var deniedRoleCount = 0;

						for (var i = 0; i < currentRoles.length; ++i) {
							var role = currentRoles[i];
							if (deniedRoles.indexOf(role) > -1) {
								++deniedRoleCount;
							}
						}

						// all roles denied?
						if (deniedRoleCount == currentRoles.length) {
							return false;
						}

						break;
					}
					default:
						break;
				}
			}

			// If we get here, it means there is no explicit (override) node authorization rule for at least one of the user's roles
			// So use the default setting
			return self.defaultNodeVisibility;
		};
			
		this.updateDefaultNavigationNodeVisibility = function () {
            var visibility = false;
			var currentClaims = self.security.currentUserClaims() == null ? null : self.security.currentUserClaims();
            if (currentClaims !== null) {
				var currentRoles = currentClaims.role.split(',');
				if (currentRoles.indexOf('Customer') > -1 || currentRoles.indexOf('Administrator') > -1 || currentRoles.indexOf('DealerAdmin') > -1) {
					visibility = true;
				}
			} 
			this.defaultNodeVisibility = visibility;
		};

		this.updateMenu = function () {
			self.navigation.updateMenu ();
			if(self.viewModelCustom !== undefined && self.viewModelCustom.updateMenu !== undefined)
				self.viewModelCustom.updateMenu();
		};

		// Language management - use shared service
		this.availableLanguages = FleetXQ.Web.Services.LanguageService.availableLanguages;
		this.currentLanguage = FleetXQ.Web.Services.LanguageService.currentLanguage;

		// Language switcher functions
		this.changeLanguage = function(language) {
			FleetXQ.Web.Services.LanguageService.changeLanguage(language);
		};

		this.isLanguageActive = function(language) {
			return FleetXQ.Web.Services.LanguageService.isLanguageActive(language);
		};

        this.initialize = function () {
			// Set up integration with TokenRenewal service
			// Set up callbacks for token management events
			FleetXQ.Web.Application.TokenRenewal.setCallbacks(
				function(authState) {
					// Login state change callback
					self.security.isLoggedIn(authState.isLoggedIn);
					self.security.jwtToken(authState.jwtToken);
					self.security.currentUserClaims(authState.currentUserClaims);
				},
				function(reason) {
					// Logout required callback
					self.logOut(reason);
				}
			);
			
			// Immediately synchronize with current TokenRenewal state to fix timing issues
			// This ensures ApplicationViewModel has current claims before DataStore operations
            var currentState = {
                isLoggedIn: FleetXQ.Web.Application.TokenRenewal.security.isLoggedIn(),
                jwtToken: FleetXQ.Web.Application.TokenRenewal.security.jwtToken(),
                currentUserClaims: FleetXQ.Web.Application.TokenRenewal.security.currentUserClaims()
            };
            
            if (currentState.isLoggedIn && currentState.currentUserClaims) {
                self.security.isLoggedIn(currentState.isLoggedIn);
                self.security.jwtToken(currentState.jwtToken);
                self.security.currentUserClaims(currentState.currentUserClaims);
            }
			
			// Initialize languages using the shared service
			FleetXQ.Web.Services.LanguageService.initializeLanguages().then(() => {
				if (self.viewModelCustom !== undefined && self.viewModelCustom.initialize !== undefined) {
					self.viewModelCustom.initialize();
				} else {
					self.onCustomInitialized();
				}
			});
        }

        this.onCustomInitialized = function () {
            
			self.navigation.updateMenu();
		};

        this.release = function () {
            // Note: We intentionally do NOT stop TokenRenewal timers here
            // TokenRenewal is a singleton service that should continue running across navigation
            // Only the callbacks are updated when a new ApplicationViewModel is created
            
            ko.cleanNode($('#form')[0]);
        };

		this.initialize();

        ko.applyBindings(self, $('#form')[0]);    

		this.initialized = true;    
    };

}(window)); 