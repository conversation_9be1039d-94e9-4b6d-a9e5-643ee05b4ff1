﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<joined-subclass 
		name="ORMSlamcoreDeviceFilter" 
		table="[SlamcoreDeviceFilter]" 
		schema="[dbo]"
		extends="ORMDashboardFilter" 
	>

		<key>
			<column name = "`Id`"></column>
		</key>


		<property name="Status" >
            <column name="`Status`" sql-type="int" not-null="false" />
        </property> 

 




    </joined-subclass> 

</hibernate-mapping>