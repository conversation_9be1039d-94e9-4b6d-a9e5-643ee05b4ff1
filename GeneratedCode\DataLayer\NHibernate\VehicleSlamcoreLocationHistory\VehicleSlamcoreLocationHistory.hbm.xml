﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMVehicleSlamcoreLocationHistory" 
		table="[VehicleSlamcoreLocationHistory]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="AcquisitionDateTime" >
            <column name="`AcquisitionDateTime`" sql-type="datetime" not-null="true" />
        </property> 
		<property name="Bearing" >
            <column name="`Bearing`" sql-type="decimal (8, 4)" not-null="false" precision="8" scale="4"/>
        </property> 
		<property name="EventType" >
            <column name="`EventType`" sql-type="int" not-null="false" />
        </property> 
		<property name="ReferenceFrameCategory" >
            <column name="`ReferenceFrameCategory`" sql-type="int" not-null="true" />
        </property> 
		<property name="Speed" >
            <column name="`Speed`" sql-type="decimal (8, 6)" not-null="false" precision="8" scale="6"/>
        </property> 
		<property name="Status" >
            <column name="`Status`" sql-type="int" not-null="true" />
        </property> 
		<property name="TrailSequence" >
            <column name="`TrailSequence`" sql-type="int" not-null="false" />
        </property> 
		<property name="WOrientation" >
            <column name="`WOrientation`" sql-type="decimal (20, 17)" not-null="true" precision="20" scale="17"/>
        </property> 
		<property name="XOrientation" >
            <column name="`XOrientation`" sql-type="decimal (20, 17)" not-null="true" precision="20" scale="17"/>
        </property> 
		<property name="XPosition" >
            <column name="`XPosition`" sql-type="decimal (20, 17)" not-null="true" precision="20" scale="17"/>
        </property> 
		<property name="YOrientation" >
            <column name="`YOrientation`" sql-type="decimal (20, 17)" not-null="true" precision="20" scale="17"/>
        </property> 
		<property name="YPosition" >
            <column name="`YPosition`" sql-type="decimal (20, 17)" not-null="true" precision="20" scale="17"/>
        </property> 
		<property name="ZPosition" >
            <column name="`ZPosition`" sql-type="decimal (20, 17)" not-null="true" precision="20" scale="17"/>
        </property> 

		
		<!-- many-to-one SlamcoreDevice -->
		<property name="SlamcoreDeviceId" type="System.Guid" not-null="true" formula = "[SlamcoreDeviceId]"></property>  
		<many-to-one name="SlamcoreDevice"  > 
			<column name="`SlamcoreDeviceId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
 




    </class> 

</hibernate-mapping>