﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>1.0.0.6765</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Business Layer Server Components</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
    <PackageReference Include="GenerativeObjects.Services" Version="2.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="NLog" Version="5.1.4" />
    <PackageReference Include="ClosedXML" Version="0.101.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Features\Security\Common\FleetXQ.Features.Security.Common.csproj" />
    <ProjectReference Include="..\Resources\FleetXQ.Data.Resources.csproj" />
    <ProjectReference Include="..\DataLayerDataProviders\FleetXQ.Data.DataProviders.csproj" />
    <ProjectReference Include="..\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj" />
    <ProjectReference Include="..\BusinessLayer\FleetXQ.BusinessLayer.csproj" />
  </ItemGroup>
</Project>