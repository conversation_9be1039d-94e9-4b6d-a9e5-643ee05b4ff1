﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

using GenerativeObjects.Practices;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Storage.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer.Extensions;
using Microsoft.AspNetCore.Antiforgery;

using FleetXQ.Data.DataObjects;

namespace FleetXQ.ServiceLayer.EntityApiControllers
{
    [ApiController]
    [ServiceFilter(typeof(ObjectGraphOrDataSetSerializationFilterAttribute))]
    public class CustomerApiController : EntityApiController<CustomerDataObject, CustomerContainer, CustomerCollectionContainer>
    {
        public CustomerApiController(IServiceProvider serviceProvider, IConfiguration configuration, ISettingsProvider settingsProvider, IDataProviderTransaction dataProviderTransaction, IDataProvider<CustomerDataObject> dataProvider, IDataObjectFactory<CustomerDataObject> dataObjectFactory, IEntityApiExtensionProvider<CustomerDataObject> extensions, IEntityDataProvider entityDataProvider, IApiFilterArgumentBuilder apiFilterArgumentBuilder, IAntiforgery antiforgery) : base(serviceProvider, configuration, settingsProvider, dataProviderTransaction, dataProvider, dataObjectFactory, extensions, entityDataProvider, apiFilterArgumentBuilder, antiforgery) { }

        /// <summary>
        /// Get a Customer instance by primary key and return data in dataset format
        /// </summary>
      /// <param name="id">Id of the Customer entity to get</param>
      /// <param name="include">list of related data to fetch with the Customer, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Customer are : DashboardVehicleCardStoreProcedureItems, DriverLicenseExpiryViewItems, TodaysPreopCheckStoreProcedureItems, ImpactFrequencyPerWeekDayViewItems, CurrentDriverStatusChartViewItems, SlamcoreDeviceItems, CustomerSnapshotItems, DriverItems, PersonItems, DashboardCardViewItems, CustomerToModelItems, CustomerToPersonViewItems, CustomerModelItems, VehicleUtilizationLastTwelveHoursViewItems, AccessGroupItems, DashboardDriverCardStoreProcedureItems, TodaysPreopCheckViewItems, CustomerFeatureSubscription, TodaysImpactStoreProcedureItems, CurrentVehicleStatusChartViewItems, DashboardFilterItems, TodaysImpactViewItems, EmailGroupsItems, Dealer, CustomerSSODetailItems, ImpactFrequencyPerWeekMonthViewItems, IncompletedChecklistViewItems, Country, DepartmentItems, ContactPersonInformation, GoUserToCustomerItems, VehicleItems, CustomerAudit, CustomerPreOperationalChecklistTemplateItems, DashboardVehicleCardViewItems, ChecklistStatusViewItems, LoggedHoursVersusSeatHoursViewItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, ImpactFrequencyPerTimeSlotViewItems, Sites, DriverLicenseExpiryStoreProcedureItems </param>
        /// <returns>Customer entity</returns>
        [HttpGet]
        [Route("dataset/api/customer/byid/{id}")]
        public async Task<ActionResult<CustomerContainer>> GetDataSetById(System.Guid id, string include)
        {
            return await DoGetDataSetByIdAsync(new List<string> { id.ToString() }, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Get a Customer instance by primary key and return data in json structured format
        /// </summary>
      /// <param name="id">Id of the Customer entity to get</param>
      /// <param name="include">list of related data to fetch with the Customer, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Customer are : DashboardVehicleCardStoreProcedureItems, DriverLicenseExpiryViewItems, TodaysPreopCheckStoreProcedureItems, ImpactFrequencyPerWeekDayViewItems, CurrentDriverStatusChartViewItems, SlamcoreDeviceItems, CustomerSnapshotItems, DriverItems, PersonItems, DashboardCardViewItems, CustomerToModelItems, CustomerToPersonViewItems, CustomerModelItems, VehicleUtilizationLastTwelveHoursViewItems, AccessGroupItems, DashboardDriverCardStoreProcedureItems, TodaysPreopCheckViewItems, CustomerFeatureSubscription, TodaysImpactStoreProcedureItems, CurrentVehicleStatusChartViewItems, DashboardFilterItems, TodaysImpactViewItems, EmailGroupsItems, Dealer, CustomerSSODetailItems, ImpactFrequencyPerWeekMonthViewItems, IncompletedChecklistViewItems, Country, DepartmentItems, ContactPersonInformation, GoUserToCustomerItems, VehicleItems, CustomerAudit, CustomerPreOperationalChecklistTemplateItems, DashboardVehicleCardViewItems, ChecklistStatusViewItems, LoggedHoursVersusSeatHoursViewItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, ImpactFrequencyPerTimeSlotViewItems, Sites, DriverLicenseExpiryStoreProcedureItems </param>
        /// <param name="byRef">Setting to define how to handle reference loop. If byRef is true, reference loops are handled by serializing the first occurrence of the object and replacing subsequent occurrences with a reference. If ByRef is false, reference loops are ignored. Default is false when byRef is not specified</param>
        /// <returns>Customer entity</returns>
        [HttpGet]
        [Route("api/customer/byid/{id}")]
        public async Task<ActionResult<CustomerDataObject>> GetById(System.Guid id, string include, bool? byRef)
        {
            return await DoGetByIdAsync(new List<string> { id.ToString() }, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Get a list of Customer instances and return data in dataset format
        /// </summary>
        /// <param name="include">list of related data to fetch with the Customer, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Customer are : DashboardVehicleCardStoreProcedureItems, DriverLicenseExpiryViewItems, TodaysPreopCheckStoreProcedureItems, ImpactFrequencyPerWeekDayViewItems, CurrentDriverStatusChartViewItems, SlamcoreDeviceItems, CustomerSnapshotItems, DriverItems, PersonItems, DashboardCardViewItems, CustomerToModelItems, CustomerToPersonViewItems, CustomerModelItems, VehicleUtilizationLastTwelveHoursViewItems, AccessGroupItems, DashboardDriverCardStoreProcedureItems, TodaysPreopCheckViewItems, CustomerFeatureSubscription, TodaysImpactStoreProcedureItems, CurrentVehicleStatusChartViewItems, DashboardFilterItems, TodaysImpactViewItems, EmailGroupsItems, Dealer, CustomerSSODetailItems, ImpactFrequencyPerWeekMonthViewItems, IncompletedChecklistViewItems, Country, DepartmentItems, ContactPersonInformation, GoUserToCustomerItems, VehicleItems, CustomerAudit, CustomerPreOperationalChecklistTemplateItems, DashboardVehicleCardViewItems, ChecklistStatusViewItems, LoggedHoursVersusSeatHoursViewItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, ImpactFrequencyPerTimeSlotViewItems, Sites, DriverLicenseExpiryStoreProcedureItems </param>
        /// <param name="filter">The filter to apply to the Query. Has to be an expression in C# format and can be referencing Customer entity properties. Can reference parameters with the format @X , X being the parameter number, starting at 0.</param>
        /// <param name="filterParameters">Filter parameters (TODO : document parameters format).</param>
        /// <param name="pageNumber">To apply pagination to the query. Page number to get. No pagination applied is not set.</param>
        /// <param name="pageSize">Size of the page to return.</param>
        /// <param name="sortOrder">Sort order for the query, desc for descending order, asc for ascending order.</param>
        /// <param name="sortColumn">Customer entity property to use to sort.</param>
        /// <returns>List of Customer entities</returns>
        [HttpGet]
        [Route("dataset/api/customer/list")]
        public async Task<ActionResult<CustomerCollectionContainer>> GetDataSetCollection(string include, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn)
        {
            //List<string> includes, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn
            return await DoGetDataSetCollectionAsync(include?.Split(',').Select(i => i.Trim()).ToList(), filter, filterParameters, pageNumber, pageSize, sortOrder, sortColumn);
        }

        /// <summary>
        /// Get a list of Customer instances and return data in json structured format 
        /// </summary>
        /// <param name="include">list of related data to fetch with the Customer, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Customer are : DashboardVehicleCardStoreProcedureItems, DriverLicenseExpiryViewItems, TodaysPreopCheckStoreProcedureItems, ImpactFrequencyPerWeekDayViewItems, CurrentDriverStatusChartViewItems, SlamcoreDeviceItems, CustomerSnapshotItems, DriverItems, PersonItems, DashboardCardViewItems, CustomerToModelItems, CustomerToPersonViewItems, CustomerModelItems, VehicleUtilizationLastTwelveHoursViewItems, AccessGroupItems, DashboardDriverCardStoreProcedureItems, TodaysPreopCheckViewItems, CustomerFeatureSubscription, TodaysImpactStoreProcedureItems, CurrentVehicleStatusChartViewItems, DashboardFilterItems, TodaysImpactViewItems, EmailGroupsItems, Dealer, CustomerSSODetailItems, ImpactFrequencyPerWeekMonthViewItems, IncompletedChecklistViewItems, Country, DepartmentItems, ContactPersonInformation, GoUserToCustomerItems, VehicleItems, CustomerAudit, CustomerPreOperationalChecklistTemplateItems, DashboardVehicleCardViewItems, ChecklistStatusViewItems, LoggedHoursVersusSeatHoursViewItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, ImpactFrequencyPerTimeSlotViewItems, Sites, DriverLicenseExpiryStoreProcedureItems </param>
        /// <param name="filter">The filter to apply to the Query. Has to be an expression in C# format and can be referencing Customer entity properties. Can reference parameters with the format @X , X being the parameter number, starting at 0.</param>
        /// <param name="filterParameters">Filter parameters (TODO : document parameters format).</param>
        /// <param name="pageNumber">To apply pagination to the query. Page number to get. No pagination applied is not set.</param>
        /// <param name="pageSize">Size of the page to return.</param>
        /// <param name="sortOrder">Sort order for the query, desc for descending order, asc for ascending order.</param>
        /// <param name="sortProperty">Customer entity property to use to sort.</param>
        /// <param name="byRef">Setting to define how to handle reference loop. If byRef is true, reference loops are handled by serializing the first occurrence of the object and replacing subsequent occurrences with a reference. If ByRef is false, reference loops are ignored. Default is false when byRef is not specified</param>
        /// <returns>List of Customer entities</returns>
        [HttpGet]
        [Route("api/customer/list")]
        public async Task<ActionResult<DataObjectCollection<CustomerDataObject>>> GetCollection(string include, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn, bool? byRef)
        {
            //List<string> includes, string filter, string filterParameters, int pageNumber, int pageSize, string sortOrder, string sortColumn
            return await DoGetCollectionAsync(include?.Split(',').Select(i => i.Trim()).ToList(), filter, filterParameters, pageNumber, pageSize, sortOrder, sortColumn);
        }

        /// <summary>
        /// Count the number of Customer instances
        /// </summary>
        /// <param name="filter">The filter to apply to the Query. Has to be an expression in C# format and can be referencing Customer entity properties. Can reference parameters with the format @X , X being the parameter number, starting at 0.</param>
        /// <param name="filterParameters">Filter parameters (TODO : document parameters format).</param>
        [HttpGet]
        [Route("dataset/api/customer/count")]
        public async Task<ActionResult<int>> Count(string filter, string filterParameters)
        {
            return await DoCountAsync(filter, filterParameters);
        }

      /// <summary>
        /// Stream the file associated to the CustomerLogo document field of a given Customer instance
        /// </summary>
      /// <param name="id">Id of the Customer entity</param>
        [HttpGet]
        [Route("api/customer/file/{id}/CustomerLogo")]
        public async Task<IActionResult> GetCustomerLogoFile(System.Guid id)
        {
            return await DoGetFileAsync(new List<string> { id.ToString() }, "CustomerLogo");
        }
        /// <summary>
        /// Save (create or update if existing) a given Customer instance, and return the saved instance data
        /// </summary>
        /// <param name="entity">Customer instance to be saved in json dataset format</param>
        /// <param name="include">list of related data to fetch with the Customer saved data, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Customer are : DashboardVehicleCardStoreProcedureItems, DriverLicenseExpiryViewItems, TodaysPreopCheckStoreProcedureItems, ImpactFrequencyPerWeekDayViewItems, CurrentDriverStatusChartViewItems, SlamcoreDeviceItems, CustomerSnapshotItems, DriverItems, PersonItems, DashboardCardViewItems, CustomerToModelItems, CustomerToPersonViewItems, CustomerModelItems, VehicleUtilizationLastTwelveHoursViewItems, AccessGroupItems, DashboardDriverCardStoreProcedureItems, TodaysPreopCheckViewItems, CustomerFeatureSubscription, TodaysImpactStoreProcedureItems, CurrentVehicleStatusChartViewItems, DashboardFilterItems, TodaysImpactViewItems, EmailGroupsItems, Dealer, CustomerSSODetailItems, ImpactFrequencyPerWeekMonthViewItems, IncompletedChecklistViewItems, Country, DepartmentItems, ContactPersonInformation, GoUserToCustomerItems, VehicleItems, CustomerAudit, CustomerPreOperationalChecklistTemplateItems, DashboardVehicleCardViewItems, ChecklistStatusViewItems, LoggedHoursVersusSeatHoursViewItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, ImpactFrequencyPerTimeSlotViewItems, Sites, DriverLicenseExpiryStoreProcedureItems </param>
        /// <returns>The saved Customer, after refetch, in json dataset format</returns>
        [HttpPost]
        [Route("dataset/api/customer")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<ActionResult<CustomerContainer>> PostDataSet([FromForm] string entity, [FromForm] string include)
        {
            return await DoPostDataSetAsync(entity, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Save (create or update if existing) a given Customer instance, and return the saved instance data
        /// </summary>
        /// <param name="entity">Customer instance to be saved in json structured format</param>
        /// <param name="include">list of related data to fetch with the Customer saved data, as a comma separated list of relations. Can include sub-relations as well. Possible relations to include for Customer are : DashboardVehicleCardStoreProcedureItems, DriverLicenseExpiryViewItems, TodaysPreopCheckStoreProcedureItems, ImpactFrequencyPerWeekDayViewItems, CurrentDriverStatusChartViewItems, SlamcoreDeviceItems, CustomerSnapshotItems, DriverItems, PersonItems, DashboardCardViewItems, CustomerToModelItems, CustomerToPersonViewItems, CustomerModelItems, VehicleUtilizationLastTwelveHoursViewItems, AccessGroupItems, DashboardDriverCardStoreProcedureItems, TodaysPreopCheckViewItems, CustomerFeatureSubscription, TodaysImpactStoreProcedureItems, CurrentVehicleStatusChartViewItems, DashboardFilterItems, TodaysImpactViewItems, EmailGroupsItems, Dealer, CustomerSSODetailItems, ImpactFrequencyPerWeekMonthViewItems, IncompletedChecklistViewItems, Country, DepartmentItems, ContactPersonInformation, GoUserToCustomerItems, VehicleItems, CustomerAudit, CustomerPreOperationalChecklistTemplateItems, DashboardVehicleCardViewItems, ChecklistStatusViewItems, LoggedHoursVersusSeatHoursViewItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, ImpactFrequencyPerTimeSlotViewItems, Sites, DriverLicenseExpiryStoreProcedureItems </param>
        /// <param name="byRef">Setting to define how to handle reference loop. If byRef is true, reference loops are handled by serializing the first occurrence of the object and replacing subsequent occurrences with a reference. If ByRef is false, reference loops are ignored. Default is false when byRef is not specified</param>
        /// <returns>The saved Customer, after refetch, in json structured format</returns>
        [HttpPost]
        [Route("api/customer")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<ActionResult<CustomerDataObject>> Post([FromForm] string entity, [FromForm] string include, bool? byRef)
        {
            return await DoPostAsync(entity, include?.Split(',').Select(i => i.Trim()).ToList());
        }

        /// <summary>
        /// Delete a given Customer instance
        /// </summary>
        /// <param name="customer">Customer instance to be deleted in json structured format. Only the primary key information is taken into account, rest ignored</param>
        /// <param name="dry">Dry delete if true, full delete otherwise</param>
        /// <returns>Delete information if dry delete</returns>
        [HttpDelete]
        [Route("dataset/api/customer")]
        public async Task<ActionResult<string>> Delete(CustomerDataObject customer, bool dry = false)
        {
            return await DoDeleteAsync(customer, dry);
        }

        /// <summary>
        /// Delete a given Customer instance
        /// </summary>
        /// <param name="customer">Customer instance to be deleted in json structured format. Only the primary key information is taken into account, rest ignored</param>
      /// <param name="id">Id of the Customer entity to delete</param>
      /// <param name="dry">Dry delete if true, full delete otherwise</param>
        /// <returns>Delete information if dry delete</returns>
        [HttpDelete]
        [Route("dataset/api/customer/{id}")]
        public async Task<ActionResult<string>> Delete(System.Guid id, bool dry = false)
        {
            return await DoDeleteAsync(new List<string> { id.ToString() }, dry);
        }
    }
}
 