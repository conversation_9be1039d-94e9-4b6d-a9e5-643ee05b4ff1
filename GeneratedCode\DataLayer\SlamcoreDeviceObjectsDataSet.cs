﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;


namespace FleetXQ.Data.DataObjects
{
	[JsonObject(MemberSerialization.OptIn)]
	public class SlamcoreDeviceObjectsDataSet : ObjectsDataSetBase
	{
        #region fields

        private ObjectsDataSet _rootObjectDataSet;

		public ObjectsDataSet RootObjectDataSet
        {
            get
            {
				return _rootObjectDataSet;
            }

			set 
			{
				_rootObjectDataSet = value;
			}
        }
		
		// Mapping between entity primary key and data set objects collection internal id
		private ConcurrentDictionary< System.Guid, int> _slamcoreDeviceObjectInternalIds = new ConcurrentDictionary< System.Guid, int>();
		
		// Collection holding all SlamcoreDevice objects for current dataset
		private ConcurrentDictionary< int, SlamcoreDeviceDataObject> _slamcoreDeviceObjects = new ConcurrentDictionary< int, SlamcoreDeviceDataObject>();

		// Temp list of merged data objects - required for merge finalization
		private ConcurrentQueue<SlamcoreDeviceDataObject> _mergedDataObjects;

		private ConcurrentQueue<SlamcoreDeviceDataObject> MergedDataObjects 
		{
			get
			{
				if (_mergedDataObjects == null)
					_mergedDataObjects = new ConcurrentQueue<SlamcoreDeviceDataObject>();
					
				return _mergedDataObjects;
			}
		}
		
		private void ClearMergedDataObjects()
		{
			_mergedDataObjects = null;
		}

        #endregion

		#region properties

		
		// Mapping between entity primary key and data set objects collection internal id
		public ConcurrentDictionary< System.Guid, int> SlamcoreDeviceObjectInternalIds
		{ 
			get { return _slamcoreDeviceObjectInternalIds; }
			set { _slamcoreDeviceObjectInternalIds = value; }
		}
		
		// Collection holding all SlamcoreDevice objects for current dataset
		[JsonProperty("SlamcoreDeviceObjects", DefaultValueHandling  = DefaultValueHandling.Ignore)]
		public ConcurrentDictionary< int, SlamcoreDeviceDataObject> SlamcoreDeviceObjects
		{ 
			get { return _slamcoreDeviceObjects; }
			set { _slamcoreDeviceObjects = value; }
		}
		
		// Index to quickly find all SlamcoreDevice with a given customer foreign key
		public ConcurrentDictionary<System.Guid, List<int>> Customer_FKIndex = new ConcurrentDictionary<System.Guid, List<int>>();
		
 
		// Index to quickly find all SlamcoreDevice with a given slamcoreAPIKey foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> SlamcoreAPIKey_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		// Index to quickly find all SlamcoreDevice with a given slamcoreAwareAuthenticationDetails foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> SlamcoreAwareAuthenticationDetails_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
 
		
 
		// Index to quickly find all SlamcoreDevice with a given vehicle foreign key
		public ConcurrentDictionary<Nullable<System.Guid>, List<int>> Vehicle_FKIndex = new ConcurrentDictionary<Nullable<System.Guid>, List<int>>();
		
 
		
 
		
		#endregion
		
		#region initialization

		[ActivatorUtilitiesConstructor]    
		public SlamcoreDeviceObjectsDataSet(IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
        }

        public SlamcoreDeviceObjectsDataSet(ObjectsDataSet rootObjectDataSet, IServiceProvider serviceProvider, ILogEngine logEngine, IEntityModelCache entityModelCache) : base(serviceProvider, logEngine, entityModelCache)
        {
            _rootObjectDataSet = rootObjectDataSet;
        }

        #endregion

		#region IObjectsDataSet implementation

		public override IObjectsDataSet Clone()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet Clone(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetRequiredService<SlamcoreDeviceObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SlamcoreDeviceObjects)
			{
                var cloneObject = (SlamcoreDeviceDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcoreDeviceObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SlamcoreDeviceObjectInternalIds)
			{
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcoreDeviceObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}

			foreach(var fkKeyValue in this.Customer_FKIndex)
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Customer_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Customer_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.SlamcoreAPIKey_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "SlamcoreDeviceObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.SlamcoreAPIKey_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.SlamcoreAPIKey_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.SlamcoreAwareAuthenticationDetails_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "SlamcoreDeviceObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.SlamcoreAwareAuthenticationDetails_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.SlamcoreAwareAuthenticationDetails_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			foreach(var fkKeyValue in this.Vehicle_FKIndex)
			{
				if(fkKeyValue.Key == null)
				{
					// removed to avoid the having to make the caller async. Do we really need logging here ? 
					// _logEngine.LogError("Unable to Clone the current Object with null items", "A null reference is not expected while trying to clone the current object", "SlamcoreDeviceObjectsDataSet.Clone()", null);
					throw new GOServerException("Unable to Clone the current Object with null items");
				}
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = clone.Vehicle_FKIndex.TryAdd(fkKeyValue.Key, new List<int>());
				}

				foreach (var pk in fkKeyValue.Value)
				{
					clone.Vehicle_FKIndex[fkKeyValue.Key].Add(pk);
				}
			}
			
			return clone;
		}

		public override IObjectsDataSet CloneDirtyObjects()
		{
			throw new GOServerException("Forbidden on sub dataset");		
		}

		public override IObjectsDataSet CloneDirtyObjects(IObjectsDataSet rootDataSet)
		{
			var clone = _serviceProvider.GetService<SlamcoreDeviceObjectsDataSet>();
			clone.RootObjectDataSet = rootDataSet as ObjectsDataSet;
			bool completed;

			foreach(var keyValue in this.SlamcoreDeviceObjects.Where(o => o.Value.IsDirty || o.Value.IsMarkedForDeletion))
			{
                var cloneObject = (SlamcoreDeviceDataObject) keyValue.Value.Clone(false);
                cloneObject.InternalObjectId = keyValue.Value.InternalObjectId;
				
				completed = false;
				while (!completed)
				{
					 completed = clone.SlamcoreDeviceObjects.TryAdd(keyValue.Key, cloneObject);
				}
			}

			foreach(var keyValue in this.SlamcoreDeviceObjectInternalIds
				.Where(o => this.SlamcoreDeviceObjects[o.Value].IsDirty || this.SlamcoreDeviceObjects[o.Value].IsMarkedForDeletion))
			{
				completed = false;
				
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = clone.SlamcoreDeviceObjectInternalIds.TryAdd(keyValue.Key, keyValue.Value);
				}
			}
			
			// CloneDirtyObjects is used to pass only dirty objects to server to save changes. Since indexes are not serialized, no need to clone them
            return clone;
		}

		public override IEnumerable<IDataObject> GetAllObjects()
		{
			foreach(var slamcoreDevice in SlamcoreDeviceObjects.Values)
			{
				yield return slamcoreDevice; 
			}				
		}

	    public override void AddObject(IDataObject objectToAdd, bool replaceIfExists)
        {
			var existingObject = GetObject(objectToAdd);
            if (!replaceIfExists && existingObject != null)
                throw new GOServerException("Object already exists");

			int newInternalId; 
			
            if (existingObject != null)
			{
                //RemoveObject(existingObject);
				if(existingObject.InternalObjectId == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Error while trying to Add Object to the SlamcoreDeviceObjectsDataSet", "The object you are trying to add doesn't have an InternalObjectId", "SlamcoreDeviceObjectsDataSet", null);
					throw new GOServerException("Error while trying to add an object to the dataset without InternalObjectId");
				}
                newInternalId = (int) existingObject.InternalObjectId;
                objectToAdd.InternalObjectId = newInternalId;
				existingObject.CopyValuesFrom(objectToAdd, false);
			}
			else
			{
            	newInternalId = GetNextNewInternalObjectId();
				objectToAdd.InternalObjectId = newInternalId;

				 var completed = false;
				 var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcoreDeviceObjects.TryAdd(newInternalId, (SlamcoreDeviceDataObject)objectToAdd);
				}
			}
			
			if (!objectToAdd.IsNew && existingObject == null)
			{
                //The following if should not be necessary...
				var completed = false;
				if (SlamcoreDeviceObjectInternalIds.ContainsKey(((SlamcoreDeviceDataObject)objectToAdd).PrimaryKey))
				{
					int value;
					var count2 = 0;
					while (!completed && count2++ < 15)
					{
						completed = SlamcoreDeviceObjectInternalIds.TryRemove(((SlamcoreDeviceDataObject)objectToAdd).PrimaryKey, out value);
					}
				}

				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcoreDeviceObjectInternalIds.TryAdd(((SlamcoreDeviceDataObject)objectToAdd).PrimaryKey, newInternalId);
				}
			}
			// Update relations including platform as "many" side or "one" side , pk side for one to one relations
			if((objectToAdd as SlamcoreDeviceDataObject) == null)
			{
				// removed for now to not make the calling method async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to Add an object which is null", "Unable to add an object which is null", "SlamcoreDeviceDataObject", null);
				throw new GOServerException("Unexpected Error: Unable to Add an object which is Null.");
			}

			// Update the Customer FK Index 
			if (!Customer_FKIndex.ContainsKey((objectToAdd as SlamcoreDeviceDataObject).CustomerId))
			{
				var iscompleted = false;
				var count2 = 0;
				while (!iscompleted && count2++ < 15)
				{
					iscompleted = Customer_FKIndex.TryAdd((objectToAdd as SlamcoreDeviceDataObject).CustomerId, new List<int>());
				}
			}
				
			if (!Customer_FKIndex[(objectToAdd as SlamcoreDeviceDataObject).CustomerId].Contains(newInternalId))
				Customer_FKIndex[(objectToAdd as SlamcoreDeviceDataObject).CustomerId].Add(newInternalId);

            CustomerDataObject relatedCustomer;
            if ((objectToAdd as SlamcoreDeviceDataObject)._customer_NewObjectId != null)
            {
				var objectToGet = _serviceProvider.GetService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
                objectToGet.IsNew = true;
                objectToGet.InternalObjectId = (objectToAdd as SlamcoreDeviceDataObject)._customer_NewObjectId;

	            relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
            }
            else
            {
                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToAdd as SlamcoreDeviceDataObject).CustomerId) { IsNew = false });
            }

			if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
                relatedCustomer.NotifyPropertyChanged("SlamcoreDeviceItems", new SeenObjectCollection());
			
	 
			// Update the SlamcoreAPIKey FK Index 
			if ((objectToAdd as SlamcoreDeviceDataObject).SlamcoreAPIKeyId != null)
			{
				if (!SlamcoreAPIKey_FKIndex.ContainsKey((System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAPIKeyId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = SlamcoreAPIKey_FKIndex.TryAdd((System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAPIKeyId, new List<int>());
					}
				}
				
				if (!SlamcoreAPIKey_FKIndex[(System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAPIKeyId].Contains(newInternalId))	
					SlamcoreAPIKey_FKIndex[(System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAPIKeyId].Add(newInternalId);

	            SlamcoreAPIKeyDataObject relatedSlamcoreAPIKey;
	            if ((objectToAdd as SlamcoreDeviceDataObject)._slamcoreAPIKey_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<SlamcoreAPIKeyDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as SlamcoreDeviceDataObject)._slamcoreAPIKey_NewObjectId;

	                relatedSlamcoreAPIKey = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedSlamcoreAPIKey = _rootObjectDataSet.GetObject(new SlamcoreAPIKeyDataObject((System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAPIKeyId) { IsNew = false });
	            }

	            if (relatedSlamcoreAPIKey != null && this.RootObjectDataSet.NotifyChanges)
	                relatedSlamcoreAPIKey.NotifyPropertyChanged("SlamcoreDevice", new SeenObjectCollection());
			}
			
	 
			// Update the SlamcoreAwareAuthenticationDetails FK Index 
			if ((objectToAdd as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId != null)
			{
				if (!SlamcoreAwareAuthenticationDetails_FKIndex.ContainsKey((System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = SlamcoreAwareAuthenticationDetails_FKIndex.TryAdd((System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId, new List<int>());
					}
				}
				
				if (!SlamcoreAwareAuthenticationDetails_FKIndex[(System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId].Contains(newInternalId))	
					SlamcoreAwareAuthenticationDetails_FKIndex[(System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId].Add(newInternalId);

	            SlamcoreAwareAuthenticationDetailsDataObject relatedSlamcoreAwareAuthenticationDetails;
	            if ((objectToAdd as SlamcoreDeviceDataObject)._slamcoreAwareAuthenticationDetails_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<SlamcoreAwareAuthenticationDetailsDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as SlamcoreDeviceDataObject)._slamcoreAwareAuthenticationDetails_NewObjectId;

	                relatedSlamcoreAwareAuthenticationDetails = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedSlamcoreAwareAuthenticationDetails = _rootObjectDataSet.GetObject(new SlamcoreAwareAuthenticationDetailsDataObject((System.Guid)(objectToAdd as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId) { IsNew = false });
	            }

	            if (relatedSlamcoreAwareAuthenticationDetails != null && this.RootObjectDataSet.NotifyChanges)
	                relatedSlamcoreAwareAuthenticationDetails.NotifyPropertyChanged("SlamcoreDevice", new SeenObjectCollection());
			}
			
	 
	 
	 
	 
			// Update the Vehicle FK Index 
			if ((objectToAdd as SlamcoreDeviceDataObject).VehicleId != null)
			{
				if (!Vehicle_FKIndex.ContainsKey((System.Guid)(objectToAdd as SlamcoreDeviceDataObject).VehicleId))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Vehicle_FKIndex.TryAdd((System.Guid)(objectToAdd as SlamcoreDeviceDataObject).VehicleId, new List<int>());
					}
				}
				
				if (!Vehicle_FKIndex[(System.Guid)(objectToAdd as SlamcoreDeviceDataObject).VehicleId].Contains(newInternalId))	
					Vehicle_FKIndex[(System.Guid)(objectToAdd as SlamcoreDeviceDataObject).VehicleId].Add(newInternalId);

	            VehicleDataObject relatedVehicle;
	            if ((objectToAdd as SlamcoreDeviceDataObject)._vehicle_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetService<IDataObjectFactory<VehicleDataObject>>().CreateDataObject();
                    objectToGet.IsNew = true;
                    objectToGet.InternalObjectId = (objectToAdd as SlamcoreDeviceDataObject)._vehicle_NewObjectId;

	                relatedVehicle = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedVehicle = _rootObjectDataSet.GetObject(new VehicleDataObject((System.Guid)(objectToAdd as SlamcoreDeviceDataObject).VehicleId) { IsNew = false });
	            }

	            if (relatedVehicle != null && this.RootObjectDataSet.NotifyChanges)
	                relatedVehicle.NotifyPropertyChanged("", new SeenObjectCollection());
			}
			
	 
	 
		
		}

        public override void RemoveObject(IDataObject objectToRemove)
        {
            if (SlamcoreDeviceObjects == null)
                return;
			bool completed;			
			int? objectToRemoveInternalId;
			
			if((objectToRemove as SlamcoreDeviceDataObject) == null)
			{
				// removed for now to not make the calling method async. do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to remove null object", "The object you are trying to remove is null", "SlamcoreDeviceObjectsDataSet.RemoveObject", null);
				throw new GOServerException("Unable to remove Null Object.");
			}

			if (objectToRemove.IsNew)
				objectToRemoveInternalId = objectToRemove.InternalObjectId;
			else
				objectToRemoveInternalId = SlamcoreDeviceObjectInternalIds.ContainsKey((objectToRemove as SlamcoreDeviceDataObject).PrimaryKey) ? (int?) SlamcoreDeviceObjectInternalIds[(objectToRemove as SlamcoreDeviceDataObject).PrimaryKey] : null;
				
			if (objectToRemoveInternalId != null)
			{
				SlamcoreDeviceDataObject value;
				completed = false;
				var count = 0;
				while (!completed && count++ < 15)
				{
					completed = SlamcoreDeviceObjects.TryRemove((int)objectToRemoveInternalId, out value);
				}

                // Reinit InternalObjectId only if the object to remove is part of the current dataset
				if (ReferenceEquals(objectToRemove.ObjectsDataSet, this._rootObjectDataSet))
					objectToRemove.InternalObjectId = null;
				
				if (!objectToRemove.IsNew)
				{
					int idvalue;
					completed = false;
					count = 0;
					while (!completed && count++ < 15)
					{
						completed = SlamcoreDeviceObjectInternalIds.TryRemove((objectToRemove as SlamcoreDeviceDataObject).PrimaryKey, out idvalue);
					}
				}
				
			// Delete the Customer FK Index 
				if (Customer_FKIndex.ContainsKey((objectToRemove as SlamcoreDeviceDataObject).CustomerId) && Customer_FKIndex[(objectToRemove as SlamcoreDeviceDataObject).CustomerId].Contains((int)objectToRemoveInternalId))
				{
					Customer_FKIndex[(objectToRemove as SlamcoreDeviceDataObject).CustomerId].Remove((int)objectToRemoveInternalId);

					if (!Customer_FKIndex[(objectToRemove as SlamcoreDeviceDataObject).CustomerId].Any())
					{
						List<int> outvalue;
						var iscompleted = false;
						var count2 = 0;
						while (!iscompleted  && count2++ < 15)
						{
							iscompleted = Customer_FKIndex.TryRemove((objectToRemove as SlamcoreDeviceDataObject).CustomerId, out outvalue);
						}
					}
				}
				
				CustomerDataObject relatedCustomer;
	            if ((objectToRemove as SlamcoreDeviceDataObject)._customer_NewObjectId != null)
	            {
					var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<CustomerDataObject>>().CreateDataObject();
					objectToGet.IsNew = true;
					objectToGet.InternalObjectId = (objectToRemove as SlamcoreDeviceDataObject)._customer_NewObjectId;

					relatedCustomer = _rootObjectDataSet.GetObject(objectToGet);
	            }
	            else
	            {
	                relatedCustomer = _rootObjectDataSet.GetObject(new CustomerDataObject((objectToRemove as SlamcoreDeviceDataObject).CustomerId) { IsNew = false });
	            }

	            if (relatedCustomer != null && this.RootObjectDataSet.NotifyChanges)
	                relatedCustomer.NotifyPropertyChanged("SlamcoreDeviceItems", new SeenObjectCollection());
				
		 
			// Delete the SlamcoreAPIKey FK Index 
				if ((objectToRemove as SlamcoreDeviceDataObject).SlamcoreAPIKeyId != null)
				{
					if (SlamcoreAPIKey_FKIndex.ContainsKey((System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAPIKeyId) && SlamcoreAPIKey_FKIndex[(System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAPIKeyId].Contains((int)objectToRemoveInternalId))
					{
						SlamcoreAPIKey_FKIndex[(System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAPIKeyId].Remove((int)objectToRemoveInternalId);

						if (!SlamcoreAPIKey_FKIndex[(System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAPIKeyId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = SlamcoreAPIKey_FKIndex.TryRemove((System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAPIKeyId, out outvalue);
							}
						}
					}

					SlamcoreAPIKeyDataObject relatedSlamcoreAPIKey;
		            if ((objectToRemove as SlamcoreDeviceDataObject)._slamcoreAPIKey_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<SlamcoreAPIKeyDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as SlamcoreDeviceDataObject)._slamcoreAPIKey_NewObjectId;

						relatedSlamcoreAPIKey = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedSlamcoreAPIKey = _rootObjectDataSet.GetObject(new SlamcoreAPIKeyDataObject((System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAPIKeyId) { IsNew = false });
		            }

		            if (relatedSlamcoreAPIKey != null && this.RootObjectDataSet.NotifyChanges)
		                relatedSlamcoreAPIKey.NotifyPropertyChanged("SlamcoreDevice", new SeenObjectCollection());
					
				}			
		 
			// Delete the SlamcoreAwareAuthenticationDetails FK Index 
				if ((objectToRemove as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId != null)
				{
					if (SlamcoreAwareAuthenticationDetails_FKIndex.ContainsKey((System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId) && SlamcoreAwareAuthenticationDetails_FKIndex[(System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId].Contains((int)objectToRemoveInternalId))
					{
						SlamcoreAwareAuthenticationDetails_FKIndex[(System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId].Remove((int)objectToRemoveInternalId);

						if (!SlamcoreAwareAuthenticationDetails_FKIndex[(System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = SlamcoreAwareAuthenticationDetails_FKIndex.TryRemove((System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId, out outvalue);
							}
						}
					}

					SlamcoreAwareAuthenticationDetailsDataObject relatedSlamcoreAwareAuthenticationDetails;
		            if ((objectToRemove as SlamcoreDeviceDataObject)._slamcoreAwareAuthenticationDetails_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<SlamcoreAwareAuthenticationDetailsDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as SlamcoreDeviceDataObject)._slamcoreAwareAuthenticationDetails_NewObjectId;

						relatedSlamcoreAwareAuthenticationDetails = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedSlamcoreAwareAuthenticationDetails = _rootObjectDataSet.GetObject(new SlamcoreAwareAuthenticationDetailsDataObject((System.Guid)(objectToRemove as SlamcoreDeviceDataObject).SlamcoreAwareAuthenticationDetailsId) { IsNew = false });
		            }

		            if (relatedSlamcoreAwareAuthenticationDetails != null && this.RootObjectDataSet.NotifyChanges)
		                relatedSlamcoreAwareAuthenticationDetails.NotifyPropertyChanged("SlamcoreDevice", new SeenObjectCollection());
					
				}			
		 
		 
		 
		 
			// Delete the Vehicle FK Index 
				if ((objectToRemove as SlamcoreDeviceDataObject).VehicleId != null)
				{
					if (Vehicle_FKIndex.ContainsKey((System.Guid)(objectToRemove as SlamcoreDeviceDataObject).VehicleId) && Vehicle_FKIndex[(System.Guid)(objectToRemove as SlamcoreDeviceDataObject).VehicleId].Contains((int)objectToRemoveInternalId))
					{
						Vehicle_FKIndex[(System.Guid)(objectToRemove as SlamcoreDeviceDataObject).VehicleId].Remove((int)objectToRemoveInternalId);

						if (!Vehicle_FKIndex[(System.Guid)(objectToRemove as SlamcoreDeviceDataObject).VehicleId].Any())
						{
							List<int> outvalue;
							var iscompleted = false;
							var count2 = 0;
							while (!iscompleted && count2++ < 15)
							{
								iscompleted = Vehicle_FKIndex.TryRemove((System.Guid)(objectToRemove as SlamcoreDeviceDataObject).VehicleId, out outvalue);
							}
						}
					}

					VehicleDataObject relatedVehicle;
		            if ((objectToRemove as SlamcoreDeviceDataObject)._vehicle_NewObjectId != null)
		            {
						var objectToGet = _serviceProvider.GetRequiredService<IDataObjectFactory<VehicleDataObject>>().CreateDataObject();
						objectToGet.IsNew = true;
						objectToGet.InternalObjectId = (objectToRemove as SlamcoreDeviceDataObject)._vehicle_NewObjectId;

						relatedVehicle = _rootObjectDataSet.GetObject(objectToGet);
		            }
		            else
		            {
		                relatedVehicle = _rootObjectDataSet.GetObject(new VehicleDataObject((System.Guid)(objectToRemove as SlamcoreDeviceDataObject).VehicleId) { IsNew = false });
		            }

		            if (relatedVehicle != null && this.RootObjectDataSet.NotifyChanges)
		                relatedVehicle.NotifyPropertyChanged("", new SeenObjectCollection());
					
				}			
		 
		 
			}		
		}

		public override TDataObject GetObject<TDataObject>(Type objectBaseType, int internalObjectId)
        {
            return SlamcoreDeviceObjects.ContainsKey(internalObjectId) ? SlamcoreDeviceObjects[internalObjectId] as TDataObject : null;
        }

        public override TDataObject GetObject<TDataObject>(TDataObject objectToGet)
        {
			int? objectToGetInternalId;
			
			if (objectToGet.IsNew)
				objectToGetInternalId = objectToGet.InternalObjectId;
			else
			{
				if((objectToGet as SlamcoreDeviceDataObject) == null)
				{
					// removed for now to not make the calling method async. Do we really need logging here ?
					// await _logEngine.LogErrorAsync("Unable to get value which value is null", "The object you are trying to get doesn't have a value", "SlamcoreDeviceObjectsDataSet", null);
					throw new GOServerException("Unable to get an element which value is null.");
				}
				objectToGetInternalId = SlamcoreDeviceObjectInternalIds.ContainsKey((objectToGet as SlamcoreDeviceDataObject).PrimaryKey) ? (int?) SlamcoreDeviceObjectInternalIds[(objectToGet as SlamcoreDeviceDataObject).PrimaryKey] : null;
			}
			if (objectToGetInternalId != null)
			{
				return SlamcoreDeviceObjects.ContainsKey((int)objectToGetInternalId) ? SlamcoreDeviceObjects[(int)objectToGetInternalId] as TDataObject : null;
			}

			return null;
        }
		
        public override IEnumerable<IDataObject> GetObjectsMarkedForDeletion()
        {
			return SlamcoreDeviceObjects.Values.Where(c => c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		public override IEnumerable<IDataObject> GetObjectsOutOfGraph()
        {
			return SlamcoreDeviceObjects.Values.Where(c => !c.IncludedInGraph && !c.IsMarkedForDeletion).Cast<IDataObject>();
        }

		
		public IEnumerable<SlamcoreDeviceDataObject> GetSlamcoreDeviceItemsForCustomer(CustomerDataObject customerInstance) 
		{
			if (customerInstance.IsNew)
            {
			
              return SlamcoreDeviceObjects.Where(o => o.Value._customer_NewObjectId != null && o.Value._customer_NewObjectId == customerInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Customer_FKIndex.ContainsKey(customerInstance.Id))
			{
				return Customer_FKIndex[customerInstance.Id].Where(e => SlamcoreDeviceObjects.ContainsKey(e)).Select(e => SlamcoreDeviceObjects[e]);
			}
			
			return new DataObjectCollection<SlamcoreDeviceDataObject>();
		}
		 
		
		public IEnumerable<SlamcoreDeviceDataObject> GetSlamcoreDeviceForSlamcoreAPIKey(SlamcoreAPIKeyDataObject slamcoreAPIKeyInstance) 
		{
			if (slamcoreAPIKeyInstance.IsNew)
            {
			
              return SlamcoreDeviceObjects.Where(o => o.Value._slamcoreAPIKey_NewObjectId != null && o.Value._slamcoreAPIKey_NewObjectId == slamcoreAPIKeyInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (SlamcoreAPIKey_FKIndex.ContainsKey(slamcoreAPIKeyInstance.Id))
			{
				return SlamcoreAPIKey_FKIndex[slamcoreAPIKeyInstance.Id].Where(e => SlamcoreDeviceObjects.ContainsKey(e)).Select(e => SlamcoreDeviceObjects[e]);
			}
			
			return new DataObjectCollection<SlamcoreDeviceDataObject>();
		}
		 
		
		public IEnumerable<SlamcoreDeviceDataObject> GetSlamcoreDeviceForSlamcoreAwareAuthenticationDetails(SlamcoreAwareAuthenticationDetailsDataObject slamcoreAwareAuthenticationDetailsInstance) 
		{
			if (slamcoreAwareAuthenticationDetailsInstance.IsNew)
            {
			
              return SlamcoreDeviceObjects.Where(o => o.Value._slamcoreAwareAuthenticationDetails_NewObjectId != null && o.Value._slamcoreAwareAuthenticationDetails_NewObjectId == slamcoreAwareAuthenticationDetailsInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (SlamcoreAwareAuthenticationDetails_FKIndex.ContainsKey(slamcoreAwareAuthenticationDetailsInstance.Id))
			{
				return SlamcoreAwareAuthenticationDetails_FKIndex[slamcoreAwareAuthenticationDetailsInstance.Id].Where(e => SlamcoreDeviceObjects.ContainsKey(e)).Select(e => SlamcoreDeviceObjects[e]);
			}
			
			return new DataObjectCollection<SlamcoreDeviceDataObject>();
		}
		 
		 
		 
		 
		
		public IEnumerable<SlamcoreDeviceDataObject> GetForVehicle(VehicleDataObject vehicleInstance) 
		{
			if (vehicleInstance.IsNew)
            {
			
              return SlamcoreDeviceObjects.Where(o => o.Value._vehicle_NewObjectId != null && o.Value._vehicle_NewObjectId == vehicleInstance.InternalObjectId).Select(o => o.Value);
			}
				
			if (Vehicle_FKIndex.ContainsKey(vehicleInstance.Id))
			{
				return Vehicle_FKIndex[vehicleInstance.Id].Where(e => SlamcoreDeviceObjects.ContainsKey(e)).Select(e => SlamcoreDeviceObjects[e]);
			}
			
			return new DataObjectCollection<SlamcoreDeviceDataObject>();
		}
		 
		 

        public override DataObjectCollection<TDataObject> GetRelatedObjects<TDataObject>(IDataObject rootObject, string relationName)
        {
 
 
 
			if (relationName == "SlamcoreDeviceConnectionViewItems")
            {
				IEnumerable< SlamcoreDeviceConnectionViewDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SlamcoreDeviceConnectionViewObjectsDataSet.GetSlamcoreDeviceConnectionViewItemsForSlamcoreDevice(rootObject as SlamcoreDeviceDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "SlamcoreDeviceHistoryItems")
            {
				IEnumerable< SlamcoreDeviceHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SlamcoreDeviceHistoryObjectsDataSet.GetSlamcoreDeviceHistoryItemsForSlamcoreDevice(rootObject as SlamcoreDeviceDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			if (relationName == "SlamcorePedestrianDetectionItems")
            {
				IEnumerable< SlamcorePedestrianDetectionDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.SlamcorePedestrianDetectionObjectsDataSet.GetSlamcorePedestrianDetectionItemsForSlamcoreDevice(rootObject as SlamcoreDeviceDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
 
			if (relationName == "VehicleSlamcoreLocationHistoryItems")
            {
				IEnumerable< VehicleSlamcoreLocationHistoryDataObject> relatedObjects;					
				relatedObjects = _rootObjectDataSet.VehicleSlamcoreLocationHistoryObjectsDataSet.GetVehicleSlamcoreLocationHistoryItemsForSlamcoreDevice(rootObject as SlamcoreDeviceDataObject);
				
				var result = new DataObjectCollection<TDataObject>();
				
				if (relatedObjects != null)
				{
                    result.NotifyChanges = false;

					foreach(var relatedObject in relatedObjects)
						result.Add(relatedObject as TDataObject);

                    result.NotifyChanges = true;
				}	
				
                result.ObjectsDataSet = this._rootObjectDataSet;

				return result;
			}
 
			return null;
		}
		
		private readonly object _mergeLock = new object();

        public override void Merge(IObjectsDataSet dataSetToMerge, bool updateOrginalInternalId)
        {
			lock(_mergeLock)
			{
				var SlamcoreDeviceDataSet = dataSetToMerge as SlamcoreDeviceObjectsDataSet;
				if(SlamcoreDeviceDataSet == null)
				{
					throw new GOServerException("Unable to merge the current DataSet with null");
				}
				foreach (var item in SlamcoreDeviceDataSet.SlamcoreDeviceObjects.Values)
				{
					var oldInternalId = item.InternalObjectId;

					var objectToMerge = item.Clone(false);
					objectToMerge.InternalObjectId = null;
					objectToMerge.ObjectsDataSet = this._rootObjectDataSet;
				
					objectToMerge.IsMarkedForDeletion = item.IsMarkedForDeletion;
                
					_rootObjectDataSet.AddOrReplaceObject(objectToMerge);
					var newInternalId = objectToMerge.InternalObjectId;
					if (updateOrginalInternalId)
						item.InternalObjectId = newInternalId;

					if (oldInternalId != null && !_rootObjectDataSet.DatasetMergingInternalIdMapping.ContainsKey((int) oldInternalId))
					{
						if(newInternalId == null)
						{
							// removed for now to not make the calling method async. Do we really need logging here ?
							// await _logEngine.LogErrorAsync("Unable to merge elements in DataSet without InternalId", "The Element you are trying to merge doesn't have an internalId", "SlamcoreDeviceObjectsDataSet", null);
							throw new GOServerException("Unable to merge elements in dataset without InternalId");
						}
						var completed = false;
						var count = 0;
						while (!completed && count++ < 15)
						{
							completed = _rootObjectDataSet.DatasetMergingInternalIdMapping.TryAdd((int) oldInternalId, (int) newInternalId);
						}
					}

					MergedDataObjects.Enqueue(objectToMerge as SlamcoreDeviceDataObject);
				}
			}
        }
		
		public override void FinalizeMerge()
		{
			foreach(var mergedObject in MergedDataObjects)
			{
				mergedObject.UpdateRelatedInternalIds(_rootObjectDataSet.DatasetMergingInternalIdMapping);
			}
			
			ClearMergedDataObjects();
		}

		public override void ReconstructIndexes()
		{
			// Reconstruct the Customer FK Index 
			Customer_FKIndex = new ConcurrentDictionary< System.Guid, List<int>>();
				
			foreach (var item in SlamcoreDeviceObjects.Values)
			{
				if (item.CustomerId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.CustomerId;	

				if (!Customer_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Customer_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SlamcoreDeviceObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Customer_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the SlamcoreAPIKey FK Index 
			SlamcoreAPIKey_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in SlamcoreDeviceObjects.Values)
			{
				if (item.SlamcoreAPIKeyId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.SlamcoreAPIKeyId;	

				if (!SlamcoreAPIKey_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = SlamcoreAPIKey_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SlamcoreDeviceObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				SlamcoreAPIKey_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
			// Reconstruct the SlamcoreAwareAuthenticationDetails FK Index 
			SlamcoreAwareAuthenticationDetails_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in SlamcoreDeviceObjects.Values)
			{
				if (item.SlamcoreAwareAuthenticationDetailsId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.SlamcoreAwareAuthenticationDetailsId;	

				if (!SlamcoreAwareAuthenticationDetails_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = SlamcoreAwareAuthenticationDetails_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SlamcoreDeviceObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				SlamcoreAwareAuthenticationDetails_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		 
		 
			// Reconstruct the Vehicle FK Index 
			Vehicle_FKIndex = new ConcurrentDictionary< Nullable<System.Guid>, List<int>>();
				
			foreach (var item in SlamcoreDeviceObjects.Values)
			{
				if (item.VehicleId == null) 
					continue;				
				
				if (item.IsMarkedForDeletion)
					continue;

				var fk = item.VehicleId;	

				if (!Vehicle_FKIndex.ContainsKey(fk))
				{
					var iscompleted = false;
					var count2 = 0;
					while (!iscompleted && count2++ < 15)
					{
						iscompleted = Vehicle_FKIndex.TryAdd(fk, new List<int>());
					}
				}
				if(item.InternalObjectId == null)
				{
					// remove to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to reconstruct indexes.", "An error occured while trying to reconstruct indexes", "SlamcoreDeviceObjectsDataSet", null);
					throw new GOServerException("Unable to reconstruct indexes.");
				}
					
				Vehicle_FKIndex[fk].Add((int)item.InternalObjectId);
			}			
		 
		 
		}

		#endregion

		#region private methods
		
		private int GetNextNewInternalObjectId()
		{
			int newInternalId = _rootObjectDataSet.GetNextNewObjectId();

			// With business entity hierarchies I was seeing objects in dataset with same internal Id. Something to do with PK being the same for entities in hierarchy perhaps?
			// Anyway, it's dangeroud because means LoadParentEntity() can get stuck in infinite loop if it finds 'itself' as parent of iteself because of the internal Ids.
			// Ensure newInternalId not already in use
			while (SlamcoreDeviceObjectInternalIds.Values.Contains(newInternalId))
			{
				newInternalId = _rootObjectDataSet.GetNextNewObjectId();
			}

			return newInternalId;
		}

		#endregion

	}	
}