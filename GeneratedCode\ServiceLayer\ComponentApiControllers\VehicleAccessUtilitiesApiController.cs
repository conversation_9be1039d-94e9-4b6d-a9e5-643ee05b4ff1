﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using Newtonsoft.Json;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Components.Server;
using GenerativeObjects.Practices.Logging;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Settings;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Microsoft.AspNetCore.Antiforgery;
using NLog;

namespace FleetXQ.ServiceLayer.ComponentApiControllers
{
    [ApiController]    
    [ServiceFilter(typeof(ObjectGraphOrDataSetSerializationFilterAttribute))]
	public class VehicleAccessUtilitiesApiController : GOControllerBase
    {
        public VehicleAccessUtilitiesApiController(IServiceProvider serviceProvider, IConfiguration configuration, ISettingsProvider settingsProvider, ILogEngine logEngine, IAntiforgery antiforgery) : base (serviceProvider, settingsProvider, logEngine)
        {
			_configuration = configuration;	
			_antiforgery = antiforgery;
        }

        private readonly IConfiguration _configuration;
        private readonly Logger _techLogger = LogManager.GetCurrentClassLogger();
		private readonly IAntiforgery _antiforgery;

        private bool _showExceptionDetails => _configuration["ShowExceptionDetails"] == StringValues.Empty ? false : Convert.ToBoolean(_configuration["ShowExceptionDetails"]);
		
        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/createondemandaccesses")]
        public async Task<ActionResult<PerVehicleNormalCardAccessCollectionContainer>> CreateOnDemandAccessesDataSet([FromForm] System.Guid[] cardIds, [FromForm] System.Guid vehicleId)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "cardIds", cardIds?.ToString() },
					{ "vehicleId", vehicleId.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "CreateOnDemandAccesses", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().CreateOnDemandAccessesAsync(cardIds, vehicleId, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessCollectionContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "CreateOnDemandAccesses", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/createondemandaccesses")]
        public async Task<ActionResult<PerVehicleNormalCardAccessCollectionContainer>> CreateOnDemandAccesses([FromForm] System.Guid[] cardIds, [FromForm] System.Guid vehicleId)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "cardIds", cardIds?.ToString() },
					{ "vehicleId", vehicleId.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "CreateOnDemandAccesses", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().CreateOnDemandAccessesAsync(cardIds, vehicleId);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessCollectionContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "CreateOnDemandAccesses", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/deleteondemandaccess")]
        public async Task<ActionResult<System.Boolean>> DeleteOnDemandAccessDataSet([FromForm] System.Guid accessId)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "accessId", accessId.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "DeleteOnDemandAccess", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().DeleteOnDemandAccessAsync(accessId, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "DeleteOnDemandAccess", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/deleteondemandaccess")]
        public async Task<ActionResult<System.Boolean>> DeleteOnDemandAccess([FromForm] System.Guid accessId)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "accessId", accessId.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "DeleteOnDemandAccess", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().DeleteOnDemandAccessAsync(accessId);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "DeleteOnDemandAccess", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/updatevehiclemodelaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateVehicleModelAccessesForPersonDataSet([FromForm] System.Guid personId, [FromForm] string updateModelAccesses)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "updateModelAccesses", updateModelAccesses?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleModelAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updateModelAccessesObject = null; 
				{
					updateModelAccessesObject = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToModelVehicleNormalAccessViewContainer>(updateModelAccesses, _jsonSerializerSettings).ExtractPersonToModelVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    updateModelAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToModelVehicleNormalAccessViewObjectsDataSet.PersonToModelVehicleNormalAccessViewObjects.Values) 
                    {
                        updateModelAccessesObject.Add(element);
                    }
				}									
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateVehicleModelAccessesForPersonAsync(personId, updateModelAccessesObject, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleModelAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/updatevehiclemodelaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateVehicleModelAccessesForPerson([FromForm] System.Guid personId, [FromForm] string updateModelAccesses,bool? byRef)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "updateModelAccesses", updateModelAccesses?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleModelAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updateModelAccessesObject = null; 
				{
					updateModelAccessesObject = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToModelVehicleNormalAccessViewContainer>(updateModelAccesses, _jsonSerializerSettings).ExtractPersonToModelVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    updateModelAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToModelVehicleNormalAccessViewObjectsDataSet.PersonToModelVehicleNormalAccessViewObjects.Values) 
                    {
                        updateModelAccessesObject.Add(element);
                    }
				}	
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateVehicleModelAccessesForPersonAsync(personId, updateModelAccessesObject);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleModelAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/copyuservehicleaccess")]
        public async Task<ActionResult<System.Boolean>> CopyUserVehicleAccessDataSet([FromForm] System.Guid personId, [FromForm] System.Guid[] driverIds)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "driverIds", driverIds?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "CopyUserVehicleAccess", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().CopyUserVehicleAccessAsync(personId, driverIds, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "CopyUserVehicleAccess", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/copyuservehicleaccess")]
        public async Task<ActionResult<System.Boolean>> CopyUserVehicleAccess([FromForm] System.Guid personId, [FromForm] System.Guid[] driverIds)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "driverIds", driverIds?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "CopyUserVehicleAccess", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().CopyUserVehicleAccessAsync(personId, driverIds);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "CopyUserVehicleAccess", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/updatevehicledepartmentaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateVehicleDepartmentAccessesForPersonDataSet([FromForm] System.Guid personId, [FromForm] string updatedPersonToDepartmentAccesses)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "updatedPersonToDepartmentAccesses", updatedPersonToDepartmentAccesses?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleDepartmentAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updatedPersonToDepartmentAccessesObject = null; 
				{
					updatedPersonToDepartmentAccessesObject = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToDepartmentVehicleNormalAccessViewContainer>(updatedPersonToDepartmentAccesses, _jsonSerializerSettings).ExtractPersonToDepartmentVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    updatedPersonToDepartmentAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToDepartmentVehicleNormalAccessViewObjectsDataSet.PersonToDepartmentVehicleNormalAccessViewObjects.Values) 
                    {
                        updatedPersonToDepartmentAccessesObject.Add(element);
                    }
				}									
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateVehicleDepartmentAccessesForPersonAsync(personId, updatedPersonToDepartmentAccessesObject, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleDepartmentAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/updatevehicledepartmentaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateVehicleDepartmentAccessesForPerson([FromForm] System.Guid personId, [FromForm] string updatedPersonToDepartmentAccesses,bool? byRef)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "updatedPersonToDepartmentAccesses", updatedPersonToDepartmentAccesses?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleDepartmentAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updatedPersonToDepartmentAccessesObject = null; 
				{
					updatedPersonToDepartmentAccessesObject = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToDepartmentVehicleNormalAccessViewContainer>(updatedPersonToDepartmentAccesses, _jsonSerializerSettings).ExtractPersonToDepartmentVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    updatedPersonToDepartmentAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToDepartmentVehicleNormalAccessViewObjectsDataSet.PersonToDepartmentVehicleNormalAccessViewObjects.Values) 
                    {
                        updatedPersonToDepartmentAccessesObject.Add(element);
                    }
				}	
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateVehicleDepartmentAccessesForPersonAsync(personId, updatedPersonToDepartmentAccessesObject);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleDepartmentAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/getaccessesfordepartments")]
        public async Task<ActionResult<PersonToDepartmentVehicleNormalAccessViewCollectionContainer>> GetAccessesForDepartmentsDataSet([FromForm] string personToSiteAccesses, [FromForm] System.Guid personId, [FromForm] System.Int32 permissionLevel)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personToSiteAccesses", personToSiteAccesses?.ToString() },
					{ "personId", personId.ToString() },
					{ "permissionLevel", permissionLevel.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForDepartments", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccessesObject = null; 
				{
					personToSiteAccessesObject = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToSiteVehicleNormalAccessViewContainer>(personToSiteAccesses, _jsonSerializerSettings).ExtractPersonToSiteVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToSiteAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToSiteVehicleNormalAccessViewObjectsDataSet.PersonToSiteVehicleNormalAccessViewObjects.Values) 
                    {
                        personToSiteAccessesObject.Add(element);
                    }
				}									
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().GetAccessesForDepartmentsAsync(personToSiteAccessesObject, personId, permissionLevel, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewCollectionContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForDepartments", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/getaccessesfordepartments")]
        public async Task<ActionResult<PersonToDepartmentVehicleNormalAccessViewCollectionContainer>> GetAccessesForDepartments([FromForm] string personToSiteAccesses, [FromForm] System.Guid personId, [FromForm] System.Int32 permissionLevel,bool? byRef)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personToSiteAccesses", personToSiteAccesses?.ToString() },
					{ "personId", personId.ToString() },
					{ "permissionLevel", permissionLevel.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForDepartments", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccessesObject = null; 
				{
					personToSiteAccessesObject = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToSiteVehicleNormalAccessViewContainer>(personToSiteAccesses, _jsonSerializerSettings).ExtractPersonToSiteVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToSiteAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToSiteVehicleNormalAccessViewObjectsDataSet.PersonToSiteVehicleNormalAccessViewObjects.Values) 
                    {
                        personToSiteAccessesObject.Add(element);
                    }
				}	
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().GetAccessesForDepartmentsAsync(personToSiteAccessesObject, personId, permissionLevel);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewCollectionContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForDepartments", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/updatevehiclesiteaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateVehicleSiteAccessesForPersonDataSet([FromForm] System.Guid personId, [FromForm] string updatedPersonToSiteAccesses)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "updatedPersonToSiteAccesses", updatedPersonToSiteAccesses?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleSiteAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updatedPersonToSiteAccessesObject = null; 
				{
					updatedPersonToSiteAccessesObject = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToSiteVehicleNormalAccessViewContainer>(updatedPersonToSiteAccesses, _jsonSerializerSettings).ExtractPersonToSiteVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    updatedPersonToSiteAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToSiteVehicleNormalAccessViewObjectsDataSet.PersonToSiteVehicleNormalAccessViewObjects.Values) 
                    {
                        updatedPersonToSiteAccessesObject.Add(element);
                    }
				}									
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateVehicleSiteAccessesForPersonAsync(personId, updatedPersonToSiteAccessesObject, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleSiteAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/updatevehiclesiteaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateVehicleSiteAccessesForPerson([FromForm] System.Guid personId, [FromForm] string updatedPersonToSiteAccesses,bool? byRef)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "updatedPersonToSiteAccesses", updatedPersonToSiteAccesses?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleSiteAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updatedPersonToSiteAccessesObject = null; 
				{
					updatedPersonToSiteAccessesObject = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToSiteVehicleNormalAccessViewContainer>(updatedPersonToSiteAccesses, _jsonSerializerSettings).ExtractPersonToSiteVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    updatedPersonToSiteAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToSiteVehicleNormalAccessViewObjectsDataSet.PersonToSiteVehicleNormalAccessViewObjects.Values) 
                    {
                        updatedPersonToSiteAccessesObject.Add(element);
                    }
				}	
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateVehicleSiteAccessesForPersonAsync(personId, updatedPersonToSiteAccessesObject);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehicleSiteAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/getaccessesforvehicles")]
        public async Task<ActionResult<PersonToPerVehicleNormalAccessViewCollectionContainer>> GetAccessesForVehiclesDataSet([FromForm] string personToDepartmentAccesses, [FromForm] string personToModelAccesses, [FromForm] System.Guid personId, [FromForm] System.Int32 permissionLevel)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personToDepartmentAccesses", personToDepartmentAccesses?.ToString() },
					{ "personToModelAccesses", personToModelAccesses?.ToString() },
					{ "personId", personId.ToString() },
					{ "permissionLevel", permissionLevel.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForVehicles", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccessesObject = null; 
				{
					personToDepartmentAccessesObject = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToDepartmentVehicleNormalAccessViewContainer>(personToDepartmentAccesses, _jsonSerializerSettings).ExtractPersonToDepartmentVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToDepartmentAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToDepartmentVehicleNormalAccessViewObjectsDataSet.PersonToDepartmentVehicleNormalAccessViewObjects.Values) 
                    {
                        personToDepartmentAccessesObject.Add(element);
                    }
				}									
				DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccessesObject = null; 
				{
					personToModelAccessesObject = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToModelVehicleNormalAccessViewContainer>(personToModelAccesses, _jsonSerializerSettings).ExtractPersonToModelVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToModelAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToModelVehicleNormalAccessViewObjectsDataSet.PersonToModelVehicleNormalAccessViewObjects.Values) 
                    {
                        personToModelAccessesObject.Add(element);
                    }
				}									
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().GetAccessesForVehiclesAsync(personToDepartmentAccessesObject, personToModelAccessesObject, personId, permissionLevel, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewCollectionContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForVehicles", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/getaccessesforvehicles")]
        public async Task<ActionResult<PersonToPerVehicleNormalAccessViewCollectionContainer>> GetAccessesForVehicles([FromForm] string personToDepartmentAccesses, [FromForm] string personToModelAccesses, [FromForm] System.Guid personId, [FromForm] System.Int32 permissionLevel,bool? byRef)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personToDepartmentAccesses", personToDepartmentAccesses?.ToString() },
					{ "personToModelAccesses", personToModelAccesses?.ToString() },
					{ "personId", personId.ToString() },
					{ "permissionLevel", permissionLevel.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForVehicles", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccessesObject = null; 
				{
					personToDepartmentAccessesObject = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToDepartmentVehicleNormalAccessViewContainer>(personToDepartmentAccesses, _jsonSerializerSettings).ExtractPersonToDepartmentVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToDepartmentAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToDepartmentVehicleNormalAccessViewObjectsDataSet.PersonToDepartmentVehicleNormalAccessViewObjects.Values) 
                    {
                        personToDepartmentAccessesObject.Add(element);
                    }
				}	
				DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccessesObject = null; 
				{
					personToModelAccessesObject = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToModelVehicleNormalAccessViewContainer>(personToModelAccesses, _jsonSerializerSettings).ExtractPersonToModelVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToModelAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToModelVehicleNormalAccessViewObjectsDataSet.PersonToModelVehicleNormalAccessViewObjects.Values) 
                    {
                        personToModelAccessesObject.Add(element);
                    }
				}	
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().GetAccessesForVehiclesAsync(personToDepartmentAccessesObject, personToModelAccessesObject, personId, permissionLevel);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewCollectionContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForVehicles", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/updatevehiclepervehicleaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateVehiclePerVehicleAccessesForPersonDataSet([FromForm] System.Guid personId, [FromForm] string updatePerVehicleAccesses)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "updatePerVehicleAccesses", updatePerVehicleAccesses?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehiclePerVehicleAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updatePerVehicleAccessesObject = null; 
				{
					updatePerVehicleAccessesObject = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToPerVehicleNormalAccessViewContainer>(updatePerVehicleAccesses, _jsonSerializerSettings).ExtractPersonToPerVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    updatePerVehicleAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToPerVehicleNormalAccessViewObjectsDataSet.PersonToPerVehicleNormalAccessViewObjects.Values) 
                    {
                        updatePerVehicleAccessesObject.Add(element);
                    }
				}									
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateVehiclePerVehicleAccessesForPersonAsync(personId, updatePerVehicleAccessesObject, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehiclePerVehicleAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/updatevehiclepervehicleaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateVehiclePerVehicleAccessesForPerson([FromForm] System.Guid personId, [FromForm] string updatePerVehicleAccesses,bool? byRef)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personId", personId.ToString() },
					{ "updatePerVehicleAccesses", updatePerVehicleAccesses?.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehiclePerVehicleAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updatePerVehicleAccessesObject = null; 
				{
					updatePerVehicleAccessesObject = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToPerVehicleNormalAccessViewContainer>(updatePerVehicleAccesses, _jsonSerializerSettings).ExtractPersonToPerVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    updatePerVehicleAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToPerVehicleNormalAccessViewObjectsDataSet.PersonToPerVehicleNormalAccessViewObjects.Values) 
                    {
                        updatePerVehicleAccessesObject.Add(element);
                    }
				}	
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateVehiclePerVehicleAccessesForPersonAsync(personId, updatePerVehicleAccessesObject);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateVehiclePerVehicleAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/getaccessesformodels")]
        public async Task<ActionResult<PersonToModelVehicleNormalAccessViewCollectionContainer>> GetAccessesForModelsDataSet([FromForm] string personToDepartmentAccesses, [FromForm] System.Guid personId, [FromForm] System.Int32 permissionLevel)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personToDepartmentAccesses", personToDepartmentAccesses?.ToString() },
					{ "personId", personId.ToString() },
					{ "permissionLevel", permissionLevel.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForModels", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccessesObject = null; 
				{
					personToDepartmentAccessesObject = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToDepartmentVehicleNormalAccessViewContainer>(personToDepartmentAccesses, _jsonSerializerSettings).ExtractPersonToDepartmentVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToDepartmentAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToDepartmentVehicleNormalAccessViewObjectsDataSet.PersonToDepartmentVehicleNormalAccessViewObjects.Values) 
                    {
                        personToDepartmentAccessesObject.Add(element);
                    }
				}									
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().GetAccessesForModelsAsync(personToDepartmentAccessesObject, personId, permissionLevel, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewCollectionContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForModels", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/getaccessesformodels")]
        public async Task<ActionResult<PersonToModelVehicleNormalAccessViewCollectionContainer>> GetAccessesForModels([FromForm] string personToDepartmentAccesses, [FromForm] System.Guid personId, [FromForm] System.Int32 permissionLevel,bool? byRef)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personToDepartmentAccesses", personToDepartmentAccesses?.ToString() },
					{ "personId", personId.ToString() },
					{ "permissionLevel", permissionLevel.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForModels", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccessesObject = null; 
				{
					personToDepartmentAccessesObject = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToDepartmentVehicleNormalAccessViewContainer>(personToDepartmentAccesses, _jsonSerializerSettings).ExtractPersonToDepartmentVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToDepartmentAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToDepartmentVehicleNormalAccessViewObjectsDataSet.PersonToDepartmentVehicleNormalAccessViewObjects.Values) 
                    {
                        personToDepartmentAccessesObject.Add(element);
                    }
				}	
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().GetAccessesForModelsAsync(personToDepartmentAccessesObject, personId, permissionLevel);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewCollectionContainer>().Construct(componentReturn);

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GetAccessesForModels", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

        [HttpPost]
		[Route("dataset/api/vehicleaccessutilities/updateaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateAccessesForPersonDataSet([FromForm] string personToSiteAccesses, [FromForm] string personToDepartmentAccesses, [FromForm] string personToModelAccesses, [FromForm] string personToVehicleAccesses, [FromForm] System.Guid personId, [FromForm] System.Int32 PermissionLevel, [FromForm] System.Boolean cascadeAddPermission)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}

                // Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personToSiteAccesses", personToSiteAccesses?.ToString() },
					{ "personToDepartmentAccesses", personToDepartmentAccesses?.ToString() },
					{ "personToModelAccesses", personToModelAccesses?.ToString() },
					{ "personToVehicleAccesses", personToVehicleAccesses?.ToString() },
					{ "personId", personId.ToString() },
					{ "PermissionLevel", PermissionLevel.ToString() },
					{ "cascadeAddPermission", cascadeAddPermission.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccessesObject = null; 
				{
					personToSiteAccessesObject = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToSiteVehicleNormalAccessViewContainer>(personToSiteAccesses, _jsonSerializerSettings).ExtractPersonToSiteVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToSiteAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToSiteVehicleNormalAccessViewObjectsDataSet.PersonToSiteVehicleNormalAccessViewObjects.Values) 
                    {
                        personToSiteAccessesObject.Add(element);
                    }
				}									
				DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccessesObject = null; 
				{
					personToDepartmentAccessesObject = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToDepartmentVehicleNormalAccessViewContainer>(personToDepartmentAccesses, _jsonSerializerSettings).ExtractPersonToDepartmentVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToDepartmentAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToDepartmentVehicleNormalAccessViewObjectsDataSet.PersonToDepartmentVehicleNormalAccessViewObjects.Values) 
                    {
                        personToDepartmentAccessesObject.Add(element);
                    }
				}									
				DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccessesObject = null; 
				{
					personToModelAccessesObject = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToModelVehicleNormalAccessViewContainer>(personToModelAccesses, _jsonSerializerSettings).ExtractPersonToModelVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToModelAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToModelVehicleNormalAccessViewObjectsDataSet.PersonToModelVehicleNormalAccessViewObjects.Values) 
                    {
                        personToModelAccessesObject.Add(element);
                    }
				}									
				DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccessesObject = null; 
				{
					personToVehicleAccessesObject = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToPerVehicleNormalAccessViewContainer>(personToVehicleAccesses, _jsonSerializerSettings).ExtractPersonToPerVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToVehicleAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToPerVehicleNormalAccessViewObjectsDataSet.PersonToPerVehicleNormalAccessViewObjects.Values) 
                    {
                        personToVehicleAccessesObject.Add(element);
                    }
				}									
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateAccessesForPersonAsync(personToSiteAccessesObject, personToDepartmentAccessesObject, personToModelAccessesObject, personToVehicleAccessesObject, personId, PermissionLevel, cascadeAddPermission, null);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

				return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }            
			}
		}

		[HttpPost]
		[Route("api/vehicleaccessutilities/updateaccessesforperson")]
        public async Task<ActionResult<System.Boolean>> UpdateAccessesForPerson([FromForm] string personToSiteAccesses, [FromForm] string personToDepartmentAccesses, [FromForm] string personToModelAccesses, [FromForm] string personToVehicleAccesses, [FromForm] System.Guid personId, [FromForm] System.Int32 PermissionLevel, [FromForm] System.Boolean cascadeAddPermission,bool? byRef)
        {
            Exception error = null;

            try
            {
				// Manually validate the CSRF token
				try
				{
					await _antiforgery.ValidateRequestAsync(HttpContext);
				}
				catch (Exception ex)
				{
					_techLogger.Warn(ex, "Antiforgery validation failed."); 
                    // Return a ProblemDetails response with 403 status
                    return Problem(
                        title: "Antiforgery validation failed", 
                        detail: _showExceptionDetails ? "Invalid or missing CSRF token." : null, 
                        statusCode: StatusCodes.Status403Forbidden 
                    );
				}
				

				// Notify 'request begins' to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "GET", context = "BEGIN", data = null });
	
				var args = new Dictionary<string, string>()
				{
					{ "personToSiteAccesses", personToSiteAccesses?.ToString() },
					{ "personToDepartmentAccesses", personToDepartmentAccesses?.ToString() },
					{ "personToModelAccesses", personToModelAccesses?.ToString() },
					{ "personToVehicleAccesses", personToVehicleAccesses?.ToString() },
					{ "personId", personId.ToString() },
					{ "PermissionLevel", PermissionLevel.ToString() },
					{ "cascadeAddPermission", cascadeAddPermission.ToString() },
				};
			
				// Notify method call to extensions
				Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateAccessesForPerson", context = "BEFORE_CALL", data = JsonConvert.SerializeObject(args) });

				DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccessesObject = null; 
				{
					personToSiteAccessesObject = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToSiteVehicleNormalAccessViewContainer>(personToSiteAccesses, _jsonSerializerSettings).ExtractPersonToSiteVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToSiteAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToSiteVehicleNormalAccessViewObjectsDataSet.PersonToSiteVehicleNormalAccessViewObjects.Values) 
                    {
                        personToSiteAccessesObject.Add(element);
                    }
				}	
				DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccessesObject = null; 
				{
					personToDepartmentAccessesObject = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToDepartmentVehicleNormalAccessViewContainer>(personToDepartmentAccesses, _jsonSerializerSettings).ExtractPersonToDepartmentVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToDepartmentAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToDepartmentVehicleNormalAccessViewObjectsDataSet.PersonToDepartmentVehicleNormalAccessViewObjects.Values) 
                    {
                        personToDepartmentAccessesObject.Add(element);
                    }
				}	
				DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccessesObject = null; 
				{
					personToModelAccessesObject = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToModelVehicleNormalAccessViewContainer>(personToModelAccesses, _jsonSerializerSettings).ExtractPersonToModelVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToModelAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToModelVehicleNormalAccessViewObjectsDataSet.PersonToModelVehicleNormalAccessViewObjects.Values) 
                    {
                        personToModelAccessesObject.Add(element);
                    }
				}	
				DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccessesObject = null; 
				{
					personToVehicleAccessesObject = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>();
					var dataset = JsonConvert.DeserializeObject<PersonToPerVehicleNormalAccessViewContainer>(personToVehicleAccesses, _jsonSerializerSettings).ExtractPersonToPerVehicleNormalAccessView().ObjectsDataSet as ObjectsDataSet;
                    
                    personToVehicleAccessesObject.ObjectsDataSet = dataset;

                    foreach (var element in  dataset.PersonToPerVehicleNormalAccessViewObjectsDataSet.PersonToPerVehicleNormalAccessViewObjects.Values) 
                    {
                        personToVehicleAccessesObject.Add(element);
                    }
				}	
				var response = await _serviceProvider.GetRequiredService<IVehicleAccessUtilitiesSurrogate>().UpdateAccessesForPersonAsync(personToSiteAccessesObject, personToDepartmentAccessesObject, personToModelAccessesObject, personToVehicleAccessesObject, personId, PermissionLevel, cascadeAddPermission);

				if (response.ShouldRedirect)
				{
					return Redirect(response.RedirectUrl);
				}				
				var componentReturn = response.Result;
				var result = componentReturn;

                // Notify result to extensions
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "UpdateAccessesForPerson", context = "JSON", data = JsonConvert.SerializeObject(result) });

                return result;
            }

            catch (GOServerException exception)
            {
                error = exception;

                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, instance : exception.Reason, title: exception.Message, statusCode: exception.HttpResponseCode);
            }
            catch (Exception exception)
            {
                error = exception;
 
                // Notify to extensions and re-throw
                Extensions.OnRequestAction(new ApiExtensionResponseData { action = "EXCEPTION", context = exception.GetType().Name, data = exception.ToString(), error = true });
                
                return Problem(detail: _showExceptionDetails ? exception.ToString() : null, title: exception.Message);
            }
            finally
            {
				try
                {
                    // Check no transaction left running
                    await _serviceProvider.GetRequiredService<IDataProviderTransaction>().AbortAnyOngoingTransactionAsync(error);
                }
                catch (Exception exception)
                {
                    _techLogger.Error(exception, "Error in call to finally");
                }
			}
		}

    }
}