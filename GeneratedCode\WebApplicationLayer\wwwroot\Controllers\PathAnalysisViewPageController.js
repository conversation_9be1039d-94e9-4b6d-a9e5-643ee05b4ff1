﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

(function () {
	// 
	FleetXQ.Web.Controllers.PathAnalysisViewPageController = function(applicationController) {
		var self = this;
		this.subscriptions = [];
		// store all subscriptions in this array, to unsubscribe on release.
		
		this._objectType = "PathAnalysisViewPage";

		this.applicationController = applicationController;
		this.ObjectsDataSet = applicationController.ObjectsDataSet;
		this.contextId = [this.applicationController.getNextContextId()];
		this.customController = undefined;

		// Integrate custom code if any
		if (FleetXQ.Web.Controllers.PathAnalysisViewPageControllerCustom !== undefined) {
		    this.customController = new FleetXQ.Web.Controllers.PathAnalysisViewPageControllerCustom(self);
		};

		// Initialize View Models and Data Stores
		
			this.SiteFloorPlanForm1ViewModel = new FleetXQ.Web.ViewModels.SiteFloorPlanForm1ViewModel(this, $("#SiteFloorPlanForm1Control"), null, null, this.contextId);		
		this.SiteFloorPlanForm1ViewModel.StatusData.ShowTitle(false);		
		this.customPageTitle = ko.observable(null);

		this.iconToUse = ko.observable(null);

		this.pageTitle = ko.pureComputed (function() {
			if (self.customPageTitle()) {
				return self.customPageTitle();
			}
			return "Path Analysis View";
		});

		this.pageTitleParams = ko.observable({});
		
		this.showGoBackBadge = ko.observable(false);
		this.goBackBadgeLabel = ko.observable("pages/PathAnalysisViewPage:navigation.BackNavigationBadge");
		this.showItemCountBadge = ko.observable(false);
		this.isLoading = ko.observable(false);
		this.itemCount = ko.pureComputed(function() {
			return null;
		}); 
		this.goBack = function () {
			if(self.IsInEditMode && self.IsInEditMode()) {
				if(!confirm(FleetXQ.Web.Messages.i18n.t('messages.unsavedChanges')))
					return;
				
				// Cancel edit mode on current page controller
				if (self.CancelEdit) {
					self.CancelEdit(false);
				}					
			}

};

		this.subscriptions.push(this.SiteFloorPlanForm1ViewModel.StatusData.IsBusy.subscribe( function (newValue) { self.OnSiteFloorPlanForm1ViewModelIsBusyChanged(newValue); }));
		
		this.IsInEditMode = function() {

			return  (self.SiteFloorPlanForm1ViewModel.StatusData.DisplayMode && self.SiteFloorPlanForm1ViewModel.StatusData.DisplayMode() == 'edit');
		};

		this.CancelEdit = function(isCommandCall) {
			if (self.SiteFloorPlanForm1ViewModel.CancelEdit) {
				self.SiteFloorPlanForm1ViewModel.CancelEdit(isCommandCall);
			}
		};

		// Events Handlers
		this.OnSiteFloorPlanForm1ViewModelIsBusyChanged = function (newValue) {
		};

		this.initialize = function() {
			if (FleetXQ.Web.Controllers.Custom && FleetXQ.Web.Controllers.Custom.getPageTitle) {
				self.customPageTitle(FleetXQ.Web.Controllers.Custom.getPageTitle(self));
			}

			var partsCount = location.hash.split("/").length;
			var lasttagindex = 0;
			if (partsCount == 4) {
				var hash = window.location.hash;
				var allPksValid = true;
				lasttagindex = hash.lastIndexOf('/');
				var pk1 = GO.Encoding.UrlDecode(hash.substring(lasttagindex + 1).split(new RegExp("#", "g"))[0]);
				allPksValid = allPksValid && GO.IsGuid(pk1);			
				
				if(allPksValid) {
					var objectToLoad = new FleetXQ.Web.Model.DataObjects.SiteFloorPlanObject();
					objectToLoad.Data.Id(pk1);	
					self.SiteFloorPlanForm1ViewModel.LoadSiteFloorPlan(objectToLoad);	
				}
			}
	
			// Call custom initialize if defined
			if (self.customController !== undefined && self.customController.initialize !== undefined) {
			    self.customController.initialize();
			}
		};
		this.initialize();



		this.release = function() {
			// unsubscribe
			for(var i = 0;i <  self.subscriptions.length;i++) {
				self.subscriptions[i].dispose();
			}
			self.subscriptions = [];
			self.ObjectsDataSet.cleanContext(self.contextId);
			
			self.SiteFloorPlanForm1ViewModel.release();
			self.SiteFloorPlanForm1ViewModel = null;
		};


		return self;
	};		
	
	if (window.ApplicationSourceHandler)
		window.ApplicationSourceHandler.onSourceLoaded("/Controllers/PathAnalysisViewPageController.js");
} ());
