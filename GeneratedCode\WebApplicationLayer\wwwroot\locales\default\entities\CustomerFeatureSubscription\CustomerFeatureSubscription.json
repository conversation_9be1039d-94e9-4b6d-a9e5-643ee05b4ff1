﻿{
  "entityName": "CustomerFeatureSubscription",
  "entityNamePlural": "CustomerFeatureSubscriptions",   "entityDescription": "This entity represents a CustomerFeatureSubscription",
  "fields": {
    "CanAccessSlamcore": {
        "displayName": "CanAccessSlamcore", 
        "description": "CanAccessSlamcore"
    },
    "Customer": {
        "displayName": "Customer", 
        "description": "Customer"
    },
    "Description": {
        "displayName": "Description", 
        "description": "Description"
    },
    "HasAdditionalHardwaresAccess": {
        "displayName": "HasAdditionalHardwaresAccess", 
        "description": "HasAdditionalHardwaresAccess"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "IsEnabled": {
        "displayName": "IsEnabled?", 
        "description": "IsEnabled?"
    },
    "IsTagged": {
        "displayName": "IsTagged?", 
        "description": "If true, will not follow dealer default subscriptions"
    },
    "Name": {
        "displayName": "Name", 
        "description": "Name"
    }
  }
} 