﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMVehicleLockout" 
		table="[VehicleLockout]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="Comment" >
            <column name="`Comment`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="LockoutTime" >
            <column name="`LockoutTime`" sql-type="datetime" not-null="true" />
        </property> 
		<property name="Note" >
            <column name="`Note`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="RealImpact" >
            <column name="`RealImpact`" sql-type="int" not-null="false" />
        </property> 
		<property name="Reason" >
            <column name="`Reason`" sql-type="int" not-null="true" />
        </property> 
		<property name="UnlockDateTime" >
            <column name="`UnlockDateTime`" sql-type="datetime" not-null="true" />
        </property> 

		
		<!-- many-to-one GOUser -->
		<property name="GOUserId" type="System.Guid" not-null="false" formula = "[GOUserId]"></property>  
		<many-to-one name="GOUser"  > 
			<column name="`GOUserId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
		
		<!-- many-to-one Vehicle -->
		<property name="VehicleId" type="System.Guid" not-null="false" formula = "[VehicleId]"></property>  
		<many-to-one name="Vehicle"  > 
			<column name="`VehicleId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
		
		<!-- many-to-one Session -->
		<property name="SessionId" type="System.Guid" not-null="false" formula = "[SessionId]"></property>  
		<many-to-one name="Session"  > 
			<column name="`SessionId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
		
		<!-- many-to-one Driver -->
		<property name="DriverId" type="System.Guid" not-null="false" formula = "[DriverId]"></property>  
		<many-to-one name="Driver"  > 
			<column name="`DriverId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
 




    </class> 

</hibernate-mapping>