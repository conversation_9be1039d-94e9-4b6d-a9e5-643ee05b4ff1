﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using GenerativeObjects.Practices.Logging;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;

namespace FleetXQ.Data.DataObjects
{
    public class EntityModelCache : IEntityModelCache
    {
        private Dictionary<string, Dictionary<string, PathNode>> _pathNodes;
        private readonly object _lockPathNodes = new object();

	    private static Dictionary<string,string> _entityRelations = new Dictionary<string,string>()
        {
          { "AccessGroup", "AccessGroupsToSites, Customer, PersonItems" },
          { "AccessGroupToSite", "Site, AccessGroup" },
          { "Alert", "AlertSubscriptions" },
          { "AlertHistory", "Vehicle, Driver, Alert" },
          { "AlertSubscription", "GOUser, Alert, Person, VehicleAlertSubscriptionItems" },
          { "AllChecklistResultView", "Dealer, ChecklistResult" },
          { "AllDriverAccessAbuseStoreProcedure", "Dealer, Driver" },
          { "AllEmailSubscriptionStoreProcedure", "ReportSubscription" },
          { "AllImpactsView", "Dealer, Impact" },
          { "AllLicenseExpiryView", "Dealer, Driver" },
          { "AllMessageHistoryStoreProcedure", "MessageHistory" },
          { "AllUserSummaryStoreProcedure", "Dealer, Person" },
          { "AllVehicleCalibrationStoreProcedure", "Vehicle" },
          { "AllVehicleUnlocksView", "VehicleLockout, Dealer" },
          { "AllVORSessionsPerVehicleStoreProcedure", "Vehicle, VORReportCombinedView" },
          { "AllVORStatusStoreProcedure", "VORReportCombinedView, VORSettingHistory" },
          { "BroadcastMessage", "VehicleBroadcastMessageItems" },
          { "BroadcastMessageHistory", "Driver, Vehicle" },
          { "Canrule", "Vehicles, CanruleDetailsItems" },
          { "CanruleDetails", "Canrule" },
          { "Card", "SiteVehicleNormalCardAccessItems, DepartmentVehicleMasterCardAccessItems, PerVehicleNormalCardAccessItems, CardToCardAccessItems, ModelVehicleMasterCardAccessItems, DealerDriver, DepartmentVehicleNormalCardAccessItems, PerVehicleMasterCardAccessItems, SiteVehicleMasterCardAccessItems, Driver, ModelVehicleNormalCardAccessItems" },
          { "CardToCardAccess", "Card, CardAccessLevel" },
          { "ChecklistDetail", "PreOperationalChecklist, ChecklistResults" },
          { "ChecklistFailurePerVechicleView", "PreOperationalChecklist, Vehicle" },
          { "ChecklistFailureView", "PreOperationalChecklist" },
          { "ChecklistResult", "Session, ChecklistAnswerDetails, AllChecklistResultViewItems" },
          { "ChecklistSettings", "Vehicle" },
          { "ChecklistStatusView", "Site, Customer, Department, Dealer" },
          { "ContactPersonInformation", "Customer" },
          { "Country", "Customers" },
          { "CurrentDriverStatusChartView", "Department, Site, Dealer, Customer" },
          { "CurrentStatusCombinedView", "CurrentStatusDriverViewItems, CurrentStatusVehicleViewItems" },
          { "CurrentStatusDriverView", "Driver, CurrentStatusCombinedView, Dealer" },
          { "CurrentStatusVehicleView", "Dealer, CurrentStatusCombinedView, Vehicle" },
          { "CurrentVehicleStatusChartView", "Customer, Department, Site, Dealer" },
          { "Customer", "DriverItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, CustomerAudit, VehicleUtilizationLastTwelveHoursViewItems, DashboardCardViewItems, ChecklistStatusViewItems, Country, ContactPersonInformation, EmailGroupsItems, DashboardDriverCardStoreProcedureItems, Sites, Dealer, TodaysPreopCheckStoreProcedureItems, DashboardFilterItems, CustomerPreOperationalChecklistTemplateItems, CustomerToModelItems, ImpactFrequencyPerWeekMonthViewItems, CustomerFeatureSubscription, CustomerSSODetailItems, SlamcoreDeviceItems, TodaysPreopCheckViewItems, CustomerToPersonViewItems, TodaysImpactStoreProcedureItems, DriverLicenseExpiryViewItems, AccessGroupItems, CustomerModelItems, ImpactFrequencyPerTimeSlotViewItems, ImpactFrequencyPerWeekDayViewItems, CustomerSnapshotItems, LoggedHoursVersusSeatHoursViewItems, DepartmentItems, DriverLicenseExpiryStoreProcedureItems, GoUserToCustomerItems, IncompletedChecklistViewItems, CurrentVehicleStatusChartViewItems, VehicleItems, CurrentDriverStatusChartViewItems, DashboardVehicleCardStoreProcedureItems, DashboardVehicleCardViewItems, TodaysImpactViewItems, PersonItems" },
          { "CustomerAudit", "GOUserWhoDeletedThisCustomer, RevisionCreated, GOUserWhoModifiedThisCustomer, RevisionDeleted, Customer, RevisionLastModified, GOUserWhoCreatedThisCustomer" },
          { "CustomerFeatureSubscription", "Customer" },
          { "CustomerModel", "Model, Customer" },
          { "CustomerPreOperationalChecklistTemplate", "Customer" },
          { "CustomerSnapshot", "CustomerBeingChangeTracked" },
          { "CustomerSSODetail", "Customer" },
          { "CustomerToModel", "Customer, Model" },
          { "CustomerToPersonView", "Customer, EmailGroupsToPersonItems, Person" },
          { "DashboardDriverCardStoreProcedure", "Customer, Site, Department, Dealer" },
          { "DashboardDriverCardView", "Site, Department, Customer, Dealer" },
          { "DashboardFilter", "Site, Customer, Department" },
          { "DashboardVehicleCardStoreProcedure", "Site, Dealer, Department, Customer" },
          { "DashboardVehicleCardView", "Dealer, Department, Site, Customer" },
          { "Dealer", "DealerFeatureSubscription, CurrentVehicleStatusChartViewItems, GeneralProductivityPerDriverViewLatestItems, TodaysImpactStoreProcedureItems, AllLicenseExpiryViewItems, AllVehicleUnlocksViewItems, ImpactFrequencyPerTimeSlotViewItems, ModuleItems, GOUserItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, TodaysImpactViewItems, DriverProficiencyViewItems, IncompletedChecklistViewItems, AllImpactsViewItems, ModelItems, AllDriverAccessAbuseStoreProcedureItems, DashboardDriverCardStoreProcedureItems, DashboardVehicleCardStoreProcedureItems, VehicleUtilizationLastTwelveHoursViewItems, DashboardCardViewItems, CurrentDriverStatusChartViewItems, DealerConfiguration, Customers, VehicleProficiencyViewItems, ChecklistStatusViewItems, DriverLicenseExpiryViewItems, LoggedHoursVersusSeatHoursViewItems, TodaysPreopCheckViewItems, CurrentStatusDriverViewItems, ImpactFrequencyPerWeekMonthViewItems, AllUserSummaryStoreProcedureItems, AllChecklistResultViewItems, TodaysPreopCheckStoreProcedureItems, CurrentStatusVehicleViewItems, GeneralProductivityPerVehicleViewItems, FeatureSubscriptionsFilterItems, DashboardVehicleCardViewItems, DriverLicenseExpiryStoreProcedureItems, Region, ImpactFrequencyPerWeekDayViewItems" },
          { "DealerConfiguration", "Dealer" },
          { "DealerDriver", "Card, GOUser" },
          { "DealerFeatureSubscription", "Dealer" },
          { "Department", "DashboardVehicleCardViewItems, DriverItems, CurrentDriverStatusChartViewItems, IncompletedChecklistViewItems, PersonToDepartmentVehicleNormalAccessViewItems, VehicleUtilizationLastTwelveHoursViewItems, DepartmentVehicleNormalCardAccessItems, DepartmentHourSettings, SiteChecklistItems, PersonToDepartmentVehicleMasterAccessViewItems, Vehicles, TodaysImpactViewItems, ImpactFrequencyPerTimeSlotViewItems, PersonAllocationItems, PersonToModelVehicleNormalAccessViewItems, DashboardDriverCardStoreProcedureItems, Site, ImpactFrequencyPerWeekDayViewItems, DashboardVehicleCardStoreProcedureItems, DriverLicenseExpiryViewItems, TodaysPreopCheckViewItems, ImpactFrequencyPerWeekMonthViewItems, VehicleHireDehireHistoryItems, TodaysImpactStoreProcedureItems, DriverLicenseExpiryStoreProcedureItems, ChecklistStatusViewItems, DashboardFilterItems, TodaysPreopCheckStoreProcedureItems, PersonItems, DashboardCardViewItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, Customer, CurrentVehicleStatusChartViewItems, LoggedHoursVersusSeatHoursViewItems, DepartmentVehicleMasterCardAccessItems" },
          { "DepartmentChecklist", "Model, PreOperationalChecklists, Department" },
          { "DepartmentHourSettings", "Department" },
          { "DepartmentVehicleMasterCardAccess", "PerVehicleMasterCardAccessItems, Card, Department" },
          { "DepartmentVehicleNormalCardAccess", "Card, Department, CardAccessLevel" },
          { "DetailedSessionView", "Vehicle, Driver, Session" },
          { "DetailedVORSessionStoreProcedure", "Session, Vehicle" },
          { "Driver", "VehicleLockouts, BroadcastMessageHistoryItems, ImpactsForVehicleViewItems, Person, LicensesByModel, AllLicenseExpiryViewItems, DetailedSessionViewItems, Customer, AllDriverAccessAbuseStoreProcedureItems, OnDemandSessionItems, Card, Department, PedestrianDetectionHistoryItems, GeneralLicence, CurrentStatusDriverViewItems, GeneralProductivityPerDriverViewLatestItems, DriverProficiencyViewItems, Site, Sessions" },
          { "DriverLicenseExpiryStoreProcedure", "Dealer, Customer, Department, Site" },
          { "DriverLicenseExpiryView", "Department, Site, Dealer, Customer" },
          { "DriverProficiencyView", "ProficiencyCombinedView, Dealer, Driver" },
          { "EmailGroups", "EmailGroupsToPersonItems, Customer, ReportSubscriptions" },
          { "EmailGroupsToPerson", "EmailGroups, Person, CustomerToPersonView" },
          { "ExportJobStatus", "GOUser" },
          { "FeatureSubscriptionsFilter", "Dealer" },
          { "FloorPlan", "FloorZonesItems, SiteFloorPlanItems, Site" },
          { "FloorZones", "FloorPlan, ZoneCoordinatesItems" },
          { "GeneralProductivityPerDriverViewLatest", "GeneralProductivityView, Driver, Dealer" },
          { "GeneralProductivityPerVehicleView", "Vehicle, GeneralProductivityView, Dealer" },
          { "GeneralProductivityView", "GeneralProductivityPerVehicleViewItems, UnitUnutilisationStoreProcedureItems, UnitUtilisationStoreProcedureItems, GeneralProductivityPerDriverViewLatestItems" },
          { "GOGroup", "GroupRoleItems, UserGroupItems" },
          { "GOGroupRole", "Group, Role" },
          { "GORole", "GOUserItems, GroupRoleItems, UserRoleItems" },
          { "GOUser", "UserGroupItems, UserRoleItems, CustomerAuditItemsCreated, DealerDriver, Person, CustomerAuditItemsDeleted, VehicleLockoutItems, MessageHistoryItems, RevisionItems, ExportJobStatusItems, ReportSubscriptionItems, AlertSubscriptionItems, CustomerAuditItemsModified, GOUserDepartmentItems, GORole, Tag, GoUserToCustomerItems, Dealer" },
          { "GOUserDepartment", "GOUser, Department" },
          { "GOUserGroup", "User, Group" },
          { "GOUserRole", "Role, User" },
          { "GoUserToCustomer", "GOUser, Customer" },
          { "GPSHistory", "Session" },
          { "Impact", "Session, ImpactsForVehicleView, AllImpactsViewItems" },
          { "ImpactFrequencyPerTimeSlotView", "Site, Dealer, Customer, Department" },
          { "ImpactFrequencyPerWeekDayView", "Dealer, Site, Customer, Department" },
          { "ImpactFrequencyPerWeekMonthView", "Dealer, Department, Customer, Site" },
          { "ImpactsForVehicleView", "Vehicle, Impact, Driver" },
          { "ImportJobBatch", "ImportJobLogItems, ImportJobStatusItems" },
          { "ImportJobLog", "ImportJobStatus, ImportJobBatch" },
          { "ImportJobStatus", "ImportJobBatch, ImportJobLogItems" },
          { "IncompletedChecklistView", "Customer, Site, Department, Dealer" },
          { "Inspection", "Vehicle" },
          { "IOFIELD", "PSTATDetailsItems, SessionDetailsItems" },
          { "LicenceDetail", "Driver" },
          { "LicenseByModel", "Driver, Model" },
          { "LoggedHoursVersusSeatHoursView", "Customer, Site, Dealer, Department" },
          { "MessageHistory", "GOUser, Vehicle, AllMessageHistoryStoreProcedureItems" },
          { "Model", "Vehicles, SiteChecklists, CustomerModelItems, VehiclesPerModelReportItems, Dealer, PersonToModelVehicleNormalAccessViewItems, CustomerToModelItems, ModelVehicleNormalCardAccessItems, ModelVehicleMasterCardAccessItems, LicensesByModel, PersonToModelVehicleMasterAccessViewItems" },
          { "ModelVehicleMasterCardAccess", "PerVehicleMasterCardAccessItems, Model, Card" },
          { "ModelVehicleNormalCardAccess", "Card, CardAccessLevel, Department, Model" },
          { "Module", "Vehicle, Dealer, ModuleHistoryItems" },
          { "ModuleHistory", "Vehicle, Module" },
          { "NetworkSettings", "Vehicle" },
          { "OnDemandAuthorisationStoreProcedure", "OnDemandSession" },
          { "OnDemandSession", "Driver, OnDemandAuthorisationStoreProcedureItems, Vehicle" },
          { "OnDemandSettings", "Vehicle" },
          { "PedestrianDetectionHistory", "Driver, Vehicle" },
          { "Permission", "CardToCardAccessItems" },
          { "Person", "Site, Customer, PersonChecklistLanguageSettings, PersonAllocationItems, PersonToSiteVehicleNormalAccessViewItems, ReportSubscriptionItems, GOUser, AllUserSummaryStoreProcedureItems, AlertSubscriptions, VORSettingHistoryItems, PersonToDepartmentVehicleMasterAccessViewItems, Driver, PersonToPerVehicleNormalAccessViewItems, PersonToModelVehicleNormalAccessViewItems, AccessGroup, WebsiteUser, Department, PersonToModelVehicleMasterAccessViewItems, PersonToDepartmentVehicleNormalAccessViewItems, EmailGroupsToPersonItems, PersonToSiteVehicleMasterAccessViewItems, PersonToPerVehicleMasterAccessViewItems, CustomerToPersonViewItems, VehicleItems" },
          { "PersonAllocation", "Site, Department, Person" },
          { "PersonChecklistLanguageSettings", "Person" },
          { "PersonToDepartmentVehicleMasterAccessView", "Department, Person" },
          { "PersonToDepartmentVehicleNormalAccessView", "Person, CardAccessLevel, Department" },
          { "PersonToModelVehicleMasterAccessView", "Model, Person" },
          { "PersonToModelVehicleNormalAccessView", "Model, Department, Person, CardAccessLevel" },
          { "PersonToPerVehicleMasterAccessView", "Person, Vehicle" },
          { "PersonToPerVehicleNormalAccessView", "Person, CardAccessLevel, Vehicle" },
          { "PersonToSiteVehicleMasterAccessView", "Person, Site" },
          { "PersonToSiteVehicleNormalAccessView", "CardAccessLevel, Person, Site" },
          { "PerVehicleMasterCardAccess", "DepartmentVehicleMasterCardAccess, Card1, ModelVehicleMasterCardAccess, Vehicle, SiteVehicleNormalCardAccess" },
          { "PerVehicleNormalCardAccess", "Card, Vehicle, CardAccessLevel, VehicleSupervisorsViewItems" },
          { "PreOperationalChecklist", "ChecklistFailureView, VehicleToPreOpCheckilstItems, ChecklistAnswersDetails, ChecklistFailurePerVechicleViewItems, SiteChecklist" },
          { "ProficiencyCombinedView", "VehicleProficiencyViewItems, DriverProficiencyViewItems" },
          { "PSTATDetails", "Session, IOFIELD" },
          { "Region", "Dealers" },
          { "ReportSubscription", "GOUser, Department, Person, ReportType, EmailGroups, Site, Customer, AllEmailSubscriptionStoreProcedureItems" },
          { "ReportType", "ReportSubscriptions" },
          { "Revision", "SnapshotItems, GOUser, TagItems" },
          { "ServiceSettings", "Vehicle" },
          { "Session", "PSTATDetailsItems, VehicleLockouts, Vehicle, Driver, SessionDetailsItems, VehicleGPSItems, GPSHistoryItems, DetailedVORSessionStoreProcedureItems, Impacts, VehicleLastGPSLocationView, DetailedSessionViewItems, ChecklistResults" },
          { "SessionDetails", "IOFIELD, Session" },
          { "Site", "CurrentVehicleStatusChartViewItems, TodaysImpactStoreProcedureItems, ImpactFrequencyPerWeekMonthViewItems, SiteVehicleMasterCardAccessItems, ImpactFrequencyPerWeekDayViewItems, DepartmentItems, TodaysPreopCheckViewItems, CurrentDriverStatusChartViewItems, DashboardDriverCardStoreProcedureItems, VehicleUtilizationLastTwelveHoursStoreProcedureItems, DashboardFilterItems, DriverItems, SiteFloorPlanItems, DashboardVehicleCardStoreProcedureItems, ImpactFrequencyPerTimeSlotViewItems, AccessGroupToSiteItems, IncompletedChecklistViewItems, PersonItems, TodaysPreopCheckStoreProcedureItems, DashboardCardViewItems, PersonToSiteVehicleMasterAccessViewItems, Timezone, TodaysImpactViewItems, PersonAllocationItems, FloorPlanItems, Customer, PersonToSiteVehicleNormalAccessViewItems, VehicleItems, VehicleUtilizationLastTwelveHoursViewItems, SiteVehicleNormalCardAccessItems, LoggedHoursVersusSeatHoursViewItems, DriverLicenseExpiryViewItems, ChecklistStatusViewItems, DashboardVehicleCardViewItems, DriverLicenseExpiryStoreProcedureItems" },
          { "SiteFloorPlan", "Site, FloorPlan" },
          { "SiteVehicleMasterCardAccess", "Site, Card" },
          { "SiteVehicleNormalCardAccess", "CardAccessLevel, Site, Card, PerVehicleMasterCardAccessItems" },
          { "SlamcoreAPIKey", "SlamcoreDevice" },
          { "SlamcoreAwareAuthenticationDetails", "SlamcoreDevice" },
          { "SlamcoreDevice", "SlamcoreAPIKey, SlamcorePedestrianDetectionItems, SlamcoreAwareAuthenticationDetails, SlamcoreDeviceConnectionViewItems, VehicleSlamcoreLocationHistoryItems, SlamcoreDeviceHistoryItems, Vehicle, Customer" },
          { "SlamcoreDeviceConnectionView", "SlamcoreDevice" },
          { "SlamcoreDeviceHistory", "SlamcoreDevice, Vehicle" },
          { "SlamcorePedestrianDetection", "SlamcoreDevice" },
          { "Snapshot", "Revision" },
          { "Tag", "GOUser, Revision" },
          { "Timezone", "Sites" },
          { "TodaysImpactStoreProcedure", "Site, Dealer, Customer, Department" },
          { "TodaysImpactView", "Department, Site, Customer, Dealer" },
          { "TodaysPreopCheckStoreProcedure", "Dealer, Site, Customer, Department" },
          { "TodaysPreopCheckView", "Site, Customer, Dealer, Department" },
          { "UnitUnutilisationStoreProcedure", "Vehicle, GeneralProductivityView" },
          { "UnitUtilisationStoreProcedure", "GeneralProductivityView, Vehicle" },
          { "UpdateFirmwareRequest", "Firmware" },
          { "Vehicle", "Module, VehicleToPreOpCheckilstItems, CurrentStatusVehicleViewItems, PedestrianDetectionHistoryItems, ChecklistSettings, DetailedVORSessionStoreProcedureItems, VehicleHireDehireHistoryItems, PersonToPerVehicleMasterAccessViewItems, VehicleSupervisorsViewItems, GeneralProductivityPerVehicleViewItems, DetailedSessionViewItems, VehicleCardAccesses, Inspection, Canrule, PersonToPerVehicleNormalAccessViewItems, Firmware, MessageHistoryItems, VORSettingHistoryItems, VehicleGPSLocations, VehicleLockoutItems, VehicleBroadcastMessageItems, DepartmentChecklist, Sessions, Department, UnitUnutilisationStoreProcedureItems, Site, OnDemandSettings, SlamcoreDeviceHistoryItems, Model, AllVORSessionsPerVehicleStoreProcedureItems, Driver, VehicleLastGPSLocationView, Customer, NetworkSettingsItems, VehicleAlertSubscriptionItems, Person, ImpactsForVehicleViewItems, ChecklistFailurePerVechicleViewItems, VehicleProficiencyViewItems, VehicleOtherSettings, AllVehicleCalibrationStoreProcedureItems, VehicleDiagnostic, VehicleSessionlessImpactItems, BroadcastMessageHistoryItems, OnDemandSessionItems, UnitUtilisationStoreProcedureItems, ServiceSettings, ModuleHistoryItems, PerVehicleNormalCardAccessItems" },
          { "VehicleAlertSubscription", "Vehicle, AlertSubscription" },
          { "VehicleBroadcastMessage", "Vehicle, BroadcastMessage" },
          { "VehicleDiagnostic", "Vehicle" },
          { "VehicleGPS", "Vehicle, Session" },
          { "VehicleHireDehireHistory", "Vehicle, Department" },
          { "VehicleLastGPSLocationView", "Session, Vehicle" },
          { "VehicleLockout", "AllVehicleUnlocksViewItems, Session, Driver, Vehicle, GOUser" },
          { "VehicleOtherSettings", "Vehicle" },
          { "VehicleProficiencyView", "Vehicle, Dealer, ProficiencyCombinedView" },
          { "VehicleSessionlessImpact", "Vehicle" },
          { "VehicleSlamcoreLocationHistory", "SlamcoreDevice" },
          { "VehiclesPerModelReport", "Model" },
          { "VehicleSupervisorsView", "Vehicle, PerVehicleNormalCardAccess" },
          { "VehicleToPreOpChecklistView", "PreOperationalChecklist, Vehicle" },
          { "VehicleUtilizationLastTwelveHoursStoreProcedure", "Customer, Department, Site, Dealer" },
          { "VehicleUtilizationLastTwelveHoursView", "Department, Customer, Dealer, Site" },
          { "VORReportCombinedView", "AllVORStatusStoreProcedureItems, AllVORSessionsPerVehicleStoreProcedureItems" },
          { "VORSettingHistory", "Vehicle, AllVORStatusStoreProcedureItems, Person" },
          { "WebsiteRole", "WebsiteUser" },
          { "WebsiteUser", "Person, WebsiteRoles" },
          { "ZoneCoordinates", "FloorZones" },
      };

        public Dictionary<string, string> EntityRelations 
	    {
		    get
		    {
			    return _entityRelations;
		    }
	    }

        public Dictionary<string, Dictionary<string, PathNode>> PathNodes
        {
            get
            {
                lock(_lockPathNodes)
                {
                    if (_pathNodes == null)
                    {
                        _pathNodes = new Dictionary<string,Dictionary<string,PathNode>>();
					    _pathNodes.Add ( "ChecklistDetailDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ChecklistDetailDataObject"].Add( "preoperationalchecklist", new PathNode { EntityName = "PreOperationalChecklistDataObject", PathName = "PreOperationalChecklist" });
					    _pathNodes["ChecklistDetailDataObject"].Add( "checklistresults", new PathNode { EntityName = "ChecklistResultDataObject", PathName = "ChecklistResults" });
					    _pathNodes.Add ( "SlamcoreAwareAuthenticationDetailsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SlamcoreAwareAuthenticationDetailsDataObject"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDeviceDataObject", PathName = "SlamcoreDevice" });
					    _pathNodes.Add ( "PersonToModelVehicleMasterAccessViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonToModelVehicleMasterAccessViewDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes["PersonToModelVehicleMasterAccessViewDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes.Add ( "VehicleLockoutDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleLockoutDataObject"].Add( "allvehicleunlocksviewitems", new PathNode { EntityName = "AllVehicleUnlocksViewDataObject", PathName = "AllVehicleUnlocksViewItems" });
					    _pathNodes["VehicleLockoutDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes["VehicleLockoutDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["VehicleLockoutDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["VehicleLockoutDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes.Add ( "RevisionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["RevisionDataObject"].Add( "snapshotitems", new PathNode { EntityName = "SnapshotDataObject", PathName = "SnapshotItems" });
					    _pathNodes["RevisionDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes["RevisionDataObject"].Add( "tagitems", new PathNode { EntityName = "TagDataObject", PathName = "TagItems" });
					    _pathNodes.Add ( "AlertDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AlertDataObject"].Add( "alertsubscriptions", new PathNode { EntityName = "AlertSubscriptionDataObject", PathName = "AlertSubscriptions" });
					    _pathNodes.Add ( "WebsiteUserDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["WebsiteUserDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["WebsiteUserDataObject"].Add( "websiteroles", new PathNode { EntityName = "WebsiteRoleDataObject", PathName = "WebsiteRoles" });
					    _pathNodes.Add ( "AllLicenseExpiryViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllLicenseExpiryViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["AllLicenseExpiryViewDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes.Add ( "DriverLicenseExpiryViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DriverLicenseExpiryViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["DriverLicenseExpiryViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["DriverLicenseExpiryViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["DriverLicenseExpiryViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "ContactPersonInformationDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ContactPersonInformationDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "ChecklistSettingsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ChecklistSettingsDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "VehicleDiagnosticDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleDiagnosticDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "VehicleGPSDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleGPSDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["VehicleGPSDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes.Add ( "ModuleDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ModuleDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["ModuleDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["ModuleDataObject"].Add( "modulehistoryitems", new PathNode { EntityName = "ModuleHistoryDataObject", PathName = "ModuleHistoryItems" });
					    _pathNodes.Add ( "PersonToPerVehicleMasterAccessViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonToPerVehicleMasterAccessViewDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["PersonToPerVehicleMasterAccessViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "AccessGroupDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AccessGroupDataObject"].Add( "accessgroupstosites", new PathNode { EntityName = "AccessGroupToSiteDataObject", PathName = "AccessGroupsToSites" });
					    _pathNodes["AccessGroupDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["AccessGroupDataObject"].Add( "personitems", new PathNode { EntityName = "PersonDataObject", PathName = "PersonItems" });
					    _pathNodes.Add ( "ChecklistStatusViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ChecklistStatusViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["ChecklistStatusViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["ChecklistStatusViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["ChecklistStatusViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "CurrentStatusCombinedViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CurrentStatusCombinedViewDataObject"].Add( "currentstatusdriverviewitems", new PathNode { EntityName = "CurrentStatusDriverViewDataObject", PathName = "CurrentStatusDriverViewItems" });
					    _pathNodes["CurrentStatusCombinedViewDataObject"].Add( "currentstatusvehicleviewitems", new PathNode { EntityName = "CurrentStatusVehicleViewDataObject", PathName = "CurrentStatusVehicleViewItems" });
					    _pathNodes.Add ( "CurrentVehicleStatusChartViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CurrentVehicleStatusChartViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["CurrentVehicleStatusChartViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["CurrentVehicleStatusChartViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["CurrentVehicleStatusChartViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "DealerFeatureSubscriptionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DealerFeatureSubscriptionDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "MessageHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["MessageHistoryDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes["MessageHistoryDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["MessageHistoryDataObject"].Add( "allmessagehistorystoreprocedureitems", new PathNode { EntityName = "AllMessageHistoryStoreProcedureDataObject", PathName = "AllMessageHistoryStoreProcedureItems" });
					    _pathNodes.Add ( "AccessGroupToSiteDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AccessGroupToSiteDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["AccessGroupToSiteDataObject"].Add( "accessgroup", new PathNode { EntityName = "AccessGroupDataObject", PathName = "AccessGroup" });
					    _pathNodes.Add ( "AllDriverAccessAbuseStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllDriverAccessAbuseStoreProcedureDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["AllDriverAccessAbuseStoreProcedureDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes.Add ( "CustomerToModelDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CustomerToModelDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["CustomerToModelDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes.Add ( "AllEmailSubscriptionStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllEmailSubscriptionStoreProcedureDataObject"].Add( "reportsubscription", new PathNode { EntityName = "ReportSubscriptionDataObject", PathName = "ReportSubscription" });
					    _pathNodes.Add ( "DashboardVehicleCardStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DashboardVehicleCardStoreProcedureDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["DashboardVehicleCardStoreProcedureDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["DashboardVehicleCardStoreProcedureDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["DashboardVehicleCardStoreProcedureDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "RegionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["RegionDataObject"].Add( "dealers", new PathNode { EntityName = "DealerDataObject", PathName = "Dealers" });
					    _pathNodes.Add ( "ModelDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ModelDataObject"].Add( "vehicles", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicles" });
					    _pathNodes["ModelDataObject"].Add( "sitechecklists", new PathNode { EntityName = "DepartmentChecklistDataObject", PathName = "SiteChecklists" });
					    _pathNodes["ModelDataObject"].Add( "customermodelitems", new PathNode { EntityName = "CustomerModelDataObject", PathName = "CustomerModelItems" });
					    _pathNodes["ModelDataObject"].Add( "vehiclespermodelreportitems", new PathNode { EntityName = "VehiclesPerModelReportDataObject", PathName = "VehiclesPerModelReportItems" });
					    _pathNodes["ModelDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["ModelDataObject"].Add( "persontomodelvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToModelVehicleNormalAccessViewDataObject", PathName = "PersonToModelVehicleNormalAccessViewItems" });
					    _pathNodes["ModelDataObject"].Add( "customertomodelitems", new PathNode { EntityName = "CustomerToModelDataObject", PathName = "CustomerToModelItems" });
					    _pathNodes["ModelDataObject"].Add( "modelvehiclenormalcardaccessitems", new PathNode { EntityName = "ModelVehicleNormalCardAccessDataObject", PathName = "ModelVehicleNormalCardAccessItems" });
					    _pathNodes["ModelDataObject"].Add( "modelvehiclemastercardaccessitems", new PathNode { EntityName = "ModelVehicleMasterCardAccessDataObject", PathName = "ModelVehicleMasterCardAccessItems" });
					    _pathNodes["ModelDataObject"].Add( "licensesbymodel", new PathNode { EntityName = "LicenseByModelDataObject", PathName = "LicensesByModel" });
					    _pathNodes["ModelDataObject"].Add( "persontomodelvehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToModelVehicleMasterAccessViewDataObject", PathName = "PersonToModelVehicleMasterAccessViewItems" });
					    _pathNodes.Add ( "AllVORSessionsPerVehicleStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllVORSessionsPerVehicleStoreProcedureDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["AllVORSessionsPerVehicleStoreProcedureDataObject"].Add( "vorreportcombinedview", new PathNode { EntityName = "VORReportCombinedViewDataObject", PathName = "VORReportCombinedView" });
					    _pathNodes.Add ( "SlamcoreDeviceConnectionViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SlamcoreDeviceConnectionViewDataObject"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDeviceDataObject", PathName = "SlamcoreDevice" });
					    _pathNodes.Add ( "AllImpactsViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllImpactsViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["AllImpactsViewDataObject"].Add( "impact", new PathNode { EntityName = "ImpactDataObject", PathName = "Impact" });
					    _pathNodes.Add ( "CustomerDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CustomerDataObject"].Add( "driveritems", new PathNode { EntityName = "DriverDataObject", PathName = "DriverItems" });
					    _pathNodes["CustomerDataObject"].Add( "vehicleutilizationlasttwelvehoursstoreprocedureitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursStoreProcedureDataObject", PathName = "VehicleUtilizationLastTwelveHoursStoreProcedureItems" });
					    _pathNodes["CustomerDataObject"].Add( "customeraudit", new PathNode { EntityName = "CustomerAuditDataObject", PathName = "CustomerAudit" });
					    _pathNodes["CustomerDataObject"].Add( "vehicleutilizationlasttwelvehoursviewitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursViewDataObject", PathName = "VehicleUtilizationLastTwelveHoursViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "dashboardcardviewitems", new PathNode { EntityName = "DashboardDriverCardViewDataObject", PathName = "DashboardCardViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "checkliststatusviewitems", new PathNode { EntityName = "ChecklistStatusViewDataObject", PathName = "ChecklistStatusViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "country", new PathNode { EntityName = "CountryDataObject", PathName = "Country" });
					    _pathNodes["CustomerDataObject"].Add( "contactpersoninformation", new PathNode { EntityName = "ContactPersonInformationDataObject", PathName = "ContactPersonInformation" });
					    _pathNodes["CustomerDataObject"].Add( "emailgroupsitems", new PathNode { EntityName = "EmailGroupsDataObject", PathName = "EmailGroupsItems" });
					    _pathNodes["CustomerDataObject"].Add( "dashboarddrivercardstoreprocedureitems", new PathNode { EntityName = "DashboardDriverCardStoreProcedureDataObject", PathName = "DashboardDriverCardStoreProcedureItems" });
					    _pathNodes["CustomerDataObject"].Add( "sites", new PathNode { EntityName = "SiteDataObject", PathName = "Sites" });
					    _pathNodes["CustomerDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["CustomerDataObject"].Add( "todayspreopcheckstoreprocedureitems", new PathNode { EntityName = "TodaysPreopCheckStoreProcedureDataObject", PathName = "TodaysPreopCheckStoreProcedureItems" });
					    _pathNodes["CustomerDataObject"].Add( "dashboardfilteritems", new PathNode { EntityName = "DashboardFilterDataObject", PathName = "DashboardFilterItems" });
					    _pathNodes["CustomerDataObject"].Add( "customerpreoperationalchecklisttemplateitems", new PathNode { EntityName = "CustomerPreOperationalChecklistTemplateDataObject", PathName = "CustomerPreOperationalChecklistTemplateItems" });
					    _pathNodes["CustomerDataObject"].Add( "customertomodelitems", new PathNode { EntityName = "CustomerToModelDataObject", PathName = "CustomerToModelItems" });
					    _pathNodes["CustomerDataObject"].Add( "impactfrequencyperweekmonthviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekMonthViewDataObject", PathName = "ImpactFrequencyPerWeekMonthViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "customerfeaturesubscription", new PathNode { EntityName = "CustomerFeatureSubscriptionDataObject", PathName = "CustomerFeatureSubscription" });
					    _pathNodes["CustomerDataObject"].Add( "customerssodetailitems", new PathNode { EntityName = "CustomerSSODetailDataObject", PathName = "CustomerSSODetailItems" });
					    _pathNodes["CustomerDataObject"].Add( "slamcoredeviceitems", new PathNode { EntityName = "SlamcoreDeviceDataObject", PathName = "SlamcoreDeviceItems" });
					    _pathNodes["CustomerDataObject"].Add( "todayspreopcheckviewitems", new PathNode { EntityName = "TodaysPreopCheckViewDataObject", PathName = "TodaysPreopCheckViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "customertopersonviewitems", new PathNode { EntityName = "CustomerToPersonViewDataObject", PathName = "CustomerToPersonViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "todaysimpactstoreprocedureitems", new PathNode { EntityName = "TodaysImpactStoreProcedureDataObject", PathName = "TodaysImpactStoreProcedureItems" });
					    _pathNodes["CustomerDataObject"].Add( "driverlicenseexpiryviewitems", new PathNode { EntityName = "DriverLicenseExpiryViewDataObject", PathName = "DriverLicenseExpiryViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "accessgroupitems", new PathNode { EntityName = "AccessGroupDataObject", PathName = "AccessGroupItems" });
					    _pathNodes["CustomerDataObject"].Add( "customermodelitems", new PathNode { EntityName = "CustomerModelDataObject", PathName = "CustomerModelItems" });
					    _pathNodes["CustomerDataObject"].Add( "impactfrequencypertimeslotviewitems", new PathNode { EntityName = "ImpactFrequencyPerTimeSlotViewDataObject", PathName = "ImpactFrequencyPerTimeSlotViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "impactfrequencyperweekdayviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekDayViewDataObject", PathName = "ImpactFrequencyPerWeekDayViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "customersnapshotitems", new PathNode { EntityName = "CustomerSnapshotDataObject", PathName = "CustomerSnapshotItems" });
					    _pathNodes["CustomerDataObject"].Add( "loggedhoursversusseathoursviewitems", new PathNode { EntityName = "LoggedHoursVersusSeatHoursViewDataObject", PathName = "LoggedHoursVersusSeatHoursViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "departmentitems", new PathNode { EntityName = "DepartmentDataObject", PathName = "DepartmentItems" });
					    _pathNodes["CustomerDataObject"].Add( "driverlicenseexpirystoreprocedureitems", new PathNode { EntityName = "DriverLicenseExpiryStoreProcedureDataObject", PathName = "DriverLicenseExpiryStoreProcedureItems" });
					    _pathNodes["CustomerDataObject"].Add( "gousertocustomeritems", new PathNode { EntityName = "GoUserToCustomerDataObject", PathName = "GoUserToCustomerItems" });
					    _pathNodes["CustomerDataObject"].Add( "incompletedchecklistviewitems", new PathNode { EntityName = "IncompletedChecklistViewDataObject", PathName = "IncompletedChecklistViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "currentvehiclestatuschartviewitems", new PathNode { EntityName = "CurrentVehicleStatusChartViewDataObject", PathName = "CurrentVehicleStatusChartViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "vehicleitems", new PathNode { EntityName = "VehicleDataObject", PathName = "VehicleItems" });
					    _pathNodes["CustomerDataObject"].Add( "currentdriverstatuschartviewitems", new PathNode { EntityName = "CurrentDriverStatusChartViewDataObject", PathName = "CurrentDriverStatusChartViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "dashboardvehiclecardstoreprocedureitems", new PathNode { EntityName = "DashboardVehicleCardStoreProcedureDataObject", PathName = "DashboardVehicleCardStoreProcedureItems" });
					    _pathNodes["CustomerDataObject"].Add( "dashboardvehiclecardviewitems", new PathNode { EntityName = "DashboardVehicleCardViewDataObject", PathName = "DashboardVehicleCardViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "todaysimpactviewitems", new PathNode { EntityName = "TodaysImpactViewDataObject", PathName = "TodaysImpactViewItems" });
					    _pathNodes["CustomerDataObject"].Add( "personitems", new PathNode { EntityName = "PersonDataObject", PathName = "PersonItems" });
					    _pathNodes.Add ( "DepartmentChecklistDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DepartmentChecklistDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes["DepartmentChecklistDataObject"].Add( "preoperationalchecklists", new PathNode { EntityName = "PreOperationalChecklistDataObject", PathName = "PreOperationalChecklists" });
					    _pathNodes["DepartmentChecklistDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "ModuleHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ModuleHistoryDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["ModuleHistoryDataObject"].Add( "module", new PathNode { EntityName = "ModuleDataObject", PathName = "Module" });
					    _pathNodes.Add ( "CountryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CountryDataObject"].Add( "customers", new PathNode { EntityName = "CustomerDataObject", PathName = "Customers" });
					    _pathNodes.Add ( "AllChecklistResultViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllChecklistResultViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["AllChecklistResultViewDataObject"].Add( "checklistresult", new PathNode { EntityName = "ChecklistResultDataObject", PathName = "ChecklistResult" });
					    _pathNodes.Add ( "GPSHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GPSHistoryDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes.Add ( "ImpactsForVehicleViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ImpactsForVehicleViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["ImpactsForVehicleViewDataObject"].Add( "impact", new PathNode { EntityName = "ImpactDataObject", PathName = "Impact" });
					    _pathNodes["ImpactsForVehicleViewDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes.Add ( "AllVehicleUnlocksViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllVehicleUnlocksViewDataObject"].Add( "vehiclelockout", new PathNode { EntityName = "VehicleLockoutDataObject", PathName = "VehicleLockout" });
					    _pathNodes["AllVehicleUnlocksViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "SlamcorePedestrianDetectionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SlamcorePedestrianDetectionDataObject"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDeviceDataObject", PathName = "SlamcoreDevice" });
					    _pathNodes.Add ( "ImpactFrequencyPerWeekMonthViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ImpactFrequencyPerWeekMonthViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["ImpactFrequencyPerWeekMonthViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["ImpactFrequencyPerWeekMonthViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["ImpactFrequencyPerWeekMonthViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes.Add ( "VehicleUtilizationLastTwelveHoursViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleUtilizationLastTwelveHoursViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["VehicleUtilizationLastTwelveHoursViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["VehicleUtilizationLastTwelveHoursViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["VehicleUtilizationLastTwelveHoursViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes.Add ( "IOFIELDDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["IOFIELDDataObject"].Add( "pstatdetailsitems", new PathNode { EntityName = "PSTATDetailsDataObject", PathName = "PSTATDetailsItems" });
					    _pathNodes["IOFIELDDataObject"].Add( "sessiondetailsitems", new PathNode { EntityName = "SessionDetailsDataObject", PathName = "SessionDetailsItems" });
					    _pathNodes.Add ( "LicenceDetailDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["LicenceDetailDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes.Add ( "AllVehicleCalibrationStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllVehicleCalibrationStoreProcedureDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "CardToCardAccessDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CardToCardAccessDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes["CardToCardAccessDataObject"].Add( "cardaccesslevel", new PathNode { EntityName = "PermissionDataObject", PathName = "CardAccessLevel" });
					    _pathNodes.Add ( "ZoneCoordinatesDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ZoneCoordinatesDataObject"].Add( "floorzones", new PathNode { EntityName = "FloorZonesDataObject", PathName = "FloorZones" });
					    _pathNodes.Add ( "SnapshotDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SnapshotDataObject"].Add( "revision", new PathNode { EntityName = "RevisionDataObject", PathName = "Revision" });
					    _pathNodes.Add ( "DepartmentVehicleNormalCardAccessDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DepartmentVehicleNormalCardAccessDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes["DepartmentVehicleNormalCardAccessDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["DepartmentVehicleNormalCardAccessDataObject"].Add( "cardaccesslevel", new PathNode { EntityName = "PermissionDataObject", PathName = "CardAccessLevel" });
					    _pathNodes.Add ( "CustomerToPersonViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CustomerToPersonViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["CustomerToPersonViewDataObject"].Add( "emailgroupstopersonitems", new PathNode { EntityName = "EmailGroupsToPersonDataObject", PathName = "EmailGroupsToPersonItems" });
					    _pathNodes["CustomerToPersonViewDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes.Add ( "VehicleSlamcoreLocationHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleSlamcoreLocationHistoryDataObject"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDeviceDataObject", PathName = "SlamcoreDevice" });
					    _pathNodes.Add ( "PermissionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PermissionDataObject"].Add( "cardtocardaccessitems", new PathNode { EntityName = "CardToCardAccessDataObject", PathName = "CardToCardAccessItems" });
					    _pathNodes.Add ( "SessionDetailsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SessionDetailsDataObject"].Add( "iofield", new PathNode { EntityName = "IOFIELDDataObject", PathName = "IOFIELD" });
					    _pathNodes["SessionDetailsDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes.Add ( "GORoleDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GORoleDataObject"].Add( "gouseritems", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUserItems" });
					    _pathNodes["GORoleDataObject"].Add( "grouproleitems", new PathNode { EntityName = "GOGroupRoleDataObject", PathName = "GroupRoleItems" });
					    _pathNodes["GORoleDataObject"].Add( "userroleitems", new PathNode { EntityName = "GOUserRoleDataObject", PathName = "UserRoleItems" });
					    _pathNodes.Add ( "GeneralProductivityPerVehicleViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GeneralProductivityPerVehicleViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["GeneralProductivityPerVehicleViewDataObject"].Add( "generalproductivityview", new PathNode { EntityName = "GeneralProductivityViewDataObject", PathName = "GeneralProductivityView" });
					    _pathNodes["GeneralProductivityPerVehicleViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "VehicleDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleDataObject"].Add( "module", new PathNode { EntityName = "ModuleDataObject", PathName = "Module" });
					    _pathNodes["VehicleDataObject"].Add( "vehicletopreopcheckilstitems", new PathNode { EntityName = "VehicleToPreOpChecklistViewDataObject", PathName = "VehicleToPreOpCheckilstItems" });
					    _pathNodes["VehicleDataObject"].Add( "currentstatusvehicleviewitems", new PathNode { EntityName = "CurrentStatusVehicleViewDataObject", PathName = "CurrentStatusVehicleViewItems" });
					    _pathNodes["VehicleDataObject"].Add( "pedestriandetectionhistoryitems", new PathNode { EntityName = "PedestrianDetectionHistoryDataObject", PathName = "PedestrianDetectionHistoryItems" });
					    _pathNodes["VehicleDataObject"].Add( "checklistsettings", new PathNode { EntityName = "ChecklistSettingsDataObject", PathName = "ChecklistSettings" });
					    _pathNodes["VehicleDataObject"].Add( "detailedvorsessionstoreprocedureitems", new PathNode { EntityName = "DetailedVORSessionStoreProcedureDataObject", PathName = "DetailedVORSessionStoreProcedureItems" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclehiredehirehistoryitems", new PathNode { EntityName = "VehicleHireDehireHistoryDataObject", PathName = "VehicleHireDehireHistoryItems" });
					    _pathNodes["VehicleDataObject"].Add( "persontopervehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToPerVehicleMasterAccessViewDataObject", PathName = "PersonToPerVehicleMasterAccessViewItems" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclesupervisorsviewitems", new PathNode { EntityName = "VehicleSupervisorsViewDataObject", PathName = "VehicleSupervisorsViewItems" });
					    _pathNodes["VehicleDataObject"].Add( "generalproductivitypervehicleviewitems", new PathNode { EntityName = "GeneralProductivityPerVehicleViewDataObject", PathName = "GeneralProductivityPerVehicleViewItems" });
					    _pathNodes["VehicleDataObject"].Add( "detailedsessionviewitems", new PathNode { EntityName = "DetailedSessionViewDataObject", PathName = "DetailedSessionViewItems" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclecardaccesses", new PathNode { EntityName = "PerVehicleMasterCardAccessDataObject", PathName = "VehicleCardAccesses" });
					    _pathNodes["VehicleDataObject"].Add( "inspection", new PathNode { EntityName = "InspectionDataObject", PathName = "Inspection" });
					    _pathNodes["VehicleDataObject"].Add( "canrule", new PathNode { EntityName = "CanruleDataObject", PathName = "Canrule" });
					    _pathNodes["VehicleDataObject"].Add( "persontopervehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToPerVehicleNormalAccessViewDataObject", PathName = "PersonToPerVehicleNormalAccessViewItems" });
					    _pathNodes["VehicleDataObject"].Add( "firmware", new PathNode { EntityName = "FirmwareDataObject", PathName = "Firmware" });
					    _pathNodes["VehicleDataObject"].Add( "messagehistoryitems", new PathNode { EntityName = "MessageHistoryDataObject", PathName = "MessageHistoryItems" });
					    _pathNodes["VehicleDataObject"].Add( "vorsettinghistoryitems", new PathNode { EntityName = "VORSettingHistoryDataObject", PathName = "VORSettingHistoryItems" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclegpslocations", new PathNode { EntityName = "VehicleGPSDataObject", PathName = "VehicleGPSLocations" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclelockoutitems", new PathNode { EntityName = "VehicleLockoutDataObject", PathName = "VehicleLockoutItems" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclebroadcastmessageitems", new PathNode { EntityName = "VehicleBroadcastMessageDataObject", PathName = "VehicleBroadcastMessageItems" });
					    _pathNodes["VehicleDataObject"].Add( "departmentchecklist", new PathNode { EntityName = "DepartmentChecklistDataObject", PathName = "DepartmentChecklist" });
					    _pathNodes["VehicleDataObject"].Add( "sessions", new PathNode { EntityName = "SessionDataObject", PathName = "Sessions" });
					    _pathNodes["VehicleDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["VehicleDataObject"].Add( "unitunutilisationstoreprocedureitems", new PathNode { EntityName = "UnitUtilisationStoreProcedureDataObject", PathName = "UnitUnutilisationStoreProcedureItems" });
					    _pathNodes["VehicleDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["VehicleDataObject"].Add( "ondemandsettings", new PathNode { EntityName = "OnDemandSettingsDataObject", PathName = "OnDemandSettings" });
					    _pathNodes["VehicleDataObject"].Add( "slamcoredevicehistoryitems", new PathNode { EntityName = "SlamcoreDeviceHistoryDataObject", PathName = "SlamcoreDeviceHistoryItems" });
					    _pathNodes["VehicleDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes["VehicleDataObject"].Add( "allvorsessionspervehiclestoreprocedureitems", new PathNode { EntityName = "AllVORSessionsPerVehicleStoreProcedureDataObject", PathName = "AllVORSessionsPerVehicleStoreProcedureItems" });
					    _pathNodes["VehicleDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclelastgpslocationview", new PathNode { EntityName = "VehicleLastGPSLocationViewDataObject", PathName = "VehicleLastGPSLocationView" });
					    _pathNodes["VehicleDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["VehicleDataObject"].Add( "networksettingsitems", new PathNode { EntityName = "NetworkSettingsDataObject", PathName = "NetworkSettingsItems" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclealertsubscriptionitems", new PathNode { EntityName = "VehicleAlertSubscriptionDataObject", PathName = "VehicleAlertSubscriptionItems" });
					    _pathNodes["VehicleDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["VehicleDataObject"].Add( "impactsforvehicleviewitems", new PathNode { EntityName = "ImpactsForVehicleViewDataObject", PathName = "ImpactsForVehicleViewItems" });
					    _pathNodes["VehicleDataObject"].Add( "checklistfailurepervechicleviewitems", new PathNode { EntityName = "ChecklistFailurePerVechicleViewDataObject", PathName = "ChecklistFailurePerVechicleViewItems" });
					    _pathNodes["VehicleDataObject"].Add( "vehicleproficiencyviewitems", new PathNode { EntityName = "VehicleProficiencyViewDataObject", PathName = "VehicleProficiencyViewItems" });
					    _pathNodes["VehicleDataObject"].Add( "vehicleothersettings", new PathNode { EntityName = "VehicleOtherSettingsDataObject", PathName = "VehicleOtherSettings" });
					    _pathNodes["VehicleDataObject"].Add( "allvehiclecalibrationstoreprocedureitems", new PathNode { EntityName = "AllVehicleCalibrationStoreProcedureDataObject", PathName = "AllVehicleCalibrationStoreProcedureItems" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclediagnostic", new PathNode { EntityName = "VehicleDiagnosticDataObject", PathName = "VehicleDiagnostic" });
					    _pathNodes["VehicleDataObject"].Add( "vehiclesessionlessimpactitems", new PathNode { EntityName = "VehicleSessionlessImpactDataObject", PathName = "VehicleSessionlessImpactItems" });
					    _pathNodes["VehicleDataObject"].Add( "broadcastmessagehistoryitems", new PathNode { EntityName = "BroadcastMessageHistoryDataObject", PathName = "BroadcastMessageHistoryItems" });
					    _pathNodes["VehicleDataObject"].Add( "ondemandsessionitems", new PathNode { EntityName = "OnDemandSessionDataObject", PathName = "OnDemandSessionItems" });
					    _pathNodes["VehicleDataObject"].Add( "unitutilisationstoreprocedureitems", new PathNode { EntityName = "UnitUnutilisationStoreProcedureDataObject", PathName = "UnitUtilisationStoreProcedureItems" });
					    _pathNodes["VehicleDataObject"].Add( "servicesettings", new PathNode { EntityName = "ServiceSettingsDataObject", PathName = "ServiceSettings" });
					    _pathNodes["VehicleDataObject"].Add( "modulehistoryitems", new PathNode { EntityName = "ModuleHistoryDataObject", PathName = "ModuleHistoryItems" });
					    _pathNodes["VehicleDataObject"].Add( "pervehiclenormalcardaccessitems", new PathNode { EntityName = "PerVehicleNormalCardAccessDataObject", PathName = "PerVehicleNormalCardAccessItems" });
					    _pathNodes.Add ( "GeneralProductivityPerDriverViewLatestDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GeneralProductivityPerDriverViewLatestDataObject"].Add( "generalproductivityview", new PathNode { EntityName = "GeneralProductivityViewDataObject", PathName = "GeneralProductivityView" });
					    _pathNodes["GeneralProductivityPerDriverViewLatestDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["GeneralProductivityPerDriverViewLatestDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "DriverDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DriverDataObject"].Add( "vehiclelockouts", new PathNode { EntityName = "VehicleLockoutDataObject", PathName = "VehicleLockouts" });
					    _pathNodes["DriverDataObject"].Add( "broadcastmessagehistoryitems", new PathNode { EntityName = "BroadcastMessageHistoryDataObject", PathName = "BroadcastMessageHistoryItems" });
					    _pathNodes["DriverDataObject"].Add( "impactsforvehicleviewitems", new PathNode { EntityName = "ImpactsForVehicleViewDataObject", PathName = "ImpactsForVehicleViewItems" });
					    _pathNodes["DriverDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["DriverDataObject"].Add( "licensesbymodel", new PathNode { EntityName = "LicenseByModelDataObject", PathName = "LicensesByModel" });
					    _pathNodes["DriverDataObject"].Add( "alllicenseexpiryviewitems", new PathNode { EntityName = "AllLicenseExpiryViewDataObject", PathName = "AllLicenseExpiryViewItems" });
					    _pathNodes["DriverDataObject"].Add( "detailedsessionviewitems", new PathNode { EntityName = "DetailedSessionViewDataObject", PathName = "DetailedSessionViewItems" });
					    _pathNodes["DriverDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["DriverDataObject"].Add( "alldriveraccessabusestoreprocedureitems", new PathNode { EntityName = "AllDriverAccessAbuseStoreProcedureDataObject", PathName = "AllDriverAccessAbuseStoreProcedureItems" });
					    _pathNodes["DriverDataObject"].Add( "ondemandsessionitems", new PathNode { EntityName = "OnDemandSessionDataObject", PathName = "OnDemandSessionItems" });
					    _pathNodes["DriverDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes["DriverDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["DriverDataObject"].Add( "pedestriandetectionhistoryitems", new PathNode { EntityName = "PedestrianDetectionHistoryDataObject", PathName = "PedestrianDetectionHistoryItems" });
					    _pathNodes["DriverDataObject"].Add( "generallicence", new PathNode { EntityName = "LicenceDetailDataObject", PathName = "GeneralLicence" });
					    _pathNodes["DriverDataObject"].Add( "currentstatusdriverviewitems", new PathNode { EntityName = "CurrentStatusDriverViewDataObject", PathName = "CurrentStatusDriverViewItems" });
					    _pathNodes["DriverDataObject"].Add( "generalproductivityperdriverviewlatestitems", new PathNode { EntityName = "GeneralProductivityPerDriverViewLatestDataObject", PathName = "GeneralProductivityPerDriverViewLatestItems" });
					    _pathNodes["DriverDataObject"].Add( "driverproficiencyviewitems", new PathNode { EntityName = "DriverProficiencyViewDataObject", PathName = "DriverProficiencyViewItems" });
					    _pathNodes["DriverDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["DriverDataObject"].Add( "sessions", new PathNode { EntityName = "SessionDataObject", PathName = "Sessions" });
					    _pathNodes.Add ( "EmailGroupsToPersonDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["EmailGroupsToPersonDataObject"].Add( "emailgroups", new PathNode { EntityName = "EmailGroupsDataObject", PathName = "EmailGroups" });
					    _pathNodes["EmailGroupsToPersonDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["EmailGroupsToPersonDataObject"].Add( "customertopersonview", new PathNode { EntityName = "CustomerToPersonViewDataObject", PathName = "CustomerToPersonView" });
					    _pathNodes.Add ( "GOGroupRoleDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GOGroupRoleDataObject"].Add( "group", new PathNode { EntityName = "GOGroupDataObject", PathName = "Group" });
					    _pathNodes["GOGroupRoleDataObject"].Add( "role", new PathNode { EntityName = "GORoleDataObject", PathName = "Role" });
					    _pathNodes.Add ( "ModelVehicleMasterCardAccessDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ModelVehicleMasterCardAccessDataObject"].Add( "pervehiclemastercardaccessitems", new PathNode { EntityName = "PerVehicleMasterCardAccessDataObject", PathName = "PerVehicleMasterCardAccessItems" });
					    _pathNodes["ModelVehicleMasterCardAccessDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes["ModelVehicleMasterCardAccessDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes.Add ( "VehicleProficiencyViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleProficiencyViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["VehicleProficiencyViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["VehicleProficiencyViewDataObject"].Add( "proficiencycombinedview", new PathNode { EntityName = "ProficiencyCombinedViewDataObject", PathName = "ProficiencyCombinedView" });
					    _pathNodes.Add ( "DashboardDriverCardViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DashboardDriverCardViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["DashboardDriverCardViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["DashboardDriverCardViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["DashboardDriverCardViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "DriverProficiencyViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DriverProficiencyViewDataObject"].Add( "proficiencycombinedview", new PathNode { EntityName = "ProficiencyCombinedViewDataObject", PathName = "ProficiencyCombinedView" });
					    _pathNodes["DriverProficiencyViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["DriverProficiencyViewDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes.Add ( "AlertHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AlertHistoryDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["AlertHistoryDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["AlertHistoryDataObject"].Add( "alert", new PathNode { EntityName = "AlertDataObject", PathName = "Alert" });
					    _pathNodes.Add ( "DashboardFilterDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DashboardFilterDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["DashboardFilterDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["DashboardFilterDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "WebsiteRoleDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["WebsiteRoleDataObject"].Add( "websiteuser", new PathNode { EntityName = "WebsiteUserDataObject", PathName = "WebsiteUser" });
					    _pathNodes.Add ( "GOUserRoleDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GOUserRoleDataObject"].Add( "role", new PathNode { EntityName = "GORoleDataObject", PathName = "Role" });
					    _pathNodes["GOUserRoleDataObject"].Add( "user", new PathNode { EntityName = "GOUserDataObject", PathName = "User" });
					    _pathNodes.Add ( "GeneralProductivityViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GeneralProductivityViewDataObject"].Add( "generalproductivitypervehicleviewitems", new PathNode { EntityName = "GeneralProductivityPerVehicleViewDataObject", PathName = "GeneralProductivityPerVehicleViewItems" });
					    _pathNodes["GeneralProductivityViewDataObject"].Add( "unitunutilisationstoreprocedureitems", new PathNode { EntityName = "UnitUnutilisationStoreProcedureDataObject", PathName = "UnitUnutilisationStoreProcedureItems" });
					    _pathNodes["GeneralProductivityViewDataObject"].Add( "unitutilisationstoreprocedureitems", new PathNode { EntityName = "UnitUtilisationStoreProcedureDataObject", PathName = "UnitUtilisationStoreProcedureItems" });
					    _pathNodes["GeneralProductivityViewDataObject"].Add( "generalproductivityperdriverviewlatestitems", new PathNode { EntityName = "GeneralProductivityPerDriverViewLatestDataObject", PathName = "GeneralProductivityPerDriverViewLatestItems" });
					    _pathNodes.Add ( "InspectionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["InspectionDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "ImpactDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ImpactDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes["ImpactDataObject"].Add( "impactsforvehicleview", new PathNode { EntityName = "ImpactsForVehicleViewDataObject", PathName = "ImpactsForVehicleView" });
					    _pathNodes["ImpactDataObject"].Add( "allimpactsviewitems", new PathNode { EntityName = "AllImpactsViewDataObject", PathName = "AllImpactsViewItems" });
					    _pathNodes.Add ( "PersonToDepartmentVehicleMasterAccessViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonToDepartmentVehicleMasterAccessViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["PersonToDepartmentVehicleMasterAccessViewDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes.Add ( "CurrentStatusVehicleViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CurrentStatusVehicleViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["CurrentStatusVehicleViewDataObject"].Add( "currentstatuscombinedview", new PathNode { EntityName = "CurrentStatusCombinedViewDataObject", PathName = "CurrentStatusCombinedView" });
					    _pathNodes["CurrentStatusVehicleViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "CustomerModelDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CustomerModelDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes["CustomerModelDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "TagDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["TagDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes["TagDataObject"].Add( "revision", new PathNode { EntityName = "RevisionDataObject", PathName = "Revision" });
					    _pathNodes.Add ( "VehicleBroadcastMessageDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleBroadcastMessageDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["VehicleBroadcastMessageDataObject"].Add( "broadcastmessage", new PathNode { EntityName = "BroadcastMessageDataObject", PathName = "BroadcastMessage" });
					    _pathNodes.Add ( "FloorPlanDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["FloorPlanDataObject"].Add( "floorzonesitems", new PathNode { EntityName = "FloorZonesDataObject", PathName = "FloorZonesItems" });
					    _pathNodes["FloorPlanDataObject"].Add( "sitefloorplanitems", new PathNode { EntityName = "SiteFloorPlanDataObject", PathName = "SiteFloorPlanItems" });
					    _pathNodes["FloorPlanDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes.Add ( "TodaysPreopCheckViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["TodaysPreopCheckViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["TodaysPreopCheckViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["TodaysPreopCheckViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["TodaysPreopCheckViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "ModelVehicleNormalCardAccessDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ModelVehicleNormalCardAccessDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes["ModelVehicleNormalCardAccessDataObject"].Add( "cardaccesslevel", new PathNode { EntityName = "PermissionDataObject", PathName = "CardAccessLevel" });
					    _pathNodes["ModelVehicleNormalCardAccessDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["ModelVehicleNormalCardAccessDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes.Add ( "SiteVehicleNormalCardAccessDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SiteVehicleNormalCardAccessDataObject"].Add( "cardaccesslevel", new PathNode { EntityName = "PermissionDataObject", PathName = "CardAccessLevel" });
					    _pathNodes["SiteVehicleNormalCardAccessDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["SiteVehicleNormalCardAccessDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes["SiteVehicleNormalCardAccessDataObject"].Add( "pervehiclemastercardaccessitems", new PathNode { EntityName = "PerVehicleMasterCardAccessDataObject", PathName = "PerVehicleMasterCardAccessItems" });
					    _pathNodes.Add ( "ImportJobStatusDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ImportJobStatusDataObject"].Add( "importjobbatch", new PathNode { EntityName = "ImportJobBatchDataObject", PathName = "ImportJobBatch" });
					    _pathNodes["ImportJobStatusDataObject"].Add( "importjoblogitems", new PathNode { EntityName = "ImportJobLogDataObject", PathName = "ImportJobLogItems" });
					    _pathNodes.Add ( "VehicleOtherSettingsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleOtherSettingsDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "VehicleSupervisorsViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleSupervisorsViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["VehicleSupervisorsViewDataObject"].Add( "pervehiclenormalcardaccess", new PathNode { EntityName = "PerVehicleNormalCardAccessDataObject", PathName = "PerVehicleNormalCardAccess" });
					    _pathNodes.Add ( "ExportJobStatusDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ExportJobStatusDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes.Add ( "TimezoneDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["TimezoneDataObject"].Add( "sites", new PathNode { EntityName = "SiteDataObject", PathName = "Sites" });
					    _pathNodes.Add ( "LoggedHoursVersusSeatHoursViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["LoggedHoursVersusSeatHoursViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["LoggedHoursVersusSeatHoursViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["LoggedHoursVersusSeatHoursViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["LoggedHoursVersusSeatHoursViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "PersonAllocationDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonAllocationDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["PersonAllocationDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["PersonAllocationDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes.Add ( "BroadcastMessageDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["BroadcastMessageDataObject"].Add( "vehiclebroadcastmessageitems", new PathNode { EntityName = "VehicleBroadcastMessageDataObject", PathName = "VehicleBroadcastMessageItems" });
					    _pathNodes.Add ( "VORReportCombinedViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VORReportCombinedViewDataObject"].Add( "allvorstatusstoreprocedureitems", new PathNode { EntityName = "AllVORStatusStoreProcedureDataObject", PathName = "AllVORStatusStoreProcedureItems" });
					    _pathNodes["VORReportCombinedViewDataObject"].Add( "allvorsessionspervehiclestoreprocedureitems", new PathNode { EntityName = "AllVORSessionsPerVehicleStoreProcedureDataObject", PathName = "AllVORSessionsPerVehicleStoreProcedureItems" });
					    _pathNodes.Add ( "UnitUtilisationStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["UnitUtilisationStoreProcedureDataObject"].Add( "generalproductivityview", new PathNode { EntityName = "GeneralProductivityViewDataObject", PathName = "GeneralProductivityView" });
					    _pathNodes["UnitUtilisationStoreProcedureDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "ChecklistResultDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ChecklistResultDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes["ChecklistResultDataObject"].Add( "checklistanswerdetails", new PathNode { EntityName = "ChecklistDetailDataObject", PathName = "ChecklistAnswerDetails" });
					    _pathNodes["ChecklistResultDataObject"].Add( "allchecklistresultviewitems", new PathNode { EntityName = "AllChecklistResultViewDataObject", PathName = "AllChecklistResultViewItems" });
					    _pathNodes.Add ( "UnitUnutilisationStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["UnitUnutilisationStoreProcedureDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["UnitUnutilisationStoreProcedureDataObject"].Add( "generalproductivityview", new PathNode { EntityName = "GeneralProductivityViewDataObject", PathName = "GeneralProductivityView" });
					    _pathNodes.Add ( "VehicleAlertSubscriptionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleAlertSubscriptionDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["VehicleAlertSubscriptionDataObject"].Add( "alertsubscription", new PathNode { EntityName = "AlertSubscriptionDataObject", PathName = "AlertSubscription" });
					    _pathNodes.Add ( "OnDemandAuthorisationStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["OnDemandAuthorisationStoreProcedureDataObject"].Add( "ondemandsession", new PathNode { EntityName = "OnDemandSessionDataObject", PathName = "OnDemandSession" });
					    _pathNodes.Add ( "ImpactFrequencyPerTimeSlotViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ImpactFrequencyPerTimeSlotViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["ImpactFrequencyPerTimeSlotViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["ImpactFrequencyPerTimeSlotViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["ImpactFrequencyPerTimeSlotViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "PersonToSiteVehicleNormalAccessViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonToSiteVehicleNormalAccessViewDataObject"].Add( "cardaccesslevel", new PathNode { EntityName = "PermissionDataObject", PathName = "CardAccessLevel" });
					    _pathNodes["PersonToSiteVehicleNormalAccessViewDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["PersonToSiteVehicleNormalAccessViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes.Add ( "SessionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SessionDataObject"].Add( "pstatdetailsitems", new PathNode { EntityName = "PSTATDetailsDataObject", PathName = "PSTATDetailsItems" });
					    _pathNodes["SessionDataObject"].Add( "vehiclelockouts", new PathNode { EntityName = "VehicleLockoutDataObject", PathName = "VehicleLockouts" });
					    _pathNodes["SessionDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["SessionDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["SessionDataObject"].Add( "sessiondetailsitems", new PathNode { EntityName = "SessionDetailsDataObject", PathName = "SessionDetailsItems" });
					    _pathNodes["SessionDataObject"].Add( "vehiclegpsitems", new PathNode { EntityName = "VehicleGPSDataObject", PathName = "VehicleGPSItems" });
					    _pathNodes["SessionDataObject"].Add( "gpshistoryitems", new PathNode { EntityName = "GPSHistoryDataObject", PathName = "GPSHistoryItems" });
					    _pathNodes["SessionDataObject"].Add( "detailedvorsessionstoreprocedureitems", new PathNode { EntityName = "DetailedVORSessionStoreProcedureDataObject", PathName = "DetailedVORSessionStoreProcedureItems" });
					    _pathNodes["SessionDataObject"].Add( "impacts", new PathNode { EntityName = "ImpactDataObject", PathName = "Impacts" });
					    _pathNodes["SessionDataObject"].Add( "vehiclelastgpslocationview", new PathNode { EntityName = "VehicleLastGPSLocationViewDataObject", PathName = "VehicleLastGPSLocationView" });
					    _pathNodes["SessionDataObject"].Add( "detailedsessionviewitems", new PathNode { EntityName = "DetailedSessionViewDataObject", PathName = "DetailedSessionViewItems" });
					    _pathNodes["SessionDataObject"].Add( "checklistresults", new PathNode { EntityName = "ChecklistResultDataObject", PathName = "ChecklistResults" });
					    _pathNodes.Add ( "DealerDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DealerDataObject"].Add( "dealerfeaturesubscription", new PathNode { EntityName = "DealerFeatureSubscriptionDataObject", PathName = "DealerFeatureSubscription" });
					    _pathNodes["DealerDataObject"].Add( "currentvehiclestatuschartviewitems", new PathNode { EntityName = "CurrentVehicleStatusChartViewDataObject", PathName = "CurrentVehicleStatusChartViewItems" });
					    _pathNodes["DealerDataObject"].Add( "generalproductivityperdriverviewlatestitems", new PathNode { EntityName = "GeneralProductivityPerDriverViewLatestDataObject", PathName = "GeneralProductivityPerDriverViewLatestItems" });
					    _pathNodes["DealerDataObject"].Add( "todaysimpactstoreprocedureitems", new PathNode { EntityName = "TodaysImpactStoreProcedureDataObject", PathName = "TodaysImpactStoreProcedureItems" });
					    _pathNodes["DealerDataObject"].Add( "alllicenseexpiryviewitems", new PathNode { EntityName = "AllLicenseExpiryViewDataObject", PathName = "AllLicenseExpiryViewItems" });
					    _pathNodes["DealerDataObject"].Add( "allvehicleunlocksviewitems", new PathNode { EntityName = "AllVehicleUnlocksViewDataObject", PathName = "AllVehicleUnlocksViewItems" });
					    _pathNodes["DealerDataObject"].Add( "impactfrequencypertimeslotviewitems", new PathNode { EntityName = "ImpactFrequencyPerTimeSlotViewDataObject", PathName = "ImpactFrequencyPerTimeSlotViewItems" });
					    _pathNodes["DealerDataObject"].Add( "moduleitems", new PathNode { EntityName = "ModuleDataObject", PathName = "ModuleItems" });
					    _pathNodes["DealerDataObject"].Add( "gouseritems", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUserItems" });
					    _pathNodes["DealerDataObject"].Add( "vehicleutilizationlasttwelvehoursstoreprocedureitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursStoreProcedureDataObject", PathName = "VehicleUtilizationLastTwelveHoursStoreProcedureItems" });
					    _pathNodes["DealerDataObject"].Add( "todaysimpactviewitems", new PathNode { EntityName = "TodaysImpactViewDataObject", PathName = "TodaysImpactViewItems" });
					    _pathNodes["DealerDataObject"].Add( "driverproficiencyviewitems", new PathNode { EntityName = "DriverProficiencyViewDataObject", PathName = "DriverProficiencyViewItems" });
					    _pathNodes["DealerDataObject"].Add( "incompletedchecklistviewitems", new PathNode { EntityName = "IncompletedChecklistViewDataObject", PathName = "IncompletedChecklistViewItems" });
					    _pathNodes["DealerDataObject"].Add( "allimpactsviewitems", new PathNode { EntityName = "AllImpactsViewDataObject", PathName = "AllImpactsViewItems" });
					    _pathNodes["DealerDataObject"].Add( "modelitems", new PathNode { EntityName = "ModelDataObject", PathName = "ModelItems" });
					    _pathNodes["DealerDataObject"].Add( "alldriveraccessabusestoreprocedureitems", new PathNode { EntityName = "AllDriverAccessAbuseStoreProcedureDataObject", PathName = "AllDriverAccessAbuseStoreProcedureItems" });
					    _pathNodes["DealerDataObject"].Add( "dashboarddrivercardstoreprocedureitems", new PathNode { EntityName = "DashboardDriverCardStoreProcedureDataObject", PathName = "DashboardDriverCardStoreProcedureItems" });
					    _pathNodes["DealerDataObject"].Add( "dashboardvehiclecardstoreprocedureitems", new PathNode { EntityName = "DashboardVehicleCardStoreProcedureDataObject", PathName = "DashboardVehicleCardStoreProcedureItems" });
					    _pathNodes["DealerDataObject"].Add( "vehicleutilizationlasttwelvehoursviewitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursViewDataObject", PathName = "VehicleUtilizationLastTwelveHoursViewItems" });
					    _pathNodes["DealerDataObject"].Add( "dashboardcardviewitems", new PathNode { EntityName = "DashboardDriverCardViewDataObject", PathName = "DashboardCardViewItems" });
					    _pathNodes["DealerDataObject"].Add( "currentdriverstatuschartviewitems", new PathNode { EntityName = "CurrentDriverStatusChartViewDataObject", PathName = "CurrentDriverStatusChartViewItems" });
					    _pathNodes["DealerDataObject"].Add( "dealerconfiguration", new PathNode { EntityName = "DealerConfigurationDataObject", PathName = "DealerConfiguration" });
					    _pathNodes["DealerDataObject"].Add( "customers", new PathNode { EntityName = "CustomerDataObject", PathName = "Customers" });
					    _pathNodes["DealerDataObject"].Add( "vehicleproficiencyviewitems", new PathNode { EntityName = "VehicleProficiencyViewDataObject", PathName = "VehicleProficiencyViewItems" });
					    _pathNodes["DealerDataObject"].Add( "checkliststatusviewitems", new PathNode { EntityName = "ChecklistStatusViewDataObject", PathName = "ChecklistStatusViewItems" });
					    _pathNodes["DealerDataObject"].Add( "driverlicenseexpiryviewitems", new PathNode { EntityName = "DriverLicenseExpiryViewDataObject", PathName = "DriverLicenseExpiryViewItems" });
					    _pathNodes["DealerDataObject"].Add( "loggedhoursversusseathoursviewitems", new PathNode { EntityName = "LoggedHoursVersusSeatHoursViewDataObject", PathName = "LoggedHoursVersusSeatHoursViewItems" });
					    _pathNodes["DealerDataObject"].Add( "todayspreopcheckviewitems", new PathNode { EntityName = "TodaysPreopCheckViewDataObject", PathName = "TodaysPreopCheckViewItems" });
					    _pathNodes["DealerDataObject"].Add( "currentstatusdriverviewitems", new PathNode { EntityName = "CurrentStatusDriverViewDataObject", PathName = "CurrentStatusDriverViewItems" });
					    _pathNodes["DealerDataObject"].Add( "impactfrequencyperweekmonthviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekMonthViewDataObject", PathName = "ImpactFrequencyPerWeekMonthViewItems" });
					    _pathNodes["DealerDataObject"].Add( "allusersummarystoreprocedureitems", new PathNode { EntityName = "AllUserSummaryStoreProcedureDataObject", PathName = "AllUserSummaryStoreProcedureItems" });
					    _pathNodes["DealerDataObject"].Add( "allchecklistresultviewitems", new PathNode { EntityName = "AllChecklistResultViewDataObject", PathName = "AllChecklistResultViewItems" });
					    _pathNodes["DealerDataObject"].Add( "todayspreopcheckstoreprocedureitems", new PathNode { EntityName = "TodaysPreopCheckStoreProcedureDataObject", PathName = "TodaysPreopCheckStoreProcedureItems" });
					    _pathNodes["DealerDataObject"].Add( "currentstatusvehicleviewitems", new PathNode { EntityName = "CurrentStatusVehicleViewDataObject", PathName = "CurrentStatusVehicleViewItems" });
					    _pathNodes["DealerDataObject"].Add( "generalproductivitypervehicleviewitems", new PathNode { EntityName = "GeneralProductivityPerVehicleViewDataObject", PathName = "GeneralProductivityPerVehicleViewItems" });
					    _pathNodes["DealerDataObject"].Add( "featuresubscriptionsfilteritems", new PathNode { EntityName = "FeatureSubscriptionsFilterDataObject", PathName = "FeatureSubscriptionsFilterItems" });
					    _pathNodes["DealerDataObject"].Add( "dashboardvehiclecardviewitems", new PathNode { EntityName = "DashboardVehicleCardViewDataObject", PathName = "DashboardVehicleCardViewItems" });
					    _pathNodes["DealerDataObject"].Add( "driverlicenseexpirystoreprocedureitems", new PathNode { EntityName = "DriverLicenseExpiryStoreProcedureDataObject", PathName = "DriverLicenseExpiryStoreProcedureItems" });
					    _pathNodes["DealerDataObject"].Add( "region", new PathNode { EntityName = "RegionDataObject", PathName = "Region" });
					    _pathNodes["DealerDataObject"].Add( "impactfrequencyperweekdayviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekDayViewDataObject", PathName = "ImpactFrequencyPerWeekDayViewItems" });
					    _pathNodes.Add ( "GOUserDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GOUserDataObject"].Add( "usergroupitems", new PathNode { EntityName = "GOUserGroupDataObject", PathName = "UserGroupItems" });
					    _pathNodes["GOUserDataObject"].Add( "userroleitems", new PathNode { EntityName = "GOUserRoleDataObject", PathName = "UserRoleItems" });
					    _pathNodes["GOUserDataObject"].Add( "customeraudititemscreated", new PathNode { EntityName = "CustomerAuditDataObject", PathName = "CustomerAuditItemsCreated" });
					    _pathNodes["GOUserDataObject"].Add( "dealerdriver", new PathNode { EntityName = "DealerDriverDataObject", PathName = "DealerDriver" });
					    _pathNodes["GOUserDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["GOUserDataObject"].Add( "customeraudititemsdeleted", new PathNode { EntityName = "CustomerAuditDataObject", PathName = "CustomerAuditItemsDeleted" });
					    _pathNodes["GOUserDataObject"].Add( "vehiclelockoutitems", new PathNode { EntityName = "VehicleLockoutDataObject", PathName = "VehicleLockoutItems" });
					    _pathNodes["GOUserDataObject"].Add( "messagehistoryitems", new PathNode { EntityName = "MessageHistoryDataObject", PathName = "MessageHistoryItems" });
					    _pathNodes["GOUserDataObject"].Add( "revisionitems", new PathNode { EntityName = "RevisionDataObject", PathName = "RevisionItems" });
					    _pathNodes["GOUserDataObject"].Add( "exportjobstatusitems", new PathNode { EntityName = "ExportJobStatusDataObject", PathName = "ExportJobStatusItems" });
					    _pathNodes["GOUserDataObject"].Add( "reportsubscriptionitems", new PathNode { EntityName = "ReportSubscriptionDataObject", PathName = "ReportSubscriptionItems" });
					    _pathNodes["GOUserDataObject"].Add( "alertsubscriptionitems", new PathNode { EntityName = "AlertSubscriptionDataObject", PathName = "AlertSubscriptionItems" });
					    _pathNodes["GOUserDataObject"].Add( "customeraudititemsmodified", new PathNode { EntityName = "CustomerAuditDataObject", PathName = "CustomerAuditItemsModified" });
					    _pathNodes["GOUserDataObject"].Add( "gouserdepartmentitems", new PathNode { EntityName = "GOUserDepartmentDataObject", PathName = "GOUserDepartmentItems" });
					    _pathNodes["GOUserDataObject"].Add( "gorole", new PathNode { EntityName = "GORoleDataObject", PathName = "GORole" });
					    _pathNodes["GOUserDataObject"].Add( "tag", new PathNode { EntityName = "TagDataObject", PathName = "Tag" });
					    _pathNodes["GOUserDataObject"].Add( "gousertocustomeritems", new PathNode { EntityName = "GoUserToCustomerDataObject", PathName = "GoUserToCustomerItems" });
					    _pathNodes["GOUserDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "ServiceSettingsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ServiceSettingsDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "UpdateFirmwareRequestDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["UpdateFirmwareRequestDataObject"].Add( "firmware", new PathNode { EntityName = "FirmwareDataObject", PathName = "Firmware" });
					    _pathNodes.Add ( "SlamcoreAPIKeyDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SlamcoreAPIKeyDataObject"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDeviceDataObject", PathName = "SlamcoreDevice" });
					    _pathNodes.Add ( "CardDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CardDataObject"].Add( "sitevehiclenormalcardaccessitems", new PathNode { EntityName = "SiteVehicleNormalCardAccessDataObject", PathName = "SiteVehicleNormalCardAccessItems" });
					    _pathNodes["CardDataObject"].Add( "departmentvehiclemastercardaccessitems", new PathNode { EntityName = "DepartmentVehicleMasterCardAccessDataObject", PathName = "DepartmentVehicleMasterCardAccessItems" });
					    _pathNodes["CardDataObject"].Add( "pervehiclenormalcardaccessitems", new PathNode { EntityName = "PerVehicleNormalCardAccessDataObject", PathName = "PerVehicleNormalCardAccessItems" });
					    _pathNodes["CardDataObject"].Add( "cardtocardaccessitems", new PathNode { EntityName = "CardToCardAccessDataObject", PathName = "CardToCardAccessItems" });
					    _pathNodes["CardDataObject"].Add( "modelvehiclemastercardaccessitems", new PathNode { EntityName = "ModelVehicleMasterCardAccessDataObject", PathName = "ModelVehicleMasterCardAccessItems" });
					    _pathNodes["CardDataObject"].Add( "dealerdriver", new PathNode { EntityName = "DealerDriverDataObject", PathName = "DealerDriver" });
					    _pathNodes["CardDataObject"].Add( "departmentvehiclenormalcardaccessitems", new PathNode { EntityName = "DepartmentVehicleNormalCardAccessDataObject", PathName = "DepartmentVehicleNormalCardAccessItems" });
					    _pathNodes["CardDataObject"].Add( "pervehiclemastercardaccessitems", new PathNode { EntityName = "PerVehicleMasterCardAccessDataObject", PathName = "PerVehicleMasterCardAccessItems" });
					    _pathNodes["CardDataObject"].Add( "sitevehiclemastercardaccessitems", new PathNode { EntityName = "SiteVehicleMasterCardAccessDataObject", PathName = "SiteVehicleMasterCardAccessItems" });
					    _pathNodes["CardDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["CardDataObject"].Add( "modelvehiclenormalcardaccessitems", new PathNode { EntityName = "ModelVehicleNormalCardAccessDataObject", PathName = "ModelVehicleNormalCardAccessItems" });
					    _pathNodes.Add ( "GOUserDepartmentDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GOUserDepartmentDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes["GOUserDepartmentDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "CanruleDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CanruleDataObject"].Add( "vehicles", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicles" });
					    _pathNodes["CanruleDataObject"].Add( "canruledetailsitems", new PathNode { EntityName = "CanruleDetailsDataObject", PathName = "CanruleDetailsItems" });
					    _pathNodes.Add ( "ImpactFrequencyPerWeekDayViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ImpactFrequencyPerWeekDayViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["ImpactFrequencyPerWeekDayViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["ImpactFrequencyPerWeekDayViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["ImpactFrequencyPerWeekDayViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "SlamcoreDeviceHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SlamcoreDeviceHistoryDataObject"].Add( "slamcoredevice", new PathNode { EntityName = "SlamcoreDeviceDataObject", PathName = "SlamcoreDevice" });
					    _pathNodes["SlamcoreDeviceHistoryDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "VehicleHireDehireHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleHireDehireHistoryDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["VehicleHireDehireHistoryDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "ChecklistFailurePerVechicleViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ChecklistFailurePerVechicleViewDataObject"].Add( "preoperationalchecklist", new PathNode { EntityName = "PreOperationalChecklistDataObject", PathName = "PreOperationalChecklist" });
					    _pathNodes["ChecklistFailurePerVechicleViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "IncompletedChecklistViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["IncompletedChecklistViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["IncompletedChecklistViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["IncompletedChecklistViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["IncompletedChecklistViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "ReportSubscriptionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ReportSubscriptionDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes["ReportSubscriptionDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["ReportSubscriptionDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["ReportSubscriptionDataObject"].Add( "reporttype", new PathNode { EntityName = "ReportTypeDataObject", PathName = "ReportType" });
					    _pathNodes["ReportSubscriptionDataObject"].Add( "emailgroups", new PathNode { EntityName = "EmailGroupsDataObject", PathName = "EmailGroups" });
					    _pathNodes["ReportSubscriptionDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["ReportSubscriptionDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["ReportSubscriptionDataObject"].Add( "allemailsubscriptionstoreprocedureitems", new PathNode { EntityName = "AllEmailSubscriptionStoreProcedureDataObject", PathName = "AllEmailSubscriptionStoreProcedureItems" });
					    _pathNodes.Add ( "VehicleLastGPSLocationViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleLastGPSLocationViewDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes["VehicleLastGPSLocationViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "PSTATDetailsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PSTATDetailsDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes["PSTATDetailsDataObject"].Add( "iofield", new PathNode { EntityName = "IOFIELDDataObject", PathName = "IOFIELD" });
					    _pathNodes.Add ( "FloorZonesDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["FloorZonesDataObject"].Add( "floorplan", new PathNode { EntityName = "FloorPlanDataObject", PathName = "FloorPlan" });
					    _pathNodes["FloorZonesDataObject"].Add( "zonecoordinatesitems", new PathNode { EntityName = "ZoneCoordinatesDataObject", PathName = "ZoneCoordinatesItems" });
					    _pathNodes.Add ( "CanruleDetailsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CanruleDetailsDataObject"].Add( "canrule", new PathNode { EntityName = "CanruleDataObject", PathName = "Canrule" });
					    _pathNodes.Add ( "DriverLicenseExpiryStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DriverLicenseExpiryStoreProcedureDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["DriverLicenseExpiryStoreProcedureDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["DriverLicenseExpiryStoreProcedureDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["DriverLicenseExpiryStoreProcedureDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes.Add ( "DepartmentVehicleMasterCardAccessDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DepartmentVehicleMasterCardAccessDataObject"].Add( "pervehiclemastercardaccessitems", new PathNode { EntityName = "PerVehicleMasterCardAccessDataObject", PathName = "PerVehicleMasterCardAccessItems" });
					    _pathNodes["DepartmentVehicleMasterCardAccessDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes["DepartmentVehicleMasterCardAccessDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "VehicleUtilizationLastTwelveHoursStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleUtilizationLastTwelveHoursStoreProcedureDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["VehicleUtilizationLastTwelveHoursStoreProcedureDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["VehicleUtilizationLastTwelveHoursStoreProcedureDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["VehicleUtilizationLastTwelveHoursStoreProcedureDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "ChecklistFailureViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ChecklistFailureViewDataObject"].Add( "preoperationalchecklist", new PathNode { EntityName = "PreOperationalChecklistDataObject", PathName = "PreOperationalChecklist" });
					    _pathNodes.Add ( "PersonChecklistLanguageSettingsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonChecklistLanguageSettingsDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes.Add ( "VehicleSessionlessImpactDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleSessionlessImpactDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "LicenseByModelDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["LicenseByModelDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["LicenseByModelDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes.Add ( "AllMessageHistoryStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllMessageHistoryStoreProcedureDataObject"].Add( "messagehistory", new PathNode { EntityName = "MessageHistoryDataObject", PathName = "MessageHistory" });
					    _pathNodes.Add ( "ProficiencyCombinedViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ProficiencyCombinedViewDataObject"].Add( "vehicleproficiencyviewitems", new PathNode { EntityName = "VehicleProficiencyViewDataObject", PathName = "VehicleProficiencyViewItems" });
					    _pathNodes["ProficiencyCombinedViewDataObject"].Add( "driverproficiencyviewitems", new PathNode { EntityName = "DriverProficiencyViewDataObject", PathName = "DriverProficiencyViewItems" });
					    _pathNodes.Add ( "CustomerSnapshotDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CustomerSnapshotDataObject"].Add( "customerbeingchangetracked", new PathNode { EntityName = "CustomerDataObject", PathName = "CustomerBeingChangeTracked" });
					    _pathNodes.Add ( "GOGroupDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GOGroupDataObject"].Add( "grouproleitems", new PathNode { EntityName = "GOGroupRoleDataObject", PathName = "GroupRoleItems" });
					    _pathNodes["GOGroupDataObject"].Add( "usergroupitems", new PathNode { EntityName = "GOUserGroupDataObject", PathName = "UserGroupItems" });
					    _pathNodes.Add ( "VehiclesPerModelReportDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehiclesPerModelReportDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes.Add ( "TodaysImpactStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["TodaysImpactStoreProcedureDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["TodaysImpactStoreProcedureDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["TodaysImpactStoreProcedureDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["TodaysImpactStoreProcedureDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "DealerDriverDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DealerDriverDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes["DealerDriverDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes.Add ( "EmailGroupsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["EmailGroupsDataObject"].Add( "emailgroupstopersonitems", new PathNode { EntityName = "EmailGroupsToPersonDataObject", PathName = "EmailGroupsToPersonItems" });
					    _pathNodes["EmailGroupsDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["EmailGroupsDataObject"].Add( "reportsubscriptions", new PathNode { EntityName = "ReportSubscriptionDataObject", PathName = "ReportSubscriptions" });
					    _pathNodes.Add ( "PreOperationalChecklistDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PreOperationalChecklistDataObject"].Add( "checklistfailureview", new PathNode { EntityName = "ChecklistFailureViewDataObject", PathName = "ChecklistFailureView" });
					    _pathNodes["PreOperationalChecklistDataObject"].Add( "vehicletopreopcheckilstitems", new PathNode { EntityName = "VehicleToPreOpChecklistViewDataObject", PathName = "VehicleToPreOpCheckilstItems" });
					    _pathNodes["PreOperationalChecklistDataObject"].Add( "checklistanswersdetails", new PathNode { EntityName = "ChecklistDetailDataObject", PathName = "ChecklistAnswersDetails" });
					    _pathNodes["PreOperationalChecklistDataObject"].Add( "checklistfailurepervechicleviewitems", new PathNode { EntityName = "ChecklistFailurePerVechicleViewDataObject", PathName = "ChecklistFailurePerVechicleViewItems" });
					    _pathNodes["PreOperationalChecklistDataObject"].Add( "sitechecklist", new PathNode { EntityName = "DepartmentChecklistDataObject", PathName = "SiteChecklist" });
					    _pathNodes.Add ( "SlamcoreDeviceDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SlamcoreDeviceDataObject"].Add( "slamcoreapikey", new PathNode { EntityName = "SlamcoreAPIKeyDataObject", PathName = "SlamcoreAPIKey" });
					    _pathNodes["SlamcoreDeviceDataObject"].Add( "slamcorepedestriandetectionitems", new PathNode { EntityName = "SlamcorePedestrianDetectionDataObject", PathName = "SlamcorePedestrianDetectionItems" });
					    _pathNodes["SlamcoreDeviceDataObject"].Add( "slamcoreawareauthenticationdetails", new PathNode { EntityName = "SlamcoreAwareAuthenticationDetailsDataObject", PathName = "SlamcoreAwareAuthenticationDetails" });
					    _pathNodes["SlamcoreDeviceDataObject"].Add( "slamcoredeviceconnectionviewitems", new PathNode { EntityName = "SlamcoreDeviceConnectionViewDataObject", PathName = "SlamcoreDeviceConnectionViewItems" });
					    _pathNodes["SlamcoreDeviceDataObject"].Add( "vehicleslamcorelocationhistoryitems", new PathNode { EntityName = "VehicleSlamcoreLocationHistoryDataObject", PathName = "VehicleSlamcoreLocationHistoryItems" });
					    _pathNodes["SlamcoreDeviceDataObject"].Add( "slamcoredevicehistoryitems", new PathNode { EntityName = "SlamcoreDeviceHistoryDataObject", PathName = "SlamcoreDeviceHistoryItems" });
					    _pathNodes["SlamcoreDeviceDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["SlamcoreDeviceDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "SiteDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SiteDataObject"].Add( "currentvehiclestatuschartviewitems", new PathNode { EntityName = "CurrentVehicleStatusChartViewDataObject", PathName = "CurrentVehicleStatusChartViewItems" });
					    _pathNodes["SiteDataObject"].Add( "todaysimpactstoreprocedureitems", new PathNode { EntityName = "TodaysImpactStoreProcedureDataObject", PathName = "TodaysImpactStoreProcedureItems" });
					    _pathNodes["SiteDataObject"].Add( "impactfrequencyperweekmonthviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekMonthViewDataObject", PathName = "ImpactFrequencyPerWeekMonthViewItems" });
					    _pathNodes["SiteDataObject"].Add( "sitevehiclemastercardaccessitems", new PathNode { EntityName = "SiteVehicleMasterCardAccessDataObject", PathName = "SiteVehicleMasterCardAccessItems" });
					    _pathNodes["SiteDataObject"].Add( "impactfrequencyperweekdayviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekDayViewDataObject", PathName = "ImpactFrequencyPerWeekDayViewItems" });
					    _pathNodes["SiteDataObject"].Add( "departmentitems", new PathNode { EntityName = "DepartmentDataObject", PathName = "DepartmentItems" });
					    _pathNodes["SiteDataObject"].Add( "todayspreopcheckviewitems", new PathNode { EntityName = "TodaysPreopCheckViewDataObject", PathName = "TodaysPreopCheckViewItems" });
					    _pathNodes["SiteDataObject"].Add( "currentdriverstatuschartviewitems", new PathNode { EntityName = "CurrentDriverStatusChartViewDataObject", PathName = "CurrentDriverStatusChartViewItems" });
					    _pathNodes["SiteDataObject"].Add( "dashboarddrivercardstoreprocedureitems", new PathNode { EntityName = "DashboardDriverCardStoreProcedureDataObject", PathName = "DashboardDriverCardStoreProcedureItems" });
					    _pathNodes["SiteDataObject"].Add( "vehicleutilizationlasttwelvehoursstoreprocedureitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursStoreProcedureDataObject", PathName = "VehicleUtilizationLastTwelveHoursStoreProcedureItems" });
					    _pathNodes["SiteDataObject"].Add( "dashboardfilteritems", new PathNode { EntityName = "DashboardFilterDataObject", PathName = "DashboardFilterItems" });
					    _pathNodes["SiteDataObject"].Add( "driveritems", new PathNode { EntityName = "DriverDataObject", PathName = "DriverItems" });
					    _pathNodes["SiteDataObject"].Add( "sitefloorplanitems", new PathNode { EntityName = "SiteFloorPlanDataObject", PathName = "SiteFloorPlanItems" });
					    _pathNodes["SiteDataObject"].Add( "dashboardvehiclecardstoreprocedureitems", new PathNode { EntityName = "DashboardVehicleCardStoreProcedureDataObject", PathName = "DashboardVehicleCardStoreProcedureItems" });
					    _pathNodes["SiteDataObject"].Add( "impactfrequencypertimeslotviewitems", new PathNode { EntityName = "ImpactFrequencyPerTimeSlotViewDataObject", PathName = "ImpactFrequencyPerTimeSlotViewItems" });
					    _pathNodes["SiteDataObject"].Add( "accessgrouptositeitems", new PathNode { EntityName = "AccessGroupToSiteDataObject", PathName = "AccessGroupToSiteItems" });
					    _pathNodes["SiteDataObject"].Add( "incompletedchecklistviewitems", new PathNode { EntityName = "IncompletedChecklistViewDataObject", PathName = "IncompletedChecklistViewItems" });
					    _pathNodes["SiteDataObject"].Add( "personitems", new PathNode { EntityName = "PersonDataObject", PathName = "PersonItems" });
					    _pathNodes["SiteDataObject"].Add( "todayspreopcheckstoreprocedureitems", new PathNode { EntityName = "TodaysPreopCheckStoreProcedureDataObject", PathName = "TodaysPreopCheckStoreProcedureItems" });
					    _pathNodes["SiteDataObject"].Add( "dashboardcardviewitems", new PathNode { EntityName = "DashboardDriverCardViewDataObject", PathName = "DashboardCardViewItems" });
					    _pathNodes["SiteDataObject"].Add( "persontositevehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToSiteVehicleMasterAccessViewDataObject", PathName = "PersonToSiteVehicleMasterAccessViewItems" });
					    _pathNodes["SiteDataObject"].Add( "timezone", new PathNode { EntityName = "TimezoneDataObject", PathName = "Timezone" });
					    _pathNodes["SiteDataObject"].Add( "todaysimpactviewitems", new PathNode { EntityName = "TodaysImpactViewDataObject", PathName = "TodaysImpactViewItems" });
					    _pathNodes["SiteDataObject"].Add( "personallocationitems", new PathNode { EntityName = "PersonAllocationDataObject", PathName = "PersonAllocationItems" });
					    _pathNodes["SiteDataObject"].Add( "floorplanitems", new PathNode { EntityName = "FloorPlanDataObject", PathName = "FloorPlanItems" });
					    _pathNodes["SiteDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["SiteDataObject"].Add( "persontositevehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToSiteVehicleNormalAccessViewDataObject", PathName = "PersonToSiteVehicleNormalAccessViewItems" });
					    _pathNodes["SiteDataObject"].Add( "vehicleitems", new PathNode { EntityName = "VehicleDataObject", PathName = "VehicleItems" });
					    _pathNodes["SiteDataObject"].Add( "vehicleutilizationlasttwelvehoursviewitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursViewDataObject", PathName = "VehicleUtilizationLastTwelveHoursViewItems" });
					    _pathNodes["SiteDataObject"].Add( "sitevehiclenormalcardaccessitems", new PathNode { EntityName = "SiteVehicleNormalCardAccessDataObject", PathName = "SiteVehicleNormalCardAccessItems" });
					    _pathNodes["SiteDataObject"].Add( "loggedhoursversusseathoursviewitems", new PathNode { EntityName = "LoggedHoursVersusSeatHoursViewDataObject", PathName = "LoggedHoursVersusSeatHoursViewItems" });
					    _pathNodes["SiteDataObject"].Add( "driverlicenseexpiryviewitems", new PathNode { EntityName = "DriverLicenseExpiryViewDataObject", PathName = "DriverLicenseExpiryViewItems" });
					    _pathNodes["SiteDataObject"].Add( "checkliststatusviewitems", new PathNode { EntityName = "ChecklistStatusViewDataObject", PathName = "ChecklistStatusViewItems" });
					    _pathNodes["SiteDataObject"].Add( "dashboardvehiclecardviewitems", new PathNode { EntityName = "DashboardVehicleCardViewDataObject", PathName = "DashboardVehicleCardViewItems" });
					    _pathNodes["SiteDataObject"].Add( "driverlicenseexpirystoreprocedureitems", new PathNode { EntityName = "DriverLicenseExpiryStoreProcedureDataObject", PathName = "DriverLicenseExpiryStoreProcedureItems" });
					    _pathNodes.Add ( "AlertSubscriptionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AlertSubscriptionDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes["AlertSubscriptionDataObject"].Add( "alert", new PathNode { EntityName = "AlertDataObject", PathName = "Alert" });
					    _pathNodes["AlertSubscriptionDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["AlertSubscriptionDataObject"].Add( "vehiclealertsubscriptionitems", new PathNode { EntityName = "VehicleAlertSubscriptionDataObject", PathName = "VehicleAlertSubscriptionItems" });
					    _pathNodes.Add ( "CustomerAuditDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CustomerAuditDataObject"].Add( "gouserwhodeletedthiscustomer", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUserWhoDeletedThisCustomer" });
					    _pathNodes["CustomerAuditDataObject"].Add( "revisioncreated", new PathNode { EntityName = "RevisionDataObject", PathName = "RevisionCreated" });
					    _pathNodes["CustomerAuditDataObject"].Add( "gouserwhomodifiedthiscustomer", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUserWhoModifiedThisCustomer" });
					    _pathNodes["CustomerAuditDataObject"].Add( "revisiondeleted", new PathNode { EntityName = "RevisionDataObject", PathName = "RevisionDeleted" });
					    _pathNodes["CustomerAuditDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["CustomerAuditDataObject"].Add( "revisionlastmodified", new PathNode { EntityName = "RevisionDataObject", PathName = "RevisionLastModified" });
					    _pathNodes["CustomerAuditDataObject"].Add( "gouserwhocreatedthiscustomer", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUserWhoCreatedThisCustomer" });
					    _pathNodes.Add ( "PerVehicleNormalCardAccessDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PerVehicleNormalCardAccessDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes["PerVehicleNormalCardAccessDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["PerVehicleNormalCardAccessDataObject"].Add( "cardaccesslevel", new PathNode { EntityName = "PermissionDataObject", PathName = "CardAccessLevel" });
					    _pathNodes["PerVehicleNormalCardAccessDataObject"].Add( "vehiclesupervisorsviewitems", new PathNode { EntityName = "VehicleSupervisorsViewDataObject", PathName = "VehicleSupervisorsViewItems" });
					    _pathNodes.Add ( "PedestrianDetectionHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PedestrianDetectionHistoryDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["PedestrianDetectionHistoryDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "PersonToPerVehicleNormalAccessViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonToPerVehicleNormalAccessViewDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["PersonToPerVehicleNormalAccessViewDataObject"].Add( "cardaccesslevel", new PathNode { EntityName = "PermissionDataObject", PathName = "CardAccessLevel" });
					    _pathNodes["PersonToPerVehicleNormalAccessViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "DashboardDriverCardStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DashboardDriverCardStoreProcedureDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["DashboardDriverCardStoreProcedureDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["DashboardDriverCardStoreProcedureDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["DashboardDriverCardStoreProcedureDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "TodaysImpactViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["TodaysImpactViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["TodaysImpactViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["TodaysImpactViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["TodaysImpactViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "DetailedSessionViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DetailedSessionViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["DetailedSessionViewDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["DetailedSessionViewDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes.Add ( "ImportJobLogDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ImportJobLogDataObject"].Add( "importjobstatus", new PathNode { EntityName = "ImportJobStatusDataObject", PathName = "ImportJobStatus" });
					    _pathNodes["ImportJobLogDataObject"].Add( "importjobbatch", new PathNode { EntityName = "ImportJobBatchDataObject", PathName = "ImportJobBatch" });
					    _pathNodes.Add ( "OnDemandSessionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["OnDemandSessionDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["OnDemandSessionDataObject"].Add( "ondemandauthorisationstoreprocedureitems", new PathNode { EntityName = "OnDemandAuthorisationStoreProcedureDataObject", PathName = "OnDemandAuthorisationStoreProcedureItems" });
					    _pathNodes["OnDemandSessionDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "AllVORStatusStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllVORStatusStoreProcedureDataObject"].Add( "vorreportcombinedview", new PathNode { EntityName = "VORReportCombinedViewDataObject", PathName = "VORReportCombinedView" });
					    _pathNodes["AllVORStatusStoreProcedureDataObject"].Add( "vorsettinghistory", new PathNode { EntityName = "VORSettingHistoryDataObject", PathName = "VORSettingHistory" });
					    _pathNodes.Add ( "VORSettingHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VORSettingHistoryDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["VORSettingHistoryDataObject"].Add( "allvorstatusstoreprocedureitems", new PathNode { EntityName = "AllVORStatusStoreProcedureDataObject", PathName = "AllVORStatusStoreProcedureItems" });
					    _pathNodes["VORSettingHistoryDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes.Add ( "AllUserSummaryStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["AllUserSummaryStoreProcedureDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["AllUserSummaryStoreProcedureDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes.Add ( "CustomerSSODetailDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CustomerSSODetailDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "DepartmentHourSettingsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DepartmentHourSettingsDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "DashboardVehicleCardViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DashboardVehicleCardViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["DashboardVehicleCardViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["DashboardVehicleCardViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["DashboardVehicleCardViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "CurrentStatusDriverViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CurrentStatusDriverViewDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["CurrentStatusDriverViewDataObject"].Add( "currentstatuscombinedview", new PathNode { EntityName = "CurrentStatusCombinedViewDataObject", PathName = "CurrentStatusCombinedView" });
					    _pathNodes["CurrentStatusDriverViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "ReportTypeDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ReportTypeDataObject"].Add( "reportsubscriptions", new PathNode { EntityName = "ReportSubscriptionDataObject", PathName = "ReportSubscriptions" });
					    _pathNodes.Add ( "SiteVehicleMasterCardAccessDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SiteVehicleMasterCardAccessDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["SiteVehicleMasterCardAccessDataObject"].Add( "card", new PathNode { EntityName = "CardDataObject", PathName = "Card" });
					    _pathNodes.Add ( "PersonToDepartmentVehicleNormalAccessViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonToDepartmentVehicleNormalAccessViewDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["PersonToDepartmentVehicleNormalAccessViewDataObject"].Add( "cardaccesslevel", new PathNode { EntityName = "PermissionDataObject", PathName = "CardAccessLevel" });
					    _pathNodes["PersonToDepartmentVehicleNormalAccessViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "GOUserGroupDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GOUserGroupDataObject"].Add( "user", new PathNode { EntityName = "GOUserDataObject", PathName = "User" });
					    _pathNodes["GOUserGroupDataObject"].Add( "group", new PathNode { EntityName = "GOGroupDataObject", PathName = "Group" });
					    _pathNodes.Add ( "CustomerFeatureSubscriptionDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CustomerFeatureSubscriptionDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "VehicleToPreOpChecklistViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["VehicleToPreOpChecklistViewDataObject"].Add( "preoperationalchecklist", new PathNode { EntityName = "PreOperationalChecklistDataObject", PathName = "PreOperationalChecklist" });
					    _pathNodes["VehicleToPreOpChecklistViewDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "PersonToSiteVehicleMasterAccessViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonToSiteVehicleMasterAccessViewDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["PersonToSiteVehicleMasterAccessViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes.Add ( "DealerConfigurationDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DealerConfigurationDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "TodaysPreopCheckStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["TodaysPreopCheckStoreProcedureDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["TodaysPreopCheckStoreProcedureDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["TodaysPreopCheckStoreProcedureDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["TodaysPreopCheckStoreProcedureDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes.Add ( "CustomerPreOperationalChecklistTemplateDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CustomerPreOperationalChecklistTemplateDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "CurrentDriverStatusChartViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["CurrentDriverStatusChartViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["CurrentDriverStatusChartViewDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["CurrentDriverStatusChartViewDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes["CurrentDriverStatusChartViewDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "SiteFloorPlanDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["SiteFloorPlanDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["SiteFloorPlanDataObject"].Add( "floorplan", new PathNode { EntityName = "FloorPlanDataObject", PathName = "FloorPlan" });
					    _pathNodes.Add ( "BroadcastMessageHistoryDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["BroadcastMessageHistoryDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["BroadcastMessageHistoryDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "GoUserToCustomerDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["GoUserToCustomerDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes["GoUserToCustomerDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes.Add ( "ImportJobBatchDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["ImportJobBatchDataObject"].Add( "importjoblogitems", new PathNode { EntityName = "ImportJobLogDataObject", PathName = "ImportJobLogItems" });
					    _pathNodes["ImportJobBatchDataObject"].Add( "importjobstatusitems", new PathNode { EntityName = "ImportJobStatusDataObject", PathName = "ImportJobStatusItems" });
					    _pathNodes.Add ( "PersonDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["PersonDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["PersonDataObject"].Add( "personchecklistlanguagesettings", new PathNode { EntityName = "PersonChecklistLanguageSettingsDataObject", PathName = "PersonChecklistLanguageSettings" });
					    _pathNodes["PersonDataObject"].Add( "personallocationitems", new PathNode { EntityName = "PersonAllocationDataObject", PathName = "PersonAllocationItems" });
					    _pathNodes["PersonDataObject"].Add( "persontositevehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToSiteVehicleNormalAccessViewDataObject", PathName = "PersonToSiteVehicleNormalAccessViewItems" });
					    _pathNodes["PersonDataObject"].Add( "reportsubscriptionitems", new PathNode { EntityName = "ReportSubscriptionDataObject", PathName = "ReportSubscriptionItems" });
					    _pathNodes["PersonDataObject"].Add( "gouser", new PathNode { EntityName = "GOUserDataObject", PathName = "GOUser" });
					    _pathNodes["PersonDataObject"].Add( "allusersummarystoreprocedureitems", new PathNode { EntityName = "AllUserSummaryStoreProcedureDataObject", PathName = "AllUserSummaryStoreProcedureItems" });
					    _pathNodes["PersonDataObject"].Add( "alertsubscriptions", new PathNode { EntityName = "AlertSubscriptionDataObject", PathName = "AlertSubscriptions" });
					    _pathNodes["PersonDataObject"].Add( "vorsettinghistoryitems", new PathNode { EntityName = "VORSettingHistoryDataObject", PathName = "VORSettingHistoryItems" });
					    _pathNodes["PersonDataObject"].Add( "persontodepartmentvehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToDepartmentVehicleMasterAccessViewDataObject", PathName = "PersonToDepartmentVehicleMasterAccessViewItems" });
					    _pathNodes["PersonDataObject"].Add( "driver", new PathNode { EntityName = "DriverDataObject", PathName = "Driver" });
					    _pathNodes["PersonDataObject"].Add( "persontopervehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToPerVehicleNormalAccessViewDataObject", PathName = "PersonToPerVehicleNormalAccessViewItems" });
					    _pathNodes["PersonDataObject"].Add( "persontomodelvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToModelVehicleNormalAccessViewDataObject", PathName = "PersonToModelVehicleNormalAccessViewItems" });
					    _pathNodes["PersonDataObject"].Add( "accessgroup", new PathNode { EntityName = "AccessGroupDataObject", PathName = "AccessGroup" });
					    _pathNodes["PersonDataObject"].Add( "websiteuser", new PathNode { EntityName = "WebsiteUserDataObject", PathName = "WebsiteUser" });
					    _pathNodes["PersonDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["PersonDataObject"].Add( "persontomodelvehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToModelVehicleMasterAccessViewDataObject", PathName = "PersonToModelVehicleMasterAccessViewItems" });
					    _pathNodes["PersonDataObject"].Add( "persontodepartmentvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToDepartmentVehicleNormalAccessViewDataObject", PathName = "PersonToDepartmentVehicleNormalAccessViewItems" });
					    _pathNodes["PersonDataObject"].Add( "emailgroupstopersonitems", new PathNode { EntityName = "EmailGroupsToPersonDataObject", PathName = "EmailGroupsToPersonItems" });
					    _pathNodes["PersonDataObject"].Add( "persontositevehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToSiteVehicleMasterAccessViewDataObject", PathName = "PersonToSiteVehicleMasterAccessViewItems" });
					    _pathNodes["PersonDataObject"].Add( "persontopervehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToPerVehicleMasterAccessViewDataObject", PathName = "PersonToPerVehicleMasterAccessViewItems" });
					    _pathNodes["PersonDataObject"].Add( "customertopersonviewitems", new PathNode { EntityName = "CustomerToPersonViewDataObject", PathName = "CustomerToPersonViewItems" });
					    _pathNodes["PersonDataObject"].Add( "vehicleitems", new PathNode { EntityName = "VehicleDataObject", PathName = "VehicleItems" });
					    _pathNodes.Add ( "DetailedVORSessionStoreProcedureDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DetailedVORSessionStoreProcedureDataObject"].Add( "session", new PathNode { EntityName = "SessionDataObject", PathName = "Session" });
					    _pathNodes["DetailedVORSessionStoreProcedureDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "PersonToModelVehicleNormalAccessViewDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PersonToModelVehicleNormalAccessViewDataObject"].Add( "model", new PathNode { EntityName = "ModelDataObject", PathName = "Model" });
					    _pathNodes["PersonToModelVehicleNormalAccessViewDataObject"].Add( "department", new PathNode { EntityName = "DepartmentDataObject", PathName = "Department" });
					    _pathNodes["PersonToModelVehicleNormalAccessViewDataObject"].Add( "person", new PathNode { EntityName = "PersonDataObject", PathName = "Person" });
					    _pathNodes["PersonToModelVehicleNormalAccessViewDataObject"].Add( "cardaccesslevel", new PathNode { EntityName = "PermissionDataObject", PathName = "CardAccessLevel" });
					    _pathNodes.Add ( "NetworkSettingsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["NetworkSettingsDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes.Add ( "DepartmentDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["DepartmentDataObject"].Add( "dashboardvehiclecardviewitems", new PathNode { EntityName = "DashboardVehicleCardViewDataObject", PathName = "DashboardVehicleCardViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "driveritems", new PathNode { EntityName = "DriverDataObject", PathName = "DriverItems" });
					    _pathNodes["DepartmentDataObject"].Add( "currentdriverstatuschartviewitems", new PathNode { EntityName = "CurrentDriverStatusChartViewDataObject", PathName = "CurrentDriverStatusChartViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "incompletedchecklistviewitems", new PathNode { EntityName = "IncompletedChecklistViewDataObject", PathName = "IncompletedChecklistViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "persontodepartmentvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToDepartmentVehicleNormalAccessViewDataObject", PathName = "PersonToDepartmentVehicleNormalAccessViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "vehicleutilizationlasttwelvehoursviewitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursViewDataObject", PathName = "VehicleUtilizationLastTwelveHoursViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "departmentvehiclenormalcardaccessitems", new PathNode { EntityName = "DepartmentVehicleNormalCardAccessDataObject", PathName = "DepartmentVehicleNormalCardAccessItems" });
					    _pathNodes["DepartmentDataObject"].Add( "departmenthoursettings", new PathNode { EntityName = "DepartmentHourSettingsDataObject", PathName = "DepartmentHourSettings" });
					    _pathNodes["DepartmentDataObject"].Add( "sitechecklistitems", new PathNode { EntityName = "DepartmentChecklistDataObject", PathName = "SiteChecklistItems" });
					    _pathNodes["DepartmentDataObject"].Add( "persontodepartmentvehiclemasteraccessviewitems", new PathNode { EntityName = "PersonToDepartmentVehicleMasterAccessViewDataObject", PathName = "PersonToDepartmentVehicleMasterAccessViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "vehicles", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicles" });
					    _pathNodes["DepartmentDataObject"].Add( "todaysimpactviewitems", new PathNode { EntityName = "TodaysImpactViewDataObject", PathName = "TodaysImpactViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "impactfrequencypertimeslotviewitems", new PathNode { EntityName = "ImpactFrequencyPerTimeSlotViewDataObject", PathName = "ImpactFrequencyPerTimeSlotViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "personallocationitems", new PathNode { EntityName = "PersonAllocationDataObject", PathName = "PersonAllocationItems" });
					    _pathNodes["DepartmentDataObject"].Add( "persontomodelvehiclenormalaccessviewitems", new PathNode { EntityName = "PersonToModelVehicleNormalAccessViewDataObject", PathName = "PersonToModelVehicleNormalAccessViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "dashboarddrivercardstoreprocedureitems", new PathNode { EntityName = "DashboardDriverCardStoreProcedureDataObject", PathName = "DashboardDriverCardStoreProcedureItems" });
					    _pathNodes["DepartmentDataObject"].Add( "site", new PathNode { EntityName = "SiteDataObject", PathName = "Site" });
					    _pathNodes["DepartmentDataObject"].Add( "impactfrequencyperweekdayviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekDayViewDataObject", PathName = "ImpactFrequencyPerWeekDayViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "dashboardvehiclecardstoreprocedureitems", new PathNode { EntityName = "DashboardVehicleCardStoreProcedureDataObject", PathName = "DashboardVehicleCardStoreProcedureItems" });
					    _pathNodes["DepartmentDataObject"].Add( "driverlicenseexpiryviewitems", new PathNode { EntityName = "DriverLicenseExpiryViewDataObject", PathName = "DriverLicenseExpiryViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "todayspreopcheckviewitems", new PathNode { EntityName = "TodaysPreopCheckViewDataObject", PathName = "TodaysPreopCheckViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "impactfrequencyperweekmonthviewitems", new PathNode { EntityName = "ImpactFrequencyPerWeekMonthViewDataObject", PathName = "ImpactFrequencyPerWeekMonthViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "vehiclehiredehirehistoryitems", new PathNode { EntityName = "VehicleHireDehireHistoryDataObject", PathName = "VehicleHireDehireHistoryItems" });
					    _pathNodes["DepartmentDataObject"].Add( "todaysimpactstoreprocedureitems", new PathNode { EntityName = "TodaysImpactStoreProcedureDataObject", PathName = "TodaysImpactStoreProcedureItems" });
					    _pathNodes["DepartmentDataObject"].Add( "driverlicenseexpirystoreprocedureitems", new PathNode { EntityName = "DriverLicenseExpiryStoreProcedureDataObject", PathName = "DriverLicenseExpiryStoreProcedureItems" });
					    _pathNodes["DepartmentDataObject"].Add( "checkliststatusviewitems", new PathNode { EntityName = "ChecklistStatusViewDataObject", PathName = "ChecklistStatusViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "dashboardfilteritems", new PathNode { EntityName = "DashboardFilterDataObject", PathName = "DashboardFilterItems" });
					    _pathNodes["DepartmentDataObject"].Add( "todayspreopcheckstoreprocedureitems", new PathNode { EntityName = "TodaysPreopCheckStoreProcedureDataObject", PathName = "TodaysPreopCheckStoreProcedureItems" });
					    _pathNodes["DepartmentDataObject"].Add( "personitems", new PathNode { EntityName = "PersonDataObject", PathName = "PersonItems" });
					    _pathNodes["DepartmentDataObject"].Add( "dashboardcardviewitems", new PathNode { EntityName = "DashboardDriverCardViewDataObject", PathName = "DashboardCardViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "vehicleutilizationlasttwelvehoursstoreprocedureitems", new PathNode { EntityName = "VehicleUtilizationLastTwelveHoursStoreProcedureDataObject", PathName = "VehicleUtilizationLastTwelveHoursStoreProcedureItems" });
					    _pathNodes["DepartmentDataObject"].Add( "customer", new PathNode { EntityName = "CustomerDataObject", PathName = "Customer" });
					    _pathNodes["DepartmentDataObject"].Add( "currentvehiclestatuschartviewitems", new PathNode { EntityName = "CurrentVehicleStatusChartViewDataObject", PathName = "CurrentVehicleStatusChartViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "loggedhoursversusseathoursviewitems", new PathNode { EntityName = "LoggedHoursVersusSeatHoursViewDataObject", PathName = "LoggedHoursVersusSeatHoursViewItems" });
					    _pathNodes["DepartmentDataObject"].Add( "departmentvehiclemastercardaccessitems", new PathNode { EntityName = "DepartmentVehicleMasterCardAccessDataObject", PathName = "DepartmentVehicleMasterCardAccessItems" });
					    _pathNodes.Add ( "PerVehicleMasterCardAccessDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["PerVehicleMasterCardAccessDataObject"].Add( "departmentvehiclemastercardaccess", new PathNode { EntityName = "DepartmentVehicleMasterCardAccessDataObject", PathName = "DepartmentVehicleMasterCardAccess" });
					    _pathNodes["PerVehicleMasterCardAccessDataObject"].Add( "card1", new PathNode { EntityName = "CardDataObject", PathName = "Card1" });
					    _pathNodes["PerVehicleMasterCardAccessDataObject"].Add( "modelvehiclemastercardaccess", new PathNode { EntityName = "ModelVehicleMasterCardAccessDataObject", PathName = "ModelVehicleMasterCardAccess" });
					    _pathNodes["PerVehicleMasterCardAccessDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
					    _pathNodes["PerVehicleMasterCardAccessDataObject"].Add( "sitevehiclenormalcardaccess", new PathNode { EntityName = "SiteVehicleNormalCardAccessDataObject", PathName = "SiteVehicleNormalCardAccess" });
					    _pathNodes.Add ( "FeatureSubscriptionsFilterDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["FeatureSubscriptionsFilterDataObject"].Add( "dealer", new PathNode { EntityName = "DealerDataObject", PathName = "Dealer" });
					    _pathNodes.Add ( "OnDemandSettingsDataObject", new Dictionary<string,PathNode>());

					    _pathNodes["OnDemandSettingsDataObject"].Add( "vehicle", new PathNode { EntityName = "VehicleDataObject", PathName = "Vehicle" });
				    }
                }

                return _pathNodes;
            }
        }
    }
}