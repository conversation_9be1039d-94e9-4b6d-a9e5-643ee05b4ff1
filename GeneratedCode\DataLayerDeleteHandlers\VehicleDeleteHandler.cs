﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;

using FleetXQ.Data.DataObjects;

using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DeleteHandlers
{
	public partial class VehicleDeleteHandler : DeleteHandlerBase<VehicleDataObject>
	{
		
        public VehicleDeleteHandler(IServiceProvider serviceProvider, IDataProvider<VehicleDataObject> tentityDataProvider, ITransactionProvider transactionProvider, IEntityDataProvider entityDataProvider) : base(serviceProvider, tentityDataProvider, transactionProvider, entityDataProvider)
        {
        }

		public override async Task RippleDeleteAsync(VehicleDataObject instance, Parameters parameters, DataProviderDeleteSettings settings)
		{
			// Set resync flag initially so that if any processing is done, it's on the latest copy of the data
			NeedResync = true;

			// Unidrectional backwards reference from AlertHistory.Vehicle (ProtectedBackReference)
			{
				instance = await ResyncAsync(instance);				
				var db = _serviceProvider.GetRequiredService<IDataProvider<AlertHistoryDataObject>>();
				var links = await db.GetCollectionAsync(null, "VehicleId == @0", new object[] { instance.Id }, parameters: parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteBackReference", instance, links);
			}
			// Vehicle.BroadcastMessageHistoryItems (Protected) (i.e. Unable to delete Vehicle instances because BroadcastMessageHistoryItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadBroadcastMessageHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.BroadcastMessageHistoryItems);
			}
			// Vehicle.ChecklistSettings (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadChecklistSettingsAsync(parameters, skipSecurity: true);
				// We are the FK side entity and the PK side is optional, so no action required here (because when we're deleted, any reference(s) to ChecklistSettings(s) is (are) deleted with us)	
			}
			// Vehicle.Driver (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadDriverAsync(parameters, skipSecurity: true);
				// We are the FK side entity and the PK side is optional, so no action required here (because when we're deleted, any reference(s) to Driver(s) is (are) deleted with us)	
			}
			// Vehicle.Inspection (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadInspectionAsync(parameters, skipSecurity: true);
				// We are the FK side entity and the PK side is optional, so no action required here (because when we're deleted, any reference(s) to Inspection(s) is (are) deleted with us)	
			}
			// Vehicle.MessageHistoryItems (Protected) (i.e. Unable to delete Vehicle instances because MessageHistoryItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadMessageHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.MessageHistoryItems);
			}
			// Vehicle.Module (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadModuleAsync(parameters, skipSecurity: true);
				
				// We are the PK side entity in a OneToOne relationship, and the FK reference to us is optional.
				// So delete can proceed, but first we need to clear down the FK reference to us.
				var item = instance.Module;				
				if (item != null && item.Vehicle != null)
				{
					item.Vehicle = null; 
					await SaveAsync(item);
				}
			}
			// Vehicle.ModuleHistoryItems (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadModuleHistoryItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.ModuleHistoryItems)			
				{					
					if (item.Vehicle != null)
					{	
						item.Vehicle = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// Vehicle.NetworkSettingsItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadNetworkSettingsItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.NetworkSettingsItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// Vehicle.OnDemandSessionItems (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadOnDemandSessionItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.OnDemandSessionItems)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// Vehicle.OnDemandSettings (Protected) (i.e. Unable to delete Vehicle instances because OnDemandSettings.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadOnDemandSettingsAsync(parameters, skipSecurity: true);
				if (instance.OnDemandSettings != null)
				{
					if (!DeleteStack.IsResolved(instance.OnDemandSettings))
					{
						await AddBlockageAsync("failDeleteProtected", instance, instance.OnDemandSettings);
					}
				}
			}
			// Vehicle.PedestrianDetectionHistoryItems (Protected) (i.e. Unable to delete Vehicle instances because PedestrianDetectionHistoryItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadPedestrianDetectionHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.PedestrianDetectionHistoryItems);
			}
			// Vehicle.VehicleCardAccesses (Protected) (i.e. Unable to delete Vehicle instances because VehicleCardAccesses.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleCardAccessesAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.VehicleCardAccesses);
			}
			// Vehicle.PerVehicleNormalCardAccessItems (Protected) (i.e. Unable to delete Vehicle instances because PerVehicleNormalCardAccessItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadPerVehicleNormalCardAccessItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.PerVehicleNormalCardAccessItems);
			}
			// Vehicle.ServiceSettings (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadServiceSettingsAsync(parameters, skipSecurity: true);
				// We are the FK side entity and the PK side is optional, so no action required here (because when we're deleted, any reference(s) to ServiceSettings(s) is (are) deleted with us)	
			}
			// Vehicle.Sessions (Cascade)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSessionsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.Sessions)			
				{					
					await DeleteAsync(item, parameters, settings, instance);
				}				
			}
			// Unidrectional backwards reference from SlamcoreDevice.Vehicle (BackReference)
			{
				instance = await ResyncAsync(instance);				
				var db = _serviceProvider.GetRequiredService<IDataProvider<SlamcoreDeviceDataObject>>();
				var links = await db.GetCollectionAsync(null, "VehicleId == @0", new object[] { instance.Id }, parameters: parameters, skipSecurity: true);
				if (links.Any())
				{
					// instance is optional from point of view of the linked SlamcoreDevices => we're allowed to clear down the references to allow the delete to proceed
					foreach (var item in links)
					{
						item.VehicleId = null;
						await SaveAsync(item);
					}
				}
			}
			// Vehicle.SlamcoreDeviceHistoryItems (Protected) (i.e. Unable to delete Vehicle instances because SlamcoreDeviceHistoryItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadSlamcoreDeviceHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.SlamcoreDeviceHistoryItems);
			}
			// Vehicle.VehicleAlertSubscriptionItems (Protected) (i.e. Unable to delete Vehicle instances because VehicleAlertSubscriptionItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleAlertSubscriptionItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.VehicleAlertSubscriptionItems);
			}
			// Vehicle.VehicleBroadcastMessageItems (Protected) (i.e. Unable to delete Vehicle instances because VehicleBroadcastMessageItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleBroadcastMessageItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.VehicleBroadcastMessageItems);
			}
			// Vehicle.VehicleDiagnostic (Protected) (i.e. Unable to delete Vehicle instances because VehicleDiagnostic.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleDiagnosticAsync(parameters, skipSecurity: true);
				if (instance.VehicleDiagnostic != null)
				{
					if (!DeleteStack.IsResolved(instance.VehicleDiagnostic))
					{
						await AddBlockageAsync("failDeleteProtected", instance, instance.VehicleDiagnostic);
					}
				}
			}
			// Vehicle.VehicleGPSLocations (Protected) (i.e. Unable to delete Vehicle instances because VehicleGPSLocations.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleGPSLocationsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.VehicleGPSLocations);
			}
			// Vehicle.VehicleHireDehireHistoryItems (Protected) (i.e. Unable to delete Vehicle instances because VehicleHireDehireHistoryItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleHireDehireHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.VehicleHireDehireHistoryItems);
			}
			// Vehicle.VehicleLockoutItems (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleLockoutItemsAsync(parameters, skipSecurity: true);
				foreach (var item in instance.VehicleLockoutItems)			
				{					
					if (item.Vehicle != null)
					{	
						item.Vehicle = null; 	
						await SaveAsync(item);							
					}
				}
			}
			// Vehicle.VehicleOtherSettings (Reference)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleOtherSettingsAsync(parameters, skipSecurity: true);
				// We are the FK side entity and the PK side is optional, so no action required here (because when we're deleted, any reference(s) to VehicleOtherSettings(s) is (are) deleted with us)	
			}
			// Vehicle.VehicleSessionlessImpactItems (Protected) (i.e. Unable to delete Vehicle instances because VehicleSessionlessImpactItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVehicleSessionlessImpactItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.VehicleSessionlessImpactItems);
			}
			// Vehicle.VORSettingHistoryItems (Protected) (i.e. Unable to delete Vehicle instances because VORSettingHistoryItems.Vehicle is not optional and needs to be cleared first)
			{
				instance = await ResyncAsync(instance);
				await instance.LoadVORSettingHistoryItemsAsync(parameters, skipSecurity: true);
				await AddAnyBlockagesAsync("failDeleteProtected", instance, instance.VORSettingHistoryItems);
			}
		}
	}
}