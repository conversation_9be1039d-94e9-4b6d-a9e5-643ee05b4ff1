using FleetXQ.Tools.BulkImporter.Configuration;

namespace FleetXQ.Tools.BulkImporter.Services;

/// <summary>
/// Service interface for SQL-based data generation and processing
/// </summary>
public interface ISqlDataGenerationService
{
    /// <summary>
    /// Generates synthetic driver data directly in SQL staging tables
    /// </summary>
    /// <param name="sessionId">Import session identifier</param>
    /// <param name="count">Number of drivers to generate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generation result summary</returns>
    Task<DataGenerationResult> GenerateDriverDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates synthetic vehicle data directly in SQL staging tables
    /// </summary>
    /// <param name="sessionId">Import session identifier</param>
    /// <param name="count">Number of vehicles to generate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generation result summary</returns>
    Task<DataGenerationResult> GenerateVehicleDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new import session for tracking operations
    /// </summary>
    /// <param name="sessionName">Name for the import session</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Session identifier</returns>
    Task<Guid> CreateImportSessionAsync(string sessionName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates import session status and statistics
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="status">Session status</param>
    /// <param name="totalRows">Total rows processed</param>
    /// <param name="successfulRows">Successfully processed rows</param>
    /// <param name="failedRows">Failed rows</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UpdateImportSessionAsync(Guid sessionId, string status, int totalRows, int successfulRows, int failedRows, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates staged data using SQL stored procedures
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result summary</returns>
    Task<ValidationResult> ValidateStagedDataAsync(Guid sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes validated staging data into production tables
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="dryRun">Whether to perform a dry run without actual changes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result summary</returns>
    Task<ProcessingResult> ProcessStagedDataAsync(Guid sessionId, bool dryRun = false, CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of data generation operation
/// </summary>
public class DataGenerationResult
{
    public bool Success { get; set; }
    public int GeneratedRows { get; set; }
    public TimeSpan Duration { get; set; }
    public List<string> Errors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of data validation operation
/// </summary>
public class ValidationResult
{
    public bool Success { get; set; }
    public int ValidRows { get; set; }
    public int InvalidRows { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of data processing operation
/// </summary>
public class ProcessingResult
{
    public bool Success { get; set; }
    public int ProcessedRows { get; set; }
    public int InsertedRows { get; set; }
    public int UpdatedRows { get; set; }
    public int SkippedRows { get; set; }
    public List<string> ProcessingErrors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}
