﻿{
  "entityName": "FloorPlan",
  "entityNamePlural": "FloorPlans",   "entityDescription": "This entity represents a FloorPlan",
  "fields": {
    "File": {
        "displayName": "File", 
        "description": "File"
    },
    "FileFileSize": {
        "displayName": "File File size (bytes)", 
        "description": "File File size (bytes)", 
        "validationRules": {
            "453b0a48-56a2-4b3e-809d-f0a4459c8e79" : {
                    "errorMessage": "Value must be a number for the field File File size (bytes)"
            }
        }
    },
    "FileInternalName": {
        "displayName": "File Internal Name", 
        "description": "File Internal Name"
    },
    "FileUrl": {
        "displayName": "File Url", 
        "description": "File Url"
    },
    "FloorLevel": {
        "displayName": "Floor Level", 
        "description": "Floor Level"
    },
    "FloorPlanNameWithFloorLevel": {
        "displayName": "FloorPlanNameWithFloorLevel", 
        "description": "FloorPlanNameWithFloorLevel"
    },
    "FloorZonesItems": {
        "displayName": "Floor Zones Items", 
        "description": "Floor Zones Items"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "Length": {
        "displayName": "Length", 
        "description": "Length", 
        "validationRules": {
            "58f0c250-a79e-474c-bbdb-ed2f30a13e0d" : {
                    "errorMessage": "Value must be a number for the field Length"
            }
        }
    },
    "Name": {
        "displayName": "Name", 
        "description": "Name"
    },
    "Opacity": {
        "displayName": "Opacity", 
        "description": "Opacity", 
        "validationRules": {
            "3102d737-9062-4ff7-a99f-0e0ab5b79513" : {
                    "errorMessage": "Value must be a number for the field Opacity"
            }
        }
    },
    "Rotation": {
        "displayName": "Rotation", 
        "description": "Rotation", 
        "validationRules": {
            "008520ce-0375-4900-9586-69a9dc9b1cb4" : {
                    "errorMessage": "Value must be a number for the field Rotation"
            }
        }
    },
    "Scale": {
        "displayName": "Scale", 
        "description": "Scale", 
        "validationRules": {
            "ac5400ac-a0a6-49f8-ab0b-d70eebba1655" : {
                    "errorMessage": "Value must be a number for the field Scale"
            }
        }
    },
    "Site": {
        "displayName": "Site", 
        "description": "Site"
    },
    "SiteFloorPlanItems": {
        "displayName": "SiteFloorPlan Items", 
        "description": "SiteFloorPlan Items"
    },
    "SiteId": {
        "displayName": "SiteId", 
        "description": "Foreign Key"
    },
    "Width": {
        "displayName": "Width", 
        "description": "Width", 
        "validationRules": {
            "27162312-e8e4-4f71-9f70-9101fe403b7f" : {
                    "errorMessage": "Value must be a number for the field Width"
            }
        }
    },
    "XPosition": {
        "displayName": "XPosition", 
        "description": "XPosition", 
        "validationRules": {
            "ecdaa65f-18af-4d89-9329-5273a20690f0" : {
                    "errorMessage": "Value must be a number for the field XPosition"
            }
        }
    },
    "YPosition": {
        "displayName": "YPosition", 
        "description": "YPosition", 
        "validationRules": {
            "e935e04b-f2f9-44f1-aab7-7189d204acf6" : {
                    "errorMessage": "Value must be a number for the field YPosition"
            }
        }
    }
  }
} 