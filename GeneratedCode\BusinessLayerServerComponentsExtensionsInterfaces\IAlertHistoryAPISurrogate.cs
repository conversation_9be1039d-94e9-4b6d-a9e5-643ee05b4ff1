﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;


namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// AlertHistoryAPI Component
	///  
	/// </summary>
	public partial interface IAlertHistoryAPISurrogate 
    {
		/// <summary>
        /// Acknowledge Method
		///  
		/// </summary>
		/// <param name="alertHistoryId"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> AcknowledgeAsync(System.Guid alertHistoryId, Dictionary<string, object> parameters = null);
		/// <summary>
        /// Resolve Method
		///  
		/// </summary>
		/// <param name="alertHistoryId"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> ResolveAsync(System.Guid alertHistoryId, Dictionary<string, object> parameters = null);
	}
}
