﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge DataObject -> ORMEntity
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	public partial class SlamcoreDeviceDataObject
	{
		protected IThreadContext _threadContext => _serviceProvider.GetRequiredService<IThreadContext>();
		protected INHibernateSessionController _nhibernateSessionController => _serviceProvider.GetRequiredService<INHibernateSessionController>();

		public override IORMEntity ToORMEntity()
		{
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);

			var result = new ORMSlamcoreDevice(); 

			CopyTo(result, session);

			// Ensure no copy of result (instance with same key) in session
			Evict(result, session);

			return result;
		}

		protected void CopyTo(ORMSlamcoreDevice x, NHibernate.ISession session)
		{
			x.Id = Id;
			x.IPAddress = IPAddress?.Truncate(50);
			x.Name = Name?.Truncate(50);
			x.SerialNo = SerialNo?.Truncate(50);
			x.LastConnectedDateTime = LastConnectedDateTime;
			x.Status = (int)Status;
			x.UpdateRate = (int)UpdateRate;
				
			x.SlamcoreAwareAuthenticationDetails = this.SlamcoreAwareAuthenticationDetails != null ? session.Load<ORMSlamcoreAwareAuthenticationDetails>(this.SlamcoreAwareAuthenticationDetails.Id) : (this.SlamcoreAwareAuthenticationDetailsId != null ? session.Load<ORMSlamcoreAwareAuthenticationDetails>(this.SlamcoreAwareAuthenticationDetailsId) : null);
			x.SlamcoreAwareAuthenticationDetailsId = this.SlamcoreAwareAuthenticationDetails != null ? this.SlamcoreAwareAuthenticationDetails.Id : SlamcoreAwareAuthenticationDetailsId; 
				
			x.Vehicle = this.Vehicle != null ? session.Load<ORMVehicle>(this.Vehicle.Id) : (this.VehicleId != null ? session.Load<ORMVehicle>(this.VehicleId) : null);
			x.VehicleId = this.Vehicle != null ? this.Vehicle.Id : VehicleId; 
				
			x.SlamcoreAPIKey = this.SlamcoreAPIKey != null ? session.Load<ORMSlamcoreAPIKey>(this.SlamcoreAPIKey.Id) : (this.SlamcoreAPIKeyId != null ? session.Load<ORMSlamcoreAPIKey>(this.SlamcoreAPIKeyId) : null);
			x.SlamcoreAPIKeyId = this.SlamcoreAPIKey != null ? this.SlamcoreAPIKey.Id : SlamcoreAPIKeyId; 
				
			x.Customer = this.Customer != null ? session.Load<ORMCustomer>(this.Customer.Id) : (this.CustomerId != null ? session.Load<ORMCustomer>(this.CustomerId) : null);
			x.CustomerId = this.Customer != null ? this.Customer.Id : CustomerId; 
		}
 
		private void Evict(ORMSlamcoreDevice result, NHibernate.ISession session)
		{
			foreach (var entity in session.GetSessionImplementation().PersistenceContext.EntitiesByKey.Values.ToArray())
			{
				var evictee = entity as ORMSlamcoreDevice;

				if (evictee != null && evictee.Id == result.Id)
				{
					session.Evict(evictee);
				}
			}
		}

	}
}