﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMGOUser" 
		table="[GOUser]" 
		schema="[GOSecurity]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="AllowedDepartmentNames" >
            <column name="`AllowedDepartmentNames`" sql-type="nvarchar (1000) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="Blocked" >
            <column name="`Blocked`" sql-type="bit" not-null="true" />
        </property> 
		<property name="DealerAdmin" >
            <column name="`DealerAdmin`" sql-type="bit" not-null="false" />
        </property> 
		<property name="EmailAddress" >
            <column name="`EmailAddress`" sql-type="nvarchar (150) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 
		<property name="EmailChangeValidationInProgress" >
            <column name="`EmailChangeValidationInProgress`" sql-type="bit" not-null="true" />
        </property> 
		<property name="EmailValidated" >
            <column name="`EmailValidated`" sql-type="bit" not-null="true" />
        </property> 
		<property name="ExternalUserId" >
            <column name="`ExternalUserId`" sql-type="nvarchar (500) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="FirstName" >
            <column name="`FirstName`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="FullName" >
            <column name="`FullName`" sql-type="nvarchar (250) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="GORoleName" >
            <column name="`GORoleName`" sql-type="nvarchar (200) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="LastName" >
            <column name="`LastName`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="NewEmailAddress" >
            <column name="`NewEmailAddress`" sql-type="nvarchar (150) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="NewEmailValidated" >
            <column name="`NewEmailValidated`" sql-type="bit" not-null="false" />
        </property> 
		<property name="Password" >
            <column name="`Password`" sql-type="nvarchar (150) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 
		<property name="PasswordExpiry" >
            <column name="`PasswordExpiry`" sql-type="datetime" not-null="false" />
        </property> 
		<property name="PreferredLocale" >
            <column name="`PreferredLocale`" sql-type="int" not-null="false" />
        </property> 
		<property name="PreferredLocaleString" >
            <column name="`PreferredLocaleString`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="Unregistered" >
            <column name="`Unregistered`" sql-type="bit" not-null="true" />
        </property> 
		<property name="UserName" >
            <column name="`UserName`" sql-type="nvarchar (150) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 
		<property name="UserValidated" >
            <column name="`UserValidated`" sql-type="bit" not-null="true" />
        </property> 
		<property name="WebsiteAccessLevel" >
            <column name="`WebsiteAccessLevel`" sql-type="int" not-null="true" />
        </property> 
		<property name="WebsiteAccessLevelValue" >
            <column name="`WebsiteAccessLevelValue`" sql-type="smallint" not-null="false" />
        </property> 

		
		<!-- many-to-one Dealer -->
		<property name="DealerId" type="System.Guid" not-null="false" formula = "[DealerId]"></property>  
		<many-to-one name="Dealer"  > 
			<column name="`DealerId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 
 

		<one-to-one 
			name="Person" 
			class="ORMPerson" 
			lazy="no-proxy"
			property-ref = "GOUser"
		>
		</one-to-one>
		<one-to-one 
			name="DealerDriver" 
			class="ORMDealerDriver" 
			lazy="no-proxy"
			property-ref = "GOUser"
		>
		</one-to-one>
		<one-to-one 
			name="Tag" 
			class="ORMTag" 
			lazy="no-proxy"
			property-ref = "GOUser"
		>
		</one-to-one>


		<bag
			name = "ReportSubscriptionItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`GOUserId`" />
			</key>
			<one-to-many class = "ORMReportSubscription" />
		</bag>
		<bag
			name = "UserGroupItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`GOUserId`" />
			</key>
			<one-to-many class = "ORMGOUserGroup" />
		</bag>
		<bag
			name = "RevisionItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`fkGOUserId`" />
			</key>
			<one-to-many class = "ORMRevision" />
		</bag>
		<bag
			name = "GOUserDepartmentItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`GOUserId`" />
			</key>
			<one-to-many class = "ORMGOUserDepartment" />
		</bag>
		<bag
			name = "CustomerAuditItemsDeleted"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`DeletedBy`" />
			</key>
			<one-to-many class = "ORMCustomerAudit" />
		</bag>
		<bag
			name = "CustomerAuditItemsModified"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`LastModifiedBy`" />
			</key>
			<one-to-many class = "ORMCustomerAudit" />
		</bag>
		<bag
			name = "GoUserToCustomerItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`GOUserId`" />
			</key>
			<one-to-many class = "ORMGoUserToCustomer" />
		</bag>
		<bag
			name = "VehicleLockoutItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`GOUserId`" />
			</key>
			<one-to-many class = "ORMVehicleLockout" />
		</bag>
		<bag
			name = "MessageHistoryItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`GOUserId`" />
			</key>
			<one-to-many class = "ORMMessageHistory" />
		</bag>
		<bag
			name = "UserRoleItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`GOUserId`" />
			</key>
			<one-to-many class = "ORMGOUserRole" />
		</bag>
		<bag
			name = "ExportJobStatusItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`GOUserId`" />
			</key>
			<one-to-many class = "ORMExportJobStatus" />
		</bag>
		<bag
			name = "AlertSubscriptionItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`GOUserId`" />
			</key>
			<one-to-many class = "ORMAlertSubscription" />
		</bag>
		<bag
			name = "CustomerAuditItemsCreated"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CreatedBy`" />
			</key>
			<one-to-many class = "ORMCustomerAudit" />
		</bag>

    </class> 

</hibernate-mapping>