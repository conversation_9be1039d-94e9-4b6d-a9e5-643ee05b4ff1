﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping 
	xmlns="urn:nhibernate-mapping-2.2" 
    namespace="FleetXQ.Data.DataObjects" 
	assembly="FleetXQ.Data.DataObjects" 
	default-lazy="true" 
>
	<class 
		name="ORMCustomer" 
		table="[Customer]" 
		schema="[dbo]"
	>


			
		<id name="Id">
            <column name="`Id`" sql-type="uniqueidentifier" not-null="true" />
            <generator class="assigned" />
        </id>

		<property name="Active" >
            <column name="`Active`" sql-type="bit" not-null="true" />
        </property> 
		<property name="Addess" >
            <column name="`Addess`" sql-type="nvarchar (250) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="CompanyName" >
            <column name="`CompanyName`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="true" />
        </property> 
		<property name="ConnectionString" >
            <column name="`ConnectionString`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="ContactNumber" >
            <column name="`ContactNumber`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="ContractDate" >
            <column name="`ContractDate`" sql-type="datetime" not-null="false" />
        </property> 
		<property name="ContractNumber" >
            <column name="`ContractNumber`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="CustomerLogo" >
            <column name="`CustomerLogo`" sql-type="nvarchar(1000)" not-null="false" />
        </property> 
		<property name="CustomerLogoFileSize" >
            <column name="`CustomerLogoFileSize`" sql-type="int" not-null="false" />
        </property> 
		<property name="CustomerLogoInternalName" >
            <column name="`CustomerLogoInternalName`" sql-type="nvarchar (100) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="DealerCustomer" >
            <column name="`DealerCustomer`" sql-type="bit" not-null="false" />
        </property> 
		<property name="DeletedAtUtc" >
            <column name="`DeletedAtUtc`" sql-type="datetime" not-null="false" />
        </property> 
		<property name="Description" >
            <column name="`Description`" sql-type="nvarchar (500) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="Email" >
            <column name="`Email`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="PreferredLocale" >
            <column name="`PreferredLocale`" sql-type="int" not-null="false" />
        </property> 
		<property name="PreferredLocaleString" >
            <column name="`PreferredLocaleString`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 
		<property name="Prefix" >
            <column name="`Prefix`" sql-type="nvarchar (50) COLLATE SQL_Latin1_General_CP1_CI_AS" not-null="false" />
        </property> 

		
		<!-- many-to-one Country -->
		<property name="CountryId" type="System.Guid" not-null="true" formula = "[CountryId]"></property>  
		<many-to-one name="Country"  > 
			<column name="`CountryId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
		
		<!-- many-to-one Dealer -->
		<property name="DealerId" type="System.Guid" not-null="true" formula = "[DealerId]"></property>  
		<many-to-one name="Dealer"  > 
			<column name="`DealerId`" sql-type="uniqueidentifier" not-null="true" />
		</many-to-one> 
 

		<!-- one-to-one CustomerAudit primary key association is mapped as a many-to-one to avoid eager fetch (see code template for more info) -->
		<many-to-one
			name="CustomerAudit" 
			class="ORMCustomerAudit" 
			column="Id" 
			unique="true"
			update="false"
			insert="false"
		>
		</many-to-one>

		<property name="ContactPersonInformationId" type="System.Guid" not-null = "false" formula = "[ContactPersonInformationId]"></property>
 
		<!-- Customer.ContactPersonInformation one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="ContactPersonInformation" unique="true"  > 
				<column name="`ContactPersonInformationId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 

		<property name="CustomerFeatureSubscriptionId" type="System.Guid" not-null = "false" formula = "[CustomerFeatureSubscriptionId]"></property>
 
		<!-- Customer.CustomerFeatureSubscription one-to-one relation is mapped as a 'many-to-one with unique' from the FK side-->
		<!-- See https://nhibernate.info/doc/nhibernate-reference/mapping.html, section 5.1.12 one-to-one 'unique foreign key association' -->
		<many-to-one name="CustomerFeatureSubscription" unique="true"  > 
				<column name="`CustomerFeatureSubscriptionId`" sql-type="uniqueidentifier" not-null="false" />
		</many-to-one> 


		<bag
			name = "DriverItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMDriver" />
		</bag>
		<bag
			name = "CurrentDriverStatusChartViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMCurrentDriverStatusChartView" />
		</bag>
		<bag
			name = "CustomerPreOperationalChecklistTemplateItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMCustomerPreOperationalChecklistTemplate" />
		</bag>
		<bag
			name = "CustomerToPersonViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMCustomerToPersonView" />
		</bag>
		<bag
			name = "CustomerSnapshotItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`fkCustomerId`" />
			</key>
			<one-to-many class = "ORMCustomerSnapshot" />
		</bag>
		<bag
			name = "AccessGroupItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMAccessGroup" />
		</bag>
		<bag
			name = "CustomerSSODetailItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMCustomerSSODetail" />
		</bag>
		<bag
			name = "DashboardCardViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMDashboardDriverCardView" />
		</bag>
		<bag
			name = "DashboardFilterItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMDashboardFilter" />
		</bag>
		<bag
			name = "VehicleItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMVehicle" />
		</bag>
		<bag
			name = "Sites"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMSite" />
		</bag>
		<bag
			name = "EmailGroupsItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMEmailGroups" />
		</bag>
		<bag
			name = "CustomerModelItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMCustomerModel" />
		</bag>
		<bag
			name = "CustomerToModelItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMCustomerToModel" />
		</bag>
		<bag
			name = "DepartmentItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMDepartment" />
		</bag>
		<bag
			name = "DashboardVehicleCardViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMDashboardVehicleCardView" />
		</bag>
		<bag
			name = "GoUserToCustomerItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMGoUserToCustomer" />
		</bag>
		<bag
			name = "CurrentVehicleStatusChartViewItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMCurrentVehicleStatusChartView" />
		</bag>
		<bag
			name = "PersonItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMPerson" />
		</bag>
		<bag
			name = "SlamcoreDeviceItems"
			inverse="true" 
			batch-size="15"
		>
			<key>
				<column name="`CustomerId`" />
			</key>
			<one-to-many class = "ORMSlamcoreDevice" />
		</bag>

    </class> 

</hibernate-mapping>