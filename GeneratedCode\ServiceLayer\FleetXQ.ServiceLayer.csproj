﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
  <PropertyGroup>
    <VersionPrefix>1.0.0.6765</VersionPrefix>
    <VersionSuffix></VersionSuffix>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Service Layer</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices" Version="2.0.3" />
    <PackageReference Include="GenerativeObjects.Services" Version="2.0.3" />
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" Version="2.0.3" />
    <PackageReference Include="GenerativeObjects.Infrastructure.Database.Azure" Version="2.0.3" />
    <PackageReference Include="GenerativeObjects.Infrastructure.Database.SqlServer" Version="2.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.5.0" />
    <PackageReference Include="I18Next.Net" Version="1.0.0" />
    <PackageReference Include="I18Next.Net.Extensions" Version="1.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.5" />

  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\CustomCode\ServiceLayer\FleetXQ.ServiceLayer.Custom.csproj" />
    <ProjectReference Include="..\..\CustomCode\DataLayerDataProviders\FleetXQ.Data.DataProviders.Custom.csproj" />
    <ProjectReference Include="..\DataLayer\FleetXQ.Data.DataObjects.csproj" />
    <ProjectReference Include="..\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj" />
    <ProjectReference Include="..\BusinessLayer\FleetXQ.BusinessLayer.csproj" />
    <ProjectReference Include="..\..\CustomCode\BusinessLayerClientComponents\FleetXQ.BusinessLayer.Components.Client.Custom.csproj" />
    <ProjectReference Include="..\..\CustomCode\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.Custom.csproj" />
    <ProjectReference Include="..\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.csproj" />
    <ProjectReference Include="..\BusinessLayerServerComponentsExtensions\FleetXQ.BusinessLayer.Components.Server.Extensions.csproj" />
    <ProjectReference Include="..\BusinessLayerServerComponentsExtensionsInterfaces\FleetXQ.BusinessLayer.Components.Server.Extensions.Interfaces.csproj" />
    <ProjectReference Include="..\DataLayerDeleteHandlers\FleetXQ.Data.DeleteHandlers.csproj" />
    <ProjectReference Include="..\DataLayerDataProviders\FleetXQ.Data.DataProviders.csproj" />
    <ProjectReference Include="..\Features\Security\Common\FleetXQ.Features.Security.Common.csproj" />
    <ProjectReference Include="..\Features\Security\DataLayer\FleetXQ.Features.Security.DataLayer.csproj" />
    <ProjectReference Include="..\Features\Security\BusinessLayer\FleetXQ.Features.Security.BusinessLayer.csproj" />
    <ProjectReference Include="..\Features\Security\ServiceLayer\FleetXQ.Features.Security.ServiceLayer.csproj" />
  </ItemGroup>
</Project>

