﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Data.DataProviders.Dispatchers
{
    public class SlamcoreDeviceFilterDataProviderDispatcher : IDataProviderDispatcher<SlamcoreDeviceFilterDataObject>
    {
		protected IServiceProvider _serviceProvider;
		
		public SlamcoreDeviceFilterDataProviderDispatcher(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
		
		protected IDataProvider<DepartmentDataObject> departmentDataProvider => _serviceProvider.GetService<IDataProvider<DepartmentDataObject>>();
		protected IDataProvider<SiteDataObject> siteDataProvider => _serviceProvider.GetService<IDataProvider<SiteDataObject>>();
		protected IDataProvider<CustomerDataObject> customerDataProvider => _serviceProvider.GetService<IDataProvider<CustomerDataObject>>();

        public async Task DispatchForEntityAsync(SlamcoreDeviceFilterDataObject entity, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("SlamcoreDeviceFilter", parameters);

            foreach (var include in includes)
            {
	
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
                  case "department":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDeviceFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Department"))
									break;

								try
								{
									var objectToFetch = await departmentDataProvider.GetAsync(new DepartmentDataObject(entity.DepartmentId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "site":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDeviceFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Site"))
									break;

								try
								{
									var objectToFetch = await siteDataProvider.GetAsync(new SiteDataObject(entity.SiteId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "customer":
							{
								// custom code can implement IPrefetch<ORMSlamcoreDeviceFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Customer"))
									break;

								try
								{
									var objectToFetch = await customerDataProvider.GetAsync(new CustomerDataObject(entity.CustomerId), null, subincludes, context, parameters, skipSecurity);
									if(objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
						default:
							throw new ApplicationException("SlamcoreDeviceFilter Entity has no relation named " + relation);
					}
          }        
		}

        public async Task DispatchForEntityCollectionAsync(IEnumerable<SlamcoreDeviceFilterDataObject> entities, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("SlamcoreDeviceFilter", parameters);

            foreach (var include in includes)
            {
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
						case "department":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDeviceFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Department"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.DepartmentId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await departmentDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "site":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDeviceFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Site"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.SiteId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await siteDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customer":
                        {
							// custom code can implement IPrefetch<ORMSlamcoreDeviceFilter> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Customer"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.CustomerId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
                  default:
                        throw new ApplicationException("SlamcoreDeviceFilter Entity has no relation named " + relation);
					}
            }        
        }
	}
}