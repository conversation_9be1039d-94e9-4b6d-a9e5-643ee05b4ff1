﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Text;
using System.IO;
using System.Collections.Generic;
using System.Net;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Http;
using System.Threading.Tasks;

using GenerativeObjects.Practices;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;	
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Serialization;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;
using GenerativeObjects.Practices.Settings;
using FleetXQ.Data.DataObjects;
using Newtonsoft.Json;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace FleetXQ.Client.Model.Components
{
    public partial class VehicleAccessUtilitiesService : BaseService, IVehicleAccessUtilitiesService
    {
        /// <summary>
        /// Constructor, with dependencies injection
        /// </summary>
        /// <param name="provider">The injected service provide</param>
        /// <param name="configuration">The injected configuration</param>
        /// <param name="threadContext">The injected thread context</param>
        public VehicleAccessUtilitiesService(IServiceProvider provider, IConfiguration configuration, IThreadContext threadContext) : base(provider, configuration, threadContext)
        {
            _endpointName = "vehicleaccessutilities";
        }

        /// <summary>
        /// CreateOnDemandAccessesAsync component operation
        /// 
        /// </summary>
        /// <param name="cardIds"></param>
        /// <param name="vehicleId"></param>
        /// <returns>PerVehicleNormalCardAccess Collection</returns>
        public async Task<DataObjectCollection<PerVehicleNormalCardAccessDataObject>> CreateOnDemandAccessesAsync(System.Guid[] cardIds, System.Guid vehicleId)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string cardIdsAsJson_local = JsonConvert.SerializeObject(cardIds, _jsonSerializerSettings);

                formData_local.Add("cardIds", cardIdsAsJson_local);
                string vehicleIdAsJson_local = JsonConvert.SerializeObject(vehicleId, _jsonSerializerSettings);

                formData_local.Add("vehicleId", vehicleIdAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}createondemandaccesses";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var container_local = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessCollectionContainer>();
                    JsonConvert.PopulateObject(responseContent_local, container_local, _jsonSerializerSettings);

                    container_local.ObjectsDataSet.EnsureInitialized();
                    container_local.ObjectsDataSet.ReconstructIndexes();
 
                    return container_local.ExtractPerVehicleNormalCardAccessItems();  
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// DeleteOnDemandAccessAsync component operation
        /// 
        /// </summary>
        /// <param name="accessId"></param>
        /// <returns>bool</returns>
        public async Task<System.Boolean> DeleteOnDemandAccessAsync(System.Guid accessId)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string accessIdAsJson_local = JsonConvert.SerializeObject(accessId, _jsonSerializerSettings);

                formData_local.Add("accessId", accessIdAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}deleteondemandaccess";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var result_local = JsonConvert.DeserializeObject<System.Boolean>(responseContent_local);
                    return result_local;
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// UpdateVehicleModelAccessesForPersonAsync component operation
        /// Update the model vehicle accesses for a person. 
        /// </summary>
        /// <param name="updateModelAccesses"></param>
        /// <param name="personId"></param>
        /// <returns>bool</returns>
        public async Task<System.Boolean> UpdateVehicleModelAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updateModelAccesses)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string updateModelAccessesAsJson_local = JsonConvert.SerializeObject(updateModelAccesses, _jsonSerializerSettings);

                formData_local.Add("updateModelAccesses", updateModelAccessesAsJson_local);
                string personIdAsJson_local = JsonConvert.SerializeObject(personId, _jsonSerializerSettings);

                formData_local.Add("personId", personIdAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}updatevehiclemodelaccessesforperson";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var result_local = JsonConvert.DeserializeObject<System.Boolean>(responseContent_local);
                    return result_local;
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// CopyUserVehicleAccessAsync component operation
        /// Copy User Vehicle Access
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="driverIds">driverIds</param>
        /// <returns>bool</returns>
        public async Task<System.Boolean> CopyUserVehicleAccessAsync(System.Guid personId, System.Guid[] driverIds)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string personIdAsJson_local = JsonConvert.SerializeObject(personId, _jsonSerializerSettings);

                formData_local.Add("personId", personIdAsJson_local);
                string driverIdsAsJson_local = JsonConvert.SerializeObject(driverIds, _jsonSerializerSettings);

                formData_local.Add("driverIds", driverIdsAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}copyuservehicleaccess";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var result_local = JsonConvert.DeserializeObject<System.Boolean>(responseContent_local);
                    return result_local;
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// UpdateVehicleDepartmentAccessesForPersonAsync component operation
        /// Update the department vehicle accesses for a person. 
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="updatedPersonToDepartmentAccesses"></param>
        /// <returns>bool</returns>
        public async Task<System.Boolean> UpdateVehicleDepartmentAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updatedPersonToDepartmentAccesses)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string personIdAsJson_local = JsonConvert.SerializeObject(personId, _jsonSerializerSettings);

                formData_local.Add("personId", personIdAsJson_local);
                string updatedPersonToDepartmentAccessesAsJson_local = JsonConvert.SerializeObject(updatedPersonToDepartmentAccesses, _jsonSerializerSettings);

                formData_local.Add("updatedPersonToDepartmentAccesses", updatedPersonToDepartmentAccessesAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}updatevehicledepartmentaccessesforperson";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var result_local = JsonConvert.DeserializeObject<System.Boolean>(responseContent_local);
                    return result_local;
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// GetAccessesForDepartmentsAsync component operation
        /// 
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="personToSiteAccesses"></param>
        /// <param name="permissionLevel"></param>
        /// <returns>PersonToDepartmentVehicleNormalAccessView Collection</returns>
        public async Task<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>> GetAccessesForDepartmentsAsync(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses, System.Guid personId, System.Int32 permissionLevel)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string personIdAsJson_local = JsonConvert.SerializeObject(personId, _jsonSerializerSettings);

                formData_local.Add("personId", personIdAsJson_local);
                string personToSiteAccessesAsJson_local = JsonConvert.SerializeObject(personToSiteAccesses, _jsonSerializerSettings);

                formData_local.Add("personToSiteAccesses", personToSiteAccessesAsJson_local);
                string permissionLevelAsJson_local = JsonConvert.SerializeObject(permissionLevel, _jsonSerializerSettings);

                formData_local.Add("permissionLevel", permissionLevelAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}getaccessesfordepartments";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var container_local = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewCollectionContainer>();
                    JsonConvert.PopulateObject(responseContent_local, container_local, _jsonSerializerSettings);

                    container_local.ObjectsDataSet.EnsureInitialized();
                    container_local.ObjectsDataSet.ReconstructIndexes();
 
                    return container_local.ExtractPersonToDepartmentVehicleNormalAccessViewItems();  
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// UpdateVehicleSiteAccessesForPersonAsync component operation
        /// Update the sites vehicle accesses for a person. 
        /// </summary>
        /// <param name="updatedPersonToSiteAccesses"></param>
        /// <param name="personId"></param>
        /// <returns>bool</returns>
        public async Task<System.Boolean> UpdateVehicleSiteAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updatedPersonToSiteAccesses)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string updatedPersonToSiteAccessesAsJson_local = JsonConvert.SerializeObject(updatedPersonToSiteAccesses, _jsonSerializerSettings);

                formData_local.Add("updatedPersonToSiteAccesses", updatedPersonToSiteAccessesAsJson_local);
                string personIdAsJson_local = JsonConvert.SerializeObject(personId, _jsonSerializerSettings);

                formData_local.Add("personId", personIdAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}updatevehiclesiteaccessesforperson";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var result_local = JsonConvert.DeserializeObject<System.Boolean>(responseContent_local);
                    return result_local;
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// GetAccessesForVehiclesAsync component operation
        /// 
        /// </summary>
        /// <param name="personToModelAccesses"></param>
        /// <param name="personToDepartmentAccesses"></param>
        /// <param name="personId"></param>
        /// <param name="permissionLevel"></param>
        /// <returns>PersonToPerVehicleNormalAccessView Collection</returns>
        public async Task<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>> GetAccessesForVehiclesAsync(DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses, System.Guid personId, System.Int32 permissionLevel)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string personToModelAccessesAsJson_local = JsonConvert.SerializeObject(personToModelAccesses, _jsonSerializerSettings);

                formData_local.Add("personToModelAccesses", personToModelAccessesAsJson_local);
                string personToDepartmentAccessesAsJson_local = JsonConvert.SerializeObject(personToDepartmentAccesses, _jsonSerializerSettings);

                formData_local.Add("personToDepartmentAccesses", personToDepartmentAccessesAsJson_local);
                string personIdAsJson_local = JsonConvert.SerializeObject(personId, _jsonSerializerSettings);

                formData_local.Add("personId", personIdAsJson_local);
                string permissionLevelAsJson_local = JsonConvert.SerializeObject(permissionLevel, _jsonSerializerSettings);

                formData_local.Add("permissionLevel", permissionLevelAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}getaccessesforvehicles";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var container_local = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewCollectionContainer>();
                    JsonConvert.PopulateObject(responseContent_local, container_local, _jsonSerializerSettings);

                    container_local.ObjectsDataSet.EnsureInitialized();
                    container_local.ObjectsDataSet.ReconstructIndexes();
 
                    return container_local.ExtractPersonToPerVehicleNormalAccessViewItems();  
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// UpdateVehiclePerVehicleAccessesForPersonAsync component operation
        /// Update the per vehicle accesses for a person.
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="updatePerVehicleAccesses"></param>
        /// <returns>bool</returns>
        public async Task<System.Boolean> UpdateVehiclePerVehicleAccessesForPersonAsync(System.Guid personId, DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updatePerVehicleAccesses)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string personIdAsJson_local = JsonConvert.SerializeObject(personId, _jsonSerializerSettings);

                formData_local.Add("personId", personIdAsJson_local);
                string updatePerVehicleAccessesAsJson_local = JsonConvert.SerializeObject(updatePerVehicleAccesses, _jsonSerializerSettings);

                formData_local.Add("updatePerVehicleAccesses", updatePerVehicleAccessesAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}updatevehiclepervehicleaccessesforperson";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var result_local = JsonConvert.DeserializeObject<System.Boolean>(responseContent_local);
                    return result_local;
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// GetAccessesForModelsAsync component operation
        /// 
        /// </summary>
        /// <param name="personToDepartmentAccesses"></param>
        /// <param name="personId"></param>
        /// <param name="permissionLevel"></param>
        /// <returns>PersonToModelVehicleNormalAccessView Collection</returns>
        public async Task<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>> GetAccessesForModelsAsync(DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, System.Guid personId, System.Int32 permissionLevel)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string personToDepartmentAccessesAsJson_local = JsonConvert.SerializeObject(personToDepartmentAccesses, _jsonSerializerSettings);

                formData_local.Add("personToDepartmentAccesses", personToDepartmentAccessesAsJson_local);
                string personIdAsJson_local = JsonConvert.SerializeObject(personId, _jsonSerializerSettings);

                formData_local.Add("personId", personIdAsJson_local);
                string permissionLevelAsJson_local = JsonConvert.SerializeObject(permissionLevel, _jsonSerializerSettings);

                formData_local.Add("permissionLevel", permissionLevelAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}getaccessesformodels";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var container_local = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewCollectionContainer>();
                    JsonConvert.PopulateObject(responseContent_local, container_local, _jsonSerializerSettings);

                    container_local.ObjectsDataSet.EnsureInitialized();
                    container_local.ObjectsDataSet.ReconstructIndexes();
 
                    return container_local.ExtractPersonToModelVehicleNormalAccessViewItems();  
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
        /// <summary>
        /// UpdateAccessesForPersonAsync component operation
        /// 
        /// </summary>
        /// <param name="PermissionLevel"></param>
        /// <param name="personToSiteAccesses"></param>
        /// <param name="personId"></param>
        /// <param name="personToModelAccesses"></param>
        /// <param name="cascadeAddPermission">true for cascading add permissions. For example : if I add permission to department, cascading will add permission for all models and vehicles in the departments added</param>
        /// <param name="personToDepartmentAccesses"></param>
        /// <param name="personToVehicleAccesses"></param>
        /// <returns>bool</returns>
        public async Task<System.Boolean> UpdateAccessesForPersonAsync(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses, DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses, DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses, System.Guid personId, System.Int32 PermissionLevel, System.Boolean cascadeAddPermission)
        {
			// Create a handler that will store cookies
            var handler_local = new HttpClientHandler
            {
                UseCookies = true,  // Ensure cookies are used
                CookieContainer = new CookieContainer()  // Store cookies in a container
            };

            using (var httpClient_local = new HttpClient(handler_local))
            {
                        
                // Prepare the form data
                var formData_local = new Dictionary<string, string>();

                string PermissionLevelAsJson_local = JsonConvert.SerializeObject(PermissionLevel, _jsonSerializerSettings);

                formData_local.Add("PermissionLevel", PermissionLevelAsJson_local);
                string personToSiteAccessesAsJson_local = JsonConvert.SerializeObject(personToSiteAccesses, _jsonSerializerSettings);

                formData_local.Add("personToSiteAccesses", personToSiteAccessesAsJson_local);
                string personIdAsJson_local = JsonConvert.SerializeObject(personId, _jsonSerializerSettings);

                formData_local.Add("personId", personIdAsJson_local);
                string personToModelAccessesAsJson_local = JsonConvert.SerializeObject(personToModelAccesses, _jsonSerializerSettings);

                formData_local.Add("personToModelAccesses", personToModelAccessesAsJson_local);
                string cascadeAddPermissionAsJson_local = JsonConvert.SerializeObject(cascadeAddPermission, _jsonSerializerSettings);

                formData_local.Add("cascadeAddPermission", cascadeAddPermissionAsJson_local);
                string personToDepartmentAccessesAsJson_local = JsonConvert.SerializeObject(personToDepartmentAccesses, _jsonSerializerSettings);

                formData_local.Add("personToDepartmentAccesses", personToDepartmentAccessesAsJson_local);
                string personToVehicleAccessesAsJson_local = JsonConvert.SerializeObject(personToVehicleAccesses, _jsonSerializerSettings);

                formData_local.Add("personToVehicleAccesses", personToVehicleAccessesAsJson_local);
                
                var queryStringParams_local = new List<string>();

                
                // get user token for currently authenticated user if any                    
                var userToken_local = _threadContext.UserToken;
                var applicationToken_local = _threadContext.ApplicationToken;

                if (!String.IsNullOrEmpty(userToken_local))
                {
                    queryStringParams_local.Add($"_user_token={userToken_local}");
                }

                if (!String.IsNullOrEmpty(applicationToken_local))
                {
                    queryStringParams_local.Add($"_application_token={applicationToken_local}");
                }

                if (!String.IsNullOrEmpty(_threadContext.DbKey))
                {
                    queryStringParams_local.Add($"dbKey={_threadContext.DbKey}");
                }

                var queryUrl_local = $"{_serviceUrl}updateaccessesforperson";
                if (queryStringParams_local.Any())
                {
                    queryUrl_local += "?" + String.Join("&", queryStringParams_local);
                }
                
                // Make the POST request to the API endpoint
                           
                var formContent_local = new FormUrlEncodedContent(formData_local);
                var response_local = await httpClient_local.PostAsync(queryUrl_local, formContent_local);

                // Check if the request was successful (status code 200)
                if (response_local.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent_local = await response_local.Content.ReadAsStringAsync();

                    var result_local = JsonConvert.DeserializeObject<System.Boolean>(responseContent_local);
                    return result_local;
                }
                else
                {
					// Read the error response content for more details
					string errorContent_local = await response_local.Content.ReadAsStringAsync();
                    throw new GOServerException($"API request failed with status code: {response_local.StatusCode}. Error details: {errorContent_local}");
                }
            }
        }
    }
}
