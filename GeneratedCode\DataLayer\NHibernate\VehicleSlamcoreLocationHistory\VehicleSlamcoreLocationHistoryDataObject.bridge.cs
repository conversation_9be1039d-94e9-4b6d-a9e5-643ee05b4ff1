﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;

namespace FleetXQ.Data.DataObjects
{
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	// Bridge DataObject -> ORMEntity
	//
	////////////////////////////////////////////////////////////////////////////////////////////////////////
	public partial class VehicleSlamcoreLocationHistoryDataObject
	{
		protected IThreadContext _threadContext => _serviceProvider.GetRequiredService<IThreadContext>();
		protected INHibernateSessionController _nhibernateSessionController => _serviceProvider.GetRequiredService<INHibernateSessionController>();

		public override IORMEntity ToORMEntity()
		{
			var session = _nhibernateSessionController.GetCurrentSession(_threadContext);

			var result = new ORMVehicleSlamcoreLocationHistory(); 

			CopyTo(result, session);

			// Ensure no copy of result (instance with same key) in session
			Evict(result, session);

			return result;
		}

		protected void CopyTo(ORMVehicleSlamcoreLocationHistory x, NHibernate.ISession session)
		{
			x.Id = Id;
			x.Speed = Speed;
			x.Bearing = Bearing;
			x.YPosition = YPosition;
			x.WOrientation = WOrientation;
			x.XOrientation = XOrientation;
			x.TrailSequence = TrailSequence;
			x.XPosition = XPosition;
			x.ZPosition = ZPosition;
			x.YOrientation = YOrientation;
			x.AcquisitionDateTime = AcquisitionDateTime;
			x.Status = (int)Status;
			x.EventType = (int?)EventType;
			x.ReferenceFrameCategory = (int)ReferenceFrameCategory;
				
			x.SlamcoreDevice = this.SlamcoreDevice != null ? session.Load<ORMSlamcoreDevice>(this.SlamcoreDevice.Id) : (this.SlamcoreDeviceId != null ? session.Load<ORMSlamcoreDevice>(this.SlamcoreDeviceId) : null);
			x.SlamcoreDeviceId = this.SlamcoreDevice != null ? this.SlamcoreDevice.Id : SlamcoreDeviceId; 
		}
 
		private void Evict(ORMVehicleSlamcoreLocationHistory result, NHibernate.ISession session)
		{
			foreach (var entity in session.GetSessionImplementation().PersistenceContext.EntitiesByKey.Values.ToArray())
			{
				var evictee = entity as ORMVehicleSlamcoreLocationHistory;

				if (evictee != null && evictee.Id == result.Id)
				{
					session.Evict(evictee);
				}
			}
		}

	}
}