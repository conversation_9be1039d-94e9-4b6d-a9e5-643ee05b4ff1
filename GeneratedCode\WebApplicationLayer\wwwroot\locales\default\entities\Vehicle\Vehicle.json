﻿{
  "entityName": "Vehicle",
  "entityNamePlural": "Vehicles",   "entityDescription": "This entity represents a Vehicle",
  "fields": {
    "Actions": {
        "displayName": "Actions", 
        "description": "Actions"
    },
    "AllVehicleCalibrationStoreProcedureItems": {
        "displayName": "All Vehicle Calibration Store Procedure Items", 
        "description": "All Vehicle Calibration Store Procedure Items"
    },
    "AllVORSessionsPerVehicleStoreProcedureItems": {
        "displayName": "All VOR Sessions Per Vehicle Store Procedure Items", 
        "description": "All VOR Sessions Per Vehicle Store Procedure Items"
    },
    "BroadcastMessageHistoryItems": {
        "displayName": "BroadcastMessageHistory Items", 
        "description": "BroadcastMessageHistory Items"
    },
    "Canrule": {
        "displayName": "Canrule", 
        "description": "Canrule"
    },
    "CanruleId": {
        "displayName": "CanruleId", 
        "description": "Foreign Key"
    },
    "ChecklistFailurePerVechicleViewItems": {
        "displayName": "Checklist failure per vechicle view Items", 
        "description": "Checklist failure per vechicle view Items"
    },
    "ChecklistSettings": {
        "displayName": "Checklist Settings", 
        "description": "Checklist Settings"
    },
    "ChecklistSettingsId": {
        "displayName": "ChecklistSettingsId", 
        "description": "Foreign Key"
    },
    "CurrentStatusVehicleViewItems": {
        "displayName": "Current Status Vehicle View Items", 
        "description": "Current Status Vehicle View Items"
    },
    "Customer": {
        "displayName": "Customer", 
        "description": "Vehicle"
    },
    "CustomerId": {
        "displayName": "CustomerId", 
        "description": "Foreign Key"
    },
    "DehireTime": {
        "displayName": "Dehire Time", 
        "description": "Dehire Time"
    },
    "DeletedAtUtc": {
        "displayName": "DeletedAtUtc", 
        "description": "DeletedAtUtc"
    },
    "Department": {
        "displayName": "Department", 
        "description": "Department"
    },
    "DepartmentAndModel": {
        "displayName": "Department and model", 
        "description": "Department and Model"
    },
    "DepartmentChecklist": {
        "displayName": "Department Checklist", 
        "description": "Department Checklist"
    },
    "DepartmentChecklistId": {
        "displayName": "DepartmentChecklistId", 
        "description": "Foreign Key"
    },
    "DepartmentId": {
        "displayName": "DepartmentId", 
        "description": "Foreign Key"
    },
    "Description": {
        "displayName": "Description", 
        "description": "Description"
    },
    "DetailedSessionViewItems": {
        "displayName": "Detailed Session View Items", 
        "description": "Detailed Session View Items"
    },
    "DetailedVORSessionStoreProcedureItems": {
        "displayName": "Detailed VOR Session Store Procedure Items", 
        "description": "Detailed VOR Session Store Procedure Items"
    },
    "Driver": {
        "displayName": "Driver", 
        "description": "Current Driver"
    },
    "DriverId": {
        "displayName": "DriverId", 
        "description": "Foreign Key"
    },
    "Firmware": {
        "displayName": "Firmware", 
        "description": "Firmware"
    },
    "FirmwareId": {
        "displayName": "FirmwareId", 
        "description": "Foreign Key"
    },
    "GeneralProductivityPerVehicleViewItems": {
        "displayName": "General Productivity Per Vehicle View Items", 
        "description": "General Productivity Per Vehicle View Items"
    },
    "GPSDateTimeDisplay": {
        "displayName": "GPSDateTimeDisplay", 
        "description": "GPSDateTimeDisplay"
    },
    "HireNo": {
        "displayName": "Hire No", 
        "description": "Hire No"
    },
    "HireTime": {
        "displayName": "Hire Time", 
        "description": "Hire Time"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "IDLETimer": {
        "displayName": "IDLE Timer", 
        "description": "IDLE Timer", 
        "validationRules": {
            "9cc0738d-aa3a-4d75-b821-fda971530ada" : {
                    "errorMessage": "Value must be a number for the field IDLE Timer"
            }
        }
    },
    "ImpactLockout": {
        "displayName": "Impact Lockout", 
        "description": "Impact Lockout"
    },
    "ImpactsForVehicleViewItems": {
        "displayName": "Impacts for vehicle view Items", 
        "description": "Impacts for vehicle view Items"
    },
    "Inspection": {
        "displayName": "Inspection", 
        "description": "Inspection"
    },
    "InspectionId": {
        "displayName": "InspectionId", 
        "description": "Foreign Key"
    },
    "IsCanbus": {
        "displayName": "Is Canbus", 
        "description": "Is Canbus"
    },
    "LastSessionDate": {
        "displayName": "Last Session Date", 
        "description": "Last Session Date of this Vehicle"
    },
    "LastSessionDateTzAdjusted": {
        "displayName": "Last Session Date Tz Adjusted", 
        "description": "Last Session Date Tz Adjusted"
    },
    "LastSessionDateTzAdjustedDisplay": {
        "displayName": "LastSessionDateTzAdjustedDisplay", 
        "description": "LastSessionDateTzAdjustedDisplay"
    },
    "LastSessionId": {
        "displayName": "Last Session Id", 
        "description": "Last Session Id"
    },
    "MessageHistoryItems": {
        "displayName": "MessageHistory Items", 
        "description": "MessageHistory"
    },
    "Model": {
        "displayName": "Model", 
        "description": "Model"
    },
    "ModelId": {
        "displayName": "ModelId", 
        "description": "Foreign Key"
    },
    "Module": {
        "displayName": "Module", 
        "description": "Module"
    },
    "ModuleHistoryItems": {
        "displayName": "Module History Items", 
        "description": "Module History Items"
    },
    "ModuleId1": {
        "displayName": "ModuleId1", 
        "description": "Foreign Key"
    },
    "ModuleIsConnected": {
        "displayName": "Module Is Connected", 
        "description": "Module Is Connected"
    },
    "ModuleSwapNote": {
        "displayName": "Module Swap Note", 
        "description": "Swap Note"
    },
    "NetworkSettingsItems": {
        "displayName": "Network Settings Items", 
        "description": "Network Settings"
    },
    "NextHireDehireActionCalculated": {
        "displayName": "Next Hire Dehire Action Calculated", 
        "description": "Shows whether the next action for the vehicle is hire or dehire.\nInverse of the current onHire "
    },
    "OnDemandSessionItems": {
        "displayName": "On Demand Session Items", 
        "description": "On Demand Session"
    },
    "OnDemandSettings": {
        "displayName": "On Demand Settings", 
        "description": "On Demand Settings"
    },
    "OnHire": {
        "displayName": "On Hire", 
        "description": "On Hire"
    },
    "OnHireCalculated": {
        "displayName": "On Hire Calculated", 
        "description": "On Hire Calculated"
    },
    "PedestrianDetectionHistoryItems": {
        "displayName": "Pedestrian Detection History Items", 
        "description": "Pedestrian Detection History Items"
    },
    "Person": {
        "displayName": "Person", 
        "description": "Person"
    },
    "PersonId": {
        "displayName": "PersonId", 
        "description": "Foreign Key"
    },
    "PersonToPerVehicleMasterAccessViewItems": {
        "displayName": "Person to per vehicle master access view Items", 
        "description": "Person to per vehicle master access view Items"
    },
    "PersonToPerVehicleNormalAccessViewItems": {
        "displayName": " Person to per vehicle normal access view Items", 
        "description": " Person to per vehicle normal access view Items"
    },
    "PerVehicleNormalCardAccessItems": {
        "displayName": "Per Vehicle Normal Card Access Items", 
        "description": "Per Vehicle Normal Card Access Items"
    },
    "SerialNo": {
        "displayName": "Serial No", 
        "description": "Serial No"
    },
    "ServiceSettings": {
        "displayName": "Service Settings", 
        "description": "Service Settings"
    },
    "ServiceSettingsId": {
        "displayName": "ServiceSettingsId", 
        "description": "Foreign Key"
    },
    "Sessions": {
        "displayName": "Sessions", 
        "description": "Sessions"
    },
    "Site": {
        "displayName": "Site", 
        "description": "Site"
    },
    "SiteId": {
        "displayName": "SiteId", 
        "description": "Foreign Key"
    },
    "SlamcoreDeviceHistoryItems": {
        "displayName": "SlamcoreDeviceHistory Items", 
        "description": "SlamcoreDeviceHistory"
    },
    "TimeoutEnabled": {
        "displayName": "Timeout Enabled", 
        "description": "Timeout Enabled"
    },
    "UnitUnutilisationStoreProcedureItems": {
        "displayName": "Unit Unutilisation Store Procedure Items", 
        "description": "Unit Unutilisation Store Procedure Items"
    },
    "UnitUtilisationStoreProcedureItems": {
        "displayName": "Unit Utilisation Store Procedure Items", 
        "description": "Unit Utilisation Store Procedure Items"
    },
    "VehicleAlertSubscriptionItems": {
        "displayName": "Vehicle Alert Subscription Items", 
        "description": "Vehicle Alert Subscription"
    },
    "VehicleBroadcastMessageItems": {
        "displayName": "VehicleBroadcastMessage Items", 
        "description": "VehicleBroadcastMessage Items"
    },
    "VehicleCardAccesses": {
        "displayName": "Vehicle Card Accesses", 
        "description": "Vehicle Card Accesses"
    },
    "VehicleDiagnostic": {
        "displayName": "VehicleDiagnostic", 
        "description": "VehicleDiagnostic"
    },
    "VehicleDiagnosticAction": {
        "displayName": "Vehicle Diagnostic Action", 
        "description": "Vehicle Diagnostic Action"
    },
    "VehicleGPSLocations": {
        "displayName": "Vehicle GPS Locations", 
        "description": "Vehicle GPS Locations"
    },
    "VehicleHireDehireHistoryItems": {
        "displayName": "Vehicle Hire Dehire History Items", 
        "description": "Vehicle Hire Dehire History Items"
    },
    "VehicleImage": {
        "displayName": "Vehicle Image", 
        "description": "Vehicle Image"
    },
    "VehicleImageFileSize": {
        "displayName": "Vehicle Image File size (bytes)", 
        "description": "Vehicle Image File size (bytes)", 
        "validationRules": {
            "8c0065ba-7428-4a05-a7f6-144c3b8fe74b" : {
                    "errorMessage": "Value must be a number for the field Vehicle Image File size (bytes)"
            }
        }
    },
    "VehicleImageInternalName": {
        "displayName": "Vehicle Image Internal Name", 
        "description": "Vehicle Image Internal Name"
    },
    "VehicleImageUrl": {
        "displayName": "Vehicle Image Url", 
        "description": "Vehicle Image Url"
    },
    "VehicleLastGPSLocationView": {
        "displayName": "Vehicle Last GPS Location View ", 
        "description": "Vehicle Last GPS Location View "
    },
    "VehicleLockoutItems": {
        "displayName": "Vehicle Lockout Items", 
        "description": "Vehicle Lockout"
    },
    "VehicleModel": {
        "displayName": "VehicleModelNo", 
        "description": "VehicleModel"
    },
    "VehicleOtherSettings": {
        "displayName": "VehicleOtherSettings", 
        "description": "VehicleOtherSettings"
    },
    "VehicleOtherSettingsId": {
        "displayName": "VehicleOtherSettingsId", 
        "description": "Foreign Key"
    },
    "VehicleProficiencyViewItems": {
        "displayName": "Vehicle Proficiency View Items", 
        "description": "Vehicle Proficiency View Items"
    },
    "VehicleSessionlessImpactItems": {
        "displayName": "Vehicle Sessionless Impact Items", 
        "description": "Vehicle Sessionless Impact Items"
    },
    "VehicleSupervisorsViewItems": {
        "displayName": "Vehicle Supervisors View Items", 
        "description": "Access group Items"
    },
    "VehicleTags": {
        "displayName": "Vehicle Tags", 
        "description": "Vehicle Tags"
    },
    "VehicleToPreOpCheckilstItems": {
        "displayName": "Vehicle To Pre Op Checkilst Items", 
        "description": "Vehicle To Pre Op Checkilst Items"
    },
    "VORSettingHistoryItems": {
        "displayName": "VOR Setting History Items", 
        "description": "VOR Setting History Items"
    }
  }
} 