﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using FleetXQ.BusinessLayer.Components.Server;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.IdentityModel.Tokens;

namespace FleetXQ.ServiceLayer.Security
{ 
    public class AccessControlMiddleware
    {
        private readonly RequestDelegate _next;
		private readonly IConfiguration _configuration;

        public AccessControlMiddleware(RequestDelegate next, IConfiguration configuration)
        {
            _next = next;
            _configuration = configuration;
		}

        public async Task Invoke(HttpContext context)
        {
            bool continuePipeline = true;
			var threadContext = context.RequestServices.GetRequiredService<IThreadContext>();
			var authentication = context.RequestServices.GetRequiredService<IAuthentication>();

            if (context.Request.Query["dbKey"] != StringValues.Empty)
            {
                threadContext.DbKey = context.Request.Query["dbKey"];
            }
            else
            {
                if (IsAPIRequest(context))
                {
                    threadContext.DbKey = null;
                    //dbKey null and API THEN default connection string
                }
            }

			(var authenticationToken, var applicationToken, var tokenTransport) = authentication.GetBearerTokens(context);

			threadContext.UserToken = authenticationToken;
            threadContext.ApplicationToken = applicationToken;

			if (authenticationToken == null)        // No user security token 
			{
                continuePipeline = await HandleNullTokenAsync(context);
			}
			else
			{
				// If we get here, there is (well, was) a valid security token at some time prior to the current request
				// Try to Validate the token
				try
				{
					await authentication.ValidateTokenAsync(authenticationToken, applicationToken, tokenTransport);
                    continuePipeline = await HandleValidTokenAsync(context);
                }
				catch (SecurityTokenExpiredException)
				{
                    continuePipeline = await HandleInvalidTokenAsync(context, "expiredSecurityToken");
				}
				catch (Exception)
				{
                    continuePipeline = await HandleInvalidTokenAsync(context, "invalidSecurityToken");
				}
			}

			if (continuePipeline) 
			{
                await _next.Invoke(context);
            }
		}
		
		private	async Task<bool> HandleNullTokenAsync(HttpContext context)
		{
			return await HandleInvalidTokenAsync(context, "nullSecurityToken"); ;
		}

		private async Task<bool> HandleInvalidTokenAsync(HttpContext context, string tokenError)
		{
			// If url is app baseURL, redirect to log-in page
			if (IsAppBaseUrl(context))
			{
				RedirectToLoginPage(context);
				return false;
			}

			// Allow unsecured access to defined targets, e.g. membership
			if (AllowUnauthenticatedAccess(context))
				return true;

			// Allow API calls through (entities and components do their own security)
			if (IsAPIRequest(context))
				return true;

			// If it's a view navigation, try to authorize it for anonymous access
			if (IsViewNavigation(context))
			{
				if (await AuthorizeNavigationAsync(context))
					return true;
				else
				{
					RedirectToLoginPage(context);
					return false;
				}
			}

			// If get here, access denied. Redirect to landing page. TODO (for now just go to login page)
			RedirectToLoginPage(context);   
			return false;
		}

		private async Task<bool> HandleValidTokenAsync(HttpContext context)
		{
			// If it's a view navigation, check access is authorized
			if (IsViewNavigation(context))
			{
				if (!await AuthorizeNavigationAsync(context))
				{ 
					context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                    context.Response.WriteAsync("Access denied. You do not have permission to view this content.").GetAwaiter().GetResult();
					return false;
				}
			}

			return true;
		}
		
		private bool AllowUnauthenticatedAccess(HttpContext context)
		{
            var uri = new System.Uri(context.Request.GetEncodedUrl());
            
			string appPath = context.Request.PathBase + "/";

			string[] anonymousTargets = _configuration["AnonymousAccessFolders"].Split(',');
            if (anonymousTargets == null || anonymousTargets.Length == 0)
            {
				anonymousTargets = new string[]
				{
					// Default unsecured content - for backwards compatability
					"Styles",
					"Membership"
				};
			}

			string url = System.Text.RegularExpressions.Regex.Replace(context.Request.GetEncodedUrl().ToString(), @"/+", @"/").ToLower();
			int portNumber = uri.Port;
			string port = url.Contains($":{portNumber}") ? $":{portNumber}" : "";
			string referrer = context.Request.Headers["Referer"] != StringValues.Empty ? System.Text.RegularExpressions.Regex.Replace(context.Request.Headers["Referer"], @"/+", @"/").ToLower() : null;

			foreach (string t in anonymousTargets)
			{
				string target = ($"{uri.Host}{port}{appPath}{t.Trim(' ')}").ToLower();
				if (url.Contains(target) || (referrer != null && referrer.Contains(target)))
				{
					// No security token required to access this resource, allow the request to proceed
					return true;
				}
			}

			return false;
		}

		private bool IsAPIRequest(HttpContext context)
		{
			return context.Request.GetEncodedUrl().ToString().ToLower().Contains("/api/");
		}

		bool IsViewNavigation(HttpContext context)
		{
			var uri = new System.Uri(context.Request.GetEncodedUrl());
			return uri.AbsolutePath.IndexOf("/ConstructedViews/") != -1;
		}

		private bool IsAppBaseUrl(HttpContext context)
		{
			string appPath = context.Request.PathBase + "/";
			return context.Request.GetEncodedUrl() == appPath || context.Request.GetEncodedUrl() == appPath + "?";
		}

		private string GetViewTarget(HttpContext context)
		{
            var uri = new System.Uri(context.Request.GetEncodedUrl());
            
			return IsViewNavigation(context)
				? System.IO.Path.GetFileNameWithoutExtension(uri.AbsolutePath).Split('.').First()
				: null;
		}
		
		private async Task<bool> AuthorizeNavigationAsync(HttpContext context)
		{
			var authentication = context.RequestServices.GetRequiredService<IAuthentication>();
			string view = GetViewTarget(context);
			if (!String.IsNullOrEmpty(view))
			{
				List<string> nodes = GetNavigationNodeNames(view);
				if (nodes != null)
				{ 
					foreach (string navigationNode in nodes)
					{
						if (!String.IsNullOrEmpty(navigationNode))
						{
							if (await AuthorizeNavigationToAsync(navigationNode, authentication) != PermissionLevel.Authorized)
							{
								return false;
							}
						}
					}
				}
			}

			return true;
		}

		/// <summary>
		/// AuthorizeNavigationTo()
		/// Summary of the permissioning logic:
		/// User is in at least one authorizing role for the resource => authorised
		/// All User roles are denied access to the resource => denied
		/// Else the default access setting for the resource is used. (so e.g. if no rules for this role, or if not all roles explicitly denied, then the default is used)
		/// </summary>
		private async Task<PermissionLevel> AuthorizeNavigationToAsync(System.String nodeName, IAuthentication authentication)
		{
			// Get user claims
			UserClaims claims = await authentication.GetCurrentUserClaimsAsync();

			switch (nodeName)
			{
				case "CountryItems":
				{
					// Roles "DealerAdmin" are denied access to CountryItems 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "Import":
				{
					// Roles "DealerAdmin" are denied access to Import 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "RegionItems":
				{
					// Roles "DealerAdmin" are denied access to RegionItems 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "GO2FAConfiguration":
				{
					// Roles "DealerAdmin" are denied access to GO2FAConfiguration 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "Firmware":
				{
					// Roles "DealerAdmin" are denied access to Firmware 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "Models":
				{
					// Roles "DealerAdmin" are denied access to Models 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "FeatureSubscriptions":
				{
					// Roles "DealerAdmin" are denied access to FeatureSubscriptions 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "CustomerSSO":
				{
					// Roles "DealerAdmin" are denied access to CustomerSSO 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "AdminSettings":
				{
					// Roles "Customer" are denied access to AdminSettings 
					var deniedRoles = new List<string> { "Customer" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "TimezoneItems":
				{
					// Roles "DealerAdmin" are denied access to TimezoneItems 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				case "AccessGroupTemplates":
				{
					// Roles "DealerAdmin" are denied access to AccessGroupTemplates 
					var deniedRoles = new List<string> { "DealerAdmin" };
					int deniedRoleCount = 0;

					foreach (string role in claims.Roles)
					{
						if (deniedRoles.Contains(role))
						{
							++deniedRoleCount;
						}
					}

					// all roles denied?
					if (deniedRoleCount == claims.Roles.Count())
					{
						return PermissionLevel.Denied;
					}

					break;
				}
				default:
					break;
			}

			// If we get here, it means there is no explicit (override) node authorization rule for at least one of the user's roles
			// So check default rules to determine outcome
			return CheckDefaultNavigationNodePermission(claims);
		}

		/// <summary>
		/// CheckDefaultNavigationNodePermission()
		/// </summary>
		private PermissionLevel CheckDefaultNavigationNodePermission(UserClaims claims)
		{
			// Roles ""Customer", "Administrator", "DealerAdmin"" have default authorized access to all navigation nodes (can still later be overridden by more specific rules)
			if (claims.Roles.Intersect(new List<string> { "Customer", "Administrator", "DealerAdmin" }).Any())
			{
                return PermissionLevel.Authorized;
			}
			
			return PermissionLevel.Denied;
		} 

		/// <summary>
		/// Get the navigation nodes.
		/// </summary>
		/// <param name="viewName">The name of the view.</param>
		/// <returns>The list of navigation node names for the view.</returns>
		private List<string> GetNavigationNodeNames(string viewName)
		{
			var result = new List<string>();

			switch (viewName) 
			{
				case "VehicleCalibrationReportPageView":
					result.Add("VehicleCalibrationReport3");
					result.Add("VehicleCalibrationReport");
					break;
				case "UserEmailAlertSummaryReportPageView":
					result.Add("UserEmailAlertSummary2");
					break;
				case "PersonDetailPagePageView":
					result.Add("UserDetail");
					result.Add("NewUser");
					break;
				case "FleetDashboardPageView":
					result.Add("Dashboard");
					break;
				case "CountryItemsPageView":
					result.Add("CountryItems");
					break;
				case "PreOpCheckReportPageView":
					result.Add("PreOpCheckReport");
					break;
				case "DriverAccessAbuseReportPageView":
					result.Add("DriverAccessAbuseReport");
					break;
				case "VORReportPageView":
					result.Add("VORReport");
					break;
				case "CurrentStatusReportPageView":
					result.Add("CurrentStatusReport");
					break;
				case "FloorPlanManagementPageView":
					result.Add("FloorPlanManagement");
					break;
				case "FloorPlanPagePageView":
					result.Add("FloorPlan");
					break;
				case "ImportJobPageView":
					result.Add("Import");
					break;
				case "VehicleDetailsPagePageView":
					result.Add("Vehicle");
					break;
				case "CountryPageView":
					result.Add("CountryDetails");
					break;
				case "HelpPageView":
					result.Add("Help");
					break;
				case "RegionItemsPageView":
					result.Add("RegionItems");
					break;
				case "ServiceCheckReportPageView":
					result.Add("ServiceCheckReport");
					break;
				case "VehilceItemsPageView":
					result.Add("Vehicles");
					break;
				case "GO2FAConfigurationPageView":
					result.Add("GO2FAConfiguration");
					break;
				case "PersonItemsPageView":
					result.Add("UserManagement");
					break;
				case "RegionPageView":
					result.Add("RegionDetails");
					break;
				case "ChecklistResultAnswersPagePageView":
					result.Add("ChecklistResultAnswers");
					break;
				case "AlertReportPageView":
					result.Add("AlertReport");
					break;
				case "DealerPageView":
					result.Add("DealerDetails");
					break;
				case "DataExportPagePageView":
					result.Add("DataExportTool");
					break;
				case "PedestrianDetectionReportPageView":
					result.Add("PedestrianDetectionReport");
					break;
				case "FirmwaresPagePageView":
					result.Add("Firmware");
					break;
				case "VehicleHireDehireReportPageView":
					result.Add("VehicleHireDehireReport");
					break;
				case "ReportSchedulerPageView":
					result.Add("ReportScheduler");
					break;
				case "ModelItemsPageView":
					result.Add("Models");
					break;
				case "FeatureSubscriptionsPageView":
					result.Add("FeatureSubscriptions");
					break;
				case "MyaccountPageView":
					result.Add("MyAccount");
					break;
				case "MachineUnlockReportPageView":
					result.Add("MachineUnlockReport");
					break;
				case "CustomerSSOPageView":
					result.Add("CustomerSSO");
					break;
				case "PathAnalysisViewPageView":
					result.Add("PathAnalysisView");
					break;
				case "ConnectionStatusDashboardPageView":
					result.Add("ConnectionStatusDashboard");
					break;
				case "ProficiencyReportPageView":
					result.Add("ProficiencyReport");
					break;
				case "DevicesPageView":
					result.Add("Devices");
					break;
				case "TimezoneItemsPageView":
					result.Add("TimezoneItems");
					break;
				case "AccessGroupTemplatesPageView":
					result.Add("AccessGroupTemplates");
					break;
				case "SynchronizationStatusReportPageView":
					result.Add("SynchronizationStatusReport");
					break;
				case "BroadcastMessageReportPageView":
					result.Add("BroadcastMessageReport");
					break;
				case "CustomerItemsPageView":
					result.Add("Customers");
					break;
				case "ExportPageView":
					result.Add("Export");
					break;
				case "UserSummaryReportPageView":
					result.Add("UserSummaryReport2");
					break;
				case "OnDemandAuthorisationReportPageView":
					result.Add("OnDemandAuthorisationReport");
					break;
				case "LiveMapPageView":
					result.Add("LiveMap");
					break;
				case "DealerItemsPageView":
					result.Add("DealerItems");
					break;
				case "ImpactReportPageView":
					result.Add("ImpactReport");
					break;
				case "SuperAdminPageView":
					result.Add("SuperAdmin");
					break;
				case "LicenseExpiryReportPageView":
					result.Add("LicenseExpiryReport");
					break;
				case "VehiclesGPSReportPageView":
					result.Add("VehiclesGPSReport");
					break;
				case "GeneralProductivityReportPagePageView":
					result.Add("GeneralProductivityReport");
					break;
				case "EmailSubscriptionReportPageView":
					result.Add("EmailSubscriptionReport2");
					break;
				case "CustomerPageView":
					result.Add("CustomerDetails");
					break;
				case "TimezonePageView":
					result.Add("TimezoneDetails");
					break;
				default:
					return null;
			}

			return result;
		}

        /// <summary>
        /// Redirects the user to the login page while preserving the original context.
        /// This method handles:
        /// 1. Constructing the login URL based on the configured authentication virtual path
        /// 2. Preserving the original virtual directory for post-login redirection
        /// 3. Maintaining query parameters (including dbKey) for context preservation
        /// 
        /// The login URL is constructed as:
        /// - If AuthenticationVirtualPath is configured: /{AuthenticationVirtualPath}/Membership/Login.html
        /// - If not configured: {CurrentPathBase}/Membership/Login.html
        /// </summary>
        /// <param name="context">The current HTTP context</param>
		private void RedirectToLoginPage(HttpContext context)
		{
			string loginUrl;
			string baseUrl = null;

            // Get the configured authentication virtual path (e.g. "/go-meta-portal")
			var authenticationVirtualPath = _configuration["AuthenticationVirtualPath"];

            // If no authentication virtual path is configured, use the current application path
            if (String.IsNullOrEmpty(authenticationVirtualPath))
            {
                string appPath = context.Request.PathBase + "/";
                loginUrl = appPath + "Membership/Login.html";
            }
			else
			{
                // Construct the login URL using the configured authentication virtual path
                // This ensures all authentication operations happen in the same virtual directory
                loginUrl = "/" + authenticationVirtualPath.TrimStart('/') + "/Membership/Login.html";
            }

            // Add the original virtual directory as a query parameter
            // This is used by the login page to redirect back to the correct virtual directory after successful login
            loginUrl += (loginUrl.Contains("?") ? "&" : "?") + "originalVirtualDir=" + context.Request.PathBase.ToString().TrimStart('/');

            // Preserve the dbKey parameter if present
            // This maintains the database context across the authentication process
            if (context.Request.Query["dbKey"] != StringValues.Empty)
			{
				loginUrl += "&dbKey=" + context.Request.Query["dbKey"];
			}

            // Preserve all other query parameters except dbKey (which was handled above)
            // This maintains any additional context needed by the application
			var queryWithoutDbkey = context.Request.Query.Where(q => q.Key != "dbKey");

            if (queryWithoutDbkey.Any())
            {
				var querystring = String.Join("&", queryWithoutDbkey.Select(q => $"{q.Key}={q.Value}"));	
				loginUrl += context.Request.Query["dbKey"] != StringValues.Empty ? "&"	+ querystring : "?" + querystring;
			}
			
            // Perform the redirect to the login page
			context.Response.Redirect(loginUrl);
		}
    }
}