﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'CustomerFeatureSubscription'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class CustomerFeatureSubscriptionDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("CanAccessSlamcore")]
		protected System.Boolean _canAccessSlamcore;
		[JsonProperty ("Description")]
		protected System.String _description;
		[JsonProperty ("HasAdditionalHardwaresAccess")]
		protected System.Boolean _hasAdditionalHardwaresAccess;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("IsEnabled")]
		protected System.Boolean _isEnabled;
		[JsonProperty ("IsTagged")]
		protected System.Boolean _isTagged;
		[JsonProperty ("Name")]
		protected System.String _name;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)

		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public CustomerFeatureSubscriptionDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
			SetHasAdditionalHardwaresAccessValue(false, false, false);
			SetIsTaggedValue(false, false, false);
			SetCanAccessSlamcoreValue(false, false, false);
			SetIsEnabledValue(false, false, false);
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public CustomerFeatureSubscriptionDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public CustomerFeatureSubscriptionDataObject Initialize(System.Guid id)
		{
			this._id = id;
			SetHasAdditionalHardwaresAccessValue(false, false, false);
			SetIsTaggedValue(false, false, false);
			SetCanAccessSlamcoreValue(false, false, false);
			SetIsEnabledValue(false, false, false);
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public CustomerFeatureSubscriptionDataObject Initialize(CustomerFeatureSubscriptionDataObject template, bool deepCopy)
		{
			this.SetCanAccessSlamcoreValue(template.CanAccessSlamcore, false, false);
			this.SetDescriptionValue(template.Description, false, false);
			this.SetHasAdditionalHardwaresAccessValue(template.HasAdditionalHardwaresAccess, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetIsEnabledValue(template.IsEnabled, false, false);
			this.SetIsTaggedValue(template.IsTagged, false, false);
			this.SetNameValue(template.Name, false, false);
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual CustomerFeatureSubscriptionDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual CustomerFeatureSubscriptionDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var customerFeatureSubscriptionSource = sourceObject as CustomerFeatureSubscriptionDataObject;

			if (ReferenceEquals(null, customerFeatureSubscriptionSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetCanAccessSlamcoreValue(customerFeatureSubscriptionSource.CanAccessSlamcore, false, false);
			this.SetDescriptionValue(customerFeatureSubscriptionSource.Description, false, false);
			this.SetHasAdditionalHardwaresAccessValue(customerFeatureSubscriptionSource.HasAdditionalHardwaresAccess, false, false);
			this.SetIdValue(customerFeatureSubscriptionSource.Id, false, false);
			this.SetIsEnabledValue(customerFeatureSubscriptionSource.IsEnabled, false, false);
			this.SetIsTaggedValue(customerFeatureSubscriptionSource.IsTagged, false, false);
			this.SetNameValue(customerFeatureSubscriptionSource.Name, false, false);

			if (deepCopy)
			{
				this.ObjectsDataSet = customerFeatureSubscriptionSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(customerFeatureSubscriptionSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as CustomerFeatureSubscriptionDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {

		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<CustomerDataObject> _customerService => _serviceProvider.GetRequiredService<IDataProvider<CustomerDataObject>>();
      public virtual void SetCustomerValue(CustomerDataObject valueToSet)
		{
			SetCustomerValue(valueToSet, true, true);
		}

        public virtual void SetCustomerValue(CustomerDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<CustomerDataObject>(this, "Customer");
			var existing_customer = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Customer", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._customerFeatureSubscription_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.CustomerFeatureSubscription = this;
					valueToSet.CustomerFeatureSubscriptionId = this.Id;
				}			
			}
			else  if (existing_customer != null)
            {
                ObjectsDataSet.RemoveObject(existing_customer);
            }
			if (!ReferenceEquals(existing_customer ,valueToSet))
				OnPropertyChanged("Customer", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __customerSemaphore = new SemaphoreSlim(1, 1);
		private bool __customerAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Customer", which is a CustomerDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a CustomerDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<CustomerDataObject> LoadCustomerAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadCustomerAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<CustomerDataObject> LoadCustomerAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __customerSemaphore.WaitAsync();
			
	        try
            {
                if (!__customerAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data Customer for the current entity. The DataObjects doesn't have an ObjectsDataSet", "CustomerFeatureSubscriptionObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var customer = (this.ObjectsDataSet as ObjectsDataSet).CustomerObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).CustomerObjects.Where(item => item.Value.CustomerFeatureSubscriptionId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(customer, null))
					{
						var filterPredicate = "CustomerFeatureSubscriptionId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						customer = (await _customerService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetCustomerValue(customer, false, false);
						__customerAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a customer, but relation field not set, encourage it to get set by removing and re-adding the customer 
					if (customer != null && this.Customer == null)
					{
						this.ObjectsDataSet.RemoveObject(customer);
						this.ObjectsDataSet.AddObject(customer);
					}			
                    __customerAlreadyLazyLoaded = true;
                }

                return await GetCustomerAsync(false);
            }
            finally
            {
                __customerSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual CustomerDataObject Customer 
		{
			get
			{			
				return GetCustomerAsync(true).Result;
			}
			set
			{
				SetCustomerValue(value);
			}
		}
		
		public virtual bool ShouldSerializeCustomer()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("CustomerFeatureSubscriptionDataObject") && ObjectsDataSet.RelationsToInclude["CustomerFeatureSubscriptionDataObject"].Contains("Customer");
		}

		public virtual async Task<CustomerDataObject> GetCustomerAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			CustomerDataObject customer;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<CustomerDataObject>(this, "Customer");
               	customer = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && customer == null && LazyLoadingEnabled && (!__customerAlreadyLazyLoaded || forceReload))
				{
					customer = await LoadCustomerAsync(forceReload : forceReload);
				}
			}
				
			return customer;
		}


		public override void ClearLazyLoadFlags()
		{
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadCustomerAsync()) != null)
				result.Add(Customer);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return false;
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetCanAccessSlamcoreValue(System.Boolean valueToSet)
		{
			SetCanAccessSlamcoreValue(valueToSet, true, true);
		}

		public virtual void SetCanAccessSlamcoreValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_canAccessSlamcore != valueToSet)
			{
				_canAccessSlamcore = valueToSet;

				OnPropertyChanged("CanAccessSlamcore", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The CanAccessSlamcore property of the CustomerFeatureSubscription DataObject</summary>
        public virtual System.Boolean CanAccessSlamcore 
		{
			get	{ return _canAccessSlamcore;}
			
			
			set
			{
				SetCanAccessSlamcoreValue(value);
			}
		}		
			
			
		public virtual void SetDescriptionValue(System.String valueToSet)
		{
			SetDescriptionValue(valueToSet, true, true);
		}

		public virtual void SetDescriptionValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_description != valueToSet)
			{
				_description = valueToSet;

				OnPropertyChanged("Description", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Description property of the CustomerFeatureSubscription DataObject</summary>
        public virtual System.String Description 
		{
			get	{ return String.IsNullOrEmpty(_description) ? null : _description; }
			
			
			set
			{
				SetDescriptionValue(value);
			}
		}		
			
			
		public virtual void SetHasAdditionalHardwaresAccessValue(System.Boolean valueToSet)
		{
			SetHasAdditionalHardwaresAccessValue(valueToSet, true, true);
		}

		public virtual void SetHasAdditionalHardwaresAccessValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_hasAdditionalHardwaresAccess != valueToSet)
			{
				_hasAdditionalHardwaresAccess = valueToSet;

				OnPropertyChanged("HasAdditionalHardwaresAccess", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The HasAdditionalHardwaresAccess property of the CustomerFeatureSubscription DataObject</summary>
        public virtual System.Boolean HasAdditionalHardwaresAccess 
		{
			get	{ return _hasAdditionalHardwaresAccess;}
			
			
			set
			{
				SetHasAdditionalHardwaresAccessValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the CustomerFeatureSubscription DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetIsEnabledValue(System.Boolean valueToSet)
		{
			SetIsEnabledValue(valueToSet, true, true);
		}

		public virtual void SetIsEnabledValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_isEnabled != valueToSet)
			{
				_isEnabled = valueToSet;

				OnPropertyChanged("IsEnabled", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The IsEnabled? property of the CustomerFeatureSubscription DataObject</summary>
        public virtual System.Boolean IsEnabled 
		{
			get	{ return _isEnabled;}
			
			
			set
			{
				SetIsEnabledValue(value);
			}
		}		
			
			
		public virtual void SetIsTaggedValue(System.Boolean valueToSet)
		{
			SetIsTaggedValue(valueToSet, true, true);
		}

		public virtual void SetIsTaggedValue(System.Boolean valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_isTagged != valueToSet)
			{
				_isTagged = valueToSet;

				OnPropertyChanged("IsTagged", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The IsTagged? property of the CustomerFeatureSubscription DataObject</summary>
        public virtual System.Boolean IsTagged 
		{
			get	{ return _isTagged;}
			
			
			set
			{
				SetIsTaggedValue(value);
			}
		}		
			
			
		public virtual void SetNameValue(System.String valueToSet)
		{
			SetNameValue(valueToSet, true, true);
		}

		public virtual void SetNameValue(System.String valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_name != valueToSet)
			{
				_name = valueToSet;

				OnPropertyChanged("Name", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Name property of the CustomerFeatureSubscription DataObject</summary>
        public virtual System.String Name 
		{
			get	{ return _name; }
			
			
			set
			{
				SetNameValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
			var customer = GetCustomerAsync(false).Result;
			if (customer != null && this.IsDirty)
            {
				customer.NotifyPropertyChanged("CustomerFeatureSubscription." + propertyName, callers);
			}
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is CustomerFeatureSubscriptionDataObject))
				return false;

			var p = (CustomerFeatureSubscriptionDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.CanAccessSlamcore == p.CanAccessSlamcore;
			fieldsComparison &= this.Description == p.Description;
			fieldsComparison &= this.HasAdditionalHardwaresAccess == p.HasAdditionalHardwaresAccess;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.IsEnabled == p.IsEnabled;
			fieldsComparison &= this.IsTagged == p.IsTagged;
			fieldsComparison &= this.Name == p.Name;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class CustomerFeatureSubscriptionCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public CustomerFeatureSubscriptionCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public CustomerFeatureSubscriptionCollectionContainer()
		{
		}
		
		public CustomerFeatureSubscriptionCollectionContainer Construct(DataObjectCollection<CustomerFeatureSubscriptionDataObject> customerFeatureSubscriptionItems)
        {
            if (customerFeatureSubscriptionItems == null)
                return this;
				
			this.PrimaryKeys = customerFeatureSubscriptionItems.Select(c => c.PrimaryKey).ToList();
            if (customerFeatureSubscriptionItems.ObjectsDataSet == null)
            {
                customerFeatureSubscriptionItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = customerFeatureSubscriptionItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = customerFeatureSubscriptionItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<CustomerFeatureSubscriptionDataObject> ExtractCustomerFeatureSubscriptionItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<CustomerFeatureSubscriptionDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<CustomerFeatureSubscriptionDataObject>(typeof(CustomerFeatureSubscriptionDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class CustomerFeatureSubscriptionContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public CustomerFeatureSubscriptionContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual CustomerFeatureSubscriptionContainer Construct(CustomerFeatureSubscriptionDataObject customerFeatureSubscription, bool includeDirtyObjectsOnly = false)
		{
            if (customerFeatureSubscription == null)
                return this;

			this.PrimaryKey = customerFeatureSubscription.PrimaryKey;
			
            if (customerFeatureSubscription.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(customerFeatureSubscription);
            }

			if(customerFeatureSubscription.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity CustomerFeatureSubscription", "Unable to set a dataset to the entity. Container may not be initialized", "CustomerFeatureSubscriptionDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : CustomerFeatureSubscription");
			}

			if(customerFeatureSubscription.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in CustomerFeatureSubscriptionDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "CustomerFeatureSubscriptionDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in CustomerFeatureSubscriptionDataObject");
			}
			this.InternalObjectId = (int) customerFeatureSubscription.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? customerFeatureSubscription.ObjectsDataSet.CloneDirtyObjects() : customerFeatureSubscription.ObjectsDataSet;

			return this;
		}
		
		public CustomerFeatureSubscriptionDataObject ExtractCustomerFeatureSubscription()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<CustomerFeatureSubscriptionDataObject>(typeof(CustomerFeatureSubscriptionDataObject), InternalObjectId);
			
			return result;
        }	
	}

}