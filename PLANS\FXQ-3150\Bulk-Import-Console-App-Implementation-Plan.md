## FXQ-3150 Bulk Import Console Application – Implementation Plan

This plan defines phases, actionable tasks, deliverables, and dependencies to build a .NET Console application that performs efficient bulk insertion of driver data, vehicle details, and related entities into the FleetXQ database. Tasks use markdown checkboxes for progress tracking.

### Scope and Acceptance Criteria
- [ ] Fully functional .NET Console app for bulk inserting drivers, vehicles, and related entities
- [ ] Efficient inserts using batching and/or `SqlBulkCopy` with appropriate tuning
- [ ] Robust error handling and structured logging
- [ ] Clear documentation for running, configuring, and verifying results
- [ ] Post-insert verification steps for data integrity
- [ ] Clean integration with existing FleetXQ systems and constraints
// Added interactive sizing and scale expectations
- [ ] Interactive prompt to set the number of Drivers and Vehicles to process/generate (e.g., 10000 drivers, 5000 vehicles), with CLI flags to override
- [ ] Optimized to handle thousands to hundreds of thousands of rows using streaming I/O and bulk operations without exhausting memory

### Architectural Alignment
- Align with FleetXQ layered architecture (Web/Service/Business/Data).
- Use SQL Server best practices and existing patterns; prefer NHibernate entities where reuse is practical.
- Follow existing authentication/authorization and configuration patterns where applicable.
- Keep custom logic isolated and maintainable to fit FleetXQ standards.

### Inputs, Dependencies, Assumptions
- Inputs: **SQL-based data generation** (eliminates file dependencies). Synthetic data generated directly in staging tables.
- DB: SQL Server with existing FleetXQ schema; staging tables implemented for performance and referential safety.
- Access: Non-production and production connection strings available via configuration.
- Packages: `Serilog` (logging), `Microsoft.Data.SqlClient`, `Polly` for transient fault handling. **CsvHelper removed**.

---

## Phase 0 – Discovery and Design Decisions
Description: Confirm domain boundaries, data ownership, and integration constraints.

- [ ] Identify entities/columns required: Drivers, Vehicles, Relationships (e.g., driver-vehicle links), and any supporting lookups
  - Deliverable: Field mapping document (source → target columns, nullability, defaults)
- [ ] Confirm upsert strategy (insert-only vs. insert-or-update) and deduplication keys (e.g., ExternalId, DriverNumber, VIN)
  - Deliverable: Upsert/dedup rules with conflict resolution policy
- [x] Define SQL-based data generation approach (staging tables, stored procedures)
  - Deliverable: SQL staging table design and data generation procedures
- [ ] Determine whether to use staging tables and stored procedures for final merges
  - Deliverable: Staging/merge approach decision note

Dependencies: Access to schema, SMEs for data rules, sample datasets.

---

## Phase 1 – Project Bootstrap
Description: Create a self-contained .NET console project with baseline tooling.

- [ ] Create solution folder and .NET console project (e.g., `Tools/BulkImporter`)
  - Deliverable: Compiling project and `.csproj`
- [ ] Add packages: `Microsoft.Data.SqlClient`, `Serilog`, `Serilog.Sinks.Console`, `Serilog.Sinks.File`, `CsvHelper`, `Polly`
  - Deliverable: Restored dependencies
- [ ] Add configuration files: `appsettings.json`, `appsettings.Development.json`
  - Deliverable: Environment-based configuration loading
- [ ] Implement configuration binding (connection strings, batch size, timeouts, file paths)
  - Deliverable: Strongly-typed configuration model
- [ ] Initialize logging (console + rolling file), include correlation/operationId
  - Deliverable: Structured logging baseline
// Defaults for high-volume runs and interactive sizing
- [ ] Add default performance settings and sizing keys (e.g., `DefaultBatchSize`, `DefaultDriversCount`, `DefaultVehiclesCount`)
  - Deliverable: Config keys with safe defaults that can be overridden by CLI/interactive input

Dependencies: .NET SDK, NuGet access.

---

## Phase 2 – Database Integration Setup
Description: Prepare database layer for safe/high-throughput writes.

- [ ] Assess existing tables and constraints; document required indexes and FKs affecting inserts
  - Deliverable: DB impact checklist
- [ ] Design staging tables for Drivers, Vehicles, and Relationships (if merge approach is chosen)
  - Deliverable: `Sql/` scripts for `CREATE TABLE` [Staging]
- [ ] Create stored procedures for merging staging → production with idempotent logic
  - Deliverable: `Sql/` scripts for `CREATE/ALTER PROC` merges
- [ ] Decide identity handling strategy (IDENTITY columns vs. natural keys)
  - Deliverable: Identity strategy note
- [ ] Validate permissions for bulk ops and `SqlBulkCopy`
  - Deliverable: Permission verification checklist

Dependencies: DBA review/approval, deployment pathway for SQL changes.

---

## Phase 3 – SQL Data Generation and Validation
Description: Generate and validate large datasets using SQL-based operations with minimal memory footprint.

- [x] Implement SQL-based data generation with schema validation and type conversion
  - Deliverable: SQL data generation service with per-row validation results
- [ ] Define required/optional fields and defaulting rules
  - Deliverable: Validation ruleset
- [ ] Implement row-level error capture without halting the entire run
  - Deliverable: Bad rows log with reasons; summary report
- [ ] Add dry-run mode to validate inputs without writing to DB
  - Deliverable: `--dry-run` flag behavior
// Interactive sizing and optional synthetic data
- [ ] Support limiting the number of processed rows per entity based on user-provided counts (drivers/vehicles)
  - Deliverable: Row processing cap controlled by CLI/interactive input
- [ ] Optional: Implement a simple synthetic data generator to create N drivers and M vehicles when `--generate` is specified
  - Deliverable: Deterministic generator for scale testing with schema-aligned data

Dependencies: Sample files, schema spec from Phase 0.

---

## Phase 4 – Bulk Insert Engine
Description: Efficiently load data into staging using `SqlBulkCopy` with tuning.

- [ ] Implement `SqlBulkCopy` writer supporting column mappings and batching
  - Deliverable: Reusable bulk writer utility
- [ ] Configure tuning parameters: `BatchSize`, `BulkCopyTimeout`, `NotifyAfter`
  - Deliverable: Configurable performance settings with sensible defaults
- [ ] Support streaming `IDataReader`/row enumerables to avoid large in-memory buffers
  - Deliverable: Memory-efficient pipeline
- [ ] Implement retries with jitter for transient errors (e.g., deadlocks, timeouts) using `Polly`
  - Deliverable: Resilient execution policy
// High-volume optimization details
- [ ] Use `SqlBulkCopyOptions.TableLock` and enable `UseInternalTransaction` per batch to reduce locking overhead
  - Deliverable: Bulk copy initialized with optimal options
- [ ] Configure column mappings explicitly and pre-size buffers to minimize allocations
  - Deliverable: Stable high-throughput mapping and buffers
- [ ] Ensure staging tables have minimal/necessary indexes only during load; rebuild/add nonclustered indexes post-merge if needed
  - Deliverable: Index strategy note and scripts

Dependencies: Confirm target staging schemas; validated connection strings.

---

## Phase 5 – Merge to Production and Referential Integrity
Description: Safe, idempotent promotion from staging → final tables.

- [ ] Implement stored procedure calls to merge Drivers and Vehicles (insert/update as per policy)
  - Deliverable: Merge invocation service with logging of counts
- [ ] Merge relationship tables after primary entities
  - Deliverable: Ordered merge logic with FK safety
- [ ] Generate import session identifier and record per-entity counts/metrics
  - Deliverable: Import audit table rows
- [ ] Optional: Partitioned runs to reduce lock contention in very large datasets
  - Deliverable: Partitioned merge capability (configurable)

Dependencies: Phase 2 stored procedures deployed.

---

## Phase 6 – Error Handling, Observability, and Auditing
Description: Ensure robust diagnostics, resumability, and traceability.

- [ ] Centralized exception handling with categorized error codes
  - Deliverable: Error categorization and standardized messages
- [ ] Structured logging (JSON-capable) including file offsets/line numbers for bad rows
  - Deliverable: Machine-parseable logs
- [ ] Summarized run report (totals, successes, failures, elapsed, throughput)
  - Deliverable: End-of-run report artifact
- [ ] Optional: Emit metrics to stdout/JSON for ingestion by external tools
  - Deliverable: Metrics export option

Dependencies: Logging baseline from Phase 1; validation from Phase 3.

---

## Phase 7 – CLI and Configuration UX
Description: Provide intuitive command-line interface and configuration.

- [ ] Implement CLI options: input path(s), entity switches, batch size, timeouts, dry-run, stop-on-first-error
  - Deliverable: `--help` documented CLI
- [ ] Support environment variable overrides for connection and secrets
  - Deliverable: Secure configuration strategy
- [ ] Validate config at startup; fail fast with actionable messages
  - Deliverable: Startup diagnostics
// Interactive prompts for counts with non-interactive overrides
- [ ] Add CLI flags `--drivers <int>` and `--vehicles <int>`; when omitted in interactive sessions, prompt the user to enter counts
  - Deliverable: Interactive prompt flow with validation and defaults (e.g., 10000 drivers, 5000 vehicles)
- [ ] Add `--non-interactive` to suppress prompts for CI and rely solely on config/flags
  - Deliverable: Deterministic non-interactive behavior
- [ ] Add `--generate` to synthesize N drivers and M vehicles; otherwise process input files up to the specified caps
  - Deliverable: Flexible data source selection

Dependencies: Configuration model from Phase 1.

---

## Phase 8 – Performance and Scale Testing
Description: Validate throughput and stability under large loads.

- [ ] Create synthetic large datasets (e.g., 100k–1M rows) matching schema
  - Deliverable: Data generation script(s)
- [ ] Benchmark end-to-end with varying batch sizes and timeouts
  - Deliverable: Performance report with chosen defaults
- [ ] Validate locking behavior and log minimal-logging conditions
  - Deliverable: DBA-validated perf notes
// Targets and scenarios
- [ ] Validate runs for 10k/50k/100k drivers and 5k/25k/50k vehicles using SQL-based generation
  - Deliverable: Timing table and throughput metrics (rows/sec) per scenario
- [ ] Establish recommended defaults (e.g., `BatchSize=10000`, tuned timeouts) meeting target throughput without excessive lock contention
  - Deliverable: Documented defaults and when to adjust them

Dependencies: Stable pipeline from Phases 3–5; test DB environment.

---

## Phase 9 – Data Integrity and Verification
Description: Provide post-insert verification and reconciliation steps.

- [ ] Implement row counts and checksum validations per entity
  - Deliverable: Verification routine and results file
- [ ] Spot-check referential integrity (FK existence) and business rules
  - Deliverable: Verification report
- [ ] Provide rollback/correction guidance for failures (targeted delete by import session id)
  - Deliverable: Rollback procedure doc and scripts

Dependencies: Import audit/session tracking from Phase 5.

---

## Phase 10 – Packaging, CI/CD, and Release
Description: Build, version, and distribute the tool.

- [ ] Add build script/target to produce a self-contained executable
  - Deliverable: Build artifacts for Windows runner
- [ ] Integrate with Azure Pipelines to publish artifacts
  - Deliverable: Pipeline job/stage definitions
- [ ] Versioning and changelog conventions
  - Deliverable: Versioning strategy doc

Dependencies: Existing Azure DevOps setup.

---

## Phase 11 – Documentation and Handover
Description: Provide complete operational documentation.

- [ ] README with prerequisites, configuration, examples, and troubleshooting
  - Deliverable: `README.md` co-located with the tool
- [ ] Operational runbook (scheduling, monitoring, log locations)
  - Deliverable: Runbook document
- [ ] Mapping spec and sample input files
  - Deliverable: `/samples` folder

Dependencies: All prior phases.

---

## Risks and Mitigations
- Duplicate or malformed data → Strict validation, dry-run, and deterministic upsert rules
- Lock escalation/perf issues → Staging + batched merges, tuned batch sizes, off-peak windows
- Constraint violations → Ordered merges, pre-validation of foreign keys, clear error reporting
- Operational errors (bad config/credentials) → Startup validation and safe failure modes

## Definition of Done
- All acceptance criteria satisfied
- CI build green with published artifact
- Benchmark thresholds met and documented
- Verification/rollback procedures validated in test environment

## Progress Log
- Use the checkboxes above; when a phase is fully complete, mark all tasks in that phase as done.


