%% ================================================================================
%% FleetXQ Tools - Database Schema and Data Flow Diagram
%% ================================================================================
%% 
%% This diagram shows the database architecture and data flow for the BulkImporter
%% tool, including staging tables, stored procedures, and production integration.
%% ================================================================================

erDiagram
    %% Staging Schema Tables
    ImportSession {
        uniqueidentifier Id PK
        nvarchar SessionName
        datetime2 StartTime
        datetime2 EndTime
        nvarchar Status "Running, Completed, Failed, RolledBack"
        int TotalRows
        int ProcessedRows
        int SuccessfulRows
        int FailedRows
        nvarchar ErrorSummary
        nvarchar ConfigurationSnapshot
        nvarchar CreatedBy
        datetime2 CreatedAt
    }
    
    DriverImport {
        bigint Id PK
        uniqueidentifier ImportSessionId FK
        int RowNumber
        nvarchar ValidationStatus "Pending, Valid, Invalid, Processed"
        nvarchar ValidationErrors
        nvarchar ExternalDriverId
        nvarchar PersonFirstName
        nvarchar PersonLastName
        nvarchar PersonEmail
        nvarchar PersonPhone
        bit DriverActive
        int DriverLicenseMode
        bit DriverVehicleAccess
        bit PersonIsActiveDriver
        bit PersonHasLicense
        bit PersonLicenseActive
        bit PersonVehicleAccess
        bit PersonCanUnlockVehicle
        bit PersonNormalDriverAccess
        nvarchar CustomerName
        nvarchar SiteName
        nvarchar DepartmentName
        nvarchar Notes
        uniqueidentifier CustomerId
        uniqueidentifier SiteId
        uniqueidentifier DepartmentId
        uniqueidentifier ExistingPersonId
        uniqueidentifier ExistingDriverId
        nvarchar ProcessingAction "Insert, Update, Skip"
        datetime2 ProcessedAt
        nvarchar ProcessingErrors
    }
    
    VehicleImport {
        bigint Id PK
        uniqueidentifier ImportSessionId FK
        int RowNumber
        nvarchar ValidationStatus "Pending, Valid, Invalid, Processed"
        nvarchar ValidationErrors
        nvarchar ExternalVehicleId
        nvarchar HireNo
        nvarchar SerialNo
        nvarchar Description
        bit OnHire
        bit ImpactLockout
        bit IsCanbus
        bit TimeoutEnabled
        bit ModuleIsConnected
        int IDLETimer
        nvarchar CustomerName
        nvarchar SiteName
        nvarchar DepartmentName
        nvarchar ModelName
        nvarchar ManufacturerName
        nvarchar ModuleSerialNumber
        nvarchar AssignedDriverEmail
        nvarchar AssignedPersonEmail
        uniqueidentifier CustomerId
        uniqueidentifier SiteId
        uniqueidentifier DepartmentId
        uniqueidentifier ModelId
        uniqueidentifier ModuleId
        uniqueidentifier AssignedDriverId
        uniqueidentifier AssignedPersonId
        uniqueidentifier ExistingVehicleId
        nvarchar ProcessingAction "Insert, Update, Skip"
        datetime2 ProcessedAt
        nvarchar ProcessingErrors
    }

    %% Production Tables (Simplified FleetXQ Schema)
    Person {
        uniqueidentifier Id PK
        nvarchar FirstName
        nvarchar LastName
        nvarchar Email
        nvarchar Phone
        bit IsActive
        datetime2 CreatedAt
        datetime2 UpdatedAt
    }
    
    Driver {
        uniqueidentifier Id PK
        uniqueidentifier PersonId FK
        nvarchar ExternalId
        bit IsActive
        int LicenseMode
        bit HasVehicleAccess
        datetime2 CreatedAt
        datetime2 UpdatedAt
    }
    
    Vehicle {
        uniqueidentifier Id PK
        nvarchar HireNo
        nvarchar SerialNo
        nvarchar Description
        bit OnHire
        bit ImpactLockout
        bit IsCanbus
        uniqueidentifier CustomerId FK
        uniqueidentifier SiteId FK
        uniqueidentifier ModelId FK
        datetime2 CreatedAt
        datetime2 UpdatedAt
    }
    
    Customer {
        uniqueidentifier Id PK
        nvarchar Name
        bit IsActive
        datetime2 CreatedAt
    }
    
    Site {
        uniqueidentifier Id PK
        uniqueidentifier CustomerId FK
        nvarchar Name
        bit IsActive
        datetime2 CreatedAt
    }
    
    Department {
        uniqueidentifier Id PK
        uniqueidentifier SiteId FK
        nvarchar Name
        bit IsActive
        datetime2 CreatedAt
    }

    %% Relationships
    ImportSession ||--o{ DriverImport : "tracks"
    ImportSession ||--o{ VehicleImport : "tracks"
    
    %% Production relationships
    Person ||--o{ Driver : "is_driver"
    Customer ||--o{ Site : "has_sites"
    Site ||--o{ Department : "has_departments"
    Customer ||--o{ Vehicle : "owns_vehicles"
    Site ||--o{ Vehicle : "located_at"
    Department ||--o{ Driver : "assigned_to"
    Department ||--o{ Vehicle : "assigned_to"

%% ================================================================================
%% Stored Procedures and Data Flow
%% ================================================================================

graph LR
    %% Data Generation Flow
    subgraph "🎲 Data Generation"
        DG1["Generate Driver Batches<br/>Complex SQL with CTEs"]
        DG2["Generate Vehicle Batches<br/>Complex SQL with CTEs"]
        DG3["Batch Size Control<br/>Memory Management"]
    end
    
    %% Staging Tables
    subgraph "🏗️ Staging Tables"
        ST1["Staging.DriverImport<br/>Raw generated data"]
        ST2["Staging.VehicleImport<br/>Raw generated data"]
        ST3["Staging.ImportSession<br/>Session tracking"]
    end
    
    %% Validation Layer
    subgraph "✅ Validation Layer"
        V1["ValidateImportData SP<br/>Field validation"]
        V2["Update ValidationStatus<br/>Valid/Invalid/Pending"]
        V3["Foreign Key Resolution<br/>Customer/Site/Department"]
    end
    
    %% Processing Layer
    subgraph "🚀 Processing Layer"
        P1["ProcessImportData SP<br/>Production processing"]
        P2["ProcessImportDataDryRun SP<br/>Simulation mode"]
        P3["Merge to Production<br/>Insert/Update/Skip"]
    end
    
    %% Production Schema
    subgraph "🎯 Production Schema"
        PROD1["Person/Driver Tables<br/>Final destination"]
        PROD2["Vehicle/Asset Tables<br/>Final destination"]
        PROD3["Customer/Site/Dept<br/>Reference data"]
    end
    
    %% Flow connections
    DG1 --> ST1
    DG2 --> ST2
    DG3 --> ST3
    
    ST1 --> V1
    ST2 --> V1
    V1 --> V2
    V2 --> V3
    
    V3 --> P1
    V3 --> P2
    P1 --> P3
    
    P3 --> PROD1
    P3 --> PROD2
    P3 --> PROD3

%% ================================================================================
%% SQL Script Dependencies and Execution Order
%% ================================================================================

graph TD
    SQL1["001-CreateStagingSchema.sql<br/>Create Staging schema<br/>Create ImportSession table"] 
    SQL2["002-CreateDriverStagingTable.sql<br/>Create DriverImport table<br/>Indexes and constraints"]
    SQL3["003-CreateVehicleStagingTable.sql<br/>Create VehicleImport table<br/>Indexes and constraints"]
    SQL4["004-CreateValidationProcedures.sql<br/>Validation stored procedures<br/>(Referenced but not implemented)"]
    SQL5["005-CreateMergeProcedures.sql<br/>Merge stored procedures<br/>(Referenced but not implemented)"]
    SQL6["006-CreateDataGenerationProcedures.sql<br/>ValidateImportData<br/>ProcessImportData<br/>ProcessImportDataDryRun"]
    
    SQL1 --> SQL2
    SQL1 --> SQL3
    SQL2 --> SQL6
    SQL3 --> SQL6
    SQL4 --> SQL6
    SQL5 --> SQL6
    
    style SQL1 fill:#e1f5fe
    style SQL6 fill:#c8e6c9
    style SQL4 fill:#fff3e0
    style SQL5 fill:#fff3e0
