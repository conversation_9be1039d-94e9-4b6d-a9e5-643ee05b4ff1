﻿{
  "entityName": "AlertHistory",
  "entityNamePlural": "AlertHistories",   "entityDescription": "This entity represents a AlertHistory",
  "fields": {
    "Alert": {
        "displayName": "Alert", 
        "description": "AlertHistory"
    },
    "AlertId": {
        "displayName": "AlertId", 
        "description": "Foreign Key"
    },
    "CreatedDateTime": {
        "displayName": "CreatedDateTime", 
        "description": "CreatedDateTime"
    },
    "Description": {
        "displayName": "Description", 
        "description": "Description"
    },
    "Driver": {
        "displayName": "Driver", 
        "description": "Driver"
    },
    "DriverId": {
        "displayName": "DriverId", 
        "description": "Foreign Key"
    },
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "IsAcknowledged": {
        "displayName": "IsAcknowledged", 
        "description": "IsAcknowledged"
    },
    "IsResolved": {
        "displayName": "IsResolved", 
        "description": "IsResolved"
    },
    "Vehicle": {
        "displayName": "Vehicle", 
        "description": "Vehicle"
    },
    "VehicleId": {
        "displayName": "VehicleId", 
        "description": "Foreign Key"
    }
  }
} 