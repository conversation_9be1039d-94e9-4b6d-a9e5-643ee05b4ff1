﻿{
  "entityName": "Slamcore Device Connection View",
  "entityNamePlural": "Slamcore Device Connection Views",   "entityDescription": "This entity represents a Slamcore Device Connection View",
  "fields": {
    "Id": {
        "displayName": "Id", 
        "description": "Default Generated Primary Key"
    },
    "Offline": {
        "displayName": "Offline Devices", 
        "description": "Offline Devices", 
        "validationRules": {
            "00ad9381-29e1-48f9-b150-fc8e0935791e" : {
                    "errorMessage": "Value must be a number for the field Offline Devices"
            }
        }
    },
    "OfflineDevicesWithLabel": {
        "displayName": "Offline Devices With Label", 
        "description": "Offline Devices"
    },
    "OnlineDevices": {
        "displayName": "Online Devices", 
        "description": "Online Devices", 
        "validationRules": {
            "ea94aaed-8f81-492d-ac92-581abdc7ca93" : {
                    "errorMessage": "Value must be a number for the field Online Devices"
            }
        }
    },
    "OnlineDevicesWithLabel": {
        "displayName": "Online Devices With Label", 
        "description": "Online Devices With Label"
    },
    "SlamcoreDevice": {
        "displayName": "SlamcoreDevice", 
        "description": "SlamcoreDevice"
    },
    "SlamcoreDeviceId": {
        "displayName": "SlamcoreDeviceId", 
        "description": "Foreign Key"
    },
    "TotalDevices": {
        "displayName": "Total Devices", 
        "description": "Total Devices", 
        "validationRules": {
            "7a463539-eb4b-4a6f-a600-855ccaf663d7" : {
                    "errorMessage": "Value must be a number for the field Total Devices"
            }
        }
    },
    "TotalDevicesWithLabel": {
        "displayName": "Total Devices with Label", 
        "description": "Total Devices with Label"
    }
  }
} 