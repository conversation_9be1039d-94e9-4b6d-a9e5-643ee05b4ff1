﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
(function (global) {
    FleetXQ.Web.Application.Controller = function () {
        var self = this;

        this.sourceHandler = global.ApplicationSourceHandler;
		this.translationHandler = new FleetXQ.Web.Application.TranslationSourceHandler();
		this.ObjectsDataSet = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();

        this.viewLoader = new FleetXQ.Web.Views.viewLoader();

		if (FleetXQ.Web.Routing.Custom) {
		    this.customRouting = new FleetXQ.Web.Routing.Custom(this);
		}

		// Hook up custom application controller 
		if (FleetXQ.Web.Application.ControllerCustom) {
			this.customApplicationController = new FleetXQ.Web.Application.ControllerCustom(this);
		}
		
		this.getProxyForComponent = function(componentName) {
			if (self[componentName + "Proxy"] === undefined)
				self[componentName + "Proxy"] = new FleetXQ.Web.Model.Components[componentName + "Proxy"](this.ObjectsDataSet);

			return self[componentName + "Proxy"];
		};

		// Not Used anymore
		this.numberZoneLoaded = 0;
		this.sourcesLoaded = false;

		this.getPageNameForNode = function (nodeName) {
			var pageName = null;
			
			switch(nodeName) {
				case 'VehicleCalibrationReport3':
					pageName = 'VehicleCalibrationReportPage';
					break;
				case 'UserEmailAlertSummary2':
					pageName = 'UserEmailAlertSummaryReportPage';
					break;
				case 'UserDetail':
					pageName = 'PersonDetailPagePage';
					break;
				case 'Dashboard':
					pageName = 'FleetDashboardPage';
					break;
				case 'CountryItems':
					pageName = 'CountryItemsPage';
					break;
				case 'PreOpCheckReport':
					pageName = 'PreOpCheckReportPage';
					break;
				case 'DriverAccessAbuseReport':
					pageName = 'DriverAccessAbuseReportPage';
					break;
				case 'VORReport':
					pageName = 'VORReportPage';
					break;
				case 'CurrentStatusReport':
					pageName = 'CurrentStatusReportPage';
					break;
				case 'FloorPlanManagement':
					pageName = 'FloorPlanManagementPage';
					break;
				case 'FloorPlan':
					pageName = 'FloorPlanPagePage';
					break;
				case 'Import':
					pageName = 'ImportJobPage';
					break;
				case 'Vehicle':
					pageName = 'VehicleDetailsPagePage';
					break;
				case 'CountryDetails':
					pageName = 'CountryPage';
					break;
				case 'NewUser':
					pageName = 'PersonDetailPagePage';
					break;
				case 'Help':
					pageName = 'HelpPage';
					break;
				case 'RegionItems':
					pageName = 'RegionItemsPage';
					break;
				case 'ServiceCheckReport':
					pageName = 'ServiceCheckReportPage';
					break;
				case 'Vehicles':
					pageName = 'VehilceItemsPage';
					break;
				case 'GO2FAConfiguration':
					pageName = 'GO2FAConfigurationPage';
					break;
				case 'UserManagement':
					pageName = 'PersonItemsPage';
					break;
				case 'RegionDetails':
					pageName = 'RegionPage';
					break;
				case 'ChecklistResultAnswers':
					pageName = 'ChecklistResultAnswersPagePage';
					break;
				case 'AlertReport':
					pageName = 'AlertReportPage';
					break;
				case 'DealerDetails':
					pageName = 'DealerPage';
					break;
				case 'DataExportTool':
					pageName = 'DataExportPagePage';
					break;
				case 'PedestrianDetectionReport':
					pageName = 'PedestrianDetectionReportPage';
					break;
				case 'Firmware':
					pageName = 'FirmwaresPagePage';
					break;
				case 'VehicleHireDehireReport':
					pageName = 'VehicleHireDehireReportPage';
					break;
				case 'ReportScheduler':
					pageName = 'ReportSchedulerPage';
					break;
				case 'Models':
					pageName = 'ModelItemsPage';
					break;
				case 'FeatureSubscriptions':
					pageName = 'FeatureSubscriptionsPage';
					break;
				case 'MyAccount':
					pageName = 'MyaccountPage';
					break;
				case 'MachineUnlockReport':
					pageName = 'MachineUnlockReportPage';
					break;
				case 'CustomerSSO':
					pageName = 'CustomerSSOPage';
					break;
				case 'PathAnalysisView':
					pageName = 'PathAnalysisViewPage';
					break;
				case 'ConnectionStatusDashboard':
					pageName = 'ConnectionStatusDashboardPage';
					break;
				case 'ProficiencyReport':
					pageName = 'ProficiencyReportPage';
					break;
				case 'Devices':
					pageName = 'DevicesPage';
					break;
				case 'TimezoneItems':
					pageName = 'TimezoneItemsPage';
					break;
				case 'AccessGroupTemplates':
					pageName = 'AccessGroupTemplatesPage';
					break;
				case 'SynchronizationStatusReport':
					pageName = 'SynchronizationStatusReportPage';
					break;
				case 'BroadcastMessageReport':
					pageName = 'BroadcastMessageReportPage';
					break;
				case 'Customers':
					pageName = 'CustomerItemsPage';
					break;
				case 'Export':
					pageName = 'ExportPage';
					break;
				case 'UserSummaryReport2':
					pageName = 'UserSummaryReportPage';
					break;
				case 'OnDemandAuthorisationReport':
					pageName = 'OnDemandAuthorisationReportPage';
					break;
				case 'VehicleCalibrationReport':
					pageName = 'VehicleCalibrationReportPage';
					break;
				case 'LiveMap':
					pageName = 'LiveMapPage';
					break;
				case 'DealerItems':
					pageName = 'DealerItemsPage';
					break;
				case 'ImpactReport':
					pageName = 'ImpactReportPage';
					break;
				case 'SuperAdmin':
					pageName = 'SuperAdminPage';
					break;
				case 'LicenseExpiryReport':
					pageName = 'LicenseExpiryReportPage';
					break;
				case 'VehiclesGPSReport':
					pageName = 'VehiclesGPSReportPage';
					break;
				case 'GeneralProductivityReport':
					pageName = 'GeneralProductivityReportPagePage';
					break;
				case 'EmailSubscriptionReport2':
					pageName = 'EmailSubscriptionReportPage';
					break;
				case 'CustomerDetails':
					pageName = 'CustomerPage';
					break;
				case 'TimezoneDetails':
					pageName = 'TimezonePage';
					break;
			}

			return pageName;
		};

		this.getRootHashTag = function (nodeName) {
		    var root = '/';

		    if (self.customRouting && self.customRouting.getRootHashTag) {
		        root += self.customRouting.getRootHashTag(nodeName);
		    }

			return root;
		};

		this.convertNodeNameToHash = function(nodeName) {
			var root = self.getRootHashTag(nodeName);

			switch (nodeName) {
			case 'VehicleCalibrationReport3':
				return root + 'AdminSettings/DealerReports2/VehicleCalibrationReport3';
			case 'UserEmailAlertSummary2':
				return root + 'Reports/MoreReports/UserEmailAlertSummary2';
			case 'UserDetail':
				return root + 'UserManagement/UserDetail';
			case 'Dashboard':
				return root + 'Dashboard';
			case 'CountryItems':
				return root + 'AdminSettings/CountryItems';
			case 'PreOpCheckReport':
				return root + 'Reports/PreOpCheckReport';
			case 'DriverAccessAbuseReport':
				return root + 'Reports/MoreReports/DriverAccessAbuseReport';
			case 'VORReport':
				return root + 'Reports/MoreReports/VORReport';
			case 'CurrentStatusReport':
				return root + 'Reports/CurrentStatusReport';
			case 'FloorPlanManagement':
				return root + 'Slamcore/FloorPlanManagement';
			case 'FloorPlan':
				return root + 'FloorPlan';
			case 'Import':
				return root + 'AdminSettings/Import';
			case 'Vehicle':
				return root + 'Vehicle';
			case 'CountryDetails':
				return root + 'AdminSettings/CountryItems/CountryDetails';
			case 'NewUser':
				return root + 'NewUser';
			case 'Help':
				return root + 'Help';
			case 'RegionItems':
				return root + 'AdminSettings/RegionItems';
			case 'ServiceCheckReport':
				return root + 'Reports/ServiceCheckReport';
			case 'Vehicles':
				return root + 'Vehicles';
			case 'GO2FAConfiguration':
				return root + 'AdminSettings/GO2FAConfiguration';
			case 'UserManagement':
				return root + 'UserManagement';
			case 'RegionDetails':
				return root + 'AdminSettings/RegionItems/RegionDetails';
			case 'ChecklistResultAnswers':
				return root + 'Reports/PreOpCheckReport/ChecklistResultAnswers';
			case 'AlertReport':
				return root + 'Reports/MoreReports/AlertReport';
			case 'DealerDetails':
				return root + 'AdminSettings/DealerItems/DealerDetails';
			case 'DataExportTool':
				return root + 'Slamcore/DataExportTool';
			case 'PedestrianDetectionReport':
				return root + 'Reports/MoreReports/PedestrianDetectionReport';
			case 'Firmware':
				return root + 'AdminSettings/Firmware';
			case 'VehicleHireDehireReport':
				return root + 'AdminSettings/DealerReports2/VehicleHireDehireReport';
			case 'ReportScheduler':
				return root + 'Slamcore/ReportScheduler';
			case 'Models':
				return root + 'AdminSettings/Models';
			case 'FeatureSubscriptions':
				return root + 'AdminSettings/FeatureSubscriptions';
			case 'MyAccount':
				return root + 'MyAccount';
			case 'MachineUnlockReport':
				return root + 'Reports/MachineUnlockReport';
			case 'CustomerSSO':
				return root + 'AdminSettings/CustomerSSO';
			case 'PathAnalysisView':
				return root + 'Slamcore/PathAnalysisView';
			case 'ConnectionStatusDashboard':
				return root + 'Slamcore/ConnectionStatusDashboard';
			case 'ProficiencyReport':
				return root + 'Reports/ProficiencyReport';
			case 'Devices':
				return root + 'Slamcore/Devices';
			case 'TimezoneItems':
				return root + 'AdminSettings/TimezoneItems';
			case 'AccessGroupTemplates':
				return root + 'AdminSettings/AccessGroupTemplates';
			case 'SynchronizationStatusReport':
				return root + 'Reports/MoreReports/SynchronizationStatusReport';
			case 'BroadcastMessageReport':
				return root + 'Reports/MoreReports/BroadcastMessageReport';
			case 'Customers':
				return root + 'Customers';
			case 'Export':
				return root + 'Reports/Export';
			case 'UserSummaryReport2':
				return root + 'AdminSettings/DealerReports2/UserSummaryReport2';
			case 'OnDemandAuthorisationReport':
				return root + 'Reports/MoreReports/OnDemandAuthorisationReport';
			case 'VehicleCalibrationReport':
				return root + 'Reports/MoreReports/VehicleCalibrationReport';
			case 'LiveMap':
				return root + 'Slamcore/LiveMap';
			case 'DealerItems':
				return root + 'AdminSettings/DealerItems';
			case 'ImpactReport':
				return root + 'Reports/ImpactReport';
			case 'SuperAdmin':
				return root + 'AdminSettings/SuperAdmin';
			case 'LicenseExpiryReport':
				return root + 'Reports/MoreReports/LicenseExpiryReport';
			case 'VehiclesGPSReport':
				return root + 'Reports/MoreReports/VehiclesGPSReport';
			case 'GeneralProductivityReport':
				return root + 'Reports/GeneralProductivityReport';
			case 'EmailSubscriptionReport2':
				return root + 'Reports/MoreReports/EmailSubscriptionReport2';
			case 'CustomerDetails':
				return root + 'Customers/CustomerDetails';
			case 'TimezoneDetails':
				return root + 'AdminSettings/TimezoneItems/TimezoneDetails';
			}
		
			return null;
		};

		this.navigateTo = function (nodeName) {
		    GO.log("App", "Navigating to " + nodeName);
			var hash = self.convertNodeNameToHash(nodeName);
            //Forcing reload of the current page
		    if (window.location.hash == "#!" + hash) {
			    $.sammy().refresh();
			} else {
				// Using JavaScript namespace pattern to access current page controller's IsInEditMode method
				// TestI18n.Web.Controllers namespace acts as a registry of all controllers
				// When a page is loaded, its controller is registered in this namespace
				// IsInEditMode is called on the current page controller through this namespace
				if(FleetXQ.Web.Controllers.IsInEditMode && FleetXQ.Web.Controllers.IsInEditMode()) {
					if(!confirm(FleetXQ.Web.Messages.i18n.t('messages.unsavedChanges')))
						return;
					
					// Cancel edit mode on current page controller
					if (FleetXQ.Web.Controllers.CancelEdit) {
						FleetXQ.Web.Controllers.CancelEdit(false);
					}					
				}
		
			    window.location.hash = "!" + hash;
			}
		};

		this.internalNavigateTo = function (nodeName) {
		    GO.log("App", "Internal navigate to " + nodeName);
		
			var pageName = self.getPageNameForNode(nodeName);

			switch(pageName)
			{
				case "ImpactReportPage":
					if (window.masterPageId !== '8cfdba24-07b5-4ebd-8609-6191b3ed1af9') {
						window.masterPageId = '8cfdba24-07b5-4ebd-8609-6191b3ed1af9';
						self.viewLoader.loadView({ viewName: 'Application8cfdba24-07b5-4ebd-8609-6191b3ed1af9Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "AlertReportPage":
				case "BroadcastMessageReportPage":
				case "DriverAccessAbuseReportPage":
				case "EmailSubscriptionReportPage":
				case "LicenseExpiryReportPage":
				case "MachineUnlockReportPage":
				case "OnDemandAuthorisationReportPage":
				case "PedestrianDetectionReportPage":
				case "ProficiencyReportPage":
				case "ServiceCheckReportPage":
				case "SynchronizationStatusReportPage":
				case "UserEmailAlertSummaryReportPage":
				case "UserSummaryReportPage":
				case "VehicleCalibrationReportPage":
				case "VehicleHireDehireReportPage":
					if (window.masterPageId !== '48ff0acc-fc0a-4c25-93a7-57d57339abe3') {
						window.masterPageId = '48ff0acc-fc0a-4c25-93a7-57d57339abe3';
						self.viewLoader.loadView({ viewName: 'Application48ff0acc-fc0a-4c25-93a7-57d57339abe3Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "PreOpCheckReportPage":
					if (window.masterPageId !== '091f77f5-9902-4b92-aaa9-0bb0efbafbd7') {
						window.masterPageId = '091f77f5-9902-4b92-aaa9-0bb0efbafbd7';
						self.viewLoader.loadView({ viewName: 'Application091f77f5-9902-4b92-aaa9-0bb0efbafbd7Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "CurrentStatusReportPage":
				case "DashboardImpactReportPage":
				case "DashboardPreOpReportPage":
				case "DashboardVehicleUtilizationReportPage":
				case "GeneralProductivityReportPagePage":
					if (window.masterPageId !== '479c2e80-8704-4bfb-a084-b1b434ac8642') {
						window.masterPageId = '479c2e80-8704-4bfb-a084-b1b434ac8642';
						self.viewLoader.loadView({ viewName: 'Application479c2e80-8704-4bfb-a084-b1b434ac8642Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "VORReportPage":
					if (window.masterPageId !== '729fbf9f-0f46-4cce-af5d-edca352bf633') {
						window.masterPageId = '729fbf9f-0f46-4cce-af5d-edca352bf633';
						self.viewLoader.loadView({ viewName: 'Application729fbf9f-0f46-4cce-af5d-edca352bf633Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "AccessGroupTemplatesPage":
				case "ChecklistResultAnswersPagePage":
				case "CountryItemsPage":
				case "CountryPage":
				case "CustomerItemsPage":
				case "CustomerPage":
				case "CustomerSSOPage":
				case "DeactivateUserPage":
				case "DealerItemsPage":
				case "DealerPage":
				case "DepartmentItemsPage":
				case "DevicesPage":
				case "DriverPagePage":
				case "ExportPage":
				case "FeatureSubscriptionsPage":
				case "FirmwaresPagePage":
				case "FloorPlanManagementPage":
				case "GO2FAConfigurationPage":
				case "ModelPage":
				case "ModuleDetailsPagePage":
				case "ModulePagePage":
				case "MyaccountPage":
				case "PersonDetailPagePage":
				case "PersonItemsPage":
				case "RegionItemsPage":
				case "RegionPage":
				case "ReportSchedulerPage":
				case "ServiceSettingsDetailsPage":
				case "ServiceSettingsPagePage":
				case "SessionDetailsPagePage":
				case "TimezoneItemsPage":
				case "TimezonePage":
				case "VehiclesGPSReportPage":
				case "VehilceItemsPage":
				case "WebsiteuserPage1":
				case "WebsiteusersPage":
					if (window.masterPageId !== '8a0f0d25-5f58-4125-bb4c-cc43a1b3c3da') {
						window.masterPageId = '8a0f0d25-5f58-4125-bb4c-cc43a1b3c3da';
						self.viewLoader.loadView({ viewName: 'Application8a0f0d25-5f58-4125-bb4c-cc43a1b3c3daLayout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "VehicleDetailsPagePage":
					if (window.masterPageId !== 'f433623a-c0d6-4785-8914-9f24f693c758') {
						window.masterPageId = 'f433623a-c0d6-4785-8914-9f24f693c758';
						self.viewLoader.loadView({ viewName: 'Applicationf433623a-c0d6-4785-8914-9f24f693c758Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "DashboardtobedeletedPage":
				case "FleetDashboardPage":
					if (window.masterPageId !== '7c30d9c9-4cf5-4685-a5f9-d6b1a5f9e489') {
						window.masterPageId = '7c30d9c9-4cf5-4685-a5f9-d6b1a5f9e489';
						self.viewLoader.loadView({ viewName: 'Application7c30d9c9-4cf5-4685-a5f9-d6b1a5f9e489Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "ExportJobPage":
				case "ImportJobPage":
					if (window.masterPageId !== '45c87622-dd4b-4d43-8706-08233b92884a') {
						window.masterPageId = '45c87622-dd4b-4d43-8706-08233b92884a';
						self.viewLoader.loadView({ viewName: 'Application45c87622-dd4b-4d43-8706-08233b92884aLayout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "ModelItemsPage":
				case "SuperAdminPage":
					if (window.masterPageId !== '3bfa44f6-031a-4afe-abd2-e250cabe3ac4') {
						window.masterPageId = '3bfa44f6-031a-4afe-abd2-e250cabe3ac4';
						self.viewLoader.loadView({ viewName: 'Application3bfa44f6-031a-4afe-abd2-e250cabe3ac4Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "DataExportPagePage":
					if (window.masterPageId !== '04850fcf-cdfb-4006-961d-4088a369cf66') {
						window.masterPageId = '04850fcf-cdfb-4006-961d-4088a369cf66';
						self.viewLoader.loadView({ viewName: 'Application04850fcf-cdfb-4006-961d-4088a369cf66Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "FloorPlanPagePage":
					if (window.masterPageId !== '90931cd5-8bfc-446b-8eb7-bd68758481b7') {
						window.masterPageId = '90931cd5-8bfc-446b-8eb7-bd68758481b7';
						self.viewLoader.loadView({ viewName: 'Application90931cd5-8bfc-446b-8eb7-bd68758481b7Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "LiveMapPage":
					if (window.masterPageId !== '871e7b0f-99a1-4a0c-b704-cc5ac4240e05') {
						window.masterPageId = '871e7b0f-99a1-4a0c-b704-cc5ac4240e05';
						self.viewLoader.loadView({ viewName: 'Application871e7b0f-99a1-4a0c-b704-cc5ac4240e05Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "PathAnalysisViewPage":
					if (window.masterPageId !== '5a552132-89c4-421d-8ea9-513bdb6195d2') {
						window.masterPageId = '5a552132-89c4-421d-8ea9-513bdb6195d2';
						self.viewLoader.loadView({ viewName: 'Application5a552132-89c4-421d-8ea9-513bdb6195d2Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "HelpPage":
					if (window.masterPageId !== 'f77d0e62-49cd-40e5-a2a0-eeb8a988ea14') {
						window.masterPageId = 'f77d0e62-49cd-40e5-a2a0-eeb8a988ea14';
						self.viewLoader.loadView({ viewName: 'Applicationf77d0e62-49cd-40e5-a2a0-eeb8a988ea14Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
				case "ConnectionStatusDashboardPage":
					if (window.masterPageId !== '6a612a16-615e-4719-ba0e-41a743ba3de1') {
						window.masterPageId = '6a612a16-615e-4719-ba0e-41a743ba3de1';
						self.viewLoader.loadView({ viewName: 'Application6a612a16-615e-4719-ba0e-41a743ba3de1Layout', successHandler: self.onMasterPageReady, errorHandler: self.onLoadViewError, context: nodeName });
					}
					else {
						self.onMasterPageReady(null, nodeName, null);
					}
					break;
			}
		};
		
		this.onMasterPageReady = function (viewData, nodeName, zone) {

			if (self.customApplicationController && self.customApplicationController.onBeforeMasterPageReady) {
				var docontinue = self.customApplicationController.onBeforeMasterPageReady(viewData, nodeName, zone);
				if (!docontinue) {
					return;
				}
			}
			
			var pageName = self.getPageNameForNode(nodeName);

		    //Get sources now
		    self.numberZoneLoaded = 0;
		    self.sourcesLoaded = false;
			self.ensureSourcesAndTranslations(pageName, "Page", self.onPageRequiredSourcesAvailable);

		    if (viewData !== null) {
		        if (self.viewModel) {
		            self.viewModel.release();

					if (self.viewModel.pageController()) {
						self.viewModel.pageController().release();	
						self.viewModel.pageController(null);
					}
		        }

		        $('#form').html(viewData);

		        self.viewModel = new FleetXQ.Web.Application.ViewModel(self);
		    }

			if (self.viewModel) {
				self.viewModel.setActiveNode(nodeName);
			}

			self.updateBreadCrumbs(nodeName);											

			self.prepareOverlay(false);		
			
			switch (window.masterPageId)
			{
				case '45c87622-dd4b-4d43-8706-08233b92884a':		
					$('#contentZone_LeftColumn').html("");
					$('#contentZone_RightColumn').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '091f77f5-9902-4b92-aaa9-0bb0efbafbd7':		
					$('#contentZone_LeftColumn').html("");
					$('#contentZone_Filter').html("");
					$('#contentZone_RightColumn').html("");
					$('#contentZone_Footer').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Filter", zone: "Filter", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Footer", zone: "Footer", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '3b6597c4-b369-4714-8f89-0e540e6f7f6a':		
					$('#contentZone_Filter').html("");
					$('#contentZone_RightColumn').html("");
					$('#contentZone_LeftColumn').html("");
					$('#contentZone_Header').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.Filter", zone: "Filter", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Header", zone: "Header", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '1dca4668-b198-43d3-831f-26617b60b9e0':		
					$('#contentZone_LeftColumn1').html("");
					$('#contentZone_RightColumn2').html("");
					$('#contentZone_RightColumn1').html("");
					$('#contentZone_Filter').html("");
					$('#contentZone_LeftColumn2').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn1", zone: "LeftColumn1", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn2", zone: "RightColumn2", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn1", zone: "RightColumn1", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Filter", zone: "Filter", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn2", zone: "LeftColumn2", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '04850fcf-cdfb-4006-961d-4088a369cf66':		
					$('#contentZone_MainZone').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '6a612a16-615e-4719-ba0e-41a743ba3de1':		
					$('#contentZone_MainZone').html("");
					$('#contentZone_Filter').html("");
					$('#contentZone_Header').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Filter", zone: "Filter", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Header", zone: "Header", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '5a552132-89c4-421d-8ea9-513bdb6195d2':		
					$('#contentZone_MainZone').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '48ff0acc-fc0a-4c25-93a7-57d57339abe3':		
					$('#contentZone_Filter').html("");
					$('#contentZone_Header').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.Filter", zone: "Filter", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Header", zone: "Header", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '8cfdba24-07b5-4ebd-8609-6191b3ed1af9':		
					$('#contentZone_LeftColumn').html("");
					$('#contentZone_MiddleColumn').html("");
					$('#contentZone_RightColumn').html("");
					$('#contentZone_Filter').html("");
					$('#contentZone_Footer').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.MiddleColumn", zone: "MiddleColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Filter", zone: "Filter", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Footer", zone: "Footer", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '1ab1f14d-682e-4531-a5d9-65855587fc91':		
					$('#contentZone_RightColumn').html("");
					$('#contentZone_LeftColumn').html("");
					$('#contentZone_Header').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Header", zone: "Header", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '4c521798-87d1-4004-94f1-73cf4e70dfc4':		
					$('#contentZone_MainZone').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case 'e4187d06-dd89-4579-bb2b-8644ca7fc15c':		
					$('#contentZone_RightColumn').html("");
					$('#contentZone_LeftColumn').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '5e4caa8c-3b6a-4927-a7dd-90dbef3ff688':		
					$('#contentZone_RightColumn').html("");
					$('#contentZone_LeftColumn').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case 'f433623a-c0d6-4785-8914-9f24f693c758':		
					$('#contentZone_MainZone').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '479c2e80-8704-4bfb-a084-b1b434ac8642':		
					$('#contentZone_Footer').html("");
					$('#contentZone_Filter').html("");
					$('#contentZone_Header').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.Footer", zone: "Footer", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Filter", zone: "Filter", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Header", zone: "Header", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '90931cd5-8bfc-446b-8eb7-bd68758481b7':		
					$('#contentZone_MainZone').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '8a0f0d25-5f58-4125-bb4c-cc43a1b3c3da':		
					$('#contentZone_MainZone').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '871e7b0f-99a1-4a0c-b704-cc5ac4240e05':		
					$('#contentZone_MainZone').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '82dfe12a-2316-4a3a-8479-d4224d408b4a':		
					$('#contentZone_MainZone').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '7c30d9c9-4cf5-4685-a5f9-d6b1a5f9e489':		
					$('#contentZone_RightColumn').html("");
					$('#contentZone_FCLeftColumn').html("");
					$('#contentZone_FCRightColumn').html("");
					$('#contentZone_LeftColumn').html("");
					$('#contentZone_MiddleColumn').html("");
					$('#contentZone_ThirdRow').html("");
					$('#contentZone_Filter').html("");
					$('#contentZone_FCMiddleColumn').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.FCLeftColumn", zone: "FCLeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.FCRightColumn", zone: "FCRightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.MiddleColumn", zone: "MiddleColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.ThirdRow", zone: "ThirdRow", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Filter", zone: "Filter", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.FCMiddleColumn", zone: "FCMiddleColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '3bfa44f6-031a-4afe-abd2-e250cabe3ac4':		
					$('#contentZone_LeftColumn').html("");
					$('#contentZone_RightColumn').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '729fbf9f-0f46-4cce-af5d-edca352bf633':		
					$('#contentZone_Filter').html("");
					$('#contentZone_Header').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.Filter", zone: "Filter", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.Header", zone: "Header", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case 'f77d0e62-49cd-40e5-a2a0-eeb8a988ea14':		
					$('#contentZone_MainZone').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.MainZone", zone: "MainZone", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
				case '9ab1d4bf-d45b-4271-9b31-f083233359a4':		
					$('#contentZone_RightColumn').html("");
					$('#contentZone_CenterColumn').html("");
					$('#contentZone_LeftColumn').html("");
					self.viewLoader.loadView({ viewName: pageName + "View.RightColumn", zone: "RightColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.CenterColumn", zone: "CenterColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					self.viewLoader.loadView({ viewName: pageName + "View.LeftColumn", zone: "LeftColumn", successHandler: self.onViewLoaded, errorHandler: self.onLoadViewError, context: pageName });
					break;
			}
			
			if (self.customApplicationController && self.customApplicationController.onAfterMasterPageReady) {
				self.customApplicationController.onAfterMasterPageReady(viewData, nodeName, zone);
			}
		};

        this.onViewLoaded = function (viewData, viewName, zone) {

			if(viewData !== "" && viewData.nodeName !== "#document") {	
				$('#contentZone_' + zone ).html(viewData);
			}
			
			self.numberZoneLoaded++;
			switch (window.masterPageId)
			{
				case '45c87622-dd4b-4d43-8706-08233b92884a':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case '091f77f5-9902-4b92-aaa9-0bb0efbafbd7':
					if (self.numberZoneLoaded < 4)
						return ;
					break;
				case '3b6597c4-b369-4714-8f89-0e540e6f7f6a':
					if (self.numberZoneLoaded < 4)
						return ;
					break;
				case '1dca4668-b198-43d3-831f-26617b60b9e0':
					if (self.numberZoneLoaded < 5)
						return ;
					break;
				case '04850fcf-cdfb-4006-961d-4088a369cf66':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '6a612a16-615e-4719-ba0e-41a743ba3de1':
					if (self.numberZoneLoaded < 3)
						return ;
					break;
				case '5a552132-89c4-421d-8ea9-513bdb6195d2':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '48ff0acc-fc0a-4c25-93a7-57d57339abe3':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case '8cfdba24-07b5-4ebd-8609-6191b3ed1af9':
					if (self.numberZoneLoaded < 5)
						return ;
					break;
				case '1ab1f14d-682e-4531-a5d9-65855587fc91':
					if (self.numberZoneLoaded < 3)
						return ;
					break;
				case '4c521798-87d1-4004-94f1-73cf4e70dfc4':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case 'e4187d06-dd89-4579-bb2b-8644ca7fc15c':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case '5e4caa8c-3b6a-4927-a7dd-90dbef3ff688':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case 'f433623a-c0d6-4785-8914-9f24f693c758':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '479c2e80-8704-4bfb-a084-b1b434ac8642':
					if (self.numberZoneLoaded < 3)
						return ;
					break;
				case '90931cd5-8bfc-446b-8eb7-bd68758481b7':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '8a0f0d25-5f58-4125-bb4c-cc43a1b3c3da':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '871e7b0f-99a1-4a0c-b704-cc5ac4240e05':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '82dfe12a-2316-4a3a-8479-d4224d408b4a':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '7c30d9c9-4cf5-4685-a5f9-d6b1a5f9e489':
					if (self.numberZoneLoaded < 8)
						return ;
					break;
				case '3bfa44f6-031a-4afe-abd2-e250cabe3ac4':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case '729fbf9f-0f46-4cce-af5d-edca352bf633':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case 'f77d0e62-49cd-40e5-a2a0-eeb8a988ea14':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '9ab1d4bf-d45b-4271-9b31-f083233359a4':
					if (self.numberZoneLoaded < 3)
						return ;
					break;
			}	

			if (self.sourcesLoaded === true)
			    self.onPageReady(viewName);
        };


		this.onPageRequiredSourcesAvailable = function (pageName) {
		    self.sourcesLoaded = true;

			switch (window.masterPageId)
			{
				case '45c87622-dd4b-4d43-8706-08233b92884a':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case '091f77f5-9902-4b92-aaa9-0bb0efbafbd7':
					if (self.numberZoneLoaded < 4)
						return ;
					break;
				case '3b6597c4-b369-4714-8f89-0e540e6f7f6a':
					if (self.numberZoneLoaded < 4)
						return ;
					break;
				case '1dca4668-b198-43d3-831f-26617b60b9e0':
					if (self.numberZoneLoaded < 5)
						return ;
					break;
				case '04850fcf-cdfb-4006-961d-4088a369cf66':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '6a612a16-615e-4719-ba0e-41a743ba3de1':
					if (self.numberZoneLoaded < 3)
						return ;
					break;
				case '5a552132-89c4-421d-8ea9-513bdb6195d2':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '48ff0acc-fc0a-4c25-93a7-57d57339abe3':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case '8cfdba24-07b5-4ebd-8609-6191b3ed1af9':
					if (self.numberZoneLoaded < 5)
						return ;
					break;
				case '1ab1f14d-682e-4531-a5d9-65855587fc91':
					if (self.numberZoneLoaded < 3)
						return ;
					break;
				case '4c521798-87d1-4004-94f1-73cf4e70dfc4':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case 'e4187d06-dd89-4579-bb2b-8644ca7fc15c':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case '5e4caa8c-3b6a-4927-a7dd-90dbef3ff688':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case 'f433623a-c0d6-4785-8914-9f24f693c758':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '479c2e80-8704-4bfb-a084-b1b434ac8642':
					if (self.numberZoneLoaded < 3)
						return ;
					break;
				case '90931cd5-8bfc-446b-8eb7-bd68758481b7':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '8a0f0d25-5f58-4125-bb4c-cc43a1b3c3da':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '871e7b0f-99a1-4a0c-b704-cc5ac4240e05':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '82dfe12a-2316-4a3a-8479-d4224d408b4a':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '7c30d9c9-4cf5-4685-a5f9-d6b1a5f9e489':
					if (self.numberZoneLoaded < 8)
						return ;
					break;
				case '3bfa44f6-031a-4afe-abd2-e250cabe3ac4':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case '729fbf9f-0f46-4cce-af5d-edca352bf633':
					if (self.numberZoneLoaded < 2)
						return ;
					break;
				case 'f77d0e62-49cd-40e5-a2a0-eeb8a988ea14':
					if (self.numberZoneLoaded < 1)
						return ;
					break;
				case '9ab1d4bf-d45b-4271-9b31-f083233359a4':
					if (self.numberZoneLoaded < 3)
						return ;
					break;
			}	

		    self.onPageReady(pageName);
		};
		
		this.onPageReady = function (pageName) {
			// Most page Loading errors occur while data-binding and loading component
			try {
				// add class to body for specific styling purpose
				var bodyClass = $('body').get(0).className.replace(/(?:^|\s)(page-|viewStyle-)(\S+)/g, ''); // reset class
				bodyClass += ' page-' + pageName;

				switch (pageName) {
					case "ServiceSettingsPagePage":
					case "VehilceItemsPage":
					case "CountryItemsPage":
					case "ModuleDetailsPagePage":
					case "ModelItemsPage":
					case "AlertReportPage":
					case "DriverPagePage":
					case "DepartmentItemsPage":
					case "CustomerItemsPage":
					case "TimezoneItemsPage":
					case "ReportSchedulerPage":
					case "PersonItemsPage":
					case "FeatureSubscriptionsPage":
					case "CustomerSSOPage":
					case "DevicesPage":
					case "FloorPlanManagementPage":
					case "FirmwaresPagePage":
					case "AccessGroupTemplatesPage":
					case "DealerItemsPage":
					case "RegionItemsPage":
					case "WebsiteusersPage":
					case "ExportPage":
					case "VehiclesGPSReportPage":
						bodyClass += ' viewStyle-list';
						break;
					case "CountryPage":
					case "RegionPage":
					case "MyaccountPage":
					case "ChecklistResultAnswersPagePage":
					case "ModulePagePage":
					case "TimezonePage":
					case "DealerPage":
					case "ModelPage":
					case "FloorPlanPagePage":
					case "GO2FAConfigurationPage":
					case "SessionDetailsPagePage":
					case "LiveMapPage":
					case "ServiceSettingsDetailsPage":
					case "PathAnalysisViewPage":
					case "WebsiteuserPage1":
					case "CustomerPage":
					case "VehicleDetailsPagePage":
					case "PersonDetailPagePage":
						bodyClass += ' viewStyle-detail';
						break;
				}

				$('body').attr('class', bodyClass);
 
				self.viewModel.pageController(FleetXQ.Web.Controllers[pageName + "Controller"](self));
				
				// Subscribe to title changes to update document title
				self.viewModel.pageTitle.subscribe(function(newTitle) {
					document.title = FleetXQ.Web.Messages.i18n.t(newTitle, self.viewModel.pageTitleParams());
				});
				self.viewModel.pageTitleParams.subscribe(function(newParams) {
					document.title = FleetXQ.Web.Messages.i18n.t(self.viewModel.pageTitle(), newParams);
				});

				// Manually trigger title update for current values (since subscriptions only fire on changes)
				document.title = FleetXQ.Web.Messages.i18n.t(self.viewModel.pageTitle(), self.viewModel.pageTitleParams());

				self.viewModel.updateMenu();
				self.removeOverlay(false);
              
				self.initToggleColumn();
              
				// we scroll back to top. because without this, chrome preserve the scroll position of previous page when navigating... However sideeffect is that when doing page back, scroll is reset to top where it would be better to preserve to previous version...
				window.scrollTo(0, 0);
			} catch (err) {
                $("#overlay").find(".overlayLoader-text").text(FleetXQ.Web.Messages.i18n.t('messages.genericPageError'))
		        throw err;
		    }
        };

		this.onLoadViewError = function (error, status) {
		    $("#overlay").find(".overlayLoader-text").text(FleetXQ.Web.Messages.i18n.t('messages.genericPageError') + " " + FleetXQ.Web.Messages.i18n.t('messages.status') + ": " + status + ", " + FleetXQ.Web.Messages.i18n.t('messages.error') + ": " + error);
		};
		
		this.errorHandler = function (jqXHR, errorThrown, errorHandler, additionalMessage, title) {
		
			// Give custom extension opportunity to handle the error first
			if (self.customApplicationController && self.customApplicationController.errorHandler) {
				if (self.customApplicationController.errorHandler(jqXHR, errorThrown, errorHandler, additionalMessage, title))
					return;
			}
			if (jqXHR.status === 401) {     // Unauthorized generic = navigate to log-in screen, unless is a log-in failure (which means we're already at log-in screen)
		        var resultError = JSON.parse(jqXHR.responseText);
		        if (resultError.instance && resultError.instance === "authenticationFailed") {
		            errorHandler(resultError.title);
		        }
		        else {
		            self.redirectToLoginPage();
		        }
		    }

            // 403 Forbidden - check why and handle accordingly
		    else if (jqXHR.status === 403) {      // Forbidden
		        var resultError = JSON.parse(jqXHR.responseText);

				// Is an active password policy forcing a password change?
				if (resultError.title.indexOf("passwordHasExpired") > -1) {
					// Redirect to the changePassword page, onpassing the reason (which includes the userId)
					var changePasswordUrl = FleetXQ.Web.Application.BaseURL + "Membership/changePassword.html?" + resultError.instance + window.location.hash;
					window.location.replace(changePasswordUrl);
					return;
				}
		        else if (resultError.instance){ 
                    // If was a security token error, redirect to log-in page, onpassing the context 'reason)
		            if (    resultError.instance === "expiredSecurityToken"
                        ||  resultError.instance === "invalidSecurityToken"
                        ||  resultError.instance === "nullSecurityToken"
						) 
					{ 
					    self.redirectToLoginPage(resultError.instance);
					}
					else {
						// Not a security token issue, which means it's a GO Role being denied access to entity / component / navigation node
						var title = title || FleetXQ.Web.Messages.i18n.t('messages.error');

						var consolemessage = self.formatConsoleErrorMessage(resultError, additionalMessage);
						console.warn(consolemessage);

					    if (errorHandler) {
							var userErrorMessage = self.formatUserErrorMessage(resultError, additionalMessage);
							errorHandler(userErrorMessage, resultError.title);
						}	
					}
		        }
				else {
					// Should never get here (means app is sending a 403 with incorrectly formatted response)
					console.warn("ApplicationController unexpected response with status 403");
					self.redirectToLoginPage();
				}
		    }
			else if (errorThrown !== "abort") {
		        var resultError = JSON.parse(jqXHR.responseText);

		        var title = title || FleetXQ.Web.Messages.i18n.t('messages.error');
				var consolemessage = self.formatConsoleErrorMessage(resultError, additionalMessage);
				console.warn(consolemessage);

		        if (errorHandler) {
					var userErrorMessage = self.formatUserErrorMessage(resultError, additionalMessage);
					errorHandler(userErrorMessage, title);
		        }
		    }
		};

		this.formatConsoleErrorMessage = function (resultError, additionalMessage) {
			var consolemessage = resultError.title + " : " + resultError.detail;
			consolemessage += additionalMessage ? ' - ' + additionalMessage : '';
			return consolemessage;
		};

		this.formatUserErrorMessage = function (resultError, additionalMessage) {
			var errorDetails = null;

			if (resultError.detail && resultError.detail != '') {
				errorDetails = "<details><summary>" + FleetXQ.Web.Messages.i18n.t('messages.showDetails') + "</summary>";

				if (additionalMessage) {
					errorDetails += "<p><span class='error-msg'>[" + additionalMessage + "]</span></p>"
				}

				errorDetails += "<p>" + resultError.detail + "</p>";

				errorDetails += "</details>";
			}

			return "<p>" + resultError.title + (errorDetails ? errorDetails : "") + "</p>";
		};

		/// <summary>
		/// Redirects to the login page while preserving context.
		/// If AuthenticationVirtualPath is configured, redirects to that virtual path.
		/// Otherwise, uses the current application's login page.
		/// </summary>
		/// <param name="prompt">Optional context message to display on login page</param>
		/// <param name="excludeHash">Whether to exclude the current hash from the redirect</param>
		this.redirectToLoginPage = function (prompt, excludeHash) {
		    var querystring = location.search;

			// Add prompt to query string if provided
			if (prompt) {
				if (querystring == "") {
					querystring += "?context=" + prompt;
				}
				else {
					querystring += "&context=" + prompt;
				}
			}

			// Clear dbKey if present

			// Get the authentication virtual path from configuration
			var authenticationVirtualPath = FleetXQ.Web.Application.AuthenticationVirtualPath;
			var loginurl;

			if (authenticationVirtualPath) {
				// Use the configured authentication virtual path
				// Get the origin (protocol + domain) from the base URL
				var origin = FleetXQ.Web.Application.BaseURL.match(/^https?:\/\/[^\/]+/)[0];
				loginurl = origin + authenticationVirtualPath + '/Membership/Login.html' + 
				          querystring + (excludeHash ? "" : window.location.hash);
			} else {
				// Use the current application's login page
				loginurl = FleetXQ.Web.Application.BaseURL + 
				          'Membership/Login.html' + 
				          querystring + (excludeHash ? "" : window.location.hash);
			}

			// Only redirect if we're not already on the login page
			if (window.location.href != loginurl) {
				window.location = loginurl;
			}
		};

		this.currentContextId = 0;

		this.getNextContextId = function () {
			self.currentContextId++;
			if (self.currentContextId === 9007199254740992)
				self.currentContextId = 0;

			return self.currentContextId;
		};

		// Popup Management 
		// Number of popups in stack
		this.popupCount = 0;
		// current popup id
		this.currentPopupId = null;
		// stack of popups 
		this.currentPopupStack = {};
		// stack of popups view models
		this.currentPopupViewModelStack = {};
		// true if at least one popup on the stack => overlay already set
		this.overlaySet = false;

		// Prepare the overlay and show popup container for showing popups
		this.prepareOverlay = function (withPopup, exitonclickout) {
			var resizeObserver = new ResizeObserver(() => {
				self.centerPopup();
			});

			resizeObserver.observe($("#popupContainer")[0]);
			if (self.overlaySet == false) {
		        var overlay = $("<div>")
                    .attr("id", "overlay")
                    .addClass("overlay");		        
                
		        $("body").append(overlay);

		        if (withPopup) {
		            $("#popupContainer").fadeIn();
					if (exitonclickout === true) {
						$("#overlay").click(function () { self.closeAllPopups(); });
					}
				} else {
					var overlayLoader = $("<div>")
                        .addClass("overlayLoader")
                        .append($("<div>").addClass("overlayLoader-image"))
                        .append($("<div>").text(FleetXQ.Web.Messages.i18n.t('messages.pageLoading')).addClass("overlayLoader-text"))
                        .appendTo(overlay);

		            overlayLoader.position({ my: "center top", at: "center top+100", of: overlay });
				}
		        self.overlaySet = true;
		    }
		};

		// Remove the overlay and hide popup container when no more popups to show
		this.removeOverlay = function (withPopup) {
		    if (self.overlaySet == true) {
		        document.body.removeChild(document.getElementById("overlay"));
		        if (withPopup)
		            $("#popupContainer").fadeOut();
		        self.overlaySet = false;
            }
		};

		// Non-intrusive Message boxes for positive feedback 
		this.showInfoMessageBox = function (htmlContent, title, isSticky) {
		    var noticeBox = new FleetXQ.Web.ViewModels.NoticeBoxViewModel(htmlContent, title, isSticky);
		    noticeBox.showAsMessageBox('info');
		};

		this.showErrorMessageBox = function (htmlContent, title, isSticky) {
		    var noticeBox = new FleetXQ.Web.ViewModels.NoticeBoxViewModel(htmlContent, title, isSticky);
		    noticeBox.showAsMessageBox('error');
		};

		this.showEditPopup = function (formName, caller, objectToEdit, isMemoryOnly, contextId, popupWidth, isOpenInEditMode, popupParameter) {
			// Displaying a temporary overlay while loading ressource		    
			self.prepareOverlay(false);
			self.ensureSourcesAndTranslations(formName,"Form", function()  {
				self.removeOverlay(); // removing temporary overlay
				var viewModel = self.preparePopup (formName, caller, contextId, popupWidth, null, isMemoryOnly);
				viewModel.isCreatePopup = false;
				viewModel.isOpenInEditMode = isOpenInEditMode;
				viewModel.popupParameter = popupParameter;
				objectToEdit.contextIds.push(viewModel.contextId);
				viewModel.showAsEditPopup(caller, objectToEdit);
			});
		};

		this.showCreateNewPopup = function (formName, caller, defaultObject, isMemoryOnly, contextId, popupWidth) {
			// Displaying a temporary overlay while loading ressource		    
			self.prepareOverlay(false);

			self.ensureSourcesAndTranslations(formName,"Form", function() {
				self.removeOverlay(); // removing temporary overlay
				var viewModel = self.preparePopup (formName, caller, contextId, popupWidth, null, isMemoryOnly);
				viewModel.isCreatePopup = true;
				defaultObject.contextIds.push(viewModel.contextId);
				self.ObjectsDataSet.setContextIdsDirty(defaultObject.contextIds);
				viewModel.showAsCreateNewPopup(caller, defaultObject);
			});
		};

		this.showAlertPopup = function (caller, message, title, callback, contextId) {
		    var viewModel = self.preparePopup("DialogPopup", caller, contextId, null, false);
		    viewModel.showAsDialog(caller, "alert", message, null, title, callback);
			self.centerPopup();
		};

		this.showConfirmPopup = function (caller, message, title, callback, contextId) {
		    var viewModel = self.preparePopup("DialogPopup", caller, contextId, null, false);
		    viewModel.showAsDialog(caller, "confirm", message, null, title, callback);
			self.centerPopup();
		};

		this.showPromptPopup = function (caller, message, valuePrompt, title, callback, contextId) {
		    var viewModel = self.preparePopup("DialogPopup", caller, contextId, null, false);
		    viewModel.showAsDialog(caller, "prompt", message, valuePrompt, title, callback);
			self.centerPopup();
		};

		// Update the back button, used to close current popup and back to previous
		this.updatePopupBackButton = function () {
		    if (self.currentPopupId === null)
		        return;

		    // if multiple popup => set proper style (will show back button)
		    if (self.currentPopupStack[self.currentPopupId - 1]) {
		        $("#popupContainer").addClass("hasMultiplePopups");
		        $("#popupContainer").removeClass("hasSinglePopup");
		    }
		    else {
		        $("#popupContainer").addClass("hasSinglePopup");
		        $("#popupContainer").removeClass("hasMultiplePopups");
		    }
		};


		// Prepare a new popup on the stack
		this.preparePopup = function (formName, caller, contextId, popupWidth, popupMaxHeight, isMemoryOnly) {
		    // instantiate a new popup container
		    var popupContainerName = "popupContainer" + self.popupCount;
		    self.popupCount++;
		    self.currentPopupId = self.popupCount;

		    var popupContainerContents = '<div id="' + popupContainerName + '" class="popupContainer"><div class="busy"></div></div>';
		    $("#popupContainer").append(popupContainerContents);
		    var $popupContainer = $("#" + popupContainerName);

			// Configuration de la largeur
		    if (!popupWidth) {
				popupWidth = "auto";
			}
		    
			$("#popupContainer").width(popupWidth);

			// Configuration de la hauteur
			if(!popupMaxHeight){
				popupMaxHeight = $(window).height() * 0.7;
			}
			
			$popupContainer.css("max-height", popupMaxHeight);
		    $popupContainer.css("overflow", "auto");
			$('html').css('overflow', 'hidden');

            var formNameParts = formName.split(".");
            var viewModelConstructor = FleetXQ.Web.ViewModels;

            for (var i = 0; i < formNameParts.length - 1; i++) {
                viewModelConstructor = viewModelConstructor[formNameParts[i]];
            }

            viewModelConstructor = viewModelConstructor[formNameParts[formNameParts.length - 1] + "ViewModel"];

            // create the popup View Model. The bindings are applied in the viewloader callback 
            var viewModel = new viewModelConstructor(caller.controller, null, null, $popupContainer, contextId, { popupWidth: popupWidth }, isMemoryOnly);

		    // push popup on popup stack
            self.currentPopupStack[self.popupCount] = $popupContainer;
            self.currentPopupViewModelStack[self.popupCount] = viewModel;

			 // hide previous popup if any
			if (self.currentPopupStack[self.currentPopupId - 1])
			    self.currentPopupStack[self.currentPopupId - 1].hide();

			self.updatePopupBackButton();
			
			var exitonclickout = formName === "ImagePopup";
			self.prepareOverlay(true, exitonclickout);

            return viewModel;
		};

		// Close all popups in stack
		this.closeAllPopups = function (rebindrequired) {
		    while (self.currentPopupId !== null) {
		        self.closeCurrentPopup(rebindrequired);
		    }
			$('html').css('overflow', '');
		};

		// Close current popup in stack
		this.closeCurrentPopup = function (rebindrequired) {
		    if (self.currentPopupId != null)
		        self.currentPopupViewModelStack[self.currentPopupId].onPopupClosed(rebindrequired);

		    var previousPopupId = null;

		    // delete old popup markup
			if(self.currentPopupStack[self.currentPopupId] && self.currentPopupStack[self.currentPopupId] !== null)
			{
			    ko.cleanNode(self.currentPopupStack[self.currentPopupId].get(0));
			    self.currentPopupStack[self.currentPopupId].remove();
			}


		    // remove old popup from stack
		    delete self.currentPopupStack[self.currentPopupId];
		    delete self.currentPopupViewModelStack[self.currentPopupId];

		    if (self.currentPopupStack[self.currentPopupId - 1]) {
		        self.currentPopupStack[self.currentPopupId - 1].fadeIn();
		        previousPopupId = self.currentPopupId - 1;
		    }
		    else {
		        previousPopupId = null;
		    }
            
		    self.currentPopupId = previousPopupId;
		    self.popupCount--;

			// If last popup => remove overlay
		    if (self.currentPopupId == null) {
		        self.removeOverlay(true);
				$('html').css('overflow', '');
		    }

		    self.updatePopupBackButton();
			if(self.currentPopupViewModelStack[self.currentPopupId])
				$("#popupContainer").width(self.currentPopupViewModelStack[self.currentPopupId].popupWidth);
		};

		this.centerPopup = function () {
		    $("#popupContainer").position({ my: "center top", at: "center top+100", of: window });
		};

		this.processCenterPopupOn = false;

        // maximize Popups
        this.maximizePopups = function () {
            $("#popupContainer").toggleClass('maximize');
            self.centerPopup();
        };
	
        // maximize Popups
        this.maximizePopups = function () {
          $("#popupContainer").toggleClass('maximize');
          self.centerPopup();
        };

		this.initToggleColumn = function () {
			$('.col-toggle a').each( function() {
				var colTarget = $(this.hash);
				var label = $(this).find("label");
				var title = colTarget.find('h4').first().text();
				label.html('Show ' + title);
				label.css("top",label.outerWidth()+'px');			
			});	

			$('.col-toggle').css({"display":"block", "top":"0"});
			$('.collapse-horizontal').css({"margin-right":"0", "margin-left":"0"}).collapse('show');
			$('.col-min-icon').css("opacity","1");
			$('.col-toggle').removeClass('collapsed');

		};
		this.toggleColumn = function (data, event) {
			var colTarget = $(event.currentTarget.hash);
			var toggle = $(event.currentTarget);
			var label = toggle.find("label");
			var parent = toggle.parent();


			if (label.text()=='Show ') {
				var title = colTarget.find('h4').first().text();
				label.html('Show ' + title);
				label.css("top",label.outerWidth()+'px');			
			}

			parent.toggleClass('collapsed');

			if (parent.hasClass('col-toggle-right')) {
				if (toggle.hasClass('collapsed')) { 
					parent.prev().find(".col-min-icon").css({"opacity":"0", "transition":"opacity 0s"});
					colTarget.prev().css({"margin-right":"35px", "transition":"margin-right 0.75s ease-out, width 0.35s ease"});
				} else { 
					parent.prev().find(".col-min-icon").css({"opacity":"1", "transition":"opacity 0s ease 0.5s"});
					colTarget.prev().css({"margin-right":"0", "transition":"margin-right 0.75s ease-out, width 0.35s ease"});
				}
			}
			if (parent.hasClass('col-toggle-left')) {
				if (toggle.hasClass('collapsed')) { 
					parent.next().find(".col-min-icon").css({"opacity":"0", "transition":"opacity 0s"});
					colTarget.next().css({"margin-left":"35px", "transition":"margin-left 0.75s ease-out, width 0.35s ease"});
				} else {
					parent.next().find(".col-min-icon").css({"opacity":"1", "transition":"opacity 0s ease 0.5s"});
					colTarget.next().css({"margin-left":"0", "transition":"margin-left 0.75s ease-out, width 0.35s ease"});
				}
			}
		};
      
		this.updateBreadCrumbs = function (nodeName) {
            self.viewModel.navigation.breadCrumbs.removeAll();
			var breadCrumbs = [];

            var root = '!' + self.getRootHashTag(nodeName);

            if (self.customRouting && self.customRouting.getRootBreadCrumb) {
                breadCrumbs.push(self.customRouting.getRootBreadCrumb());
            }


			switch(nodeName) {
				case 'VehicleCalibrationReport3':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.VehicleCalibrationReport3) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.VehicleCalibrationReport3();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.DealerReports2', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerReports2', hash: root + 'AdminSettings/DealerReports2', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.VehicleCalibrationReport3', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerReports2/VehicleCalibrationReport3', hash: root + 'AdminSettings/DealerReports2/VehicleCalibrationReport3', currentpage: true, isactive: false });
					}
					break;
				case 'UserEmailAlertSummary2':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.UserEmailAlertSummary2) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.UserEmailAlertSummary2();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.UserEmailAlertSummary2', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/UserEmailAlertSummary2', hash: root + 'Reports/MoreReports/UserEmailAlertSummary2', currentpage: true, isactive: false });
					}
					break;
				case 'UserDetail':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.UserDetail) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.UserDetail();
					}
					else {
						breadCrumbs.push({ text: 'navigation.UserManagement', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'UserManagement', hash: root + 'UserManagement', currentpage: false, isactive: true });
						breadCrumbs.push({ text: 'navigation.UserDetail', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'UserManagement/UserDetail', hash: root + 'UserManagement/UserDetail', currentpage: true, isactive: false });
					}
					break;
				case 'Reports':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Reports) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Reports();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: true, isactive: false });
					}
					break;
				case 'Dashboard':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Dashboard) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Dashboard();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Dashboard', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Dashboard', hash: root + 'Dashboard', currentpage: true, isactive: false });
					}
					break;
				case 'CountryItems':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.CountryItems) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.CountryItems();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.CountryItems', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/CountryItems', hash: root + 'AdminSettings/CountryItems', currentpage: true, isactive: false });
					}
					break;
				case 'PreOpCheckReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.PreOpCheckReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.PreOpCheckReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.PreOpCheckReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/PreOpCheckReport', hash: root + 'Reports/PreOpCheckReport', currentpage: true, isactive: false });
					}
					break;
				case 'DriverAccessAbuseReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.DriverAccessAbuseReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.DriverAccessAbuseReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.DriverAccessAbuseReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/DriverAccessAbuseReport', hash: root + 'Reports/MoreReports/DriverAccessAbuseReport', currentpage: true, isactive: false });
					}
					break;
				case 'VORReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.VORReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.VORReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.VORReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/VORReport', hash: root + 'Reports/MoreReports/VORReport', currentpage: true, isactive: false });
					}
					break;
				case 'CurrentStatusReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.CurrentStatusReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.CurrentStatusReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.CurrentStatusReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/CurrentStatusReport', hash: root + 'Reports/CurrentStatusReport', currentpage: true, isactive: false });
					}
					break;
				case 'FloorPlanManagement':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.FloorPlanManagement) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.FloorPlanManagement();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Slamcore', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore', hash: root + 'Slamcore', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.FloorPlanManagement', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore/FloorPlanManagement', hash: root + 'Slamcore/FloorPlanManagement', currentpage: true, isactive: false });
					}
					break;
				case 'FloorPlan':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.FloorPlan) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.FloorPlan();
					}
					else {
						breadCrumbs.push({ text: 'navigation.FloorPlan', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'FloorPlan', hash: root + 'FloorPlan', currentpage: true, isactive: false });
					}
					break;
				case 'Import':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Import) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Import();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.Import', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/Import', hash: root + 'AdminSettings/Import', currentpage: true, isactive: false });
					}
					break;
				case 'Vehicle':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Vehicle) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Vehicle();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Vehicle', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Vehicle', hash: root + 'Vehicle', currentpage: true, isactive: false });
					}
					break;
				case 'CountryDetails':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.CountryDetails) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.CountryDetails();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.CountryItems', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/CountryItems', hash: root + 'AdminSettings/CountryItems', currentpage: false, isactive: true });
						breadCrumbs.push({ text: 'navigation.CountryDetails', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/CountryItems/CountryDetails', hash: root + 'AdminSettings/CountryItems/CountryDetails', currentpage: true, isactive: false });
					}
					break;
				case 'NewUser':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.NewUser) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.NewUser();
					}
					else {
						breadCrumbs.push({ text: 'navigation.NewUser', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'NewUser', hash: root + 'NewUser', currentpage: true, isactive: false });
					}
					break;
				case 'Help':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Help) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Help();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Help', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Help', hash: root + 'Help', currentpage: true, isactive: false });
					}
					break;
				case 'RegionItems':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.RegionItems) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.RegionItems();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.RegionItems', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/RegionItems', hash: root + 'AdminSettings/RegionItems', currentpage: true, isactive: false });
					}
					break;
				case 'ServiceCheckReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.ServiceCheckReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.ServiceCheckReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.ServiceCheckReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/ServiceCheckReport', hash: root + 'Reports/ServiceCheckReport', currentpage: true, isactive: false });
					}
					break;
				case 'Vehicles':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Vehicles) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Vehicles();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Vehicles', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Vehicles', hash: root + 'Vehicles', currentpage: true, isactive: false });
					}
					break;
				case 'GO2FAConfiguration':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.GO2FAConfiguration) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.GO2FAConfiguration();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.GO2FAConfiguration', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/GO2FAConfiguration', hash: root + 'AdminSettings/GO2FAConfiguration', currentpage: true, isactive: false });
					}
					break;
				case 'UserManagement':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.UserManagement) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.UserManagement();
					}
					else {
						breadCrumbs.push({ text: 'navigation.UserManagement', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'UserManagement', hash: root + 'UserManagement', currentpage: true, isactive: false });
					}
					break;
				case 'Slamcore':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Slamcore) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Slamcore();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Slamcore', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore', hash: root + 'Slamcore', currentpage: true, isactive: false });
					}
					break;
				case 'RegionDetails':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.RegionDetails) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.RegionDetails();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.RegionItems', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/RegionItems', hash: root + 'AdminSettings/RegionItems', currentpage: false, isactive: true });
						breadCrumbs.push({ text: 'navigation.RegionDetails', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/RegionItems/RegionDetails', hash: root + 'AdminSettings/RegionItems/RegionDetails', currentpage: true, isactive: false });
					}
					break;
				case 'ChecklistResultAnswers':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.ChecklistResultAnswers) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.ChecklistResultAnswers();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.PreOpCheckReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/PreOpCheckReport', hash: root + 'Reports/PreOpCheckReport', currentpage: false, isactive: true });
						breadCrumbs.push({ text: 'navigation.ChecklistResultAnswers', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/PreOpCheckReport/ChecklistResultAnswers', hash: root + 'Reports/PreOpCheckReport/ChecklistResultAnswers', currentpage: true, isactive: false });
					}
					break;
				case 'AlertReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.AlertReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.AlertReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.AlertReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/AlertReport', hash: root + 'Reports/MoreReports/AlertReport', currentpage: true, isactive: false });
					}
					break;
				case 'DealerDetails':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.DealerDetails) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.DealerDetails();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.DealerItems', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerItems', hash: root + 'AdminSettings/DealerItems', currentpage: false, isactive: true });
						breadCrumbs.push({ text: 'navigation.DealerDetails', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerItems/DealerDetails', hash: root + 'AdminSettings/DealerItems/DealerDetails', currentpage: true, isactive: false });
					}
					break;
				case 'DataExportTool':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.DataExportTool) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.DataExportTool();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Slamcore', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore', hash: root + 'Slamcore', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.DataExportTool', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore/DataExportTool', hash: root + 'Slamcore/DataExportTool', currentpage: true, isactive: false });
					}
					break;
				case 'PedestrianDetectionReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.PedestrianDetectionReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.PedestrianDetectionReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.PedestrianDetectionReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/PedestrianDetectionReport', hash: root + 'Reports/MoreReports/PedestrianDetectionReport', currentpage: true, isactive: false });
					}
					break;
				case 'Firmware':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Firmware) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Firmware();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.Firmware', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/Firmware', hash: root + 'AdminSettings/Firmware', currentpage: true, isactive: false });
					}
					break;
				case 'VehicleHireDehireReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.VehicleHireDehireReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.VehicleHireDehireReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.DealerReports2', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerReports2', hash: root + 'AdminSettings/DealerReports2', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.VehicleHireDehireReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerReports2/VehicleHireDehireReport', hash: root + 'AdminSettings/DealerReports2/VehicleHireDehireReport', currentpage: true, isactive: false });
					}
					break;
				case 'ReportScheduler':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.ReportScheduler) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.ReportScheduler();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Slamcore', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore', hash: root + 'Slamcore', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.ReportScheduler', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore/ReportScheduler', hash: root + 'Slamcore/ReportScheduler', currentpage: true, isactive: false });
					}
					break;
				case 'Models':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Models) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Models();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.Models', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/Models', hash: root + 'AdminSettings/Models', currentpage: true, isactive: false });
					}
					break;
				case 'FeatureSubscriptions':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.FeatureSubscriptions) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.FeatureSubscriptions();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.FeatureSubscriptions', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/FeatureSubscriptions', hash: root + 'AdminSettings/FeatureSubscriptions', currentpage: true, isactive: false });
					}
					break;
				case 'MyAccount':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.MyAccount) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.MyAccount();
					}
					else {
						breadCrumbs.push({ text: 'navigation.MyAccount', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'MyAccount', hash: root + 'MyAccount', currentpage: true, isactive: false });
					}
					break;
				case 'MachineUnlockReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.MachineUnlockReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.MachineUnlockReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MachineUnlockReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MachineUnlockReport', hash: root + 'Reports/MachineUnlockReport', currentpage: true, isactive: false });
					}
					break;
				case 'CustomerSSO':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.CustomerSSO) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.CustomerSSO();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.CustomerSSO', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/CustomerSSO', hash: root + 'AdminSettings/CustomerSSO', currentpage: true, isactive: false });
					}
					break;
				case 'PathAnalysisView':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.PathAnalysisView) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.PathAnalysisView();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Slamcore', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore', hash: root + 'Slamcore', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.PathAnalysisView', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore/PathAnalysisView', hash: root + 'Slamcore/PathAnalysisView', currentpage: true, isactive: false });
					}
					break;
				case 'ConnectionStatusDashboard':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.ConnectionStatusDashboard) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.ConnectionStatusDashboard();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Slamcore', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore', hash: root + 'Slamcore', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.ConnectionStatusDashboard', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore/ConnectionStatusDashboard', hash: root + 'Slamcore/ConnectionStatusDashboard', currentpage: true, isactive: false });
					}
					break;
				case 'ProficiencyReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.ProficiencyReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.ProficiencyReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.ProficiencyReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/ProficiencyReport', hash: root + 'Reports/ProficiencyReport', currentpage: true, isactive: false });
					}
					break;
				case 'Devices':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Devices) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Devices();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Slamcore', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore', hash: root + 'Slamcore', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.Devices', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore/Devices', hash: root + 'Slamcore/Devices', currentpage: true, isactive: false });
					}
					break;
				case 'AdminSettings':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.AdminSettings) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.AdminSettings();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: true, isactive: false });
					}
					break;
				case 'TimezoneItems':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.TimezoneItems) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.TimezoneItems();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.TimezoneItems', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/TimezoneItems', hash: root + 'AdminSettings/TimezoneItems', currentpage: true, isactive: false });
					}
					break;
				case 'AccessGroupTemplates':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.AccessGroupTemplates) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.AccessGroupTemplates();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.AccessGroupTemplates', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/AccessGroupTemplates', hash: root + 'AdminSettings/AccessGroupTemplates', currentpage: true, isactive: false });
					}
					break;
				case 'MoreReports':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.MoreReports) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.MoreReports();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: true, isactive: false });
					}
					break;
				case 'SynchronizationStatusReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.SynchronizationStatusReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.SynchronizationStatusReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.SynchronizationStatusReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/SynchronizationStatusReport', hash: root + 'Reports/MoreReports/SynchronizationStatusReport', currentpage: true, isactive: false });
					}
					break;
				case 'BroadcastMessageReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.BroadcastMessageReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.BroadcastMessageReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.BroadcastMessageReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/BroadcastMessageReport', hash: root + 'Reports/MoreReports/BroadcastMessageReport', currentpage: true, isactive: false });
					}
					break;
				case 'Customers':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Customers) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Customers();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Customers', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Customers', hash: root + 'Customers', currentpage: true, isactive: false });
					}
					break;
				case 'Export':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.Export) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.Export();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.Export', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/Export', hash: root + 'Reports/Export', currentpage: true, isactive: false });
					}
					break;
				case 'UserSummaryReport2':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.UserSummaryReport2) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.UserSummaryReport2();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.DealerReports2', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerReports2', hash: root + 'AdminSettings/DealerReports2', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.UserSummaryReport2', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerReports2/UserSummaryReport2', hash: root + 'AdminSettings/DealerReports2/UserSummaryReport2', currentpage: true, isactive: false });
					}
					break;
				case 'OnDemandAuthorisationReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.OnDemandAuthorisationReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.OnDemandAuthorisationReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.OnDemandAuthorisationReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/OnDemandAuthorisationReport', hash: root + 'Reports/MoreReports/OnDemandAuthorisationReport', currentpage: true, isactive: false });
					}
					break;
				case 'VehicleCalibrationReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.VehicleCalibrationReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.VehicleCalibrationReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.VehicleCalibrationReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/VehicleCalibrationReport', hash: root + 'Reports/MoreReports/VehicleCalibrationReport', currentpage: true, isactive: false });
					}
					break;
				case 'DealerReports2':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.DealerReports2) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.DealerReports2();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.DealerReports2', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerReports2', hash: root + 'AdminSettings/DealerReports2', currentpage: true, isactive: false });
					}
					break;
				case 'LiveMap':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.LiveMap) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.LiveMap();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Slamcore', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore', hash: root + 'Slamcore', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.LiveMap', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Slamcore/LiveMap', hash: root + 'Slamcore/LiveMap', currentpage: true, isactive: false });
					}
					break;
				case 'DealerItems':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.DealerItems) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.DealerItems();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.DealerItems', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/DealerItems', hash: root + 'AdminSettings/DealerItems', currentpage: true, isactive: false });
					}
					break;
				case 'ImpactReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.ImpactReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.ImpactReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.ImpactReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/ImpactReport', hash: root + 'Reports/ImpactReport', currentpage: true, isactive: false });
					}
					break;
				case 'SuperAdmin':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.SuperAdmin) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.SuperAdmin();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.SuperAdmin', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/SuperAdmin', hash: root + 'AdminSettings/SuperAdmin', currentpage: true, isactive: false });
					}
					break;
				case 'LicenseExpiryReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.LicenseExpiryReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.LicenseExpiryReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.LicenseExpiryReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/LicenseExpiryReport', hash: root + 'Reports/MoreReports/LicenseExpiryReport', currentpage: true, isactive: false });
					}
					break;
				case 'VehiclesGPSReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.VehiclesGPSReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.VehiclesGPSReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.VehiclesGPSReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/VehiclesGPSReport', hash: root + 'Reports/MoreReports/VehiclesGPSReport', currentpage: true, isactive: false });
					}
					break;
				case 'GeneralProductivityReport':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.GeneralProductivityReport) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.GeneralProductivityReport();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.GeneralProductivityReport', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/GeneralProductivityReport', hash: root + 'Reports/GeneralProductivityReport', currentpage: true, isactive: false });
					}
					break;
				case 'EmailSubscriptionReport2':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.EmailSubscriptionReport2) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.EmailSubscriptionReport2();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Reports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports', hash: root + 'Reports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.MoreReports', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports', hash: root + 'Reports/MoreReports', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.EmailSubscriptionReport2', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Reports/MoreReports/EmailSubscriptionReport2', hash: root + 'Reports/MoreReports/EmailSubscriptionReport2', currentpage: true, isactive: false });
					}
					break;
				case 'CustomerDetails':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.CustomerDetails) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.CustomerDetails();
					}
					else {
						breadCrumbs.push({ text: 'navigation.Customers', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Customers', hash: root + 'Customers', currentpage: false, isactive: true });
						breadCrumbs.push({ text: 'navigation.CustomerDetails', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'Customers/CustomerDetails', hash: root + 'Customers/CustomerDetails', currentpage: true, isactive: false });
					}
					break;
				case 'TimezoneDetails':
					if (self.customRouting && self.customRouting.overridenBreadCrumbs && self.customRouting.overridenBreadCrumbs.TimezoneDetails) {
						breadCrumbs = self.customRouting.overridenBreadCrumbs.TimezoneDetails();
					}
					else {
						breadCrumbs.push({ text: 'navigation.AdminSettings', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings', hash: root + 'AdminSettings', currentpage: false, isactive: false });
						breadCrumbs.push({ text: 'navigation.TimezoneItems', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/TimezoneItems', hash: root + 'AdminSettings/TimezoneItems', currentpage: false, isactive: true });
						breadCrumbs.push({ text: 'navigation.TimezoneDetails', url: FleetXQ.Web.Application.BaseURL + '#' + root + 'AdminSettings/TimezoneItems/TimezoneDetails', hash: root + 'AdminSettings/TimezoneItems/TimezoneDetails', currentpage: true, isactive: false });
					}
					break;
			}

			for (var i = 0; i < breadCrumbs.length; i++) {
				if (i === breadCrumbs.length-1)
					breadCrumbs[i].currentpage = true;

				self.viewModel.navigation.breadCrumbs.push(breadCrumbs[i]);
			}
        };

        // Client-side routes
		Sammy(function () {
		    var sammycontext = this;
		    var root = '#!/';

		    if (self.customRouting && self.customRouting.registerRootRoute) {
		        root = self.customRouting.registerRootRoute(sammycontext);
		    }

          this.get(root + ':part1', function () {
              var doContinue = true;
                if (self.customRouting && self.customRouting.onBeforeParseForLevel) {
                    doContinue = self.customRouting.onBeforeParseForLevel(this, 1);
                }
               
                if (doContinue) {
                    if (self.customRouting && self.customRouting.parseAdditionnalRoutesForLevel) {
                        var isNavigatingCustom = self.customRouting.parseAdditionnalRoutesForLevel(this, 1);                        
                        if (isNavigatingCustom) return; // If custom routing has returned true, we stop navigating
                    }
                  if (this.params.part1.split(new RegExp("#", "g"))[0] == 'Reports') {
                        self.internalNavigateTo('Reports');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'Dashboard') {
                        self.internalNavigateTo('Dashboard');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'FloorPlan') {
                        self.internalNavigateTo('FloorPlan');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'Vehicle') {
                        self.internalNavigateTo('Vehicle');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'NewUser') {
                        self.internalNavigateTo('NewUser');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'Help') {
                        self.internalNavigateTo('Help');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'Vehicles') {
                        self.internalNavigateTo('Vehicles');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'UserManagement') {
                        self.internalNavigateTo('UserManagement');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'Slamcore') {
                        self.internalNavigateTo('Slamcore');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'MyAccount') {
                        self.internalNavigateTo('MyAccount');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'AdminSettings') {
                        self.internalNavigateTo('AdminSettings');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'Customers') {
                        self.internalNavigateTo('Customers');
                    }
                }
            }); 
          this.get(root + ':part1/:part2', function () {
              var doContinue = true;
                if (self.customRouting && self.customRouting.onBeforeParseForLevel) {
                    doContinue = self.customRouting.onBeforeParseForLevel(this, 2);
                }
               
                if (doContinue) {
                    if (self.customRouting && self.customRouting.parseAdditionnalRoutesForLevel) {
                        var isNavigatingCustom = self.customRouting.parseAdditionnalRoutesForLevel(this, 2);                        
                        if (isNavigatingCustom) return; // If custom routing has returned true, we stop navigating
                    }
                  if (this.params.part1 == 'UserManagement' && this.params.part2.split(new RegExp("#", "g"))[0] == 'UserDetail') {
                        self.internalNavigateTo('UserDetail');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'CountryItems') {
                        self.internalNavigateTo('CountryItems');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2.split(new RegExp("#", "g"))[0] == 'PreOpCheckReport') {
                        self.internalNavigateTo('PreOpCheckReport');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2.split(new RegExp("#", "g"))[0] == 'CurrentStatusReport') {
                        self.internalNavigateTo('CurrentStatusReport');
                    }
                  else if (this.params.part1 == 'Slamcore' && this.params.part2.split(new RegExp("#", "g"))[0] == 'FloorPlanManagement') {
                        self.internalNavigateTo('FloorPlanManagement');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'Import') {
                        self.internalNavigateTo('Import');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'RegionItems') {
                        self.internalNavigateTo('RegionItems');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2.split(new RegExp("#", "g"))[0] == 'ServiceCheckReport') {
                        self.internalNavigateTo('ServiceCheckReport');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'GO2FAConfiguration') {
                        self.internalNavigateTo('GO2FAConfiguration');
                    }
                  else if (this.params.part1 == 'Slamcore' && this.params.part2.split(new RegExp("#", "g"))[0] == 'DataExportTool') {
                        self.internalNavigateTo('DataExportTool');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'Firmware') {
                        self.internalNavigateTo('Firmware');
                    }
                  else if (this.params.part1 == 'Slamcore' && this.params.part2.split(new RegExp("#", "g"))[0] == 'ReportScheduler') {
                        self.internalNavigateTo('ReportScheduler');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'Models') {
                        self.internalNavigateTo('Models');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'FeatureSubscriptions') {
                        self.internalNavigateTo('FeatureSubscriptions');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2.split(new RegExp("#", "g"))[0] == 'MachineUnlockReport') {
                        self.internalNavigateTo('MachineUnlockReport');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'CustomerSSO') {
                        self.internalNavigateTo('CustomerSSO');
                    }
                  else if (this.params.part1 == 'Slamcore' && this.params.part2.split(new RegExp("#", "g"))[0] == 'PathAnalysisView') {
                        self.internalNavigateTo('PathAnalysisView');
                    }
                  else if (this.params.part1 == 'Slamcore' && this.params.part2.split(new RegExp("#", "g"))[0] == 'ConnectionStatusDashboard') {
                        self.internalNavigateTo('ConnectionStatusDashboard');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2.split(new RegExp("#", "g"))[0] == 'ProficiencyReport') {
                        self.internalNavigateTo('ProficiencyReport');
                    }
                  else if (this.params.part1 == 'Slamcore' && this.params.part2.split(new RegExp("#", "g"))[0] == 'Devices') {
                        self.internalNavigateTo('Devices');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'TimezoneItems') {
                        self.internalNavigateTo('TimezoneItems');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'AccessGroupTemplates') {
                        self.internalNavigateTo('AccessGroupTemplates');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2.split(new RegExp("#", "g"))[0] == 'MoreReports') {
                        self.internalNavigateTo('MoreReports');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2.split(new RegExp("#", "g"))[0] == 'Export') {
                        self.internalNavigateTo('Export');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'DealerReports2') {
                        self.internalNavigateTo('DealerReports2');
                    }
                  else if (this.params.part1 == 'Slamcore' && this.params.part2.split(new RegExp("#", "g"))[0] == 'LiveMap') {
                        self.internalNavigateTo('LiveMap');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'DealerItems') {
                        self.internalNavigateTo('DealerItems');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2.split(new RegExp("#", "g"))[0] == 'ImpactReport') {
                        self.internalNavigateTo('ImpactReport');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'SuperAdmin') {
                        self.internalNavigateTo('SuperAdmin');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2.split(new RegExp("#", "g"))[0] == 'GeneralProductivityReport') {
                        self.internalNavigateTo('GeneralProductivityReport');
                    }
                  else if (this.params.part1 == 'Customers' && this.params.part2.split(new RegExp("#", "g"))[0] == 'CustomerDetails') {
                        self.internalNavigateTo('CustomerDetails');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'FloorPlan') {
                        self.internalNavigateTo('FloorPlan');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'Vehicle') {
                        self.internalNavigateTo('Vehicle');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'NewUser') {
                        self.internalNavigateTo('NewUser');
                    }
                  else if (this.params.part1.split(new RegExp("#", "g"))[0] == 'MyAccount') {
                        self.internalNavigateTo('MyAccount');
                    }
                }
            }); 
          this.get(root + ':part1/:part2/:part3', function () {
              var doContinue = true;
                if (self.customRouting && self.customRouting.onBeforeParseForLevel) {
                    doContinue = self.customRouting.onBeforeParseForLevel(this, 3);
                }
               
                if (doContinue) {
                    if (self.customRouting && self.customRouting.parseAdditionnalRoutesForLevel) {
                        var isNavigatingCustom = self.customRouting.parseAdditionnalRoutesForLevel(this, 3);                        
                        if (isNavigatingCustom) return; // If custom routing has returned true, we stop navigating
                    }
                  if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'DealerReports2' && this.params.part3.split(new RegExp("#", "g"))[0] == 'VehicleCalibrationReport3') {
                        self.internalNavigateTo('VehicleCalibrationReport3');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'UserEmailAlertSummary2') {
                        self.internalNavigateTo('UserEmailAlertSummary2');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'DriverAccessAbuseReport') {
                        self.internalNavigateTo('DriverAccessAbuseReport');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'VORReport') {
                        self.internalNavigateTo('VORReport');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'CountryItems' && this.params.part3.split(new RegExp("#", "g"))[0] == 'CountryDetails') {
                        self.internalNavigateTo('CountryDetails');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'RegionItems' && this.params.part3.split(new RegExp("#", "g"))[0] == 'RegionDetails') {
                        self.internalNavigateTo('RegionDetails');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'PreOpCheckReport' && this.params.part3.split(new RegExp("#", "g"))[0] == 'ChecklistResultAnswers') {
                        self.internalNavigateTo('ChecklistResultAnswers');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'AlertReport') {
                        self.internalNavigateTo('AlertReport');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'DealerItems' && this.params.part3.split(new RegExp("#", "g"))[0] == 'DealerDetails') {
                        self.internalNavigateTo('DealerDetails');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'PedestrianDetectionReport') {
                        self.internalNavigateTo('PedestrianDetectionReport');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'DealerReports2' && this.params.part3.split(new RegExp("#", "g"))[0] == 'VehicleHireDehireReport') {
                        self.internalNavigateTo('VehicleHireDehireReport');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'SynchronizationStatusReport') {
                        self.internalNavigateTo('SynchronizationStatusReport');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'BroadcastMessageReport') {
                        self.internalNavigateTo('BroadcastMessageReport');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'DealerReports2' && this.params.part3.split(new RegExp("#", "g"))[0] == 'UserSummaryReport2') {
                        self.internalNavigateTo('UserSummaryReport2');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'OnDemandAuthorisationReport') {
                        self.internalNavigateTo('OnDemandAuthorisationReport');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'VehicleCalibrationReport') {
                        self.internalNavigateTo('VehicleCalibrationReport');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'LicenseExpiryReport') {
                        self.internalNavigateTo('LicenseExpiryReport');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'VehiclesGPSReport') {
                        self.internalNavigateTo('VehiclesGPSReport');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'MoreReports' && this.params.part3.split(new RegExp("#", "g"))[0] == 'EmailSubscriptionReport2') {
                        self.internalNavigateTo('EmailSubscriptionReport2');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'TimezoneItems' && this.params.part3.split(new RegExp("#", "g"))[0] == 'TimezoneDetails') {
                        self.internalNavigateTo('TimezoneDetails');
                    }
                  else if (this.params.part1 == 'UserManagement' && this.params.part2.split(new RegExp("#", "g"))[0] == 'UserDetail') {
                        self.internalNavigateTo('UserDetail');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2.split(new RegExp("#", "g"))[0] == 'GO2FAConfiguration') {
                        self.internalNavigateTo('GO2FAConfiguration');
                    }
                  else if (this.params.part1 == 'Slamcore' && this.params.part2.split(new RegExp("#", "g"))[0] == 'PathAnalysisView') {
                        self.internalNavigateTo('PathAnalysisView');
                    }
                  else if (this.params.part1 == 'Slamcore' && this.params.part2.split(new RegExp("#", "g"))[0] == 'LiveMap') {
                        self.internalNavigateTo('LiveMap');
                    }
                  else if (this.params.part1 == 'Customers' && this.params.part2.split(new RegExp("#", "g"))[0] == 'CustomerDetails') {
                        self.internalNavigateTo('CustomerDetails');
                    }
                }
            }); 
          this.get(root + ':part1/:part2/:part3/:part4', function () {
              var doContinue = true;
                if (self.customRouting && self.customRouting.onBeforeParseForLevel) {
                    doContinue = self.customRouting.onBeforeParseForLevel(this, 4);
                }
               
                if (doContinue) {
                    if (self.customRouting && self.customRouting.parseAdditionnalRoutesForLevel) {
                        var isNavigatingCustom = self.customRouting.parseAdditionnalRoutesForLevel(this, 4);                        
                        if (isNavigatingCustom) return; // If custom routing has returned true, we stop navigating
                    }
                  if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'CountryItems' && this.params.part3.split(new RegExp("#", "g"))[0] == 'CountryDetails') {
                        self.internalNavigateTo('CountryDetails');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'RegionItems' && this.params.part3.split(new RegExp("#", "g"))[0] == 'RegionDetails') {
                        self.internalNavigateTo('RegionDetails');
                    }
                  else if (this.params.part1 == 'Reports' && this.params.part2 == 'PreOpCheckReport' && this.params.part3.split(new RegExp("#", "g"))[0] == 'ChecklistResultAnswers') {
                        self.internalNavigateTo('ChecklistResultAnswers');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'DealerItems' && this.params.part3.split(new RegExp("#", "g"))[0] == 'DealerDetails') {
                        self.internalNavigateTo('DealerDetails');
                    }
                  else if (this.params.part1 == 'AdminSettings' && this.params.part2 == 'TimezoneItems' && this.params.part3.split(new RegExp("#", "g"))[0] == 'TimezoneDetails') {
                        self.internalNavigateTo('TimezoneDetails');
                    }
                }
            }); 
			
			if (self.customRouting && self.customRouting.additionnalRoutesParser) {
				// Parse additionnal routes
				for (var level in self.customRouting.additionnalRoutesParser) {
					var levelasint = parseInt(level);
					if (levelasint <= 4)
						continue;

					var levelarray = [];
					for (var i = 0; i < levelasint; i++)
						levelarray.push(i + 1);

					var path = "#!/:part" + levelarray.join("/:part");

					sammycontext.get(path, function () {
						self.customRouting.additionnalRoutesParser[level].parse(this);
					});
				}
			}

			// When leaving a page, make sur all the popup and menus are closed.
			this.before("", function () {
			    if (self.currentPopupId != null) {
			        self.closeAllPopups(false);
			    }
				if ($.sidr && GO.Web.GetEnvironment().isMobile) {
			        $.sidr('close', 'horizontal-menu');
			    }
			});
		
		});

		this.initialize = function () {
		    // Load global sources and translations
		    self.ensureSourcesAndTranslations(null, "Global", self.onInitialized);
		};

		this.onInitialized = function () {
			if (self.customApplicationController && self.customApplicationController.initialize) {
				self.customApplicationController.initialize();
			}
			else {
				self.onCustomInitialized();
			}
		};

		this.onCustomInitialized = function () {
			Sammy().run();
		
			FleetXQ.Web.Application.contextId = [self.getNextContextId()];
			if (!location.hash) {
				self.navigateTo("Dashboard");
			}
		}

		// Method to ensure both sources and translations are loaded
		this.ensureSourcesAndTranslations = function(elementName, elementType, callback) {
			let jsLoaded = false;
			let translationsLoaded = false;
        
			// Check if both are loaded and call callback if so
			function checkBothLoaded() {
				if (jsLoaded && translationsLoaded && callback) {
					callback(elementName);
				}
			}
        
			// Load JavaScript sources
			self.sourceHandler.EnsureSourcesForElement(elementName, elementType, function() {
				jsLoaded = true;
				checkBothLoaded();
			});
        
			// Load translations in parallel
			self.translationHandler.ensureNamespacesForElement(elementName, elementType, function() {
				translationsLoaded = true;
				checkBothLoaded();
			});
		};

		this.initialize();
    };
} (window));

