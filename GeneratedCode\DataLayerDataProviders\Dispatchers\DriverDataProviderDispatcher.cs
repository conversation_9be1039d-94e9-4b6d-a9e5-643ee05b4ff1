﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.Data.DataProviders.Dispatchers
{
    public class DriverDataProviderDispatcher : IDataProviderDispatcher<DriverDataObject>
    {
		protected IServiceProvider _serviceProvider;
		
		public DriverDataProviderDispatcher(IServiceProvider provider)
		{
  			_serviceProvider = provider;
		}
		
		protected IDataProvider<CurrentStatusDriverViewDataObject> currentStatusDriverViewDataProvider => _serviceProvider.GetService<IDataProvider<CurrentStatusDriverViewDataObject>>();
		protected IDataProvider<VehicleLockoutDataObject> vehicleLockoutDataProvider => _serviceProvider.GetService<IDataProvider<VehicleLockoutDataObject>>();
		protected IDataProvider<OnDemandSessionDataObject> onDemandSessionDataProvider => _serviceProvider.GetService<IDataProvider<OnDemandSessionDataObject>>();
		protected IDataProvider<DepartmentDataObject> departmentDataProvider => _serviceProvider.GetService<IDataProvider<DepartmentDataObject>>();
		protected IDataProvider<PedestrianDetectionHistoryDataObject> pedestrianDetectionHistoryDataProvider => _serviceProvider.GetService<IDataProvider<PedestrianDetectionHistoryDataObject>>();
		protected IDataProvider<DetailedSessionViewDataObject> detailedSessionViewDataProvider => _serviceProvider.GetService<IDataProvider<DetailedSessionViewDataObject>>();
		protected IDataProvider<GeneralProductivityPerDriverViewLatestDataObject> generalProductivityPerDriverViewLatestDataProvider => _serviceProvider.GetService<IDataProvider<GeneralProductivityPerDriverViewLatestDataObject>>();
		protected IDataProvider<LicenceDetailDataObject> licenceDetailDataProvider => _serviceProvider.GetService<IDataProvider<LicenceDetailDataObject>>();
		protected IDataProvider<ImpactsForVehicleViewDataObject> impactsForVehicleViewDataProvider => _serviceProvider.GetService<IDataProvider<ImpactsForVehicleViewDataObject>>();
		protected IDataProvider<AllLicenseExpiryViewDataObject> allLicenseExpiryViewDataProvider => _serviceProvider.GetService<IDataProvider<AllLicenseExpiryViewDataObject>>();
		protected IDataProvider<SiteDataObject> siteDataProvider => _serviceProvider.GetService<IDataProvider<SiteDataObject>>();
		protected IDataProvider<LicenseByModelDataObject> licenseByModelDataProvider => _serviceProvider.GetService<IDataProvider<LicenseByModelDataObject>>();
		protected IDataProvider<DriverProficiencyViewDataObject> driverProficiencyViewDataProvider => _serviceProvider.GetService<IDataProvider<DriverProficiencyViewDataObject>>();
		protected IDataProvider<SessionDataObject> sessionDataProvider => _serviceProvider.GetService<IDataProvider<SessionDataObject>>();
		protected IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject> allDriverAccessAbuseStoreProcedureDataProvider => _serviceProvider.GetService<IDataProvider<AllDriverAccessAbuseStoreProcedureDataObject>>();
		protected IDataProvider<CardDataObject> cardDataProvider => _serviceProvider.GetService<IDataProvider<CardDataObject>>();
		protected IDataProvider<CustomerDataObject> customerDataProvider => _serviceProvider.GetService<IDataProvider<CustomerDataObject>>();
		protected IDataProvider<PersonDataObject> personDataProvider => _serviceProvider.GetService<IDataProvider<PersonDataObject>>();
		protected IDataProvider<BroadcastMessageHistoryDataObject> broadcastMessageHistoryDataProvider => _serviceProvider.GetService<IDataProvider<BroadcastMessageHistoryDataObject>>();

        public async Task DispatchForEntityAsync(DriverDataObject entity, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("Driver", parameters);

            foreach (var include in includes)
            {
	
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
                  case "currentstatusdriverviewitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("CurrentStatusDriverViewItems"))
									break;

								try
								{
									var objectToFetch = await currentStatusDriverViewDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "vehiclelockouts":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("VehicleLockouts"))
									break;

								try
								{
									var objectToFetch = await vehicleLockoutDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "ondemandsessionitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("OnDemandSessionItems"))
									break;

								try
								{
									var objectToFetch = await onDemandSessionDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "department":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Department"))
									break;

								if (entity.DepartmentId != null) 
								{
									try
									{
										var objectToFetch = await departmentDataProvider.GetAsync(new DepartmentDataObject((System.Guid)entity.DepartmentId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "pedestriandetectionhistoryitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("PedestrianDetectionHistoryItems"))
									break;

								try
								{
									var objectToFetch = await pedestrianDetectionHistoryDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "detailedsessionviewitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DetailedSessionViewItems"))
									break;

								try
								{
									var objectToFetch = await detailedSessionViewDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "generalproductivityperdriverviewlatestitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("GeneralProductivityPerDriverViewLatestItems"))
									break;

								try
								{
									var objectToFetch = await generalProductivityPerDriverViewLatestDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "generallicence":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("GeneralLicence"))
									break;

								if (entity.LicenceDetailId != null) 
								{
									try
									{
										var objectToFetch = await licenceDetailDataProvider.GetAsync(new LicenceDetailDataObject((System.Guid)entity.LicenceDetailId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "impactsforvehicleviewitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("ImpactsForVehicleViewItems"))
									break;

								try
								{
									var objectToFetch = await impactsForVehicleViewDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "alllicenseexpiryviewitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("AllLicenseExpiryViewItems"))
									break;

								try
								{
									var objectToFetch = await allLicenseExpiryViewDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "site":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Site"))
									break;

								if (entity.SiteId != null) 
								{
									try
									{
										var objectToFetch = await siteDataProvider.GetAsync(new SiteDataObject((System.Guid)entity.SiteId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "licensesbymodel":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("LicensesByModel"))
									break;

								try
								{
									var objectToFetch = await licenseByModelDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "driverproficiencyviewitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("DriverProficiencyViewItems"))
									break;

								try
								{
									var objectToFetch = await driverProficiencyViewDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "sessions":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Sessions"))
									break;

								try
								{
									var objectToFetch = await sessionDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "alldriveraccessabusestoreprocedureitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("AllDriverAccessAbuseStoreProcedureItems"))
									break;

								try
								{
									var objectToFetch = await allDriverAccessAbuseStoreProcedureDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "card":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Card"))
									break;

								if (entity.CardDetailsId != null) 
								{
									try
									{
										var objectToFetch = await cardDataProvider.GetAsync(new CardDataObject((System.Guid)entity.CardDetailsId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "customer":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Customer"))
									break;

								if (entity.CustomerId != null) 
								{
									try
									{
										var objectToFetch = await customerDataProvider.GetAsync(new CustomerDataObject((System.Guid)entity.CustomerId), null, subincludes, context, parameters, skipSecurity);
										if (objectToFetch != null) 
										{
											entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
										}
									}
									catch (GOServerException e)
									{
										if (e.Reason != "accessDenied")
											throw;
									}
								}
								break;
							}
                  case "person":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("Person"))
									break;

								try
								{
									var objectToFetch = await personDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
                  case "broadcastmessagehistoryitems":
							{
								// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
								if (prefetches.Contains("BroadcastMessageHistoryItems"))
									break;

								try
								{
									var objectToFetch = await broadcastMessageHistoryDataProvider.GetCollectionAsync(null, "DriverId == @0",  new object[] { entity.Id }, null, 0, 0, subincludes, context, parameters, skipSecurity);
									if (objectToFetch != null) 
									{
										entity.ObjectsDataSet.Merge(objectToFetch.ObjectsDataSet);
									}
								}
								catch (GOServerException e)
								{
									if (e.Reason != "accessDenied")
										throw;
								}
								break;
							}
						default:
							throw new ApplicationException("Driver Entity has no relation named " + relation);
					}
          }        
		}

        public async Task DispatchForEntityCollectionAsync(IEnumerable<DriverDataObject> entities, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters, bool skipSecurity = false)
        {
			// Remember includes we've already dispatched so as to avoid multiple data fetches
			var dispatched = new HashSet<string>();

			// get (custom) prefetch list so we can skip the dispatch for stuff we already fetched
			var prefetches = PrefetchAssociations.Get("Driver", parameters);

            foreach (var include in includes)
            {
					string relation = include.Split('.').First().ToLower();
					var subincludes = DispatchPath.GetSubIncludes(relation, includes);

					if (relation.Contains(":"))
						relation = relation.Substring(relation.IndexOf(':') + 1);

					if (dispatched.Contains(relation))
						continue;

					dispatched.Add(relation);

					switch (relation)
					{
						case "currentstatusdriverviewitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("CurrentStatusDriverViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await currentStatusDriverViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "vehiclelockouts":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("VehicleLockouts"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await vehicleLockoutDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "ondemandsessionitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("OnDemandSessionItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await onDemandSessionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "department":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Department"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.DepartmentId != null).Select(e => (System.Guid)e.DepartmentId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await departmentDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "pedestriandetectionhistoryitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("PedestrianDetectionHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await pedestrianDetectionHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "detailedsessionviewitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DetailedSessionViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await detailedSessionViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "generalproductivityperdriverviewlatestitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("GeneralProductivityPerDriverViewLatestItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await generalProductivityPerDriverViewLatestDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "generallicence":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("GeneralLicence"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.LicenceDetailId != null).Select(e => (System.Guid)e.LicenceDetailId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await licenceDetailDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "impactsforvehicleviewitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("ImpactsForVehicleViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await impactsForVehicleViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "alllicenseexpiryviewitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("AllLicenseExpiryViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await allLicenseExpiryViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "site":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Site"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.SiteId != null).Select(e => (System.Guid)e.SiteId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await siteDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "licensesbymodel":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("LicensesByModel"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await licenseByModelDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "driverproficiencyviewitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("DriverProficiencyViewItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await driverProficiencyViewDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "sessions":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Sessions"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await sessionDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "alldriveraccessabusestoreprocedureitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("AllDriverAccessAbuseStoreProcedureItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await allDriverAccessAbuseStoreProcedureDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "card":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Card"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.CardDetailsId != null).Select(e => (System.Guid)e.CardDetailsId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await cardDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "customer":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Customer"))
								break;

							var filterparameters = new object[] { entities.Where(e => e.CustomerId != null).Select(e => (System.Guid)e.CustomerId).Distinct().ToArray() } ;
							try
							{
								entities.First().ObjectsDataSet.Merge((await customerDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.Id))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "person":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("Person"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await personDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
						case "broadcastmessagehistoryitems":
                        {
							// custom code can implement IPrefetch<ORMDriver> and add prefetch info through PrefetchAssociations helper => if set, we skip the dispatch-fetch
							if (prefetches.Contains("BroadcastMessageHistoryItems"))
								break;

							var filterparameters = new object[] { entities.Select(e => e.Id).Distinct().ToArray() } ; 
							try
							{
								entities.First().ObjectsDataSet.Merge((await broadcastMessageHistoryDataProvider.GetCollectionAsync(null, "(@0.Contains(outerIt.DriverId.Value))", filterparameters, null, 0, 0, subincludes, context, parameters, skipSecurity)).ObjectsDataSet);
							}
							catch (GOServerException e)
							{
								if (e.Reason != "accessDenied")
									throw;
							}
							break;
						}
                  default:
                        throw new ApplicationException("Driver Entity has no relation named " + relation);
					}
            }        
        }
	}
}