﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using GenerativeObjects.Practices.Settings;
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.Logging;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Newtonsoft.Json;
using System.Xml.Serialization;
using System.Collections.Specialized;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Concurrent;
using GenerativeObjects.Practices.LayerSupportClasses;
 
using Parameters = System.Collections.Generic.Dictionary<string, object>;


namespace FleetXQ.Data.DataObjects
{
	/// <summary>
	/// DataObject class for the entity 'Session'.
	/// </summary>
    [JsonObject(MemberSerialization.OptIn)]
	public partial class SessionDataObject : DataObject
	{
		#region dependencies

		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional. Call GetService instead of GetRequiredService
		protected IDataProviderTransaction _dataProviderTransaction => _serviceProvider.GetService<IDataProviderTransaction>();
		#endregion

		#region Fields
		// In case of SDK layer => No transaction. Therefore IDataProviderTransaction is optional
		public virtual Parameters CurrentTransactionParameters => _dataProviderTransaction?.Parameters;
 
		[JsonProperty ("DriverId")]
		protected System.Guid _driverId;
		[JsonProperty ("EndTime")]
		protected Nullable<System.DateTime> _endTime;
		[JsonProperty("EndTime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _endTime_WithTimezoneOffset;
		[JsonProperty ("Id")]
		protected System.Guid _id = Guid.NewGuid();
		[JsonProperty ("isVOR")]
		protected Nullable<System.Boolean> _isVOR;
		[JsonProperty ("StartTime")]
		protected System.DateTime _startTime;
		[JsonProperty("StartTime_WithTimezoneOffset")]
		protected System.DateTimeOffset? _startTime_WithTimezoneOffset;
		[JsonProperty ("VehicleId")]
		protected System.Guid _vehicleId;
	
	
		// fields to store relation Ids when relating to new objects (with no PK set yet)



		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _driver_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_driver_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }






		[JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
		protected internal virtual int? _vehicle_NewObjectId { get; set; }
        
		public virtual bool ShouldSerialize_vehicle_NewObjectId()
        {
            return ObjectsDataSet != null && ObjectsDataSet.SerializeTechnicalProperties;
        }





		#endregion
		
		#region initialization
		
		[ActivatorUtilitiesConstructor]
		public SessionDataObject(IServiceProvider serviceProvider) : base (serviceProvider)
		{
			SetisVORValue(false, false, false);
		}

		/// <summary>
		/// Main Constructor
		/// </summary>
		public SessionDataObject(System.Guid id)
		{
			this.Initialize(id);
		}

		public SessionDataObject Initialize(System.Guid id)
		{
			this._id = id;
			SetisVORValue(false, false, false);
			return this;
		}

		/// <summary>
		/// Copy Constructor
		/// </summary>
		public SessionDataObject Initialize(SessionDataObject template, bool deepCopy)
		{
			this.SetEndTimeValue(template.EndTime, false, false);
			this._endTime_WithTimezoneOffset = template._endTime_WithTimezoneOffset;
			this.SetStartTimeValue(template.StartTime, false, false);
			this._startTime_WithTimezoneOffset = template._startTime_WithTimezoneOffset;
			this.SetDriverIdValue(template.DriverId, false, false);
			this.SetIdValue(template.Id, false, false);
			this.SetisVORValue(template.isVOR, false, false);
			this.SetVehicleIdValue(template.VehicleId, false, false);
 
 
 
 
			this._driver_NewObjectId = template._driver_NewObjectId;
 
 
 
 
 
			this._vehicle_NewObjectId = template._vehicle_NewObjectId;
 
 
 
 
			this.SetIsNewValue(template.IsNew, false, false);

			if (deepCopy)
			{

				this.ObjectsDataSet = template.ObjectsDataSet.Clone();
				// Remove the template object from the dataset
                this.ObjectsDataSet.RemoveObject(template);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}
		
			this.SetIsDirtyValue(template.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(template.IsMarkedForDeletion, false, false);

			return this;
		}

		public virtual SessionDataObject Copy()
		{
			return Copy(true);
		}
		
		public virtual SessionDataObject Copy(bool deepCopy)
		{
			return _serviceProvider.GetRequiredService<SessionDataObject>().Initialize(this, deepCopy);
		}
		
		public override void CopyValuesFrom(IDataObject sourceObject, bool deepCopy)
        {
			var sessionSource = sourceObject as SessionDataObject;

			if (ReferenceEquals(null, sessionSource))
				throw new GOServerException("Wrong type of object");

			this.SetIsNewValue(sourceObject.IsNew, false, false);						
			this.SetDriverIdValue(sessionSource.DriverId, false, false);
			this.SetEndTimeValue(sessionSource.EndTime, false, false);
			this.SetIdValue(sessionSource.Id, false, false);
			this.SetisVORValue(sessionSource.isVOR, false, false);
			this.SetStartTimeValue(sessionSource.StartTime, false, false);
			this.SetVehicleIdValue(sessionSource.VehicleId, false, false);



			this._driver_NewObjectId = (sourceObject as SessionDataObject)._driver_NewObjectId;





			this._vehicle_NewObjectId = (sourceObject as SessionDataObject)._vehicle_NewObjectId;




			if (deepCopy)
			{
				this.ObjectsDataSet = sessionSource.ObjectsDataSet.Clone();
				// Remove the source object from the dataset
                this.ObjectsDataSet.RemoveObject(sessionSource);
				// And Replace by the one we're currently constructing
                this.ObjectsDataSet.AddObject(this);
			}

			this.SetIsDirtyValue(sourceObject.IsDirty, false, false);
			this.SetIsMarkedForDeletionValue(sourceObject.IsMarkedForDeletion, false, false);
		}

        public override bool Equals(System.Object obj)
        {
            var p = obj as SessionDataObject;
            
			if (p == null)
            {
                return false;
            }

			if (p.IsNew) 
				return base.Equals(obj);
				
            // Return true if the identifying fields match:
			return  p._id == this._id;
        }


        public override int GetHashCode()
        {		
			
			return _id == null ? -1 : _id.GetHashCode();
		}

		public override void UpdateRelatedInternalIds(ConcurrentDictionary<int, int> datasetMergingInternalIdMapping)
        {



			if (this._driver_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._driver_NewObjectId))
				{
                    this._driver_NewObjectId = null;
				}
                else
				{
					this._driver_NewObjectId = datasetMergingInternalIdMapping[(int) this._driver_NewObjectId];
				}
			}





			if (this._vehicle_NewObjectId != null)
			{
				if (!datasetMergingInternalIdMapping.ContainsKey((int) this._vehicle_NewObjectId))
				{
                    this._vehicle_NewObjectId = null;
				}
                else
				{
					this._vehicle_NewObjectId = datasetMergingInternalIdMapping[(int) this._vehicle_NewObjectId];
				}
			}




		}

		#endregion
        
		#region Relation properties		
		
		protected IDataProvider<ChecklistResultDataObject> _checklistResultService => _serviceProvider.GetRequiredService<IDataProvider<ChecklistResultDataObject>>();

		private readonly SemaphoreSlim __checklistResultsSemaphore = new SemaphoreSlim(1, 1);
		private bool __checklistResultsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "ChecklistResults", which is a collection of ChecklistResultDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ChecklistResultDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ChecklistResultDataObject>> LoadChecklistResultsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadChecklistResultsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ChecklistResultDataObject>> LoadChecklistResultsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __checklistResultsSemaphore.WaitAsync();
			
	        try
            {
                if (!__checklistResultsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SessionId1 == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _checklistResultService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __checklistResultsAlreadyLazyLoaded = true;
                }

                return await GetChecklistResultsAsync(false);
            }
            finally
            {
                __checklistResultsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ChecklistResultDataObject> ChecklistResults 
		{
			get
			{			
				return GetChecklistResultsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeChecklistResults()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("ChecklistResults");
		}

		public virtual async Task<DataObjectCollection<ChecklistResultDataObject>> GetChecklistResultsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__checklistResultsAlreadyLazyLoaded || forceReload) )
			{
				await LoadChecklistResultsAsync(forceReload : forceReload);
			}
			var checklistResults = ObjectsDataSet.GetRelatedObjects<ChecklistResultDataObject>(this, "ChecklistResults");							
			checklistResults.CollectionChanged += new NotifyCollectionChangedEventHandler(ChecklistResults_CollectionChanged);
				
			return checklistResults;
		}

        private void ChecklistResults_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ChecklistResultDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : ChecklistResult", "SessionDataObject.ChecklistResults_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SessionDataObject throw an exception while trying to add ChecklistResultDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._session_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SessionId1 = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SessionId1 == default(System.Guid))
							relatedObj.SessionId1 = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ChecklistResultDataObject).Session = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DetailedSessionViewDataObject> _detailedSessionViewService => _serviceProvider.GetRequiredService<IDataProvider<DetailedSessionViewDataObject>>();

		private readonly SemaphoreSlim __detailedSessionViewItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __detailedSessionViewItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DetailedSessionViewItems", which is a collection of DetailedSessionViewDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DetailedSessionViewDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DetailedSessionViewDataObject>> LoadDetailedSessionViewItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDetailedSessionViewItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DetailedSessionViewDataObject>> LoadDetailedSessionViewItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __detailedSessionViewItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__detailedSessionViewItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SessionId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _detailedSessionViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __detailedSessionViewItemsAlreadyLazyLoaded = true;
                }

                return await GetDetailedSessionViewItemsAsync(false);
            }
            finally
            {
                __detailedSessionViewItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DetailedSessionViewDataObject> DetailedSessionViewItems 
		{
			get
			{			
				return GetDetailedSessionViewItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDetailedSessionViewItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("DetailedSessionViewItems");
		}

		public virtual async Task<DataObjectCollection<DetailedSessionViewDataObject>> GetDetailedSessionViewItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__detailedSessionViewItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDetailedSessionViewItemsAsync(forceReload : forceReload);
			}
			var detailedSessionViewItems = ObjectsDataSet.GetRelatedObjects<DetailedSessionViewDataObject>(this, "DetailedSessionViewItems");							
			detailedSessionViewItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DetailedSessionViewItems_CollectionChanged);
				
			return detailedSessionViewItems;
		}

        private void DetailedSessionViewItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DetailedSessionViewDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DetailedSessionView", "SessionDataObject.DetailedSessionViewItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SessionDataObject throw an exception while trying to add DetailedSessionViewDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._session_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SessionId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SessionId == default(System.Guid))
							relatedObj.SessionId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DetailedSessionViewDataObject).Session = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DetailedVORSessionStoreProcedureDataObject> _detailedVORSessionStoreProcedureService => _serviceProvider.GetRequiredService<IDataProvider<DetailedVORSessionStoreProcedureDataObject>>();

		private readonly SemaphoreSlim __detailedVORSessionStoreProcedureItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "DetailedVORSessionStoreProcedureItems", which is a collection of DetailedVORSessionStoreProcedureDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of DetailedVORSessionStoreProcedureDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<DetailedVORSessionStoreProcedureDataObject>> LoadDetailedVORSessionStoreProcedureItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDetailedVORSessionStoreProcedureItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<DetailedVORSessionStoreProcedureDataObject>> LoadDetailedVORSessionStoreProcedureItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __detailedVORSessionStoreProcedureItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SessionId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _detailedVORSessionStoreProcedureService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded = true;
                }

                return await GetDetailedVORSessionStoreProcedureItemsAsync(false);
            }
            finally
            {
                __detailedVORSessionStoreProcedureItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<DetailedVORSessionStoreProcedureDataObject> DetailedVORSessionStoreProcedureItems 
		{
			get
			{			
				return GetDetailedVORSessionStoreProcedureItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeDetailedVORSessionStoreProcedureItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("DetailedVORSessionStoreProcedureItems");
		}

		public virtual async Task<DataObjectCollection<DetailedVORSessionStoreProcedureDataObject>> GetDetailedVORSessionStoreProcedureItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadDetailedVORSessionStoreProcedureItemsAsync(forceReload : forceReload);
			}
			var detailedVORSessionStoreProcedureItems = ObjectsDataSet.GetRelatedObjects<DetailedVORSessionStoreProcedureDataObject>(this, "DetailedVORSessionStoreProcedureItems");							
			detailedVORSessionStoreProcedureItems.CollectionChanged += new NotifyCollectionChangedEventHandler(DetailedVORSessionStoreProcedureItems_CollectionChanged);
				
			return detailedVORSessionStoreProcedureItems;
		}

        private void DetailedVORSessionStoreProcedureItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as DetailedVORSessionStoreProcedureDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : DetailedVORSessionStoreProcedure", "SessionDataObject.DetailedVORSessionStoreProcedureItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SessionDataObject throw an exception while trying to add DetailedVORSessionStoreProcedureDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._session_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SessionId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SessionId == default(System.Guid))
							relatedObj.SessionId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as DetailedVORSessionStoreProcedureDataObject).Session = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<DriverDataObject> _driverService => _serviceProvider.GetRequiredService<IDataProvider<DriverDataObject>>();
      public virtual void SetDriverValue(DriverDataObject valueToSet)
		{
			SetDriverValue(valueToSet, true, true);
		}

        public virtual void SetDriverValue(DriverDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			DriverDataObject existing_driver = null ;

			if ( !(ObjectsDataSet == null))
			{
				DriverDataObject key;

				if (this._driver_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<DriverDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._driver_NewObjectId;			
				}

				existing_driver = (DriverDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_driver ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Driver", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "SessionDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_driver_NewObjectId != valueToSet.InternalObjectId)
					{
						_driver_NewObjectId = valueToSet.InternalObjectId;
						_driverId = valueToSet.Id;
						OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_driverId != valueToSet.Id)
					{
						_driver_NewObjectId = null;

						_driverId = valueToSet.Id;
						OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_driverId = Guid.Empty;
				OnPropertyChanged("DriverId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_driver ,valueToSet))
				OnPropertyChanged("Driver", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __driverSemaphore = new SemaphoreSlim(1, 1);
		private bool __driverAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Driver", which is a DriverDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a DriverDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DriverDataObject> LoadDriverAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadDriverAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DriverDataObject> LoadDriverAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __driverSemaphore.WaitAsync();
			
	        try
            {
                if (!__driverAlreadyLazyLoaded || forceReload)
                {
								
					DriverDataObject driver = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __driverAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId);
						driver.IsNew = false;
						driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
						if (driver != null)
						{
							return driver;
						}
					}

					driver = await _driverService.GetAsync(_serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId), parameters : parameters, skipSecurity: skipSecurity);

					SetDriverValue(driver, false, false);
					__driverAlreadyLazyLoaded = true;				
		
					driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId);
					driver.IsNew = false;
					driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
                    __driverAlreadyLazyLoaded = true;
                }

                return await GetDriverAsync(false);
            }
            finally
            {
                __driverSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DriverDataObject Driver 
		{
			get
			{			
				return GetDriverAsync(true).Result;
			}
			set
			{
				SetDriverValue(value);
			}
		}
		
		public virtual bool ShouldSerializeDriver()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("Driver");
		}

		public virtual async Task<DriverDataObject> GetDriverAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			DriverDataObject driver;

				
			if (_driver_NewObjectId != null)
			{
				driver = _serviceProvider.GetRequiredService<DriverDataObject>();
				driver.IsNew = true;
				driver.InternalObjectId = _driver_NewObjectId;
				driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
			}
			else
			{
				driver = _serviceProvider.GetRequiredService<DriverDataObject>().Initialize(this.DriverId);
				driver.IsNew = false;
				driver = (DriverDataObject)ObjectsDataSet.GetObject(driver);
				
				if (allowLazyLoading && driver == null && LazyLoadingEnabled && (!__driverAlreadyLazyLoaded || forceReload))
				{
					driver = await LoadDriverAsync(forceReload : forceReload);
				}
			}
				
			return driver;
		}

		public virtual System.Guid DriverForeignKey
		{
			get { return DriverId; }
			set 
			{	
				DriverId = value;
			}
			
		}
		

		protected IDataProvider<GPSHistoryDataObject> _gPSHistoryService => _serviceProvider.GetRequiredService<IDataProvider<GPSHistoryDataObject>>();

		private readonly SemaphoreSlim __gPSHistoryItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __gPSHistoryItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "GPSHistoryItems", which is a collection of GPSHistoryDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of GPSHistoryDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<GPSHistoryDataObject>> LoadGPSHistoryItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadGPSHistoryItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<GPSHistoryDataObject>> LoadGPSHistoryItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __gPSHistoryItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__gPSHistoryItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SessionId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _gPSHistoryService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __gPSHistoryItemsAlreadyLazyLoaded = true;
                }

                return await GetGPSHistoryItemsAsync(false);
            }
            finally
            {
                __gPSHistoryItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<GPSHistoryDataObject> GPSHistoryItems 
		{
			get
			{			
				return GetGPSHistoryItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeGPSHistoryItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("GPSHistoryItems");
		}

		public virtual async Task<DataObjectCollection<GPSHistoryDataObject>> GetGPSHistoryItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__gPSHistoryItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadGPSHistoryItemsAsync(forceReload : forceReload);
			}
			var gPSHistoryItems = ObjectsDataSet.GetRelatedObjects<GPSHistoryDataObject>(this, "GPSHistoryItems");							
			gPSHistoryItems.CollectionChanged += new NotifyCollectionChangedEventHandler(GPSHistoryItems_CollectionChanged);
				
			return gPSHistoryItems;
		}

        private void GPSHistoryItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as GPSHistoryDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : GPSHistory", "SessionDataObject.GPSHistoryItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SessionDataObject throw an exception while trying to add GPSHistoryDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._session_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SessionId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SessionId == default(System.Guid))
							relatedObj.SessionId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as GPSHistoryDataObject).Session = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<ImpactDataObject> _impactService => _serviceProvider.GetRequiredService<IDataProvider<ImpactDataObject>>();

		private readonly SemaphoreSlim __impactsSemaphore = new SemaphoreSlim(1, 1);
		private bool __impactsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Impacts", which is a collection of ImpactDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of ImpactDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<ImpactDataObject>> LoadImpactsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadImpactsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<ImpactDataObject>> LoadImpactsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __impactsSemaphore.WaitAsync();
			
	        try
            {
                if (!__impactsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SessionId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _impactService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __impactsAlreadyLazyLoaded = true;
                }

                return await GetImpactsAsync(false);
            }
            finally
            {
                __impactsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<ImpactDataObject> Impacts 
		{
			get
			{			
				return GetImpactsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeImpacts()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("Impacts");
		}

		public virtual async Task<DataObjectCollection<ImpactDataObject>> GetImpactsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__impactsAlreadyLazyLoaded || forceReload) )
			{
				await LoadImpactsAsync(forceReload : forceReload);
			}
			var impacts = ObjectsDataSet.GetRelatedObjects<ImpactDataObject>(this, "Impacts");							
			impacts.CollectionChanged += new NotifyCollectionChangedEventHandler(Impacts_CollectionChanged);
				
			return impacts;
		}

        private void Impacts_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as ImpactDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : Impact", "SessionDataObject.Impacts_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SessionDataObject throw an exception while trying to add ImpactDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._session_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SessionId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SessionId == default(System.Guid))
							relatedObj.SessionId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as ImpactDataObject).Session = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<PSTATDetailsDataObject> _pSTATDetailsService => _serviceProvider.GetRequiredService<IDataProvider<PSTATDetailsDataObject>>();

		private readonly SemaphoreSlim __pSTATDetailsItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __pSTATDetailsItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "PSTATDetailsItems", which is a collection of PSTATDetailsDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of PSTATDetailsDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<PSTATDetailsDataObject>> LoadPSTATDetailsItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadPSTATDetailsItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<PSTATDetailsDataObject>> LoadPSTATDetailsItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __pSTATDetailsItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__pSTATDetailsItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SessionId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _pSTATDetailsService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __pSTATDetailsItemsAlreadyLazyLoaded = true;
                }

                return await GetPSTATDetailsItemsAsync(false);
            }
            finally
            {
                __pSTATDetailsItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<PSTATDetailsDataObject> PSTATDetailsItems 
		{
			get
			{			
				return GetPSTATDetailsItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializePSTATDetailsItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("PSTATDetailsItems");
		}

		public virtual async Task<DataObjectCollection<PSTATDetailsDataObject>> GetPSTATDetailsItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__pSTATDetailsItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadPSTATDetailsItemsAsync(forceReload : forceReload);
			}
			var pSTATDetailsItems = ObjectsDataSet.GetRelatedObjects<PSTATDetailsDataObject>(this, "PSTATDetailsItems");							
			pSTATDetailsItems.CollectionChanged += new NotifyCollectionChangedEventHandler(PSTATDetailsItems_CollectionChanged);
				
			return pSTATDetailsItems;
		}

        private void PSTATDetailsItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as PSTATDetailsDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : PSTATDetails", "SessionDataObject.PSTATDetailsItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SessionDataObject throw an exception while trying to add PSTATDetailsDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._session_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SessionId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SessionId == default(System.Guid))
							relatedObj.SessionId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as PSTATDetailsDataObject).Session = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<SessionDetailsDataObject> _sessionDetailsService => _serviceProvider.GetRequiredService<IDataProvider<SessionDetailsDataObject>>();

		private readonly SemaphoreSlim __sessionDetailsItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __sessionDetailsItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "SessionDetailsItems", which is a collection of SessionDetailsDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of SessionDetailsDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<SessionDetailsDataObject>> LoadSessionDetailsItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadSessionDetailsItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<SessionDetailsDataObject>> LoadSessionDetailsItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __sessionDetailsItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__sessionDetailsItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SessionId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _sessionDetailsService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __sessionDetailsItemsAlreadyLazyLoaded = true;
                }

                return await GetSessionDetailsItemsAsync(false);
            }
            finally
            {
                __sessionDetailsItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<SessionDetailsDataObject> SessionDetailsItems 
		{
			get
			{			
				return GetSessionDetailsItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeSessionDetailsItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("SessionDetailsItems");
		}

		public virtual async Task<DataObjectCollection<SessionDetailsDataObject>> GetSessionDetailsItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__sessionDetailsItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadSessionDetailsItemsAsync(forceReload : forceReload);
			}
			var sessionDetailsItems = ObjectsDataSet.GetRelatedObjects<SessionDetailsDataObject>(this, "SessionDetailsItems");							
			sessionDetailsItems.CollectionChanged += new NotifyCollectionChangedEventHandler(SessionDetailsItems_CollectionChanged);
				
			return sessionDetailsItems;
		}

        private void SessionDetailsItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as SessionDetailsDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : SessionDetails", "SessionDataObject.SessionDetailsItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SessionDataObject throw an exception while trying to add SessionDetailsDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._session_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SessionId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SessionId == default(System.Guid))
							relatedObj.SessionId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as SessionDetailsDataObject).Session = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleDataObject> _vehicleService => _serviceProvider.GetRequiredService<IDataProvider<VehicleDataObject>>();
      public virtual void SetVehicleValue(VehicleDataObject valueToSet)
		{
			SetVehicleValue(valueToSet, true, true);
		}

        public virtual void SetVehicleValue(VehicleDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			VehicleDataObject existing_vehicle = null ;

			if ( !(ObjectsDataSet == null))
			{
				VehicleDataObject key;

				if (this._vehicle_NewObjectId == null)
				{
					key =  _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId);
					key.IsNew = false;
				}
				else
				{
					key = _serviceProvider.GetRequiredService<VehicleDataObject>();
					key.IsNew = true;
					key.InternalObjectId = this._vehicle_NewObjectId;			
				}

				existing_vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(key);
			}
				
			if (ReferenceEquals(existing_vehicle ,valueToSet))
            {
				return;
            }
			// Give opportunity to change value before set
			OnBeforeSetRelationField("Vehicle", valueToSet);
					
			// Setting the navigator desync the FK. The FK should be resync
			if (!ReferenceEquals(null, valueToSet))
			{
				if(ObjectsDataSet == null)
				{
					// remove logging to avoid to have to make the caller async. Do we really need logging here ?
					// _logEngine.LogError("Unable to set Relation Field", "Unable to set Relation Field, your entity doesn't have a DataSet.", "SessionDataObject", null);
					throw new GOServerException("Unable to set Relation fields, your entity doesn't have a DataSet");
				}

                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
				if (valueToSet.IsNew)
				{
					if (_vehicle_NewObjectId != valueToSet.InternalObjectId)
					{
						_vehicle_NewObjectId = valueToSet.InternalObjectId;
						_vehicleId = valueToSet.Id;
						OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
					}
				}
				else
				{
					if (_vehicleId != valueToSet.Id)
					{
						_vehicle_NewObjectId = null;

						_vehicleId = valueToSet.Id;
						OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
					}
				}
			}
			else
			{
				_vehicleId = Guid.Empty;
				OnPropertyChanged("VehicleId",notifyChanges, dirtyHandlerOn);
			}
			if (!ReferenceEquals(existing_vehicle ,valueToSet))
				OnPropertyChanged("Vehicle", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __vehicleSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "Vehicle", which is a VehicleDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a VehicleDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<VehicleDataObject> LoadVehicleAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<VehicleDataObject> LoadVehicleAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleAlreadyLazyLoaded || forceReload)
                {
								
					VehicleDataObject vehicle = null;

					if (!forceReload) // in case of force reload, no need to check if there is already data. There can be already data and __vehicleAlreadyLazyLoaded set to false, in case of a dataset initialized as such (for example with a load with includes
					{
						vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId);
						vehicle.IsNew = false;
						vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
						if (vehicle != null)
						{
							return vehicle;
						}
					}

					vehicle = await _vehicleService.GetAsync(_serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId), parameters : parameters, skipSecurity: skipSecurity);

					SetVehicleValue(vehicle, false, false);
					__vehicleAlreadyLazyLoaded = true;				
		
					vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId);
					vehicle.IsNew = false;
					vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
                    __vehicleAlreadyLazyLoaded = true;
                }

                return await GetVehicleAsync(false);
            }
            finally
            {
                __vehicleSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual VehicleDataObject Vehicle 
		{
			get
			{			
				return GetVehicleAsync(true).Result;
			}
			set
			{
				SetVehicleValue(value);
			}
		}
		
		public virtual bool ShouldSerializeVehicle()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("Vehicle");
		}

		public virtual async Task<VehicleDataObject> GetVehicleAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			VehicleDataObject vehicle;

				
			if (_vehicle_NewObjectId != null)
			{
				vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
				vehicle.IsNew = true;
				vehicle.InternalObjectId = _vehicle_NewObjectId;
				vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
			}
			else
			{
				vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>().Initialize(this.VehicleId);
				vehicle.IsNew = false;
				vehicle = (VehicleDataObject)ObjectsDataSet.GetObject(vehicle);
				
				if (allowLazyLoading && vehicle == null && LazyLoadingEnabled && (!__vehicleAlreadyLazyLoaded || forceReload))
				{
					vehicle = await LoadVehicleAsync(forceReload : forceReload);
				}
			}
				
			return vehicle;
		}

		public virtual System.Guid VehicleForeignKey
		{
			get { return VehicleId; }
			set 
			{	
				VehicleId = value;
			}
			
		}
		

		protected IDataProvider<VehicleGPSDataObject> _vehicleGPSService => _serviceProvider.GetRequiredService<IDataProvider<VehicleGPSDataObject>>();

		private readonly SemaphoreSlim __vehicleGPSItemsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleGPSItemsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleGPSItems", which is a collection of VehicleGPSDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleGPSDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleGPSDataObject>> LoadVehicleGPSItemsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleGPSItemsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleGPSDataObject>> LoadVehicleGPSItemsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleGPSItemsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleGPSItemsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SessionId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleGPSService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleGPSItemsAlreadyLazyLoaded = true;
                }

                return await GetVehicleGPSItemsAsync(false);
            }
            finally
            {
                __vehicleGPSItemsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleGPSDataObject> VehicleGPSItems 
		{
			get
			{			
				return GetVehicleGPSItemsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleGPSItems()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("VehicleGPSItems");
		}

		public virtual async Task<DataObjectCollection<VehicleGPSDataObject>> GetVehicleGPSItemsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleGPSItemsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleGPSItemsAsync(forceReload : forceReload);
			}
			var vehicleGPSItems = ObjectsDataSet.GetRelatedObjects<VehicleGPSDataObject>(this, "VehicleGPSItems");							
			vehicleGPSItems.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleGPSItems_CollectionChanged);
				
			return vehicleGPSItems;
		}

        private void VehicleGPSItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleGPSDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleGPS", "SessionDataObject.VehicleGPSItems_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SessionDataObject throw an exception while trying to add VehicleGPSDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._session_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SessionId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SessionId == default(Nullable<System.Guid>))
							relatedObj.SessionId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleGPSDataObject).Session = null;
                    // }
                    break;
            }            
        }

		protected IDataProvider<VehicleLastGPSLocationViewDataObject> _vehicleLastGPSLocationViewService => _serviceProvider.GetRequiredService<IDataProvider<VehicleLastGPSLocationViewDataObject>>();
      public virtual void SetVehicleLastGPSLocationViewValue(VehicleLastGPSLocationViewDataObject valueToSet)
		{
			SetVehicleLastGPSLocationViewValue(valueToSet, true, true);
		}

        public virtual void SetVehicleLastGPSLocationViewValue(VehicleLastGPSLocationViewDataObject valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{	
		
			var _relatedObjects = ObjectsDataSet.GetRelatedObjects<VehicleLastGPSLocationViewDataObject>(this, "VehicleLastGPSLocationView");
			var existing_vehicleLastGPSLocationView = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
			// Give opportunity to change value before set
			OnBeforeSetRelationField("VehicleLastGPSLocationView", valueToSet);
					
			if (!ReferenceEquals(null, valueToSet))
			{
                ObjectsDataSet.AddObjectIfDoesNotExist(valueToSet);
				
                if (this.IsNew)
                    valueToSet._session_NewObjectId = this.InternalObjectId;
                else
				{
					// Sync the one-to-one association from the FK side
					valueToSet = ObjectsDataSet.GetObject(valueToSet);
					valueToSet.Session = this;
					valueToSet.SessionId = this.Id;
				}			
			}
			else  if (existing_vehicleLastGPSLocationView != null)
            {
                ObjectsDataSet.RemoveObject(existing_vehicleLastGPSLocationView);
            }
			if (!ReferenceEquals(existing_vehicleLastGPSLocationView ,valueToSet))
				OnPropertyChanged("VehicleLastGPSLocationView", notifyChanges, dirtyHandlerOn);
		}

		private readonly SemaphoreSlim __vehicleLastGPSLocationViewSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleLastGPSLocationViewAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleLastGPSLocationView", which is a VehicleLastGPSLocationViewDataObject.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a VehicleLastGPSLocationViewDataObject</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<VehicleLastGPSLocationViewDataObject> LoadVehicleLastGPSLocationViewAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleLastGPSLocationViewAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<VehicleLastGPSLocationViewDataObject> LoadVehicleLastGPSLocationViewAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleLastGPSLocationViewSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleLastGPSLocationViewAlreadyLazyLoaded || forceReload)
                {
				if(this.ObjectsDataSet == null)
					{
						// remove logging to avoid to have to make the caller async. Do we really need logging here ?
						// _logEngine.LogError("Unable to Load Related Data", "Unable to Load Related Data VehicleLastGPSLocationView for the current entity. The DataObjects doesn't have an ObjectsDataSet", "SessionObjectsDataSet", null);
						throw new GOServerException("Unable to Load Related Data. The current Data Object doesn't have an ObjectsDataSet");
					}

					var vehicleLastGPSLocationView = (this.ObjectsDataSet as ObjectsDataSet).VehicleLastGPSLocationViewObjects == null ? null : (this.ObjectsDataSet as ObjectsDataSet).VehicleLastGPSLocationViewObjects.Where(item => item.Value.SessionId == Id).SingleOrDefault().Value;

					// If null relation or forceReload => load the entity
					if (forceReload || ReferenceEquals(vehicleLastGPSLocationView, null))
					{
						var filterPredicate = "SessionId == @0";
						var filterArguments = new object[] { (System.Guid)this.Id };

						vehicleLastGPSLocationView = (await _vehicleLastGPSLocationViewService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity)).SingleOrDefault();

						SetVehicleLastGPSLocationViewValue(vehicleLastGPSLocationView, false, false);
						__vehicleLastGPSLocationViewAlreadyLazyLoaded = true;
					}

					// Patch for what is most likely an internal dataset fault when the relation field is on PK side of a OneToOne relation
					// If we've got a vehicleLastGPSLocationView, but relation field not set, encourage it to get set by removing and re-adding the vehicleLastGPSLocationView 
					if (vehicleLastGPSLocationView != null && this.VehicleLastGPSLocationView == null)
					{
						this.ObjectsDataSet.RemoveObject(vehicleLastGPSLocationView);
						this.ObjectsDataSet.AddObject(vehicleLastGPSLocationView);
					}			
                    __vehicleLastGPSLocationViewAlreadyLazyLoaded = true;
                }

                return await GetVehicleLastGPSLocationViewAsync(false);
            }
            finally
            {
                __vehicleLastGPSLocationViewSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual VehicleLastGPSLocationViewDataObject VehicleLastGPSLocationView 
		{
			get
			{			
				return GetVehicleLastGPSLocationViewAsync(true).Result;
			}
			set
			{
				SetVehicleLastGPSLocationViewValue(value);
			}
		}
		
		public virtual bool ShouldSerializeVehicleLastGPSLocationView()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("VehicleLastGPSLocationView");
		}

		public virtual async Task<VehicleLastGPSLocationViewDataObject> GetVehicleLastGPSLocationViewAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			VehicleLastGPSLocationViewDataObject vehicleLastGPSLocationView;

				
			{
             	var _relatedObjects = ObjectsDataSet.GetRelatedObjects<VehicleLastGPSLocationViewDataObject>(this, "VehicleLastGPSLocationView");
               	vehicleLastGPSLocationView = _relatedObjects == null ? null : _relatedObjects.SingleOrDefault();
				
				if (allowLazyLoading && vehicleLastGPSLocationView == null && LazyLoadingEnabled && (!__vehicleLastGPSLocationViewAlreadyLazyLoaded || forceReload))
				{
					vehicleLastGPSLocationView = await LoadVehicleLastGPSLocationViewAsync(forceReload : forceReload);
				}
			}
				
			return vehicleLastGPSLocationView;
		}


		protected IDataProvider<VehicleLockoutDataObject> _vehicleLockoutService => _serviceProvider.GetRequiredService<IDataProvider<VehicleLockoutDataObject>>();

		private readonly SemaphoreSlim __vehicleLockoutsSemaphore = new SemaphoreSlim(1, 1);
		private bool __vehicleLockoutsAlreadyLazyLoaded = false;

		/// <summary>
		/// Asynchronously loads the property "VehicleLockouts", which is a collection of VehicleLockoutDataObjects.
		/// </summary>
		/// <param name="parameters">Parameters to be used in the loading process. These may include filters, sorting, etc.</param>
		/// <param name="skipSecurity">If set to true, security checks are bypassed during the loading process. Default is false.</param>
		/// <param name="forceReload">If set to true, forces a reload of the AuthorizationRole items even if they have already been loaded. Useful when you know the data set might have changed. Default is false.</param>		
		/// <returns>A Task resulting in a collection of VehicleLockoutDataObjects</returns>
		/// <remarks>
		/// This method performs an asynchronous operation to retrieve the data.
		/// It uses a semaphore to ensure thread-safe access to the underlying data.
		/// In case the items are not already loaded or if 'forceReload' is true, it calls the _authorizationRoleService
		/// to fetch the data asynchronously.
		/// If the items are already loaded and 'forceReload' is false, it retrieves the cached items.
		/// </remarks> 
		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> LoadVehicleLockoutsAsync(bool skipSecurity = false, bool forceReload = false)
		{
			return await LoadVehicleLockoutsAsync(CurrentTransactionParameters ?? new Parameters(), skipSecurity);
		}

		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> LoadVehicleLockoutsAsync(Parameters parameters, bool skipSecurity = false, bool forceReload = false)
		{
			await __vehicleLockoutsSemaphore.WaitAsync();
			
	        try
            {
                if (!__vehicleLockoutsAlreadyLazyLoaded || forceReload)
                {
					var filterPredicate = "SessionId == @0";
					var filterArguments = new object[] { (System.Guid)this.Id };
					var result = await _vehicleLockoutService.GetCollectionAsync(null, filterPredicate, filterArguments, parameters : parameters, skipSecurity: skipSecurity);
					// Reference Links are not serialized => should reconstruct them now
					if (result != null && result.ObjectsDataSet != null) 
					{ 
						Merge(result.ObjectsDataSet);
					}
                    __vehicleLockoutsAlreadyLazyLoaded = true;
                }

                return await GetVehicleLockoutsAsync(false);
            }
            finally
            {
                __vehicleLockoutsSemaphore.Release();
            }
		}
		
		[JsonProperty]
		public virtual DataObjectCollection<VehicleLockoutDataObject> VehicleLockouts 
		{
			get
			{			
				return GetVehicleLockoutsAsync(true).Result;
			}
		}
		
		public virtual bool ShouldSerializeVehicleLockouts()
		{
			return ObjectsDataSet != null && ObjectsDataSet.RelationsToInclude != null && ObjectsDataSet.RelationsToInclude.ContainsKey("SessionDataObject") && ObjectsDataSet.RelationsToInclude["SessionDataObject"].Contains("VehicleLockouts");
		}

		public virtual async Task<DataObjectCollection<VehicleLockoutDataObject>> GetVehicleLockoutsAsync(bool allowLazyLoading, bool forceReload = false)
		{
			if (ObjectsDataSet == null)
				return null;

			// Lazy loading enabled and collection not yet loaded => load the collection
			if (allowLazyLoading && LazyLoadingEnabled && (!__vehicleLockoutsAlreadyLazyLoaded || forceReload) )
			{
				await LoadVehicleLockoutsAsync(forceReload : forceReload);
			}
			var vehicleLockouts = ObjectsDataSet.GetRelatedObjects<VehicleLockoutDataObject>(this, "VehicleLockouts");							
			vehicleLockouts.CollectionChanged += new NotifyCollectionChangedEventHandler(VehicleLockouts_CollectionChanged);
				
			return vehicleLockouts;
		}

        private void VehicleLockouts_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            switch (e.Action)
            {
                case NotifyCollectionChangedAction.Add:
                    foreach (var item in e.NewItems)
                    {
						var relatedObj = item as VehicleLockoutDataObject;
						if (relatedObj == null)
						{
							// remove logging to avoid to have to make the caller async. Do we really need logging here ?
							// _logEngine.LogError("Add Event throw an Exception", "Unable to get value of expected related Object : VehicleLockout", "SessionDataObject.VehicleLockouts_CollectionChanged", null);
							throw new GOServerException("Unexpected Error : The Add Event of SessionDataObject throw an exception while trying to add VehicleLockoutDataObject : NullReference occured");
						}

						if (this.IsNew)
						{
							relatedObj._session_NewObjectId = this.InternalObjectId;
						}
						else
						{
							relatedObj.SessionId = this.Id;
						}
 
						if (relatedObj.IsNew && relatedObj.SessionId == default(Nullable<System.Guid>))
							relatedObj.SessionId = this.Id;
                    }
                    break;
                case NotifyCollectionChangedAction.Remove:
                    // foreach (var item in e.OldItems)
                    // {
                        //(item as VehicleLockoutDataObject).Session = null;
                    // }
                    break;
            }            
        }

		public override void ClearLazyLoadFlags()
		{
			__checklistResultsAlreadyLazyLoaded = false;
			__detailedSessionViewItemsAlreadyLazyLoaded = false;
			__detailedVORSessionStoreProcedureItemsAlreadyLazyLoaded = false;
			__gPSHistoryItemsAlreadyLazyLoaded = false;
			__impactsAlreadyLazyLoaded = false;
			__pSTATDetailsItemsAlreadyLazyLoaded = false;
			__sessionDetailsItemsAlreadyLazyLoaded = false;
			__vehicleGPSItemsAlreadyLazyLoaded = false;
			__vehicleLockoutsAlreadyLazyLoaded = false;
		}

		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencedObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadDriverAsync()) != null)
				result.Add(Driver);
			if ((await LoadVehicleAsync()) != null)
				result.Add(Vehicle);
			return result;
		}
		
		public override async Task<IEnumerable<IDataObject>> GetAllRelatedReferencingObjectsAsync()
		{
			var result = new List<IDataObject>();
			if ((await LoadChecklistResultsAsync()) != null)
				result.AddRange(ChecklistResults);
			if ((await LoadDetailedSessionViewItemsAsync()) != null)
				result.AddRange(DetailedSessionViewItems);
			if ((await LoadDetailedVORSessionStoreProcedureItemsAsync()) != null)
				result.AddRange(DetailedVORSessionStoreProcedureItems);
			if ((await LoadGPSHistoryItemsAsync()) != null)
				result.AddRange(GPSHistoryItems);
			if ((await LoadImpactsAsync()) != null)
				result.AddRange(Impacts);
			if ((await LoadPSTATDetailsItemsAsync()) != null)
				result.AddRange(PSTATDetailsItems);
			if ((await LoadSessionDetailsItemsAsync()) != null)
				result.AddRange(SessionDetailsItems);
			if ((await LoadVehicleGPSItemsAsync()) != null)
				result.AddRange(VehicleGPSItems);
			if ((await LoadVehicleLastGPSLocationViewAsync()) != null)
				result.Add(VehicleLastGPSLocationView);
			if ((await LoadVehicleLockoutsAsync()) != null)
				result.AddRange(VehicleLockouts);
			return result;
		}

		public override bool HasUpstreamReferenceTo(IDataObject other)
		{
			if (other == null)
				return false;

			return
				Vehicle == other ||
				(other is VehicleDataObject && (VehicleId != default(System.Guid)) && (VehicleId == (other as VehicleDataObject).Id)) || 
				Driver == other ||
				(other is DriverDataObject && (DriverId != default(System.Guid)) && (DriverId == (other as DriverDataObject).Id)); 
		}

		#endregion

		#region Commands for Custom fields


		#endregion

		#region Properties
		
		public virtual System.Guid PrimaryKey
		{
			get { return Id; }
		}

		public virtual void SetPrimaryKeyValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			SetIdValue(valueToSet, notifyChanges, dirtyHandlerOn);
		}
	
			
			
		public virtual void SetDriverIdValue(System.Guid valueToSet)
		{
			SetDriverIdValue(valueToSet, true, true);
		}

		public virtual void SetDriverIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_driverId != valueToSet)
			{
				_driverId = valueToSet;

				// DriverId is a FK. Setting its value should result in a event
				OnPropertyChanged("Driver", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("DriverId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The DriverId property of the Session DataObject</summary>
        public virtual System.Guid DriverId 
		{
			get	{ return _driverId;}
			
			
			set
			{
				SetDriverIdValue(value);
			}
		}		
			
			
		public virtual void SetEndTimeValue(Nullable<System.DateTime> valueToSet)
		{
			SetEndTimeValue(valueToSet, true, true);
		}

		public virtual void SetEndTimeValue(Nullable<System.DateTime> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null) 
			{
				if (_endTime != null)
				{
					_endTime = null;
					OnPropertyChanged("EndTime", notifyChanges, dirtyHandlerOn);
				}
			}
			else 
			if (valueToSet == DateTime.MinValue) 
			{
				if (_endTime != DateTime.MinValue.ToUniversalTime())
				{
					_endTime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("EndTime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_endTime != DateTime.MaxValue.ToUniversalTime())
				{
					_endTime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("EndTime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_endTime != valueToSet ||
                (_endTime != null && ((DateTime)_endTime).Kind == DateTimeKind.Unspecified))
			{
				_endTime = DateTime.SpecifyKind((DateTime)valueToSet, DateTimeKind.Local);

				OnPropertyChanged("EndTime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The End Time property of the Session DataObject</summary>
        public virtual Nullable<System.DateTime> EndTime 
		{
			get	{ return _endTime;}
			
			
			set
			{
				SetEndTimeValue(value);
			}
		}		
			
			
		public virtual void SetIdValue(System.Guid valueToSet)
		{
			SetIdValue(valueToSet, true, true);
		}

		public virtual void SetIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_id != valueToSet)
			{
              if (this.ObjectsDataSet != null && !this.IsNew)
                {
                    throw new GOServerException("PK field of an existing object is being changed");
                }
				_id = valueToSet;

				OnPropertyChanged("Id", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("PrimaryKey", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Id property of the Session DataObject</summary>
        public virtual System.Guid Id 
		{
			get	{ return _id;}
			
			
			set
			{
				SetIdValue(value);
			}
		}		
			
			
		public virtual void SetisVORValue(Nullable<System.Boolean> valueToSet)
		{
			SetisVORValue(valueToSet, true, true);
		}

		public virtual void SetisVORValue(Nullable<System.Boolean> valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_isVOR != valueToSet)
			{
				_isVOR = valueToSet;

				OnPropertyChanged("isVOR", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The isVOR property of the Session DataObject</summary>
        public virtual Nullable<System.Boolean> isVOR 
		{
			get	{ return _isVOR;}
			
			
			set
			{
				SetisVORValue(value);
			}
		}		
			
			
		public virtual void SetStartTimeValue(System.DateTime valueToSet)
		{
			SetStartTimeValue(valueToSet, true, true);
		}

		public virtual void SetStartTimeValue(System.DateTime valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (valueToSet == null || valueToSet == DateTime.MinValue) 
			{
				if (_startTime != DateTime.MinValue.ToUniversalTime())
				{
					_startTime = DateTime.MinValue.ToUniversalTime();
					OnPropertyChanged("StartTime", notifyChanges, dirtyHandlerOn);
				}	
			}
			else if (valueToSet == DateTime.MaxValue)
			{
				if (_startTime != DateTime.MaxValue.ToUniversalTime())
				{
					_startTime = DateTime.MaxValue.ToUniversalTime();
					OnPropertyChanged("StartTime", notifyChanges, dirtyHandlerOn);
				}
			} 
			else if (_startTime != valueToSet ||
                (_startTime != null && ((DateTime)_startTime).Kind == DateTimeKind.Unspecified))
			{
				_startTime = DateTime.SpecifyKind(valueToSet, DateTimeKind.Local);
				OnPropertyChanged("StartTime", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The Start Time property of the Session DataObject</summary>
        public virtual System.DateTime StartTime 
		{
			get	{ return _startTime;}
			
			
			set
			{
				SetStartTimeValue(value);
			}
		}		
			
			
		public virtual void SetVehicleIdValue(System.Guid valueToSet)
		{
			SetVehicleIdValue(valueToSet, true, true);
		}

		public virtual void SetVehicleIdValue(System.Guid valueToSet, bool notifyChanges, bool dirtyHandlerOn)
		{
			if (_vehicleId != valueToSet)
			{
				_vehicleId = valueToSet;

				// VehicleId is a FK. Setting its value should result in a event
				OnPropertyChanged("Vehicle", notifyChanges, dirtyHandlerOn);
				OnPropertyChanged("VehicleId", notifyChanges, dirtyHandlerOn);
			}
		}
		
		/// <summary> The VehicleId property of the Session DataObject</summary>
        public virtual System.Guid VehicleId 
		{
			get	{ return _vehicleId;}
			
			
			set
			{
				SetVehicleIdValue(value);
			}
		}		
		#endregion
		
		#region Business rules implementation

		
		protected override void OnPropertyChanged(string propertyName, bool notifyChanges, bool dirtyHandlerOn, SeenObjectCollection callers)
        {
            // This is to avoid infinite loops: in case a caller receives back a property changed notification it has initially sent
            if (callers.GetSeenObject(this) != null)
                return;

            base.OnPropertyChanged(propertyName, notifyChanges, dirtyHandlerOn, callers);
			
            if (!notifyChanges)
                return;

			
			// Push the notification to related objects
			var _checklistResults = GetChecklistResultsAsync(false).Result;
			if (_checklistResults != null)
            {
                foreach (var item in _checklistResults)
                {
                    item.NotifyPropertyChanged(String.Concat("Session.", propertyName), callers);                    
                }
            }
			var _detailedSessionViewItems = GetDetailedSessionViewItemsAsync(false).Result;
			if (_detailedSessionViewItems != null)
            {
                foreach (var item in _detailedSessionViewItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Session.", propertyName), callers);                    
                }
            }
			var _detailedVORSessionStoreProcedureItems = GetDetailedVORSessionStoreProcedureItemsAsync(false).Result;
			if (_detailedVORSessionStoreProcedureItems != null)
            {
                foreach (var item in _detailedVORSessionStoreProcedureItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Session.", propertyName), callers);                    
                }
            }
			var _gPSHistoryItems = GetGPSHistoryItemsAsync(false).Result;
			if (_gPSHistoryItems != null)
            {
                foreach (var item in _gPSHistoryItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Session.", propertyName), callers);                    
                }
            }
			var _impacts = GetImpactsAsync(false).Result;
			if (_impacts != null)
            {
                foreach (var item in _impacts)
                {
                    item.NotifyPropertyChanged(String.Concat("Session.", propertyName), callers);                    
                }
            }
			var _pSTATDetailsItems = GetPSTATDetailsItemsAsync(false).Result;
			if (_pSTATDetailsItems != null)
            {
                foreach (var item in _pSTATDetailsItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Session.", propertyName), callers);                    
                }
            }
			var _sessionDetailsItems = GetSessionDetailsItemsAsync(false).Result;
			if (_sessionDetailsItems != null)
            {
                foreach (var item in _sessionDetailsItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Session.", propertyName), callers);                    
                }
            }
			var _vehicleGPSItems = GetVehicleGPSItemsAsync(false).Result;
			if (_vehicleGPSItems != null)
            {
                foreach (var item in _vehicleGPSItems)
                {
                    item.NotifyPropertyChanged(String.Concat("Session.", propertyName), callers);                    
                }
            }
			var vehicleLastGPSLocationView = GetVehicleLastGPSLocationViewAsync(false).Result;
			if (vehicleLastGPSLocationView != null && this.IsDirty)
            {
				vehicleLastGPSLocationView.NotifyPropertyChanged("Session." + propertyName, callers);
			}
			var _vehicleLockouts = GetVehicleLockoutsAsync(false).Result;
			if (_vehicleLockouts != null)
            {
                foreach (var item in _vehicleLockouts)
                {
                    item.NotifyPropertyChanged(String.Concat("Session.", propertyName), callers);                    
                }
            }
        }		

		#endregion
								
        #region Implementation of IDataObject

	    public override IDataObject Clone()
	    {
	        return Clone(true);
	    }
		
		public override IDataObject Clone(bool recursive)
	    {
			var newobject = _serviceProvider.GetRequiredService<SessionDataObject>();
			newobject.CopyValuesFrom(this , recursive);    
			return newobject;
	    }

		public override bool Compare(IDataObject obj)
		{
			if(!(obj is SessionDataObject))
				return false;

			var p = (SessionDataObject) obj;
			var fieldsComparison = true;
			fieldsComparison &= this.DriverId == p.DriverId;
			fieldsComparison &= this.EndTime == p.EndTime;
			fieldsComparison &= this.Id == p.Id;
			fieldsComparison &= this.isVOR == p.isVOR;
			fieldsComparison &= this.StartTime == p.StartTime;
			fieldsComparison &= this.VehicleId == p.VehicleId;
			return fieldsComparison;
		}

		
		#endregion

        public override void AttachEventHandlers()
        {
        }

		public override string PrimaryKeyString
		{
			get
			{
				return $"{Id}";
			}
		}

		// Intended for use only by DataProvider to sync database generated PK to this instance
		public override void AssignPrimaryKey(object pk) 
		{
			this.Id = (System.Guid)pk;
		}

		public override void OnDeserialized(bool dataset = true)
		{
			OnDataObjectDeserialized();

			if (dataset)
			{
				if (this.ObjectsDataSet != null)
				{
					foreach (var obj in this.ObjectsDataSet.GetAllObjects().OfType<DataObject>().Where(o => o != this))
					{
						obj.OnDeserialized(dataset: false);
					}
				}
			}
		}

		private void OnDataObjectDeserialized()
		{	
			// StartTime is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._startTime_WithTimezoneOffset != null)
			{
				this.StartTime = ((DateTimeOffset)this._startTime_WithTimezoneOffset).DateTime;
			}
			// EndTime is an absolute datetime: Ensure we preserve the user-local timezone datetime
			if (this._endTime_WithTimezoneOffset != null)
			{
				this.EndTime = ((DateTimeOffset)this._endTime_WithTimezoneOffset).DateTime;
			}
        }
	}

	[JsonObject(MemberSerialization.OptIn)]
	public class SessionCollectionContainer
	{
		protected IServiceProvider _serviceProvider;
		
		public SessionCollectionContainer(IServiceProvider provider)
		{
	  		_serviceProvider = provider;
		}

		[JsonProperty("InternalObjectIds")]
		public List<int> InternalObjectIds { get; set; }

		[JsonProperty("PrimaryKeys")]
		public List<System.Guid> PrimaryKeys { get; set; }
	
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }

		[ActivatorUtilitiesConstructor]
		public SessionCollectionContainer()
		{
		}
		
		public SessionCollectionContainer Construct(DataObjectCollection<SessionDataObject> sessionItems)
        {
            if (sessionItems == null)
                return this;
				
			this.PrimaryKeys = sessionItems.Select(c => c.PrimaryKey).ToList();
            if (sessionItems.ObjectsDataSet == null)
            {
                sessionItems.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            }
	
			this.InternalObjectIds = sessionItems.Select(c => c.InternalObjectId).Cast<int>().ToList();
            this.ObjectsDataSet = sessionItems.ObjectsDataSet;

			return this;
		}

		public DataObjectCollection<SessionDataObject> ExtractSessionItems()
        {
			if (InternalObjectIds == null)
				return null;

            var result = new DataObjectCollection<SessionDataObject> {ObjectsDataSet = this.ObjectsDataSet };

			result.ObjectsDataSet.DirtyHandlerOn = false;
			result.ObjectsDataSet.NotifyChanges = false;


			foreach (var internalObjectId in InternalObjectIds)
            {
                var item = this.ObjectsDataSet.GetObject<SessionDataObject>(typeof(SessionDataObject), internalObjectId);
                result.Add(item);
            }

			result.ObjectsDataSet.DirtyHandlerOn = true;
			result.ObjectsDataSet.NotifyChanges = true;
		
            return result;
        }
	}

	// Container classes
	
	[JsonObject(MemberSerialization.OptIn)]
	public class SessionContainer 
	{
		IServiceProvider _serviceProvider;

		protected ILogEngine _logEngine => _serviceProvider.GetRequiredService<ILogEngine>();
		[JsonProperty("InternalObjectId")]
		public int InternalObjectId { get; set; }

		[JsonProperty("PrimaryKey")]
		public System.Guid PrimaryKey { get; set; }
		[JsonProperty("ObjectsDataSet")]
		public IObjectsDataSet ObjectsDataSet { get; set; }
        [ActivatorUtilitiesConstructor]
		public SessionContainer(IServiceProvider serviceProvider)
		{
			_serviceProvider = serviceProvider;
		}

		public virtual SessionContainer Construct(SessionDataObject session, bool includeDirtyObjectsOnly = false)
		{
            if (session == null)
                return this;

			this.PrimaryKey = session.PrimaryKey;
			
            if (session.ObjectsDataSet == null)
            {
                var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                dataset.AddObject(session);
            }

			if(session.ObjectsDataSet == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to set a dataset to the Entity Session", "Unable to set a dataset to the entity. Container may not be initialized", "SessionDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to set a dataset to the entity : Session");
			}

			if(session.InternalObjectId == null)
			{
				// remove logging to avoid to have to make the caller async. Do we really need logging here ?
				// await _logEngine.LogErrorAsync("Unable to construct an object without InternalObjectId in SessionDataObject", "The Object you are trying to construct doesn't have an InternalObjectId", "SessionDataObject", null);
				throw new GOServerException("Unexpected Error : Unable to construct an object without InternalObjectId in SessionDataObject");
			}
			this.InternalObjectId = (int) session.InternalObjectId;
			this.ObjectsDataSet = includeDirtyObjectsOnly ? session.ObjectsDataSet.CloneDirtyObjects() : session.ObjectsDataSet;

			return this;
		}
		
		public SessionDataObject ExtractSession()
        {
            if (InternalObjectId == 0)
                return null;

            var result = this.ObjectsDataSet.GetObject<SessionDataObject>(typeof(SessionDataObject), InternalObjectId);
			
			return result;
        }	
	}

}