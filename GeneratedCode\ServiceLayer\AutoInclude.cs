﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic;

namespace FleetXQ.ServiceLayer.IncludeMapping
{
	public partial class AutoInclude : IAutoInclude
	{
		partial void CustomInit();

		public AutoInclude()
		{
			CustomInit();
		}

		public IEnumerable<string> Lookup(string key)
		{
			if (_map.ContainsKey(key))
				return _map[key];

			return null;
		}

		public void Resolve(List<string> includes)
		{
			if (includes == null || !includes.Any())
				return;

			var autoIncludes = includes.Where(i => i.StartsWith("auto-include-id-")).ToArray();

			foreach (var i in autoIncludes)
			{
				includes.Remove(i);
				
				var includeList = Lookup(i);
				if (includeList != null && includeList.Any())
				{ 
					includes.AddRange(includeList);
				}
			}
		}

		private Dictionary< string, string[] > _map = new Dictionary< string, string[] >
		{
			// 
			// Forms
			// 
			// AccessGroupForm 
			{  "auto-include-id-09ad4ce5-d9c1-4cc2-b85c-05ffa118e399", new string[] {  } },
			// AccessGroupForm1 
			{  "auto-include-id-1611a163-3ed0-4c3e-8dd5-458b63a4a93b", new string[] {  } },
			// AccessGroupForm2 
			{  "auto-include-id-061cbc35-099c-40ea-84a0-b90aafa4a703", new string[] {  } },
			// AccessGroupForm3 
			{  "auto-include-id-23e3db11-99aa-4399-9a64-9f8bfe5e697f", new string[] {  } },
			// AccessGroupTemplateForm 
			{  "auto-include-id-b8f980eb-7a8a-45df-9798-aa004429372b", new string[] {  } },
			// AccessGroupToSiteForm 
			{  "auto-include-id-723b35f0-8335-4581-8b2c-523c0a42a1c8", new string[] { "Site", "AccessGroup" } },
			// AddCustomerCategoryForm 
			{  "auto-include-id-10688381-ed85-4b76-9cd9-58274ea4e361", new string[] {  } },
			// AddVehicleAlertSubscription 
			{  "auto-include-id-b86191ec-d1c5-489c-8940-aa58c2405810", new string[] {  } },
			// AlertSubscriptionForm 
			{  "auto-include-id-ecf63116-c41b-41ff-9366-e75d9b5e4d40", new string[] { "Alert" } },
			// AlertSubscriptionForm1 
			{  "auto-include-id-a0b829e8-3bb4-4f8e-9e93-a9cf14707c43", new string[] { "Person.Site", "Person.Department", "Person.Customer" } },
			// AllChecklistResultViewForm 
			{  "auto-include-id-1f0468bf-6ead-4f27-952a-e6a132dd16c1", new string[] { "ChecklistResult.ChecklistAnswerDetails" } },
			// AllVehicleCalibrationFilterForm 
			{  "auto-include-id-348d79e3-3126-44ff-b219-1adb50256bf1", new string[] { "DashboardFilter:Site", "DashboardFilter:Department", "DashboardFilter:Customer" } },
			// AllVehicleUnlocksViewForm 
			{  "auto-include-id-3138d2ff-b073-4d62-a3d9-27b56e754783", new string[] { "VehicleLockout" } },
			// AmberImpactForm 
			{  "auto-include-id-f2dbb8d3-6e0b-4fa8-ac56-4a6ba44e5655", new string[] {  } },
			// ApplyAccessGroupTemplateForm 
			{  "auto-include-id-ba348de8-aa3d-42b4-a6df-7140e0542328", new string[] {  } },
			// ApplyCategoryToCustomersForm 
			{  "auto-include-id-0bff7fd7-a2d6-4fd6-b2a2-29294aa0df7c", new string[] {  } },
			// BroadcastMessageForm 
			{  "auto-include-id-70fd08c7-5095-4b4a-a78e-072c2b8f2687", new string[] {  } },
			// BroadcastMessageHistoryFilterForm 
			{  "auto-include-id-7c1b013e-df47-4c9c-9723-fd5043feeb3c", new string[] { "DashboardFilter:Customer", "DashboardFilter:Department", "DashboardFilter:Site" } },
			// BulkUpdateFirmwareForm 
			{  "auto-include-id-06730256-6898-470b-bd71-c754a0b5fefd", new string[] { "Firmware" } },
			// CardDetailsCreateNewForm 
			{  "auto-include-id-091af906-1dda-489f-91d1-c112be905baa", new string[] {  } },
			// CardDetailsForm 
			{  "auto-include-id-6ec00045-01f1-4170-91d0-42010d49766a", new string[] {  } },
			// ChecklistDetailForm 
			{  "auto-include-id-b749e5c4-4e77-4af7-a1fa-ccec92007406", new string[] { "PreOperationalChecklist" } },
			// ChecklistResultForm 
			{  "auto-include-id-26c38b5d-f8a1-483d-927e-237f37c58035", new string[] {  } },
			// ChecklistSettingsForm 
			{  "auto-include-id-eeb005fe-4e7c-44ff-a209-208b1cf90c6d", new string[] {  } },
			// ContactPersonInformationForm 
			{  "auto-include-id-70725eb5-b8a7-4a7a-92ee-da5f00e3d9f0", new string[] {  } },
			// ContactPersonInformationForm1 
			{  "auto-include-id-c7c318f3-798d-44bc-a7f2-4e747fc3fa72", new string[] {  } },
			// CopyAccessGroupForm 
			{  "auto-include-id-f752c5b5-cc70-47ba-b6b9-5681888caadc", new string[] {  } },
			// CopyDriverAccessSettingsForm 
			{  "auto-include-id-81146709-17e6-4e80-b000-c01a642139cf", new string[] { "Customer.DriverItems" } },
			// CountryCreateNewForm 
			{  "auto-include-id-cb2b3144-5964-46bb-b991-5bfa11a0ec93", new string[] {  } },
			// CountryForm 
			{  "auto-include-id-cd90768a-6154-4505-9053-dd7cbc745a07", new string[] {  } },
			// CreateNewDealerUserForm 
			{  "auto-include-id-5781e37c-3734-4175-83ce-2e18141f8067", new string[] {  } },
			// CurrentStatusCombinedViewForm 
			{  "auto-include-id-e4ed712a-89d0-430a-8de5-8760574df06e", new string[] {  } },
			// CustomerAccessGroupTemplateForm 
			{  "auto-include-id-e023ef81-62d6-4757-81da-370459fb4efb", new string[] {  } },
			// CustomerCreateNewForm 
			{  "auto-include-id-3610b7fe-b294-4ccc-bb8b-40e64b78063a", new string[] { "Country", "Dealer" } },
			// CustomerFeatureSubscriptionForm 
			{  "auto-include-id-446939be-be31-4ffb-a486-4e3adb841d3b", new string[] {  } },
			// CustomerForm 
			{  "auto-include-id-39da08d4-b6e3-4c7e-8bc6-11066a12694f", new string[] { "ContactPersonInformation", "Country" } },
			// CustomerForm1 
			{  "auto-include-id-2cf8cf7c-6b6c-4a2f-a07b-51757f07d801", new string[] { "Country" } },
			// CustomerForm2 
			{  "auto-include-id-8b8dc871-d4a5-4f94-935c-d4b5d9b57fa9", new string[] { "ContactPersonInformation" } },
			// CustomerForm3 
			{  "auto-include-id-f6938684-74bf-4108-ba2b-31adcf6c3209", new string[] { "CustomerFeatureSubscription" } },
			// CustomerForm4 
			{  "auto-include-id-60c74721-aa85-4b16-a24c-f35d515f10d5", new string[] { "CustomerFeatureSubscription" } },
			// CustomerModelForm 
			{  "auto-include-id-cf58acfd-f8e6-4ace-894d-f566838a991a", new string[] { "Model" } },
			// CustomerPreOperationalChecklistTemplateForm 
			{  "auto-include-id-68e4d68d-3111-48c5-9d48-9e7041a2af93", new string[] {  } },
			// CustomerPreOperationalChecklistTemplateForm1 
			{  "auto-include-id-cfee0900-40f7-4444-914c-37cd024ead92", new string[] {  } },
			// CustomerPreOperationalChecklistTemplateForm2 
			{  "auto-include-id-21300212-392d-4ee1-9d48-15dc7c37ed82", new string[] { "Customer.CustomerModelItems.Model.SiteChecklists.PreOperationalChecklists", "Customer.Sites.DepartmentItems" } },
			// CustomerSSODetailForm 
			{  "auto-include-id-05334d88-3c17-4931-82df-97caa49a5e6d", new string[] { "Customer" } },
			// CustomerToModelForm 
			{  "auto-include-id-3b36e741-727d-4fdf-8ca7-7f0bef50a4bd", new string[] { "Model" } },
			// DashboardCardViewForm 
			{  "auto-include-id-6d27c55b-c941-44a4-8e23-3b7521467ab0", new string[] {  } },
			// DashboardCardViewForm1 
			{  "auto-include-id-c95e6926-1e4a-4f78-97a6-63f88d41d8e6", new string[] {  } },
			// DashboardCardViewForm2 
			{  "auto-include-id-83d6989c-6a63-4f62-84a1-4a7f932d6652", new string[] {  } },
			// DashboardCardViewForm3 
			{  "auto-include-id-640f03f4-078a-4f16-b0e4-bba7e6dc3903", new string[] {  } },
			// DashboardDriverCardStoreProcedureForm 
			{  "auto-include-id-abd9343a-b5b2-46df-a930-2248d0d4dc87", new string[] {  } },
			// DashboardFilterForm 
			{  "auto-include-id-28cb6569-6a62-4d68-9596-62d98e1cccc2", new string[] { "Customer", "Site", "Department" } },
			// DashboardVehicleCardStoreProcedureForm 
			{  "auto-include-id-cf2cc1b9-5d4a-48dd-8051-d003c671bf62", new string[] {  } },
			// DashboardVehicleCardStoreProcedureForm1 
			{  "auto-include-id-e71ccb92-f507-4675-b6dc-2391d8855905", new string[] {  } },
			// DealerCreateNewForm 
			{  "auto-include-id-c86b9add-133a-4c60-9093-3bccd98ce38e", new string[] { "Region" } },
			// DealerDriverForm 
			{  "auto-include-id-2386a53d-8dc2-4ee1-a523-ea2c41db2a6a", new string[] {  } },
			// DealerFeatureSubscriptionForm 
			{  "auto-include-id-a01d6a4b-1783-40d1-809a-53d82b18b203", new string[] {  } },
			// DealerForm 
			{  "auto-include-id-675c4161-4511-4a96-963c-970f25da3762", new string[] { "Region" } },
			// DealerForm1 
			{  "auto-include-id-38ec9154-3693-407c-873d-0f229edb0619", new string[] { "Region" } },
			// DealerForm2 
			{  "auto-include-id-c37c53bf-bd29-4a3f-9467-aac373ac29cb", new string[] { "Region" } },
			// DealerForm3 
			{  "auto-include-id-c7c1a157-9b09-4549-bb93-4d57aff5a679", new string[] { "DealerFeatureSubscription" } },
			// DealerForm4 
			{  "auto-include-id-6a26b6d9-11e1-4833-849f-91f42670be6c", new string[] {  } },
			// DealerUserDetailForm 
			{  "auto-include-id-3773604d-8043-4fee-ba09-559c4b04c23c", new string[] { "DealerDriver.Card" } },
			// DepartmentChecklistForm 
			{  "auto-include-id-aa384103-6a23-4b72-b1de-e2dbbc19f50b", new string[] {  } },
			// DepartmentCreateNewForm 
			{  "auto-include-id-08ce79ec-083e-4f9f-80d6-3b161d7476f0", new string[] {  } },
			// DepartmentForm 
			{  "auto-include-id-f104314c-f0f1-4b65-9460-b51de69f86e8", new string[] { "DepartmentHourSettings" } },
			// DepartmentHourSettingsForm 
			{  "auto-include-id-4b58c05e-3c51-4ab3-8a10-367b8629038c", new string[] {  } },
			// DriverAccessAbuseFilterForm 
			{  "auto-include-id-2188425e-1f39-43fc-a71c-4d22b2c02c2e", new string[] { "DashboardFilter:Site", "DashboardFilter:Department", "DashboardFilter:Customer" } },
			// DriverForm3 
			{  "auto-include-id-4d50742d-a3e2-4240-8863-2d81e8f5b834", new string[] {  } },
			// DriverForm4 
			{  "auto-include-id-9a6770da-d1dd-4b2e-9650-ee13252e31e9", new string[] { "Card" } },
			// DriverLicensesForm 
			{  "auto-include-id-7ef3c320-a091-4880-8662-bde23891f45c", new string[] { "LicensesByModel.Model", "Person", "GeneralLicence" } },
			// DriverSessionsForm 
			{  "auto-include-id-6d244e1a-2c61-4f45-9cbb-f622e482ae55", new string[] { "Driver.Person.Department", "Driver.Person.Site", "Driver.DetailedSessionViewItems" } },
			// EditSlamcoreDeviceForm 
			{  "auto-include-id-afa60fa3-81a8-4416-afe4-ddc56f0e083b", new string[] { "Vehicle" } },
			// EmailGroupsForm 
			{  "auto-include-id-ff690002-12b7-44d1-b7f6-36ca2f5e5fa8", new string[] {  } },
			// EmailGroupsForm1 
			{  "auto-include-id-244b9009-dcee-448b-ac8b-8a376123e650", new string[] {  } },
			// EmailGroupsToPersonForm 
			{  "auto-include-id-dd51bceb-5d2f-4cd8-8b4d-6c0b2ca73d6a", new string[] { "Person", "EmailGroups" } },
			// EmailSubscriptionReportFilterForm 
			{  "auto-include-id-e8f1924f-8a6e-48d0-bf50-2e53e29012f7", new string[] { "DashboardFilter:Department", "DashboardFilter:Customer", "DashboardFilter:Site" } },
			// ExportJobStatusForm 
			{  "auto-include-id-8cf3f3a6-7ae1-4a9a-bde1-d23890eafb04", new string[] {  } },
			// FeatureSubscriptionsFilterForm 
			{  "auto-include-id-b2f18560-ba49-431e-882b-289b248674c9", new string[] { "Dealer", "DashboardFilter:Customer" } },
			// FirmwareForm 
			{  "auto-include-id-9eccbf64-04f7-4e8d-9053-f7af5983cb14", new string[] {  } },
			// FirmwareSettings 
			{  "auto-include-id-fc3cbd78-250d-4e67-9d56-b19f03ae3863", new string[] {  } },
			// FloorPlanForm 
			{  "auto-include-id-3be76c00-04a7-447d-b09e-af4d755ba121", new string[] {  } },
			// GeneralProductivityPerDriverViewLatestForm 
			{  "auto-include-id-996c6efa-ca56-4849-93bb-6d3a0bdfd00c", new string[] { "GeneralProductivityView.GeneralProductivityPerVehicleViewItems" } },
			// GeneralProductivityReportFilterForm 
			{  "auto-include-id-6697af10-7b80-4144-b2b8-2b11fa5effc5", new string[] { "DashboardFilter:Site", "DashboardFilter:Customer", "DashboardFilter:Department" } },
			// GeneralProductivityViewForm 
			{  "auto-include-id-8896b5f1-5bcf-4a4a-800d-ca4c829ef1d9", new string[] {  } },
			// GForceRequiredToCauseImpacts 
			{  "auto-include-id-bc193b68-c182-4e19-bca8-ac0e64e01539", new string[] {  } },
			// GO2FAConfigurationForm 
			{  "auto-include-id-9a1f75da-fa21-4f07-a145-c683167fb1ea", new string[] {  } },
			// GOUserDepartmentForm 
			{  "auto-include-id-664bfbe6-9b82-4b50-ba85-ebe96b8ac45b", new string[] { "Department" } },
			// GOUserForm 
			{  "auto-include-id-fe29f9c5-b5fe-4902-b524-c50340710b14", new string[] {  } },
			// GOUserForm1 
			{  "auto-include-id-d147641c-48c9-4a1d-9599-2a199e0db2e3", new string[] {  } },
			// GOUserForm2 
			{  "auto-include-id-80c189e9-66a7-4fe8-9583-9fd702976143", new string[] { "Dealer" } },
			// GOUserForm3 
			{  "auto-include-id-3bd57677-7bae-40eb-bbd9-81b8b18d7c76", new string[] { "Dealer" } },
			// GOUserForm4 
			{  "auto-include-id-0c99683e-87e6-4a4c-a0c0-72a02ac64d9c", new string[] { "DealerDriver.Card" } },
			// GOUserRoleForm 
			{  "auto-include-id-04ad8e0c-c32b-436f-ae2c-7d7e44f8ddb6", new string[] { "Role" } },
			// GoUserToCustomerForm 
			{  "auto-include-id-24a44e99-935d-4e32-b22f-07a9ae247911", new string[] {  } },
			// HelpForm 
			{  "auto-include-id-b5717e55-6e50-4a97-b2dc-7c458e134f7f", new string[] {  } },
			// HireDeHireReportFilterForm 
			{  "auto-include-id-56cd2893-031d-4f88-b2a3-cae2037a766e", new string[] { "DashboardFilter:Customer", "DashboardFilter:Department", "DashboardFilter:Site" } },
			// ImpactForm 
			{  "auto-include-id-b0eb3399-aea2-4304-8506-6e867cb93429", new string[] { "Session" } },
			// ImpactLocationForm 
			{  "auto-include-id-581e94ef-c9b9-4148-a333-c22801f33422", new string[] { "Impact.Session.Vehicle" } },
			// ImpactReportFilterForm 
			{  "auto-include-id-aecf0f8b-fdd8-4182-bc4b-962f82585367", new string[] { "DashboardFilter:Site", "DashboardFilter:Customer", "DashboardFilter:Department" } },
			// ImpactSettings 
			{  "auto-include-id-e1260545-c6a6-49c5-9bae-2b9b093e2743", new string[] { "Module" } },
			// ImportJobLogForm 
			{  "auto-include-id-cd4ea3a6-0fdf-4b2d-a384-3c20b539b5aa", new string[] {  } },
			// ImportJobStatusForm 
			{  "auto-include-id-0bb34afb-a9cd-445d-b13b-0d5cbc94ebaf", new string[] {  } },
			// InspectionForm 
			{  "auto-include-id-b466ff4e-96b6-4904-9a3e-eb11002b0f56", new string[] {  } },
			// LicenceDetailCardForm 
			{  "auto-include-id-cfe9e1a9-2722-4c1f-8eae-b35ccdfdafd0", new string[] {  } },
			// LicenceDetailForm 
			{  "auto-include-id-217c4197-5d0a-4a33-970a-cd824fcf0362", new string[] {  } },
			// LicenceDetailForm1 
			{  "auto-include-id-a735c90e-a982-4ded-b47a-3b1a9b0cf927", new string[] {  } },
			// LicenseActiveForm 
			{  "auto-include-id-d97a64a7-2f1c-447a-a5eb-b28b4d26e606", new string[] {  } },
			// LicenseByModelForm 
			{  "auto-include-id-9a8e3079-a103-442a-838e-e7a46ed964da", new string[] { "Model" } },
			// LicenseExpiryReportFilterForm 
			{  "auto-include-id-b34d1196-f407-4e43-840a-2cd646e440be", new string[] { "DashboardFilter:Site", "DashboardFilter:Department", "DashboardFilter:Customer" } },
			// MachineUnlockReportFilterForm 
			{  "auto-include-id-4ca2391b-c3ca-4c4c-939e-0cf42746362f", new string[] { "DashboardFilter:Site", "DashboardFilter:Customer", "DashboardFilter:Department" } },
			// MainDashboardFilterForm 
			{  "auto-include-id-0ff38c51-5c4c-4e09-8778-d2222cdb95d2", new string[] { "DashboardFilter:Site", "DashboardFilter:Department", "DashboardFilter:Customer" } },
			// ModelCreateNewForm 
			{  "auto-include-id-4ff672f8-ef86-4514-a27a-4f8f7751f7c3", new string[] {  } },
			// ModelForm 
			{  "auto-include-id-87e30438-6c44-467c-bef4-22a56ac00ccd", new string[] {  } },
			// ModelForm1 
			{  "auto-include-id-c1984be9-964d-4aea-a10c-177a87343bbb", new string[] {  } },
			// ModelForm2 
			{  "auto-include-id-1c6941d6-b67a-44e6-9a42-f44c08a05a48", new string[] {  } },
			// ModuleDetailForm 
			{  "auto-include-id-243b2170-ebdc-4c82-8d5f-3b0ce9417f12", new string[] {  } },
			// ModuleForm 
			{  "auto-include-id-9c25187a-6d35-4d2b-b1ea-8f7aca5622ad", new string[] {  } },
			// ModuleForm1 
			{  "auto-include-id-5a1b6641-bb4e-4548-9c4c-292f9879dd80", new string[] {  } },
			// ModuleForm2 
			{  "auto-include-id-7d847eaa-a1ae-46de-8884-6d9a22ce7855", new string[] {  } },
			// ModuleForm3 
			{  "auto-include-id-d1b0559a-b338-418d-97b7-4885f197f51e", new string[] {  } },
			// NetworkSettingsForm 
			{  "auto-include-id-0684839b-be0f-49d0-956c-9296831b6e47", new string[] {  } },
			// NewSlamcoreDeviceForm 
			{  "auto-include-id-2861032b-b8cf-4e36-bafe-c6b0944b7c08", new string[] { "Customer", "Vehicle" } },
			// NextServiceSettingsForm 
			{  "auto-include-id-8a8df333-1d72-429f-b099-a7b27c42eaac", new string[] {  } },
			// OnDemandAuthorisationFilterForm 
			{  "auto-include-id-d4612ee4-722a-41e2-8402-1c435ed99e83", new string[] { "DashboardFilter:Department", "DashboardFilter:Customer", "DashboardFilter:Site" } },
			// OnDemandSettingsForm 
			{  "auto-include-id-0cd3037d-adc9-438d-86b4-5b08325b11bf", new string[] {  } },
			// OnDemandSettingsForm1 
			{  "auto-include-id-cc6e1b8a-ef92-404b-8413-175f993c7dca", new string[] {  } },
			// PedestrianDetectionHistoryFilterForm 
			{  "auto-include-id-fe756e7e-9a39-4930-8f68-c891dbee3f7d", new string[] { "DashboardFilter:Customer", "DashboardFilter:Department", "DashboardFilter:Site" } },
			// PermissionForm 
			{  "auto-include-id-8f27e79b-6988-4851-8c58-aadf8e33166d", new string[] {  } },
			// PermissionForm1 
			{  "auto-include-id-60cc4bcb-b953-4ee8-8f59-fc45a207b087", new string[] {  } },
			// PersonAlertSubscriptionsForm 
			{  "auto-include-id-231964ca-ccc5-48a8-8746-be8a6126f15a", new string[] {  } },
			// PersonAllocationForm 
			{  "auto-include-id-098e4429-c64e-449d-a054-94f6414b1047", new string[] { "Department", "Site", "Person" } },
			// PersonChecklistLanguageSettingsForm 
			{  "auto-include-id-7e7d8669-e423-4a02-a465-bd6d8acff3bb", new string[] {  } },
			// PersonCreateNewForm 
			{  "auto-include-id-76822fea-4c83-4f05-810b-c99ea37b5fd3", new string[] { "Site", "Customer", "Department" } },
			// PersonDetailsHeaderForm 
			{  "auto-include-id-6c1cfa82-bcdd-4c72-9418-91d66e6122c5", new string[] {  } },
			// PersonForm 
			{  "auto-include-id-2cdbeb9c-1683-41c4-8f60-7469a720fe08", new string[] { "Customer.EmailGroupsItems", "GOUser.GOUserDepartmentItems", "Driver.Card", "Driver.LicensesByModel.Model", "Driver.GeneralLicence", "PersonChecklistLanguageSettings", "AccessGroup", "PersonToModelVehicleNormalAccessViewItems", "PersonToSiteVehicleNormalAccessViewItems", "PersonToDepartmentVehicleNormalAccessViewItems", "PersonToPerVehicleNormalAccessViewItems", "Site", "Department" } },
			// PersonInformationForm 
			{  "auto-include-id-2155dd7a-81be-48f1-a0b9-5c6da56f94d0", new string[] { "Site", "Department", "Customer" } },
			// PersonToDepartmentVehicleMasterAccessViewForm 
			{  "auto-include-id-2e38af9a-765b-44b5-8ddc-1fe044cd9e2f", new string[] {  } },
			// PersonToDepartmentVehicleNormalAccessViewForm 
			{  "auto-include-id-5d8cb012-a38e-4362-b904-578e26f561f0", new string[] {  } },
			// PersonToModelVehicleMasterAccessViewForm 
			{  "auto-include-id-29f33b2b-7f15-4001-8967-d343f60c799a", new string[] {  } },
			// PersonToModelVehicleNormalAccessViewForm 
			{  "auto-include-id-1fdd448d-38ad-4a8b-9548-2e3fcbc47def", new string[] {  } },
			// PersonToPerVehicleMasterAccessViewForm 
			{  "auto-include-id-bd5d6c22-66e8-4477-ad1f-6c0cbfd5fe07", new string[] {  } },
			// PersonToPerVehicleNormalAccessViewForm 
			{  "auto-include-id-bf366a59-1199-4493-85cd-9c6e1ed5fcdd", new string[] {  } },
			// PersonToSiteVehicleMasterAccessViewForm 
			{  "auto-include-id-889391f9-06f9-491e-8ec0-ffb652d7ca42", new string[] { "Site" } },
			// PersonToSiteVehicleNormalAccessViewForm 
			{  "auto-include-id-2676a9a3-0c4b-4bd5-a4f1-e5b189d794cc", new string[] { "Site" } },
			// PersonVehicleAccessForm 
			{  "auto-include-id-d69ed8b0-e4a4-4a29-8d71-58ac266b05fb", new string[] { "PersonToModelVehicleNormalAccessViewItems", "PersonToSiteVehicleNormalAccessViewItems", "PersonToDepartmentVehicleNormalAccessViewItems", "PersonToPerVehicleNormalAccessViewItems" } },
			// PersonWebsiteAccessForm 
			{  "auto-include-id-0bc6f5ae-c410-4389-858b-a9367ef3231d", new string[] { "AccessGroup", "GOUser" } },
			// PerVehicleNormalCardAccessForm 
			{  "auto-include-id-ed6e9bc3-3e1b-48e8-a777-dfc27944d5a1", new string[] {  } },
			// PreOperationalChecklistForm 
			{  "auto-include-id-4b791cab-f47e-40ca-92e1-a74de7f8ceab", new string[] {  } },
			// PreOperationalChecklistForm1 
			{  "auto-include-id-cd15c12b-9b13-4014-817f-b1b114dc06ef", new string[] {  } },
			// PreOpReportFilterForm 
			{  "auto-include-id-ef65f194-21a5-42cd-8bcf-10d29fdd13c7", new string[] { "DashboardFilter:Department", "DashboardFilter:Customer", "DashboardFilter:Site" } },
			// ProficiencyCombinedViewForm 
			{  "auto-include-id-2c7041ef-d26a-469c-8d0d-5f4fd32acce2", new string[] {  } },
			// ProficiencyReportFilterForm 
			{  "auto-include-id-bdd3e744-2c66-4eda-86d4-b0a2ae06e6d4", new string[] { "DashboardFilter:Site", "DashboardFilter:Customer", "DashboardFilter:Department" } },
			// RebootVehicle 
			{  "auto-include-id-e3e8bbdd-1599-4c52-9102-091dc763bc0e", new string[] {  } },
			// RedImpactForm 
			{  "auto-include-id-9509f102-56bc-4576-9695-4933cd810e92", new string[] {  } },
			// RegionCreateNewForm 
			{  "auto-include-id-fb13bfea-d31d-4f42-bd5c-b1f743422b18", new string[] {  } },
			// RegionForm 
			{  "auto-include-id-5757830b-1374-4c15-8098-59fea15e9348", new string[] {  } },
			// ReportsAccessGroupTemplateForm 
			{  "auto-include-id-fb6e66ee-41eb-4946-a746-c1c3a316299e", new string[] {  } },
			// ReportSubscriptionForm 
			{  "auto-include-id-8572850e-4ad6-41a3-b868-f32ccbe106aa", new string[] { "Customer", "Site", "Department", "ReportType" } },
			// SendReportForm 
			{  "auto-include-id-1edcda9d-d72d-4eb3-83f5-4a8b76a1ca91", new string[] {  } },
			// SensorCalibrationForm 
			{  "auto-include-id-76a40c19-a205-4193-a489-dc51beb32a18", new string[] {  } },
			// ServiceSettingsForm 
			{  "auto-include-id-dc5dfb15-6a53-42fd-9d28-17faedc634c0", new string[] {  } },
			// ServiceSettingsForm1 
			{  "auto-include-id-31ebe7a5-baeb-48f5-a473-38b062aff6c0", new string[] {  } },
			// SessionForm 
			{  "auto-include-id-a390fe9a-933d-4cc0-8176-5ea682a1800c", new string[] {  } },
			// SiteChecklistForm 
			{  "auto-include-id-60233be7-0026-4f9e-a3fc-8e4bd1324365", new string[] { "Model" } },
			// SiteChecklistForm1 
			{  "auto-include-id-b38cfa3e-0c5a-40a6-88ca-908848d552dd", new string[] { "Model" } },
			// SiteCreateNewForm 
			{  "auto-include-id-ed41695a-97ab-408b-ba17-33c004c8d22a", new string[] { "Timezone" } },
			// SiteFloorPlanForm1 
			{  "auto-include-id-5c8b0d35-19c2-4b2c-85f5-841a2a908d41", new string[] { "FloorPlan", "Site" } },
			// SiteForm 
			{  "auto-include-id-d1803122-ed17-4686-b1f5-d7351d8e27fe", new string[] {  } },
			// SiteForm1 
			{  "auto-include-id-b2fbd9ef-a897-4b83-ada6-a14f5819a621", new string[] { "Timezone" } },
			// SiteForm2 
			{  "auto-include-id-7b8f9afe-fb99-4b33-ac29-1a1e5c1c3f71", new string[] { "FloorPlanItems" } },
			// SiteForm3 
			{  "auto-include-id-58afdb7e-08e8-4ce2-91a8-c04439afbdac", new string[] { "FloorPlanItems" } },
			// SlamcoreAccountAuthenticationDetailsForm 
			{  "auto-include-id-cabaadb5-49d1-4cb5-82f1-460a4d47db37", new string[] {  } },
			// SlamcoreAPIKeyForm 
			{  "auto-include-id-6b4dd2a5-37ed-4fcd-ac36-b6260aa51b1e", new string[] {  } },
			// SlamcoreAwareAuthenticationDetailsForm 
			{  "auto-include-id-376d0d7a-2e77-428a-b81c-8ea69956d325", new string[] {  } },
			// SlamcoreDeviceAuthSettingsForm 
			{  "auto-include-id-6fe6b37e-c1e5-4cc8-8591-14f280de91dd", new string[] { "SlamcoreAPIKey", "SlamcoreAwareAuthenticationDetails" } },
			// SlamcoreDeviceConnectionViewForm 
			{  "auto-include-id-2f98f630-6588-4373-96e2-ec48beafbd90", new string[] {  } },
			// SlamcoreDeviceConnectionViewForm1 
			{  "auto-include-id-********-f240-404e-8f6f-05c40db219d3", new string[] {  } },
			// SlamcoreDeviceConnectionViewForm2 
			{  "auto-include-id-011d5b8a-783f-4c47-8267-964c2e54e354", new string[] {  } },
			// SlamcoreDeviceConnectionViewForm3 
			{  "auto-include-id-36b2288f-fb93-493b-90ac-ff896182967b", new string[] {  } },
			// SlamcoreDeviceFilterForm 
			{  "auto-include-id-afac93fc-8252-43ad-8d53-20e734824055", new string[] { "DashboardFilter:Customer", "DashboardFilter:Site" } },
			// SubscribeForm 
			{  "auto-include-id-5a938774-9907-4634-b4e2-e33936f4a3e4", new string[] { "Customer", "Department", "Site" } },
			// SupervisorAuthorization 
			{  "auto-include-id-215c913e-a576-436a-9893-846f4099cc05", new string[] {  } },
			// SupervisorVehicleAccessForm 
			{  "auto-include-id-ffdadd52-de63-4336-9d07-8c7e053a525c", new string[] { "PersonToModelVehicleNormalAccessViewItems", "PersonToPerVehicleNormalAccessViewItems", "PersonToSiteVehicleNormalAccessViewItems", "PersonToDepartmentVehicleNormalAccessViewItems" } },
			// SynchronizationStatusReportFilterForm 
			{  "auto-include-id-0a470f10-4491-490c-b2c1-5fbcf0be6af0", new string[] { "DashboardFilter:Department", "DashboardFilter:Site", "DashboardFilter:Customer" } },
			// TimezoneCreateNewForm 
			{  "auto-include-id-a37d464d-df11-48f5-bad4-3549792bb956", new string[] {  } },
			// TimezoneForm 
			{  "auto-include-id-2a7a365b-90ff-404b-9436-f3f141d388da", new string[] {  } },
			// UnlockVehicle 
			{  "auto-include-id-47075fc3-5d4a-455e-bc0e-0b4bb63e8616", new string[] {  } },
			// UpdateLastServiceDateForm 
			{  "auto-include-id-c93a89ed-f675-4e77-bf86-aea4ca3bf0cc", new string[] {  } },
			// UpdateQuestionOrderForm 
			{  "auto-include-id-c63ee975-79c1-49a3-a914-d77993485c60", new string[] {  } },
			// UpdateQuestionsOrderForm 
			{  "auto-include-id-f8dd04b1-e1be-4f2c-a9ae-30a4c9e98967", new string[] { "DepartmentChecklist.PreOperationalChecklists" } },
			// UpdateVehicleFirmware 
			{  "auto-include-id-9863d280-ee74-4111-94c7-d40d62ad8ba4", new string[] { "Firmware" } },
			// UploadFileForm 
			{  "auto-include-id-bd729cb2-9d97-47df-bfb0-c1c872bfe1b9", new string[] {  } },
			// UploadLogoRequestForm 
			{  "auto-include-id-5998f560-85d4-497e-870b-c1f568a18da6", new string[] {  } },
			// UsersAccessGroupForm 
			{  "auto-include-id-149f807a-9ee6-4251-afe9-4af6188ac93f", new string[] {  } },
			// UsersAccessGroupTemplateForm 
			{  "auto-include-id-c63775c4-8657-4c93-9396-00eb2dcd7182", new string[] {  } },
			// VehicleBroadcastMessageForm 
			{  "auto-include-id-1b80163f-17a3-4593-8f93-a35c00d1c514", new string[] {  } },
			// VehicleChecklistForm 
			{  "auto-include-id-d3dcafb1-fc3d-4c59-a5aa-9457fb53db5d", new string[] { "Department", "Model", "ChecklistSettings", "DepartmentChecklist" } },
			// VehicleDiagnosticForm 
			{  "auto-include-id-2cac9812-94e4-422b-8251-cf81b3f5b727", new string[] { "Vehicle.Module" } },
			// VehicleForm 
			{  "auto-include-id-abb67ca7-9416-4ec1-81cf-200c48955f88", new string[] { "ServiceSettings", "Department", "Model", "ChecklistSettings", "DepartmentChecklist", "Inspection", "Module", "Site", "Canrule", "Customer", "VehicleOtherSettings" } },
			// VehicleForm1 
			{  "auto-include-id-b6057b1b-9538-4487-8ccb-4a4862551e97", new string[] { "Model" } },
			// VehicleForm2 
			{  "auto-include-id-d6033751-7e14-4626-9cc5-1b83fb4ae913", new string[] {  } },
			// VehicleForm3 
			{  "auto-include-id-3f9f350c-b9ff-48cc-9faf-025b00694ecf", new string[] {  } },
			// VehicleForm4 
			{  "auto-include-id-ae8a59c6-503a-473b-bceb-505096a448fe", new string[] { "VehicleDiagnostic" } },
			// VehicleForm5 
			{  "auto-include-id-80335cc8-87a8-4557-b243-ed5bc022c8c4", new string[] { "Person", "OnDemandSettings" } },
			// VehicleHireDehireForm 
			{  "auto-include-id-84cca3c7-3859-4a57-b008-e9e1d50a07e6", new string[] { "Customer", "Department", "Site" } },
			// VehicleInformationForm 
			{  "auto-include-id-3c4d05e8-81fe-47a2-a125-0a4b9aef147d", new string[] { "Module", "Site", "Department", "Canrule", "Model", "Customer" } },
			// VehicleLockoutForm 
			{  "auto-include-id-ae999ab7-ef43-4dfe-99dc-d7485378392a", new string[] {  } },
			// VehicleNetworkSettingsForm 
			{  "auto-include-id-68453848-3e56-4b33-8dda-2c7ab46be0d7", new string[] {  } },
			// VehicleOnDemandSettingsForm 
			{  "auto-include-id-ca5b04f6-e682-4b9c-9484-8107153efd20", new string[] { "OnDemandSettings" } },
			// VehicleOtherSettingsForm 
			{  "auto-include-id-5bcd023d-0d3b-4a15-a023-2989c6cf6ded", new string[] {  } },
			// VehicleRAModuleSwapForm 
			{  "auto-include-id-f94fff0f-7d93-4f0b-ba9c-a2b84c3b4ccc", new string[] { "Customer.Dealer.ModuleItems" } },
			// VehiclesAccessGroupTemplateForm 
			{  "auto-include-id-00d94d20-0295-41e5-92a9-6d5355e3d41e", new string[] {  } },
			// VehicleServiceSettings 
			{  "auto-include-id-9b504001-bc72-470f-884c-bd956bc786c5", new string[] {  } },
			// VehicleServiceSettingsForm 
			{  "auto-include-id-5316e639-85f1-4ee9-ba0b-a98560115a93", new string[] {  } },
			// VehicleServiceStatusForm 
			{  "auto-include-id-73b43b05-81b6-455c-be86-95c1d60de97c", new string[] {  } },
			// VehicleSessionsForm 
			{  "auto-include-id-60e461e7-ce6d-41fe-ad4d-17e8195bcf73", new string[] { "Vehicle.Department", "Vehicle.Site", "Vehicle.DetailedSessionViewItems" } },
			// VehicleToPreOpChecklistViewForm 
			{  "auto-include-id-9002d3c8-20bd-446f-a03f-73b5683ff684", new string[] { "Vehicle.Model", "Vehicle.Department", "PreOperationalChecklist" } },
			// VehicleVORSessionsForm 
			{  "auto-include-id-45c0dc9b-00e7-4f78-b905-ac7019a4ed34", new string[] { "Vehicle.Department", "Vehicle.Site", "Vehicle.DetailedVORSessionStoreProcedureItems" } },
			// VehilceForm1 
			{  "auto-include-id-439d4c7f-778f-4936-b334-0c6202bcd80c", new string[] { "Site", "Model", "Department", "Customer", "Module", "Canrule" } },
			// VORReportCombinedViewForm 
			{  "auto-include-id-68d94fef-c8a0-4658-a8cf-497e4bc630b9", new string[] {  } },
			// VORReportFilterForm 
			{  "auto-include-id-3af2f891-f9bb-49ad-b25a-b9487cfa058d", new string[] { "DashboardFilter:Site", "DashboardFilter:Department", "DashboardFilter:Customer" } },
 		    // 
			// Grids in pages
			//
			// Grid AccessGroupTemplateGrid in page AccessGroupTemplatesPage
			{  "auto-include-id-6baa56f7-9709-4bfa-8bd1-5c382e0a7024-4933f1f9-c174-4ea4-9b57-28085f83258a", new string[] {  } },
			// Grid AlertHistoryGrid in page AlertReportPage
			{  "auto-include-id-bc390d44-6b2a-4c2c-9bce-0953f6c11656-8a9a971e-459c-4b6e-b538-823acc074e95", new string[] { "Vehicle", "Driver.Person", "Alert" } },
			// Grid BroadcastMessageHistoryGrid in page BroadcastMessageReportPage
			{  "auto-include-id-bc3ae718-8ca4-4bcb-9923-995fef3df3f2-e7678c16-6709-41be-b284-9dc820d408a5", new string[] { "Driver.Person", "Vehicle" } },
			// Grid SlamcoreDeviceGrid1 in page ConnectionStatusDashboardPage
			{  "auto-include-id-5e3a13fc-f85e-417e-858e-297ad5c3e4e5-10184ded-1f5d-44d6-8b31-b13e0ca87f1c", new string[] {  } },
			// Grid CountryGrid in page CountryItemsPage
			{  "auto-include-id-27b76ea3-d350-4449-af18-9c4f3b17cf52-343178ff-6c09-400e-a581-ba1ff370c8f8", new string[] {  } },
			// Grid CustomerGrid in page CustomerItemsPage
			{  "auto-include-id-4440c716-f98f-4e9a-8624-b8a0d4f066ce-a995f7de-d6ee-486d-a3ff-27058390d547", new string[] {  } },
			// Grid CustomerSSODetailGrid in page CustomerSSOPage
			{  "auto-include-id-b08c0398-0b7d-406e-91c4-4706dbcb6fbb-a5daece9-b8e5-4e85-a42b-0b81688fbb24", new string[] { "Customer" } },
			// Grid AllImpactsViewGrid in page DashboardImpactReportPage
			{  "auto-include-id-d9851c27-155e-4e2e-82a0-19202dcd0d1f-0444bba5-858e-4414-96e4-0155d482290a", new string[] { "Impact.Session.Vehicle.Model", "Impact.Session.Vehicle.Site", "Impact.Session.Vehicle.Department", "Impact.Session.Driver.Person", "Impact.Session.Driver.Card" } },
			// Grid AllChecklistResultViewGrid1 in page DashboardPreOpReportPage
			{  "auto-include-id-6f282d4b-b627-45de-a40f-74225767da27-bf33d15c-9cd2-4edb-b73c-1c445e3d388f", new string[] { "ChecklistResult.Session.Vehicle.Department", "ChecklistResult.Session.Vehicle.Model", "ChecklistResult.Session.Vehicle.Site", "ChecklistResult.Session.Driver.Person" } },
			// Grid AlertHistoryGrid1 in page DataExportPagePage
			{  "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-3aa74726-06eb-4523-bd76-31689039e46f", new string[] { "Vehicle", "Driver.Person" } },
			// Grid ExportJobStatusGrid2 in page DataExportPagePage
			{  "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-f6c1eb1d-719e-4f6d-8dac-652d9cf685e4", new string[] {  } },
			// Grid SlamcoreDeviceGrid2 in page DataExportPagePage
			{  "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-b40c80bb-1fba-4472-9ab5-8a710249af63", new string[] {  } },
			// Grid SlamcorePedestrianDetectionGrid in page DataExportPagePage
			{  "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-226cf864-e6d5-40a2-b938-9535492e60a8", new string[] { "SlamcoreDevice" } },
			// Grid VehicleSlamcoreLocationHistoryGrid in page DataExportPagePage
			{  "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-b96fddb5-673b-4152-a01a-5b017669d4ce", new string[] { "SlamcoreDevice" } },
			// Grid VehicleSlamcoreLocationHistoryGrid1 in page DataExportPagePage
			{  "auto-include-id-8c9be86f-37dd-45ed-bf39-74ac357c27c4-91b72f91-4a77-4671-850c-81afedb76554", new string[] { "SlamcoreDevice" } },
			// Grid ActiveUsersGrid in page DeactivateUserPage
			{  "auto-include-id-b3255fe6-a0b8-4371-9723-76e929fd52c5-4f5812e5-7fa6-4946-9a72-300ff1044387", new string[] {  } },
			// Grid DeactivatedUsersGrid in page DeactivateUserPage
			{  "auto-include-id-b3255fe6-a0b8-4371-9723-76e929fd52c5-bdb5a1ca-e583-4183-9ad8-e5676956bca3", new string[] {  } },
			// Grid DealerGrid1 in page DealerItemsPage
			{  "auto-include-id-068f5772-b07e-4553-98a0-fed7b8ebc860-0cfdf21b-3d90-450a-b9f4-83471fefe4e4", new string[] { "Region" } },
			// Grid DepartmentGrid in page DepartmentItemsPage
			{  "auto-include-id-b61392d6-86b3-42b9-90e9-b59a4cb5d925-4709a021-995c-4c5c-af03-644aa640387e", new string[] {  } },
			// Grid SlamcoreDeviceGrid in page DevicesPage
			{  "auto-include-id-72acb4aa-41da-4a07-b13f-e210ab88ae6f-bba73f80-59ae-4e93-a09e-81cf2fbd324e", new string[] { "Vehicle" } },
			// Grid AllDriverAccessAbuseStoreProcedureGrid in page DriverAccessAbuseReportPage
			{  "auto-include-id-c84e6276-960b-48f3-939e-ad148fb43dfd-0f86e4d0-6a11-4fc3-8ec3-be475bbeb68e", new string[] { "Driver.Person.Site", "Driver.Person.Department" } },
			// Grid DriverGrid in page DriverPagePage
			{  "auto-include-id-766a31f0-c08b-4950-9582-b3f93bfea185-8c5df8e9-22f7-417f-86d9-5a332d512bca", new string[] {  } },
			// Grid AllEmailSubscriptionStoreProcedureGrid in page EmailSubscriptionReportPage
			{  "auto-include-id-ea1d3a53-71a1-4a93-9b71-2cce0bb70ecb-2071ee91-353f-4f6f-8667-ac1d24404a97", new string[] { "ReportSubscription.Person.GOUser", "ReportSubscription.Person.Department", "ReportSubscription.Person.Site", "ReportSubscription.ReportType" } },
			// Grid ExportJobStatusGrid in page ExportJobPage
			{  "auto-include-id-0c0a61c4-77b9-44cf-a1e5-c22b656f4712-ac199e4d-33a0-40aa-8042-c1d61d653d30", new string[] {  } },
			// Grid ExportJobStatusGrid1 in page ExportPage
			{  "auto-include-id-858d0c7d-6c94-44c3-ab84-6efce8266a7d-1d1d09e4-aa4c-499e-a7e6-99f72fd6b2f5", new string[] {  } },
			// Grid DealerGrid in page FeatureSubscriptionsPage
			{  "auto-include-id-dd7cc104-5784-4e97-9216-3a6bc6994579-491a8f16-abc8-4132-88b2-2cc2eb504383", new string[] {  } },
			// Grid FirmwareGrid in page FirmwaresPagePage
			{  "auto-include-id-8f2b21a3-ca93-4a40-bcac-f6d7ed15c775-b2762780-8907-4c44-ba1d-08d4cf77b420", new string[] {  } },
			// Grid SiteGrid in page FloorPlanManagementPage
			{  "auto-include-id-e474ac15-4b10-4b40-ad1c-eb3bd41a6ae3-8ce34a10-328b-4fe1-9f10-bbd594f373c6", new string[] {  } },
			// Grid AllImpactsViewGrid in page ImpactReportPage
			{  "auto-include-id-167b559b-fbf4-4b51-9ccc-6902c02b2797-0444bba5-858e-4414-96e4-0155d482290a", new string[] { "Impact.Session.Vehicle.Model", "Impact.Session.Vehicle.Site", "Impact.Session.Vehicle.Department", "Impact.Session.Driver.Person", "Impact.Session.Driver.Card" } },
			// Grid ImportJobStatusGrid in page ImportJobPage
			{  "auto-include-id-4dc7b5f3-637b-47c9-b9e1-6169c51e04d6-60a80294-c0e0-4741-9409-6743b92e09fc", new string[] {  } },
			// Grid AllLicenseExpiryViewGrid in page LicenseExpiryReportPage
			{  "auto-include-id-17bf072c-667c-4391-ab65-57a898953334-c644677a-1776-407a-b4c7-933358d7eae2", new string[] { "Driver.Person.Department", "Driver.Person.Site" } },
			// Grid AllVehicleUnlocksViewGrid in page MachineUnlockReportPage
			{  "auto-include-id-a6c3cbf1-ea10-4961-a510-da5fb8f082a4-ab63f7d1-207e-405b-bee8-db1cc3b9d0e0", new string[] { "VehicleLockout.Vehicle.Model", "VehicleLockout.Vehicle.Site", "VehicleLockout.Vehicle.Department", "VehicleLockout.Session.Driver.Person" } },
			// Grid ModelGrid in page ModelItemsPage
			{  "auto-include-id-72dd475d-b0b2-4db4-a55d-092dfe44dfab-898cb33b-3086-4419-a82d-888e4294f246", new string[] {  } },
			// Grid ModuleGrid in page ModuleDetailsPagePage
			{  "auto-include-id-36a21c57-e9c2-47fe-a872-9f327a3972f7-bd59521d-44f7-4c5e-a374-96e68346cf25", new string[] {  } },
			// Grid OnDemandAuthorisationStoreProcedureGrid in page OnDemandAuthorisationReportPage
			{  "auto-include-id-5971ea6e-32ea-475c-a500-89f963f67f30-7afdce90-fb0c-4568-a818-f07adfb8e63f", new string[] { "OnDemandSession.Vehicle", "OnDemandSession.Driver.Person" } },
			// Grid PedestrianDetectionHistoryGrid in page PedestrianDetectionReportPage
			{  "auto-include-id-8eb279d7-c847-46f2-9c43-6849900845db-298e094d-2b0e-4f74-a742-304673623880", new string[] { "Vehicle.Department", "Vehicle.Site", "Driver.Person" } },
			// Grid PersonGrid in page PersonItemsPage
			{  "auto-include-id-ee34811f-65e2-4a17-a1df-bb78e3dc73c5-cb371c05-dca9-4398-8a37-e71864bd7e70", new string[] { "Driver.Card", "Site", "Department", "GOUser" } },
			// Grid AllChecklistResultViewGrid1 in page PreOpCheckReportPage
			{  "auto-include-id-9d01d33e-c396-4891-9e77-691e159c26c6-bf33d15c-9cd2-4edb-b73c-1c445e3d388f", new string[] { "ChecklistResult.Session.Vehicle.Department", "ChecklistResult.Session.Vehicle.Model", "ChecklistResult.Session.Vehicle.Site", "ChecklistResult.Session.Driver.Person" } },
			// Grid RegionGrid in page RegionItemsPage
			{  "auto-include-id-a33935cb-3b71-4a5f-ae58-639bc3f59977-f422927f-9661-4065-9916-5aa4eb4499b8", new string[] {  } },
			// Grid ReportSubscriptionGrid1 in page ReportSchedulerPage
			{  "auto-include-id-4d042bf5-2013-45be-8de9-2574f1ccec32-94c0accd-d3a9-4c7d-ab5d-eee588fcef95", new string[] { "Customer", "Department", "Site" } },
			// Grid ServiceSettingsGrid in page ServiceSettingsPagePage
			{  "auto-include-id-a9358988-ea54-4749-a41d-966354ead78e-16cd7294-0a4a-4fc7-ac83-af65556ea53a", new string[] {  } },
			// Grid ActiveUsersGrid in page SuperAdminPage
			{  "auto-include-id-d0c2068b-1ee9-4ae6-9399-bb6cc90d1177-4f5812e5-7fa6-4946-9a72-300ff1044387", new string[] {  } },
			// Grid AllMessageHistoryStoreProcedureGrid in page SynchronizationStatusReportPage
			{  "auto-include-id-e04ef553-c95d-4d26-9f00-004c042b3d5a-a4bf3a96-63e5-4b72-a178-3c43f25630f7", new string[] { "MessageHistory.Vehicle.Model", "MessageHistory.Vehicle.Site", "MessageHistory.Vehicle.Department", "MessageHistory.GOUser.Person" } },
			// Grid TimezoneGrid in page TimezoneItemsPage
			{  "auto-include-id-44cafc8f-9c50-4148-8147-23986b5c5075-5c128323-804c-4eac-b866-7a3bc53b97e6", new string[] {  } },
			// Grid AlertSubscriptionGrid in page UserEmailAlertSummaryReportPage
			{  "auto-include-id-5a94f189-a583-4e1c-90f3-c4e2ca2eb9f4-eafe3d68-96fe-46be-879b-d5076b08a338", new string[] { "Person.Site", "Person.Customer", "Person.Department", "Person.GOUser", "Alert" } },
			// Grid AllUserSummaryStoreProcedureGrid in page UserSummaryReportPage
			{  "auto-include-id-eda070c9-304f-4fdf-809e-41309567cdef-aff8bcb2-74e5-4ed6-9b27-9ef149eae00c", new string[] { "Person.GOUser", "Person.AccessGroup", "Person.Customer", "Person.Site", "Person.Department" } },
			// Grid AllVehicleCalibrationStoreProcedureGridAdmin in page VehicleCalibrationReportPage
			{  "auto-include-id-de8b1e3c-9163-4316-9479-4a83d1c1be0b-50b40c84-c645-4dd8-bce5-6d046fc68ca5", new string[] { "Vehicle.Module", "Vehicle.Model", "Vehicle.Site", "Vehicle.Department" } },
			// Grid AllVehicleCalibrationStoreProcedureGridCustomer in page VehicleCalibrationReportPage
			{  "auto-include-id-de8b1e3c-9163-4316-9479-4a83d1c1be0b-a19a791e-7273-40dc-b575-d4b143714271", new string[] { "Vehicle.Site", "Vehicle.Model", "Vehicle.Department" } },
			// Grid VehicleHireDehireHistoryGrid in page VehicleHireDehireReportPage
			{  "auto-include-id-5745bb94-fff5-4978-a082-85fd64ac0e2c-a9cfc1fb-e14e-4062-b42d-2c367ffd4497", new string[] { "Vehicle.Module" } },
			// Grid VehiclesGPSLocationsGrid in page VehiclesGPSReportPage
			{  "auto-include-id-2e2930c7-d464-4f45-947b-729a7d97ebcb-505dd7e4-9e21-454f-9c71-0a8e9191a1b0", new string[] { "VehicleLastGPSLocationView", "Model", "Department.Site", "Module", "VehicleGPSLocations" } },
			// Grid VehilceGrid in page VehilceItemsPage
			{  "auto-include-id-9e581779-42bc-4c4c-adc5-9bda905b2e4c-8d8a93b4-115b-4964-83f7-ac527906a021", new string[] { "Model", "Department.Site", "Driver.Person", "Module" } },
			// Grid GOUserGrid in page WebsiteusersPage
			{  "auto-include-id-4c211da9-7cb3-4531-93fb-6eab14174d8a-ee647a3a-7e7e-4269-8f7f-498d32860169", new string[] {  } },
		};
	}
}
